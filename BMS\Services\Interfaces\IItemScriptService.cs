using BMS.Models.DTOs;
using BMS.Models.Common;

namespace BMS.Services.Interfaces
{
    /// <summary>
    /// 道具脚本服务接口
    /// </summary>
    public interface IItemScriptService
    {
        /// <summary>
        /// 根据道具编号获取脚本
        /// </summary>
        /// <param name="itemNo">道具编号</param>
        /// <returns></returns>
        Task<ItemScriptDto?> GetByItemNoAsync(int itemNo);

        /// <summary>
        /// 创建或更新道具脚本
        /// </summary>
        /// <param name="upsertDto">创建/更新DTO</param>
        /// <returns></returns>
        Task<ApiResult<int>> UpsertAsync(ItemScriptUpsertDto upsertDto);

        /// <summary>
        /// 删除道具脚本
        /// </summary>
        /// <param name="id">脚本ID</param>
        /// <returns></returns>
        Task<ApiResult<bool>> DeleteAsync(int id);

        /// <summary>
        /// 根据道具编号删除脚本
        /// </summary>
        /// <param name="itemNo">道具编号</param>
        /// <returns></returns>
        Task<ApiResult<bool>> DeleteByItemNoAsync(int itemNo);
    }
} 