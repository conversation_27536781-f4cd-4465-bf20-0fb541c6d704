@{
    ViewData["Title"] = "地图配置管理";
}

<!-- 科幻背景 -->
<div class="cyber-bg"></div>

<!-- 科幻地图配置管理应用容器 -->
<div id="mapConfigApp">
    <!-- 科幻页面标题 -->
    <div class="container-fluid py-4">
        <div class="row mb-4">
            <div class="col-12">
                <div class="cyber-card">
                    <div class="cyber-card-header">
                        <div class="cyber-icon">🗺️</div>
                        <h1 class="cyber-card-title">地图配置管理</h1>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 科幻主要内容 -->
    <div class="container-fluid">
        <!-- 科幻搜索条件 -->
        <div class="cyber-card">
            <div class="cyber-card-header">
                <div class="cyber-icon">🔍</div>
                <h3 class="cyber-card-title">搜索条件</h3>
            </div>
            <div class="cyber-card-body">
                <div class="row">
                    <div class="col-md-3">
                        <div class="cyber-form-group">
                            <label class="cyber-form-label" for="search-map-id">地图ID</label>
                            <input type="number" class="cyber-form-control" id="search-map-id" v-model="searchForm.mapId" placeholder="请输入地图ID">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="cyber-form-group">
                            <label class="cyber-form-label" for="search-map-name">地图名称</label>
                            <input type="text" class="cyber-form-control" id="search-map-name" v-model="searchForm.mapName" placeholder="请输入地图名称">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="cyber-form-group">
                            <label class="cyber-form-label" for="search-atlas-name">图集名称</label>
                            <input type="text" class="cyber-form-control" id="search-atlas-name" v-model="searchForm.atlastName" placeholder="请输入图集名称">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="cyber-form-group">
                            <label class="cyber-form-label" for="search-map-type">地图类型</label>
                            <input type="number" class="cyber-form-control" id="search-map-type" v-model="searchForm.mapType" placeholder="请输入地图类型">
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-12">
                        <div class="cyber-form-group">
                            <label class="cyber-form-label">&nbsp;</label>
                            <div class="d-flex gap-2 flex-wrap">
                                <button type="button" class="cyber-btn cyber-btn-primary" v-on:click="searchMapConfigs">
                                    <i class="fas fa-search"></i> 搜索
                                </button>
                                <button type="button" class="cyber-btn cyber-btn-outline" v-on:click="resetSearch">
                                    <i class="fas fa-redo"></i> 重置
                                </button>
                                <button type="button" class="cyber-btn cyber-btn-success" v-on:click="showCreateModal">
                                    <i class="fas fa-plus"></i> 新增地图配置
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>


        <!-- 科幻地图配置列表 -->
        <div class="cyber-card">
            <div class="cyber-card-header">
                <div class="cyber-icon">📋</div>
                <h3 class="cyber-card-title">地图配置列表</h3>
            </div>
            <div class="cyber-card-body">
                <div class="cyber-table-container">
                    <table class="cyber-table">
                        <thead class="cyber-thead">
                            <tr>
                                <th class="cyber-th">ID</th>
                                <th class="cyber-th">地图ID</th>
                                <th class="cyber-th">地图名称</th>
                                <th class="cyber-th">图集名称</th>
                                <th class="cyber-th">地图描述</th>
                                <th class="cyber-th">背景图片</th>
                                <th class="cyber-th">地图大小</th>
                                <th class="cyber-th">地图类型</th>
                                <th class="cyber-th">背景音乐</th>
                                <th class="cyber-th">操作</th>
                            </tr>
                        </thead>
                        <tbody class="cyber-tbody" v-if="dataLoading">
                            <tr class="cyber-tr">
                                <td colspan="10" class="cyber-td text-center py-4">
                                    <i class="fas fa-spinner fa-spin me-2"></i>加载中...
                                </td>
                            </tr>
                        </tbody>
                        <tbody class="cyber-tbody" v-else-if="mapConfigs.length === 0">
                            <tr class="cyber-tr">
                                <td colspan="10" class="cyber-td text-center py-4 text-muted">
                                    <i class="fas fa-inbox me-2"></i>暂无数据
                                </td>
                            </tr>
                        </tbody>
                        <tbody class="cyber-tbody" v-else>
                            <tr class="cyber-tr" v-for="item in mapConfigs" v-bind:key="item.id">
                                <td class="cyber-td">{{ item.id }}</td>
                                <td class="cyber-td">{{ item.mapId }}</td>
                                <td class="cyber-td">
                                    <i class="fas fa-map me-1" style="color: var(--cyber-blue);"></i>
                                    {{ item.mapName }}
                                </td>
                                <td class="cyber-td">{{ item.atlastName || '-' }}</td>
                                <td class="cyber-td">{{ item.mapDesc || '-' }}</td>
                                <td class="cyber-td">{{ item.background || '-' }}</td>
                                <td class="cyber-td">{{ item.mapSize }}</td>
                                <td class="cyber-td">{{ item.mapType }}</td>
                                <td class="cyber-td">{{ item.bgm || '-' }}</td>
                                <td class="cyber-td">
                                    <div class="d-flex gap-1 flex-wrap">
                                        <button type="button" class="cyber-btn cyber-btn-sm cyber-btn-warning" v-on:click="showEditModal(item)" title="编辑">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button type="button" class="cyber-btn cyber-btn-sm cyber-btn-danger" v-on:click="deleteMapConfig(item.id)" title="删除">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- 科幻分页 -->
            <div class="mt-3" v-if="mapConfigs.length > 0">
                <div class="row align-items-center">
                    <div class="col-sm-6">
                        <div class="text-muted">
                            显示第 {{ (pagination.page - 1) * pagination.pageSize + 1 }} 到 {{ Math.min(pagination.page * pagination.pageSize, pagination.totalCount) }} 条记录，共 {{ pagination.totalCount }} 条记录
                        </div>
                    </div>
                    <div class="col-sm-6">
                        <ul class="cyber-pagination justify-content-end">
                            <li class="page-item" v-bind:class="{ disabled: pagination.page <= 1 }">
                                <a href="#" class="page-link" v-on:click.prevent="changePage(pagination.page - 1)">上一页</a>
                            </li>
                            <li v-for="page in getPageNumbers()" v-bind:key="page" class="page-item" v-bind:class="{ active: page === pagination.page }">
                                <a href="#" class="page-link" v-on:click.prevent="changePage(page)">{{ page }}</a>
                            </li>
                            <li class="page-item" v-bind:class="{ disabled: pagination.page >= pagination.totalPages }">
                                <a href="#" class="page-link" v-on:click.prevent="changePage(pagination.page + 1)">下一页</a>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

    </div>

    <!-- 科幻新增/编辑地图配置模态框 -->
    <div class="modal fade cyber-modal" id="mapConfigModal" tabindex="-1" role="dialog" aria-labelledby="mapConfigModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="mapConfigModalLabel">
                        <i class="fas fa-map me-2"></i>{{ modalTitle }}
                    </h5>
                    <button type="button" class="btn-close" v-on:click="closeModal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="cyber-form-group">
                                    <label class="cyber-form-label" for="modalMapId">地图ID <span class="text-danger">*</span></label>
                                    <input type="number" class="cyber-form-control" id="modalMapId" v-model="formData.mapId" v-bind:class="{ 'is-invalid': formErrors.mapId }" required>
                                    <div class="invalid-feedback" v-if="formErrors.mapId">{{ formErrors.mapId }}</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="cyber-form-group">
                                    <label class="cyber-form-label" for="modalMapName">地图名称 <span class="text-danger">*</span></label>
                                    <input type="text" class="cyber-form-control" id="modalMapName" v-model="formData.mapName" v-bind:class="{ 'is-invalid': formErrors.mapName }" required>
                                    <div class="invalid-feedback" v-if="formErrors.mapName">{{ formErrors.mapName }}</div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="cyber-form-group">
                                    <label class="cyber-form-label" for="modalAtlastName">图集名称</label>
                                    <input type="text" class="cyber-form-control" id="modalAtlastName" v-model="formData.atlastName" placeholder="请输入图集名称">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="cyber-form-group">
                                    <label class="cyber-form-label" for="modalMapDesc">地图描述</label>
                                    <textarea class="cyber-form-control" id="modalMapDesc" rows="3" v-model="formData.mapDesc" placeholder="请输入地图描述"></textarea>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="cyber-form-group">
                                    <label class="cyber-form-label" for="modalMapSize">地图大小</label>
                                    <input type="number" class="cyber-form-control" id="modalMapSize" v-model="formData.mapSize">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="cyber-form-group">
                                    <label class="cyber-form-label" for="modalMapType">地图类型</label>
                                    <input type="number" class="cyber-form-control" id="modalMapType" v-model="formData.mapType">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="cyber-form-group">
                                    <label class="cyber-form-label" for="modalType">类型（备用）</label>
                                    <input type="number" class="cyber-form-control" id="modalType" v-model="formData.type">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="cyber-form-group">
                                    <label class="cyber-form-label" for="modalBackground">背景图片</label>
                                    <input type="text" class="cyber-form-control" id="modalBackground" v-model="formData.background" placeholder="背景图片路径">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="cyber-form-group">
                                    <label class="cyber-form-label" for="modalIco">地图图标</label>
                                    <input type="text" class="cyber-form-control" id="modalIco" v-model="formData.ico" placeholder="地图图标路径">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="cyber-form-group">
                                    <label class="cyber-form-label" for="modalBgm">背景音乐</label>
                                    <input type="text" class="cyber-form-control" id="modalBgm" v-model="formData.bgm" placeholder="背景音乐路径">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="cyber-form-group">
                                    <label class="cyber-form-label" for="modalBgmVolume">音量 (0.00-1.00)</label>
                                    <input type="number" class="cyber-form-control" id="modalBgmVolume" v-model="formData.bgmVolume" step="0.01" min="0" max="1">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-3">
                                <div class="cyber-form-group">
                                    <div class="cyber-checkbox">
                                        <input type="checkbox" id="modalBgmLoop" v-model="formData.bgmLoop">
                                        <label for="modalBgmLoop" class="cyber-checkbox-label">循环播放</label>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="cyber-form-group">
                                    <div class="cyber-checkbox">
                                        <input type="checkbox" id="modalBgmPlay" v-model="formData.bgmPlay">
                                        <label for="modalBgmPlay" class="cyber-checkbox-label">播放音乐</label>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="cyber-form-group">
                                    <div class="cyber-checkbox">
                                        <input type="checkbox" id="modalBgmMute" v-model="formData.bgmMute">
                                        <label for="modalBgmMute" class="cyber-checkbox-label">静音</label>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="cyber-form-group">
                                    <div class="cyber-checkbox">
                                        <input type="checkbox" id="modalBgmPause" v-model="formData.bgmPause">
                                        <label for="modalBgmPause" class="cyber-checkbox-label">暂停</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="cyber-btn cyber-btn-outline" v-on:click="closeModal">
                        <i class="fas fa-times me-1"></i>取消
                    </button>
                    <button type="button" class="cyber-btn cyber-btn-primary" v-on:click="saveMapConfig" v-bind:disabled="loading">
                        <i v-if="loading" class="fas fa-spinner fa-spin me-1"></i>
                        <i v-else class="fas fa-save me-1"></i>
                        {{ loading ? '保存中...' : (isEdit ? '更新' : '保存') }}
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        // 初始化Vue应用
        Vue.createApp({
    data() {
        return {
            mapConfigs: [],
            dataLoading: true,
            searchForm: {
                mapId: null,
                mapName: '',
                atlastName: '',
                mapType: null
            },
            pagination: {
                page: 1,
                pageSize: 10,
                totalCount: 0,
                totalPages: 0
            },
            formData: {
                id: 0,
                mapId: null,
                mapName: '',
                atlastName: '',
                mapDesc: '',
                mapSize: 0,
                mapType: 0,
                background: '',
                bgm: '',
                bgmLoop: false,
                bgmVolume: 1.00,
                bgmPlay: true,
                bgmMute: false,
                bgmPause: false,
                ico: '',
                type: 0
            },
            formErrors: {},
            loading: false,
            isEdit: false,
            modalTitle: ''
        };
    },
    mounted() {
        console.log('地图配置管理页面已加载');
        this.loadMapConfigs();
    },
    methods: {
        // 加载地图配置列表
        async loadMapConfigs() {
            this.dataLoading = true;
            try {
                const params = {
                    page: this.pagination.page,
                    pageSize: this.pagination.pageSize,
                    ...this.searchForm
                };
                
                const params_str = new URLSearchParams(params).toString();
                const response = await fetch('/MapConfig/GetPagedList?' + params_str);
                const data = await response.json();
                console.log('加载地图配置列表 - 响应:', data);
                
                if (data.success) {
                    if (Array.isArray(data.data)) {
                        this.mapConfigs = [...data.data];
                        console.log(`成功加载 ${data.data.length} 条地图配置记录`);
                    } else {
                        console.error('API返回的data不是数组:', data.data);
                        this.mapConfigs = [];
                    }
                    
                    this.pagination.totalCount = data.totalCount || 0;
                    this.pagination.totalPages = data.totalPages || 0;
                } else {
                    console.error('API返回失败:', data.message);
                    toastr.error('加载数据失败：' + data.message);
                }
            } catch (error) {
                console.error('加载地图配置列表失败：', error);
                toastr.error('加载数据失败');
            } finally {
                this.dataLoading = false;
            }
        },
        
        // 搜索
        searchMapConfigs() {
            this.pagination.page = 1;
            this.loadMapConfigs();
        },
        
        // 重置搜索条件
        resetSearch() {
            this.searchForm = {
                mapId: null,
                mapName: '',
                atlastName: '',
                mapType: null
            };
            this.pagination.page = 1;
            this.loadMapConfigs();
        },
        
        // 分页
        changePage(page) {
            if (page >= 1 && page <= this.pagination.totalPages) {
                this.pagination.page = page;
                this.loadMapConfigs();
            }
        },
        
        // 获取页码数组
        getPageNumbers() {
            const pages = [];
            const start = Math.max(1, this.pagination.page - 2);
            const end = Math.min(this.pagination.totalPages, start + 4);
            
            for (let i = start; i <= end; i++) {
                pages.push(i);
            }
            return pages;
        },
        
        // 显示新增模态框
        showCreateModal() {
            console.log('showCreateModal clicked');
            this.isEdit = false;
            this.modalTitle = '新增地图配置';
            this.resetForm();
            $('#mapConfigModal').modal('show');
        },
        
        // 显示编辑模态框
        showEditModal(item) {
            console.log('showEditModal clicked', item);
            this.isEdit = true;
            this.modalTitle = '编辑地图配置';
            // 直接使用camelCase字段名
            this.formData = {
                id: item.id,
                mapId: item.mapId,
                mapName: item.mapName,
                atlastName: item.atlastName,
                mapDesc: item.mapDesc,
                mapSize: item.mapSize,
                mapType: item.mapType,
                background: item.background,
                bgm: item.bgm,
                bgmLoop: item.bgmLoop,
                bgmVolume: item.bgmVolume,
                bgmPlay: item.bgmPlay,
                bgmMute: item.bgmMute,
                bgmPause: item.bgmPause,
                ico: item.ico,
                type: item.type
            };
            this.formErrors = {};
            $('#mapConfigModal').modal('show');
        },
        
        // 重置表单
        resetForm() {
            this.formData = {
                id: 0,
                mapId: null,
                mapName: '',
                atlastName: '',
                mapDesc: '',
                mapSize: 0,
                mapType: 0,
                background: '',
                bgm: '',
                bgmLoop: false,
                bgmVolume: 1.00,
                bgmPlay: true,
                bgmMute: false,
                bgmPause: false,
                ico: '',
                type: 0
            };
            this.formErrors = {};
        },
        
        // 验证表单
        validateForm() {
            this.formErrors = {};
            
            if (!this.formData.mapId) {
                this.formErrors.mapId = '地图ID不能为空';
            }
            
            if (!this.formData.mapName) {
                this.formErrors.mapName = '地图名称不能为空';
            }
            
            return Object.keys(this.formErrors).length === 0;
        },
        
        // 关闭模态框
        closeModal() {
            $('#mapConfigModal').modal('hide');
        },

        // 保存地图配置
        async saveMapConfig() {
            if (!this.validateForm()) {
                return;
            }

            this.loading = true;

            try {
                const url = this.isEdit ? '/MapConfig/Update' : '/MapConfig/Create';
                const response = await fetch(url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(this.formData)
                });
                const data = await response.json();

                if (data.success) {
                    alert(data.message || (this.isEdit ? '更新成功' : '创建成功'));
                    $('#mapConfigModal').modal('hide');
                    this.loadMapConfigs();
                } else {
                    alert(data.message || '操作失败');
                }
            } catch (error) {
                console.error('保存地图配置失败：', error);
                alert('保存失败，请重试');
            } finally {
                this.loading = false;
            }
        },
        
        // 删除地图配置
        async deleteMapConfig(id) {
            console.log('deleteMapConfig clicked', id);
            if (!confirm('确定要删除这个地图配置吗？此操作无法撤销！')) {
                return;
            }

            try {
                const response = await fetch('/MapConfig/Delete', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ id })
                });
                const data = await response.json();

                if (data.success) {
                    alert(data.message || '删除成功');
                    this.loadMapConfigs();
                } else {
                    alert(data.message || '删除失败');
                }
            } catch (error) {
                console.error('删除地图配置失败：', error);
                alert('删除失败，请重试');
            }
        }
    }
}).mount('#mapConfigApp');
    </script>
}