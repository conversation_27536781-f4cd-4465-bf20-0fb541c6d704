using BMS.Models.Common;
using BMS.Models.DTOs;
using BMS.Models.Entities;
using BMS.Services.Interfaces;
using SqlSugar;

namespace BMS.Services.Implementations
{
    /// <summary>
    /// 用户宠物服务实现
    /// </summary>
    public class UserPetService : IUserPetService
    {
        private readonly IDbService _dbService;

        public UserPetService(IDbService dbService)
        {
            _dbService = dbService;
        }

        /// <summary>
        /// 分页查询用户宠物列表
        /// </summary>
        /// <param name="queryDto">查询条件</param>
        /// <returns>分页结果</returns>
        public async Task<PagedResult<UserPetDto>> GetUserPetsAsync(UserPetQueryDto queryDto)
        {
            try
            {
                var query = _dbService.Queryable<user_pet>()
                    .LeftJoin<user>((up, u) => up.user_id == u.id)
                    .LeftJoin<pet_config>((up, u, pc) => up.pet_no == pc.pet_no);

                // 构建查询条件
                if (queryDto.UserId.HasValue)
                {
                    query = query.Where((up, u, pc) => up.user_id == queryDto.UserId.Value);
                }

                if (!string.IsNullOrWhiteSpace(queryDto.UserName))
                {
                    query = query.Where((up, u, pc) => u.username.Contains(queryDto.UserName));
                }

                if (queryDto.PetNo.HasValue)
                {
                    query = query.Where((up, u, pc) => up.pet_no == queryDto.PetNo.Value);
                }

                if (!string.IsNullOrWhiteSpace(queryDto.PetName))
                {
                    query = query.Where((up, u, pc) => pc.name.Contains(queryDto.PetName));
                }

                if (!string.IsNullOrWhiteSpace(queryDto.PetAttribute))
                {
                    query = query.Where((up, u, pc) => pc.attribute == queryDto.PetAttribute);
                }

                if (!string.IsNullOrWhiteSpace(queryDto.Realm))
                {
                    query = query.Where((up, u, pc) => up.realm == queryDto.Realm);
                }

                if (!string.IsNullOrWhiteSpace(queryDto.Element))
                {
                    query = query.Where((up, u, pc) => up.element == queryDto.Element);
                }

                // 新增字段查询条件
                if (!string.IsNullOrWhiteSpace(queryDto.Status))
                {
                    query = query.Where((up, u, pc) => up.status == queryDto.Status);
                }

                if (queryDto.IsMain.HasValue)
                {
                    query = query.Where((up, u, pc) => up.is_main == queryDto.IsMain.Value);
                }

                // 获取总数
                var totalCount = await query.CountAsync();

                // 分页查询 - 使用ToPageList方式
                var pageResult = await query
                    .OrderByDescending((up, u, pc) => up.create_time)
                    .Select((up, u, pc) => new UserPetDto
                    {
                        Id = up.id,
                        UserId = up.user_id,
                        UserName = u.username ?? "",
                        PetNo = up.pet_no,
                        PetName = pc.name ?? "",
                        PetAttribute = pc.attribute ?? "",
                        Image = up.image,
                        Exp = up.exp,
                        Hp = up.hp,
                        Mp = up.mp,
                        Atk = up.atk,
                        Def = up.def,
                        Spd = up.spd,
                        State = up.state,
                        Dodge = up.dodge,
                        Growth = up.growth,
                        Hit = up.hit,
                        Realm = up.realm,
                        EvolveCount = up.evolve_count,
                        Element = up.element,
                        CreateTime = up.create_time,
                        Status = up.status ?? "",
                        IsMain = up.is_main
                    })
                    .ToPageListAsync(queryDto.Page, queryDto.PageSize, totalCount);

                var userPets = pageResult.ToList();

                // 为每个宠物加载技能信息和计算等级
                foreach (var pet in userPets)
                {
                    pet.Skills = await GetUserPetSkillsAsync(pet.Id);
                    pet.Level = await CalculateLevelByExpAsync(pet.Exp);
                }

                return new PagedResult<UserPetDto>(userPets, queryDto.Page, queryDto.PageSize, totalCount);
            }
            catch (Exception ex)
            {
                throw new Exception($"查询用户宠物列表失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 根据ID获取用户宠物信息
        /// </summary>
        /// <param name="id">用户宠物ID</param>
        /// <returns>用户宠物信息</returns>
        public async Task<UserPetDto?> GetUserPetByIdAsync(int id)
        {
            try
            {
                var userPet = await _dbService.GetClient().Queryable<user_pet, user, pet_config>((up, u, pc) => new JoinQueryInfos(
                    JoinType.Left, up.user_id == u.id,
                    JoinType.Left, up.pet_no == pc.pet_no
                ))
                .Where((up, u, pc) => up.id == id)
                .Select((up, u, pc) => new UserPetDto
                {
                    Id = up.id,
                    UserId = up.user_id,
                    UserName = u.username ?? "",
                    PetNo = up.pet_no,
                    PetName = pc.name ?? "",
                    PetAttribute = pc.attribute ?? "",
                    Image = up.image,
                    Exp = up.exp,
                    Hp = up.hp,
                    Mp = up.mp,
                    Atk = up.atk,
                    Def = up.def,
                    Spd = up.spd,
                    State = up.state,
                    Dodge = up.dodge,
                    Growth = up.growth,
                    Hit = up.hit,
                    Realm = up.realm,
                    EvolveCount = up.evolve_count,
                    Element = up.element,
                    CreateTime = up.create_time,
                    Status = up.status ?? "",
                    IsMain = up.is_main
                })
                .FirstAsync();

                if (userPet != null)
                {
                    userPet.Skills = await GetUserPetSkillsAsync(userPet.Id);
                    userPet.Level = await CalculateLevelByExpAsync(userPet.Exp);
                }

                return userPet;
            }
            catch (Exception ex)
            {
                throw new Exception($"获取用户宠物信息失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 根据用户ID获取该用户的所有宠物
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>用户宠物列表</returns>
        public async Task<List<UserPetDto>> GetPetsByUserIdAsync(int userId)
        {
            try
            {
                var userPets = await _dbService.GetClient().Queryable<user_pet, pet_config>((up, pc) => new JoinQueryInfos(
                    JoinType.Left, up.pet_no == pc.pet_no
                ))
                .Where((up, pc) => up.user_id == userId)
                .OrderByDescending((up, pc) => up.create_time)
                .Select((up, pc) => new UserPetDto
                {
                    Id = up.id,
                    UserId = up.user_id,
                    PetNo = up.pet_no,
                    PetName = pc.name ?? "",
                    PetAttribute = pc.attribute ?? "",
                    Image = up.image,
                    Exp = up.exp,
                    Hp = up.hp,
                    Mp = up.mp,
                    Atk = up.atk,
                    Def = up.def,
                    Spd = up.spd,
                    State = up.state,
                    Dodge = up.dodge,
                    Growth = up.growth,
                    Hit = up.hit,
                    Realm = up.realm,
                    EvolveCount = up.evolve_count,
                    Element = up.element,
                    CreateTime = up.create_time,
                    Status = up.status ?? "",
                    IsMain = up.is_main
                })
                .ToListAsync();

                // 为每个宠物加载技能信息和计算等级
                foreach (var pet in userPets)
                {
                    pet.Skills = await GetUserPetSkillsAsync(pet.Id);
                    pet.Level = await CalculateLevelByExpAsync(pet.Exp);
                }

                return userPets;
            }
            catch (Exception ex)
            {
                throw new Exception($"获取用户宠物列表失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 创建用户宠物
        /// </summary>
        /// <param name="createDto">创建用户宠物DTO</param>
        /// <returns>创建结果</returns>
        public async Task<ApiResult<int>> CreateUserPetAsync(UserPetCreateDto createDto)
        {
            try
            {
                // 检查用户是否存在
                var userExists = await _dbService.Queryable<user>()
                    .Where(x => x.id == createDto.UserId)
                    .AnyAsync();

                if (!userExists)
                {
                    return ApiResult<int>.Fail("用户不存在");
                }

                // 检查宠物配置是否存在
                var petConfig = await _dbService.Queryable<pet_config>()
                    .Where(x => x.pet_no == createDto.PetNo)
                    .FirstAsync();

                if (petConfig == null)
                {
                    return ApiResult<int>.Fail("宠物配置不存在");
                }

                // 检查用户是否已拥有该宠物
                //bool exists = await CheckUserPetExistsAsync(createDto.UserId, createDto.PetNo);
                //if (exists)
                //{
                //    return ApiResult<int>.Fail("用户已拥有该宠物");
                //}

                // 如果设置为主战宠物，需要先取消该用户其他宠物的主战状态
                if (createDto.IsMain)
                {
                    await  _dbService.Updateable<user_pet>()
                        .SetColumns(x => x.is_main == false)
                        .Where(x => x.user_id == createDto.UserId && x.is_main == true)
                        .ExecuteCommandAsync();
                }

                // 创建实体
                var entity = new user_pet
                {
                    user_id = createDto.UserId,
                    pet_no = createDto.PetNo,
                    image = createDto.Image,
                    exp = createDto.Exp,
                    hp = createDto.Hp,
                    mp = createDto.Mp,
                    atk = createDto.Atk,
                    def = createDto.Def,
                    spd = createDto.Spd,
                    state = createDto.State,
                    dodge = createDto.Dodge,
                    growth = createDto.Growth,
                    hit = createDto.Hit,
                    realm = createDto.Realm,
                    evolve_count = createDto.EvolveCount,
                    element = createDto.Element,
                    create_time = DateTime.Now,
                    status = createDto.Status,
                    is_main = createDto.IsMain
                };

                // 插入数据库
                var result = await  _dbService.Insertable(entity).ExecuteReturnIdentityAsync();

                // 自动分配宠物配置中的技能
                await AssignSkillsByPetConfigAsync(result);

                return ApiResult<int>.Ok(result, "创建用户宠物成功");
            }
            catch (Exception ex)
            {
                return ApiResult<int>.Fail($"创建用户宠物失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 更新用户宠物信息
        /// </summary>
        /// <param name="updateDto">更新用户宠物DTO</param>
        /// <returns>更新结果</returns>
        public async Task<ApiResult<bool>> UpdateUserPetAsync(UserPetUpdateDto updateDto)
        {
            try
            {
                // 检查用户宠物是否存在
                var existing = await  _dbService.Queryable<user_pet>()
                    .Where(x => x.id == updateDto.Id)
                    .FirstAsync();

                if (existing == null)
                {
                    return ApiResult<bool>.Fail("用户宠物不存在");
                }

                // 如果设置为主战宠物，需要先取消该用户其他宠物的主战状态
                if (updateDto.IsMain && !existing.is_main)
                {
                    await  _dbService.Updateable<user_pet>()
                        .SetColumns(x => x.is_main == false)
                        .Where(x => x.user_id == existing.user_id && x.is_main == true && x.id != updateDto.Id)
                        .ExecuteCommandAsync();
                }

                // 更新实体
                existing.image = updateDto.Image;
                existing.exp = updateDto.Exp;
                existing.hp = updateDto.Hp;
                existing.mp = updateDto.Mp;
                existing.atk = updateDto.Atk;
                existing.def = updateDto.Def;
                existing.spd = updateDto.Spd;
                existing.state = updateDto.State;
                existing.dodge = updateDto.Dodge;
                existing.growth = updateDto.Growth;
                existing.hit = updateDto.Hit;
                existing.realm = updateDto.Realm;
                existing.evolve_count = updateDto.EvolveCount;
                existing.element = updateDto.Element;
                existing.status = updateDto.Status;
                existing.is_main = updateDto.IsMain;

                // 更新数据库
                var result = await  _dbService.Updateable(existing).ExecuteCommandAsync();

                return result > 0 
                    ? ApiResult<bool>.Ok(true, "更新用户宠物成功")
                    : ApiResult<bool>.Fail("更新用户宠物失败");
            }
            catch (Exception ex)
            {
                return ApiResult<bool>.Fail($"更新用户宠物失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 删除用户宠物
        /// </summary>
        /// <param name="deleteDto">删除用户宠物DTO</param>
        /// <returns>删除结果</returns>
        public async Task<ApiResult<bool>> DeleteUserPetAsync(UserPetDeleteDto deleteDto)
        {
            try
            {
                // 检查用户宠物是否存在
                var existing = await  _dbService.Queryable<user_pet>()
                    .Where(x => x.id == deleteDto.Id)
                    .FirstAsync();

                if (existing == null)
                {
                    return ApiResult<bool>.Fail("用户宠物不存在");
                }

                // 开始事务
                await _dbService.GetClient().Ado.BeginTranAsync();

                try
                {
                    // 先删除宠物技能
                    await _dbService.Deleteable<user_pet_skill>()
                        .Where(x => x.user_pet_id == deleteDto.Id)
                        .ExecuteCommandAsync();

                    // 删除用户宠物
                    var result = await _dbService.Deleteable<user_pet>()
                        .Where(x => x.id == deleteDto.Id)
                        .ExecuteCommandAsync();

                    await _dbService.GetClient().Ado.CommitTranAsync();

                    return result > 0 
                        ? ApiResult<bool>.Ok(true, "删除用户宠物成功")
                        : ApiResult<bool>.Fail("删除用户宠物失败");
                }
                catch
                {
                    await _dbService.GetClient().Ado.RollbackTranAsync();
                    throw;
                }
            }
            catch (Exception ex)
            {
                return ApiResult<bool>.Fail($"删除用户宠物失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 检查用户是否已拥有指定宠物
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="petNo">宠物序号</param>
        /// <param name="excludeId">排除的用户宠物ID（用于更新时检查）</param>
        /// <returns>是否已拥有</returns>
        public async Task<bool> CheckUserPetExistsAsync(int userId, int petNo, int? excludeId = null)
        {
            try
            {
                var query =  _dbService.Queryable<user_pet>()
                    .Where(x => x.user_id == userId && x.pet_no == petNo);

                if (excludeId.HasValue)
                {
                    query = query.Where(x => x.id != excludeId.Value);
                }

                return await query.AnyAsync();
            }
            catch (Exception ex)
            {
                throw new Exception($"检查用户宠物是否存在失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 获取用户宠物的技能列表
        /// </summary>
        /// <param name="userPetId">用户宠物ID</param>
        /// <returns>技能列表</returns>
        public async Task<List<UserPetSkillDto>> GetUserPetSkillsAsync(int userPetId)
        {
            try
            {
                var skills = await _dbService.GetClient().Queryable<user_pet_skill, skill>((ups, s) => new JoinQueryInfos(
                    JoinType.Left, ups.skill_id.ToString() == s.skill_id
                ))
                .Where((ups, s) => ups.user_pet_id == userPetId)
                .Select((ups, s) => new UserPetSkillDto
                {
                    Id = ups.id,
                    UserPetId = ups.user_pet_id,
                    SkillId = ups.skill_id,
                    SkillName = s.skill_name ?? "",
                    SkillLevel = ups.skill_level,
                    CreateTime = ups.create_time
                })
                .ToListAsync();

                return skills;
            }
            catch (Exception ex)
            {
                throw new Exception($"获取用户宠物技能失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 为用户宠物添加技能
        /// </summary>
        /// <param name="manageDto">添加技能DTO</param>
        /// <returns>添加结果</returns>
        public async Task<ApiResult<int>> AddUserPetSkillAsync(UserPetSkillManageDto manageDto)
        {
            try
            {
                // 检查用户宠物是否存在
                var petExists = await  _dbService.Queryable<user_pet>()
                    .Where(x => x.id == manageDto.UserPetId)
                    .AnyAsync();

                if (!petExists)
                {
                    return ApiResult<int>.Fail("用户宠物不存在");
                }

                // 检查技能是否存在
                var skillExists = await  _dbService.Queryable<skill>()
                    .Where(x => x.skill_id == manageDto.SkillId.ToString())
                    .AnyAsync();

                if (!skillExists)
                {
                    return ApiResult<int>.Fail("技能不存在");
                }

                // 检查是否已拥有该技能
                bool exists = await CheckUserPetSkillExistsAsync(manageDto.UserPetId, manageDto.SkillId);
                if (exists)
                {
                    return ApiResult<int>.Fail("宠物已拥有该技能");
                }

                // 创建技能记录
                var entity = new user_pet_skill
                {
                    user_pet_id = manageDto.UserPetId,
                    skill_id = manageDto.SkillId,
                    skill_level = manageDto.SkillLevel,
                    create_time = DateTime.Now
                };

                var result = await  _dbService.Insertable(entity).ExecuteReturnIdentityAsync();

                return ApiResult<int>.Ok(result, "添加宠物技能成功");
            }
            catch (Exception ex)
            {
                return ApiResult<int>.Fail($"添加宠物技能失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 更新用户宠物技能等级
        /// </summary>
        /// <param name="skillId">技能记录ID</param>
        /// <param name="skillLevel">技能等级</param>
        /// <returns>更新结果</returns>
        public async Task<ApiResult<bool>> UpdateUserPetSkillLevelAsync(int skillId, int skillLevel)
        {
            try
            {
                var result = await  _dbService.Updateable<user_pet_skill>()
                    .SetColumns(x => x.skill_level == skillLevel)
                    .Where(x => x.id == skillId)
                    .ExecuteCommandAsync();

                return result > 0 
                    ? ApiResult<bool>.Ok(true, "更新技能等级成功")
                    : ApiResult<bool>.Fail("更新技能等级失败");
            }
            catch (Exception ex)
            {
                return ApiResult<bool>.Fail($"更新技能等级失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 删除用户宠物技能
        /// </summary>
        /// <param name="deleteDto">删除技能DTO</param>
        /// <returns>删除结果</returns>
        public async Task<ApiResult<bool>> DeleteUserPetSkillAsync(UserPetSkillDeleteDto deleteDto)
        {
            try
            {
                var result = await  _dbService.Deleteable<user_pet_skill>()
                    .Where(x => x.id == deleteDto.Id)
                    .ExecuteCommandAsync();

                return result > 0 
                    ? ApiResult<bool>.Ok(true, "删除宠物技能成功")
                    : ApiResult<bool>.Fail("删除宠物技能失败");
            }
            catch (Exception ex)
            {
                return ApiResult<bool>.Fail($"删除宠物技能失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 检查用户宠物是否已拥有指定技能
        /// </summary>
        /// <param name="userPetId">用户宠物ID</param>
        /// <param name="skillId">技能编号</param>
        /// <returns>是否已拥有</returns>
        public async Task<bool> CheckUserPetSkillExistsAsync(int userPetId, int skillId)
        {
            try
            {
                return await  _dbService.Queryable<user_pet_skill>()
                    .Where(x => x.user_pet_id == userPetId && x.skill_id == skillId)
                    .AnyAsync();
            }
            catch (Exception ex)
            {
                throw new Exception($"检查宠物技能是否存在失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 获取宠物配置下拉选项
        /// </summary>
        /// <returns>宠物配置选项</returns>
        public async Task<List<PetConfigDto>> GetPetConfigOptionsAsync()
        {
            try
            {
                var petConfigs = await  _dbService.Queryable<pet_config>()
                    .OrderBy(x => x.pet_no)
                    .Select(x => new PetConfigDto
                    {
                        Id = x.id,
                        PetNo = x.pet_no,
                        Name = x.name,
                        Attribute = x.attribute,
                        Skill = x.skill
                    })
                    .ToListAsync();

                return petConfigs;
            }
            catch (Exception ex)
            {
                throw new Exception($"获取宠物配置选项失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 批量为用户宠物分配技能（根据宠物配置）
        /// </summary>
        /// <param name="userPetId">用户宠物ID</param>
        /// <returns>分配结果</returns>
        public async Task<ApiResult<bool>> AssignSkillsByPetConfigAsync(int userPetId)
        {
            try
            {
                // 获取用户宠物信息
                var userPet = await  _dbService.Queryable<user_pet>()
                    .Where(x => x.id == userPetId)
                    .FirstAsync();

                if (userPet == null)
                {
                    return ApiResult<bool>.Fail("用户宠物不存在");
                }

                // 获取宠物配置
                var petConfig = await  _dbService.Queryable<pet_config>()
                    .Where(x => x.pet_no == userPet.pet_no)
                    .FirstAsync();

                if (petConfig == null || string.IsNullOrWhiteSpace(petConfig.skill))
                {
                    return ApiResult<bool>.Ok(true, "该宠物无默认技能配置");
                }

                // 解析技能编号（用逗号分隔）
                var skillIds = petConfig.skill.Split(',', StringSplitOptions.RemoveEmptyEntries)
                    .Select(s => s.Trim())
                    .Where(s => int.TryParse(s, out _))
                    .Select(int.Parse)
                    .ToList();

                if (!skillIds.Any())
                {
                    return ApiResult<bool>.Ok(true, "该宠物无有效技能配置");
                }

                var successCount = 0;
                var skillRecords = new List<user_pet_skill>();

                foreach (var skillId in skillIds)
                {
                    // 检查是否已拥有该技能
                    bool exists = await CheckUserPetSkillExistsAsync(userPetId, skillId);
                    if (!exists)
                    {
                        // 检查技能是否存在
                        var skillExists = await  _dbService.Queryable<skill>()
                            .Where(x => x.skill_id == skillId.ToString())
                            .AnyAsync();

                        if (skillExists)
                        {
                            skillRecords.Add(new user_pet_skill
                            {
                                user_pet_id = userPetId,
                                skill_id = skillId,
                                skill_level = 1,
                                create_time = DateTime.Now
                            });
                            successCount++;
                        }
                    }
                }

                // 批量插入技能
                if (skillRecords.Any())
                {
                    await  _dbService.Insertable(skillRecords).ExecuteCommandAsync();
                }

                return ApiResult<bool>.Ok(true, $"成功为宠物分配了 {successCount} 个技能");
            }
            catch (Exception ex)
            {
                return ApiResult<bool>.Fail($"分配技能失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 宠物进化
        /// </summary>
        /// <param name="userPetId">用户宠物ID</param>
        /// <returns>进化结果</returns>
        public async Task<ApiResult<bool>> EvolvePetAsync(int userPetId)
        {
            try
            {
                var userPet = await  _dbService.Queryable<user_pet>()
                    .Where(x => x.id == userPetId)
                    .FirstAsync();

                if (userPet == null)
                {
                    return ApiResult<bool>.Fail("用户宠物不存在");
                }

                // 简单的进化逻辑：增加进化次数，提升属性
                var evolveCount = (userPet.evolve_count ?? 0) + 1;
                var growthBonus = 0.1m; // 每次进化成长值增加0.1

                var result = await  _dbService.Updateable<user_pet>()
                    .SetColumns(x => new user_pet
                    {
                        evolve_count = evolveCount,
                        growth = (x.growth ?? 1.0m) + growthBonus,
                        atk = (long)((x.atk ?? 0) * 1.1), // 属性提升10%
                        def = (long)((x.def ?? 0) * 1.1),
                        hp = (long)((x.hp ?? 0) * 1.1),
                        mp = (long)((x.mp ?? 0) * 1.1),
                        spd = (long)((x.spd ?? 0) * 1.1),
                        hit = (long)((x.hit ?? 0) * 1.1),
                        dodge = (long)((x.dodge ?? 0) * 1.1)
                    })
                    .Where(x => x.id == userPetId)
                    .ExecuteCommandAsync();

                return result > 0 
                    ? ApiResult<bool>.Ok(true, $"宠物进化成功，当前进化次数：{evolveCount}")
                    : ApiResult<bool>.Fail("宠物进化失败");
            }
            catch (Exception ex)
            {
                return ApiResult<bool>.Fail($"宠物进化失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 重置宠物属性
        /// </summary>
        /// <param name="userPetId">用户宠物ID</param>
        /// <returns>重置结果</returns>
        public async Task<ApiResult<bool>> ResetPetAttributesAsync(int userPetId)
        {
            try
            {
                var result = await  _dbService.Updateable<user_pet>()
                    .SetColumns(x => new user_pet
                    {
                        exp = 0,
                        hp = 100,
                        mp = 100,
                        atk = 10,
                        def = 10,
                        spd = 10,
                        dodge = 10,
                        growth = 1.0m,
                        hit = 10,
                        evolve_count = 0,
                        realm = null,
                        element = null
                    })
                    .Where(x => x.id == userPetId)
                    .ExecuteCommandAsync();

                return result > 0
                    ? ApiResult<bool>.Ok(true, "重置宠物属性成功")
                    : ApiResult<bool>.Fail("重置宠物属性失败");
            }
            catch (Exception ex)
            {
                return ApiResult<bool>.Fail($"重置宠物属性失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 根据经验值计算等级
        /// </summary>
        /// <param name="exp">当前经验值</param>
        /// <returns>等级</returns>
        private async Task<int> CalculateLevelByExpAsync(long? exp)
        {
            if (!exp.HasValue || exp.Value <= 0)
            {
                return 0;
            }

            try
            {
                // 从level_config表中查询等级
                var levelConfig = await _dbService.Queryable<LevelConfig>()
                    .Where(x => x.required_exp <= exp.Value && x.is_active == 1)
                    .OrderByDescending(x => x.level)
                    .FirstAsync();

                return levelConfig?.level ?? 0;
            }
            catch (Exception)
            {
                // 如果查询失败，返回默认等级0
                return 0;
            }
        }

        /// <summary>
        /// 复制用户宠物
        /// </summary>
        /// <param name="userPetId">要复制的用户宠物ID</param>
        /// <returns>复制结果</returns>
        public async Task<ApiResult<int>> CopyUserPetAsync(int userPetId)
        {
            try
            {
                // 获取原宠物信息
                var originalPet = await _dbService.Queryable<user_pet>()
                    .Where(x => x.id == userPetId)
                    .FirstAsync();

                if (originalPet == null)
                {
                    return ApiResult<int>.Fail("原宠物不存在");
                }

                // 检查用户携带宠物数量限制（最多3只）
                var userPets = await GetPetsByUserIdAsync(originalPet.user_id);
                var carriedPets = userPets.Where(p => p.Status == "携带").ToList();

                // 复制的宠物默认放入牧场，避免超过携带限制
                string newStatus = "牧场";
                bool isMain = false;

                // 如果用户携带的宠物少于3只，可以直接复制为携带状态
                if (carriedPets.Count < 3 && originalPet.status == "携带")
                {
                    newStatus = "携带";
                }

                // 创建复制的宠物实体
                var copiedPet = new user_pet
                {
                    user_id = originalPet.user_id,
                    pet_no = originalPet.pet_no,
                    image = originalPet.image,
                    exp = originalPet.exp,
                    hp = originalPet.hp,
                    mp = originalPet.mp,
                    atk = originalPet.atk,
                    def = originalPet.def,
                    spd = originalPet.spd,
                    state = originalPet.state,
                    dodge = originalPet.dodge,
                    growth = originalPet.growth,
                    hit = originalPet.hit,
                    realm = originalPet.realm,
                    evolve_count = originalPet.evolve_count,
                    element = originalPet.element,
                    create_time = DateTime.Now,
                    status = newStatus,
                    is_main = isMain
                };

                // 插入数据库
                var newPetId = await _dbService.Insertable(copiedPet).ExecuteReturnIdentityAsync();

                // 复制宠物技能
                var originalSkills = await _dbService.Queryable<user_pet_skill>()
                    .Where(x => x.user_pet_id == userPetId)
                    .ToListAsync();

                if (originalSkills.Any())
                {
                    var copiedSkills = originalSkills.Select(skill => new user_pet_skill
                    {
                        user_pet_id = newPetId,
                        skill_id = skill.skill_id,
                        skill_level = skill.skill_level,
                        create_time = DateTime.Now
                    }).ToList();

                    await _dbService.Insertable(copiedSkills).ExecuteCommandAsync();
                }

                string message = newStatus == "牧场"
                    ? "复制宠物成功，新宠物已放入牧场"
                    : "复制宠物成功";

                return ApiResult<int>.Ok(newPetId, message);
            }
            catch (Exception ex)
            {
                return ApiResult<int>.Fail($"复制宠物失败：{ex.Message}");
            }
        }
    }
}