namespace BMS.Models.DTOs
{
    /// <summary>
    /// 技能查询DTO
    /// </summary>
    public class SkillQueryDto
    {
        /// <summary>
        /// 技能ID（支持模糊查询）
        /// </summary>
        public string? SkillId { get; set; }

        /// <summary>
        /// 技能名称（支持模糊查询）
        /// </summary>
        public string? SkillName { get; set; }

        /// <summary>
        /// 技能效果类型
        /// </summary>
        public string? EffectType { get; set; }

        /// <summary>
        /// 五行限制
        /// </summary>
        public string? ElementLimit { get; set; }

        /// <summary>
        /// 页码
        /// </summary>
        public int Page { get; set; } = 1;

        /// <summary>
        /// 每页数量
        /// </summary>
        public int PageSize { get; set; } = 10;
    }
} 