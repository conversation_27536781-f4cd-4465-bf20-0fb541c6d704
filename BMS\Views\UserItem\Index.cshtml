@{
    ViewData["Title"] = "用户道具管理";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<style>
    /* 强制应用科幻主题 */
    #userItemApp .cyber-card {
        background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%) !important;
        border: 1px solid rgba(255, 255, 255, 0.1) !important;
        color: #e2e8f0 !important;
        border-radius: 12px !important;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3) !important;
        margin-bottom: 1.5rem !important;
    }

    #userItemApp .cyber-card-header {
        background: linear-gradient(135deg, rgba(0, 212, 255, 0.1), rgba(139, 92, 246, 0.1)) !important;
        border-bottom: 1px solid rgba(0, 212, 255, 0.3) !important;
        padding: 1rem 1.5rem !important;
        border-radius: 12px 12px 0 0 !important;
        display: flex !important;
        align-items: center !important;
        gap: 0.75rem !important;
    }

    #userItemApp .cyber-card-body {
        padding: 1.5rem !important;
    }

    #userItemApp .cyber-form-control {
        background: rgba(26, 26, 46, 0.8) !important;
        border: 1px solid rgba(255, 255, 255, 0.2) !important;
        color: #e2e8f0 !important;
        border-radius: 8px !important;
        padding: 0.75rem 1rem !important;
        transition: all 0.3s ease !important;
    }

    #userItemApp .cyber-form-control:focus {
        border-color: #00d4ff !important;
        box-shadow: 0 0 0 0.2rem rgba(0, 212, 255, 0.25) !important;
        background: rgba(26, 26, 46, 0.9) !important;
    }

    #userItemApp .cyber-btn {
        background: linear-gradient(135deg, #00d4ff, #8b5cf6) !important;
        border: none !important;
        color: white !important;
        padding: 0.75rem 1.5rem !important;
        border-radius: 8px !important;
        font-weight: 500 !important;
        transition: all 0.3s ease !important;
    }

    #userItemApp .cyber-btn:hover {
        transform: translateY(-2px) !important;
        box-shadow: 0 8px 25px rgba(0, 212, 255, 0.3) !important;
    }

    #userItemApp .cyber-table {
        background: transparent !important;
        color: #e2e8f0 !important;
        border-radius: 8px !important;
        overflow: hidden !important;
    }

    #userItemApp .cyber-table th {
        background: linear-gradient(135deg, #1a1a2e, #16213e) !important;
        border-bottom: 1px solid rgba(0, 212, 255, 0.3) !important;
        color: #e2e8f0 !important;
        padding: 1rem !important;
        font-weight: 600 !important;
    }

    #userItemApp .cyber-table td {
        border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
        color: #e2e8f0 !important;
        padding: 0.75rem 1rem !important;
    }

    #userItemApp .cyber-table tbody tr:hover {
        background: rgba(0, 212, 255, 0.1) !important;
    }

    /* 科幻统计卡片 */
    #userItemApp .cyber-stat-card {
        background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%) !important;
        border: 1px solid rgba(255, 255, 255, 0.1) !important;
        border-radius: 12px !important;
        padding: 2rem !important;
        color: white !important;
        transition: all 0.3s ease !important;
        position: relative !important;
        overflow: hidden !important;
        margin-bottom: 1.5rem !important;
    }

    #userItemApp .cyber-stat-card::before {
        content: '' !important;
        position: absolute !important;
        top: 0 !important;
        left: 0 !important;
        right: 0 !important;
        bottom: 0 !important;
        background: linear-gradient(45deg, rgba(0, 212, 255, 0.1) 0%, transparent 100%) !important;
        pointer-events: none !important;
    }

    #userItemApp .cyber-stat-card:hover {
        transform: translateY(-5px) !important;
        box-shadow: 0 20px 40px rgba(0, 212, 255, 0.2) !important;
    }

    #userItemApp .cyber-stat-card.stat-info {
        border-left: 4px solid #00d4ff !important;
    }

    #userItemApp .cyber-stat-card.stat-success {
        border-left: 4px solid #10b981 !important;
    }

    #userItemApp .cyber-stat-card.stat-warning {
        border-left: 4px solid #f59e0b !important;
    }

    #userItemApp .cyber-stat-card.stat-danger {
        border-left: 4px solid #ef4444 !important;
    }

    /* 科幻按钮变体 */
    #userItemApp .cyber-btn-outline {
        background: transparent !important;
        border: 1px solid rgba(0, 212, 255, 0.5) !important;
        color: #00d4ff !important;
    }

    #userItemApp .cyber-btn-outline:hover {
        background: rgba(0, 212, 255, 0.1) !important;
        border-color: #00d4ff !important;
    }

    #userItemApp .cyber-btn-success {
        background: linear-gradient(135deg, #10b981, #059669) !important;
    }

    #userItemApp .cyber-btn-warning {
        background: linear-gradient(135deg, #f59e0b, #d97706) !important;
    }

    #userItemApp .cyber-btn-danger {
        background: linear-gradient(135deg, #ef4444, #dc2626) !important;
    }

    #userItemApp .cyber-btn-info {
        background: linear-gradient(135deg, #3b82f6, #2563eb) !important;
    }

    #userItemApp .cyber-btn-sm {
        padding: 0.5rem 1rem !important;
        font-size: 0.875rem !important;
    }

    #userItemApp .cyber-btn-secondary {
        background: linear-gradient(135deg, #6b7280, #4b5563) !important;
    }

    /* 科幻分页样式 */
    #userItemApp .cyber-pagination .page-link {
        background: rgba(26, 26, 46, 0.8) !important;
        border: 1px solid rgba(255, 255, 255, 0.2) !important;
        color: #e2e8f0 !important;
        margin: 0 2px !important;
        border-radius: 6px !important;
    }

    #userItemApp .cyber-pagination .page-link:hover {
        background: rgba(0, 212, 255, 0.2) !important;
        border-color: #00d4ff !important;
        color: #00d4ff !important;
    }

    #userItemApp .cyber-pagination .page-item.active .page-link {
        background: linear-gradient(135deg, #00d4ff, #8b5cf6) !important;
        border-color: #00d4ff !important;
        color: white !important;
    }

    /* 科幻徽章样式 */
    #userItemApp .cyber-badge {
        background: linear-gradient(135deg, #00d4ff, #8b5cf6) !important;
        color: white !important;
        padding: 0.25rem 0.75rem !important;
        border-radius: 12px !important;
        font-size: 0.75rem !important;
        font-weight: 500 !important;
    }

    #userItemApp .cyber-badge-success {
        background: linear-gradient(135deg, #10b981, #059669) !important;
    }

    #userItemApp .cyber-badge-warning {
        background: linear-gradient(135deg, #f59e0b, #d97706) !important;
    }

    #userItemApp .cyber-badge-danger {
        background: linear-gradient(135deg, #ef4444, #dc2626) !important;
    }

    #userItemApp .cyber-badge-info {
        background: linear-gradient(135deg, #3b82f6, #2563eb) !important;
    }

    /* 科幻模态框样式 */
    .cyber-modal .modal-content {
        background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%) !important;
        border: 1px solid rgba(255, 255, 255, 0.1) !important;
        color: #e2e8f0 !important;
        border-radius: 12px !important;
    }

    .cyber-modal .modal-header {
        background: linear-gradient(135deg, rgba(0, 212, 255, 0.1), rgba(139, 92, 246, 0.1)) !important;
        border-bottom: 1px solid rgba(0, 212, 255, 0.3) !important;
        color: #e2e8f0 !important;
    }

    .cyber-modal .modal-body {
        background: transparent !important;
        color: #e2e8f0 !important;
    }

    .cyber-modal .modal-footer {
        background: transparent !important;
        border-top: 1px solid rgba(255, 255, 255, 0.1) !important;
    }

    /* 科幻表单标签样式 */
    #userItemApp .cyber-form-label {
        color: #e2e8f0 !important;
        font-weight: 500 !important;
        margin-bottom: 0.5rem !important;
    }

    #userItemApp .cyber-form-group {
        margin-bottom: 1rem !important;
    }

    /* 科幻图标样式 */
    #userItemApp .cyber-icon {
        color: #00d4ff !important;
        font-size: 1.25rem !important;
    }

    #userItemApp .cyber-card-title {
        color: #e2e8f0 !important;
        margin: 0 !important;
        font-weight: 600 !important;
    }

    /* 道具图标样式 */
    #userItemApp .item-icon {
        width: 32px !important;
        height: 32px !important;
        border-radius: 8px !important;
        display: inline-flex !important;
        align-items: center !important;
        justify-content: center !important;
        margin-right: 0.5rem !important;
        font-size: 1rem !important;
    }

    #userItemApp .item-icon.consumable {
        background: linear-gradient(135deg, #10b981, #059669) !important;
        color: white !important;
    }

    #userItemApp .item-icon.equipment {
        background: linear-gradient(135deg, #3b82f6, #2563eb) !important;
        color: white !important;
    }

    #userItemApp .item-icon.material {
        background: linear-gradient(135deg, #f59e0b, #d97706) !important;
        color: white !important;
    }

    #userItemApp .item-icon.other {
        background: linear-gradient(135deg, #ef4444, #dc2626) !important;
        color: white !important;
    }

    /* 数量徽章样式 */
    #userItemApp .quantity-badge {
        background: rgba(0, 212, 255, 0.2) !important;
        color: #00d4ff !important;
        padding: 0.25rem 0.5rem !important;
        border-radius: 6px !important;
        font-weight: 600 !important;
        font-size: 0.8rem !important;
    }

    /* 用户信息样式 */
    #userItemApp .user-info {
        display: flex !important;
        align-items: center !important;
        gap: 0.5rem !important;
    }

    #userItemApp .user-avatar {
        width: 32px !important;
        height: 32px !important;
        border-radius: 50% !important;
        background: linear-gradient(135deg, #00d4ff, #8b5cf6) !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        color: white !important;
        font-weight: 600 !important;
        font-size: 0.9rem !important;
    }

    /* 自定义选择器样式 */
    #userItemApp .custom-select-container {
        position: relative !important;
    }

    #userItemApp .custom-select-arrow {
        position: absolute !important;
        right: 12px !important;
        top: 50% !important;
        transform: translateY(-50%) !important;
        color: #00d4ff !important;
        pointer-events: none !important;
    }

    #userItemApp .custom-select-dropdown {
        position: absolute !important;
        top: 100% !important;
        left: 0 !important;
        right: 0 !important;
        background: rgba(26, 26, 46, 0.95) !important;
        border: 1px solid rgba(0, 212, 255, 0.3) !important;
        border-radius: 8px !important;
        max-height: 300px !important;
        overflow-y: auto !important;
        z-index: 1000 !important;
        display: none !important;
    }

    #userItemApp .custom-select-dropdown.show {
        display: block !important;
    }

    #userItemApp .custom-select-option {
        padding: 0.75rem !important;
        cursor: pointer !important;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
        transition: all 0.3s ease !important;
    }

    #userItemApp .custom-select-option:hover {
        background: rgba(0, 212, 255, 0.1) !important;
    }

    #userItemApp .custom-select-option.selected {
        background: rgba(0, 212, 255, 0.2) !important;
        border-left: 3px solid #00d4ff !important;
    }

    #userItemApp .item-name {
        color: #e2e8f0 !important;
        font-weight: 500 !important;
    }

    #userItemApp .item-details {
        color: #94a3b8 !important;
        font-size: 0.8rem !important;
        margin-top: 0.25rem !important;
    }

    /* 科幻背景 */
    .cyber-bg {
        position: fixed !important;
        top: 0 !important;
        left: 0 !important;
        width: 100% !important;
        height: 100% !important;
        background: linear-gradient(135deg, #0a0a0f 0%, #1a1a2e 50%, #16213e 100%) !important;
        z-index: -1 !important;
    }

    .cyber-bg::before {
        content: '' !important;
        position: absolute !important;
        top: 0 !important;
        left: 0 !important;
        width: 100% !important;
        height: 100% !important;
        background-image:
            radial-gradient(circle at 20% 80%, rgba(0, 212, 255, 0.1) 0%, transparent 50%),
            radial-gradient(circle at 80% 20%, rgba(139, 92, 246, 0.1) 0%, transparent 50%) !important;
        animation: pulse 4s ease-in-out infinite alternate !important;
    }

    @@keyframes pulse {
        0% { opacity: 0.5; }
        100% { opacity: 1; }
    }
</style>

<!-- 科幻背景 -->
<div class="cyber-bg"></div>

<div class="container-fluid p-4">
    <div id="userItemApp">
        <!-- 科幻页面标题 -->
        <div class="cyber-card mb-4">
            <div class="cyber-card-header">
                <div class="cyber-icon">
                    <i class="fas fa-box"></i>
                </div>
                <h1 class="cyber-card-title">用户道具管理</h1>
            </div>
        </div>

        <!-- 科幻统计卡片 -->
        <div class="row mb-4" v-if="showStatistics">
            <div class="col-lg-3 col-6">
                <div class="cyber-stat-card stat-info">
                    <div class="inner">
                        <h3>{{ formatNumber(statistics.totalItems) || 0 }}</h3>
                        <p>道具总数</p>
                    </div>
                    <div class="icon">
                        <i class="fas fa-boxes"></i>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-6">
                <div class="cyber-stat-card stat-success">
                    <div class="inner">
                        <h3>{{ statistics.itemTypes || 0 }}</h3>
                        <p>道具种类</p>
                    </div>
                    <div class="icon">
                        <i class="fas fa-layer-group"></i>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-6">
                <div class="cyber-stat-card stat-warning">
                    <div class="inner">
                        <h3>{{ formatNumber(statistics.totalValue) || 0 }}</h3>
                        <p>道具总价值</p>
                    </div>
                    <div class="icon">
                        <i class="fas fa-coins"></i>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-6">
                <div class="cyber-stat-card stat-danger">
                    <div class="inner">
                        <h3>{{ formatNumber(statistics.orangeItems + statistics.purpleItems) || 0 }}</h3>
                        <p>稀有道具</p>
                    </div>
                    <div class="icon">
                        <i class="fas fa-gem"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- 科幻搜索条件 -->
        <div class="cyber-card">
            <div class="cyber-card-header">
                <div class="cyber-icon">
                    <i class="fas fa-search"></i>
                </div>
                <h5 class="cyber-card-title">查询条件</h5>
                <div class="ms-auto">
                    <button type="button" class="cyber-btn cyber-btn-info cyber-btn-sm" v-on:click="toggleStatistics">
                        <i class="fas fa-chart-bar"></i> {{ showStatistics ? '隐藏' : '显示' }}统计
                    </button>
                </div>
            </div>
            <div class="cyber-card-body">
                <div class="row">
                    <div class="col-md-3">
                        <div class="cyber-form-group">
                            <label class="cyber-form-label">用户</label>
                            <select class="cyber-form-control" v-model="queryForm.userId">
                                <option value="">全部用户</option>
                                <option v-for="user in userOptions" v-bind:key="user.value" v-bind:value="user.value">
                                    {{ user.label }}
                                </option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="cyber-form-group">
                            <label class="cyber-form-label">道具名称</label>
                            <input type="text" class="cyber-form-control" v-model="queryForm.itemName" placeholder="请输入道具名称">
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="cyber-form-group">
                            <label class="cyber-form-label">道具类型</label>
                            <select class="cyber-form-control" v-model="queryForm.itemType">
                                <option value="">全部</option>
                                <option value="weapon">武器</option>
                                <option value="armor">装备</option>
                                <option value="consumable">消耗品</option>
                                <option value="material">材料</option>
                                <option value="quest">任务物品</option>
                                <option value="other">其它</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="cyber-form-group">
                            <label class="cyber-form-label">道具品质</label>
                            <select class="cyber-form-control" v-model="queryForm.itemQuality">
                                <option value="">全部</option>
                                <option value="white">普通</option>
                                <option value="green">精良</option>
                                <option value="blue">稀有</option>
                                <option value="purple">史诗</option>
                                <option value="orange">传说</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="cyber-form-group">
                            <label class="cyber-form-label">&nbsp;</label>
                            <div class="d-flex gap-2">
                                <button type="button" class="cyber-btn" v-on:click="loadData">
                                    <i class="fas fa-search"></i> 查询
                                </button>
                                <button type="button" class="cyber-btn cyber-btn-outline" v-on:click="resetQuery">
                                    <i class="fas fa-redo"></i> 重置
                                </button>
                                <button type="button" class="cyber-btn cyber-btn-success" v-on:click="showAddModal">
                                    <i class="fas fa-plus"></i> 添加道具
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                </div>
            </div>

        <!-- 科幻数据列表 -->
        <div class="cyber-card">
            <div class="cyber-card-header">
                <div class="cyber-icon">
                    <i class="fas fa-list"></i>
                </div>
                <h5 class="cyber-card-title">道具列表</h5>
                <div class="ms-auto">
                    <span class="cyber-badge cyber-badge-info">
                        共 {{ totalCount }} 条记录
                    </span>
                </div>
            </div>
            <div class="cyber-card-body">
                <div class="cyber-table-container">
                    <table class="cyber-table">
                        <thead>
                            <tr>
                                <th>记录ID</th>
                                <th>用户信息</th>
                                <th>道具信息</th>
                                <th>道具品质</th>
                                <th>道具类型</th>
                                <th>数量</th>
                                <th>位置</th>
                                <th>序号</th>
                                <th>单价</th>
                                <th width="200">操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr v-if="loading">
                                <td colspan="10" class="text-center">
                                    <i class="fas fa-spinner fa-spin"></i> 加载中...
                                </td>
                            </tr>
                            <tr v-else-if="itemList.length === 0">
                                <td colspan="10" class="text-center">暂无数据</td>
                            </tr>
                            <tr v-else v-for="item in itemList" v-bind:key="item.id">
                                <td><span class="text-info fw-bold">{{ item.id }}</span></td>
                                <td>
                                    <div class="user-info">
                                        <div class="user-avatar">{{ item.username.charAt(0) }}</div>
                                        <div>
                                            <div><strong>{{ item.username }}</strong></div>
                                            <small class="text-muted">ID: {{ item.userId }}</small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="item-icon" v-bind:class="getItemTypeClass(item.itemType)">
                                            <i v-bind:class="getItemTypeIcon(item.itemType)"></i>
                                        </div>
                                        <div>
                                            <div><strong>{{ item.itemName }}</strong></div>
                                            <small class="text-muted">编号: {{ item.itemNo }}</small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <span class="cyber-badge" v-bind:class="getQualityClass(item.itemQuality)">{{ item.qualityText }}</span>
                                </td>
                                <td>{{ getTypeText(item.itemType) }}</td>
                                <td>
                                    <span class="quantity-badge">{{ formatNumber(item.itemCount) }}</span>
                                </td>
                                <td class="text-muted">{{ item.itemPos || '-' }}</td>
                                <td class="text-muted">{{ item.itemSeq }}</td>
                                <td><span class="text-warning fw-bold">{{ item.itemPrice || 0 }}</span></td>
                                <td>
                                    <div class="d-flex gap-1">
                                        <button type="button" class="cyber-btn cyber-btn-info cyber-btn-sm" v-on:click="showDetailModal(item)" title="查看详情">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button type="button" class="cyber-btn cyber-btn-warning cyber-btn-sm" v-on:click="showEditModal(item)" title="编辑">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button type="button" class="cyber-btn cyber-btn-danger cyber-btn-sm" v-on:click="deleteItem(item)" title="删除">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- 科幻分页 -->
                <div v-if="totalPages > 1" class="d-flex justify-content-between align-items-center mt-3">
                    <div class="text-muted">
                        显示第 {{ (currentPage - 1) * pageSize + 1 }} 到 {{ Math.min(currentPage * pageSize, totalCount) }} 条，共 {{ totalCount }} 条记录
                    </div>
                    <nav>
                        <ul class="cyber-pagination pagination">
                            <li class="page-item" v-bind:class="{ disabled: currentPage === 1 }">
                                <a href="#" class="page-link" v-on:click.prevent="changePage(currentPage - 1)">上一页</a>
                            </li>
                            <li class="page-item"
                                v-for="page in visiblePages"
                                v-bind:key="page"
                                v-bind:class="{ active: page === currentPage }">
                                <a href="#" class="page-link" v-on:click.prevent="changePage(page)">{{ page }}</a>
                            </li>
                            <li class="page-item" v-bind:class="{ disabled: currentPage === totalPages }">
                                <a href="#" class="page-link" v-on:click.prevent="changePage(currentPage + 1)">下一页</a>
                            </li>
                        </ul>
                    </nav>
                </div>
            </div>
        </div>

        <!-- 科幻添加道具模态框 -->
        <div class="modal fade cyber-modal" id="addModal" tabindex="-1" role="dialog">
            <div class="modal-dialog modal-lg" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="fas fa-plus me-2"></i>添加道具
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                        <div class="modal-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="cyber-form-group">
                                        <label class="cyber-form-label">用户</label>
                                        <select class="cyber-form-control" v-model="addForm.userId">
                                            <option value="">请选择用户</option>
                                            <option v-for="user in userOptions" v-bind:key="user.value" v-bind:value="user.value">
                                                {{ user.label }}
                                            </option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="cyber-form-group">
                                        <label class="cyber-form-label">道具</label>
                                        <div class="custom-select-container" v-bind:class="{ open: showItemDropdown }">
                                            <input
                                                type="text"
                                                class="cyber-form-control"
                                                v-bind:value="selectedItemDisplay"
                                                v-on:click="toggleItemDropdown"
                                                placeholder="请选择道具"
                                                readonly
                                            >
                                            <i class="fas fa-chevron-down custom-select-arrow"></i>
                                            <div class="custom-select-dropdown" v-bind:class="{ show: showItemDropdown }">
                                                <input
                                                    type="text"
                                                    class="cyber-form-control"
                                                    v-model="itemSearchText"
                                                    placeholder="搜索道具名称或编号..."
                                                    v-on:input="filterItems"
                                                >
                                                <div
                                                    v-for="item in filteredItems"
                                                    v-bind:key="item.itemNo"
                                                    class="custom-select-option"
                                                    v-bind:class="{ selected: addForm.itemNo == item.itemNo }"
                                                    v-on:click="selectItem(item)"
                                                >
                                                    <div class="item-name">{{ item.name }} ({{ item.itemNo }})</div>
                                                    <div class="item-details">
                                                        {{ getTypeText(item.type) }} | {{ getQualityText(item.quality) }} | 价格: {{ item.price || 0 }}
                                                    </div>
                                                </div>
                                                <div v-if="filteredItems.length === 0" class="custom-select-option" style="color: #64748b; text-align: center;">
                                                    未找到匹配的道具
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="cyber-form-group">
                                        <label class="cyber-form-label">数量</label>
                                        <input type="number" class="cyber-form-control" v-model="addForm.itemCount" placeholder="请输入数量" min="1">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="cyber-form-group">
                                        <label class="cyber-form-label">位置</label>
                                        <input type="number" class="cyber-form-control" v-model="addForm.itemPos" placeholder="道具位置（可选）">
                                    </div>
                                </div>
                            </div>
                            <!-- 道具预览 -->
                            <div class="row" v-if="selectedItemConfig">
                                <div class="col-md-12">
                                    <div class="alert alert-info">
                                        <h6><i class="fas fa-info-circle"></i> 道具信息</h6>
                                        <p><strong>名称：</strong>{{ selectedItemConfig.name }}</p>
                                        <p><strong>类型：</strong>{{ getTypeText(selectedItemConfig.type) }}</p>
                                        <p><strong>品质：</strong><span class="badge" v-bind:class="getQualityCssClass(selectedItemConfig.quality)">{{ getQualityText(selectedItemConfig.quality) }}</span></p>
                                        <p><strong>价格：</strong>{{ selectedItemConfig.price || 0 }}</p>
                                        <p v-if="selectedItemConfig.description"><strong>描述：</strong>{{ selectedItemConfig.description }}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="cyber-btn cyber-btn-secondary" data-bs-dismiss="modal">取消</button>
                            <button type="button" class="cyber-btn" v-on:click="addItem" v-bind:disabled="submitting">
                                {{ submitting ? '提交中...' : '确定' }}
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 科幻编辑道具模态框 -->
            <div class="modal fade cyber-modal" id="editModal" tabindex="-1" role="dialog">
                <div class="modal-dialog" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-edit me-2"></i>编辑道具
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <div class="cyber-form-group">
                                <label class="cyber-form-label">道具信息</label>
                                <input type="text" class="cyber-form-control" v-bind:value="editForm.itemName + ' (编号: ' + editForm.itemNo + ')'" readonly>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="cyber-form-group">
                                        <label class="cyber-form-label">数量</label>
                                        <input type="number" class="cyber-form-control" v-model="editForm.itemCount" placeholder="请输入数量" min="0">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="cyber-form-group">
                                        <label class="cyber-form-label">位置</label>
                                        <input type="number" class="cyber-form-control" v-model="editForm.itemPos" placeholder="道具位置（可选）">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="cyber-btn cyber-btn-secondary" data-bs-dismiss="modal">取消</button>
                            <button type="button" class="cyber-btn" v-on:click="updateItem" v-bind:disabled="submitting">
                                {{ submitting ? '提交中...' : '确定' }}
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 科幻道具详情模态框 -->
            <div class="modal fade cyber-modal" id="detailModal" tabindex="-1" role="dialog">
                <div class="modal-dialog modal-lg" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-info-circle me-2"></i>道具详情
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <div class="row" v-if="detailItem">
                                <div class="col-md-6">
                                    <table class="table table-sm">
                                        <tr>
                                            <td><strong>记录ID：</strong></td>
                                            <td>{{ detailItem.id }}</td>
                                        </tr>                                        <tr>
                                            <td><strong>用户：</strong></td>
                                            <td>{{ detailItem.username }} (ID: {{ detailItem.userId }})</td>
                                        </tr>
                                        <tr>
                                            <td><strong>道具编号：</strong></td>
                                            <td>{{ detailItem.itemNo }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>道具名称：</strong></td>
                                            <td>{{ detailItem.itemName }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>道具品质：</strong></td>
                                            <td><span class="badge" v-bind:class="detailItem.qualityCssClass">{{ detailItem.qualityText }}</span></td>
                                        </tr>
                                    </table>
                                </div>
                                <div class="col-md-6">
                                    <table class="table table-sm">
                                        <tr>
                                            <td><strong>道具类型：</strong></td>
                                            <td>{{ getTypeText(detailItem.itemType) }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>道具数量：</strong></td>
                                            <td><strong class="text-primary">{{ formatNumber(detailItem.itemCount) }}</strong></td>
                                        </tr>
                                        <tr>
                                            <td><strong>道具位置：</strong></td>
                                            <td>{{ detailItem.itemPos || '-' }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>道具序号：</strong></td>
                                            <td>{{ detailItem.itemSeq }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>单价：</strong></td>
                                            <td>{{ detailItem.itemPrice || 0 }}</td>
                                        </tr>
                                    </table>
                                </div>
                                <div class="col-md-12" v-if="detailItem.itemDescription">
                                    <div class="alert alert-info">
                                        <h6><i class="fas fa-info-circle"></i> 道具描述</h6>
                                        <p>{{ detailItem.itemDescription }}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="cyber-btn cyber-btn-secondary" data-bs-dismiss="modal">关闭</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    // 确保Vue和axios已加载
    if (typeof Vue === 'undefined') {
        console.error('Vue.js未加载！请检查_Layout.cshtml中的Vue.js引用');
        document.getElementById('userItemApp').innerHTML = '<div class="alert alert-danger">Vue.js加载失败，请刷新页面重试</div>';
    } else if (typeof axios === 'undefined') {
        console.error('axios未加载！请检查_Layout.cshtml中的axios引用');
        document.getElementById('userItemApp').innerHTML = '<div class="alert alert-danger">axios加载失败，请刷新页面重试</div>';
    } else {
        console.log('Vue和axios加载成功，开始初始化应用...');

        const { createApp } = Vue;

    const app = createApp({
        data() {
            return {
                // 查询条件
                queryForm: {
                    userId: '',
                    itemName: '',
                    itemType: '',
                    itemQuality: '',
                    page: 1,
                    pageSize: 15
                },
                // 道具列表
                itemList: [],
                totalCount: 0,
                totalPages: 0,
                currentPage: 1,
                pageSize: 15,
                loading: false,
                submitting: false,
                // 统计信息
                showStatistics: true,
                statistics: {},
                // 添加表单
                addForm: {
                    userId: '',
                    itemNo: '',
                    itemCount: 1,
                    itemPos: ''
                },
                // 编辑表单
                editForm: {
                    id: '',
                    itemName: '',
                    itemNo: '',
                    itemCount: 0,
                    itemPos: ''
                },
                // 详情信息
                detailItem: null,
                // 道具配置
                itemConfigs: [],
                selectedItemConfig: null,
                // 用户选项
                userOptions: [],
                // 道具选择下拉框相关
                showItemDropdown: false,
                itemSearchText: '',
                filteredItems: [],
                allItems: [] // 存储所有道具配置，用于过滤
            };
        },        computed: {
            // 计算可见的页码
            visiblePages() {
                const pages = [];
                const total = this.totalPages;
                const current = this.currentPage;
                
                let start = Math.max(1, current - 2);
                let end = Math.min(total, current + 2);
                
                if (end - start < 4) {
                    if (start === 1) {
                        end = Math.min(total, start + 4);
                    } else {
                        start = Math.max(1, end - 4);
                    }
                }
                
                for (let i = start; i <= end; i++) {
                    pages.push(i);
                }
                
                return pages;
            },
            // 道具选择下拉框的显示文本
            selectedItemDisplay() {
                if (!this.addForm.itemNo) {
                    return '请选择道具';
                }
                const config = this.itemConfigs.find(c => c.itemNo == this.addForm.itemNo);
                if (!config) {
                    return this.addForm.itemNo; // 如果没有找到，显示编号
                }
                return `${config.name} (${config.itemNo})`;
            }
        },
        async mounted() {
            await this.loadUserOptions();
            await this.loadItemConfigs();
            await this.loadStatistics();
            await this.loadData();
            this.allItems = this.itemConfigs; // 初始化所有道具配置
        },
        methods: {
            // 加载用户选项
            async loadUserOptions() {
                try {
                    const response = await axios.get('/UserPet/GetUserOptions');
                    if (response.data.code === 200) {
                        this.userOptions = response.data.data || [];
                    }
                } catch (error) {
                    console.error('加载用户选项失败：', error);
                }
            },

            // 加载道具配置
            async loadItemConfigs() {
                try {
                    const response = await axios.get('/UserItem/GetItemConfigs');
                    if (response.data.success) {
                        this.itemConfigs = response.data.data || [];
                        this.allItems = this.itemConfigs; // 更新所有道具配置
                    }
                } catch (error) {
                    console.error('加载道具配置失败：', error);
                }
            },

            // 加载统计信息
            async loadStatistics() {
                try {
                    const response = await axios.get('/UserItem/GetStatistics');
                    if (response.data.success) {
                        this.statistics = response.data.data || {};
                    }
                } catch (error) {
                    console.error('加载统计信息失败：', error);
                }
            },            // 切换统计显示
            toggleStatistics() {
                this.showStatistics = !this.showStatistics;
                if (this.showStatistics) {
                    this.loadStatistics();
                }
            },

            // 加载数据
            async loadData() {
                this.loading = true;
                try {
                    const response = await axios.post('/UserItem/GetList', {
                        userId: this.queryForm.userId || null,
                        itemName: this.queryForm.itemName || null,
                        itemType: this.queryForm.itemType || null,
                        itemQuality: this.queryForm.itemQuality || null,
                        page: this.currentPage,
                        pageSize: this.pageSize
                    });
                    
                    if (response.data.success) {
                        const result = response.data.data || {};
                        this.itemList = result.data || [];
                        this.totalCount = result.totalCount || 0;
                        this.totalPages = result.totalPages || 0;
                        this.currentPage = result.pageIndex || 1;
                    } else {
                        alert(response.data.message || '加载数据失败');
                    }
                } catch (error) {
                    console.error('加载数据失败：', error);
                    alert('加载数据失败，请重试');
                } finally {
                    this.loading = false;
                }
            },

            // 重置查询条件
            resetQuery() {
                this.queryForm = {
                    userId: '',
                    itemName: '',
                    itemType: '',
                    itemQuality: '',
                    page: 1,
                    pageSize: 15
                };
                this.currentPage = 1;
                this.loadData();
            },

            // 换页
            changePage(page) {
                if (page < 1 || page > this.totalPages || page === this.currentPage) {
                    return;
                }
                this.currentPage = page;
                this.loadData();
            },            // 显示添加模态框
            showAddModal() {
                this.addForm = {
                    userId: '',
                    itemNo: '',
                    itemCount: 1,
                    itemPos: ''
                };
                this.selectedItemConfig = null;
                this.showItemDropdown = false; // 关闭下拉框
                this.itemSearchText = ''; // 清空搜索文本
                this.filteredItems = []; // 清空过滤列表
                
                const modal = new bootstrap.Modal(document.getElementById('addModal'));
                modal.show();
                
                // 添加点击外部关闭下拉框的事件监听
                setTimeout(() => {
                    document.addEventListener('click', this.handleOutsideClick);
                }, 100);
                
                // 监听模态框关闭事件
                document.getElementById('addModal').addEventListener('hidden.bs.modal', () => {
                    document.removeEventListener('click', this.handleOutsideClick);
                }, { once: true });
            },

            // 处理点击外部关闭下拉框
            handleOutsideClick(event) {
                const container = event.target.closest('.custom-select-container');
                if (!container) {
                    this.showItemDropdown = false;
                }
            },

            // 显示编辑模态框
            showEditModal(item) {
                this.editForm = {
                    id: item.id,
                    itemName: item.itemName,
                    itemNo: item.itemNo,
                    itemCount: item.itemCount,
                    itemPos: item.itemPos || ''
                };
                new bootstrap.Modal(document.getElementById('editModal')).show();
            },

            // 显示详情模态框
            showDetailModal(item) {
                this.detailItem = item;
                new bootstrap.Modal(document.getElementById('detailModal')).show();
            },

            // 添加道具
            async addItem() {
                if (!this.validateAddForm()) {
                    return;
                }
                
                this.submitting = true;
                try {
                    const response = await axios.post('/UserItem/Add', this.addForm);
                    
                    if (response.data.success) {
                        alert('道具添加成功');
                        bootstrap.Modal.getInstance(document.getElementById('addModal')).hide();
                        this.loadData();
                        this.loadStatistics();
                    } else {
                        alert(response.data.message || '添加失败');
                    }
                } catch (error) {
                    console.error('添加道具失败：', error);
                    alert('添加道具失败，请重试');
                } finally {
                    this.submitting = false;
                }
            },            // 更新道具
            async updateItem() {
                if (!this.validateEditForm()) {
                    return;
                }
                
                this.submitting = true;
                try {
                    const response = await axios.post('/UserItem/Update', this.editForm);
                    
                    if (response.data.success) {
                        alert('道具更新成功');
                        bootstrap.Modal.getInstance(document.getElementById('editModal')).hide();
                        this.loadData();
                        this.loadStatistics();
                    } else {
                        alert(response.data.message || '更新失败');
                    }
                } catch (error) {
                    console.error('更新道具失败：', error);
                    alert('更新道具失败，请重试');
                } finally {
                    this.submitting = false;
                }
            },

            // 删除道具
            async deleteItem(item) {
                if (!confirm(`确定要删除用户 "${item.username}" 的道具 "${item.itemName}" 吗？\n\n数量：${this.formatNumber(item.itemCount)}`)) {
                    return;
                }

                try {
                    const response = await axios.post('/UserItem/Delete', { id: item.id });
                    
                    if (response.data.success) {
                        alert('道具删除成功');
                        this.loadData();
                        this.loadStatistics();
                    } else {
                        alert(response.data.message || '删除失败');
                    }
                } catch (error) {
                    console.error('删除道具失败：', error);
                    alert('删除道具失败，请重试');
                }
            },

            // 验证添加表单
            validateAddForm() {
                if (!this.addForm.userId) {
                    alert('请选择用户');
                    return false;
                }
                if (!this.addForm.itemNo) {
                    alert('请选择道具');
                    return false;
                }
                if (!this.addForm.itemCount || this.addForm.itemCount < 1) {
                    alert('请输入正确的道具数量');
                    return false;
                }
                return true;
            },            // 验证编辑表单
            validateEditForm() {
                if (this.editForm.itemCount < 0) {
                    alert('道具数量不能为负数');
                    return false;
                }
                return true;
            },

            // 格式化数字
            formatNumber(num) {
                if (num == null) return '0';
                return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
            },

            // 获取类型文本
            getTypeText(type) {
                const typeMap = {
                    'weapon': '武器',
                    'armor': '装备', 
                    'consumable': '消耗品',
                    'material': '材料',
                    'quest': '任务物品',
                    'other': '其它'
                };
                return typeMap[type] || type || '未知';
            },

            // 获取品质文本
            getQualityText(quality) {
                const qualityMap = {
                    'white': '普通',
                    'green': '精良',
                    'blue': '稀有',
                    'purple': '史诗',
                    'orange': '传说'
                };
                return qualityMap[quality] || quality || '未知';
            },

            // 获取品质CSS类
            getQualityCssClass(quality) {
                const classMap = {
                    'white': 'badge-secondary',
                    'green': 'badge-success',
                    'blue': 'badge-info',
                    'purple': 'badge-primary',
                    'orange': 'badge-warning'
                };
                return classMap[quality] || 'badge-secondary';
            },

            // 获取科幻品质CSS类
            getQualityClass(quality) {
                const classMap = {
                    'white': 'cyber-badge',
                    'green': 'cyber-badge-success',
                    'blue': 'cyber-badge-info',
                    'purple': 'cyber-badge',
                    'orange': 'cyber-badge-warning'
                };
                return classMap[quality] || 'cyber-badge';
            },

            // 获取道具类型图标
            getItemTypeIcon(type) {
                const iconMap = {
                    'weapon': 'fas fa-sword',
                    'armor': 'fas fa-shield-alt',
                    'consumable': 'fas fa-flask',
                    'material': 'fas fa-cube',
                    'quest': 'fas fa-scroll',
                    'other': 'fas fa-box'
                };
                return iconMap[type] || 'fas fa-box';
            },

            // 获取道具类型CSS类
            getItemTypeClass(type) {
                const classMap = {
                    'weapon': 'equipment',
                    'armor': 'equipment',
                    'consumable': 'consumable',
                    'material': 'material',
                    'quest': 'other',
                    'other': 'other'
                };
                return classMap[type] || 'other';
            },

            // 切换道具下拉框的显示状态
            toggleItemDropdown() {
                this.showItemDropdown = !this.showItemDropdown;
                if (this.showItemDropdown) {
                    this.filteredItems = this.allItems; // 显示所有道具
                }
            },

            // 过滤道具列表
            filterItems() {
                const searchText = this.itemSearchText.toLowerCase();
                this.filteredItems = this.allItems.filter(item => 
                    item.name.toLowerCase().includes(searchText) || 
                    item.itemNo.toString().includes(searchText)
                );
            },

            // 选择道具
            selectItem(item) {
                this.addForm.itemNo = item.itemNo;
                this.selectedItemConfig = item;
                this.showItemDropdown = false;
                this.itemSearchText = '';
                this.filteredItems = [];
            }
        }
    });

    // 挂载Vue应用
    try {
        app.mount('#userItemApp');
        console.log('用户道具管理应用挂载成功');
    } catch (error) {
        console.error('Vue应用挂载失败:', error);
        document.getElementById('userItemApp').innerHTML = '<div class="alert alert-danger">应用初始化失败: ' + error.message + '</div>';
    }

    } // 结束Vue和axios检查
</script>