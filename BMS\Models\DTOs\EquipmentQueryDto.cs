namespace BMS.Models.DTOs
{
    /// <summary>
    /// 装备查询DTO
    /// </summary>
    public class EquipmentQueryDto
    {
        /// <summary>
        /// 装备ID（支持模糊查询）
        /// </summary>
        public string? EquipId { get; set; }

        /// <summary>
        /// 装备名称（支持模糊查询）
        /// </summary>
        public string? Name { get; set; }

        /// <summary>
        /// 装备类型ID
        /// </summary>
        public string? EquipTypeId { get; set; }

        /// <summary>
        /// 五行属性
        /// </summary>
        public string? Element { get; set; }

        /// <summary>
        /// 五行限制
        /// </summary>
        public string? ElementLimit { get; set; }

        /// <summary>
        /// 页码
        /// </summary>
        public int Page { get; set; } = 1;

        /// <summary>
        /// 每页数量
        /// </summary>
        public int PageSize { get; set; } = 10;

        /// <summary>
        /// 套装ID
        /// </summary>
        public string? SuitId { get; set; }

        /// <summary>
        /// 主属性
        /// </summary>
        public string? MainAttr { get; set; }

        /// <summary>
        /// 副属性
        /// </summary>
        public string? SubAttr { get; set; }
    }
} 