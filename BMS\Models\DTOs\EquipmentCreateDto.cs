using System.ComponentModel.DataAnnotations;

namespace BMS.Models.DTOs
{
    /// <summary>
    /// 创建装备DTO
    /// </summary>
    public class EquipmentCreateDto
    {
        /// <summary>
        /// 装备唯一ID
        /// </summary>
        [Required(ErrorMessage = "装备ID不能为空")]
        [StringLength(20, ErrorMessage = "装备ID长度不能超过20个字符")]
        public string EquipId { get; set; } = string.Empty;

        /// <summary>
        /// 装备类ID
        /// </summary>
        [Required(ErrorMessage = "装备类ID不能为空")]
        [StringLength(20, ErrorMessage = "装备类ID长度不能超过20个字符")]
        public string ClassId { get; set; } = string.Empty;

        /// <summary>
        /// 装备名称
        /// </summary>
        [Required(ErrorMessage = "装备名称不能为空")]
        [StringLength(50, ErrorMessage = "装备名称长度不能超过50个字符")]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 装备图标
        /// </summary>
        [StringLength(100, ErrorMessage = "装备图标路径长度不能超过100个字符")]
        public string? Icon { get; set; }

        /// <summary>
        /// 装备类型ID
        /// </summary>
        [Required(ErrorMessage = "装备类型不能为空")]
        [StringLength(20, ErrorMessage = "装备类型ID长度不能超过20个字符")]
        public string EquipTypeId { get; set; } = string.Empty;

        /// <summary>
        /// 五行属性
        /// </summary>
        [StringLength(10, ErrorMessage = "五行属性长度不能超过10个字符")]
        public string? Element { get; set; }

        /// <summary>
        /// 扩展槽位
        /// </summary>
        [Range(0, 99, ErrorMessage = "扩展槽位必须在0-99之间")]
        public int Slot { get; set; } = 0;

        /// <summary>
        /// 强化等级
        /// </summary>
        [Range(0, 99, ErrorMessage = "强化等级必须在0-99之间")]
        public int StrengthenLevel { get; set; } = 0;

        /// <summary>
        /// 攻击加成
        /// </summary>
        [Range(0, 9999.99, ErrorMessage = "攻击加成必须在0-9999.99之间")]
        public decimal Atk { get; set; } = 0;

        /// <summary>
        /// 命中加成
        /// </summary>
        [Range(0, 9999.99, ErrorMessage = "命中加成必须在0-9999.99之间")]
        public decimal Hit { get; set; } = 0;

        /// <summary>
        /// 防御加成
        /// </summary>
        [Range(0, 9999.99, ErrorMessage = "防御加成必须在0-9999.99之间")]
        public decimal Def { get; set; } = 0;

        /// <summary>
        /// 速度加成
        /// </summary>
        [Range(0, 9999.99, ErrorMessage = "速度加成必须在0-9999.99之间")]
        public decimal Spd { get; set; } = 0;

        /// <summary>
        /// 闪避加成
        /// </summary>
        [Range(0, 9999.99, ErrorMessage = "闪避加成必须在0-9999.99之间")]
        public decimal Dodge { get; set; } = 0;

        /// <summary>
        /// 生命加成
        /// </summary>
        [Range(0, 9999999.99, ErrorMessage = "生命加成必须在0-9999999.99之间")]
        public decimal Hp { get; set; } = 0;

        /// <summary>
        /// 魔法加成
        /// </summary>
        [Range(0, 9999999.99, ErrorMessage = "魔法加成必须在0-9999999.99之间")]
        public decimal Mp { get; set; } = 0;

        /// <summary>
        /// 加深加成
        /// </summary>
        [Range(0, 9999.99, ErrorMessage = "加深加成必须在0-9999.99之间")]
        public decimal Deepen { get; set; } = 0;

        /// <summary>
        /// 抵消加成
        /// </summary>
        [Range(0, 9999.99, ErrorMessage = "抵消加成必须在0-9999.99之间")]
        public decimal Offset { get; set; } = 0;

        /// <summary>
        /// 吸血加成
        /// </summary>
        [Range(0, 9999.99, ErrorMessage = "吸血加成必须在0-9999.99之间")]
        public decimal Vamp { get; set; } = 0;

        /// <summary>
        /// 吸魔加成
        /// </summary>
        [Range(0, 9999.99, ErrorMessage = "吸魔加成必须在0-9999.99之间")]
        public decimal VampMp { get; set; } = 0;

        /// <summary>
        /// 装备说明
        /// </summary>
        [StringLength(255, ErrorMessage = "装备说明长度不能超过255个字符")]
        public string? Description { get; set; }

        /// <summary>
        /// 主属性
        /// </summary>
        [StringLength(20, ErrorMessage = "主属性长度不能超过20个字符")]
        public string? MainAttr { get; set; }

        /// <summary>
        /// 五行限制
        /// </summary>
        [StringLength(10, ErrorMessage = "五行限制长度不能超过10个字符")]
        public string? ElementLimit { get; set; }

        /// <summary>
        /// 套装ID
        /// </summary>
        [StringLength(50, ErrorMessage = "套装ID长度不能超过50个字符")]
        public string? SuitId { get; set; }

        /// <summary>
        /// 主属性值
        /// </summary>
        [StringLength(100, ErrorMessage = "主属性值长度不能超过100个字符")]
        public string? MainAttrValue { get; set; }

        /// <summary>
        /// 副属性
        /// </summary>
        [StringLength(100, ErrorMessage = "副属性长度不能超过100个字符")]
        public string? SubAttr { get; set; }

        /// <summary>
        /// 副属性值
        /// </summary>
        [StringLength(100, ErrorMessage = "副属性值长度不能超过100个字符")]
        public string? SubAttrValue { get; set; }

        /// <summary>
        /// 装备名称（详情表）
        /// </summary>
        [StringLength(50, ErrorMessage = "装备名称长度不能超过50个字符")]
        public string? EquipName { get; set; }
    }
} 