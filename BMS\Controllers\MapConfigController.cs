using Microsoft.AspNetCore.Mvc;
using BMS.Models.DTOs;
using BMS.Models.Common;
using BMS.Services.Interfaces;
using System.ComponentModel.DataAnnotations;

namespace BMS.Controllers
{
    /// <summary>
    /// 地图配置控制器
    /// </summary>
    public class MapConfigController : Controller
    {
        private readonly IMapConfigService _mapConfigService;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="mapConfigService">地图配置服务</param>
        public MapConfigController(IMapConfigService mapConfigService)
        {
            _mapConfigService = mapConfigService;
        }

        /// <summary>
        /// 地图配置管理页面
        /// </summary>
        /// <returns>页面视图</returns>
        public IActionResult Index()
        {
            return View();
        }

        /// <summary>
        /// 创建地图配置页面
        /// </summary>
        /// <returns>页面视图</returns>
        public IActionResult Create()
        {
            return View();
        }

        /// <summary>
        /// 编辑地图配置页面
        /// </summary>
        /// <param name="id">地图配置ID</param>
        /// <returns>页面视图</returns>
        public async Task<IActionResult> Edit(int id)
        {
            var mapConfig = await _mapConfigService.GetByIdAsync(id);
            if (mapConfig == null)
            {
                return NotFound();
            }
            return View(mapConfig);
        }

        /// <summary>
        /// 分页获取地图配置列表
        /// </summary>
        /// <param name="queryDto">查询条件</param>
        /// <returns>分页结果</returns>
        [HttpGet]
        public async Task<IActionResult> GetPagedList([FromQuery] MapConfigQueryDto queryDto)
        {
            try
            {
                var result = await _mapConfigService.GetPagedListAsync(queryDto);
                return Json(new
                {
                    success = true,
                    data = result.Data,
                    totalCount = result.TotalCount,
                    totalPages = result.TotalPages,
                    page = result.PageIndex,
                    pageSize = result.PageSize
                });
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = $"获取地图配置列表失败：{ex.Message}" });
            }
        }

        /// <summary>
        /// 根据ID获取地图配置
        /// </summary>
        /// <param name="id">地图配置ID</param>
        /// <returns>地图配置信息</returns>
        [HttpGet]
        public async Task<IActionResult> GetById(int id)
        {
            try
            {
                var mapConfig = await _mapConfigService.GetByIdAsync(id);
                if (mapConfig == null)
                {
                    return Json(new { success = false, message = "地图配置不存在" });
                }
                return Json(new { success = true, data = mapConfig });
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = $"获取地图配置失败：{ex.Message}" });
            }
        }

        /// <summary>
        /// 创建地图配置
        /// </summary>
        /// <param name="createDto">创建DTO</param>
        /// <returns>操作结果</returns>
        [HttpPost]
        public async Task<IActionResult> Create([FromBody] MapConfigCreateDto createDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    var errors = ModelState.Values
                        .SelectMany(v => v.Errors)
                        .Select(e => e.ErrorMessage)
                        .ToList();
                    return Json(new { success = false, message = string.Join(", ", errors) });
                }

                var result = await _mapConfigService.CreateAsync(createDto);
                return Json(new { success = result.Success, message = result.Message });
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = $"创建地图配置失败：{ex.Message}" });
            }
        }

        /// <summary>
        /// 更新地图配置
        /// </summary>
        /// <param name="updateDto">更新DTO</param>
        /// <returns>操作结果</returns>
        [HttpPost]
        public async Task<IActionResult> Update([FromBody] MapConfigUpdateDto updateDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    var errors = ModelState.Values
                        .SelectMany(v => v.Errors)
                        .Select(e => e.ErrorMessage)
                        .ToList();
                    return Json(new { success = false, message = string.Join(", ", errors) });
                }

                var result = await _mapConfigService.UpdateAsync(updateDto);
                return Json(new { success = result.Success, message = result.Message });
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = $"更新地图配置失败：{ex.Message}" });
            }
        }

        /// <summary>
        /// 删除地图配置
        /// </summary>
        /// <param name="dto">删除地图配置DTO</param>
        /// <returns>操作结果</returns>
        [HttpPost]
        public async Task<IActionResult> Delete([FromBody] MapConfigDeleteDto dto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    var errors = ModelState.Values
                        .SelectMany(v => v.Errors)
                        .Select(e => e.ErrorMessage)
                        .ToList();
                    return Json(new { success = false, message = string.Join(", ", errors) });
                }

                var result = await _mapConfigService.DeleteAsync(dto.Id);
                return Json(new { success = result.Success, message = result.Message });
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = $"删除地图配置失败：{ex.Message}" });
            }
        }

        /// <summary>
        /// 检查地图ID是否存在
        /// </summary>
        /// <param name="mapId">地图ID</param>
        /// <param name="id">排除的ID（编辑时使用）</param>
        /// <returns>检查结果</returns>
        [HttpGet]
        public async Task<IActionResult> CheckMapIdExists(int mapId, int? id = null)
        {
            try
            {
                var exists = await _mapConfigService.CheckMapIdExistsAsync(mapId, id);
                return Json(new { exists });
            }
            catch (Exception ex)
            {
                return Json(new { exists = false, message = $"检查失败：{ex.Message}" });
            }
        }

        /// <summary>
        /// 获取所有地图配置（用于下拉选择）
        /// </summary>
        /// <returns>地图配置列表</returns>
        [HttpGet]
        public async Task<IActionResult> GetAll()
        {
            try
            {
                var result = await _mapConfigService.GetAllAsync();
                return Json(new { success = true, data = result });
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = $"获取地图配置列表失败：{ex.Message}" });
            }
        }
    }
} 