using System.ComponentModel.DataAnnotations;

namespace BMS.Models.DTOs
{
    /// <summary>
    /// 创建技能DTO
    /// </summary>
    public class SkillCreateDto
    {
        /// <summary>
        /// 技能唯一标识
        /// </summary>
        [Required(ErrorMessage = "技能ID不能为空")]
        [StringLength(20, ErrorMessage = "技能ID长度不能超过20个字符")]
        public string SkillId { get; set; } = string.Empty;

        /// <summary>
        /// 技能名称
        /// </summary>
        [Required(ErrorMessage = "技能名称不能为空")]
        [StringLength(50, ErrorMessage = "技能名称长度不能超过50个字符")]
        public string SkillName { get; set; } = string.Empty;

        /// <summary>
        /// 技能百分比
        /// </summary>
        [Required(ErrorMessage = "技能百分比不能为空")]
        [Range(0, 999.99, ErrorMessage = "技能百分比必须在0-999.99之间")]
        public decimal SkillPercent { get; set; }

        /// <summary>
        /// 技能效果类型
        /// </summary>
        public string? EffectType { get; set; }

        /// <summary>
        /// 效果数值（JSON格式）
        /// </summary>
        public string? EffectValue { get; set; }

        /// <summary>
        /// 耗蓝量
        /// </summary>
        [Range(0, int.MaxValue, ErrorMessage = "耗蓝量不能为负数")]
        public int ManaCost { get; set; }

        /// <summary>
        /// BUFF信息（JSON格式）
        /// </summary>
        public string? BuffInfo { get; set; }

        /// <summary>
        /// 五行限制
        /// </summary>
        public string? ElementLimit { get; set; }
    }
} 