[2025-09-04 00:01:28.729 +08:00 INF] BMS: 🚀 应用程序启动，日志系统已配置
[2025-09-04 00:01:28.762 +08:00 DBG] BMS: 🔧 调试级别日志测试
[2025-09-04 00:01:28.815 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 00:01:28.819 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 00:01:28.910 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 00:01:29.918 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 00:01:29.930 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 00:01:29.932 +08:00 INF] BMS: ✅ DbContext 初始化成功，日志文件位置: logs/
[2025-09-04 00:01:30.867 +08:00 WRN] Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware: Failed to determine the https port for redirect.
[2025-09-04 00:01:33.187 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 00:01:33.188 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 00:01:33.192 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 00:01:33.238 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 00:01:33.239 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 00:01:33.312 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 00:01:33.313 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 00:01:33.315 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 00:01:33.315 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 00:01:33.316 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 00:01:33.319 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 00:01:33.358 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 00:01:33.363 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 00:01:33.531 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `user`  
[2025-09-04 00:01:33.585 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 00:01:33.751 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 00:01:33.777 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `user`  
[2025-09-04 00:01:34.033 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `user`  
[2025-09-04 00:01:34.044 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`username`,`password`,`nickname`,`sex`,`vip_level`,`vip_score`,`gold`,`yuanbao`,`crystal`,`reg_time`,`title`,`main_pet_id`,`main_pet_name`,`create_time`,`evolution_cd`,`synthesis_cd`,`nirvana_cd`,`money`,`supreme_vip`,`prop_capacity`,`pasture_capacity`,`dragon_ball_exp`,`dragon_ball_level`,`status`,`prestige`,`auto_battle_count`,`auto_nirvana_count`,`monster_count`,`next_battle`,`open_maps`,`star_vip`,`star_vip_expire`,`task_helper_enabled`,`title_attributes`,`soul_pet`,`hell_floor`,`tower_floor` FROM `user`    ORDER BY `create_time` DESC LIMIT 0,1000
[2025-09-04 00:01:34.082 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `user`  
[2025-09-04 00:01:34.088 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  
[2025-09-04 00:01:34.093 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`username`,`password`,`nickname`,`sex`,`vip_level`,`vip_score`,`gold`,`yuanbao`,`crystal`,`reg_time`,`title`,`main_pet_id`,`main_pet_name`,`create_time`,`evolution_cd`,`synthesis_cd`,`nirvana_cd`,`money`,`supreme_vip`,`prop_capacity`,`pasture_capacity`,`dragon_ball_exp`,`dragon_ball_level`,`status`,`prestige`,`auto_battle_count`,`auto_nirvana_count`,`monster_count`,`next_battle`,`open_maps`,`star_vip`,`star_vip_expire`,`task_helper_enabled`,`title_attributes`,`soul_pet`,`hell_floor`,`tower_floor` FROM `user`    ORDER BY `create_time` DESC LIMIT 0,1000
[2025-09-04 00:01:34.138 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  
[2025-09-04 00:01:34.143 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment_type`  
[2025-09-04 00:01:34.156 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 00:01:34.165 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 00:01:34.169 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 00:01:34.192 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment_type`  
[2025-09-04 00:01:34.197 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `user_equipment`  
[2025-09-04 00:01:34.218 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 00:01:34.219 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 00:01:34.229 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `equip_type_id` AS `Value` , `type_name` AS `Label`  FROM `equipment_type`  
[2025-09-04 00:01:34.238 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `user_equipment`  
[2025-09-04 00:01:34.266 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `equip_type_id` AS `Value` , `type_name` AS `Label`  FROM `equipment_type`  
[2025-09-04 00:01:34.277 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 00:01:34.278 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 00:01:34.281 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 00:01:34.310 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM `user_equipment` `ue` Left JOIN `user` `u` ON ( `ue`.`user_id` = `u`.`id` )  Left JOIN `equipment` `e` ON ( `ue`.`equip_id` = `e`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )   WHERE ( 1 = 1 )   | 参数: [@MethodConst0=, @MethodConst1=, @MethodConst2=, @MethodConst3=, @MethodConst4=0, @MethodConst5=0, @MethodConst6=False, @MethodConst7=2025/9/4 0:01:34]
[2025-09-04 00:01:34.320 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 00:01:34.322 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 00:01:34.327 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `equip_id` AS `Value` , `name` AS `Label` , `name` AS `Name` , `icon` AS `Icon` , `equip_type_id` AS `EquipTypeId`  FROM `equipment`  
[2025-09-04 00:01:34.362 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM `user_equipment` `ue` Left JOIN `user` `u` ON ( `ue`.`user_id` = `u`.`id` )  Left JOIN `equipment` `e` ON ( `ue`.`equip_id` = `e`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )   WHERE ( 1 = 1 )  
[2025-09-04 00:01:34.367 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ue`.`id` AS `Id` , `ue`.`user_id` AS `UserId` , IFNULL(`u`.`username`,@MethodConst8) AS `UserName` , `ue`.`equip_id` AS `EquipId` , `ue`.`name` AS `Name` , IFNULL(`ue`.`icon`,@MethodConst9) AS `Icon` , IFNULL(`et`.`equip_type_id`,@MethodConst10) AS `EquipTypeId` , IFNULL(`et`.`type_name`,@MethodConst11) AS `EquipTypeName` , IFNULL(`ue`.`strengthen_level`,@MethodConst12) AS `StrengthenLevel` , IFNULL(`ue`.`slot`,@MethodConst13) AS `Slot` , `ue`.`position` AS `Position` , IFNULL(CAST(`ue`.`is_equipped` as SIGNED),@MethodConst14) AS `IsEquipped` , IFNULL(`ue`.`create_time`,@MethodConst15) AS `CreateTime`  FROM `user_equipment` `ue` Left JOIN `user` `u` ON ( `ue`.`user_id` = `u`.`id` )  Left JOIN `equipment` `e` ON ( `ue`.`equip_id` = `e`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )    WHERE ( 1 = 1 )   ORDER BY `ue`.`create_time` DESC LIMIT 0,10 | 参数: [@MethodConst0=, @MethodConst1=, @MethodConst2=, @MethodConst3=, @MethodConst4=0, @MethodConst5=0, @MethodConst6=False, @MethodConst7=2025/9/4 0:01:34, @MethodConst8=, @MethodConst9=, @MethodConst10=, @MethodConst11=, @MethodConst12=0, @MethodConst13=0, @MethodConst14=False, @MethodConst15=2025/9/4 0:01:34]
[2025-09-04 00:01:34.368 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `equip_id` AS `Value` , `name` AS `Label` , `name` AS `Name` , `icon` AS `Icon` , `equip_type_id` AS `EquipTypeId`  FROM `equipment`  
[2025-09-04 00:01:34.418 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ue`.`id` AS `Id` , `ue`.`user_id` AS `UserId` , IFNULL(`u`.`username`,@MethodConst8) AS `UserName` , `ue`.`equip_id` AS `EquipId` , `ue`.`name` AS `Name` , IFNULL(`ue`.`icon`,@MethodConst9) AS `Icon` , IFNULL(`et`.`equip_type_id`,@MethodConst10) AS `EquipTypeId` , IFNULL(`et`.`type_name`,@MethodConst11) AS `EquipTypeName` , IFNULL(`ue`.`strengthen_level`,@MethodConst12) AS `StrengthenLevel` , IFNULL(`ue`.`slot`,@MethodConst13) AS `Slot` , `ue`.`position` AS `Position` , IFNULL(CAST(`ue`.`is_equipped` as SIGNED),@MethodConst14) AS `IsEquipped` , IFNULL(`ue`.`create_time`,@MethodConst15) AS `CreateTime`  FROM `user_equipment` `ue` Left JOIN `user` `u` ON ( `ue`.`user_id` = `u`.`id` )  Left JOIN `equipment` `e` ON ( `ue`.`equip_id` = `e`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )    WHERE ( 1 = 1 )   ORDER BY `ue`.`create_time` DESC LIMIT 0,10
[2025-09-04 00:01:34.433 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1 | 参数: [@equip_id0=2020092809, @MethodConst1=0, @MethodConst2=0, @MethodConst3=0, @MethodConst4=0, @MethodConst5=0, @MethodConst6=0, @MethodConst7=0, @MethodConst8=0, @MethodConst9=0, @MethodConst10=0, @MethodConst11=0, @MethodConst12=, @MethodConst13=, @MethodConst14=, @MethodConst15=0, @MethodConst16=0, @MethodConst17=0, @MethodConst18=0, @MethodConst19=0, @MethodConst20=0, @MethodConst21=0, @MethodConst22=0, @MethodConst23=0, @MethodConst24=0, @MethodConst25=0, @MethodConst26=, @MethodConst27=, @MethodConst28=]
[2025-09-04 00:01:34.477 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1
[2025-09-04 00:01:34.485 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1 | 参数: [@equip_id0=789035, @MethodConst1=0, @MethodConst2=0, @MethodConst3=0, @MethodConst4=0, @MethodConst5=0, @MethodConst6=0, @MethodConst7=0, @MethodConst8=0, @MethodConst9=0, @MethodConst10=0, @MethodConst11=0, @MethodConst12=, @MethodConst13=, @MethodConst14=, @MethodConst15=0, @MethodConst16=0, @MethodConst17=0, @MethodConst18=0, @MethodConst19=0, @MethodConst20=0, @MethodConst21=0, @MethodConst22=0, @MethodConst23=0, @MethodConst24=0, @MethodConst25=0, @MethodConst26=, @MethodConst27=, @MethodConst28=]
[2025-09-04 00:01:34.528 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1
[2025-09-04 00:01:34.532 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1 | 参数: [@equip_id0=2020092802, @MethodConst1=0, @MethodConst2=0, @MethodConst3=0, @MethodConst4=0, @MethodConst5=0, @MethodConst6=0, @MethodConst7=0, @MethodConst8=0, @MethodConst9=0, @MethodConst10=0, @MethodConst11=0, @MethodConst12=, @MethodConst13=, @MethodConst14=, @MethodConst15=0, @MethodConst16=0, @MethodConst17=0, @MethodConst18=0, @MethodConst19=0, @MethodConst20=0, @MethodConst21=0, @MethodConst22=0, @MethodConst23=0, @MethodConst24=0, @MethodConst25=0, @MethodConst26=, @MethodConst27=, @MethodConst28=]
[2025-09-04 00:01:34.575 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1
[2025-09-04 00:01:34.580 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1 | 参数: [@equip_id0=789019, @MethodConst1=0, @MethodConst2=0, @MethodConst3=0, @MethodConst4=0, @MethodConst5=0, @MethodConst6=0, @MethodConst7=0, @MethodConst8=0, @MethodConst9=0, @MethodConst10=0, @MethodConst11=0, @MethodConst12=, @MethodConst13=, @MethodConst14=, @MethodConst15=0, @MethodConst16=0, @MethodConst17=0, @MethodConst18=0, @MethodConst19=0, @MethodConst20=0, @MethodConst21=0, @MethodConst22=0, @MethodConst23=0, @MethodConst24=0, @MethodConst25=0, @MethodConst26=, @MethodConst27=, @MethodConst28=]
[2025-09-04 00:01:34.623 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1
[2025-09-04 00:01:34.628 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1 | 参数: [@equip_id0=2018102703, @MethodConst1=0, @MethodConst2=0, @MethodConst3=0, @MethodConst4=0, @MethodConst5=0, @MethodConst6=0, @MethodConst7=0, @MethodConst8=0, @MethodConst9=0, @MethodConst10=0, @MethodConst11=0, @MethodConst12=, @MethodConst13=, @MethodConst14=, @MethodConst15=0, @MethodConst16=0, @MethodConst17=0, @MethodConst18=0, @MethodConst19=0, @MethodConst20=0, @MethodConst21=0, @MethodConst22=0, @MethodConst23=0, @MethodConst24=0, @MethodConst25=0, @MethodConst26=, @MethodConst27=, @MethodConst28=]
[2025-09-04 00:01:34.671 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1
[2025-09-04 00:01:34.675 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1 | 参数: [@equip_id0=2016121602, @MethodConst1=0, @MethodConst2=0, @MethodConst3=0, @MethodConst4=0, @MethodConst5=0, @MethodConst6=0, @MethodConst7=0, @MethodConst8=0, @MethodConst9=0, @MethodConst10=0, @MethodConst11=0, @MethodConst12=, @MethodConst13=, @MethodConst14=, @MethodConst15=0, @MethodConst16=0, @MethodConst17=0, @MethodConst18=0, @MethodConst19=0, @MethodConst20=0, @MethodConst21=0, @MethodConst22=0, @MethodConst23=0, @MethodConst24=0, @MethodConst25=0, @MethodConst26=, @MethodConst27=, @MethodConst28=]
[2025-09-04 00:01:34.718 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1
[2025-09-04 00:01:34.722 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1 | 参数: [@equip_id0=2019042702, @MethodConst1=0, @MethodConst2=0, @MethodConst3=0, @MethodConst4=0, @MethodConst5=0, @MethodConst6=0, @MethodConst7=0, @MethodConst8=0, @MethodConst9=0, @MethodConst10=0, @MethodConst11=0, @MethodConst12=, @MethodConst13=, @MethodConst14=, @MethodConst15=0, @MethodConst16=0, @MethodConst17=0, @MethodConst18=0, @MethodConst19=0, @MethodConst20=0, @MethodConst21=0, @MethodConst22=0, @MethodConst23=0, @MethodConst24=0, @MethodConst25=0, @MethodConst26=, @MethodConst27=, @MethodConst28=]
[2025-09-04 00:01:34.771 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1
[2025-09-04 00:01:34.774 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1 | 参数: [@equip_id0=2021010102, @MethodConst1=0, @MethodConst2=0, @MethodConst3=0, @MethodConst4=0, @MethodConst5=0, @MethodConst6=0, @MethodConst7=0, @MethodConst8=0, @MethodConst9=0, @MethodConst10=0, @MethodConst11=0, @MethodConst12=, @MethodConst13=, @MethodConst14=, @MethodConst15=0, @MethodConst16=0, @MethodConst17=0, @MethodConst18=0, @MethodConst19=0, @MethodConst20=0, @MethodConst21=0, @MethodConst22=0, @MethodConst23=0, @MethodConst24=0, @MethodConst25=0, @MethodConst26=, @MethodConst27=, @MethodConst28=]
[2025-09-04 00:01:34.817 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1
[2025-09-04 00:01:34.820 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1 | 参数: [@equip_id0=789011, @MethodConst1=0, @MethodConst2=0, @MethodConst3=0, @MethodConst4=0, @MethodConst5=0, @MethodConst6=0, @MethodConst7=0, @MethodConst8=0, @MethodConst9=0, @MethodConst10=0, @MethodConst11=0, @MethodConst12=, @MethodConst13=, @MethodConst14=, @MethodConst15=0, @MethodConst16=0, @MethodConst17=0, @MethodConst18=0, @MethodConst19=0, @MethodConst20=0, @MethodConst21=0, @MethodConst22=0, @MethodConst23=0, @MethodConst24=0, @MethodConst25=0, @MethodConst26=, @MethodConst27=, @MethodConst28=]
[2025-09-04 00:01:34.861 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1
[2025-09-04 00:01:34.865 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1 | 参数: [@equip_id0=789009, @MethodConst1=0, @MethodConst2=0, @MethodConst3=0, @MethodConst4=0, @MethodConst5=0, @MethodConst6=0, @MethodConst7=0, @MethodConst8=0, @MethodConst9=0, @MethodConst10=0, @MethodConst11=0, @MethodConst12=, @MethodConst13=, @MethodConst14=, @MethodConst15=0, @MethodConst16=0, @MethodConst17=0, @MethodConst18=0, @MethodConst19=0, @MethodConst20=0, @MethodConst21=0, @MethodConst22=0, @MethodConst23=0, @MethodConst24=0, @MethodConst25=0, @MethodConst26=, @MethodConst27=, @MethodConst28=]
[2025-09-04 00:01:34.909 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1
[2025-09-04 00:01:40.161 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 00:01:40.523 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 00:01:40.602 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 00:01:40.692 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 00:01:40.746 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 00:01:40.755 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ue`.`id` AS `Id` , `ue`.`user_id` AS `UserId` , IFNULL(`u`.`username`,@MethodConst9) AS `UserName` , `ue`.`equip_id` AS `EquipId` , `ue`.`name` AS `Name` , IFNULL(`ue`.`icon`,@MethodConst10) AS `Icon` , IFNULL(`et`.`equip_type_id`,@MethodConst11) AS `EquipTypeId` , IFNULL(`et`.`type_name`,@MethodConst12) AS `EquipTypeName` , IFNULL(`ue`.`strengthen_level`,@MethodConst13) AS `StrengthenLevel` , IFNULL(`ue`.`slot`,@MethodConst14) AS `Slot` , `ue`.`position` AS `Position` , IFNULL(CAST(`ue`.`is_equipped` as SIGNED),@MethodConst15) AS `IsEquipped` , IFNULL(`ue`.`create_time`,@MethodConst16) AS `CreateTime`  FROM `user_equipment` `ue` Left JOIN `user` `u` ON ( `ue`.`user_id` = `u`.`id` )  Left JOIN `equipment` `e` ON ( `ue`.`equip_id` = `e`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )    WHERE ( `ue`.`id` = @id0 )   LIMIT 0,1 | 参数: [@id0=20, @MethodConst1=, @MethodConst2=, @MethodConst3=, @MethodConst4=, @MethodConst5=0, @MethodConst6=0, @MethodConst7=False, @MethodConst8=2025/9/4 0:01:40, @MethodConst9=, @MethodConst10=, @MethodConst11=, @MethodConst12=, @MethodConst13=0, @MethodConst14=0, @MethodConst15=False, @MethodConst16=2025/9/4 0:01:40]
[2025-09-04 00:01:40.804 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ue`.`id` AS `Id` , `ue`.`user_id` AS `UserId` , IFNULL(`u`.`username`,@MethodConst9) AS `UserName` , `ue`.`equip_id` AS `EquipId` , `ue`.`name` AS `Name` , IFNULL(`ue`.`icon`,@MethodConst10) AS `Icon` , IFNULL(`et`.`equip_type_id`,@MethodConst11) AS `EquipTypeId` , IFNULL(`et`.`type_name`,@MethodConst12) AS `EquipTypeName` , IFNULL(`ue`.`strengthen_level`,@MethodConst13) AS `StrengthenLevel` , IFNULL(`ue`.`slot`,@MethodConst14) AS `Slot` , `ue`.`position` AS `Position` , IFNULL(CAST(`ue`.`is_equipped` as SIGNED),@MethodConst15) AS `IsEquipped` , IFNULL(`ue`.`create_time`,@MethodConst16) AS `CreateTime`  FROM `user_equipment` `ue` Left JOIN `user` `u` ON ( `ue`.`user_id` = `u`.`id` )  Left JOIN `equipment` `e` ON ( `ue`.`equip_id` = `e`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )    WHERE ( `ue`.`id` = @id0 )   LIMIT 0,1
[2025-09-04 00:01:40.810 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1 | 参数: [@equip_id0=2020092809, @MethodConst1=0, @MethodConst2=0, @MethodConst3=0, @MethodConst4=0, @MethodConst5=0, @MethodConst6=0, @MethodConst7=0, @MethodConst8=0, @MethodConst9=0, @MethodConst10=0, @MethodConst11=0, @MethodConst12=, @MethodConst13=, @MethodConst14=, @MethodConst15=0, @MethodConst16=0, @MethodConst17=0, @MethodConst18=0, @MethodConst19=0, @MethodConst20=0, @MethodConst21=0, @MethodConst22=0, @MethodConst23=0, @MethodConst24=0, @MethodConst25=0, @MethodConst26=, @MethodConst27=, @MethodConst28=]
[2025-09-04 00:01:40.881 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1
[2025-09-04 00:01:41.278 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 00:01:41.314 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 00:01:41.360 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 00:01:41.413 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 00:01:41.415 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 00:01:41.419 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ue`.`id` AS `Id` , `ue`.`user_id` AS `UserId` , IFNULL(`u`.`username`,@MethodConst9) AS `UserName` , `ue`.`equip_id` AS `EquipId` , `ue`.`name` AS `Name` , IFNULL(`ue`.`icon`,@MethodConst10) AS `Icon` , IFNULL(`et`.`equip_type_id`,@MethodConst11) AS `EquipTypeId` , IFNULL(`et`.`type_name`,@MethodConst12) AS `EquipTypeName` , IFNULL(`ue`.`strengthen_level`,@MethodConst13) AS `StrengthenLevel` , IFNULL(`ue`.`slot`,@MethodConst14) AS `Slot` , `ue`.`position` AS `Position` , IFNULL(CAST(`ue`.`is_equipped` as SIGNED),@MethodConst15) AS `IsEquipped` , IFNULL(`ue`.`create_time`,@MethodConst16) AS `CreateTime`  FROM `user_equipment` `ue` Left JOIN `user` `u` ON ( `ue`.`user_id` = `u`.`id` )  Left JOIN `equipment` `e` ON ( `ue`.`equip_id` = `e`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )    WHERE ( `ue`.`id` = @id0 )   LIMIT 0,1 | 参数: [@id0=20, @MethodConst1=, @MethodConst2=, @MethodConst3=, @MethodConst4=, @MethodConst5=0, @MethodConst6=0, @MethodConst7=False, @MethodConst8=2025/9/4 0:01:41, @MethodConst9=, @MethodConst10=, @MethodConst11=, @MethodConst12=, @MethodConst13=0, @MethodConst14=0, @MethodConst15=False, @MethodConst16=2025/9/4 0:01:41]
[2025-09-04 00:01:41.464 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ue`.`id` AS `Id` , `ue`.`user_id` AS `UserId` , IFNULL(`u`.`username`,@MethodConst9) AS `UserName` , `ue`.`equip_id` AS `EquipId` , `ue`.`name` AS `Name` , IFNULL(`ue`.`icon`,@MethodConst10) AS `Icon` , IFNULL(`et`.`equip_type_id`,@MethodConst11) AS `EquipTypeId` , IFNULL(`et`.`type_name`,@MethodConst12) AS `EquipTypeName` , IFNULL(`ue`.`strengthen_level`,@MethodConst13) AS `StrengthenLevel` , IFNULL(`ue`.`slot`,@MethodConst14) AS `Slot` , `ue`.`position` AS `Position` , IFNULL(CAST(`ue`.`is_equipped` as SIGNED),@MethodConst15) AS `IsEquipped` , IFNULL(`ue`.`create_time`,@MethodConst16) AS `CreateTime`  FROM `user_equipment` `ue` Left JOIN `user` `u` ON ( `ue`.`user_id` = `u`.`id` )  Left JOIN `equipment` `e` ON ( `ue`.`equip_id` = `e`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )    WHERE ( `ue`.`id` = @id0 )   LIMIT 0,1
[2025-09-04 00:01:41.467 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1 | 参数: [@equip_id0=2020092809, @MethodConst1=0, @MethodConst2=0, @MethodConst3=0, @MethodConst4=0, @MethodConst5=0, @MethodConst6=0, @MethodConst7=0, @MethodConst8=0, @MethodConst9=0, @MethodConst10=0, @MethodConst11=0, @MethodConst12=, @MethodConst13=, @MethodConst14=, @MethodConst15=0, @MethodConst16=0, @MethodConst17=0, @MethodConst18=0, @MethodConst19=0, @MethodConst20=0, @MethodConst21=0, @MethodConst22=0, @MethodConst23=0, @MethodConst24=0, @MethodConst25=0, @MethodConst26=, @MethodConst27=, @MethodConst28=]
[2025-09-04 00:01:41.511 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1
[2025-09-04 00:01:42.340 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 00:01:42.375 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 00:01:42.388 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 00:01:42.432 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 00:01:42.462 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 00:01:42.479 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ue`.`id` AS `Id` , `ue`.`user_id` AS `UserId` , IFNULL(`u`.`username`,@MethodConst9) AS `UserName` , `ue`.`equip_id` AS `EquipId` , `ue`.`name` AS `Name` , IFNULL(`ue`.`icon`,@MethodConst10) AS `Icon` , IFNULL(`et`.`equip_type_id`,@MethodConst11) AS `EquipTypeId` , IFNULL(`et`.`type_name`,@MethodConst12) AS `EquipTypeName` , IFNULL(`ue`.`strengthen_level`,@MethodConst13) AS `StrengthenLevel` , IFNULL(`ue`.`slot`,@MethodConst14) AS `Slot` , `ue`.`position` AS `Position` , IFNULL(CAST(`ue`.`is_equipped` as SIGNED),@MethodConst15) AS `IsEquipped` , IFNULL(`ue`.`create_time`,@MethodConst16) AS `CreateTime`  FROM `user_equipment` `ue` Left JOIN `user` `u` ON ( `ue`.`user_id` = `u`.`id` )  Left JOIN `equipment` `e` ON ( `ue`.`equip_id` = `e`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )    WHERE ( `ue`.`id` = @id0 )   LIMIT 0,1 | 参数: [@id0=20, @MethodConst1=, @MethodConst2=, @MethodConst3=, @MethodConst4=, @MethodConst5=0, @MethodConst6=0, @MethodConst7=False, @MethodConst8=2025/9/4 0:01:42, @MethodConst9=, @MethodConst10=, @MethodConst11=, @MethodConst12=, @MethodConst13=0, @MethodConst14=0, @MethodConst15=False, @MethodConst16=2025/9/4 0:01:42]
[2025-09-04 00:01:42.528 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ue`.`id` AS `Id` , `ue`.`user_id` AS `UserId` , IFNULL(`u`.`username`,@MethodConst9) AS `UserName` , `ue`.`equip_id` AS `EquipId` , `ue`.`name` AS `Name` , IFNULL(`ue`.`icon`,@MethodConst10) AS `Icon` , IFNULL(`et`.`equip_type_id`,@MethodConst11) AS `EquipTypeId` , IFNULL(`et`.`type_name`,@MethodConst12) AS `EquipTypeName` , IFNULL(`ue`.`strengthen_level`,@MethodConst13) AS `StrengthenLevel` , IFNULL(`ue`.`slot`,@MethodConst14) AS `Slot` , `ue`.`position` AS `Position` , IFNULL(CAST(`ue`.`is_equipped` as SIGNED),@MethodConst15) AS `IsEquipped` , IFNULL(`ue`.`create_time`,@MethodConst16) AS `CreateTime`  FROM `user_equipment` `ue` Left JOIN `user` `u` ON ( `ue`.`user_id` = `u`.`id` )  Left JOIN `equipment` `e` ON ( `ue`.`equip_id` = `e`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )    WHERE ( `ue`.`id` = @id0 )   LIMIT 0,1
[2025-09-04 00:01:42.568 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1 | 参数: [@equip_id0=2020092809, @MethodConst1=0, @MethodConst2=0, @MethodConst3=0, @MethodConst4=0, @MethodConst5=0, @MethodConst6=0, @MethodConst7=0, @MethodConst8=0, @MethodConst9=0, @MethodConst10=0, @MethodConst11=0, @MethodConst12=, @MethodConst13=, @MethodConst14=, @MethodConst15=0, @MethodConst16=0, @MethodConst17=0, @MethodConst18=0, @MethodConst19=0, @MethodConst20=0, @MethodConst21=0, @MethodConst22=0, @MethodConst23=0, @MethodConst24=0, @MethodConst25=0, @MethodConst26=, @MethodConst27=, @MethodConst28=]
[2025-09-04 00:01:42.611 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1
[2025-09-04 00:01:43.533 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 00:01:44.019 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 00:01:44.054 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 00:01:44.114 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 00:01:44.127 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 00:01:44.135 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ue`.`id` AS `Id` , `ue`.`user_id` AS `UserId` , IFNULL(`u`.`username`,@MethodConst9) AS `UserName` , `ue`.`equip_id` AS `EquipId` , `ue`.`name` AS `Name` , IFNULL(`ue`.`icon`,@MethodConst10) AS `Icon` , IFNULL(`et`.`equip_type_id`,@MethodConst11) AS `EquipTypeId` , IFNULL(`et`.`type_name`,@MethodConst12) AS `EquipTypeName` , IFNULL(`ue`.`strengthen_level`,@MethodConst13) AS `StrengthenLevel` , IFNULL(`ue`.`slot`,@MethodConst14) AS `Slot` , `ue`.`position` AS `Position` , IFNULL(CAST(`ue`.`is_equipped` as SIGNED),@MethodConst15) AS `IsEquipped` , IFNULL(`ue`.`create_time`,@MethodConst16) AS `CreateTime`  FROM `user_equipment` `ue` Left JOIN `user` `u` ON ( `ue`.`user_id` = `u`.`id` )  Left JOIN `equipment` `e` ON ( `ue`.`equip_id` = `e`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )    WHERE ( `ue`.`id` = @id0 )   LIMIT 0,1 | 参数: [@id0=20, @MethodConst1=, @MethodConst2=, @MethodConst3=, @MethodConst4=, @MethodConst5=0, @MethodConst6=0, @MethodConst7=False, @MethodConst8=2025/9/4 0:01:44, @MethodConst9=, @MethodConst10=, @MethodConst11=, @MethodConst12=, @MethodConst13=0, @MethodConst14=0, @MethodConst15=False, @MethodConst16=2025/9/4 0:01:44]
[2025-09-04 00:01:44.193 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ue`.`id` AS `Id` , `ue`.`user_id` AS `UserId` , IFNULL(`u`.`username`,@MethodConst9) AS `UserName` , `ue`.`equip_id` AS `EquipId` , `ue`.`name` AS `Name` , IFNULL(`ue`.`icon`,@MethodConst10) AS `Icon` , IFNULL(`et`.`equip_type_id`,@MethodConst11) AS `EquipTypeId` , IFNULL(`et`.`type_name`,@MethodConst12) AS `EquipTypeName` , IFNULL(`ue`.`strengthen_level`,@MethodConst13) AS `StrengthenLevel` , IFNULL(`ue`.`slot`,@MethodConst14) AS `Slot` , `ue`.`position` AS `Position` , IFNULL(CAST(`ue`.`is_equipped` as SIGNED),@MethodConst15) AS `IsEquipped` , IFNULL(`ue`.`create_time`,@MethodConst16) AS `CreateTime`  FROM `user_equipment` `ue` Left JOIN `user` `u` ON ( `ue`.`user_id` = `u`.`id` )  Left JOIN `equipment` `e` ON ( `ue`.`equip_id` = `e`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )    WHERE ( `ue`.`id` = @id0 )   LIMIT 0,1
[2025-09-04 00:01:44.199 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1 | 参数: [@equip_id0=2020092809, @MethodConst1=0, @MethodConst2=0, @MethodConst3=0, @MethodConst4=0, @MethodConst5=0, @MethodConst6=0, @MethodConst7=0, @MethodConst8=0, @MethodConst9=0, @MethodConst10=0, @MethodConst11=0, @MethodConst12=, @MethodConst13=, @MethodConst14=, @MethodConst15=0, @MethodConst16=0, @MethodConst17=0, @MethodConst18=0, @MethodConst19=0, @MethodConst20=0, @MethodConst21=0, @MethodConst22=0, @MethodConst23=0, @MethodConst24=0, @MethodConst25=0, @MethodConst26=, @MethodConst27=, @MethodConst28=]
[2025-09-04 00:01:44.243 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1
[2025-09-04 11:15:31.565 +08:00 INF] BMS: 🚀 应用程序启动，日志系统已配置
[2025-09-04 11:15:31.593 +08:00 DBG] BMS: 🔧 调试级别日志测试
[2025-09-04 11:15:31.609 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 11:15:31.611 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 11:15:31.706 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 11:15:32.312 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 11:15:32.322 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 11:15:32.323 +08:00 INF] BMS: ✅ DbContext 初始化成功，日志文件位置: logs/
[2025-09-04 11:15:33.461 +08:00 WRN] Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware: Failed to determine the https port for redirect.
[2025-09-04 11:15:37.081 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 11:15:37.085 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 11:15:37.091 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 11:15:37.157 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 11:15:37.160 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 11:15:37.269 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 11:15:37.272 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 11:15:37.494 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 11:15:37.626 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 11:15:37.678 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 11:15:37.680 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 11:15:37.761 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 11:15:37.763 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 11:15:37.858 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `user`  
[2025-09-04 11:15:37.923 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `user`  
[2025-09-04 11:15:37.944 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`username`,`password`,`nickname`,`sex`,`vip_level`,`vip_score`,`gold`,`yuanbao`,`crystal`,`reg_time`,`title`,`main_pet_id`,`main_pet_name`,`create_time`,`evolution_cd`,`synthesis_cd`,`nirvana_cd`,`money`,`supreme_vip`,`prop_capacity`,`pasture_capacity`,`dragon_ball_exp`,`dragon_ball_level`,`status`,`prestige`,`auto_battle_count`,`auto_nirvana_count`,`monster_count`,`next_battle`,`open_maps`,`star_vip`,`star_vip_expire`,`task_helper_enabled`,`title_attributes`,`soul_pet`,`hell_floor`,`tower_floor` FROM `user`    ORDER BY `create_time` DESC LIMIT 0,1000
[2025-09-04 11:15:37.972 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 11:15:37.974 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 11:15:38.007 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `user`  
[2025-09-04 11:15:38.009 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`username`,`password`,`nickname`,`sex`,`vip_level`,`vip_score`,`gold`,`yuanbao`,`crystal`,`reg_time`,`title`,`main_pet_id`,`main_pet_name`,`create_time`,`evolution_cd`,`synthesis_cd`,`nirvana_cd`,`money`,`supreme_vip`,`prop_capacity`,`pasture_capacity`,`dragon_ball_exp`,`dragon_ball_level`,`status`,`prestige`,`auto_battle_count`,`auto_nirvana_count`,`monster_count`,`next_battle`,`open_maps`,`star_vip`,`star_vip_expire`,`task_helper_enabled`,`title_attributes`,`soul_pet`,`hell_floor`,`tower_floor` FROM `user`    ORDER BY `create_time` DESC LIMIT 0,1000
[2025-09-04 11:15:38.050 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 11:15:38.052 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `user`  
[2025-09-04 11:15:38.054 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 11:15:38.056 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 11:15:38.058 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  
[2025-09-04 11:15:38.105 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 11:15:38.107 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 11:15:38.116 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `equip_type_id` AS `Value` , `type_name` AS `Label`  FROM `equipment_type`  
[2025-09-04 11:15:38.116 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  
[2025-09-04 11:15:38.122 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment_type`  
[2025-09-04 11:15:38.165 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `equip_type_id` AS `Value` , `type_name` AS `Label`  FROM `equipment_type`  
[2025-09-04 11:15:38.187 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment_type`  
[2025-09-04 11:15:38.189 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 11:15:38.190 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `user_equipment`  
[2025-09-04 11:15:38.190 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 11:15:38.193 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 11:15:38.237 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 11:15:38.246 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `user_equipment`  
[2025-09-04 11:15:38.273 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 11:15:38.296 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `equip_id` AS `Value` , `name` AS `Label` , `name` AS `Name` , `icon` AS `Icon` , `equip_type_id` AS `EquipTypeId`  FROM `equipment`  
[2025-09-04 11:15:38.358 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `equip_id` AS `Value` , `name` AS `Label` , `name` AS `Name` , `icon` AS `Icon` , `equip_type_id` AS `EquipTypeId`  FROM `equipment`  
[2025-09-04 11:15:38.360 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM `user_equipment` `ue` Left JOIN `user` `u` ON ( `ue`.`user_id` = `u`.`id` )  Left JOIN `equipment` `e` ON ( `ue`.`equip_id` = `e`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )   WHERE ( 1 = 1 )   | 参数: [@MethodConst0=, @MethodConst1=, @MethodConst2=, @MethodConst3=, @MethodConst4=0, @MethodConst5=0, @MethodConst6=False, @MethodConst7=2025/9/4 11:15:38]
[2025-09-04 11:15:38.422 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM `user_equipment` `ue` Left JOIN `user` `u` ON ( `ue`.`user_id` = `u`.`id` )  Left JOIN `equipment` `e` ON ( `ue`.`equip_id` = `e`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )   WHERE ( 1 = 1 )  
[2025-09-04 11:15:38.425 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ue`.`id` AS `Id` , `ue`.`user_id` AS `UserId` , IFNULL(`u`.`username`,@MethodConst8) AS `UserName` , `ue`.`equip_id` AS `EquipId` , `ue`.`name` AS `Name` , IFNULL(`ue`.`icon`,@MethodConst9) AS `Icon` , IFNULL(`et`.`equip_type_id`,@MethodConst10) AS `EquipTypeId` , IFNULL(`et`.`type_name`,@MethodConst11) AS `EquipTypeName` , IFNULL(`ue`.`strengthen_level`,@MethodConst12) AS `StrengthenLevel` , IFNULL(`ue`.`slot`,@MethodConst13) AS `Slot` , `ue`.`position` AS `Position` , IFNULL(CAST(`ue`.`is_equipped` as SIGNED),@MethodConst14) AS `IsEquipped` , IFNULL(`ue`.`create_time`,@MethodConst15) AS `CreateTime`  FROM `user_equipment` `ue` Left JOIN `user` `u` ON ( `ue`.`user_id` = `u`.`id` )  Left JOIN `equipment` `e` ON ( `ue`.`equip_id` = `e`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )    WHERE ( 1 = 1 )   ORDER BY `ue`.`create_time` DESC LIMIT 0,10 | 参数: [@MethodConst0=, @MethodConst1=, @MethodConst2=, @MethodConst3=, @MethodConst4=0, @MethodConst5=0, @MethodConst6=False, @MethodConst7=2025/9/4 11:15:38, @MethodConst8=, @MethodConst9=, @MethodConst10=, @MethodConst11=, @MethodConst12=0, @MethodConst13=0, @MethodConst14=False, @MethodConst15=2025/9/4 11:15:38]
[2025-09-04 11:15:38.497 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ue`.`id` AS `Id` , `ue`.`user_id` AS `UserId` , IFNULL(`u`.`username`,@MethodConst8) AS `UserName` , `ue`.`equip_id` AS `EquipId` , `ue`.`name` AS `Name` , IFNULL(`ue`.`icon`,@MethodConst9) AS `Icon` , IFNULL(`et`.`equip_type_id`,@MethodConst10) AS `EquipTypeId` , IFNULL(`et`.`type_name`,@MethodConst11) AS `EquipTypeName` , IFNULL(`ue`.`strengthen_level`,@MethodConst12) AS `StrengthenLevel` , IFNULL(`ue`.`slot`,@MethodConst13) AS `Slot` , `ue`.`position` AS `Position` , IFNULL(CAST(`ue`.`is_equipped` as SIGNED),@MethodConst14) AS `IsEquipped` , IFNULL(`ue`.`create_time`,@MethodConst15) AS `CreateTime`  FROM `user_equipment` `ue` Left JOIN `user` `u` ON ( `ue`.`user_id` = `u`.`id` )  Left JOIN `equipment` `e` ON ( `ue`.`equip_id` = `e`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )    WHERE ( 1 = 1 )   ORDER BY `ue`.`create_time` DESC LIMIT 0,10
[2025-09-04 11:15:38.514 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1 | 参数: [@equip_id0=2020092809, @MethodConst1=0, @MethodConst2=0, @MethodConst3=0, @MethodConst4=0, @MethodConst5=0, @MethodConst6=0, @MethodConst7=0, @MethodConst8=0, @MethodConst9=0, @MethodConst10=0, @MethodConst11=0, @MethodConst12=, @MethodConst13=, @MethodConst14=, @MethodConst15=0, @MethodConst16=0, @MethodConst17=0, @MethodConst18=0, @MethodConst19=0, @MethodConst20=0, @MethodConst21=0, @MethodConst22=0, @MethodConst23=0, @MethodConst24=0, @MethodConst25=0, @MethodConst26=, @MethodConst27=, @MethodConst28=]
[2025-09-04 11:15:38.574 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1
[2025-09-04 11:15:38.580 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1 | 参数: [@equip_id0=789035, @MethodConst1=0, @MethodConst2=0, @MethodConst3=0, @MethodConst4=0, @MethodConst5=0, @MethodConst6=0, @MethodConst7=0, @MethodConst8=0, @MethodConst9=0, @MethodConst10=0, @MethodConst11=0, @MethodConst12=, @MethodConst13=, @MethodConst14=, @MethodConst15=0, @MethodConst16=0, @MethodConst17=0, @MethodConst18=0, @MethodConst19=0, @MethodConst20=0, @MethodConst21=0, @MethodConst22=0, @MethodConst23=0, @MethodConst24=0, @MethodConst25=0, @MethodConst26=, @MethodConst27=, @MethodConst28=]
[2025-09-04 11:15:38.642 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1
[2025-09-04 11:15:38.645 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1 | 参数: [@equip_id0=2020092802, @MethodConst1=0, @MethodConst2=0, @MethodConst3=0, @MethodConst4=0, @MethodConst5=0, @MethodConst6=0, @MethodConst7=0, @MethodConst8=0, @MethodConst9=0, @MethodConst10=0, @MethodConst11=0, @MethodConst12=, @MethodConst13=, @MethodConst14=, @MethodConst15=0, @MethodConst16=0, @MethodConst17=0, @MethodConst18=0, @MethodConst19=0, @MethodConst20=0, @MethodConst21=0, @MethodConst22=0, @MethodConst23=0, @MethodConst24=0, @MethodConst25=0, @MethodConst26=, @MethodConst27=, @MethodConst28=]
[2025-09-04 11:15:38.704 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1
[2025-09-04 11:15:38.709 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1 | 参数: [@equip_id0=789019, @MethodConst1=0, @MethodConst2=0, @MethodConst3=0, @MethodConst4=0, @MethodConst5=0, @MethodConst6=0, @MethodConst7=0, @MethodConst8=0, @MethodConst9=0, @MethodConst10=0, @MethodConst11=0, @MethodConst12=, @MethodConst13=, @MethodConst14=, @MethodConst15=0, @MethodConst16=0, @MethodConst17=0, @MethodConst18=0, @MethodConst19=0, @MethodConst20=0, @MethodConst21=0, @MethodConst22=0, @MethodConst23=0, @MethodConst24=0, @MethodConst25=0, @MethodConst26=, @MethodConst27=, @MethodConst28=]
[2025-09-04 11:15:38.771 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1
[2025-09-04 11:15:38.799 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1 | 参数: [@equip_id0=2018102703, @MethodConst1=0, @MethodConst2=0, @MethodConst3=0, @MethodConst4=0, @MethodConst5=0, @MethodConst6=0, @MethodConst7=0, @MethodConst8=0, @MethodConst9=0, @MethodConst10=0, @MethodConst11=0, @MethodConst12=, @MethodConst13=, @MethodConst14=, @MethodConst15=0, @MethodConst16=0, @MethodConst17=0, @MethodConst18=0, @MethodConst19=0, @MethodConst20=0, @MethodConst21=0, @MethodConst22=0, @MethodConst23=0, @MethodConst24=0, @MethodConst25=0, @MethodConst26=, @MethodConst27=, @MethodConst28=]
[2025-09-04 11:15:38.863 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1
[2025-09-04 11:15:38.867 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1 | 参数: [@equip_id0=2016121602, @MethodConst1=0, @MethodConst2=0, @MethodConst3=0, @MethodConst4=0, @MethodConst5=0, @MethodConst6=0, @MethodConst7=0, @MethodConst8=0, @MethodConst9=0, @MethodConst10=0, @MethodConst11=0, @MethodConst12=, @MethodConst13=, @MethodConst14=, @MethodConst15=0, @MethodConst16=0, @MethodConst17=0, @MethodConst18=0, @MethodConst19=0, @MethodConst20=0, @MethodConst21=0, @MethodConst22=0, @MethodConst23=0, @MethodConst24=0, @MethodConst25=0, @MethodConst26=, @MethodConst27=, @MethodConst28=]
[2025-09-04 11:15:38.926 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1
[2025-09-04 11:15:38.941 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1 | 参数: [@equip_id0=2019042702, @MethodConst1=0, @MethodConst2=0, @MethodConst3=0, @MethodConst4=0, @MethodConst5=0, @MethodConst6=0, @MethodConst7=0, @MethodConst8=0, @MethodConst9=0, @MethodConst10=0, @MethodConst11=0, @MethodConst12=, @MethodConst13=, @MethodConst14=, @MethodConst15=0, @MethodConst16=0, @MethodConst17=0, @MethodConst18=0, @MethodConst19=0, @MethodConst20=0, @MethodConst21=0, @MethodConst22=0, @MethodConst23=0, @MethodConst24=0, @MethodConst25=0, @MethodConst26=, @MethodConst27=, @MethodConst28=]
[2025-09-04 11:15:39.019 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1
[2025-09-04 11:15:39.024 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1 | 参数: [@equip_id0=2021010102, @MethodConst1=0, @MethodConst2=0, @MethodConst3=0, @MethodConst4=0, @MethodConst5=0, @MethodConst6=0, @MethodConst7=0, @MethodConst8=0, @MethodConst9=0, @MethodConst10=0, @MethodConst11=0, @MethodConst12=, @MethodConst13=, @MethodConst14=, @MethodConst15=0, @MethodConst16=0, @MethodConst17=0, @MethodConst18=0, @MethodConst19=0, @MethodConst20=0, @MethodConst21=0, @MethodConst22=0, @MethodConst23=0, @MethodConst24=0, @MethodConst25=0, @MethodConst26=, @MethodConst27=, @MethodConst28=]
[2025-09-04 11:15:39.083 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1
[2025-09-04 11:15:39.155 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1 | 参数: [@equip_id0=789011, @MethodConst1=0, @MethodConst2=0, @MethodConst3=0, @MethodConst4=0, @MethodConst5=0, @MethodConst6=0, @MethodConst7=0, @MethodConst8=0, @MethodConst9=0, @MethodConst10=0, @MethodConst11=0, @MethodConst12=, @MethodConst13=, @MethodConst14=, @MethodConst15=0, @MethodConst16=0, @MethodConst17=0, @MethodConst18=0, @MethodConst19=0, @MethodConst20=0, @MethodConst21=0, @MethodConst22=0, @MethodConst23=0, @MethodConst24=0, @MethodConst25=0, @MethodConst26=, @MethodConst27=, @MethodConst28=]
[2025-09-04 11:15:39.212 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1
[2025-09-04 11:15:39.215 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1 | 参数: [@equip_id0=789009, @MethodConst1=0, @MethodConst2=0, @MethodConst3=0, @MethodConst4=0, @MethodConst5=0, @MethodConst6=0, @MethodConst7=0, @MethodConst8=0, @MethodConst9=0, @MethodConst10=0, @MethodConst11=0, @MethodConst12=, @MethodConst13=, @MethodConst14=, @MethodConst15=0, @MethodConst16=0, @MethodConst17=0, @MethodConst18=0, @MethodConst19=0, @MethodConst20=0, @MethodConst21=0, @MethodConst22=0, @MethodConst23=0, @MethodConst24=0, @MethodConst25=0, @MethodConst26=, @MethodConst27=, @MethodConst28=]
[2025-09-04 11:15:39.277 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1
[2025-09-04 11:15:41.130 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 11:15:41.135 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 11:15:41.136 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 11:15:41.192 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 11:15:41.193 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 11:15:41.198 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ue`.`id` AS `Id` , `ue`.`user_id` AS `UserId` , IFNULL(`u`.`username`,@MethodConst9) AS `UserName` , `ue`.`equip_id` AS `EquipId` , `ue`.`name` AS `Name` , IFNULL(`ue`.`icon`,@MethodConst10) AS `Icon` , IFNULL(`et`.`equip_type_id`,@MethodConst11) AS `EquipTypeId` , IFNULL(`et`.`type_name`,@MethodConst12) AS `EquipTypeName` , IFNULL(`ue`.`strengthen_level`,@MethodConst13) AS `StrengthenLevel` , IFNULL(`ue`.`slot`,@MethodConst14) AS `Slot` , `ue`.`position` AS `Position` , IFNULL(CAST(`ue`.`is_equipped` as SIGNED),@MethodConst15) AS `IsEquipped` , IFNULL(`ue`.`create_time`,@MethodConst16) AS `CreateTime`  FROM `user_equipment` `ue` Left JOIN `user` `u` ON ( `ue`.`user_id` = `u`.`id` )  Left JOIN `equipment` `e` ON ( `ue`.`equip_id` = `e`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )    WHERE ( `ue`.`id` = @id0 )   LIMIT 0,1 | 参数: [@id0=20, @MethodConst1=, @MethodConst2=, @MethodConst3=, @MethodConst4=, @MethodConst5=0, @MethodConst6=0, @MethodConst7=False, @MethodConst8=2025/9/4 11:15:41, @MethodConst9=, @MethodConst10=, @MethodConst11=, @MethodConst12=, @MethodConst13=0, @MethodConst14=0, @MethodConst15=False, @MethodConst16=2025/9/4 11:15:41]
[2025-09-04 11:15:41.256 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ue`.`id` AS `Id` , `ue`.`user_id` AS `UserId` , IFNULL(`u`.`username`,@MethodConst9) AS `UserName` , `ue`.`equip_id` AS `EquipId` , `ue`.`name` AS `Name` , IFNULL(`ue`.`icon`,@MethodConst10) AS `Icon` , IFNULL(`et`.`equip_type_id`,@MethodConst11) AS `EquipTypeId` , IFNULL(`et`.`type_name`,@MethodConst12) AS `EquipTypeName` , IFNULL(`ue`.`strengthen_level`,@MethodConst13) AS `StrengthenLevel` , IFNULL(`ue`.`slot`,@MethodConst14) AS `Slot` , `ue`.`position` AS `Position` , IFNULL(CAST(`ue`.`is_equipped` as SIGNED),@MethodConst15) AS `IsEquipped` , IFNULL(`ue`.`create_time`,@MethodConst16) AS `CreateTime`  FROM `user_equipment` `ue` Left JOIN `user` `u` ON ( `ue`.`user_id` = `u`.`id` )  Left JOIN `equipment` `e` ON ( `ue`.`equip_id` = `e`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )    WHERE ( `ue`.`id` = @id0 )   LIMIT 0,1
[2025-09-04 11:15:41.260 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1 | 参数: [@equip_id0=2020092809, @MethodConst1=0, @MethodConst2=0, @MethodConst3=0, @MethodConst4=0, @MethodConst5=0, @MethodConst6=0, @MethodConst7=0, @MethodConst8=0, @MethodConst9=0, @MethodConst10=0, @MethodConst11=0, @MethodConst12=, @MethodConst13=, @MethodConst14=, @MethodConst15=0, @MethodConst16=0, @MethodConst17=0, @MethodConst18=0, @MethodConst19=0, @MethodConst20=0, @MethodConst21=0, @MethodConst22=0, @MethodConst23=0, @MethodConst24=0, @MethodConst25=0, @MethodConst26=, @MethodConst27=, @MethodConst28=]
[2025-09-04 11:15:41.318 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1
[2025-09-04 11:16:09.622 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 11:16:09.627 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 11:16:09.631 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 11:16:09.688 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 11:16:09.689 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 11:16:09.750 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 11:16:09.751 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 11:16:09.752 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 11:16:09.817 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 11:16:09.818 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 11:16:09.821 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `user`  
[2025-09-04 11:16:09.878 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `user`  
[2025-09-04 11:16:09.893 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`username`,`password`,`nickname`,`sex`,`vip_level`,`vip_score`,`gold`,`yuanbao`,`crystal`,`reg_time`,`title`,`main_pet_id`,`main_pet_name`,`create_time`,`evolution_cd`,`synthesis_cd`,`nirvana_cd`,`money`,`supreme_vip`,`prop_capacity`,`pasture_capacity`,`dragon_ball_exp`,`dragon_ball_level`,`status`,`prestige`,`auto_battle_count`,`auto_nirvana_count`,`monster_count`,`next_battle`,`open_maps`,`star_vip`,`star_vip_expire`,`task_helper_enabled`,`title_attributes`,`soul_pet`,`hell_floor`,`tower_floor` FROM `user`    ORDER BY `create_time` DESC LIMIT 0,1000
[2025-09-04 11:16:09.949 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`username`,`password`,`nickname`,`sex`,`vip_level`,`vip_score`,`gold`,`yuanbao`,`crystal`,`reg_time`,`title`,`main_pet_id`,`main_pet_name`,`create_time`,`evolution_cd`,`synthesis_cd`,`nirvana_cd`,`money`,`supreme_vip`,`prop_capacity`,`pasture_capacity`,`dragon_ball_exp`,`dragon_ball_level`,`status`,`prestige`,`auto_battle_count`,`auto_nirvana_count`,`monster_count`,`next_battle`,`open_maps`,`star_vip`,`star_vip_expire`,`task_helper_enabled`,`title_attributes`,`soul_pet`,`hell_floor`,`tower_floor` FROM `user`    ORDER BY `create_time` DESC LIMIT 0,1000
[2025-09-04 11:16:09.963 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 11:16:09.964 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 11:16:09.967 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 11:16:10.022 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 11:16:10.023 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 11:16:10.030 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `id` AS `Id` , `item_no` AS `ItemNo` , `name` AS `Name` , `type` AS `Type` , `description` AS `Description` , `quality` AS `Quality` , `icon` AS `Icon` , `price` AS `Price` , `use_limit` AS `UseLimit` , `create_time` AS `CreateTime`  FROM `item_config` ORDER BY `item_no` ASC 
[2025-09-04 11:16:10.088 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `id` AS `Id` , `item_no` AS `ItemNo` , `name` AS `Name` , `type` AS `Type` , `description` AS `Description` , `quality` AS `Quality` , `icon` AS `Icon` , `price` AS `Price` , `use_limit` AS `UseLimit` , `create_time` AS `CreateTime`  FROM `item_config` ORDER BY `item_no` ASC 
[2025-09-04 11:16:10.147 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 11:16:10.149 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 11:16:10.151 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 11:16:10.206 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 11:16:10.213 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 11:16:10.220 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT SUM(`item_count`) FROM `user_item`  
[2025-09-04 11:16:10.276 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT SUM(`item_count`) FROM `user_item`  
[2025-09-04 11:16:10.286 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `user_item` GROUP BY `item_id`  
[2025-09-04 11:16:10.345 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `user_item` GROUP BY `item_id`  
[2025-09-04 11:16:10.362 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ic`.`quality` AS `Quality` , SUM(`ui`.`item_count`) AS `Count`  FROM `user_item` `ui` Left JOIN `item_config` `ic` ON ( `ui`.`item_id` = CAST(`ic`.`item_no` AS CHAR))  GROUP BY `ic`.`quality`  
[2025-09-04 11:16:10.426 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ic`.`quality` AS `Quality` , SUM(`ui`.`item_count`) AS `Count`  FROM `user_item` `ui` Left JOIN `item_config` `ic` ON ( `ui`.`item_id` = CAST(`ic`.`item_no` AS CHAR))  GROUP BY `ic`.`quality`  
[2025-09-04 11:16:10.446 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT SUM(( `ui`.`item_count` *IFNULL(`ic`.`price`,@MethodConst0))) FROM `user_item` `ui` Left JOIN `item_config` `ic` ON ( `ui`.`item_id` = CAST(`ic`.`item_no` AS CHAR))    | 参数: [@MethodConst0=0]
[2025-09-04 11:16:10.509 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT SUM(( `ui`.`item_count` *IFNULL(`ic`.`price`,@MethodConst0))) FROM `user_item` `ui` Left JOIN `item_config` `ic` ON ( `ui`.`item_id` = CAST(`ic`.`item_no` AS CHAR))   
[2025-09-04 11:16:10.520 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 11:16:10.521 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 11:16:10.522 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 11:16:10.584 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 11:16:10.586 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 11:16:10.594 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `user_item` `ui` Left JOIN `user` `u` ON ( `ui`.`user_id` = `u`.`id` )  Left JOIN `item_config` `ic` ON ( `ui`.`item_id` = CAST(`ic`.`item_no` AS CHAR))   
[2025-09-04 11:16:10.658 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `user_item` `ui` Left JOIN `user` `u` ON ( `ui`.`user_id` = `u`.`id` )  Left JOIN `item_config` `ic` ON ( `ui`.`item_id` = CAST(`ic`.`item_no` AS CHAR))   
[2025-09-04 11:16:10.665 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ui`.`id` AS `Id` , `ui`.`user_id` AS `UserId` , IFNULL(`u`.`username`,@MethodConst2) AS `Username` , `ui`.`item_id` AS `ItemId` , CAST(`ui`.`item_id` AS SIGNED) AS `ItemNo` , IFNULL(`ic`.`name`,@MethodConst3) AS `ItemName` , `ic`.`type` AS `ItemType` , `ic`.`quality` AS `ItemQuality` , `ui`.`item_count` AS `ItemCount` , `ui`.`item_pos` AS `ItemPos` , `ui`.`item_seq` AS `ItemSeq` , `ic`.`description` AS `ItemDescription` , `ic`.`price` AS `ItemPrice` , `ic`.`icon` AS `ItemIcon`  FROM `user_item` `ui` Left JOIN `user` `u` ON ( `ui`.`user_id` = `u`.`id` )  Left JOIN `item_config` `ic` ON ( `ui`.`item_id` = CAST(`ic`.`item_no` AS CHAR))     ORDER BY `ui`.`id` DESC LIMIT 0,15 | 参数: [@MethodConst0=, @MethodConst1=, @MethodConst2=, @MethodConst3=]
[2025-09-04 11:16:10.736 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ui`.`id` AS `Id` , `ui`.`user_id` AS `UserId` , IFNULL(`u`.`username`,@MethodConst2) AS `Username` , `ui`.`item_id` AS `ItemId` , CAST(`ui`.`item_id` AS SIGNED) AS `ItemNo` , IFNULL(`ic`.`name`,@MethodConst3) AS `ItemName` , `ic`.`type` AS `ItemType` , `ic`.`quality` AS `ItemQuality` , `ui`.`item_count` AS `ItemCount` , `ui`.`item_pos` AS `ItemPos` , `ui`.`item_seq` AS `ItemSeq` , `ic`.`description` AS `ItemDescription` , `ic`.`price` AS `ItemPrice` , `ic`.`icon` AS `ItemIcon`  FROM `user_item` `ui` Left JOIN `user` `u` ON ( `ui`.`user_id` = `u`.`id` )  Left JOIN `item_config` `ic` ON ( `ui`.`item_id` = CAST(`ic`.`item_no` AS CHAR))     ORDER BY `ui`.`id` DESC LIMIT 0,15
[2025-09-04 11:16:20.476 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 11:16:20.478 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 11:16:20.480 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 11:16:20.537 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 11:16:20.542 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 11:16:20.596 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 11:16:20.597 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 11:16:20.599 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 11:16:20.661 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 11:16:20.662 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 11:16:20.664 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `user`  
[2025-09-04 11:16:20.723 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `user`  
[2025-09-04 11:16:20.726 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`username`,`password`,`nickname`,`sex`,`vip_level`,`vip_score`,`gold`,`yuanbao`,`crystal`,`reg_time`,`title`,`main_pet_id`,`main_pet_name`,`create_time`,`evolution_cd`,`synthesis_cd`,`nirvana_cd`,`money`,`supreme_vip`,`prop_capacity`,`pasture_capacity`,`dragon_ball_exp`,`dragon_ball_level`,`status`,`prestige`,`auto_battle_count`,`auto_nirvana_count`,`monster_count`,`next_battle`,`open_maps`,`star_vip`,`star_vip_expire`,`task_helper_enabled`,`title_attributes`,`soul_pet`,`hell_floor`,`tower_floor` FROM `user`    ORDER BY `create_time` DESC LIMIT 0,1000
[2025-09-04 11:16:20.792 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`username`,`password`,`nickname`,`sex`,`vip_level`,`vip_score`,`gold`,`yuanbao`,`crystal`,`reg_time`,`title`,`main_pet_id`,`main_pet_name`,`create_time`,`evolution_cd`,`synthesis_cd`,`nirvana_cd`,`money`,`supreme_vip`,`prop_capacity`,`pasture_capacity`,`dragon_ball_exp`,`dragon_ball_level`,`status`,`prestige`,`auto_battle_count`,`auto_nirvana_count`,`monster_count`,`next_battle`,`open_maps`,`star_vip`,`star_vip_expire`,`task_helper_enabled`,`title_attributes`,`soul_pet`,`hell_floor`,`tower_floor` FROM `user`    ORDER BY `create_time` DESC LIMIT 0,1000
[2025-09-04 11:16:20.799 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 11:16:20.801 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 11:16:20.803 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 11:16:20.864 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 11:16:20.865 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 11:16:20.868 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `id` AS `Id` , `pet_no` AS `PetNo` , `name` AS `Name` , `attribute` AS `Attribute` , `skill` AS `Skill`  FROM `pet_config` ORDER BY `pet_no` ASC 
[2025-09-04 11:16:20.926 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `id` AS `Id` , `pet_no` AS `PetNo` , `name` AS `Name` , `attribute` AS `Attribute` , `skill` AS `Skill`  FROM `pet_config` ORDER BY `pet_no` ASC 
[2025-09-04 11:16:20.945 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 11:16:20.957 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 11:16:20.960 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 11:16:21.016 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 11:16:21.019 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 11:16:21.030 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )   
[2025-09-04 11:16:21.096 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )   
[2025-09-04 11:16:21.098 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )    | 参数: [@MethodConst0=, @MethodConst1=, @MethodConst2=, @MethodConst3=]
[2025-09-04 11:16:21.157 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )   
[2025-09-04 11:16:21.158 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `up`.`id` AS `Id` , `up`.`user_id` AS `UserId` , IFNULL(`u`.`username`,@MethodConst4) AS `UserName` , `up`.`pet_no` AS `PetNo` , IFNULL(`pc`.`name`,@MethodConst5) AS `PetName` , IFNULL(`pc`.`attribute`,@MethodConst6) AS `PetAttribute` , `up`.`image` AS `Image` , `up`.`exp` AS `Exp` , `up`.`hp` AS `Hp` , `up`.`mp` AS `Mp` , `up`.`atk` AS `Atk` , `up`.`def` AS `Def` , `up`.`spd` AS `Spd` , `up`.`state` AS `State` , `up`.`dodge` AS `Dodge` , `up`.`growth` AS `Growth` , `up`.`hit` AS `Hit` , `up`.`realm` AS `Realm` , `up`.`evolve_count` AS `EvolveCount` , `up`.`element` AS `Element` , `up`.`create_time` AS `CreateTime` , IFNULL(`up`.`status`,@MethodConst7) AS `Status` , `up`.`is_main` AS `IsMain`  FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )     ORDER BY `up`.`create_time` DESC LIMIT 0,10 | 参数: [@MethodConst0=, @MethodConst1=, @MethodConst2=, @MethodConst3=, @MethodConst4=, @MethodConst5=, @MethodConst6=, @MethodConst7=]
[2025-09-04 11:16:21.218 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `up`.`id` AS `Id` , `up`.`user_id` AS `UserId` , IFNULL(`u`.`username`,@MethodConst4) AS `UserName` , `up`.`pet_no` AS `PetNo` , IFNULL(`pc`.`name`,@MethodConst5) AS `PetName` , IFNULL(`pc`.`attribute`,@MethodConst6) AS `PetAttribute` , `up`.`image` AS `Image` , `up`.`exp` AS `Exp` , `up`.`hp` AS `Hp` , `up`.`mp` AS `Mp` , `up`.`atk` AS `Atk` , `up`.`def` AS `Def` , `up`.`spd` AS `Spd` , `up`.`state` AS `State` , `up`.`dodge` AS `Dodge` , `up`.`growth` AS `Growth` , `up`.`hit` AS `Hit` , `up`.`realm` AS `Realm` , `up`.`evolve_count` AS `EvolveCount` , `up`.`element` AS `Element` , `up`.`create_time` AS `CreateTime` , IFNULL(`up`.`status`,@MethodConst7) AS `Status` , `up`.`is_main` AS `IsMain`  FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )     ORDER BY `up`.`create_time` DESC LIMIT 0,10
[2025-09-04 11:16:21.227 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=22, @MethodConst1=]
[2025-09-04 11:16:21.284 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-04 11:16:21.286 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=13, @MethodConst1=]
[2025-09-04 11:16:21.345 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-04 11:16:21.349 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=12, @MethodConst1=]
[2025-09-04 11:16:21.409 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-04 11:16:21.426 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=11, @MethodConst1=]
[2025-09-04 11:16:21.484 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-04 11:16:21.488 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=15, @MethodConst1=]
[2025-09-04 11:16:21.549 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-04 11:16:21.562 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=17, @MethodConst1=]
[2025-09-04 11:16:21.649 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-04 11:16:21.861 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=19, @MethodConst1=]
[2025-09-04 11:16:21.917 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-04 11:16:21.926 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=21, @MethodConst1=]
[2025-09-04 11:16:21.981 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-04 11:16:21.985 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=18, @MethodConst1=]
[2025-09-04 11:16:22.092 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-04 11:16:22.094 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=20, @MethodConst1=]
[2025-09-04 11:16:22.149 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-04 11:16:31.653 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 11:16:31.661 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 11:16:31.664 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 11:16:31.728 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 11:16:31.731 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 11:16:31.787 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 11:16:31.787 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 11:16:31.788 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 11:16:31.789 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 11:16:31.790 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 11:16:31.791 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 11:16:31.840 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 11:16:31.843 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 11:16:31.847 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `user`  
[2025-09-04 11:16:31.847 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 11:16:31.849 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 11:16:31.851 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `user`  
[2025-09-04 11:16:31.889 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `user`  
[2025-09-04 11:16:31.891 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  
[2025-09-04 11:16:31.911 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `user`  
[2025-09-04 11:16:31.913 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`username`,`password`,`nickname`,`sex`,`vip_level`,`vip_score`,`gold`,`yuanbao`,`crystal`,`reg_time`,`title`,`main_pet_id`,`main_pet_name`,`create_time`,`evolution_cd`,`synthesis_cd`,`nirvana_cd`,`money`,`supreme_vip`,`prop_capacity`,`pasture_capacity`,`dragon_ball_exp`,`dragon_ball_level`,`status`,`prestige`,`auto_battle_count`,`auto_nirvana_count`,`monster_count`,`next_battle`,`open_maps`,`star_vip`,`star_vip_expire`,`task_helper_enabled`,`title_attributes`,`soul_pet`,`hell_floor`,`tower_floor` FROM `user`    ORDER BY `create_time` DESC LIMIT 0,1000
[2025-09-04 11:16:31.933 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  
[2025-09-04 11:16:31.944 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment_type`  
[2025-09-04 11:16:31.972 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`username`,`password`,`nickname`,`sex`,`vip_level`,`vip_score`,`gold`,`yuanbao`,`crystal`,`reg_time`,`title`,`main_pet_id`,`main_pet_name`,`create_time`,`evolution_cd`,`synthesis_cd`,`nirvana_cd`,`money`,`supreme_vip`,`prop_capacity`,`pasture_capacity`,`dragon_ball_exp`,`dragon_ball_level`,`status`,`prestige`,`auto_battle_count`,`auto_nirvana_count`,`monster_count`,`next_battle`,`open_maps`,`star_vip`,`star_vip_expire`,`task_helper_enabled`,`title_attributes`,`soul_pet`,`hell_floor`,`tower_floor` FROM `user`    ORDER BY `create_time` DESC LIMIT 0,1000
[2025-09-04 11:16:31.978 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 11:16:31.980 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 11:16:31.982 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 11:16:31.990 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment_type`  
[2025-09-04 11:16:31.996 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `user_equipment`  
[2025-09-04 11:16:32.041 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 11:16:32.041 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `user_equipment`  
[2025-09-04 11:16:32.044 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 11:16:32.058 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM `user_equipment` `ue` Left JOIN `user` `u` ON ( `ue`.`user_id` = `u`.`id` )  Left JOIN `equipment` `e` ON ( `ue`.`equip_id` = `e`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )   WHERE ( 1 = 1 )   | 参数: [@MethodConst0=, @MethodConst1=, @MethodConst2=, @MethodConst3=, @MethodConst4=0, @MethodConst5=0, @MethodConst6=False, @MethodConst7=2025/9/4 11:16:32]
[2025-09-04 11:16:32.061 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `equip_type_id` AS `Value` , `type_name` AS `Label`  FROM `equipment_type`  
[2025-09-04 11:16:32.106 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM `user_equipment` `ue` Left JOIN `user` `u` ON ( `ue`.`user_id` = `u`.`id` )  Left JOIN `equipment` `e` ON ( `ue`.`equip_id` = `e`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )   WHERE ( 1 = 1 )  
[2025-09-04 11:16:32.109 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ue`.`id` AS `Id` , `ue`.`user_id` AS `UserId` , IFNULL(`u`.`username`,@MethodConst8) AS `UserName` , `ue`.`equip_id` AS `EquipId` , `ue`.`name` AS `Name` , IFNULL(`ue`.`icon`,@MethodConst9) AS `Icon` , IFNULL(`et`.`equip_type_id`,@MethodConst10) AS `EquipTypeId` , IFNULL(`et`.`type_name`,@MethodConst11) AS `EquipTypeName` , IFNULL(`ue`.`strengthen_level`,@MethodConst12) AS `StrengthenLevel` , IFNULL(`ue`.`slot`,@MethodConst13) AS `Slot` , `ue`.`position` AS `Position` , IFNULL(CAST(`ue`.`is_equipped` as SIGNED),@MethodConst14) AS `IsEquipped` , IFNULL(`ue`.`create_time`,@MethodConst15) AS `CreateTime`  FROM `user_equipment` `ue` Left JOIN `user` `u` ON ( `ue`.`user_id` = `u`.`id` )  Left JOIN `equipment` `e` ON ( `ue`.`equip_id` = `e`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )    WHERE ( 1 = 1 )   ORDER BY `ue`.`create_time` DESC LIMIT 0,10 | 参数: [@MethodConst0=, @MethodConst1=, @MethodConst2=, @MethodConst3=, @MethodConst4=0, @MethodConst5=0, @MethodConst6=False, @MethodConst7=2025/9/4 11:16:32, @MethodConst8=, @MethodConst9=, @MethodConst10=, @MethodConst11=, @MethodConst12=0, @MethodConst13=0, @MethodConst14=False, @MethodConst15=2025/9/4 11:16:32]
[2025-09-04 11:16:32.125 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `equip_type_id` AS `Value` , `type_name` AS `Label`  FROM `equipment_type`  
[2025-09-04 11:16:32.129 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 11:16:32.132 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 11:16:32.133 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 11:16:32.156 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ue`.`id` AS `Id` , `ue`.`user_id` AS `UserId` , IFNULL(`u`.`username`,@MethodConst8) AS `UserName` , `ue`.`equip_id` AS `EquipId` , `ue`.`name` AS `Name` , IFNULL(`ue`.`icon`,@MethodConst9) AS `Icon` , IFNULL(`et`.`equip_type_id`,@MethodConst10) AS `EquipTypeId` , IFNULL(`et`.`type_name`,@MethodConst11) AS `EquipTypeName` , IFNULL(`ue`.`strengthen_level`,@MethodConst12) AS `StrengthenLevel` , IFNULL(`ue`.`slot`,@MethodConst13) AS `Slot` , `ue`.`position` AS `Position` , IFNULL(CAST(`ue`.`is_equipped` as SIGNED),@MethodConst14) AS `IsEquipped` , IFNULL(`ue`.`create_time`,@MethodConst15) AS `CreateTime`  FROM `user_equipment` `ue` Left JOIN `user` `u` ON ( `ue`.`user_id` = `u`.`id` )  Left JOIN `equipment` `e` ON ( `ue`.`equip_id` = `e`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )    WHERE ( 1 = 1 )   ORDER BY `ue`.`create_time` DESC LIMIT 0,10
[2025-09-04 11:16:32.159 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1 | 参数: [@equip_id0=2020092809, @MethodConst1=0, @MethodConst2=0, @MethodConst3=0, @MethodConst4=0, @MethodConst5=0, @MethodConst6=0, @MethodConst7=0, @MethodConst8=0, @MethodConst9=0, @MethodConst10=0, @MethodConst11=0, @MethodConst12=, @MethodConst13=, @MethodConst14=, @MethodConst15=0, @MethodConst16=0, @MethodConst17=0, @MethodConst18=0, @MethodConst19=0, @MethodConst20=0, @MethodConst21=0, @MethodConst22=0, @MethodConst23=0, @MethodConst24=0, @MethodConst25=0, @MethodConst26=, @MethodConst27=, @MethodConst28=]
[2025-09-04 11:16:32.192 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 11:16:32.193 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 11:16:32.194 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `equip_id` AS `Value` , `name` AS `Label` , `name` AS `Name` , `icon` AS `Icon` , `equip_type_id` AS `EquipTypeId`  FROM `equipment`  
[2025-09-04 11:16:32.203 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1
[2025-09-04 11:16:32.206 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1 | 参数: [@equip_id0=789035, @MethodConst1=0, @MethodConst2=0, @MethodConst3=0, @MethodConst4=0, @MethodConst5=0, @MethodConst6=0, @MethodConst7=0, @MethodConst8=0, @MethodConst9=0, @MethodConst10=0, @MethodConst11=0, @MethodConst12=, @MethodConst13=, @MethodConst14=, @MethodConst15=0, @MethodConst16=0, @MethodConst17=0, @MethodConst18=0, @MethodConst19=0, @MethodConst20=0, @MethodConst21=0, @MethodConst22=0, @MethodConst23=0, @MethodConst24=0, @MethodConst25=0, @MethodConst26=, @MethodConst27=, @MethodConst28=]
[2025-09-04 11:16:32.248 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1
[2025-09-04 11:16:32.249 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `equip_id` AS `Value` , `name` AS `Label` , `name` AS `Name` , `icon` AS `Icon` , `equip_type_id` AS `EquipTypeId`  FROM `equipment`  
[2025-09-04 11:16:32.250 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1 | 参数: [@equip_id0=2020092802, @MethodConst1=0, @MethodConst2=0, @MethodConst3=0, @MethodConst4=0, @MethodConst5=0, @MethodConst6=0, @MethodConst7=0, @MethodConst8=0, @MethodConst9=0, @MethodConst10=0, @MethodConst11=0, @MethodConst12=, @MethodConst13=, @MethodConst14=, @MethodConst15=0, @MethodConst16=0, @MethodConst17=0, @MethodConst18=0, @MethodConst19=0, @MethodConst20=0, @MethodConst21=0, @MethodConst22=0, @MethodConst23=0, @MethodConst24=0, @MethodConst25=0, @MethodConst26=, @MethodConst27=, @MethodConst28=]
[2025-09-04 11:16:32.314 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1
[2025-09-04 11:16:32.317 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1 | 参数: [@equip_id0=789019, @MethodConst1=0, @MethodConst2=0, @MethodConst3=0, @MethodConst4=0, @MethodConst5=0, @MethodConst6=0, @MethodConst7=0, @MethodConst8=0, @MethodConst9=0, @MethodConst10=0, @MethodConst11=0, @MethodConst12=, @MethodConst13=, @MethodConst14=, @MethodConst15=0, @MethodConst16=0, @MethodConst17=0, @MethodConst18=0, @MethodConst19=0, @MethodConst20=0, @MethodConst21=0, @MethodConst22=0, @MethodConst23=0, @MethodConst24=0, @MethodConst25=0, @MethodConst26=, @MethodConst27=, @MethodConst28=]
[2025-09-04 11:16:32.377 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1
[2025-09-04 11:16:32.380 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1 | 参数: [@equip_id0=2018102703, @MethodConst1=0, @MethodConst2=0, @MethodConst3=0, @MethodConst4=0, @MethodConst5=0, @MethodConst6=0, @MethodConst7=0, @MethodConst8=0, @MethodConst9=0, @MethodConst10=0, @MethodConst11=0, @MethodConst12=, @MethodConst13=, @MethodConst14=, @MethodConst15=0, @MethodConst16=0, @MethodConst17=0, @MethodConst18=0, @MethodConst19=0, @MethodConst20=0, @MethodConst21=0, @MethodConst22=0, @MethodConst23=0, @MethodConst24=0, @MethodConst25=0, @MethodConst26=, @MethodConst27=, @MethodConst28=]
[2025-09-04 11:16:32.438 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1
[2025-09-04 11:16:32.444 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1 | 参数: [@equip_id0=2016121602, @MethodConst1=0, @MethodConst2=0, @MethodConst3=0, @MethodConst4=0, @MethodConst5=0, @MethodConst6=0, @MethodConst7=0, @MethodConst8=0, @MethodConst9=0, @MethodConst10=0, @MethodConst11=0, @MethodConst12=, @MethodConst13=, @MethodConst14=, @MethodConst15=0, @MethodConst16=0, @MethodConst17=0, @MethodConst18=0, @MethodConst19=0, @MethodConst20=0, @MethodConst21=0, @MethodConst22=0, @MethodConst23=0, @MethodConst24=0, @MethodConst25=0, @MethodConst26=, @MethodConst27=, @MethodConst28=]
[2025-09-04 11:16:32.515 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1
[2025-09-04 11:16:32.529 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1 | 参数: [@equip_id0=2019042702, @MethodConst1=0, @MethodConst2=0, @MethodConst3=0, @MethodConst4=0, @MethodConst5=0, @MethodConst6=0, @MethodConst7=0, @MethodConst8=0, @MethodConst9=0, @MethodConst10=0, @MethodConst11=0, @MethodConst12=, @MethodConst13=, @MethodConst14=, @MethodConst15=0, @MethodConst16=0, @MethodConst17=0, @MethodConst18=0, @MethodConst19=0, @MethodConst20=0, @MethodConst21=0, @MethodConst22=0, @MethodConst23=0, @MethodConst24=0, @MethodConst25=0, @MethodConst26=, @MethodConst27=, @MethodConst28=]
[2025-09-04 11:16:32.594 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1
[2025-09-04 11:16:32.600 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1 | 参数: [@equip_id0=2021010102, @MethodConst1=0, @MethodConst2=0, @MethodConst3=0, @MethodConst4=0, @MethodConst5=0, @MethodConst6=0, @MethodConst7=0, @MethodConst8=0, @MethodConst9=0, @MethodConst10=0, @MethodConst11=0, @MethodConst12=, @MethodConst13=, @MethodConst14=, @MethodConst15=0, @MethodConst16=0, @MethodConst17=0, @MethodConst18=0, @MethodConst19=0, @MethodConst20=0, @MethodConst21=0, @MethodConst22=0, @MethodConst23=0, @MethodConst24=0, @MethodConst25=0, @MethodConst26=, @MethodConst27=, @MethodConst28=]
[2025-09-04 11:16:32.664 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1
[2025-09-04 11:16:32.667 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1 | 参数: [@equip_id0=789011, @MethodConst1=0, @MethodConst2=0, @MethodConst3=0, @MethodConst4=0, @MethodConst5=0, @MethodConst6=0, @MethodConst7=0, @MethodConst8=0, @MethodConst9=0, @MethodConst10=0, @MethodConst11=0, @MethodConst12=, @MethodConst13=, @MethodConst14=, @MethodConst15=0, @MethodConst16=0, @MethodConst17=0, @MethodConst18=0, @MethodConst19=0, @MethodConst20=0, @MethodConst21=0, @MethodConst22=0, @MethodConst23=0, @MethodConst24=0, @MethodConst25=0, @MethodConst26=, @MethodConst27=, @MethodConst28=]
[2025-09-04 11:16:32.730 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1
[2025-09-04 11:16:32.765 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1 | 参数: [@equip_id0=789009, @MethodConst1=0, @MethodConst2=0, @MethodConst3=0, @MethodConst4=0, @MethodConst5=0, @MethodConst6=0, @MethodConst7=0, @MethodConst8=0, @MethodConst9=0, @MethodConst10=0, @MethodConst11=0, @MethodConst12=, @MethodConst13=, @MethodConst14=, @MethodConst15=0, @MethodConst16=0, @MethodConst17=0, @MethodConst18=0, @MethodConst19=0, @MethodConst20=0, @MethodConst21=0, @MethodConst22=0, @MethodConst23=0, @MethodConst24=0, @MethodConst25=0, @MethodConst26=, @MethodConst27=, @MethodConst28=]
[2025-09-04 11:16:32.827 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1
[2025-09-04 11:16:42.190 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 11:16:42.191 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 11:16:42.192 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 11:16:42.252 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 11:16:42.255 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 11:16:42.257 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `user`  
[2025-09-04 11:16:42.315 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `user`  
[2025-09-04 11:16:42.317 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  
[2025-09-04 11:16:42.379 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  
[2025-09-04 11:16:42.380 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment_type`  
[2025-09-04 11:16:42.441 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment_type`  
[2025-09-04 11:16:42.444 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `user_equipment`  
[2025-09-04 11:16:42.498 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `user_equipment`  
[2025-09-04 11:16:42.501 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM `user_equipment` `ue` Left JOIN `user` `u` ON ( `ue`.`user_id` = `u`.`id` )  Left JOIN `equipment` `e` ON ( `ue`.`equip_id` = `e`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )   WHERE ( 1 = 1 )   | 参数: [@MethodConst0=, @MethodConst1=, @MethodConst2=, @MethodConst3=, @MethodConst4=0, @MethodConst5=0, @MethodConst6=False, @MethodConst7=2025/9/4 11:16:42]
[2025-09-04 11:16:42.561 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM `user_equipment` `ue` Left JOIN `user` `u` ON ( `ue`.`user_id` = `u`.`id` )  Left JOIN `equipment` `e` ON ( `ue`.`equip_id` = `e`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )   WHERE ( 1 = 1 )  
[2025-09-04 11:16:42.563 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ue`.`id` AS `Id` , `ue`.`user_id` AS `UserId` , IFNULL(`u`.`username`,@MethodConst8) AS `UserName` , `ue`.`equip_id` AS `EquipId` , `ue`.`name` AS `Name` , IFNULL(`ue`.`icon`,@MethodConst9) AS `Icon` , IFNULL(`et`.`equip_type_id`,@MethodConst10) AS `EquipTypeId` , IFNULL(`et`.`type_name`,@MethodConst11) AS `EquipTypeName` , IFNULL(`ue`.`strengthen_level`,@MethodConst12) AS `StrengthenLevel` , IFNULL(`ue`.`slot`,@MethodConst13) AS `Slot` , `ue`.`position` AS `Position` , IFNULL(CAST(`ue`.`is_equipped` as SIGNED),@MethodConst14) AS `IsEquipped` , IFNULL(`ue`.`create_time`,@MethodConst15) AS `CreateTime`  FROM `user_equipment` `ue` Left JOIN `user` `u` ON ( `ue`.`user_id` = `u`.`id` )  Left JOIN `equipment` `e` ON ( `ue`.`equip_id` = `e`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )    WHERE ( 1 = 1 )   ORDER BY `ue`.`create_time` DESC LIMIT 10,10 | 参数: [@MethodConst0=, @MethodConst1=, @MethodConst2=, @MethodConst3=, @MethodConst4=0, @MethodConst5=0, @MethodConst6=False, @MethodConst7=2025/9/4 11:16:42, @MethodConst8=, @MethodConst9=, @MethodConst10=, @MethodConst11=, @MethodConst12=0, @MethodConst13=0, @MethodConst14=False, @MethodConst15=2025/9/4 11:16:42]
[2025-09-04 11:16:42.622 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ue`.`id` AS `Id` , `ue`.`user_id` AS `UserId` , IFNULL(`u`.`username`,@MethodConst8) AS `UserName` , `ue`.`equip_id` AS `EquipId` , `ue`.`name` AS `Name` , IFNULL(`ue`.`icon`,@MethodConst9) AS `Icon` , IFNULL(`et`.`equip_type_id`,@MethodConst10) AS `EquipTypeId` , IFNULL(`et`.`type_name`,@MethodConst11) AS `EquipTypeName` , IFNULL(`ue`.`strengthen_level`,@MethodConst12) AS `StrengthenLevel` , IFNULL(`ue`.`slot`,@MethodConst13) AS `Slot` , `ue`.`position` AS `Position` , IFNULL(CAST(`ue`.`is_equipped` as SIGNED),@MethodConst14) AS `IsEquipped` , IFNULL(`ue`.`create_time`,@MethodConst15) AS `CreateTime`  FROM `user_equipment` `ue` Left JOIN `user` `u` ON ( `ue`.`user_id` = `u`.`id` )  Left JOIN `equipment` `e` ON ( `ue`.`equip_id` = `e`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )    WHERE ( 1 = 1 )   ORDER BY `ue`.`create_time` DESC LIMIT 10,10
[2025-09-04 11:16:42.627 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1 | 参数: [@equip_id0=789012, @MethodConst1=0, @MethodConst2=0, @MethodConst3=0, @MethodConst4=0, @MethodConst5=0, @MethodConst6=0, @MethodConst7=0, @MethodConst8=0, @MethodConst9=0, @MethodConst10=0, @MethodConst11=0, @MethodConst12=, @MethodConst13=, @MethodConst14=, @MethodConst15=0, @MethodConst16=0, @MethodConst17=0, @MethodConst18=0, @MethodConst19=0, @MethodConst20=0, @MethodConst21=0, @MethodConst22=0, @MethodConst23=0, @MethodConst24=0, @MethodConst25=0, @MethodConst26=, @MethodConst27=, @MethodConst28=]
[2025-09-04 11:16:42.687 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1
[2025-09-04 11:16:42.690 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1 | 参数: [@equip_id0=789010, @MethodConst1=0, @MethodConst2=0, @MethodConst3=0, @MethodConst4=0, @MethodConst5=0, @MethodConst6=0, @MethodConst7=0, @MethodConst8=0, @MethodConst9=0, @MethodConst10=0, @MethodConst11=0, @MethodConst12=, @MethodConst13=, @MethodConst14=, @MethodConst15=0, @MethodConst16=0, @MethodConst17=0, @MethodConst18=0, @MethodConst19=0, @MethodConst20=0, @MethodConst21=0, @MethodConst22=0, @MethodConst23=0, @MethodConst24=0, @MethodConst25=0, @MethodConst26=, @MethodConst27=, @MethodConst28=]
[2025-09-04 11:16:42.746 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1
[2025-09-04 11:16:42.748 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1 | 参数: [@equip_id0=789014, @MethodConst1=0, @MethodConst2=0, @MethodConst3=0, @MethodConst4=0, @MethodConst5=0, @MethodConst6=0, @MethodConst7=0, @MethodConst8=0, @MethodConst9=0, @MethodConst10=0, @MethodConst11=0, @MethodConst12=, @MethodConst13=, @MethodConst14=, @MethodConst15=0, @MethodConst16=0, @MethodConst17=0, @MethodConst18=0, @MethodConst19=0, @MethodConst20=0, @MethodConst21=0, @MethodConst22=0, @MethodConst23=0, @MethodConst24=0, @MethodConst25=0, @MethodConst26=, @MethodConst27=, @MethodConst28=]
[2025-09-04 11:16:42.806 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1
[2025-09-04 11:16:42.810 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1 | 参数: [@equip_id0=789013, @MethodConst1=0, @MethodConst2=0, @MethodConst3=0, @MethodConst4=0, @MethodConst5=0, @MethodConst6=0, @MethodConst7=0, @MethodConst8=0, @MethodConst9=0, @MethodConst10=0, @MethodConst11=0, @MethodConst12=, @MethodConst13=, @MethodConst14=, @MethodConst15=0, @MethodConst16=0, @MethodConst17=0, @MethodConst18=0, @MethodConst19=0, @MethodConst20=0, @MethodConst21=0, @MethodConst22=0, @MethodConst23=0, @MethodConst24=0, @MethodConst25=0, @MethodConst26=, @MethodConst27=, @MethodConst28=]
[2025-09-04 11:16:42.874 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1
[2025-09-04 11:16:42.877 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1 | 参数: [@equip_id0=2016121601, @MethodConst1=0, @MethodConst2=0, @MethodConst3=0, @MethodConst4=0, @MethodConst5=0, @MethodConst6=0, @MethodConst7=0, @MethodConst8=0, @MethodConst9=0, @MethodConst10=0, @MethodConst11=0, @MethodConst12=, @MethodConst13=, @MethodConst14=, @MethodConst15=0, @MethodConst16=0, @MethodConst17=0, @MethodConst18=0, @MethodConst19=0, @MethodConst20=0, @MethodConst21=0, @MethodConst22=0, @MethodConst23=0, @MethodConst24=0, @MethodConst25=0, @MethodConst26=, @MethodConst27=, @MethodConst28=]
[2025-09-04 11:16:42.936 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1
[2025-09-04 11:16:42.939 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1 | 参数: [@equip_id0=789015, @MethodConst1=0, @MethodConst2=0, @MethodConst3=0, @MethodConst4=0, @MethodConst5=0, @MethodConst6=0, @MethodConst7=0, @MethodConst8=0, @MethodConst9=0, @MethodConst10=0, @MethodConst11=0, @MethodConst12=, @MethodConst13=, @MethodConst14=, @MethodConst15=0, @MethodConst16=0, @MethodConst17=0, @MethodConst18=0, @MethodConst19=0, @MethodConst20=0, @MethodConst21=0, @MethodConst22=0, @MethodConst23=0, @MethodConst24=0, @MethodConst25=0, @MethodConst26=, @MethodConst27=, @MethodConst28=]
[2025-09-04 11:16:42.993 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1
[2025-09-04 11:16:46.637 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 11:16:46.639 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 11:16:46.642 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 11:16:46.698 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 11:16:46.718 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 11:16:46.740 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `user`  
[2025-09-04 11:16:46.798 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `user`  
[2025-09-04 11:16:46.801 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  
[2025-09-04 11:16:46.869 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  
[2025-09-04 11:16:46.872 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment_type`  
[2025-09-04 11:16:46.928 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment_type`  
[2025-09-04 11:16:46.930 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `user_equipment`  
[2025-09-04 11:16:46.994 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `user_equipment`  
[2025-09-04 11:16:46.997 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM `user_equipment` `ue` Left JOIN `user` `u` ON ( `ue`.`user_id` = `u`.`id` )  Left JOIN `equipment` `e` ON ( `ue`.`equip_id` = `e`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )   WHERE ( 1 = 1 )   | 参数: [@MethodConst0=, @MethodConst1=, @MethodConst2=, @MethodConst3=, @MethodConst4=0, @MethodConst5=0, @MethodConst6=False, @MethodConst7=2025/9/4 11:16:46]
[2025-09-04 11:16:47.067 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM `user_equipment` `ue` Left JOIN `user` `u` ON ( `ue`.`user_id` = `u`.`id` )  Left JOIN `equipment` `e` ON ( `ue`.`equip_id` = `e`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )   WHERE ( 1 = 1 )  
[2025-09-04 11:16:47.070 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ue`.`id` AS `Id` , `ue`.`user_id` AS `UserId` , IFNULL(`u`.`username`,@MethodConst8) AS `UserName` , `ue`.`equip_id` AS `EquipId` , `ue`.`name` AS `Name` , IFNULL(`ue`.`icon`,@MethodConst9) AS `Icon` , IFNULL(`et`.`equip_type_id`,@MethodConst10) AS `EquipTypeId` , IFNULL(`et`.`type_name`,@MethodConst11) AS `EquipTypeName` , IFNULL(`ue`.`strengthen_level`,@MethodConst12) AS `StrengthenLevel` , IFNULL(`ue`.`slot`,@MethodConst13) AS `Slot` , `ue`.`position` AS `Position` , IFNULL(CAST(`ue`.`is_equipped` as SIGNED),@MethodConst14) AS `IsEquipped` , IFNULL(`ue`.`create_time`,@MethodConst15) AS `CreateTime`  FROM `user_equipment` `ue` Left JOIN `user` `u` ON ( `ue`.`user_id` = `u`.`id` )  Left JOIN `equipment` `e` ON ( `ue`.`equip_id` = `e`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )    WHERE ( 1 = 1 )   ORDER BY `ue`.`create_time` DESC LIMIT 0,10 | 参数: [@MethodConst0=, @MethodConst1=, @MethodConst2=, @MethodConst3=, @MethodConst4=0, @MethodConst5=0, @MethodConst6=False, @MethodConst7=2025/9/4 11:16:47, @MethodConst8=, @MethodConst9=, @MethodConst10=, @MethodConst11=, @MethodConst12=0, @MethodConst13=0, @MethodConst14=False, @MethodConst15=2025/9/4 11:16:47]
[2025-09-04 11:16:47.128 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ue`.`id` AS `Id` , `ue`.`user_id` AS `UserId` , IFNULL(`u`.`username`,@MethodConst8) AS `UserName` , `ue`.`equip_id` AS `EquipId` , `ue`.`name` AS `Name` , IFNULL(`ue`.`icon`,@MethodConst9) AS `Icon` , IFNULL(`et`.`equip_type_id`,@MethodConst10) AS `EquipTypeId` , IFNULL(`et`.`type_name`,@MethodConst11) AS `EquipTypeName` , IFNULL(`ue`.`strengthen_level`,@MethodConst12) AS `StrengthenLevel` , IFNULL(`ue`.`slot`,@MethodConst13) AS `Slot` , `ue`.`position` AS `Position` , IFNULL(CAST(`ue`.`is_equipped` as SIGNED),@MethodConst14) AS `IsEquipped` , IFNULL(`ue`.`create_time`,@MethodConst15) AS `CreateTime`  FROM `user_equipment` `ue` Left JOIN `user` `u` ON ( `ue`.`user_id` = `u`.`id` )  Left JOIN `equipment` `e` ON ( `ue`.`equip_id` = `e`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )    WHERE ( 1 = 1 )   ORDER BY `ue`.`create_time` DESC LIMIT 0,10
[2025-09-04 11:16:47.131 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1 | 参数: [@equip_id0=2020092809, @MethodConst1=0, @MethodConst2=0, @MethodConst3=0, @MethodConst4=0, @MethodConst5=0, @MethodConst6=0, @MethodConst7=0, @MethodConst8=0, @MethodConst9=0, @MethodConst10=0, @MethodConst11=0, @MethodConst12=, @MethodConst13=, @MethodConst14=, @MethodConst15=0, @MethodConst16=0, @MethodConst17=0, @MethodConst18=0, @MethodConst19=0, @MethodConst20=0, @MethodConst21=0, @MethodConst22=0, @MethodConst23=0, @MethodConst24=0, @MethodConst25=0, @MethodConst26=, @MethodConst27=, @MethodConst28=]
[2025-09-04 11:16:47.191 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1
[2025-09-04 11:16:47.194 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1 | 参数: [@equip_id0=789035, @MethodConst1=0, @MethodConst2=0, @MethodConst3=0, @MethodConst4=0, @MethodConst5=0, @MethodConst6=0, @MethodConst7=0, @MethodConst8=0, @MethodConst9=0, @MethodConst10=0, @MethodConst11=0, @MethodConst12=, @MethodConst13=, @MethodConst14=, @MethodConst15=0, @MethodConst16=0, @MethodConst17=0, @MethodConst18=0, @MethodConst19=0, @MethodConst20=0, @MethodConst21=0, @MethodConst22=0, @MethodConst23=0, @MethodConst24=0, @MethodConst25=0, @MethodConst26=, @MethodConst27=, @MethodConst28=]
[2025-09-04 11:16:47.255 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1
[2025-09-04 11:16:47.257 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1 | 参数: [@equip_id0=2020092802, @MethodConst1=0, @MethodConst2=0, @MethodConst3=0, @MethodConst4=0, @MethodConst5=0, @MethodConst6=0, @MethodConst7=0, @MethodConst8=0, @MethodConst9=0, @MethodConst10=0, @MethodConst11=0, @MethodConst12=, @MethodConst13=, @MethodConst14=, @MethodConst15=0, @MethodConst16=0, @MethodConst17=0, @MethodConst18=0, @MethodConst19=0, @MethodConst20=0, @MethodConst21=0, @MethodConst22=0, @MethodConst23=0, @MethodConst24=0, @MethodConst25=0, @MethodConst26=, @MethodConst27=, @MethodConst28=]
[2025-09-04 11:16:47.321 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1
[2025-09-04 11:16:47.328 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1 | 参数: [@equip_id0=789019, @MethodConst1=0, @MethodConst2=0, @MethodConst3=0, @MethodConst4=0, @MethodConst5=0, @MethodConst6=0, @MethodConst7=0, @MethodConst8=0, @MethodConst9=0, @MethodConst10=0, @MethodConst11=0, @MethodConst12=, @MethodConst13=, @MethodConst14=, @MethodConst15=0, @MethodConst16=0, @MethodConst17=0, @MethodConst18=0, @MethodConst19=0, @MethodConst20=0, @MethodConst21=0, @MethodConst22=0, @MethodConst23=0, @MethodConst24=0, @MethodConst25=0, @MethodConst26=, @MethodConst27=, @MethodConst28=]
[2025-09-04 11:16:47.385 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1
[2025-09-04 11:16:47.388 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1 | 参数: [@equip_id0=2018102703, @MethodConst1=0, @MethodConst2=0, @MethodConst3=0, @MethodConst4=0, @MethodConst5=0, @MethodConst6=0, @MethodConst7=0, @MethodConst8=0, @MethodConst9=0, @MethodConst10=0, @MethodConst11=0, @MethodConst12=, @MethodConst13=, @MethodConst14=, @MethodConst15=0, @MethodConst16=0, @MethodConst17=0, @MethodConst18=0, @MethodConst19=0, @MethodConst20=0, @MethodConst21=0, @MethodConst22=0, @MethodConst23=0, @MethodConst24=0, @MethodConst25=0, @MethodConst26=, @MethodConst27=, @MethodConst28=]
[2025-09-04 11:16:47.445 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1
[2025-09-04 11:16:47.447 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1 | 参数: [@equip_id0=2016121602, @MethodConst1=0, @MethodConst2=0, @MethodConst3=0, @MethodConst4=0, @MethodConst5=0, @MethodConst6=0, @MethodConst7=0, @MethodConst8=0, @MethodConst9=0, @MethodConst10=0, @MethodConst11=0, @MethodConst12=, @MethodConst13=, @MethodConst14=, @MethodConst15=0, @MethodConst16=0, @MethodConst17=0, @MethodConst18=0, @MethodConst19=0, @MethodConst20=0, @MethodConst21=0, @MethodConst22=0, @MethodConst23=0, @MethodConst24=0, @MethodConst25=0, @MethodConst26=, @MethodConst27=, @MethodConst28=]
[2025-09-04 11:16:47.508 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1
[2025-09-04 11:16:47.511 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1 | 参数: [@equip_id0=2019042702, @MethodConst1=0, @MethodConst2=0, @MethodConst3=0, @MethodConst4=0, @MethodConst5=0, @MethodConst6=0, @MethodConst7=0, @MethodConst8=0, @MethodConst9=0, @MethodConst10=0, @MethodConst11=0, @MethodConst12=, @MethodConst13=, @MethodConst14=, @MethodConst15=0, @MethodConst16=0, @MethodConst17=0, @MethodConst18=0, @MethodConst19=0, @MethodConst20=0, @MethodConst21=0, @MethodConst22=0, @MethodConst23=0, @MethodConst24=0, @MethodConst25=0, @MethodConst26=, @MethodConst27=, @MethodConst28=]
[2025-09-04 11:16:47.577 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1
[2025-09-04 11:16:47.587 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1 | 参数: [@equip_id0=2021010102, @MethodConst1=0, @MethodConst2=0, @MethodConst3=0, @MethodConst4=0, @MethodConst5=0, @MethodConst6=0, @MethodConst7=0, @MethodConst8=0, @MethodConst9=0, @MethodConst10=0, @MethodConst11=0, @MethodConst12=, @MethodConst13=, @MethodConst14=, @MethodConst15=0, @MethodConst16=0, @MethodConst17=0, @MethodConst18=0, @MethodConst19=0, @MethodConst20=0, @MethodConst21=0, @MethodConst22=0, @MethodConst23=0, @MethodConst24=0, @MethodConst25=0, @MethodConst26=, @MethodConst27=, @MethodConst28=]
[2025-09-04 11:16:47.645 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1
[2025-09-04 11:16:47.673 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1 | 参数: [@equip_id0=789011, @MethodConst1=0, @MethodConst2=0, @MethodConst3=0, @MethodConst4=0, @MethodConst5=0, @MethodConst6=0, @MethodConst7=0, @MethodConst8=0, @MethodConst9=0, @MethodConst10=0, @MethodConst11=0, @MethodConst12=, @MethodConst13=, @MethodConst14=, @MethodConst15=0, @MethodConst16=0, @MethodConst17=0, @MethodConst18=0, @MethodConst19=0, @MethodConst20=0, @MethodConst21=0, @MethodConst22=0, @MethodConst23=0, @MethodConst24=0, @MethodConst25=0, @MethodConst26=, @MethodConst27=, @MethodConst28=]
[2025-09-04 11:16:47.730 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1
[2025-09-04 11:16:47.803 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1 | 参数: [@equip_id0=789009, @MethodConst1=0, @MethodConst2=0, @MethodConst3=0, @MethodConst4=0, @MethodConst5=0, @MethodConst6=0, @MethodConst7=0, @MethodConst8=0, @MethodConst9=0, @MethodConst10=0, @MethodConst11=0, @MethodConst12=, @MethodConst13=, @MethodConst14=, @MethodConst15=0, @MethodConst16=0, @MethodConst17=0, @MethodConst18=0, @MethodConst19=0, @MethodConst20=0, @MethodConst21=0, @MethodConst22=0, @MethodConst23=0, @MethodConst24=0, @MethodConst25=0, @MethodConst26=, @MethodConst27=, @MethodConst28=]
[2025-09-04 11:16:47.864 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1
[2025-09-04 11:16:53.199 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 11:16:53.201 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 11:16:53.205 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 11:16:53.261 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 11:16:53.263 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 11:16:53.318 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 11:16:53.319 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 11:16:53.320 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 11:16:53.386 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 11:16:53.403 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 11:16:53.415 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `map_config`  
[2025-09-04 11:16:53.478 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `map_config`  
[2025-09-04 11:16:53.481 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`map_id`,`map_name`,`map_desc`,`map_size`,`map_type`,`atlast_name`,`background`,`bgm`,`bgm_loop`,`bgm_volume`,`bgm_play`,`bgm_mute`,`bgm_pause`,`ico`,`type` FROM `map_config`    ORDER BY `id` ASC LIMIT 0,10
[2025-09-04 11:16:53.539 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`map_id`,`map_name`,`map_desc`,`map_size`,`map_type`,`atlast_name`,`background`,`bgm`,`bgm_loop`,`bgm_volume`,`bgm_play`,`bgm_mute`,`bgm_pause`,`ico`,`type` FROM `map_config`    ORDER BY `id` ASC LIMIT 0,10
[2025-09-04 11:20:42.629 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 11:20:42.630 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 11:20:42.632 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 11:20:42.911 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 11:20:42.913 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 11:20:42.962 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 11:20:42.963 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 11:20:42.965 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 11:20:43.020 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 11:20:43.022 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 11:20:43.026 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `map_config`  
[2025-09-04 11:20:43.073 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `map_config`  
[2025-09-04 11:20:43.074 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`map_id`,`map_name`,`map_desc`,`map_size`,`map_type`,`atlast_name`,`background`,`bgm`,`bgm_loop`,`bgm_volume`,`bgm_play`,`bgm_mute`,`bgm_pause`,`ico`,`type` FROM `map_config`    ORDER BY `id` ASC LIMIT 0,10
[2025-09-04 11:20:43.124 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`map_id`,`map_name`,`map_desc`,`map_size`,`map_type`,`atlast_name`,`background`,`bgm`,`bgm_loop`,`bgm_volume`,`bgm_play`,`bgm_mute`,`bgm_pause`,`ico`,`type` FROM `map_config`    ORDER BY `id` ASC LIMIT 0,10
[2025-09-04 11:36:32.164 +08:00 INF] BMS: 🚀 应用程序启动，日志系统已配置
[2025-09-04 11:36:32.201 +08:00 DBG] BMS: 🔧 调试级别日志测试
[2025-09-04 11:36:32.223 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 11:36:32.228 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 11:36:32.334 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 11:36:32.760 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 11:36:32.771 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 11:36:32.773 +08:00 INF] BMS: ✅ DbContext 初始化成功，日志文件位置: logs/
[2025-09-04 11:36:33.718 +08:00 WRN] Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware: Failed to determine the https port for redirect.
[2025-09-04 11:36:37.260 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 11:36:37.265 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 11:36:37.274 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 11:36:37.311 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 11:36:37.334 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 11:36:37.440 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 11:36:37.602 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 11:36:37.780 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 11:36:37.835 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 11:36:37.836 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 11:36:37.931 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `map_config`  
[2025-09-04 11:36:37.979 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `map_config`  
[2025-09-04 11:36:38.003 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`map_id`,`map_name`,`map_desc`,`map_size`,`map_type`,`atlast_name`,`background`,`bgm`,`bgm_loop`,`bgm_volume`,`bgm_play`,`bgm_mute`,`bgm_pause`,`ico`,`type` FROM `map_config`    ORDER BY `id` ASC LIMIT 0,10
[2025-09-04 11:36:38.038 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`map_id`,`map_name`,`map_desc`,`map_size`,`map_type`,`atlast_name`,`background`,`bgm`,`bgm_loop`,`bgm_volume`,`bgm_play`,`bgm_mute`,`bgm_pause`,`ico`,`type` FROM `map_config`    ORDER BY `id` ASC LIMIT 0,10
[2025-09-04 11:37:27.697 +08:00 INF] BMS: 🚀 应用程序启动，日志系统已配置
[2025-09-04 11:37:27.736 +08:00 DBG] BMS: 🔧 调试级别日志测试
[2025-09-04 11:37:27.755 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 11:37:27.758 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 11:37:27.848 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 11:37:28.360 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 11:37:28.375 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 11:37:28.384 +08:00 INF] BMS: ✅ DbContext 初始化成功，日志文件位置: logs/
[2025-09-04 11:37:29.566 +08:00 WRN] Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware: Failed to determine the https port for redirect.
[2025-09-04 11:37:59.007 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 11:37:59.012 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 11:37:59.023 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 11:37:59.072 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 11:37:59.306 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 11:37:59.394 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 11:37:59.396 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 11:37:59.400 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 11:37:59.445 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 11:37:59.446 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 11:37:59.559 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `map_config`  
[2025-09-04 11:37:59.608 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `map_config`  
[2025-09-04 11:37:59.630 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`map_id`,`map_name`,`map_desc`,`map_size`,`map_type`,`atlast_name`,`background`,`bgm`,`bgm_loop`,`bgm_volume`,`bgm_play`,`bgm_mute`,`bgm_pause`,`ico`,`type` FROM `map_config`    ORDER BY `id` ASC LIMIT 0,10
[2025-09-04 11:37:59.675 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`map_id`,`map_name`,`map_desc`,`map_size`,`map_type`,`atlast_name`,`background`,`bgm`,`bgm_loop`,`bgm_volume`,`bgm_play`,`bgm_mute`,`bgm_pause`,`ico`,`type` FROM `map_config`    ORDER BY `id` ASC LIMIT 0,10
[2025-09-04 11:38:33.136 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 11:38:33.150 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 11:38:33.153 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 11:38:33.199 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 11:38:33.201 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 11:38:33.311 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 11:38:33.314 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 11:38:33.318 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 11:38:33.362 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 11:38:33.366 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 11:38:33.395 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `pet_config`  
[2025-09-04 11:38:33.443 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `pet_config`  
[2025-09-04 11:38:33.446 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`pet_no`,`name`,`attribute`,`skill`,`is_active`,`create_time` FROM `pet_config`    ORDER BY `pet_no` ASC LIMIT 0,10
[2025-09-04 11:38:33.495 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`pet_no`,`name`,`attribute`,`skill`,`is_active`,`create_time` FROM `pet_config`    ORDER BY `pet_no` ASC LIMIT 0,10
[2025-09-04 11:38:47.233 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 11:38:47.235 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 11:38:47.236 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 11:38:47.278 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 11:38:47.279 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 11:38:47.363 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 11:38:47.364 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 11:38:47.366 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 11:38:47.410 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 11:38:47.411 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 11:38:47.431 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `monster_config`  
[2025-09-04 11:38:47.509 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `monster_config`  
[2025-09-04 11:38:47.510 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`monster_no`,`skill`,`name`,`attribute` FROM `monster_config`    ORDER BY `monster_no` ASC LIMIT 0,10
[2025-09-04 11:38:47.575 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`monster_no`,`skill`,`name`,`attribute` FROM `monster_config`    ORDER BY `monster_no` ASC LIMIT 0,10
[2025-09-04 11:44:24.708 +08:00 INF] BMS: 🚀 应用程序启动，日志系统已配置
[2025-09-04 11:44:24.857 +08:00 DBG] BMS: 🔧 调试级别日志测试
[2025-09-04 11:44:24.874 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 11:44:24.884 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 11:44:24.969 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 11:44:25.428 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 11:44:25.437 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 11:44:25.451 +08:00 INF] BMS: ✅ DbContext 初始化成功，日志文件位置: logs/
[2025-09-04 11:44:26.718 +08:00 WRN] Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware: Failed to determine the https port for redirect.
[2025-09-04 11:44:36.214 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 11:44:36.216 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 11:44:36.221 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 11:44:36.257 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 11:44:36.259 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 11:44:36.321 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 11:44:36.323 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 11:44:36.324 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 11:44:36.359 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 11:44:36.362 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 11:44:36.461 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `monster_config`  
[2025-09-04 11:44:36.499 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `monster_config`  
[2025-09-04 11:44:36.518 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`monster_no`,`skill`,`name`,`attribute` FROM `monster_config`    ORDER BY `monster_no` ASC LIMIT 0,10
[2025-09-04 11:44:36.554 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`monster_no`,`skill`,`name`,`attribute` FROM `monster_config`    ORDER BY `monster_no` ASC LIMIT 0,10
[2025-09-04 11:44:58.871 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 11:44:58.889 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 11:44:58.892 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 11:44:58.931 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 11:44:58.934 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 11:44:58.937 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `monster_config`  
[2025-09-04 11:44:58.975 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `monster_config`  
[2025-09-04 11:44:58.979 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`monster_no`,`skill`,`name`,`attribute` FROM `monster_config`    ORDER BY `monster_no` ASC LIMIT 10,10
[2025-09-04 11:44:59.015 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`monster_no`,`skill`,`name`,`attribute` FROM `monster_config`    ORDER BY `monster_no` ASC LIMIT 10,10
[2025-09-04 11:45:00.220 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 11:45:00.223 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 11:45:00.230 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 11:45:00.270 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 11:45:00.272 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 11:45:00.274 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `monster_config`  
[2025-09-04 11:45:00.321 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `monster_config`  
[2025-09-04 11:45:00.325 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`monster_no`,`skill`,`name`,`attribute` FROM `monster_config`    ORDER BY `monster_no` ASC LIMIT 20,10
[2025-09-04 11:45:00.364 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`monster_no`,`skill`,`name`,`attribute` FROM `monster_config`    ORDER BY `monster_no` ASC LIMIT 20,10
[2025-09-04 11:45:01.348 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 11:45:01.358 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 11:45:01.370 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 11:45:01.411 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 11:45:01.413 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 11:45:01.415 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `monster_config`  
[2025-09-04 11:45:01.448 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `monster_config`  
[2025-09-04 11:45:01.477 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`monster_no`,`skill`,`name`,`attribute` FROM `monster_config`    ORDER BY `monster_no` ASC LIMIT 0,10
[2025-09-04 11:45:01.511 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`monster_no`,`skill`,`name`,`attribute` FROM `monster_config`    ORDER BY `monster_no` ASC LIMIT 0,10
[2025-09-04 11:45:03.827 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 11:45:03.829 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 11:45:03.830 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 11:45:03.865 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 11:45:03.868 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 11:45:03.873 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `monster_config`  
[2025-09-04 11:45:03.911 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `monster_config`  
[2025-09-04 11:45:03.913 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`monster_no`,`skill`,`name`,`attribute` FROM `monster_config`    ORDER BY `monster_no` ASC LIMIT 10,10
[2025-09-04 11:45:03.949 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`monster_no`,`skill`,`name`,`attribute` FROM `monster_config`    ORDER BY `monster_no` ASC LIMIT 10,10
[2025-09-04 11:45:07.563 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 11:45:07.588 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 11:45:07.593 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 11:45:07.633 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 11:45:07.635 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 11:45:07.638 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `monster_config`  
[2025-09-04 11:45:07.707 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `monster_config`  
[2025-09-04 11:45:07.709 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`monster_no`,`skill`,`name`,`attribute` FROM `monster_config`    ORDER BY `monster_no` ASC LIMIT 20,10
[2025-09-04 11:45:07.742 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`monster_no`,`skill`,`name`,`attribute` FROM `monster_config`    ORDER BY `monster_no` ASC LIMIT 20,10
[2025-09-04 11:45:20.038 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 11:45:20.040 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 11:45:20.041 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 11:45:20.086 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 11:45:20.088 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 11:45:20.128 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  DISTINCT `type` FROM `item_config`  WHERE NOT( `type` IS NULL   OR `type`='') 
[2025-09-04 11:45:20.176 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  DISTINCT `type` FROM `item_config`  WHERE NOT( `type` IS NULL   OR `type`='') 
[2025-09-04 11:45:20.195 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  DISTINCT `quality` FROM `item_config`  WHERE NOT( `quality` IS NULL   OR `quality`='') 
[2025-09-04 11:45:20.241 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  DISTINCT `quality` FROM `item_config`  WHERE NOT( `quality` IS NULL   OR `quality`='') 
[2025-09-04 11:45:20.302 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 11:45:20.305 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 11:45:20.306 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 11:45:20.341 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 11:45:20.342 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 11:45:20.390 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-09-04 11:45:20.599 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-09-04 11:45:20.609 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 0,10
[2025-09-04 11:45:21.410 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 0,10
[2025-09-04 11:45:26.918 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 11:45:26.920 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 11:45:26.923 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 11:45:26.964 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 11:45:26.965 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 11:45:26.985 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active`,`update_time` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=756]
[2025-09-04 11:45:27.104 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active`,`update_time` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-09-04 11:45:27.129 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1 | 参数: [@item_no0=45452]
[2025-09-04 11:45:27.165 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1
[2025-09-04 11:55:02.902 +08:00 INF] BMS: 🚀 应用程序启动，日志系统已配置
[2025-09-04 11:55:02.931 +08:00 DBG] BMS: 🔧 调试级别日志测试
[2025-09-04 11:55:02.950 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 11:55:02.953 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 11:55:03.043 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 11:55:03.604 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 11:55:03.615 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 11:55:03.616 +08:00 INF] BMS: ✅ DbContext 初始化成功，日志文件位置: logs/
[2025-09-04 11:55:04.387 +08:00 WRN] Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware: Failed to determine the https port for redirect.
[2025-09-04 11:55:13.179 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 11:55:13.183 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 11:55:13.189 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 11:55:13.230 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 11:55:13.232 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 11:55:13.283 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  DISTINCT `type` FROM `item_config`  WHERE NOT( `type` IS NULL   OR `type`='') 
[2025-09-04 11:55:13.329 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  DISTINCT `type` FROM `item_config`  WHERE NOT( `type` IS NULL   OR `type`='') 
[2025-09-04 11:55:13.355 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  DISTINCT `quality` FROM `item_config`  WHERE NOT( `quality` IS NULL   OR `quality`='') 
[2025-09-04 11:55:13.396 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  DISTINCT `quality` FROM `item_config`  WHERE NOT( `quality` IS NULL   OR `quality`='') 
[2025-09-04 11:55:13.516 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 11:55:13.524 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 11:55:13.531 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 11:55:13.570 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 11:55:13.572 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 11:55:13.713 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-09-04 11:55:13.926 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-09-04 11:55:13.929 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 0,10
[2025-09-04 11:55:14.648 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 0,10
[2025-09-04 11:55:24.158 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 11:55:24.161 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 11:55:24.164 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 11:55:24.202 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 11:55:24.207 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 11:55:24.210 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-09-04 11:55:24.439 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-09-04 11:55:24.442 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 10,10
[2025-09-04 11:55:25.192 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 10,10
[2025-09-04 11:55:32.748 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 11:55:32.804 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 11:55:32.806 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 11:55:32.903 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 11:55:32.926 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 11:55:32.932 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-09-04 11:55:33.154 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-09-04 11:55:33.157 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 20,10
[2025-09-04 11:55:33.926 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 20,10
[2025-09-04 11:55:50.038 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 11:55:50.040 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 11:55:50.041 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 11:55:50.078 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 11:55:50.080 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 11:55:50.090 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active`,`update_time` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=768]
[2025-09-04 11:55:50.137 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active`,`update_time` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-09-04 11:55:50.154 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1 | 参数: [@item_no0=454514]
[2025-09-04 11:55:50.194 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1
[2025-09-04 11:55:58.107 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 11:55:58.111 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 11:55:58.113 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 11:55:58.155 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 11:55:58.162 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 11:55:58.168 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-09-04 11:55:58.396 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-09-04 11:55:58.408 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 0,10
[2025-09-04 11:55:59.211 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 0,10
[2025-09-04 11:56:25.768 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 11:56:25.770 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 11:56:25.772 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 11:56:25.825 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 11:56:25.826 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 11:56:25.870 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: UPDATE `item_config`  SET
             `is_active` = @is_active0    WHERE  (`id` IN (884,32,848,62,1280,755,756,757,758,759))  | 参数: [@is_active0=True, @id=0]
[2025-09-04 11:56:25.976 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: UPDATE `item_config`  SET
             `is_active` = @is_active0    WHERE  (`id` IN (884,32,848,62,1280,755,756,757,758,759)) 
[2025-09-04 11:56:25.985 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 11:56:25.986 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 11:56:25.987 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 11:56:26.028 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 11:56:26.029 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 11:56:26.031 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-09-04 11:56:26.242 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-09-04 11:56:26.244 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 0,10
[2025-09-04 11:56:27.028 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 0,10
[2025-09-04 11:56:38.449 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 11:56:38.450 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 11:56:38.453 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 11:56:38.492 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 11:56:38.495 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 11:56:39.063 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 11:56:39.065 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 11:56:39.067 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 11:56:39.103 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 11:56:39.105 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 11:56:39.114 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `pet_no` AS `PetNo` , IFNULL(`name`,@MethodConst0) AS `Name` , IFNULL(`attribute`,@MethodConst1) AS `Attribute`  FROM `pet_config` `x` ORDER BY `pet_no` ASC  | 参数: [@MethodConst0=, @MethodConst1=]
[2025-09-04 11:56:39.153 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `pet_no` AS `PetNo` , IFNULL(`name`,@MethodConst0) AS `Name` , IFNULL(`attribute`,@MethodConst1) AS `Attribute`  FROM `pet_config` `x` ORDER BY `pet_no` ASC 
[2025-09-04 11:56:39.177 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 11:56:39.183 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 11:56:39.187 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 11:56:39.223 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 11:56:39.224 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 11:56:39.229 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  CAST(`item_no` AS CHAR) AS `ItemId` , IFNULL(`name`,@MethodConst0) AS `Name` , IFNULL(`type`,@MethodConst1) AS `Type`  FROM `item_config` `x` ORDER BY `item_no` ASC  | 参数: [@MethodConst0=, @MethodConst1=]
[2025-09-04 11:56:39.292 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  CAST(`item_no` AS CHAR) AS `ItemId` , IFNULL(`name`,@MethodConst0) AS `Name` , IFNULL(`type`,@MethodConst1) AS `Type`  FROM `item_config` `x` ORDER BY `item_no` ASC 
[2025-09-04 11:56:39.331 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 11:56:39.338 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 11:56:39.356 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 11:56:39.426 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 11:56:39.427 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 11:56:39.450 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `pet_evolution_config` `pec` Left JOIN `pet_config` `pc` ON ( `pec`.`pet_no` = `pc`.`pet_no` )  Left JOIN `pet_config` `tpc` ON ( `pec`.`target_pet_no` = `tpc`.`pet_no` )  Left JOIN `item_config` `ic` ON ( `pec`.`required_item_id` = CAST(`ic`.`item_no` AS CHAR))   
[2025-09-04 11:56:39.635 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `pet_evolution_config` `pec` Left JOIN `pet_config` `pc` ON ( `pec`.`pet_no` = `pc`.`pet_no` )  Left JOIN `pet_config` `tpc` ON ( `pec`.`target_pet_no` = `tpc`.`pet_no` )  Left JOIN `item_config` `ic` ON ( `pec`.`required_item_id` = CAST(`ic`.`item_no` AS CHAR))   
[2025-09-04 11:56:39.648 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `pec`.`id` AS `Id` , `pec`.`pet_no` AS `PetNo` , IFNULL(`pc`.`name`,@MethodConst15) AS `PetName` , IFNULL(`pc`.`attribute`,@MethodConst16) AS `PetAttribute` , `pec`.`evolution_type` AS `EvolutionType` , `pec`.`target_pet_no` AS `TargetPetNo` , IFNULL(`tpc`.`name`,@MethodConst17) AS `TargetPetName` , IFNULL(`tpc`.`attribute`,@MethodConst18) AS `TargetPetAttribute` , `pec`.`required_level` AS `RequiredLevel` , `pec`.`required_item_id` AS `RequiredItemId` , IFNULL(`ic`.`name`,@MethodConst19) AS `RequiredItemName` , IFNULL(`pec`.`required_item_count`,@MethodConst20) AS `RequiredItemCount` , IFNULL(`pec`.`cost_gold`,@MethodConst21) AS `CostGold` , IFNULL(`pec`.`growth_min`,@MethodConst22) AS `GrowthMin` , IFNULL(`pec`.`growth_max`,@MethodConst23) AS `GrowthMax` , IFNULL(`pec`.`success_rate`,@MethodConst24) AS `SuccessRate` , ( CASE  WHEN ( `pec`.`is_active` = @is_active25 ) THEN @constant26  ELSE @constant27 END ) AS `IsActive` , IFNULL(`pec`.`description`,@MethodConst28) AS `Description` , IFNULL(`pec`.`create_time`,@MethodConst29) AS `CreateTime`  FROM `pet_evolution_config` `pec` Left JOIN `pet_config` `pc` ON ( `pec`.`pet_no` = `pc`.`pet_no` )  Left JOIN `pet_config` `tpc` ON ( `pec`.`target_pet_no` = `tpc`.`pet_no` )  Left JOIN `item_config` `ic` ON ( `pec`.`required_item_id` = CAST(`ic`.`item_no` AS CHAR))     ORDER BY `pec`.`id` DESC LIMIT 0,10 | 参数: [@MethodConst0=, @MethodConst1=, @MethodConst2=, @MethodConst3=, @MethodConst4=, @MethodConst5=1, @MethodConst6=1000, @MethodConst7=0.100, @MethodConst8=0.500, @MethodConst9=100.00, @is_active10=1, @constant11=True, @constant12=False, @MethodConst13=, @MethodConst14=2025/9/4 11:56:39, @MethodConst15=, @MethodConst16=, @MethodConst17=, @MethodConst18=, @MethodConst19=, @MethodConst20=1, @MethodConst21=1000, @MethodConst22=0.100, @MethodConst23=0.500, @MethodConst24=100.00, @is_active25=1, @constant26=True, @constant27=False, @MethodConst28=, @MethodConst29=2025/9/4 11:56:39]
[2025-09-04 11:56:40.022 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `pec`.`id` AS `Id` , `pec`.`pet_no` AS `PetNo` , IFNULL(`pc`.`name`,@MethodConst15) AS `PetName` , IFNULL(`pc`.`attribute`,@MethodConst16) AS `PetAttribute` , `pec`.`evolution_type` AS `EvolutionType` , `pec`.`target_pet_no` AS `TargetPetNo` , IFNULL(`tpc`.`name`,@MethodConst17) AS `TargetPetName` , IFNULL(`tpc`.`attribute`,@MethodConst18) AS `TargetPetAttribute` , `pec`.`required_level` AS `RequiredLevel` , `pec`.`required_item_id` AS `RequiredItemId` , IFNULL(`ic`.`name`,@MethodConst19) AS `RequiredItemName` , IFNULL(`pec`.`required_item_count`,@MethodConst20) AS `RequiredItemCount` , IFNULL(`pec`.`cost_gold`,@MethodConst21) AS `CostGold` , IFNULL(`pec`.`growth_min`,@MethodConst22) AS `GrowthMin` , IFNULL(`pec`.`growth_max`,@MethodConst23) AS `GrowthMax` , IFNULL(`pec`.`success_rate`,@MethodConst24) AS `SuccessRate` , ( CASE  WHEN ( `pec`.`is_active` = @is_active25 ) THEN @constant26  ELSE @constant27 END ) AS `IsActive` , IFNULL(`pec`.`description`,@MethodConst28) AS `Description` , IFNULL(`pec`.`create_time`,@MethodConst29) AS `CreateTime`  FROM `pet_evolution_config` `pec` Left JOIN `pet_config` `pc` ON ( `pec`.`pet_no` = `pc`.`pet_no` )  Left JOIN `pet_config` `tpc` ON ( `pec`.`target_pet_no` = `tpc`.`pet_no` )  Left JOIN `item_config` `ic` ON ( `pec`.`required_item_id` = CAST(`ic`.`item_no` AS CHAR))     ORDER BY `pec`.`id` DESC LIMIT 0,10
[2025-09-04 11:56:48.219 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 11:56:48.364 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 11:56:48.365 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 11:56:48.402 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 11:56:48.402 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 11:56:48.535 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 11:56:48.535 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 11:56:48.536 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 11:56:48.572 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 11:56:48.573 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 11:56:48.742 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `monster_config`  
[2025-09-04 11:56:48.786 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `monster_config`  
[2025-09-04 11:56:48.787 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`monster_no`,`skill`,`name`,`attribute` FROM `monster_config`    ORDER BY `monster_no` ASC LIMIT 0,10
[2025-09-04 11:56:48.822 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`monster_no`,`skill`,`name`,`attribute` FROM `monster_config`    ORDER BY `monster_no` ASC LIMIT 0,10
[2025-09-04 11:57:05.998 +08:00 INF] BMS: 🚀 应用程序启动，日志系统已配置
[2025-09-04 11:57:06.044 +08:00 DBG] BMS: 🔧 调试级别日志测试
[2025-09-04 11:57:06.067 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 11:57:06.070 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 11:57:06.167 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 11:57:06.709 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 11:57:06.733 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 11:57:06.734 +08:00 INF] BMS: ✅ DbContext 初始化成功，日志文件位置: logs/
[2025-09-04 11:57:07.628 +08:00 WRN] Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware: Failed to determine the https port for redirect.
[2025-09-04 11:57:32.709 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 11:57:32.734 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 11:57:32.740 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 11:57:32.779 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 11:57:32.782 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 11:57:32.921 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 11:57:32.922 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 11:57:32.924 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 11:57:32.969 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 11:57:32.974 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 11:57:33.024 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `pet_no` AS `PetNo` , IFNULL(`name`,@MethodConst0) AS `Name` , IFNULL(`attribute`,@MethodConst1) AS `Attribute`  FROM `pet_config` `x` ORDER BY `pet_no` ASC  | 参数: [@MethodConst0=, @MethodConst1=]
[2025-09-04 11:57:33.083 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `pet_no` AS `PetNo` , IFNULL(`name`,@MethodConst0) AS `Name` , IFNULL(`attribute`,@MethodConst1) AS `Attribute`  FROM `pet_config` `x` ORDER BY `pet_no` ASC 
[2025-09-04 11:57:33.163 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 11:57:33.174 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 11:57:33.176 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 11:57:33.246 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 11:57:33.249 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 11:57:33.260 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  CAST(`item_no` AS CHAR) AS `ItemId` , IFNULL(`name`,@MethodConst0) AS `Name` , IFNULL(`type`,@MethodConst1) AS `Type`  FROM `item_config` `x` ORDER BY `item_no` ASC  | 参数: [@MethodConst0=, @MethodConst1=]
[2025-09-04 11:57:33.303 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  CAST(`item_no` AS CHAR) AS `ItemId` , IFNULL(`name`,@MethodConst0) AS `Name` , IFNULL(`type`,@MethodConst1) AS `Type`  FROM `item_config` `x` ORDER BY `item_no` ASC 
[2025-09-04 11:57:33.359 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 11:57:33.363 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 11:57:33.365 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 11:57:33.406 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 11:57:33.429 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 11:57:33.557 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `pet_evolution_config` `pec` Left JOIN `pet_config` `pc` ON ( `pec`.`pet_no` = `pc`.`pet_no` )  Left JOIN `pet_config` `tpc` ON ( `pec`.`target_pet_no` = `tpc`.`pet_no` )  Left JOIN `item_config` `ic` ON ( `pec`.`required_item_id` = CAST(`ic`.`item_no` AS CHAR))   
[2025-09-04 11:57:33.765 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `pet_evolution_config` `pec` Left JOIN `pet_config` `pc` ON ( `pec`.`pet_no` = `pc`.`pet_no` )  Left JOIN `pet_config` `tpc` ON ( `pec`.`target_pet_no` = `tpc`.`pet_no` )  Left JOIN `item_config` `ic` ON ( `pec`.`required_item_id` = CAST(`ic`.`item_no` AS CHAR))   
[2025-09-04 11:57:33.779 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `pec`.`id` AS `Id` , `pec`.`pet_no` AS `PetNo` , IFNULL(`pc`.`name`,@MethodConst15) AS `PetName` , IFNULL(`pc`.`attribute`,@MethodConst16) AS `PetAttribute` , `pec`.`evolution_type` AS `EvolutionType` , `pec`.`target_pet_no` AS `TargetPetNo` , IFNULL(`tpc`.`name`,@MethodConst17) AS `TargetPetName` , IFNULL(`tpc`.`attribute`,@MethodConst18) AS `TargetPetAttribute` , `pec`.`required_level` AS `RequiredLevel` , `pec`.`required_item_id` AS `RequiredItemId` , IFNULL(`ic`.`name`,@MethodConst19) AS `RequiredItemName` , IFNULL(`pec`.`required_item_count`,@MethodConst20) AS `RequiredItemCount` , IFNULL(`pec`.`cost_gold`,@MethodConst21) AS `CostGold` , IFNULL(`pec`.`growth_min`,@MethodConst22) AS `GrowthMin` , IFNULL(`pec`.`growth_max`,@MethodConst23) AS `GrowthMax` , IFNULL(`pec`.`success_rate`,@MethodConst24) AS `SuccessRate` , ( CASE  WHEN ( `pec`.`is_active` = @is_active25 ) THEN @constant26  ELSE @constant27 END ) AS `IsActive` , IFNULL(`pec`.`description`,@MethodConst28) AS `Description` , IFNULL(`pec`.`create_time`,@MethodConst29) AS `CreateTime`  FROM `pet_evolution_config` `pec` Left JOIN `pet_config` `pc` ON ( `pec`.`pet_no` = `pc`.`pet_no` )  Left JOIN `pet_config` `tpc` ON ( `pec`.`target_pet_no` = `tpc`.`pet_no` )  Left JOIN `item_config` `ic` ON ( `pec`.`required_item_id` = CAST(`ic`.`item_no` AS CHAR))     ORDER BY `pec`.`id` DESC LIMIT 0,10 | 参数: [@MethodConst0=, @MethodConst1=, @MethodConst2=, @MethodConst3=, @MethodConst4=, @MethodConst5=1, @MethodConst6=1000, @MethodConst7=0.100, @MethodConst8=0.500, @MethodConst9=100.00, @is_active10=1, @constant11=True, @constant12=False, @MethodConst13=, @MethodConst14=2025/9/4 11:57:33, @MethodConst15=, @MethodConst16=, @MethodConst17=, @MethodConst18=, @MethodConst19=, @MethodConst20=1, @MethodConst21=1000, @MethodConst22=0.100, @MethodConst23=0.500, @MethodConst24=100.00, @is_active25=1, @constant26=True, @constant27=False, @MethodConst28=, @MethodConst29=2025/9/4 11:57:33]
[2025-09-04 11:57:34.122 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `pec`.`id` AS `Id` , `pec`.`pet_no` AS `PetNo` , IFNULL(`pc`.`name`,@MethodConst15) AS `PetName` , IFNULL(`pc`.`attribute`,@MethodConst16) AS `PetAttribute` , `pec`.`evolution_type` AS `EvolutionType` , `pec`.`target_pet_no` AS `TargetPetNo` , IFNULL(`tpc`.`name`,@MethodConst17) AS `TargetPetName` , IFNULL(`tpc`.`attribute`,@MethodConst18) AS `TargetPetAttribute` , `pec`.`required_level` AS `RequiredLevel` , `pec`.`required_item_id` AS `RequiredItemId` , IFNULL(`ic`.`name`,@MethodConst19) AS `RequiredItemName` , IFNULL(`pec`.`required_item_count`,@MethodConst20) AS `RequiredItemCount` , IFNULL(`pec`.`cost_gold`,@MethodConst21) AS `CostGold` , IFNULL(`pec`.`growth_min`,@MethodConst22) AS `GrowthMin` , IFNULL(`pec`.`growth_max`,@MethodConst23) AS `GrowthMax` , IFNULL(`pec`.`success_rate`,@MethodConst24) AS `SuccessRate` , ( CASE  WHEN ( `pec`.`is_active` = @is_active25 ) THEN @constant26  ELSE @constant27 END ) AS `IsActive` , IFNULL(`pec`.`description`,@MethodConst28) AS `Description` , IFNULL(`pec`.`create_time`,@MethodConst29) AS `CreateTime`  FROM `pet_evolution_config` `pec` Left JOIN `pet_config` `pc` ON ( `pec`.`pet_no` = `pc`.`pet_no` )  Left JOIN `pet_config` `tpc` ON ( `pec`.`target_pet_no` = `tpc`.`pet_no` )  Left JOIN `item_config` `ic` ON ( `pec`.`required_item_id` = CAST(`ic`.`item_no` AS CHAR))     ORDER BY `pec`.`id` DESC LIMIT 0,10
[2025-09-04 13:36:51.587 +08:00 INF] BMS: 🚀 应用程序启动，日志系统已配置
[2025-09-04 13:36:51.614 +08:00 DBG] BMS: 🔧 调试级别日志测试
[2025-09-04 13:36:51.633 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 13:36:51.647 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 13:36:51.736 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 13:36:52.711 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 13:36:52.719 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 13:36:52.720 +08:00 INF] BMS: ✅ DbContext 初始化成功，日志文件位置: logs/
[2025-09-04 13:36:53.700 +08:00 WRN] Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware: Failed to determine the https port for redirect.
[2025-09-04 13:37:12.329 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 13:37:12.331 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 13:37:12.338 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 13:37:12.541 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 13:37:12.543 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 13:37:33.208 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 13:37:33.209 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 13:37:33.211 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 13:37:33.276 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 13:37:33.301 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 13:37:33.361 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  DISTINCT `type` FROM `item_config`  WHERE NOT( `type` IS NULL   OR `type`='') 
[2025-09-04 13:37:33.460 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  DISTINCT `type` FROM `item_config`  WHERE NOT( `type` IS NULL   OR `type`='') 
[2025-09-04 13:37:33.490 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  DISTINCT `quality` FROM `item_config`  WHERE NOT( `quality` IS NULL   OR `quality`='') 
[2025-09-04 13:37:33.627 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  DISTINCT `quality` FROM `item_config`  WHERE NOT( `quality` IS NULL   OR `quality`='') 
[2025-09-04 13:37:33.738 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 13:37:33.740 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 13:37:33.741 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 13:37:33.812 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 13:37:33.814 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 13:37:33.940 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-09-04 13:37:34.258 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-09-04 13:37:34.263 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 0,10
[2025-09-04 13:37:35.061 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 0,10
[2025-09-04 13:37:40.469 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 13:37:40.472 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 13:37:40.475 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 13:37:40.545 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 13:37:40.581 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 13:44:48.796 +08:00 INF] BMS: 🚀 应用程序启动，日志系统已配置
[2025-09-04 13:44:48.821 +08:00 DBG] BMS: 🔧 调试级别日志测试
[2025-09-04 13:44:48.825 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 13:44:48.826 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 13:44:48.870 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 13:44:49.391 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 13:44:49.398 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 13:44:49.400 +08:00 INF] BMS: ✅ DbContext 初始化成功，日志文件位置: logs/
[2025-09-04 13:46:49.012 +08:00 INF] BMS: 🚀 应用程序启动，日志系统已配置
[2025-09-04 13:46:49.036 +08:00 DBG] BMS: 🔧 调试级别日志测试
[2025-09-04 13:46:49.040 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 13:46:49.042 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 13:46:49.085 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 13:46:49.575 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 13:46:49.583 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 13:46:49.584 +08:00 INF] BMS: ✅ DbContext 初始化成功，日志文件位置: logs/
[2025-09-04 13:48:50.684 +08:00 INF] BMS: 🚀 应用程序启动，日志系统已配置
[2025-09-04 13:48:50.714 +08:00 DBG] BMS: 🔧 调试级别日志测试
[2025-09-04 13:48:50.728 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 13:48:50.731 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 13:48:50.810 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 13:48:51.313 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 13:48:51.406 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 13:48:51.408 +08:00 INF] BMS: ✅ DbContext 初始化成功，日志文件位置: logs/
[2025-09-04 13:48:54.317 +08:00 WRN] Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware: Failed to determine the https port for redirect.
[2025-09-04 13:48:59.351 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 13:48:59.904 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 13:49:00.187 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 13:49:00.426 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 13:49:00.564 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 13:49:00.813 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 13:49:00.813 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 13:49:00.816 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 13:49:00.935 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 13:49:01.054 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 13:49:01.226 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 13:49:01.560 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 13:49:01.805 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 13:49:01.931 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 13:49:02.114 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 13:49:02.442 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 13:49:02.535 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 13:49:02.629 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 13:49:02.667 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  CAST(`item_no` AS CHAR) AS `ItemId` , IFNULL(`name`,@MethodConst0) AS `Name` , IFNULL(`type`,@MethodConst1) AS `Type`  FROM `item_config` `x` ORDER BY `item_no` ASC  | 参数: [@MethodConst0=, @MethodConst1=]
[2025-09-04 13:49:02.811 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 13:49:03.112 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 13:49:03.333 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  CAST(`item_no` AS CHAR) AS `ItemId` , IFNULL(`name`,@MethodConst0) AS `Name` , IFNULL(`type`,@MethodConst1) AS `Type`  FROM `item_config` `x` ORDER BY `item_no` ASC 
[2025-09-04 13:49:03.365 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `pet_no` AS `PetNo` , IFNULL(`name`,@MethodConst0) AS `Name` , IFNULL(`attribute`,@MethodConst1) AS `Attribute`  FROM `pet_config` `x` ORDER BY `pet_no` ASC  | 参数: [@MethodConst0=, @MethodConst1=]
[2025-09-04 13:49:03.601 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `pet_evolution_config` `pec` Left JOIN `pet_config` `pc` ON ( `pec`.`pet_no` = `pc`.`pet_no` )  Left JOIN `pet_config` `tpc` ON ( `pec`.`target_pet_no` = `tpc`.`pet_no` )  Left JOIN `item_config` `ic` ON ( `pec`.`required_item_id` = CAST(`ic`.`item_no` AS CHAR))   
[2025-09-04 13:49:03.699 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `pet_no` AS `PetNo` , IFNULL(`name`,@MethodConst0) AS `Name` , IFNULL(`attribute`,@MethodConst1) AS `Attribute`  FROM `pet_config` `x` ORDER BY `pet_no` ASC 
[2025-09-04 13:49:03.894 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `pet_evolution_config` `pec` Left JOIN `pet_config` `pc` ON ( `pec`.`pet_no` = `pc`.`pet_no` )  Left JOIN `pet_config` `tpc` ON ( `pec`.`target_pet_no` = `tpc`.`pet_no` )  Left JOIN `item_config` `ic` ON ( `pec`.`required_item_id` = CAST(`ic`.`item_no` AS CHAR))   
[2025-09-04 13:49:03.913 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `pec`.`id` AS `Id` , `pec`.`pet_no` AS `PetNo` , IFNULL(`pc`.`name`,@MethodConst15) AS `PetName` , IFNULL(`pc`.`attribute`,@MethodConst16) AS `PetAttribute` , `pec`.`evolution_type` AS `EvolutionType` , `pec`.`target_pet_no` AS `TargetPetNo` , IFNULL(`tpc`.`name`,@MethodConst17) AS `TargetPetName` , IFNULL(`tpc`.`attribute`,@MethodConst18) AS `TargetPetAttribute` , `pec`.`required_level` AS `RequiredLevel` , `pec`.`required_item_id` AS `RequiredItemId` , IFNULL(`ic`.`name`,@MethodConst19) AS `RequiredItemName` , IFNULL(`pec`.`required_item_count`,@MethodConst20) AS `RequiredItemCount` , IFNULL(`pec`.`cost_gold`,@MethodConst21) AS `CostGold` , IFNULL(`pec`.`growth_min`,@MethodConst22) AS `GrowthMin` , IFNULL(`pec`.`growth_max`,@MethodConst23) AS `GrowthMax` , IFNULL(`pec`.`success_rate`,@MethodConst24) AS `SuccessRate` , ( CASE  WHEN ( `pec`.`is_active` = @is_active25 ) THEN @constant26  ELSE @constant27 END ) AS `IsActive` , IFNULL(`pec`.`description`,@MethodConst28) AS `Description` , IFNULL(`pec`.`create_time`,@MethodConst29) AS `CreateTime`  FROM `pet_evolution_config` `pec` Left JOIN `pet_config` `pc` ON ( `pec`.`pet_no` = `pc`.`pet_no` )  Left JOIN `pet_config` `tpc` ON ( `pec`.`target_pet_no` = `tpc`.`pet_no` )  Left JOIN `item_config` `ic` ON ( `pec`.`required_item_id` = CAST(`ic`.`item_no` AS CHAR))     ORDER BY `pec`.`id` DESC LIMIT 0,10 | 参数: [@MethodConst0=, @MethodConst1=, @MethodConst2=, @MethodConst3=, @MethodConst4=, @MethodConst5=1, @MethodConst6=1000, @MethodConst7=0.100, @MethodConst8=0.500, @MethodConst9=100.00, @is_active10=1, @constant11=True, @constant12=False, @MethodConst13=, @MethodConst14=2025/9/4 13:49:03, @MethodConst15=, @MethodConst16=, @MethodConst17=, @MethodConst18=, @MethodConst19=, @MethodConst20=1, @MethodConst21=1000, @MethodConst22=0.100, @MethodConst23=0.500, @MethodConst24=100.00, @is_active25=1, @constant26=True, @constant27=False, @MethodConst28=, @MethodConst29=2025/9/4 13:49:03]
[2025-09-04 13:49:04.281 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `pec`.`id` AS `Id` , `pec`.`pet_no` AS `PetNo` , IFNULL(`pc`.`name`,@MethodConst15) AS `PetName` , IFNULL(`pc`.`attribute`,@MethodConst16) AS `PetAttribute` , `pec`.`evolution_type` AS `EvolutionType` , `pec`.`target_pet_no` AS `TargetPetNo` , IFNULL(`tpc`.`name`,@MethodConst17) AS `TargetPetName` , IFNULL(`tpc`.`attribute`,@MethodConst18) AS `TargetPetAttribute` , `pec`.`required_level` AS `RequiredLevel` , `pec`.`required_item_id` AS `RequiredItemId` , IFNULL(`ic`.`name`,@MethodConst19) AS `RequiredItemName` , IFNULL(`pec`.`required_item_count`,@MethodConst20) AS `RequiredItemCount` , IFNULL(`pec`.`cost_gold`,@MethodConst21) AS `CostGold` , IFNULL(`pec`.`growth_min`,@MethodConst22) AS `GrowthMin` , IFNULL(`pec`.`growth_max`,@MethodConst23) AS `GrowthMax` , IFNULL(`pec`.`success_rate`,@MethodConst24) AS `SuccessRate` , ( CASE  WHEN ( `pec`.`is_active` = @is_active25 ) THEN @constant26  ELSE @constant27 END ) AS `IsActive` , IFNULL(`pec`.`description`,@MethodConst28) AS `Description` , IFNULL(`pec`.`create_time`,@MethodConst29) AS `CreateTime`  FROM `pet_evolution_config` `pec` Left JOIN `pet_config` `pc` ON ( `pec`.`pet_no` = `pc`.`pet_no` )  Left JOIN `pet_config` `tpc` ON ( `pec`.`target_pet_no` = `tpc`.`pet_no` )  Left JOIN `item_config` `ic` ON ( `pec`.`required_item_id` = CAST(`ic`.`item_no` AS CHAR))     ORDER BY `pec`.`id` DESC LIMIT 0,10
[2025-09-04 13:54:08.533 +08:00 INF] BMS: 🚀 应用程序启动，日志系统已配置
[2025-09-04 13:54:08.556 +08:00 DBG] BMS: 🔧 调试级别日志测试
[2025-09-04 13:54:08.561 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 13:54:08.563 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 13:54:08.606 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 13:54:09.097 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 13:54:09.104 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 13:54:09.106 +08:00 INF] BMS: ✅ DbContext 初始化成功，日志文件位置: logs/
[2025-09-04 13:55:26.409 +08:00 INF] BMS: 🚀 应用程序启动，日志系统已配置
[2025-09-04 13:55:26.569 +08:00 DBG] BMS: 🔧 调试级别日志测试
[2025-09-04 13:55:26.635 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 13:55:26.642 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 13:55:26.796 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 13:55:27.022 +08:00 WRN] Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware: Failed to determine the https port for redirect.
[2025-09-04 13:55:27.507 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 13:55:27.537 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 13:55:27.569 +08:00 INF] BMS: ✅ DbContext 初始化成功，日志文件位置: logs/
[2025-09-04 13:55:27.881 +08:00 ERR] Microsoft.Extensions.Hosting.Internal.Host: Hosting failed to start
System.IO.IOException: Failed to bind to address http://127.0.0.1:5078: address already in use.
 ---> Microsoft.AspNetCore.Connections.AddressInUseException: 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
 ---> System.Net.Sockets.SocketException (10048): 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
   at System.Net.Sockets.Socket.UpdateStatusAfterSocketErrorAndThrowException(SocketError error, Boolean disconnectOnFailure, String callerName)
   at System.Net.Sockets.Socket.DoBind(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Bind(EndPoint localEP)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportOptions.CreateDefaultBoundListenSocket(EndPoint endpoint)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportFactory.BindAsync(EndPoint endpoint, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Infrastructure.TransportManager.BindAsync(EndPoint endPoint, ConnectionDelegate connectionDelegate, EndpointConfig endpointConfig, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.<>c__DisplayClass28_0`1.<<StartAsync>g__OnBind|0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.LocalhostListenOptions.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.AddressesStrategy.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindAsync(ListenOptions[] listenOptions, AddressBindContext context, Func`2 useHttps, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.BindAsync(CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.StartAsync[TContext](IHttpApplication`1 application, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Hosting.GenericWebHostService.StartAsync(CancellationToken cancellationToken)
   at Microsoft.Extensions.Hosting.Internal.Host.<StartAsync>b__15_1(IHostedService service, CancellationToken token)
   at Microsoft.Extensions.Hosting.Internal.Host.ForeachService[T](IEnumerable`1 services, CancellationToken token, Boolean concurrent, Boolean abortOnFirstException, List`1 exceptions, Func`3 operation)
[2025-09-04 13:55:28.897 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 13:55:28.980 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 13:55:29.023 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 13:55:29.119 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 13:55:29.124 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 13:55:29.226 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 13:55:29.226 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 13:55:29.251 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 13:55:29.249 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 13:55:29.230 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 13:55:29.255 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 13:55:29.258 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 13:55:29.259 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 13:55:29.270 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 13:55:29.323 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 13:55:29.326 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 13:55:29.431 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `pet_no` AS `PetNo` , IFNULL(`name`,@MethodConst0) AS `Name` , IFNULL(`attribute`,@MethodConst1) AS `Attribute`  FROM `pet_config` `x` ORDER BY `pet_no` ASC  | 参数: [@MethodConst0=, @MethodConst1=]
[2025-09-04 13:55:29.476 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 13:55:29.529 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 13:55:29.541 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 13:55:29.543 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 13:55:29.551 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  CAST(`item_no` AS CHAR) AS `ItemId` , IFNULL(`name`,@MethodConst0) AS `Name` , IFNULL(`type`,@MethodConst1) AS `Type`  FROM `item_config` `x` ORDER BY `item_no` ASC  | 参数: [@MethodConst0=, @MethodConst1=]
[2025-09-04 13:55:29.600 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `pet_no` AS `PetNo` , IFNULL(`name`,@MethodConst0) AS `Name` , IFNULL(`attribute`,@MethodConst1) AS `Attribute`  FROM `pet_config` `x` ORDER BY `pet_no` ASC 
[2025-09-04 13:55:29.815 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  CAST(`item_no` AS CHAR) AS `ItemId` , IFNULL(`name`,@MethodConst0) AS `Name` , IFNULL(`type`,@MethodConst1) AS `Type`  FROM `item_config` `x` ORDER BY `item_no` ASC 
[2025-09-04 13:55:29.912 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `pet_evolution_config` `pec` Left JOIN `pet_config` `pc` ON ( `pec`.`pet_no` = `pc`.`pet_no` )  Left JOIN `pet_config` `tpc` ON ( `pec`.`target_pet_no` = `tpc`.`pet_no` )  Left JOIN `item_config` `ic` ON ( `pec`.`required_item_id` = CAST(`ic`.`item_no` AS CHAR))   
[2025-09-04 13:55:30.132 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `pet_evolution_config` `pec` Left JOIN `pet_config` `pc` ON ( `pec`.`pet_no` = `pc`.`pet_no` )  Left JOIN `pet_config` `tpc` ON ( `pec`.`target_pet_no` = `tpc`.`pet_no` )  Left JOIN `item_config` `ic` ON ( `pec`.`required_item_id` = CAST(`ic`.`item_no` AS CHAR))   
[2025-09-04 13:55:30.226 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `pec`.`id` AS `Id` , `pec`.`pet_no` AS `PetNo` , IFNULL(`pc`.`name`,@MethodConst15) AS `PetName` , IFNULL(`pc`.`attribute`,@MethodConst16) AS `PetAttribute` , `pec`.`evolution_type` AS `EvolutionType` , `pec`.`target_pet_no` AS `TargetPetNo` , IFNULL(`tpc`.`name`,@MethodConst17) AS `TargetPetName` , IFNULL(`tpc`.`attribute`,@MethodConst18) AS `TargetPetAttribute` , `pec`.`required_level` AS `RequiredLevel` , `pec`.`required_item_id` AS `RequiredItemId` , IFNULL(`ic`.`name`,@MethodConst19) AS `RequiredItemName` , IFNULL(`pec`.`required_item_count`,@MethodConst20) AS `RequiredItemCount` , IFNULL(`pec`.`cost_gold`,@MethodConst21) AS `CostGold` , IFNULL(`pec`.`growth_min`,@MethodConst22) AS `GrowthMin` , IFNULL(`pec`.`growth_max`,@MethodConst23) AS `GrowthMax` , IFNULL(`pec`.`success_rate`,@MethodConst24) AS `SuccessRate` , ( CASE  WHEN ( `pec`.`is_active` = @is_active25 ) THEN @constant26  ELSE @constant27 END ) AS `IsActive` , IFNULL(`pec`.`description`,@MethodConst28) AS `Description` , IFNULL(`pec`.`create_time`,@MethodConst29) AS `CreateTime`  FROM `pet_evolution_config` `pec` Left JOIN `pet_config` `pc` ON ( `pec`.`pet_no` = `pc`.`pet_no` )  Left JOIN `pet_config` `tpc` ON ( `pec`.`target_pet_no` = `tpc`.`pet_no` )  Left JOIN `item_config` `ic` ON ( `pec`.`required_item_id` = CAST(`ic`.`item_no` AS CHAR))     ORDER BY `pec`.`id` DESC LIMIT 0,10 | 参数: [@MethodConst0=, @MethodConst1=, @MethodConst2=, @MethodConst3=, @MethodConst4=, @MethodConst5=1, @MethodConst6=1000, @MethodConst7=0.100, @MethodConst8=0.500, @MethodConst9=100.00, @is_active10=1, @constant11=True, @constant12=False, @MethodConst13=, @MethodConst14=2025/9/4 13:55:30, @MethodConst15=, @MethodConst16=, @MethodConst17=, @MethodConst18=, @MethodConst19=, @MethodConst20=1, @MethodConst21=1000, @MethodConst22=0.100, @MethodConst23=0.500, @MethodConst24=100.00, @is_active25=1, @constant26=True, @constant27=False, @MethodConst28=, @MethodConst29=2025/9/4 13:55:30]
[2025-09-04 13:55:30.576 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `pec`.`id` AS `Id` , `pec`.`pet_no` AS `PetNo` , IFNULL(`pc`.`name`,@MethodConst15) AS `PetName` , IFNULL(`pc`.`attribute`,@MethodConst16) AS `PetAttribute` , `pec`.`evolution_type` AS `EvolutionType` , `pec`.`target_pet_no` AS `TargetPetNo` , IFNULL(`tpc`.`name`,@MethodConst17) AS `TargetPetName` , IFNULL(`tpc`.`attribute`,@MethodConst18) AS `TargetPetAttribute` , `pec`.`required_level` AS `RequiredLevel` , `pec`.`required_item_id` AS `RequiredItemId` , IFNULL(`ic`.`name`,@MethodConst19) AS `RequiredItemName` , IFNULL(`pec`.`required_item_count`,@MethodConst20) AS `RequiredItemCount` , IFNULL(`pec`.`cost_gold`,@MethodConst21) AS `CostGold` , IFNULL(`pec`.`growth_min`,@MethodConst22) AS `GrowthMin` , IFNULL(`pec`.`growth_max`,@MethodConst23) AS `GrowthMax` , IFNULL(`pec`.`success_rate`,@MethodConst24) AS `SuccessRate` , ( CASE  WHEN ( `pec`.`is_active` = @is_active25 ) THEN @constant26  ELSE @constant27 END ) AS `IsActive` , IFNULL(`pec`.`description`,@MethodConst28) AS `Description` , IFNULL(`pec`.`create_time`,@MethodConst29) AS `CreateTime`  FROM `pet_evolution_config` `pec` Left JOIN `pet_config` `pc` ON ( `pec`.`pet_no` = `pc`.`pet_no` )  Left JOIN `pet_config` `tpc` ON ( `pec`.`target_pet_no` = `tpc`.`pet_no` )  Left JOIN `item_config` `ic` ON ( `pec`.`required_item_id` = CAST(`ic`.`item_no` AS CHAR))     ORDER BY `pec`.`id` DESC LIMIT 0,10
[2025-09-04 13:55:47.608 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 13:55:47.613 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 13:55:47.616 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 13:55:47.670 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 13:55:47.672 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 13:55:47.675 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `pet_evolution_config` `pec` Left JOIN `pet_config` `pc` ON ( `pec`.`pet_no` = `pc`.`pet_no` )  Left JOIN `pet_config` `tpc` ON ( `pec`.`target_pet_no` = `tpc`.`pet_no` )  Left JOIN `item_config` `ic` ON ( `pec`.`required_item_id` = CAST(`ic`.`item_no` AS CHAR))   
[2025-09-04 13:55:47.891 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `pet_evolution_config` `pec` Left JOIN `pet_config` `pc` ON ( `pec`.`pet_no` = `pc`.`pet_no` )  Left JOIN `pet_config` `tpc` ON ( `pec`.`target_pet_no` = `tpc`.`pet_no` )  Left JOIN `item_config` `ic` ON ( `pec`.`required_item_id` = CAST(`ic`.`item_no` AS CHAR))   
[2025-09-04 13:55:47.897 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `pec`.`id` AS `Id` , `pec`.`pet_no` AS `PetNo` , IFNULL(`pc`.`name`,@MethodConst15) AS `PetName` , IFNULL(`pc`.`attribute`,@MethodConst16) AS `PetAttribute` , `pec`.`evolution_type` AS `EvolutionType` , `pec`.`target_pet_no` AS `TargetPetNo` , IFNULL(`tpc`.`name`,@MethodConst17) AS `TargetPetName` , IFNULL(`tpc`.`attribute`,@MethodConst18) AS `TargetPetAttribute` , `pec`.`required_level` AS `RequiredLevel` , `pec`.`required_item_id` AS `RequiredItemId` , IFNULL(`ic`.`name`,@MethodConst19) AS `RequiredItemName` , IFNULL(`pec`.`required_item_count`,@MethodConst20) AS `RequiredItemCount` , IFNULL(`pec`.`cost_gold`,@MethodConst21) AS `CostGold` , IFNULL(`pec`.`growth_min`,@MethodConst22) AS `GrowthMin` , IFNULL(`pec`.`growth_max`,@MethodConst23) AS `GrowthMax` , IFNULL(`pec`.`success_rate`,@MethodConst24) AS `SuccessRate` , ( CASE  WHEN ( `pec`.`is_active` = @is_active25 ) THEN @constant26  ELSE @constant27 END ) AS `IsActive` , IFNULL(`pec`.`description`,@MethodConst28) AS `Description` , IFNULL(`pec`.`create_time`,@MethodConst29) AS `CreateTime`  FROM `pet_evolution_config` `pec` Left JOIN `pet_config` `pc` ON ( `pec`.`pet_no` = `pc`.`pet_no` )  Left JOIN `pet_config` `tpc` ON ( `pec`.`target_pet_no` = `tpc`.`pet_no` )  Left JOIN `item_config` `ic` ON ( `pec`.`required_item_id` = CAST(`ic`.`item_no` AS CHAR))     ORDER BY `pec`.`id` DESC LIMIT 10,10 | 参数: [@MethodConst0=, @MethodConst1=, @MethodConst2=, @MethodConst3=, @MethodConst4=, @MethodConst5=1, @MethodConst6=1000, @MethodConst7=0.100, @MethodConst8=0.500, @MethodConst9=100.00, @is_active10=1, @constant11=True, @constant12=False, @MethodConst13=, @MethodConst14=2025/9/4 13:55:47, @MethodConst15=, @MethodConst16=, @MethodConst17=, @MethodConst18=, @MethodConst19=, @MethodConst20=1, @MethodConst21=1000, @MethodConst22=0.100, @MethodConst23=0.500, @MethodConst24=100.00, @is_active25=1, @constant26=True, @constant27=False, @MethodConst28=, @MethodConst29=2025/9/4 13:55:47]
[2025-09-04 13:55:48.235 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `pec`.`id` AS `Id` , `pec`.`pet_no` AS `PetNo` , IFNULL(`pc`.`name`,@MethodConst15) AS `PetName` , IFNULL(`pc`.`attribute`,@MethodConst16) AS `PetAttribute` , `pec`.`evolution_type` AS `EvolutionType` , `pec`.`target_pet_no` AS `TargetPetNo` , IFNULL(`tpc`.`name`,@MethodConst17) AS `TargetPetName` , IFNULL(`tpc`.`attribute`,@MethodConst18) AS `TargetPetAttribute` , `pec`.`required_level` AS `RequiredLevel` , `pec`.`required_item_id` AS `RequiredItemId` , IFNULL(`ic`.`name`,@MethodConst19) AS `RequiredItemName` , IFNULL(`pec`.`required_item_count`,@MethodConst20) AS `RequiredItemCount` , IFNULL(`pec`.`cost_gold`,@MethodConst21) AS `CostGold` , IFNULL(`pec`.`growth_min`,@MethodConst22) AS `GrowthMin` , IFNULL(`pec`.`growth_max`,@MethodConst23) AS `GrowthMax` , IFNULL(`pec`.`success_rate`,@MethodConst24) AS `SuccessRate` , ( CASE  WHEN ( `pec`.`is_active` = @is_active25 ) THEN @constant26  ELSE @constant27 END ) AS `IsActive` , IFNULL(`pec`.`description`,@MethodConst28) AS `Description` , IFNULL(`pec`.`create_time`,@MethodConst29) AS `CreateTime`  FROM `pet_evolution_config` `pec` Left JOIN `pet_config` `pc` ON ( `pec`.`pet_no` = `pc`.`pet_no` )  Left JOIN `pet_config` `tpc` ON ( `pec`.`target_pet_no` = `tpc`.`pet_no` )  Left JOIN `item_config` `ic` ON ( `pec`.`required_item_id` = CAST(`ic`.`item_no` AS CHAR))     ORDER BY `pec`.`id` DESC LIMIT 10,10
[2025-09-04 13:56:54.121 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 13:56:54.121 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 13:56:54.122 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 13:56:54.174 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 13:56:54.175 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 13:56:55.389 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 13:56:55.390 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 13:56:55.391 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 13:56:55.441 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 13:56:55.443 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 13:56:55.445 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `pet_no` AS `PetNo` , IFNULL(`name`,@MethodConst0) AS `Name` , IFNULL(`attribute`,@MethodConst1) AS `Attribute`  FROM `pet_config` `x` ORDER BY `pet_no` ASC  | 参数: [@MethodConst0=, @MethodConst1=]
[2025-09-04 13:56:55.499 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `pet_no` AS `PetNo` , IFNULL(`name`,@MethodConst0) AS `Name` , IFNULL(`attribute`,@MethodConst1) AS `Attribute`  FROM `pet_config` `x` ORDER BY `pet_no` ASC 
[2025-09-04 13:56:55.532 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 13:56:55.533 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 13:56:55.534 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 13:56:55.585 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 13:56:55.586 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 13:56:55.602 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `pet_synthesis_formula` `psf` Left JOIN `pet_config` `mpc` ON ( `psf`.`main_pet_no` = `mpc`.`pet_no` )  Left JOIN `pet_config` `spc` ON ( `psf`.`sub_pet_no` = `spc`.`pet_no` )  Left JOIN `pet_config` `rpc` ON ( `psf`.`result_pet_no` = `rpc`.`pet_no` )   
[2025-09-04 13:56:55.656 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `pet_synthesis_formula` `psf` Left JOIN `pet_config` `mpc` ON ( `psf`.`main_pet_no` = `mpc`.`pet_no` )  Left JOIN `pet_config` `spc` ON ( `psf`.`sub_pet_no` = `spc`.`pet_no` )  Left JOIN `pet_config` `rpc` ON ( `psf`.`result_pet_no` = `rpc`.`pet_no` )   
[2025-09-04 13:56:55.665 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `psf`.`id` AS `Id` , `psf`.`main_pet_no` AS `MainPetNo` , IFNULL(`mpc`.`name`,concat('宠物', CAST(`psf`.`main_pet_no` AS CHAR),'')) AS `MainPetName` , IFNULL(`mpc`.`attribute`,@MethodConst15) AS `MainPetAttribute` , `psf`.`sub_pet_no` AS `SubPetNo` , IFNULL(`spc`.`name`,concat('宠物', CAST(`psf`.`sub_pet_no` AS CHAR),'')) AS `SubPetName` , IFNULL(`spc`.`attribute`,@MethodConst17) AS `SubPetAttribute` , IFNULL(`psf`.`main_growth_min`,@MethodConst18) AS `MainGrowthMin` , IFNULL(`psf`.`sub_growth_min`,@MethodConst19) AS `SubGrowthMin` , `psf`.`result_pet_no` AS `ResultPetNo` , IFNULL(`rpc`.`name`,concat('宠物', CAST(`psf`.`result_pet_no` AS CHAR),'')) AS `ResultPetName` , IFNULL(`rpc`.`attribute`,@MethodConst21) AS `ResultPetAttribute` , IFNULL(`psf`.`base_success_rate`,@MethodConst22) AS `BaseSuccessRate` , IFNULL(`psf`.`required_level`,@MethodConst23) AS `RequiredLevel` , IFNULL(`psf`.`cost_gold`,@MethodConst24) AS `CostGold` , IFNULL(`psf`.`formula_type`,@MethodConst25) AS `FormulaType` , IFNULL(CAST(`psf`.`is_active` as SIGNED),@MethodConst26) AS `IsActive` , IFNULL(`psf`.`create_time`,@MethodConst27) AS `CreateTime`  FROM `pet_synthesis_formula` `psf` Left JOIN `pet_config` `mpc` ON ( `psf`.`main_pet_no` = `mpc`.`pet_no` )  Left JOIN `pet_config` `spc` ON ( `psf`.`sub_pet_no` = `spc`.`pet_no` )  Left JOIN `pet_config` `rpc` ON ( `psf`.`result_pet_no` = `rpc`.`pet_no` )     ORDER BY `psf`.`id` DESC LIMIT 0,10 | 参数: [@MethodConst1=未知, @MethodConst3=未知, @MethodConst4=0.000000, @MethodConst5=0.000000, @MethodConst7=未知, @MethodConst8=50.00, @MethodConst9=40, @MethodConst10=50000, @MethodConst11=FIXED, @MethodConst12=True, @MethodConst13=2025/9/4 13:56:55, @MethodConst15=未知, @MethodConst17=未知, @MethodConst18=0.000000, @MethodConst19=0.000000, @MethodConst21=未知, @MethodConst22=50.00, @MethodConst23=40, @MethodConst24=50000, @MethodConst25=FIXED, @MethodConst26=True, @MethodConst27=2025/9/4 13:56:55]
[2025-09-04 13:56:55.731 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `psf`.`id` AS `Id` , `psf`.`main_pet_no` AS `MainPetNo` , IFNULL(`mpc`.`name`,concat('宠物', CAST(`psf`.`main_pet_no` AS CHAR),'')) AS `MainPetName` , IFNULL(`mpc`.`attribute`,@MethodConst15) AS `MainPetAttribute` , `psf`.`sub_pet_no` AS `SubPetNo` , IFNULL(`spc`.`name`,concat('宠物', CAST(`psf`.`sub_pet_no` AS CHAR),'')) AS `SubPetName` , IFNULL(`spc`.`attribute`,@MethodConst17) AS `SubPetAttribute` , IFNULL(`psf`.`main_growth_min`,@MethodConst18) AS `MainGrowthMin` , IFNULL(`psf`.`sub_growth_min`,@MethodConst19) AS `SubGrowthMin` , `psf`.`result_pet_no` AS `ResultPetNo` , IFNULL(`rpc`.`name`,concat('宠物', CAST(`psf`.`result_pet_no` AS CHAR),'')) AS `ResultPetName` , IFNULL(`rpc`.`attribute`,@MethodConst21) AS `ResultPetAttribute` , IFNULL(`psf`.`base_success_rate`,@MethodConst22) AS `BaseSuccessRate` , IFNULL(`psf`.`required_level`,@MethodConst23) AS `RequiredLevel` , IFNULL(`psf`.`cost_gold`,@MethodConst24) AS `CostGold` , IFNULL(`psf`.`formula_type`,@MethodConst25) AS `FormulaType` , IFNULL(CAST(`psf`.`is_active` as SIGNED),@MethodConst26) AS `IsActive` , IFNULL(`psf`.`create_time`,@MethodConst27) AS `CreateTime`  FROM `pet_synthesis_formula` `psf` Left JOIN `pet_config` `mpc` ON ( `psf`.`main_pet_no` = `mpc`.`pet_no` )  Left JOIN `pet_config` `spc` ON ( `psf`.`sub_pet_no` = `spc`.`pet_no` )  Left JOIN `pet_config` `rpc` ON ( `psf`.`result_pet_no` = `rpc`.`pet_no` )     ORDER BY `psf`.`id` DESC LIMIT 0,10
[2025-09-04 13:57:13.597 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 13:57:13.598 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 13:57:13.599 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 13:57:13.649 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 13:57:13.650 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 13:57:13.651 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `pet_synthesis_formula` `psf` Left JOIN `pet_config` `mpc` ON ( `psf`.`main_pet_no` = `mpc`.`pet_no` )  Left JOIN `pet_config` `spc` ON ( `psf`.`sub_pet_no` = `spc`.`pet_no` )  Left JOIN `pet_config` `rpc` ON ( `psf`.`result_pet_no` = `rpc`.`pet_no` )   
[2025-09-04 13:57:13.711 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `pet_synthesis_formula` `psf` Left JOIN `pet_config` `mpc` ON ( `psf`.`main_pet_no` = `mpc`.`pet_no` )  Left JOIN `pet_config` `spc` ON ( `psf`.`sub_pet_no` = `spc`.`pet_no` )  Left JOIN `pet_config` `rpc` ON ( `psf`.`result_pet_no` = `rpc`.`pet_no` )   
[2025-09-04 13:57:13.713 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `psf`.`id` AS `Id` , `psf`.`main_pet_no` AS `MainPetNo` , IFNULL(`mpc`.`name`,concat('宠物', CAST(`psf`.`main_pet_no` AS CHAR),'')) AS `MainPetName` , IFNULL(`mpc`.`attribute`,@MethodConst15) AS `MainPetAttribute` , `psf`.`sub_pet_no` AS `SubPetNo` , IFNULL(`spc`.`name`,concat('宠物', CAST(`psf`.`sub_pet_no` AS CHAR),'')) AS `SubPetName` , IFNULL(`spc`.`attribute`,@MethodConst17) AS `SubPetAttribute` , IFNULL(`psf`.`main_growth_min`,@MethodConst18) AS `MainGrowthMin` , IFNULL(`psf`.`sub_growth_min`,@MethodConst19) AS `SubGrowthMin` , `psf`.`result_pet_no` AS `ResultPetNo` , IFNULL(`rpc`.`name`,concat('宠物', CAST(`psf`.`result_pet_no` AS CHAR),'')) AS `ResultPetName` , IFNULL(`rpc`.`attribute`,@MethodConst21) AS `ResultPetAttribute` , IFNULL(`psf`.`base_success_rate`,@MethodConst22) AS `BaseSuccessRate` , IFNULL(`psf`.`required_level`,@MethodConst23) AS `RequiredLevel` , IFNULL(`psf`.`cost_gold`,@MethodConst24) AS `CostGold` , IFNULL(`psf`.`formula_type`,@MethodConst25) AS `FormulaType` , IFNULL(CAST(`psf`.`is_active` as SIGNED),@MethodConst26) AS `IsActive` , IFNULL(`psf`.`create_time`,@MethodConst27) AS `CreateTime`  FROM `pet_synthesis_formula` `psf` Left JOIN `pet_config` `mpc` ON ( `psf`.`main_pet_no` = `mpc`.`pet_no` )  Left JOIN `pet_config` `spc` ON ( `psf`.`sub_pet_no` = `spc`.`pet_no` )  Left JOIN `pet_config` `rpc` ON ( `psf`.`result_pet_no` = `rpc`.`pet_no` )     ORDER BY `psf`.`id` DESC LIMIT 0,10 | 参数: [@MethodConst1=未知, @MethodConst3=未知, @MethodConst4=0.000000, @MethodConst5=0.000000, @MethodConst7=未知, @MethodConst8=50.00, @MethodConst9=40, @MethodConst10=50000, @MethodConst11=FIXED, @MethodConst12=True, @MethodConst13=2025/9/4 13:57:13, @MethodConst15=未知, @MethodConst17=未知, @MethodConst18=0.000000, @MethodConst19=0.000000, @MethodConst21=未知, @MethodConst22=50.00, @MethodConst23=40, @MethodConst24=50000, @MethodConst25=FIXED, @MethodConst26=True, @MethodConst27=2025/9/4 13:57:13]
[2025-09-04 13:57:13.814 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `psf`.`id` AS `Id` , `psf`.`main_pet_no` AS `MainPetNo` , IFNULL(`mpc`.`name`,concat('宠物', CAST(`psf`.`main_pet_no` AS CHAR),'')) AS `MainPetName` , IFNULL(`mpc`.`attribute`,@MethodConst15) AS `MainPetAttribute` , `psf`.`sub_pet_no` AS `SubPetNo` , IFNULL(`spc`.`name`,concat('宠物', CAST(`psf`.`sub_pet_no` AS CHAR),'')) AS `SubPetName` , IFNULL(`spc`.`attribute`,@MethodConst17) AS `SubPetAttribute` , IFNULL(`psf`.`main_growth_min`,@MethodConst18) AS `MainGrowthMin` , IFNULL(`psf`.`sub_growth_min`,@MethodConst19) AS `SubGrowthMin` , `psf`.`result_pet_no` AS `ResultPetNo` , IFNULL(`rpc`.`name`,concat('宠物', CAST(`psf`.`result_pet_no` AS CHAR),'')) AS `ResultPetName` , IFNULL(`rpc`.`attribute`,@MethodConst21) AS `ResultPetAttribute` , IFNULL(`psf`.`base_success_rate`,@MethodConst22) AS `BaseSuccessRate` , IFNULL(`psf`.`required_level`,@MethodConst23) AS `RequiredLevel` , IFNULL(`psf`.`cost_gold`,@MethodConst24) AS `CostGold` , IFNULL(`psf`.`formula_type`,@MethodConst25) AS `FormulaType` , IFNULL(CAST(`psf`.`is_active` as SIGNED),@MethodConst26) AS `IsActive` , IFNULL(`psf`.`create_time`,@MethodConst27) AS `CreateTime`  FROM `pet_synthesis_formula` `psf` Left JOIN `pet_config` `mpc` ON ( `psf`.`main_pet_no` = `mpc`.`pet_no` )  Left JOIN `pet_config` `spc` ON ( `psf`.`sub_pet_no` = `spc`.`pet_no` )  Left JOIN `pet_config` `rpc` ON ( `psf`.`result_pet_no` = `rpc`.`pet_no` )     ORDER BY `psf`.`id` DESC LIMIT 0,10
[2025-09-04 13:57:32.558 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 13:57:32.559 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 13:57:32.560 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 13:57:32.609 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 13:57:32.611 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 13:57:32.675 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 13:57:32.676 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 13:57:32.677 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 13:57:32.677 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 13:57:32.678 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 13:57:32.678 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 13:57:32.679 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 13:57:32.680 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 13:57:32.681 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 13:57:32.682 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 13:57:32.684 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 13:57:32.686 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 13:57:32.731 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 13:57:32.732 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 13:57:32.905 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 13:57:32.906 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 13:57:32.908 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  CAST(`map_id` AS CHAR) AS `Value` , `map_name` AS `Label`  FROM `map_config`  
[2025-09-04 13:57:32.924 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 13:57:32.925 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 13:57:32.927 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  CAST(`item_no` AS CHAR) AS `Value` , `name` AS `Label`  FROM `item_config`  
[2025-09-04 13:57:32.941 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  CAST(`map_id` AS CHAR) AS `Value` , `map_name` AS `Label`  FROM `map_config`  
[2025-09-04 13:57:32.966 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 13:57:32.966 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 13:57:32.979 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM `drop_config` `dc` Left JOIN `map_config` `mc` ON ( `dc`.`map_id` = `mc`.`map_id` )  Left JOIN `map_monster` `mm` ON (( `dc`.`monster_id` = `mm`.`monster_id` ) AND ( `dc`.`map_id` = `mm`.`map_id` ))  Left JOIN `item_config` `ic` ON ( `dc`.`item_id` = CAST(`ic`.`item_no` AS CHAR))    | 参数: [@MethodConst0=, @MethodConst1=, @MethodConst2=1.0, @MethodConst3=1, @MethodConst4=1, @MethodConst5=2025/9/4 13:57:32, @MethodConst6=2025/9/4 13:57:32]
[2025-09-04 13:57:32.980 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  CAST(`item_no` AS CHAR) AS `Value` , `name` AS `Label`  FROM `item_config`  
[2025-09-04 13:57:33.028 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM `drop_config` `dc` Left JOIN `map_config` `mc` ON ( `dc`.`map_id` = `mc`.`map_id` )  Left JOIN `map_monster` `mm` ON (( `dc`.`monster_id` = `mm`.`monster_id` ) AND ( `dc`.`map_id` = `mm`.`map_id` ))  Left JOIN `item_config` `ic` ON ( `dc`.`item_id` = CAST(`ic`.`item_no` AS CHAR))   
[2025-09-04 13:57:33.030 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `dc`.`id` AS `Id` , `dc`.`map_id` AS `MapId` , IFNULL(`mc`.`map_name`,@MethodConst7) AS `MapName` , `dc`.`drop_type` AS `DropType` , `dc`.`monster_id` AS `MonsterId` , `mm`.`monster_name` AS `MonsterName` , `dc`.`item_id` AS `ItemId` , IFNULL(`ic`.`name`,@MethodConst8) AS `ItemName` , IFNULL(`dc`.`drop_rate`,@MethodConst9) AS `DropRate` , IFNULL(`dc`.`min_count`,@MethodConst10) AS `MinCount` , IFNULL(`dc`.`max_count`,@MethodConst11) AS `MaxCount` , `dc`.`remark` AS `Remark` , IFNULL(`dc`.`create_time`,@MethodConst12) AS `CreateTime` , IFNULL(`dc`.`update_time`,@MethodConst13) AS `UpdateTime` , `dc`.`drop_items_json` AS `DropItemsJson`  FROM `drop_config` `dc` Left JOIN `map_config` `mc` ON ( `dc`.`map_id` = `mc`.`map_id` )  Left JOIN `map_monster` `mm` ON (( `dc`.`monster_id` = `mm`.`monster_id` ) AND ( `dc`.`map_id` = `mm`.`map_id` ))  Left JOIN `item_config` `ic` ON ( `dc`.`item_id` = CAST(`ic`.`item_no` AS CHAR))     ORDER BY `dc`.`create_time` DESC LIMIT 0,10 | 参数: [@MethodConst0=, @MethodConst1=, @MethodConst2=1.0, @MethodConst3=1, @MethodConst4=1, @MethodConst5=2025/9/4 13:57:33, @MethodConst6=2025/9/4 13:57:33, @MethodConst7=, @MethodConst8=, @MethodConst9=1.0, @MethodConst10=1, @MethodConst11=1, @MethodConst12=2025/9/4 13:57:33, @MethodConst13=2025/9/4 13:57:33]
[2025-09-04 13:57:33.078 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `dc`.`id` AS `Id` , `dc`.`map_id` AS `MapId` , IFNULL(`mc`.`map_name`,@MethodConst7) AS `MapName` , `dc`.`drop_type` AS `DropType` , `dc`.`monster_id` AS `MonsterId` , `mm`.`monster_name` AS `MonsterName` , `dc`.`item_id` AS `ItemId` , IFNULL(`ic`.`name`,@MethodConst8) AS `ItemName` , IFNULL(`dc`.`drop_rate`,@MethodConst9) AS `DropRate` , IFNULL(`dc`.`min_count`,@MethodConst10) AS `MinCount` , IFNULL(`dc`.`max_count`,@MethodConst11) AS `MaxCount` , `dc`.`remark` AS `Remark` , IFNULL(`dc`.`create_time`,@MethodConst12) AS `CreateTime` , IFNULL(`dc`.`update_time`,@MethodConst13) AS `UpdateTime` , `dc`.`drop_items_json` AS `DropItemsJson`  FROM `drop_config` `dc` Left JOIN `map_config` `mc` ON ( `dc`.`map_id` = `mc`.`map_id` )  Left JOIN `map_monster` `mm` ON (( `dc`.`monster_id` = `mm`.`monster_id` ) AND ( `dc`.`map_id` = `mm`.`map_id` ))  Left JOIN `item_config` `ic` ON ( `dc`.`item_id` = CAST(`ic`.`item_no` AS CHAR))     ORDER BY `dc`.`create_time` DESC LIMIT 0,10
[2025-09-04 14:08:28.698 +08:00 INF] BMS: 🚀 应用程序启动，日志系统已配置
[2025-09-04 14:08:28.722 +08:00 DBG] BMS: 🔧 调试级别日志测试
[2025-09-04 14:08:28.726 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 14:08:28.728 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 14:08:28.775 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:08:29.314 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:08:29.322 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 14:08:29.323 +08:00 INF] BMS: ✅ DbContext 初始化成功，日志文件位置: logs/
[2025-09-04 14:11:30.786 +08:00 INF] BMS: 🚀 应用程序启动，日志系统已配置
[2025-09-04 14:11:30.468 +08:00 WRN] Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware: Failed to determine the https port for redirect.
[2025-09-04 14:11:31.494 +08:00 DBG] BMS: 🔧 调试级别日志测试
[2025-09-04 14:11:31.843 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 14:11:31.998 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 14:11:32.172 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:11:32.781 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:11:33.532 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 14:11:33.654 +08:00 INF] BMS: ✅ DbContext 初始化成功，日志文件位置: logs/
[2025-09-04 14:11:34.009 +08:00 ERR] Microsoft.Extensions.Hosting.Internal.Host: Hosting failed to start
System.IO.IOException: Failed to bind to address http://127.0.0.1:5078: address already in use.
 ---> Microsoft.AspNetCore.Connections.AddressInUseException: 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
 ---> System.Net.Sockets.SocketException (10048): 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
   at System.Net.Sockets.Socket.UpdateStatusAfterSocketErrorAndThrowException(SocketError error, Boolean disconnectOnFailure, String callerName)
   at System.Net.Sockets.Socket.DoBind(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Bind(EndPoint localEP)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportOptions.CreateDefaultBoundListenSocket(EndPoint endpoint)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportFactory.BindAsync(EndPoint endpoint, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Infrastructure.TransportManager.BindAsync(EndPoint endPoint, ConnectionDelegate connectionDelegate, EndpointConfig endpointConfig, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.<>c__DisplayClass28_0`1.<<StartAsync>g__OnBind|0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.LocalhostListenOptions.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.AddressesStrategy.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindAsync(ListenOptions[] listenOptions, AddressBindContext context, Func`2 useHttps, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.BindAsync(CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.StartAsync[TContext](IHttpApplication`1 application, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Hosting.GenericWebHostService.StartAsync(CancellationToken cancellationToken)
   at Microsoft.Extensions.Hosting.Internal.Host.<StartAsync>b__15_1(IHostedService service, CancellationToken token)
   at Microsoft.Extensions.Hosting.Internal.Host.ForeachService[T](IEnumerable`1 services, CancellationToken token, Boolean concurrent, Boolean abortOnFirstException, List`1 exceptions, Func`3 operation)
[2025-09-04 14:11:34.410 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 14:11:34.414 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 14:11:34.424 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:11:34.699 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:11:34.701 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 14:11:35.241 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 14:11:35.246 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 14:11:35.264 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 14:11:35.266 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 14:11:35.767 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 14:11:35.782 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 14:11:35.801 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 14:11:35.818 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 14:11:35.835 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:11:35.838 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:11:35.858 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:11:35.866 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:11:35.912 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:11:35.933 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 14:11:36.025 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  CAST(`item_no` AS CHAR) AS `Value` , `name` AS `Label`  FROM `item_config`  
[2025-09-04 14:11:36.080 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  CAST(`item_no` AS CHAR) AS `Value` , `name` AS `Label`  FROM `item_config`  
[2025-09-04 14:11:36.141 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:11:36.146 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 14:11:36.173 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:11:36.175 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 14:11:36.198 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:11:36.201 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 14:11:36.212 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  CAST(`map_id` AS CHAR) AS `Value` , `map_name` AS `Label`  FROM `map_config`  
[2025-09-04 14:11:36.265 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  CAST(`map_id` AS CHAR) AS `Value` , `map_name` AS `Label`  FROM `map_config`  
[2025-09-04 14:11:36.376 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM `drop_config` `dc` Left JOIN `map_config` `mc` ON ( `dc`.`map_id` = `mc`.`map_id` )  Left JOIN `map_monster` `mm` ON (( `dc`.`monster_id` = `mm`.`monster_id` ) AND ( `dc`.`map_id` = `mm`.`map_id` ))  Left JOIN `item_config` `ic` ON ( `dc`.`item_id` = CAST(`ic`.`item_no` AS CHAR))    | 参数: [@MethodConst0=, @MethodConst1=, @MethodConst2=1.0, @MethodConst3=1, @MethodConst4=1, @MethodConst5=2025/9/4 14:11:36, @MethodConst6=2025/9/4 14:11:36]
[2025-09-04 14:11:36.453 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM `drop_config` `dc` Left JOIN `map_config` `mc` ON ( `dc`.`map_id` = `mc`.`map_id` )  Left JOIN `map_monster` `mm` ON (( `dc`.`monster_id` = `mm`.`monster_id` ) AND ( `dc`.`map_id` = `mm`.`map_id` ))  Left JOIN `item_config` `ic` ON ( `dc`.`item_id` = CAST(`ic`.`item_no` AS CHAR))   
[2025-09-04 14:11:36.461 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `dc`.`id` AS `Id` , `dc`.`map_id` AS `MapId` , IFNULL(`mc`.`map_name`,@MethodConst7) AS `MapName` , `dc`.`drop_type` AS `DropType` , `dc`.`monster_id` AS `MonsterId` , `mm`.`monster_name` AS `MonsterName` , `dc`.`item_id` AS `ItemId` , IFNULL(`ic`.`name`,@MethodConst8) AS `ItemName` , IFNULL(`dc`.`drop_rate`,@MethodConst9) AS `DropRate` , IFNULL(`dc`.`min_count`,@MethodConst10) AS `MinCount` , IFNULL(`dc`.`max_count`,@MethodConst11) AS `MaxCount` , `dc`.`remark` AS `Remark` , IFNULL(`dc`.`create_time`,@MethodConst12) AS `CreateTime` , IFNULL(`dc`.`update_time`,@MethodConst13) AS `UpdateTime` , `dc`.`drop_items_json` AS `DropItemsJson`  FROM `drop_config` `dc` Left JOIN `map_config` `mc` ON ( `dc`.`map_id` = `mc`.`map_id` )  Left JOIN `map_monster` `mm` ON (( `dc`.`monster_id` = `mm`.`monster_id` ) AND ( `dc`.`map_id` = `mm`.`map_id` ))  Left JOIN `item_config` `ic` ON ( `dc`.`item_id` = CAST(`ic`.`item_no` AS CHAR))     ORDER BY `dc`.`create_time` DESC LIMIT 0,10 | 参数: [@MethodConst0=, @MethodConst1=, @MethodConst2=1.0, @MethodConst3=1, @MethodConst4=1, @MethodConst5=2025/9/4 14:11:36, @MethodConst6=2025/9/4 14:11:36, @MethodConst7=, @MethodConst8=, @MethodConst9=1.0, @MethodConst10=1, @MethodConst11=1, @MethodConst12=2025/9/4 14:11:36, @MethodConst13=2025/9/4 14:11:36]
[2025-09-04 14:11:36.514 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `dc`.`id` AS `Id` , `dc`.`map_id` AS `MapId` , IFNULL(`mc`.`map_name`,@MethodConst7) AS `MapName` , `dc`.`drop_type` AS `DropType` , `dc`.`monster_id` AS `MonsterId` , `mm`.`monster_name` AS `MonsterName` , `dc`.`item_id` AS `ItemId` , IFNULL(`ic`.`name`,@MethodConst8) AS `ItemName` , IFNULL(`dc`.`drop_rate`,@MethodConst9) AS `DropRate` , IFNULL(`dc`.`min_count`,@MethodConst10) AS `MinCount` , IFNULL(`dc`.`max_count`,@MethodConst11) AS `MaxCount` , `dc`.`remark` AS `Remark` , IFNULL(`dc`.`create_time`,@MethodConst12) AS `CreateTime` , IFNULL(`dc`.`update_time`,@MethodConst13) AS `UpdateTime` , `dc`.`drop_items_json` AS `DropItemsJson`  FROM `drop_config` `dc` Left JOIN `map_config` `mc` ON ( `dc`.`map_id` = `mc`.`map_id` )  Left JOIN `map_monster` `mm` ON (( `dc`.`monster_id` = `mm`.`monster_id` ) AND ( `dc`.`map_id` = `mm`.`map_id` ))  Left JOIN `item_config` `ic` ON ( `dc`.`item_id` = CAST(`ic`.`item_no` AS CHAR))     ORDER BY `dc`.`create_time` DESC LIMIT 0,10
[2025-09-04 14:11:40.436 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 14:11:40.536 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 14:11:40.551 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:11:40.607 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:11:40.608 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 14:11:40.632 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  CAST(`monster_id` AS CHAR) AS `Value` , `monster_name` AS `Label`  FROM `map_monster`  WHERE ( `map_id` = @map_id0 )  | 参数: [@map_id0=100]
[2025-09-04 14:11:40.688 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  CAST(`monster_id` AS CHAR) AS `Value` , `monster_name` AS `Label`  FROM `map_monster`  WHERE ( `map_id` = @map_id0 ) 
[2025-09-04 14:12:22.333 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 14:12:22.483 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 14:12:22.491 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:12:22.562 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:12:22.564 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 14:12:22.644 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 14:12:22.647 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 14:12:22.649 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:12:22.699 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:12:22.700 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 14:12:22.717 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 14:12:22.718 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 14:12:22.719 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:12:22.762 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:12:22.766 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 14:12:22.772 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `task_id` AS `Value` , `task_name` AS `Label`  FROM `task_config`  WHERE ( `is_active` = @is_active0 )ORDER BY `sort_order` ASC  | 参数: [@is_active0=1]
[2025-09-04 14:12:22.814 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `task_id` AS `Value` , `task_name` AS `Label`  FROM `task_config`  WHERE ( `is_active` = @is_active0 )ORDER BY `sort_order` ASC 
[2025-09-04 14:12:22.825 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 14:12:22.827 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 14:12:22.830 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:12:22.880 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:12:22.882 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 14:12:22.903 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM `task_config`  
[2025-09-04 14:12:22.947 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM `task_config`  
[2025-09-04 14:12:22.950 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`    ORDER BY `sort_order` ASC,`created_at` DESC LIMIT 0,10
[2025-09-04 14:12:23.004 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`    ORDER BY `sort_order` ASC,`created_at` DESC LIMIT 0,10
[2025-09-04 14:12:23.023 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_001]
[2025-09-04 14:12:23.068 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-09-04 14:12:23.071 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_002]
[2025-09-04 14:12:23.118 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-09-04 14:12:23.123 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_003]
[2025-09-04 14:12:23.183 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-09-04 14:12:23.186 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_004]
[2025-09-04 14:12:23.266 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-09-04 14:12:23.286 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_005]
[2025-09-04 14:12:23.381 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-09-04 14:12:23.396 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_010]
[2025-09-04 14:12:23.452 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-09-04 14:13:48.592 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 14:13:48.658 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 14:13:48.663 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:13:48.710 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:13:48.714 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 14:13:48.765 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 14:13:48.771 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 14:13:48.772 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:13:48.824 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:13:48.826 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 14:13:48.831 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 14:13:48.833 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 14:13:48.834 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:13:48.878 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:13:48.879 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 14:13:48.881 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `task_id` AS `Value` , `task_name` AS `Label`  FROM `task_config`  WHERE ( `is_active` = @is_active0 )ORDER BY `sort_order` ASC  | 参数: [@is_active0=1]
[2025-09-04 14:13:48.929 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `task_id` AS `Value` , `task_name` AS `Label`  FROM `task_config`  WHERE ( `is_active` = @is_active0 )ORDER BY `sort_order` ASC 
[2025-09-04 14:13:48.935 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 14:13:48.936 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 14:13:48.938 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:13:48.984 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:13:48.985 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 14:13:48.988 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM `task_config`  
[2025-09-04 14:13:49.032 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM `task_config`  
[2025-09-04 14:13:49.039 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`    ORDER BY `sort_order` ASC,`created_at` DESC LIMIT 0,10
[2025-09-04 14:13:49.093 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`    ORDER BY `sort_order` ASC,`created_at` DESC LIMIT 0,10
[2025-09-04 14:13:49.096 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_001]
[2025-09-04 14:13:49.139 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-09-04 14:13:49.143 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_002]
[2025-09-04 14:13:49.189 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-09-04 14:13:49.193 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_003]
[2025-09-04 14:13:49.239 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-09-04 14:13:49.243 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_004]
[2025-09-04 14:13:49.342 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-09-04 14:13:49.346 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_005]
[2025-09-04 14:13:49.409 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-09-04 14:13:49.433 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_010]
[2025-09-04 14:13:49.499 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-09-04 14:23:10.148 +08:00 INF] BMS: 🚀 应用程序启动，日志系统已配置
[2025-09-04 14:23:10.171 +08:00 DBG] BMS: 🔧 调试级别日志测试
[2025-09-04 14:23:10.175 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 14:23:10.177 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 14:23:10.221 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:23:10.731 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:23:10.738 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 14:23:10.739 +08:00 INF] BMS: ✅ DbContext 初始化成功，日志文件位置: logs/
[2025-09-04 14:25:17.261 +08:00 INF] BMS: 🚀 应用程序启动，日志系统已配置
[2025-09-04 14:25:17.285 +08:00 DBG] BMS: 🔧 调试级别日志测试
[2025-09-04 14:25:17.289 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 14:25:17.291 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 14:25:17.341 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:25:17.852 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:25:17.860 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 14:25:17.861 +08:00 INF] BMS: ✅ DbContext 初始化成功，日志文件位置: logs/
[2025-09-04 14:25:58.685 +08:00 WRN] Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware: Failed to determine the https port for redirect.
[2025-09-04 14:27:09.144 +08:00 INF] BMS: 🚀 应用程序启动，日志系统已配置
[2025-09-04 14:27:09.201 +08:00 DBG] BMS: 🔧 调试级别日志测试
[2025-09-04 14:27:09.233 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 14:27:09.246 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 14:27:09.373 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:27:10.058 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:27:10.271 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 14:27:10.332 +08:00 INF] BMS: ✅ DbContext 初始化成功，日志文件位置: logs/
[2025-09-04 14:27:10.640 +08:00 ERR] Microsoft.Extensions.Hosting.Internal.Host: Hosting failed to start
System.IO.IOException: Failed to bind to address http://127.0.0.1:5078: address already in use.
 ---> Microsoft.AspNetCore.Connections.AddressInUseException: 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
 ---> System.Net.Sockets.SocketException (10048): 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
   at System.Net.Sockets.Socket.UpdateStatusAfterSocketErrorAndThrowException(SocketError error, Boolean disconnectOnFailure, String callerName)
   at System.Net.Sockets.Socket.DoBind(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Bind(EndPoint localEP)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportOptions.CreateDefaultBoundListenSocket(EndPoint endpoint)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportFactory.BindAsync(EndPoint endpoint, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Infrastructure.TransportManager.BindAsync(EndPoint endPoint, ConnectionDelegate connectionDelegate, EndpointConfig endpointConfig, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.<>c__DisplayClass28_0`1.<<StartAsync>g__OnBind|0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.LocalhostListenOptions.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.AddressesStrategy.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindAsync(ListenOptions[] listenOptions, AddressBindContext context, Func`2 useHttps, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.BindAsync(CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.StartAsync[TContext](IHttpApplication`1 application, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Hosting.GenericWebHostService.StartAsync(CancellationToken cancellationToken)
   at Microsoft.Extensions.Hosting.Internal.Host.<StartAsync>b__15_1(IHostedService service, CancellationToken token)
   at Microsoft.Extensions.Hosting.Internal.Host.ForeachService[T](IEnumerable`1 services, CancellationToken token, Boolean concurrent, Boolean abortOnFirstException, List`1 exceptions, Func`3 operation)
[2025-09-04 14:27:37.249 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 14:27:37.257 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 14:27:37.267 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:27:37.569 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:27:37.572 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 14:27:37.674 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 14:27:37.675 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 14:27:37.679 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:27:37.734 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:27:37.735 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 14:27:37.773 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 14:27:37.775 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 14:27:37.777 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:27:37.816 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:27:37.819 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 14:27:37.875 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `task_id` AS `Value` , `task_name` AS `Label`  FROM `task_config`  WHERE ( `is_active` = @is_active0 )ORDER BY `sort_order` ASC  | 参数: [@is_active0=1]
[2025-09-04 14:27:37.939 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `task_id` AS `Value` , `task_name` AS `Label`  FROM `task_config`  WHERE ( `is_active` = @is_active0 )ORDER BY `sort_order` ASC 
[2025-09-04 14:27:38.021 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 14:27:38.024 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 14:27:38.026 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:27:38.075 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:27:38.080 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 14:27:38.755 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM `task_config`  
[2025-09-04 14:27:39.007 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM `task_config`  
[2025-09-04 14:27:39.013 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`    ORDER BY `sort_order` ASC,`created_at` DESC LIMIT 0,10
[2025-09-04 14:27:39.056 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`    ORDER BY `sort_order` ASC,`created_at` DESC LIMIT 0,10
[2025-09-04 14:27:39.075 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_001]
[2025-09-04 14:27:39.120 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-09-04 14:27:39.125 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_002]
[2025-09-04 14:27:39.167 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-09-04 14:27:39.171 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_003]
[2025-09-04 14:27:39.216 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-09-04 14:27:39.219 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_004]
[2025-09-04 14:27:39.268 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-09-04 14:27:39.271 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_005]
[2025-09-04 14:27:39.311 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-09-04 14:27:39.315 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_010]
[2025-09-04 14:27:39.354 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-09-04 14:28:11.502 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 14:28:11.909 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 14:28:11.915 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:28:11.974 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:28:11.976 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 14:28:12.099 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 14:28:12.102 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 14:28:12.104 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:28:12.143 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:28:12.145 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 14:28:12.238 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `user`  
[2025-09-04 14:28:12.279 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `user`  
[2025-09-04 14:28:12.307 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `user`  WHERE ( `create_time` >= @create_time0 )  | 参数: [@create_time0=2025/9/4 0:00:00]
[2025-09-04 14:28:12.351 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `user`  WHERE ( `create_time` >= @create_time0 ) 
[2025-09-04 14:28:12.355 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `user`  WHERE ( `vip_level` > @vip_level0 )  | 参数: [@vip_level0=0]
[2025-09-04 14:28:12.395 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `user`  WHERE ( `vip_level` > @vip_level0 ) 
[2025-09-04 14:28:12.432 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT SUM(IFNULL(`gold`,@MethodConst0)) FROM `user`   | 参数: [@MethodConst0=0]
[2025-09-04 14:28:12.478 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT SUM(IFNULL(`gold`,@MethodConst0)) FROM `user`  
[2025-09-04 14:28:12.510 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 14:28:12.513 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 14:28:12.518 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:28:12.559 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:28:12.560 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 14:28:12.571 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `user`  
[2025-09-04 14:28:12.610 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `user`  
[2025-09-04 14:28:12.642 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`username`,`password`,`nickname`,`sex`,`vip_level`,`vip_score`,`gold`,`yuanbao`,`crystal`,`reg_time`,`title`,`main_pet_id`,`main_pet_name`,`create_time`,`evolution_cd`,`synthesis_cd`,`nirvana_cd`,`money`,`supreme_vip`,`prop_capacity`,`pasture_capacity`,`dragon_ball_exp`,`dragon_ball_level`,`status`,`prestige`,`auto_battle_count`,`auto_nirvana_count`,`monster_count`,`next_battle`,`open_maps`,`star_vip`,`star_vip_expire`,`task_helper_enabled`,`title_attributes`,`soul_pet`,`hell_floor`,`tower_floor` FROM `user`    ORDER BY `create_time` DESC LIMIT 0,15
[2025-09-04 14:28:12.688 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`username`,`password`,`nickname`,`sex`,`vip_level`,`vip_score`,`gold`,`yuanbao`,`crystal`,`reg_time`,`title`,`main_pet_id`,`main_pet_name`,`create_time`,`evolution_cd`,`synthesis_cd`,`nirvana_cd`,`money`,`supreme_vip`,`prop_capacity`,`pasture_capacity`,`dragon_ball_exp`,`dragon_ball_level`,`status`,`prestige`,`auto_battle_count`,`auto_nirvana_count`,`monster_count`,`next_battle`,`open_maps`,`star_vip`,`star_vip_expire`,`task_helper_enabled`,`title_attributes`,`soul_pet`,`hell_floor`,`tower_floor` FROM `user`    ORDER BY `create_time` DESC LIMIT 0,15
[2025-09-04 14:28:19.791 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 14:28:19.792 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 14:28:19.793 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:28:19.836 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:28:19.838 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 14:28:19.891 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 14:28:19.892 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 14:28:19.894 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:28:19.937 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:28:19.939 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 14:28:19.945 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 14:28:19.946 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 14:28:19.948 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:28:19.988 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:28:19.990 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 14:28:19.992 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `task_id` AS `Value` , `task_name` AS `Label`  FROM `task_config`  WHERE ( `is_active` = @is_active0 )ORDER BY `sort_order` ASC  | 参数: [@is_active0=1]
[2025-09-04 14:28:20.029 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `task_id` AS `Value` , `task_name` AS `Label`  FROM `task_config`  WHERE ( `is_active` = @is_active0 )ORDER BY `sort_order` ASC 
[2025-09-04 14:28:20.036 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 14:28:20.038 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 14:28:20.040 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:28:20.078 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:28:20.079 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 14:28:20.082 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM `task_config`  
[2025-09-04 14:28:20.122 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM `task_config`  
[2025-09-04 14:28:20.124 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`    ORDER BY `sort_order` ASC,`created_at` DESC LIMIT 0,10
[2025-09-04 14:28:20.177 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`    ORDER BY `sort_order` ASC,`created_at` DESC LIMIT 0,10
[2025-09-04 14:28:20.184 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_001]
[2025-09-04 14:28:20.225 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-09-04 14:28:20.229 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_002]
[2025-09-04 14:28:20.273 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-09-04 14:28:20.276 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_003]
[2025-09-04 14:28:20.317 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-09-04 14:28:20.323 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_004]
[2025-09-04 14:28:20.384 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-09-04 14:28:20.422 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_005]
[2025-09-04 14:28:20.513 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-09-04 14:28:20.691 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_010]
[2025-09-04 14:28:21.079 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-09-04 14:28:23.386 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 14:28:23.388 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 14:28:23.390 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:28:23.438 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:28:23.451 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 14:28:23.530 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 14:28:23.539 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 14:28:23.540 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:28:23.577 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:28:23.587 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 14:28:23.597 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `monster_config`  
[2025-09-04 14:28:23.638 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `monster_config`  
[2025-09-04 14:28:23.642 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`monster_no`,`skill`,`name`,`attribute` FROM `monster_config`    ORDER BY `monster_no` ASC LIMIT 0,10
[2025-09-04 14:28:23.684 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`monster_no`,`skill`,`name`,`attribute` FROM `monster_config`    ORDER BY `monster_no` ASC LIMIT 0,10
[2025-09-04 14:28:28.219 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 14:28:28.221 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 14:28:28.223 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:28:28.262 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:28:28.263 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 14:28:28.319 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 14:28:28.320 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 14:28:28.321 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:28:28.360 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:28:28.362 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 14:28:28.367 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `user`  
[2025-09-04 14:28:28.407 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `user`  
[2025-09-04 14:28:28.409 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`username`,`password`,`nickname`,`sex`,`vip_level`,`vip_score`,`gold`,`yuanbao`,`crystal`,`reg_time`,`title`,`main_pet_id`,`main_pet_name`,`create_time`,`evolution_cd`,`synthesis_cd`,`nirvana_cd`,`money`,`supreme_vip`,`prop_capacity`,`pasture_capacity`,`dragon_ball_exp`,`dragon_ball_level`,`status`,`prestige`,`auto_battle_count`,`auto_nirvana_count`,`monster_count`,`next_battle`,`open_maps`,`star_vip`,`star_vip_expire`,`task_helper_enabled`,`title_attributes`,`soul_pet`,`hell_floor`,`tower_floor` FROM `user`    ORDER BY `create_time` DESC LIMIT 0,1000
[2025-09-04 14:28:28.453 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`username`,`password`,`nickname`,`sex`,`vip_level`,`vip_score`,`gold`,`yuanbao`,`crystal`,`reg_time`,`title`,`main_pet_id`,`main_pet_name`,`create_time`,`evolution_cd`,`synthesis_cd`,`nirvana_cd`,`money`,`supreme_vip`,`prop_capacity`,`pasture_capacity`,`dragon_ball_exp`,`dragon_ball_level`,`status`,`prestige`,`auto_battle_count`,`auto_nirvana_count`,`monster_count`,`next_battle`,`open_maps`,`star_vip`,`star_vip_expire`,`task_helper_enabled`,`title_attributes`,`soul_pet`,`hell_floor`,`tower_floor` FROM `user`    ORDER BY `create_time` DESC LIMIT 0,1000
[2025-09-04 14:28:28.473 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 14:28:28.474 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 14:28:28.475 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:28:28.513 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:28:28.518 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 14:28:28.523 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `id` AS `Id` , `item_no` AS `ItemNo` , `name` AS `Name` , `type` AS `Type` , `description` AS `Description` , `quality` AS `Quality` , `icon` AS `Icon` , `price` AS `Price` , `use_limit` AS `UseLimit` , `create_time` AS `CreateTime`  FROM `item_config` ORDER BY `item_no` ASC 
[2025-09-04 14:28:28.565 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `id` AS `Id` , `item_no` AS `ItemNo` , `name` AS `Name` , `type` AS `Type` , `description` AS `Description` , `quality` AS `Quality` , `icon` AS `Icon` , `price` AS `Price` , `use_limit` AS `UseLimit` , `create_time` AS `CreateTime`  FROM `item_config` ORDER BY `item_no` ASC 
[2025-09-04 14:28:28.623 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 14:28:28.627 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 14:28:28.628 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:28:28.666 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:28:28.667 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 14:28:28.671 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT SUM(`item_count`) FROM `user_item`  
[2025-09-04 14:28:28.710 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT SUM(`item_count`) FROM `user_item`  
[2025-09-04 14:28:28.718 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `user_item` GROUP BY `item_id`  
[2025-09-04 14:28:28.761 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `user_item` GROUP BY `item_id`  
[2025-09-04 14:28:28.802 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ic`.`quality` AS `Quality` , SUM(`ui`.`item_count`) AS `Count`  FROM `user_item` `ui` Left JOIN `item_config` `ic` ON ( `ui`.`item_id` = CAST(`ic`.`item_no` AS CHAR))  GROUP BY `ic`.`quality`  
[2025-09-04 14:28:28.847 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ic`.`quality` AS `Quality` , SUM(`ui`.`item_count`) AS `Count`  FROM `user_item` `ui` Left JOIN `item_config` `ic` ON ( `ui`.`item_id` = CAST(`ic`.`item_no` AS CHAR))  GROUP BY `ic`.`quality`  
[2025-09-04 14:28:28.879 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT SUM(( `ui`.`item_count` *IFNULL(`ic`.`price`,@MethodConst0))) FROM `user_item` `ui` Left JOIN `item_config` `ic` ON ( `ui`.`item_id` = CAST(`ic`.`item_no` AS CHAR))    | 参数: [@MethodConst0=0]
[2025-09-04 14:28:28.929 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT SUM(( `ui`.`item_count` *IFNULL(`ic`.`price`,@MethodConst0))) FROM `user_item` `ui` Left JOIN `item_config` `ic` ON ( `ui`.`item_id` = CAST(`ic`.`item_no` AS CHAR))   
[2025-09-04 14:28:28.943 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 14:28:28.947 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 14:28:28.954 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:28:28.990 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:28:28.991 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 14:28:29.009 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `user_item` `ui` Left JOIN `user` `u` ON ( `ui`.`user_id` = `u`.`id` )  Left JOIN `item_config` `ic` ON ( `ui`.`item_id` = CAST(`ic`.`item_no` AS CHAR))   
[2025-09-04 14:28:29.053 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `user_item` `ui` Left JOIN `user` `u` ON ( `ui`.`user_id` = `u`.`id` )  Left JOIN `item_config` `ic` ON ( `ui`.`item_id` = CAST(`ic`.`item_no` AS CHAR))   
[2025-09-04 14:28:29.062 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ui`.`id` AS `Id` , `ui`.`user_id` AS `UserId` , IFNULL(`u`.`username`,@MethodConst2) AS `Username` , `ui`.`item_id` AS `ItemId` , CAST(`ui`.`item_id` AS SIGNED) AS `ItemNo` , IFNULL(`ic`.`name`,@MethodConst3) AS `ItemName` , `ic`.`type` AS `ItemType` , `ic`.`quality` AS `ItemQuality` , `ui`.`item_count` AS `ItemCount` , `ui`.`item_pos` AS `ItemPos` , `ui`.`item_seq` AS `ItemSeq` , `ic`.`description` AS `ItemDescription` , `ic`.`price` AS `ItemPrice` , `ic`.`icon` AS `ItemIcon`  FROM `user_item` `ui` Left JOIN `user` `u` ON ( `ui`.`user_id` = `u`.`id` )  Left JOIN `item_config` `ic` ON ( `ui`.`item_id` = CAST(`ic`.`item_no` AS CHAR))     ORDER BY `ui`.`id` DESC LIMIT 0,15 | 参数: [@MethodConst0=, @MethodConst1=, @MethodConst2=, @MethodConst3=]
[2025-09-04 14:28:29.113 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ui`.`id` AS `Id` , `ui`.`user_id` AS `UserId` , IFNULL(`u`.`username`,@MethodConst2) AS `Username` , `ui`.`item_id` AS `ItemId` , CAST(`ui`.`item_id` AS SIGNED) AS `ItemNo` , IFNULL(`ic`.`name`,@MethodConst3) AS `ItemName` , `ic`.`type` AS `ItemType` , `ic`.`quality` AS `ItemQuality` , `ui`.`item_count` AS `ItemCount` , `ui`.`item_pos` AS `ItemPos` , `ui`.`item_seq` AS `ItemSeq` , `ic`.`description` AS `ItemDescription` , `ic`.`price` AS `ItemPrice` , `ic`.`icon` AS `ItemIcon`  FROM `user_item` `ui` Left JOIN `user` `u` ON ( `ui`.`user_id` = `u`.`id` )  Left JOIN `item_config` `ic` ON ( `ui`.`item_id` = CAST(`ic`.`item_no` AS CHAR))     ORDER BY `ui`.`id` DESC LIMIT 0,15
[2025-09-04 14:29:15.065 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 14:29:15.211 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 14:29:15.215 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:29:15.257 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:29:15.258 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 14:29:15.268 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `admin_bm`  
[2025-09-04 14:29:15.313 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `admin_bm`  
[2025-09-04 14:29:15.315 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`username`,`password`,`real_name`,`phone`,`email`,`status`,`create_time`,`update_time`,`last_login_time` FROM `admin_bm`    ORDER BY `create_time` DESC LIMIT 0,20
[2025-09-04 14:29:15.358 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`username`,`password`,`real_name`,`phone`,`email`,`status`,`create_time`,`update_time`,`last_login_time` FROM `admin_bm`    ORDER BY `create_time` DESC LIMIT 0,20
[2025-09-04 14:29:15.462 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 14:29:15.463 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 14:29:15.464 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:29:15.501 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:29:15.506 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 14:29:15.514 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `admin_bm`  
[2025-09-04 14:29:15.552 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `admin_bm`  
[2025-09-04 14:29:15.558 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`username`,`password`,`real_name`,`phone`,`email`,`status`,`create_time`,`update_time`,`last_login_time` FROM `admin_bm`    ORDER BY `create_time` DESC LIMIT 0,10
[2025-09-04 14:29:15.598 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`username`,`password`,`real_name`,`phone`,`email`,`status`,`create_time`,`update_time`,`last_login_time` FROM `admin_bm`    ORDER BY `create_time` DESC LIMIT 0,10
[2025-09-04 14:29:21.112 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 14:29:21.130 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 14:29:21.140 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:29:21.183 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:29:21.187 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 14:29:21.244 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 14:29:21.245 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 14:29:21.246 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:29:21.285 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:29:21.289 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 14:29:21.294 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `user`  
[2025-09-04 14:29:21.378 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `user`  
[2025-09-04 14:29:21.412 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `user`  WHERE ( `create_time` >= @create_time0 )  | 参数: [@create_time0=2025/9/4 0:00:00]
[2025-09-04 14:29:21.460 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `user`  WHERE ( `create_time` >= @create_time0 ) 
[2025-09-04 14:29:21.596 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `user`  WHERE ( `vip_level` > @vip_level0 )  | 参数: [@vip_level0=0]
[2025-09-04 14:29:21.651 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `user`  WHERE ( `vip_level` > @vip_level0 ) 
[2025-09-04 14:29:21.680 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT SUM(IFNULL(`gold`,@MethodConst0)) FROM `user`   | 参数: [@MethodConst0=0]
[2025-09-04 14:29:21.780 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT SUM(IFNULL(`gold`,@MethodConst0)) FROM `user`  
[2025-09-04 14:29:21.836 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 14:29:21.838 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 14:29:21.853 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:29:22.017 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:29:22.019 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 14:29:22.021 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `user`  
[2025-09-04 14:29:22.058 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `user`  
[2025-09-04 14:29:22.059 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`username`,`password`,`nickname`,`sex`,`vip_level`,`vip_score`,`gold`,`yuanbao`,`crystal`,`reg_time`,`title`,`main_pet_id`,`main_pet_name`,`create_time`,`evolution_cd`,`synthesis_cd`,`nirvana_cd`,`money`,`supreme_vip`,`prop_capacity`,`pasture_capacity`,`dragon_ball_exp`,`dragon_ball_level`,`status`,`prestige`,`auto_battle_count`,`auto_nirvana_count`,`monster_count`,`next_battle`,`open_maps`,`star_vip`,`star_vip_expire`,`task_helper_enabled`,`title_attributes`,`soul_pet`,`hell_floor`,`tower_floor` FROM `user`    ORDER BY `create_time` DESC LIMIT 0,15
[2025-09-04 14:29:22.097 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`username`,`password`,`nickname`,`sex`,`vip_level`,`vip_score`,`gold`,`yuanbao`,`crystal`,`reg_time`,`title`,`main_pet_id`,`main_pet_name`,`create_time`,`evolution_cd`,`synthesis_cd`,`nirvana_cd`,`money`,`supreme_vip`,`prop_capacity`,`pasture_capacity`,`dragon_ball_exp`,`dragon_ball_level`,`status`,`prestige`,`auto_battle_count`,`auto_nirvana_count`,`monster_count`,`next_battle`,`open_maps`,`star_vip`,`star_vip_expire`,`task_helper_enabled`,`title_attributes`,`soul_pet`,`hell_floor`,`tower_floor` FROM `user`    ORDER BY `create_time` DESC LIMIT 0,15
[2025-09-04 14:29:34.861 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 14:29:34.863 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 14:29:34.865 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:29:34.905 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:29:34.907 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 14:29:34.972 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 14:29:34.973 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 14:29:34.974 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:29:35.014 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:29:35.015 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 14:29:35.019 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `user`  
[2025-09-04 14:29:35.056 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `user`  
[2025-09-04 14:29:35.058 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`username`,`password`,`nickname`,`sex`,`vip_level`,`vip_score`,`gold`,`yuanbao`,`crystal`,`reg_time`,`title`,`main_pet_id`,`main_pet_name`,`create_time`,`evolution_cd`,`synthesis_cd`,`nirvana_cd`,`money`,`supreme_vip`,`prop_capacity`,`pasture_capacity`,`dragon_ball_exp`,`dragon_ball_level`,`status`,`prestige`,`auto_battle_count`,`auto_nirvana_count`,`monster_count`,`next_battle`,`open_maps`,`star_vip`,`star_vip_expire`,`task_helper_enabled`,`title_attributes`,`soul_pet`,`hell_floor`,`tower_floor` FROM `user`    ORDER BY `create_time` DESC LIMIT 0,1000
[2025-09-04 14:29:35.101 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`username`,`password`,`nickname`,`sex`,`vip_level`,`vip_score`,`gold`,`yuanbao`,`crystal`,`reg_time`,`title`,`main_pet_id`,`main_pet_name`,`create_time`,`evolution_cd`,`synthesis_cd`,`nirvana_cd`,`money`,`supreme_vip`,`prop_capacity`,`pasture_capacity`,`dragon_ball_exp`,`dragon_ball_level`,`status`,`prestige`,`auto_battle_count`,`auto_nirvana_count`,`monster_count`,`next_battle`,`open_maps`,`star_vip`,`star_vip_expire`,`task_helper_enabled`,`title_attributes`,`soul_pet`,`hell_floor`,`tower_floor` FROM `user`    ORDER BY `create_time` DESC LIMIT 0,1000
[2025-09-04 14:29:35.114 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 14:29:35.117 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 14:29:35.223 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:29:35.422 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:29:35.429 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 14:29:35.434 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `id` AS `Id` , `pet_no` AS `PetNo` , `name` AS `Name` , `attribute` AS `Attribute` , `skill` AS `Skill`  FROM `pet_config` ORDER BY `pet_no` ASC 
[2025-09-04 14:29:35.480 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `id` AS `Id` , `pet_no` AS `PetNo` , `name` AS `Name` , `attribute` AS `Attribute` , `skill` AS `Skill`  FROM `pet_config` ORDER BY `pet_no` ASC 
[2025-09-04 14:29:35.506 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 14:29:35.509 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 14:29:35.516 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:29:35.554 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:29:35.555 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 14:29:35.573 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )   
[2025-09-04 14:29:35.616 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )   
[2025-09-04 14:29:35.621 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )    | 参数: [@MethodConst0=, @MethodConst1=, @MethodConst2=, @MethodConst3=]
[2025-09-04 14:29:35.666 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )   
[2025-09-04 14:29:35.669 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `up`.`id` AS `Id` , `up`.`user_id` AS `UserId` , IFNULL(`u`.`username`,@MethodConst4) AS `UserName` , `up`.`pet_no` AS `PetNo` , IFNULL(`pc`.`name`,@MethodConst5) AS `PetName` , IFNULL(`pc`.`attribute`,@MethodConst6) AS `PetAttribute` , `up`.`image` AS `Image` , `up`.`exp` AS `Exp` , `up`.`hp` AS `Hp` , `up`.`mp` AS `Mp` , `up`.`atk` AS `Atk` , `up`.`def` AS `Def` , `up`.`spd` AS `Spd` , `up`.`state` AS `State` , `up`.`dodge` AS `Dodge` , `up`.`growth` AS `Growth` , `up`.`hit` AS `Hit` , `up`.`realm` AS `Realm` , `up`.`evolve_count` AS `EvolveCount` , `up`.`element` AS `Element` , `up`.`create_time` AS `CreateTime` , IFNULL(`up`.`status`,@MethodConst7) AS `Status` , `up`.`is_main` AS `IsMain`  FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )     ORDER BY `up`.`create_time` DESC LIMIT 0,10 | 参数: [@MethodConst0=, @MethodConst1=, @MethodConst2=, @MethodConst3=, @MethodConst4=, @MethodConst5=, @MethodConst6=, @MethodConst7=]
[2025-09-04 14:29:35.723 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `up`.`id` AS `Id` , `up`.`user_id` AS `UserId` , IFNULL(`u`.`username`,@MethodConst4) AS `UserName` , `up`.`pet_no` AS `PetNo` , IFNULL(`pc`.`name`,@MethodConst5) AS `PetName` , IFNULL(`pc`.`attribute`,@MethodConst6) AS `PetAttribute` , `up`.`image` AS `Image` , `up`.`exp` AS `Exp` , `up`.`hp` AS `Hp` , `up`.`mp` AS `Mp` , `up`.`atk` AS `Atk` , `up`.`def` AS `Def` , `up`.`spd` AS `Spd` , `up`.`state` AS `State` , `up`.`dodge` AS `Dodge` , `up`.`growth` AS `Growth` , `up`.`hit` AS `Hit` , `up`.`realm` AS `Realm` , `up`.`evolve_count` AS `EvolveCount` , `up`.`element` AS `Element` , `up`.`create_time` AS `CreateTime` , IFNULL(`up`.`status`,@MethodConst7) AS `Status` , `up`.`is_main` AS `IsMain`  FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )     ORDER BY `up`.`create_time` DESC LIMIT 0,10
[2025-09-04 14:29:35.732 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=22, @MethodConst1=]
[2025-09-04 14:29:35.785 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-04 14:29:35.789 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=13, @MethodConst1=]
[2025-09-04 14:29:35.843 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-04 14:29:35.851 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=12, @MethodConst1=]
[2025-09-04 14:29:35.919 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-04 14:29:35.991 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=11, @MethodConst1=]
[2025-09-04 14:29:36.149 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-04 14:29:36.154 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=15, @MethodConst1=]
[2025-09-04 14:29:36.203 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-04 14:29:36.209 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=17, @MethodConst1=]
[2025-09-04 14:29:36.258 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-04 14:29:36.261 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=19, @MethodConst1=]
[2025-09-04 14:29:36.314 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-04 14:29:36.387 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=21, @MethodConst1=]
[2025-09-04 14:29:36.438 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-04 14:29:36.440 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=18, @MethodConst1=]
[2025-09-04 14:29:36.483 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-04 14:29:36.485 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=20, @MethodConst1=]
[2025-09-04 14:29:36.524 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-04 14:29:43.688 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 14:29:43.701 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 14:29:43.808 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:29:43.892 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:29:44.036 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 14:29:44.091 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 14:29:44.207 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 14:29:44.220 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:29:44.268 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:29:44.282 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 14:29:44.288 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `user`  
[2025-09-04 14:29:44.390 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `user`  
[2025-09-04 14:29:44.423 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`username`,`password`,`nickname`,`sex`,`vip_level`,`vip_score`,`gold`,`yuanbao`,`crystal`,`reg_time`,`title`,`main_pet_id`,`main_pet_name`,`create_time`,`evolution_cd`,`synthesis_cd`,`nirvana_cd`,`money`,`supreme_vip`,`prop_capacity`,`pasture_capacity`,`dragon_ball_exp`,`dragon_ball_level`,`status`,`prestige`,`auto_battle_count`,`auto_nirvana_count`,`monster_count`,`next_battle`,`open_maps`,`star_vip`,`star_vip_expire`,`task_helper_enabled`,`title_attributes`,`soul_pet`,`hell_floor`,`tower_floor` FROM `user`    ORDER BY `create_time` DESC LIMIT 0,1000
[2025-09-04 14:29:44.464 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`username`,`password`,`nickname`,`sex`,`vip_level`,`vip_score`,`gold`,`yuanbao`,`crystal`,`reg_time`,`title`,`main_pet_id`,`main_pet_name`,`create_time`,`evolution_cd`,`synthesis_cd`,`nirvana_cd`,`money`,`supreme_vip`,`prop_capacity`,`pasture_capacity`,`dragon_ball_exp`,`dragon_ball_level`,`status`,`prestige`,`auto_battle_count`,`auto_nirvana_count`,`monster_count`,`next_battle`,`open_maps`,`star_vip`,`star_vip_expire`,`task_helper_enabled`,`title_attributes`,`soul_pet`,`hell_floor`,`tower_floor` FROM `user`    ORDER BY `create_time` DESC LIMIT 0,1000
[2025-09-04 14:29:44.469 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 14:29:44.472 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 14:29:44.474 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:29:44.519 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:29:44.520 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 14:29:44.524 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `id` AS `Id` , `pet_no` AS `PetNo` , `name` AS `Name` , `attribute` AS `Attribute` , `skill` AS `Skill`  FROM `pet_config` ORDER BY `pet_no` ASC 
[2025-09-04 14:29:44.569 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `id` AS `Id` , `pet_no` AS `PetNo` , `name` AS `Name` , `attribute` AS `Attribute` , `skill` AS `Skill`  FROM `pet_config` ORDER BY `pet_no` ASC 
[2025-09-04 14:29:44.582 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 14:29:44.583 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 14:29:44.586 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:29:44.632 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:29:44.634 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 14:29:44.636 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )   
[2025-09-04 14:29:44.678 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )   
[2025-09-04 14:29:44.682 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )    | 参数: [@MethodConst0=, @MethodConst1=, @MethodConst2=, @MethodConst3=]
[2025-09-04 14:29:44.725 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )   
[2025-09-04 14:29:44.731 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `up`.`id` AS `Id` , `up`.`user_id` AS `UserId` , IFNULL(`u`.`username`,@MethodConst4) AS `UserName` , `up`.`pet_no` AS `PetNo` , IFNULL(`pc`.`name`,@MethodConst5) AS `PetName` , IFNULL(`pc`.`attribute`,@MethodConst6) AS `PetAttribute` , `up`.`image` AS `Image` , `up`.`exp` AS `Exp` , `up`.`hp` AS `Hp` , `up`.`mp` AS `Mp` , `up`.`atk` AS `Atk` , `up`.`def` AS `Def` , `up`.`spd` AS `Spd` , `up`.`state` AS `State` , `up`.`dodge` AS `Dodge` , `up`.`growth` AS `Growth` , `up`.`hit` AS `Hit` , `up`.`realm` AS `Realm` , `up`.`evolve_count` AS `EvolveCount` , `up`.`element` AS `Element` , `up`.`create_time` AS `CreateTime` , IFNULL(`up`.`status`,@MethodConst7) AS `Status` , `up`.`is_main` AS `IsMain`  FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )     ORDER BY `up`.`create_time` DESC LIMIT 0,10 | 参数: [@MethodConst0=, @MethodConst1=, @MethodConst2=, @MethodConst3=, @MethodConst4=, @MethodConst5=, @MethodConst6=, @MethodConst7=]
[2025-09-04 14:29:44.779 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `up`.`id` AS `Id` , `up`.`user_id` AS `UserId` , IFNULL(`u`.`username`,@MethodConst4) AS `UserName` , `up`.`pet_no` AS `PetNo` , IFNULL(`pc`.`name`,@MethodConst5) AS `PetName` , IFNULL(`pc`.`attribute`,@MethodConst6) AS `PetAttribute` , `up`.`image` AS `Image` , `up`.`exp` AS `Exp` , `up`.`hp` AS `Hp` , `up`.`mp` AS `Mp` , `up`.`atk` AS `Atk` , `up`.`def` AS `Def` , `up`.`spd` AS `Spd` , `up`.`state` AS `State` , `up`.`dodge` AS `Dodge` , `up`.`growth` AS `Growth` , `up`.`hit` AS `Hit` , `up`.`realm` AS `Realm` , `up`.`evolve_count` AS `EvolveCount` , `up`.`element` AS `Element` , `up`.`create_time` AS `CreateTime` , IFNULL(`up`.`status`,@MethodConst7) AS `Status` , `up`.`is_main` AS `IsMain`  FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )     ORDER BY `up`.`create_time` DESC LIMIT 0,10
[2025-09-04 14:29:44.782 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=22, @MethodConst1=]
[2025-09-04 14:29:44.823 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-04 14:29:44.828 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=13, @MethodConst1=]
[2025-09-04 14:29:44.868 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-04 14:29:44.870 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=12, @MethodConst1=]
[2025-09-04 14:29:44.906 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-04 14:29:44.911 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=11, @MethodConst1=]
[2025-09-04 14:29:44.950 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-04 14:29:44.952 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=15, @MethodConst1=]
[2025-09-04 14:29:44.988 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-04 14:29:44.991 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=17, @MethodConst1=]
[2025-09-04 14:29:45.033 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-04 14:29:45.036 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=19, @MethodConst1=]
[2025-09-04 14:29:45.081 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-04 14:29:45.084 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=21, @MethodConst1=]
[2025-09-04 14:29:45.123 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-04 14:29:45.132 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=18, @MethodConst1=]
[2025-09-04 14:29:45.171 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-04 14:29:45.173 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=20, @MethodConst1=]
[2025-09-04 14:29:45.227 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-04 14:29:54.435 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 14:29:54.436 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 14:29:54.437 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:29:54.474 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:29:54.487 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 14:29:54.557 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 14:29:54.557 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 14:29:54.558 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 14:29:54.559 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 14:29:54.560 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:29:54.561 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:29:54.600 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:29:54.602 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 14:29:54.605 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `user`  
[2025-09-04 14:29:54.648 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `user`  
[2025-09-04 14:29:54.650 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`username`,`password`,`nickname`,`sex`,`vip_level`,`vip_score`,`gold`,`yuanbao`,`crystal`,`reg_time`,`title`,`main_pet_id`,`main_pet_name`,`create_time`,`evolution_cd`,`synthesis_cd`,`nirvana_cd`,`money`,`supreme_vip`,`prop_capacity`,`pasture_capacity`,`dragon_ball_exp`,`dragon_ball_level`,`status`,`prestige`,`auto_battle_count`,`auto_nirvana_count`,`monster_count`,`next_battle`,`open_maps`,`star_vip`,`star_vip_expire`,`task_helper_enabled`,`title_attributes`,`soul_pet`,`hell_floor`,`tower_floor` FROM `user`    ORDER BY `create_time` DESC LIMIT 0,1000
[2025-09-04 14:29:54.695 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`username`,`password`,`nickname`,`sex`,`vip_level`,`vip_score`,`gold`,`yuanbao`,`crystal`,`reg_time`,`title`,`main_pet_id`,`main_pet_name`,`create_time`,`evolution_cd`,`synthesis_cd`,`nirvana_cd`,`money`,`supreme_vip`,`prop_capacity`,`pasture_capacity`,`dragon_ball_exp`,`dragon_ball_level`,`status`,`prestige`,`auto_battle_count`,`auto_nirvana_count`,`monster_count`,`next_battle`,`open_maps`,`star_vip`,`star_vip_expire`,`task_helper_enabled`,`title_attributes`,`soul_pet`,`hell_floor`,`tower_floor` FROM `user`    ORDER BY `create_time` DESC LIMIT 0,1000
[2025-09-04 14:29:54.715 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 14:29:54.754 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 14:29:54.766 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:29:54.810 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:29:54.819 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 14:29:54.829 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `equip_type_id` AS `Value` , `type_name` AS `Label`  FROM `equipment_type`  
[2025-09-04 14:29:54.851 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:29:54.852 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 14:29:54.879 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `user`  
[2025-09-04 14:29:54.883 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `equip_type_id` AS `Value` , `type_name` AS `Label`  FROM `equipment_type`  
[2025-09-04 14:29:54.893 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 14:29:54.899 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 14:29:54.925 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `user`  
[2025-09-04 14:29:54.937 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:29:54.942 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  
[2025-09-04 14:29:54.983 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  
[2025-09-04 14:29:54.985 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment_type`  
[2025-09-04 14:29:54.987 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:29:54.988 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 14:29:54.998 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `equip_id` AS `Value` , `name` AS `Label` , `name` AS `Name` , `icon` AS `Icon` , `equip_type_id` AS `EquipTypeId`  FROM `equipment`  
[2025-09-04 14:29:55.024 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment_type`  
[2025-09-04 14:29:55.029 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `user_equipment`  
[2025-09-04 14:29:55.046 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `equip_id` AS `Value` , `name` AS `Label` , `name` AS `Name` , `icon` AS `Icon` , `equip_type_id` AS `EquipTypeId`  FROM `equipment`  
[2025-09-04 14:29:55.068 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `user_equipment`  
[2025-09-04 14:29:55.083 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM `user_equipment` `ue` Left JOIN `user` `u` ON ( `ue`.`user_id` = `u`.`id` )  Left JOIN `equipment` `e` ON ( `ue`.`equip_id` = `e`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )   WHERE ( 1 = 1 )   | 参数: [@MethodConst0=, @MethodConst1=, @MethodConst2=, @MethodConst3=, @MethodConst4=0, @MethodConst5=0, @MethodConst6=False, @MethodConst7=2025/9/4 14:29:55]
[2025-09-04 14:29:55.121 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM `user_equipment` `ue` Left JOIN `user` `u` ON ( `ue`.`user_id` = `u`.`id` )  Left JOIN `equipment` `e` ON ( `ue`.`equip_id` = `e`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )   WHERE ( 1 = 1 )  
[2025-09-04 14:29:55.126 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ue`.`id` AS `Id` , `ue`.`user_id` AS `UserId` , IFNULL(`u`.`username`,@MethodConst8) AS `UserName` , `ue`.`equip_id` AS `EquipId` , `ue`.`name` AS `Name` , IFNULL(`ue`.`icon`,@MethodConst9) AS `Icon` , IFNULL(`et`.`equip_type_id`,@MethodConst10) AS `EquipTypeId` , IFNULL(`et`.`type_name`,@MethodConst11) AS `EquipTypeName` , IFNULL(`ue`.`strengthen_level`,@MethodConst12) AS `StrengthenLevel` , IFNULL(`ue`.`slot`,@MethodConst13) AS `Slot` , `ue`.`position` AS `Position` , IFNULL(CAST(`ue`.`is_equipped` as SIGNED),@MethodConst14) AS `IsEquipped` , IFNULL(`ue`.`create_time`,@MethodConst15) AS `CreateTime`  FROM `user_equipment` `ue` Left JOIN `user` `u` ON ( `ue`.`user_id` = `u`.`id` )  Left JOIN `equipment` `e` ON ( `ue`.`equip_id` = `e`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )    WHERE ( 1 = 1 )   ORDER BY `ue`.`create_time` DESC LIMIT 0,10 | 参数: [@MethodConst0=, @MethodConst1=, @MethodConst2=, @MethodConst3=, @MethodConst4=0, @MethodConst5=0, @MethodConst6=False, @MethodConst7=2025/9/4 14:29:55, @MethodConst8=, @MethodConst9=, @MethodConst10=, @MethodConst11=, @MethodConst12=0, @MethodConst13=0, @MethodConst14=False, @MethodConst15=2025/9/4 14:29:55]
[2025-09-04 14:29:55.168 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ue`.`id` AS `Id` , `ue`.`user_id` AS `UserId` , IFNULL(`u`.`username`,@MethodConst8) AS `UserName` , `ue`.`equip_id` AS `EquipId` , `ue`.`name` AS `Name` , IFNULL(`ue`.`icon`,@MethodConst9) AS `Icon` , IFNULL(`et`.`equip_type_id`,@MethodConst10) AS `EquipTypeId` , IFNULL(`et`.`type_name`,@MethodConst11) AS `EquipTypeName` , IFNULL(`ue`.`strengthen_level`,@MethodConst12) AS `StrengthenLevel` , IFNULL(`ue`.`slot`,@MethodConst13) AS `Slot` , `ue`.`position` AS `Position` , IFNULL(CAST(`ue`.`is_equipped` as SIGNED),@MethodConst14) AS `IsEquipped` , IFNULL(`ue`.`create_time`,@MethodConst15) AS `CreateTime`  FROM `user_equipment` `ue` Left JOIN `user` `u` ON ( `ue`.`user_id` = `u`.`id` )  Left JOIN `equipment` `e` ON ( `ue`.`equip_id` = `e`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )    WHERE ( 1 = 1 )   ORDER BY `ue`.`create_time` DESC LIMIT 0,10
[2025-09-04 14:29:55.182 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1 | 参数: [@equip_id0=2020092809, @MethodConst1=0, @MethodConst2=0, @MethodConst3=0, @MethodConst4=0, @MethodConst5=0, @MethodConst6=0, @MethodConst7=0, @MethodConst8=0, @MethodConst9=0, @MethodConst10=0, @MethodConst11=0, @MethodConst12=, @MethodConst13=, @MethodConst14=, @MethodConst15=0, @MethodConst16=0, @MethodConst17=0, @MethodConst18=0, @MethodConst19=0, @MethodConst20=0, @MethodConst21=0, @MethodConst22=0, @MethodConst23=0, @MethodConst24=0, @MethodConst25=0, @MethodConst26=, @MethodConst27=, @MethodConst28=]
[2025-09-04 14:29:55.225 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1
[2025-09-04 14:29:55.233 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1 | 参数: [@equip_id0=789035, @MethodConst1=0, @MethodConst2=0, @MethodConst3=0, @MethodConst4=0, @MethodConst5=0, @MethodConst6=0, @MethodConst7=0, @MethodConst8=0, @MethodConst9=0, @MethodConst10=0, @MethodConst11=0, @MethodConst12=, @MethodConst13=, @MethodConst14=, @MethodConst15=0, @MethodConst16=0, @MethodConst17=0, @MethodConst18=0, @MethodConst19=0, @MethodConst20=0, @MethodConst21=0, @MethodConst22=0, @MethodConst23=0, @MethodConst24=0, @MethodConst25=0, @MethodConst26=, @MethodConst27=, @MethodConst28=]
[2025-09-04 14:29:55.277 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1
[2025-09-04 14:29:55.280 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1 | 参数: [@equip_id0=2020092802, @MethodConst1=0, @MethodConst2=0, @MethodConst3=0, @MethodConst4=0, @MethodConst5=0, @MethodConst6=0, @MethodConst7=0, @MethodConst8=0, @MethodConst9=0, @MethodConst10=0, @MethodConst11=0, @MethodConst12=, @MethodConst13=, @MethodConst14=, @MethodConst15=0, @MethodConst16=0, @MethodConst17=0, @MethodConst18=0, @MethodConst19=0, @MethodConst20=0, @MethodConst21=0, @MethodConst22=0, @MethodConst23=0, @MethodConst24=0, @MethodConst25=0, @MethodConst26=, @MethodConst27=, @MethodConst28=]
[2025-09-04 14:29:55.319 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1
[2025-09-04 14:29:55.325 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1 | 参数: [@equip_id0=789019, @MethodConst1=0, @MethodConst2=0, @MethodConst3=0, @MethodConst4=0, @MethodConst5=0, @MethodConst6=0, @MethodConst7=0, @MethodConst8=0, @MethodConst9=0, @MethodConst10=0, @MethodConst11=0, @MethodConst12=, @MethodConst13=, @MethodConst14=, @MethodConst15=0, @MethodConst16=0, @MethodConst17=0, @MethodConst18=0, @MethodConst19=0, @MethodConst20=0, @MethodConst21=0, @MethodConst22=0, @MethodConst23=0, @MethodConst24=0, @MethodConst25=0, @MethodConst26=, @MethodConst27=, @MethodConst28=]
[2025-09-04 14:29:55.372 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1
[2025-09-04 14:29:55.376 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1 | 参数: [@equip_id0=2018102703, @MethodConst1=0, @MethodConst2=0, @MethodConst3=0, @MethodConst4=0, @MethodConst5=0, @MethodConst6=0, @MethodConst7=0, @MethodConst8=0, @MethodConst9=0, @MethodConst10=0, @MethodConst11=0, @MethodConst12=, @MethodConst13=, @MethodConst14=, @MethodConst15=0, @MethodConst16=0, @MethodConst17=0, @MethodConst18=0, @MethodConst19=0, @MethodConst20=0, @MethodConst21=0, @MethodConst22=0, @MethodConst23=0, @MethodConst24=0, @MethodConst25=0, @MethodConst26=, @MethodConst27=, @MethodConst28=]
[2025-09-04 14:29:55.415 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1
[2025-09-04 14:29:55.420 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1 | 参数: [@equip_id0=2016121602, @MethodConst1=0, @MethodConst2=0, @MethodConst3=0, @MethodConst4=0, @MethodConst5=0, @MethodConst6=0, @MethodConst7=0, @MethodConst8=0, @MethodConst9=0, @MethodConst10=0, @MethodConst11=0, @MethodConst12=, @MethodConst13=, @MethodConst14=, @MethodConst15=0, @MethodConst16=0, @MethodConst17=0, @MethodConst18=0, @MethodConst19=0, @MethodConst20=0, @MethodConst21=0, @MethodConst22=0, @MethodConst23=0, @MethodConst24=0, @MethodConst25=0, @MethodConst26=, @MethodConst27=, @MethodConst28=]
[2025-09-04 14:29:55.464 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1
[2025-09-04 14:29:55.468 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1 | 参数: [@equip_id0=2019042702, @MethodConst1=0, @MethodConst2=0, @MethodConst3=0, @MethodConst4=0, @MethodConst5=0, @MethodConst6=0, @MethodConst7=0, @MethodConst8=0, @MethodConst9=0, @MethodConst10=0, @MethodConst11=0, @MethodConst12=, @MethodConst13=, @MethodConst14=, @MethodConst15=0, @MethodConst16=0, @MethodConst17=0, @MethodConst18=0, @MethodConst19=0, @MethodConst20=0, @MethodConst21=0, @MethodConst22=0, @MethodConst23=0, @MethodConst24=0, @MethodConst25=0, @MethodConst26=, @MethodConst27=, @MethodConst28=]
[2025-09-04 14:29:55.510 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1
[2025-09-04 14:29:55.514 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1 | 参数: [@equip_id0=2021010102, @MethodConst1=0, @MethodConst2=0, @MethodConst3=0, @MethodConst4=0, @MethodConst5=0, @MethodConst6=0, @MethodConst7=0, @MethodConst8=0, @MethodConst9=0, @MethodConst10=0, @MethodConst11=0, @MethodConst12=, @MethodConst13=, @MethodConst14=, @MethodConst15=0, @MethodConst16=0, @MethodConst17=0, @MethodConst18=0, @MethodConst19=0, @MethodConst20=0, @MethodConst21=0, @MethodConst22=0, @MethodConst23=0, @MethodConst24=0, @MethodConst25=0, @MethodConst26=, @MethodConst27=, @MethodConst28=]
[2025-09-04 14:29:55.555 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1
[2025-09-04 14:29:55.560 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1 | 参数: [@equip_id0=789011, @MethodConst1=0, @MethodConst2=0, @MethodConst3=0, @MethodConst4=0, @MethodConst5=0, @MethodConst6=0, @MethodConst7=0, @MethodConst8=0, @MethodConst9=0, @MethodConst10=0, @MethodConst11=0, @MethodConst12=, @MethodConst13=, @MethodConst14=, @MethodConst15=0, @MethodConst16=0, @MethodConst17=0, @MethodConst18=0, @MethodConst19=0, @MethodConst20=0, @MethodConst21=0, @MethodConst22=0, @MethodConst23=0, @MethodConst24=0, @MethodConst25=0, @MethodConst26=, @MethodConst27=, @MethodConst28=]
[2025-09-04 14:29:55.600 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1
[2025-09-04 14:29:55.606 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1 | 参数: [@equip_id0=789009, @MethodConst1=0, @MethodConst2=0, @MethodConst3=0, @MethodConst4=0, @MethodConst5=0, @MethodConst6=0, @MethodConst7=0, @MethodConst8=0, @MethodConst9=0, @MethodConst10=0, @MethodConst11=0, @MethodConst12=, @MethodConst13=, @MethodConst14=, @MethodConst15=0, @MethodConst16=0, @MethodConst17=0, @MethodConst18=0, @MethodConst19=0, @MethodConst20=0, @MethodConst21=0, @MethodConst22=0, @MethodConst23=0, @MethodConst24=0, @MethodConst25=0, @MethodConst26=, @MethodConst27=, @MethodConst28=]
[2025-09-04 14:29:55.648 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1
[2025-09-04 14:30:03.802 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 14:30:03.808 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 14:30:03.811 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:30:03.854 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:30:03.855 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 14:30:03.915 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 14:30:03.916 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 14:30:03.917 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:30:03.955 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:30:03.960 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 14:30:03.968 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `map_config`  
[2025-09-04 14:30:04.009 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `map_config`  
[2025-09-04 14:30:04.012 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`map_id`,`map_name`,`map_desc`,`map_size`,`map_type`,`atlast_name`,`background`,`bgm`,`bgm_loop`,`bgm_volume`,`bgm_play`,`bgm_mute`,`bgm_pause`,`ico`,`type` FROM `map_config`    ORDER BY `id` ASC LIMIT 0,10
[2025-09-04 14:30:04.049 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`map_id`,`map_name`,`map_desc`,`map_size`,`map_type`,`atlast_name`,`background`,`bgm`,`bgm_loop`,`bgm_volume`,`bgm_play`,`bgm_mute`,`bgm_pause`,`ico`,`type` FROM `map_config`    ORDER BY `id` ASC LIMIT 0,10
[2025-09-04 14:30:11.747 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 14:30:11.850 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 14:30:11.854 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:30:11.894 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:30:11.897 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 14:30:11.975 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 14:30:11.977 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 14:30:11.978 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:30:12.015 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:30:12.016 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 14:30:12.037 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `pet_config`  
[2025-09-04 14:30:12.082 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `pet_config`  
[2025-09-04 14:30:12.087 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`pet_no`,`name`,`attribute`,`skill`,`is_active`,`create_time` FROM `pet_config`    ORDER BY `pet_no` ASC LIMIT 0,10
[2025-09-04 14:30:12.128 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`pet_no`,`name`,`attribute`,`skill`,`is_active`,`create_time` FROM `pet_config`    ORDER BY `pet_no` ASC LIMIT 0,10
[2025-09-04 14:30:23.561 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 14:30:23.562 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 14:30:23.564 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:30:23.607 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:30:23.609 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 14:30:23.656 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 14:30:23.658 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 14:30:23.660 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:30:23.699 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:30:23.700 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 14:30:23.702 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `monster_config`  
[2025-09-04 14:30:23.741 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `monster_config`  
[2025-09-04 14:30:23.743 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`monster_no`,`skill`,`name`,`attribute` FROM `monster_config`    ORDER BY `monster_no` ASC LIMIT 0,10
[2025-09-04 14:30:23.779 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`monster_no`,`skill`,`name`,`attribute` FROM `monster_config`    ORDER BY `monster_no` ASC LIMIT 0,10
[2025-09-04 14:30:28.740 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 14:30:28.742 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 14:30:28.744 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:30:28.786 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:30:28.788 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 14:30:28.792 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  DISTINCT `type` FROM `item_config`  WHERE NOT( `type` IS NULL   OR `type`='') 
[2025-09-04 14:30:28.843 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  DISTINCT `type` FROM `item_config`  WHERE NOT( `type` IS NULL   OR `type`='') 
[2025-09-04 14:30:28.856 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  DISTINCT `quality` FROM `item_config`  WHERE NOT( `quality` IS NULL   OR `quality`='') 
[2025-09-04 14:30:28.903 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  DISTINCT `quality` FROM `item_config`  WHERE NOT( `quality` IS NULL   OR `quality`='') 
[2025-09-04 14:30:29.080 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 14:30:29.082 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 14:30:29.084 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:30:29.124 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:30:29.128 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 14:30:29.188 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-09-04 14:30:29.431 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-09-04 14:30:29.433 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 0,10
[2025-09-04 14:30:30.179 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 0,10
[2025-09-04 14:30:32.891 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 14:30:32.893 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 14:30:32.894 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:30:32.936 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:30:32.938 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 14:30:32.942 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active`,`update_time` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=32]
[2025-09-04 14:30:32.986 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active`,`update_time` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-09-04 14:30:32.992 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1 | 参数: [@item_no0=20]
[2025-09-04 14:30:33.035 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1
[2025-09-04 14:30:43.581 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 14:30:43.922 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 14:30:43.931 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:30:43.980 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:30:43.981 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 14:30:43.990 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 14:30:43.992 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 14:30:43.999 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:30:44.054 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:30:44.058 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 14:40:15.762 +08:00 INF] BMS: 🚀 应用程序启动，日志系统已配置
[2025-09-04 14:40:15.786 +08:00 DBG] BMS: 🔧 调试级别日志测试
[2025-09-04 14:40:15.790 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 14:40:15.792 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 14:40:15.837 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:40:16.230 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:40:16.238 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 14:40:16.239 +08:00 INF] BMS: ✅ DbContext 初始化成功，日志文件位置: logs/
[2025-09-04 14:41:23.073 +08:00 WRN] Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware: Failed to determine the https port for redirect.
[2025-09-04 14:41:23.118 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 14:41:23.119 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 14:41:23.123 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:41:23.152 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:41:23.153 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 14:42:45.153 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 14:42:45.154 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 14:42:45.156 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:42:45.190 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:42:45.192 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 14:42:45.204 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 14:42:45.205 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 14:42:45.207 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:42:45.234 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:42:45.235 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 14:48:22.533 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 14:48:22.534 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 14:48:22.535 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:48:22.820 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:48:22.823 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 14:49:06.178 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 14:49:06.180 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 14:49:06.181 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:49:06.225 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:49:06.226 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 14:52:37.586 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 14:52:37.588 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 14:52:37.589 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:52:37.788 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:52:37.790 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 14:55:28.051 +08:00 INF] BMS: 🚀 应用程序启动，日志系统已配置
[2025-09-04 14:55:28.074 +08:00 DBG] BMS: 🔧 调试级别日志测试
[2025-09-04 14:55:28.077 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 14:55:28.079 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 14:55:28.120 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:55:28.635 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:55:28.643 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 14:55:28.644 +08:00 INF] BMS: ✅ DbContext 初始化成功，日志文件位置: logs/
[2025-09-04 14:56:35.371 +08:00 WRN] Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware: Failed to determine the https port for redirect.
[2025-09-04 14:56:35.402 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 14:56:35.403 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 14:56:35.406 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:56:35.452 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:56:35.454 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 14:57:20.229 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 14:57:20.230 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 14:57:20.231 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:57:20.284 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:57:20.285 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 14:57:20.327 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`username`,`password`,`real_name`,`phone`,`email`,`status`,`create_time`,`update_time`,`last_login_time` FROM `admin_bm`   WHERE ( `username` = @username0 )   LIMIT 0,1 | 参数: [@username0=HM]
[2025-09-04 14:57:20.394 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`username`,`password`,`real_name`,`phone`,`email`,`status`,`create_time`,`update_time`,`last_login_time` FROM `admin_bm`   WHERE ( `username` = @username0 )   LIMIT 0,1
[2025-09-04 14:57:30.126 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 14:57:30.126 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 14:57:30.128 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:57:30.177 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:57:30.179 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 14:57:30.181 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`username`,`password`,`real_name`,`phone`,`email`,`status`,`create_time`,`update_time`,`last_login_time` FROM `admin_bm`   WHERE ( `username` = @username0 )   LIMIT 0,1 | 参数: [@username0=HM]
[2025-09-04 14:57:30.260 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`username`,`password`,`real_name`,`phone`,`email`,`status`,`create_time`,`update_time`,`last_login_time` FROM `admin_bm`   WHERE ( `username` = @username0 )   LIMIT 0,1
[2025-09-04 14:57:30.290 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: UPDATE `admin_bm`  SET
            `update_time` = @constant1 , `last_login_time` = @constant0   WHERE ( `id` = @id2 ) | 参数: [@constant0=2025/9/4 14:57:30, @constant1=2025/9/4 14:57:30, @id2=1, @id=0]
[2025-09-04 14:57:30.453 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: UPDATE `admin_bm`  SET
            `update_time` = @constant1 , `last_login_time` = @constant0   WHERE ( `id` = @id2 )
[2025-09-04 14:59:49.026 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 14:59:49.028 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 14:59:49.029 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:59:49.329 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:59:49.330 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 14:59:49.376 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 14:59:49.377 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 14:59:49.378 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:59:49.456 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:59:49.457 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 14:59:49.470 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 14:59:49.471 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 14:59:49.472 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:59:49.524 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:59:49.526 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 14:59:49.535 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `task_id` AS `Value` , `task_name` AS `Label`  FROM `task_config`  WHERE ( `is_active` = @is_active0 )ORDER BY `sort_order` ASC  | 参数: [@is_active0=1]
[2025-09-04 14:59:49.586 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `task_id` AS `Value` , `task_name` AS `Label`  FROM `task_config`  WHERE ( `is_active` = @is_active0 )ORDER BY `sort_order` ASC 
[2025-09-04 14:59:49.592 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 14:59:49.593 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 14:59:49.594 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:59:49.643 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 14:59:49.645 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 14:59:49.669 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM `task_config`  
[2025-09-04 14:59:49.723 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM `task_config`  
[2025-09-04 14:59:49.725 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`    ORDER BY `sort_order` ASC,`created_at` DESC LIMIT 0,10
[2025-09-04 14:59:49.778 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`    ORDER BY `sort_order` ASC,`created_at` DESC LIMIT 0,10
[2025-09-04 14:59:49.783 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_001]
[2025-09-04 14:59:49.838 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-09-04 14:59:49.841 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_002]
[2025-09-04 14:59:49.895 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-09-04 14:59:49.897 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_003]
[2025-09-04 14:59:49.948 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-09-04 14:59:49.949 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_004]
[2025-09-04 14:59:50.012 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-09-04 14:59:50.014 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_005]
[2025-09-04 14:59:50.066 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-09-04 14:59:50.069 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_010]
[2025-09-04 14:59:50.126 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-09-04 15:10:37.817 +08:00 INF] BMS: 🚀 应用程序启动，日志系统已配置
[2025-09-04 15:10:37.868 +08:00 DBG] BMS: 🔧 调试级别日志测试
[2025-09-04 15:10:37.888 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 15:10:37.890 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 15:10:37.986 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 15:10:38.475 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 15:10:38.488 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 15:10:38.495 +08:00 INF] BMS: ✅ DbContext 初始化成功，日志文件位置: logs/
[2025-09-04 15:10:39.561 +08:00 WRN] Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware: Failed to determine the https port for redirect.
[2025-09-04 15:10:41.692 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 15:10:41.701 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 15:10:41.706 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 15:10:41.744 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 15:10:41.746 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 21:35:36.207 +08:00 INF] BMS: 🚀 应用程序启动，日志系统已配置
[2025-09-04 21:35:36.232 +08:00 DBG] BMS: 🔧 调试级别日志测试
[2025-09-04 21:35:36.236 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 21:35:36.238 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 21:35:36.287 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 21:35:36.906 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 21:35:36.914 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 21:35:36.915 +08:00 INF] BMS: ✅ DbContext 初始化成功，日志文件位置: logs/
[2025-09-04 21:36:37.868 +08:00 WRN] Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware: Failed to determine the https port for redirect.
[2025-09-04 21:36:37.920 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 21:36:37.922 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 21:36:37.927 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 21:36:37.974 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 21:36:37.976 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 21:36:38.153 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 21:36:38.155 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 21:36:38.158 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 21:36:38.209 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 21:36:38.210 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 21:36:38.303 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `user`  
[2025-09-04 21:36:38.359 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `user`  
[2025-09-04 21:36:38.393 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`username`,`password`,`nickname`,`sex`,`vip_level`,`vip_score`,`gold`,`yuanbao`,`crystal`,`reg_time`,`title`,`main_pet_id`,`main_pet_name`,`create_time`,`evolution_cd`,`synthesis_cd`,`nirvana_cd`,`money`,`supreme_vip`,`prop_capacity`,`pasture_capacity`,`dragon_ball_exp`,`dragon_ball_level`,`status`,`prestige`,`auto_battle_count`,`auto_nirvana_count`,`monster_count`,`next_battle`,`open_maps`,`star_vip`,`star_vip_expire`,`task_helper_enabled`,`title_attributes`,`soul_pet`,`hell_floor`,`tower_floor` FROM `user`    ORDER BY `create_time` DESC LIMIT 0,1000
[2025-09-04 21:36:38.447 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`username`,`password`,`nickname`,`sex`,`vip_level`,`vip_score`,`gold`,`yuanbao`,`crystal`,`reg_time`,`title`,`main_pet_id`,`main_pet_name`,`create_time`,`evolution_cd`,`synthesis_cd`,`nirvana_cd`,`money`,`supreme_vip`,`prop_capacity`,`pasture_capacity`,`dragon_ball_exp`,`dragon_ball_level`,`status`,`prestige`,`auto_battle_count`,`auto_nirvana_count`,`monster_count`,`next_battle`,`open_maps`,`star_vip`,`star_vip_expire`,`task_helper_enabled`,`title_attributes`,`soul_pet`,`hell_floor`,`tower_floor` FROM `user`    ORDER BY `create_time` DESC LIMIT 0,1000
[2025-09-04 21:36:38.501 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 21:36:38.501 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 21:36:38.502 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 21:36:38.550 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 21:36:38.551 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 21:36:38.562 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `id` AS `Id` , `pet_no` AS `PetNo` , `name` AS `Name` , `attribute` AS `Attribute` , `skill` AS `Skill`  FROM `pet_config` ORDER BY `pet_no` ASC 
[2025-09-04 21:36:38.612 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `id` AS `Id` , `pet_no` AS `PetNo` , `name` AS `Name` , `attribute` AS `Attribute` , `skill` AS `Skill`  FROM `pet_config` ORDER BY `pet_no` ASC 
[2025-09-04 21:36:38.643 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 21:36:38.645 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 21:36:38.646 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 21:36:38.703 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 21:36:38.705 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 21:36:38.749 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )   
[2025-09-04 21:36:38.797 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )   
[2025-09-04 21:36:38.825 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )    | 参数: [@MethodConst0=, @MethodConst1=, @MethodConst2=, @MethodConst3=]
[2025-09-04 21:36:38.878 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )   
[2025-09-04 21:36:38.880 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `up`.`id` AS `Id` , `up`.`user_id` AS `UserId` , IFNULL(`u`.`username`,@MethodConst4) AS `UserName` , `up`.`pet_no` AS `PetNo` , IFNULL(`pc`.`name`,@MethodConst5) AS `PetName` , IFNULL(`pc`.`attribute`,@MethodConst6) AS `PetAttribute` , `up`.`image` AS `Image` , `up`.`exp` AS `Exp` , `up`.`hp` AS `Hp` , `up`.`mp` AS `Mp` , `up`.`atk` AS `Atk` , `up`.`def` AS `Def` , `up`.`spd` AS `Spd` , `up`.`state` AS `State` , `up`.`dodge` AS `Dodge` , `up`.`growth` AS `Growth` , `up`.`hit` AS `Hit` , `up`.`realm` AS `Realm` , `up`.`evolve_count` AS `EvolveCount` , `up`.`element` AS `Element` , `up`.`create_time` AS `CreateTime` , IFNULL(`up`.`status`,@MethodConst7) AS `Status` , `up`.`is_main` AS `IsMain`  FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )     ORDER BY `up`.`create_time` DESC LIMIT 0,10 | 参数: [@MethodConst0=, @MethodConst1=, @MethodConst2=, @MethodConst3=, @MethodConst4=, @MethodConst5=, @MethodConst6=, @MethodConst7=]
[2025-09-04 21:36:38.932 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `up`.`id` AS `Id` , `up`.`user_id` AS `UserId` , IFNULL(`u`.`username`,@MethodConst4) AS `UserName` , `up`.`pet_no` AS `PetNo` , IFNULL(`pc`.`name`,@MethodConst5) AS `PetName` , IFNULL(`pc`.`attribute`,@MethodConst6) AS `PetAttribute` , `up`.`image` AS `Image` , `up`.`exp` AS `Exp` , `up`.`hp` AS `Hp` , `up`.`mp` AS `Mp` , `up`.`atk` AS `Atk` , `up`.`def` AS `Def` , `up`.`spd` AS `Spd` , `up`.`state` AS `State` , `up`.`dodge` AS `Dodge` , `up`.`growth` AS `Growth` , `up`.`hit` AS `Hit` , `up`.`realm` AS `Realm` , `up`.`evolve_count` AS `EvolveCount` , `up`.`element` AS `Element` , `up`.`create_time` AS `CreateTime` , IFNULL(`up`.`status`,@MethodConst7) AS `Status` , `up`.`is_main` AS `IsMain`  FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )     ORDER BY `up`.`create_time` DESC LIMIT 0,10
[2025-09-04 21:36:38.953 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=22, @MethodConst1=]
[2025-09-04 21:36:39.001 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-04 21:36:39.003 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=13, @MethodConst1=]
[2025-09-04 21:36:39.050 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-04 21:36:39.052 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=12, @MethodConst1=]
[2025-09-04 21:36:39.097 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-04 21:36:39.099 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=11, @MethodConst1=]
[2025-09-04 21:36:39.146 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-04 21:36:39.148 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=15, @MethodConst1=]
[2025-09-04 21:36:39.194 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-04 21:36:39.196 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=17, @MethodConst1=]
[2025-09-04 21:36:39.242 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-04 21:36:39.245 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=19, @MethodConst1=]
[2025-09-04 21:36:39.296 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-04 21:36:39.300 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=21, @MethodConst1=]
[2025-09-04 21:36:39.346 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-04 21:36:39.348 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=18, @MethodConst1=]
[2025-09-04 21:36:39.393 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-04 21:36:39.395 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=20, @MethodConst1=]
[2025-09-04 21:36:39.441 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-04 21:42:21.029 +08:00 INF] BMS: 🚀 应用程序启动，日志系统已配置
[2025-09-04 21:42:21.468 +08:00 DBG] BMS: 🔧 调试级别日志测试
[2025-09-04 21:42:21.577 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 21:42:21.608 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 21:42:21.740 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 21:42:22.329 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 21:42:22.338 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 21:42:22.340 +08:00 INF] BMS: ✅ DbContext 初始化成功，日志文件位置: logs/
[2025-09-04 21:42:23.769 +08:00 WRN] Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware: Failed to determine the https port for redirect.
[2025-09-04 21:46:20.222 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 21:46:20.224 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 21:46:20.228 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 21:46:20.517 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 21:46:20.518 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 21:46:43.070 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 21:46:43.075 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 21:46:43.078 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 21:46:43.127 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 21:46:43.136 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 22:01:15.474 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 22:01:15.498 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 22:01:15.500 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 22:01:15.770 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 22:01:15.933 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-04 22:01:34.619 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-04 22:01:34.626 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-04 22:01:34.628 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 22:01:34.674 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-04 22:01:34.680 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
