using Microsoft.AspNetCore.Mvc;
using BMS.Models.DTOs;
using BMS.Models.Common;
using BMS.Services.Interfaces;

namespace BMS.Controllers
{
    /// <summary>
    /// 地图怪物控制器
    /// </summary>
    public class MapMonsterController : Controller
    {
        private readonly IMapMonsterService _mapMonsterService;
        private readonly IMapConfigService _mapConfigService;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="mapMonsterService">地图怪物服务</param>
        /// <param name="mapConfigService">地图配置服务</param>
        public MapMonsterController(IMapMonsterService mapMonsterService, IMapConfigService mapConfigService)
        {
            _mapMonsterService = mapMonsterService;
            _mapConfigService = mapConfigService;
        }

        /// <summary>
        /// 地图怪物管理页面
        /// </summary>
        /// <returns>页面视图</returns>
        public async Task<IActionResult> Index()
        {
            // 获取地图列表用于下拉选择
            var maps = await _mapConfigService.GetAllAsync();
            ViewBag.Maps = maps;
            return View();
        }

        /// <summary>
        /// 创建地图怪物页面
        /// </summary>
        /// <returns>页面视图</returns>
        public async Task<IActionResult> Create()
        {
            // 获取地图列表用于下拉选择
            var maps = await _mapConfigService.GetAllAsync();
            ViewBag.Maps = maps;
            return View();
        }

        /// <summary>
        /// 编辑地图怪物页面
        /// </summary>
        /// <param name="id">地图怪物ID</param>
        /// <returns>页面视图</returns>
        public async Task<IActionResult> Edit(int id)
        {
            var mapMonster = await _mapMonsterService.GetByIdAsync(id);
            if (mapMonster == null)
            {
                return NotFound();
            }

            // 获取地图列表用于下拉选择
            var maps = await _mapConfigService.GetAllAsync();
            ViewBag.Maps = maps;
            
            return View(mapMonster);
        }

        /// <summary>
        /// 分页获取地图怪物列表
        /// </summary>
        /// <param name="queryDto">查询条件</param>
        /// <returns>分页结果</returns>
        [HttpGet]
        public async Task<IActionResult> GetPagedList([FromQuery] MapMonsterQueryDto queryDto)
        {
            try
            {
                var result = await _mapMonsterService.GetPagedListAsync(queryDto);
                return Json(new
                {
                    success = true,
                    data = result.Data,
                    page = result.PageIndex,
                    pageSize = result.PageSize,
                    totalCount = result.TotalCount,
                    totalPages = result.TotalPages
                });
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = $"获取地图怪物列表失败：{ex.Message}" });
            }
        }

        /// <summary>
        /// 根据ID获取地图怪物
        /// </summary>
        /// <param name="id">地图怪物ID</param>
        /// <returns>地图怪物信息</returns>
        [HttpGet]
        public async Task<IActionResult> GetById(int id)
        {
            try
            {
                var mapMonster = await _mapMonsterService.GetByIdAsync(id);
                if (mapMonster == null)
                {
                    return Json(new { success = false, message = "地图怪物不存在" });
                }
                return Json(new { success = true, data = mapMonster });
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = $"获取地图怪物失败：{ex.Message}" });
            }
        }

        /// <summary>
        /// 根据地图ID获取怪物列表
        /// </summary>
        /// <param name="mapId">地图ID</param>
        /// <returns>怪物列表</returns>
        [HttpGet]
        public async Task<IActionResult> GetByMapId(int mapId)
        {
            try
            {
                var monsters = await _mapMonsterService.GetByMapIdAsync(mapId);
                return Json(new { success = true, data = monsters });
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = $"获取地图怪物列表失败：{ex.Message}" });
            }
        }

        /// <summary>
        /// 创建地图怪物
        /// </summary>
        /// <param name="createDto">创建DTO</param>
        /// <returns>操作结果</returns>
        [HttpPost]
        public async Task<IActionResult> Create([FromBody] MapMonsterCreateDto createDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    var errors = ModelState.Values
                        .SelectMany(v => v.Errors)
                        .Select(e => e.ErrorMessage)
                        .ToList();
                    return Json(new { success = false, message = string.Join(", ", errors) });
                }

                var result = await _mapMonsterService.CreateAsync(createDto);
                return Json(new { success = result.Success, message = result.Message });
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = $"创建地图怪物失败：{ex.Message}" });
            }
        }

        /// <summary>
        /// 更新地图怪物
        /// </summary>
        /// <param name="updateDto">更新DTO</param>
        /// <returns>操作结果</returns>
        [HttpPost]
        public async Task<IActionResult> Update([FromBody] MapMonsterUpdateDto updateDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    var errors = ModelState.Values
                        .SelectMany(v => v.Errors)
                        .Select(e => e.ErrorMessage)
                        .ToList();
                    return Json(new { success = false, message = string.Join(", ", errors) });
                }

                var result = await _mapMonsterService.UpdateAsync(updateDto);
                return Json(new { success = result.Success, message = result.Message });
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = $"更新地图怪物失败：{ex.Message}" });
            }
        }

        /// <summary>
        /// 删除地图怪物
        /// </summary>
        /// <param name="dto">删除地图怪物DTO</param>
        /// <returns>操作结果</returns>
        [HttpPost]
        public async Task<IActionResult> Delete([FromBody] MapMonsterDeleteDto dto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    var errors = ModelState.Values
                        .SelectMany(v => v.Errors)
                        .Select(e => e.ErrorMessage)
                        .ToList();
                    return Json(new { success = false, message = string.Join(", ", errors) });
                }

                var result = await _mapMonsterService.DeleteAsync(dto.Id);
                return Json(new { success = result.Success, message = result.Message });
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = $"删除地图怪物失败：{ex.Message}" });
            }
        }

        /// <summary>
        /// 检查地图怪物是否存在
        /// </summary>
        /// <param name="mapId">地图ID</param>
        /// <param name="monsterId">怪物序号</param>
        /// <param name="id">排除的ID（编辑时使用）</param>
        /// <returns>检查结果</returns>
        [HttpGet]
        public async Task<IActionResult> CheckMapMonsterExists(int mapId, int monsterId, int? id = null)
        {
            try
            {
                var exists = await _mapMonsterService.CheckMapMonsterExistsAsync(mapId, monsterId, id);
                return Json(new { exists });
            }
            catch (Exception ex)
            {
                return Json(new { exists = false, message = $"检查失败：{ex.Message}" });
            }
        }
    }
} 