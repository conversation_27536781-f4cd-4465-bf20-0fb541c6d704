using BMS.Models.DTOs;
using BMS.Models.Common;

namespace BMS.Services.Interfaces
{
    /// <summary>
    /// 地图详情服务接口
    /// </summary>
    public interface IMapDetailService
    {
        /// <summary>
        /// 分页获取地图详情列表
        /// </summary>
        /// <param name="queryDto">查询条件</param>
        /// <returns>分页结果</returns>
        Task<PagedResult<MapDetailDto>> GetPagedListAsync(MapDetailQueryDto queryDto);

        /// <summary>
        /// 根据ID获取地图详情
        /// </summary>
        /// <param name="id">地图详情ID</param>
        /// <returns>地图详情信息</returns>
        Task<MapDetailDto?> GetByIdAsync(int id);

        /// <summary>
        /// 根据地图ID获取地图详情
        /// </summary>
        /// <param name="mapId">地图ID</param>
        /// <returns>地图详情信息</returns>
        Task<MapDetailDto?> GetByMapIdAsync(int mapId);

        /// <summary>
        /// 创建地图详情
        /// </summary>
        /// <param name="createDto">创建DTO</param>
        /// <returns>操作结果</returns>
        Task<ApiResult<bool>> CreateAsync(MapDetailCreateDto createDto);

        /// <summary>
        /// 更新地图详情
        /// </summary>
        /// <param name="updateDto">更新DTO</param>
        /// <returns>操作结果</returns>
        Task<ApiResult<bool>> UpdateAsync(MapDetailUpdateDto updateDto);

        /// <summary>
        /// 删除地图详情
        /// </summary>
        /// <param name="id">地图详情ID</param>
        /// <returns>操作结果</returns>
        Task<ApiResult<bool>> DeleteAsync(int id);

        /// <summary>
        /// 根据地图ID删除地图详情
        /// </summary>
        /// <param name="mapId">地图ID</param>
        /// <returns>操作结果</returns>
        Task<ApiResult<bool>> DeleteByMapIdAsync(int mapId);
    }
} 