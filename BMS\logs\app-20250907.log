[2025-09-07 20:41:43.768 +08:00 INF] BMS: 🚀 应用程序启动，日志系统已配置
[2025-09-07 20:41:43.803 +08:00 DBG] BMS: 🔧 调试级别日志测试
[2025-09-07 20:41:43.828 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 20:41:43.832 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 20:41:43.975 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 20:41:44.581 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 20:41:44.592 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 20:41:44.596 +08:00 INF] BMS: ✅ DbContext 初始化成功，日志文件位置: logs/
[2025-09-07 20:41:46.127 +08:00 WRN] Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware: Failed to determine the https port for redirect.
[2025-09-07 20:42:00.568 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 20:42:00.571 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 20:42:00.575 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 20:42:00.609 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 20:42:00.611 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 20:42:00.644 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `equip_type_id` AS `EquipTypeId` , `type_name` AS `TypeName`  FROM `equipment_type` ORDER BY `id` ASC 
[2025-09-07 20:42:00.684 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `equip_type_id` AS `EquipTypeId` , `type_name` AS `TypeName`  FROM `equipment_type` ORDER BY `id` ASC 
[2025-09-07 20:42:00.843 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 20:42:00.844 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 20:42:00.846 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 20:42:00.879 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 20:42:00.882 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 20:42:00.976 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  
[2025-09-07 20:42:01.010 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  
[2025-09-07 20:42:01.020 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment_detail`  
[2025-09-07 20:42:01.055 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment_detail`  
[2025-09-07 20:42:01.058 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment_type`  
[2025-09-07 20:42:01.092 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment_type`  
[2025-09-07 20:42:01.109 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )   
[2025-09-07 20:42:01.154 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )   
[2025-09-07 20:42:01.186 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `e`.`equip_id` AS `EquipId` , @constant14 AS `ClassId` , `e`.`name` AS `Name` , `e`.`icon` AS `Icon` , `e`.`equip_type_id` AS `EquipTypeId` , `et`.`type_name` AS `EquipTypeName` , `e`.`element` AS `Element` , IFNULL(`e`.`slot`,@MethodConst15) AS `Slot` , IFNULL(`e`.`strengthen_level`,@MethodConst16) AS `StrengthenLevel` , `e`.`suit_id` AS `SuitId` , IFNULL(`ed`.`atk`,@MethodConst17) AS `Atk` , IFNULL(`ed`.`hit`,@MethodConst18) AS `Hit` , IFNULL(`ed`.`def`,@MethodConst19) AS `Def` , IFNULL(`ed`.`spd`,@MethodConst20) AS `Spd` , IFNULL(`ed`.`dodge`,@MethodConst21) AS `Dodge` , IFNULL(`ed`.`hp`,@MethodConst22) AS `Hp` , IFNULL(`ed`.`mp`,@MethodConst23) AS `Mp` , IFNULL(`ed`.`deepen`,@MethodConst24) AS `Deepen` , IFNULL(`ed`.`offset`,@MethodConst25) AS `Offset` , IFNULL(`ed`.`vamp`,@MethodConst26) AS `Vamp` , IFNULL(`ed`.`vamp_mp`,@MethodConst27) AS `VampMp` , `ed`.`description` AS `Description` , `ed`.`main_attr` AS `MainAttr` , `ed`.`main_attr_value` AS `MainAttrValue` , `ed`.`sub_attr` AS `SubAttr` , `ed`.`sub_attr_value` AS `SubAttrValue` , `ed`.`element_limit` AS `ElementLimit` , `ed`.`equip_name` AS `EquipName`  FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )     ORDER BY `e`.`equip_id` ASC LIMIT 0,10 | 参数: [@constant0=, @MethodConst1=0, @MethodConst2=0, @MethodConst3=0, @MethodConst4=0, @MethodConst5=0, @MethodConst6=0, @MethodConst7=0, @MethodConst8=0, @MethodConst9=0, @MethodConst10=0, @MethodConst11=0, @MethodConst12=0, @MethodConst13=0, @constant14=, @MethodConst15=0, @MethodConst16=0, @MethodConst17=0, @MethodConst18=0, @MethodConst19=0, @MethodConst20=0, @MethodConst21=0, @MethodConst22=0, @MethodConst23=0, @MethodConst24=0, @MethodConst25=0, @MethodConst26=0, @MethodConst27=0]
[2025-09-07 20:42:01.245 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `e`.`equip_id` AS `EquipId` , @constant14 AS `ClassId` , `e`.`name` AS `Name` , `e`.`icon` AS `Icon` , `e`.`equip_type_id` AS `EquipTypeId` , `et`.`type_name` AS `EquipTypeName` , `e`.`element` AS `Element` , IFNULL(`e`.`slot`,@MethodConst15) AS `Slot` , IFNULL(`e`.`strengthen_level`,@MethodConst16) AS `StrengthenLevel` , `e`.`suit_id` AS `SuitId` , IFNULL(`ed`.`atk`,@MethodConst17) AS `Atk` , IFNULL(`ed`.`hit`,@MethodConst18) AS `Hit` , IFNULL(`ed`.`def`,@MethodConst19) AS `Def` , IFNULL(`ed`.`spd`,@MethodConst20) AS `Spd` , IFNULL(`ed`.`dodge`,@MethodConst21) AS `Dodge` , IFNULL(`ed`.`hp`,@MethodConst22) AS `Hp` , IFNULL(`ed`.`mp`,@MethodConst23) AS `Mp` , IFNULL(`ed`.`deepen`,@MethodConst24) AS `Deepen` , IFNULL(`ed`.`offset`,@MethodConst25) AS `Offset` , IFNULL(`ed`.`vamp`,@MethodConst26) AS `Vamp` , IFNULL(`ed`.`vamp_mp`,@MethodConst27) AS `VampMp` , `ed`.`description` AS `Description` , `ed`.`main_attr` AS `MainAttr` , `ed`.`main_attr_value` AS `MainAttrValue` , `ed`.`sub_attr` AS `SubAttr` , `ed`.`sub_attr_value` AS `SubAttrValue` , `ed`.`element_limit` AS `ElementLimit` , `ed`.`equip_name` AS `EquipName`  FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )     ORDER BY `e`.`equip_id` ASC LIMIT 0,10
[2025-09-07 20:42:01.289 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 20:42:01.293 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 20:42:01.296 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 20:42:01.331 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 20:42:01.335 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 20:42:01.343 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment_type`  
[2025-09-07 20:42:01.377 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment_type`  
[2025-09-07 20:42:01.380 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `id` AS `Id` , `equip_type_id` AS `EquipTypeId` , `type_name` AS `TypeName` , @constant6 AS `Description` , @constant7 AS `SortOrder` , @constant8 AS `IsActive` , @constant9 AS `CreateTime` , @constant10 AS `UpdateTime` , @constant11 AS `EquipmentCount`  FROM `equipment_type`    ORDER BY `id` ASC LIMIT 0,10 | 参数: [@constant0=, @constant1=0, @constant2=True, @constant3=, @constant4=, @constant5=0, @constant6=, @constant7=0, @constant8=True, @constant9=, @constant10=, @constant11=0]
[2025-09-07 20:42:01.417 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `id` AS `Id` , `equip_type_id` AS `EquipTypeId` , `type_name` AS `TypeName` , @constant6 AS `Description` , @constant7 AS `SortOrder` , @constant8 AS `IsActive` , @constant9 AS `CreateTime` , @constant10 AS `UpdateTime` , @constant11 AS `EquipmentCount`  FROM `equipment_type`    ORDER BY `id` ASC LIMIT 0,10
[2025-09-07 20:42:01.454 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=1]
[2025-09-07 20:42:01.490 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-09-07 20:42:01.511 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=2]
[2025-09-07 20:42:01.544 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-09-07 20:42:01.551 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=3]
[2025-09-07 20:42:01.586 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-09-07 20:42:01.593 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=4]
[2025-09-07 20:42:01.624 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-09-07 20:42:01.633 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=5]
[2025-09-07 20:42:01.668 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-09-07 20:42:01.675 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=6]
[2025-09-07 20:42:01.708 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-09-07 20:42:01.710 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=7]
[2025-09-07 20:42:01.743 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-09-07 20:42:01.745 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=8]
[2025-09-07 20:42:01.778 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-09-07 20:42:01.784 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=9]
[2025-09-07 20:42:01.819 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-09-07 20:42:01.822 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=10]
[2025-09-07 20:42:01.853 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-09-07 20:42:12.041 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 20:42:12.049 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 20:42:12.052 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 20:42:12.087 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 20:42:12.088 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 20:42:12.146 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 20:42:12.147 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 20:42:12.148 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 20:42:12.181 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 20:42:12.183 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 20:42:12.189 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `skill`  
[2025-09-07 20:42:12.222 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `skill`  
[2025-09-07 20:42:12.224 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `skill_id`,`skill_name`,`skill_percent`,`effect_type`,`effect_value`,`mana_cost`,`buff_info`,`element_limit`,`create_time`,`skill_type` FROM `skill`    ORDER BY `create_time` DESC LIMIT 0,10
[2025-09-07 20:42:12.256 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `skill_id`,`skill_name`,`skill_percent`,`effect_type`,`effect_value`,`mana_cost`,`buff_info`,`element_limit`,`create_time`,`skill_type` FROM `skill`    ORDER BY `create_time` DESC LIMIT 0,10
[2025-09-07 20:42:15.247 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 20:42:15.248 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 20:42:15.250 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 20:42:15.281 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 20:42:15.331 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 20:42:15.345 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `skill`  
[2025-09-07 20:42:15.388 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `skill`  
[2025-09-07 20:42:15.402 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `skill_id`,`skill_name`,`skill_percent`,`effect_type`,`effect_value`,`mana_cost`,`buff_info`,`element_limit`,`create_time`,`skill_type` FROM `skill`    ORDER BY `create_time` DESC LIMIT 10,10
[2025-09-07 20:42:15.437 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `skill_id`,`skill_name`,`skill_percent`,`effect_type`,`effect_value`,`mana_cost`,`buff_info`,`element_limit`,`create_time`,`skill_type` FROM `skill`    ORDER BY `create_time` DESC LIMIT 10,10
[2025-09-07 20:42:16.334 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 20:42:16.346 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 20:42:16.348 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 20:42:16.381 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 20:42:16.382 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 20:42:16.383 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `skill`  
[2025-09-07 20:42:16.415 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `skill`  
[2025-09-07 20:42:16.417 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `skill_id`,`skill_name`,`skill_percent`,`effect_type`,`effect_value`,`mana_cost`,`buff_info`,`element_limit`,`create_time`,`skill_type` FROM `skill`    ORDER BY `create_time` DESC LIMIT 0,10
[2025-09-07 20:42:16.450 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `skill_id`,`skill_name`,`skill_percent`,`effect_type`,`effect_value`,`mana_cost`,`buff_info`,`element_limit`,`create_time`,`skill_type` FROM `skill`    ORDER BY `create_time` DESC LIMIT 0,10
[2025-09-07 20:42:21.224 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 20:42:21.228 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 20:42:21.230 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 20:42:21.264 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 20:42:21.266 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 20:42:21.269 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`map_id`,`map_name`,`map_desc`,`map_size`,`map_type`,`atlast_name`,`background`,`bgm`,`bgm_loop`,`bgm_volume`,`bgm_play`,`bgm_mute`,`bgm_pause`,`ico`,`type` FROM `map_config` ORDER BY `map_id` ASC 
[2025-09-07 20:42:21.304 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`map_id`,`map_name`,`map_desc`,`map_size`,`map_type`,`atlast_name`,`background`,`bgm`,`bgm_loop`,`bgm_volume`,`bgm_play`,`bgm_mute`,`bgm_pause`,`ico`,`type` FROM `map_config` ORDER BY `map_id` ASC 
[2025-09-07 20:42:21.358 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 20:42:21.359 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 20:42:21.359 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 20:42:21.360 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 20:42:21.361 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 20:42:21.363 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 20:42:21.393 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 20:42:21.396 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 20:42:21.399 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`map_id`,`map_name`,`map_desc`,`map_size`,`map_type`,`atlast_name`,`background`,`bgm`,`bgm_loop`,`bgm_volume`,`bgm_play`,`bgm_mute`,`bgm_pause`,`ico`,`type` FROM `map_config` ORDER BY `map_id` ASC 
[2025-09-07 20:42:21.430 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`map_id`,`map_name`,`map_desc`,`map_size`,`map_type`,`atlast_name`,`background`,`bgm`,`bgm_loop`,`bgm_volume`,`bgm_play`,`bgm_mute`,`bgm_pause`,`ico`,`type` FROM `map_config` ORDER BY `map_id` ASC 
[2025-09-07 20:42:21.632 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 20:42:21.695 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 20:42:21.717 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM `map_detail` `d` Left JOIN `map_config` `c` ON ( `d`.`map_id` = `c`.`map_id` )    | 参数: [@MethodConst0=0, @MethodConst1=0, @MethodConst2=False, @MethodConst3=0, @MethodConst4=0, @MethodConst5=0, @MethodConst6=0, @MethodConst7=0, @MethodConst8=0, @MethodConst9=0]
[2025-09-07 20:42:21.765 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM `map_detail` `d` Left JOIN `map_config` `c` ON ( `d`.`map_id` = `c`.`map_id` )   
[2025-09-07 20:42:21.796 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `d`.`id` AS `Id` , `d`.`map_id` AS `MapId` , `c`.`map_name` AS `MapName` , IFNULL(`d`.`limit_growth`,@MethodConst10) AS `LimitGrowth` , IFNULL(`d`.`limit_level`,@MethodConst11) AS `LimitLevel` , IFNULL(CAST(`d`.`limit_key` as SIGNED),@MethodConst12) AS `LimitKey` , IFNULL(`d`.`min_gold`,@MethodConst13) AS `MinGold` , IFNULL(`d`.`max_gold`,@MethodConst14) AS `MaxGold` , IFNULL(`d`.`min_yuanbao`,@MethodConst15) AS `MinYuanbao` , IFNULL(`d`.`max_yuanbao`,@MethodConst16) AS `MaxYuanbao` , IFNULL(`d`.`min_drop`,@MethodConst17) AS `MinDrop` , IFNULL(`d`.`max_drop`,@MethodConst18) AS `MaxDrop` , `d`.`icon` AS `Icon` , IFNULL(`d`.`type`,@MethodConst19) AS `Type`  FROM `map_detail` `d` Left JOIN `map_config` `c` ON ( `d`.`map_id` = `c`.`map_id` )     ORDER BY `d`.`id` ASC LIMIT 0,10 | 参数: [@MethodConst0=0, @MethodConst1=0, @MethodConst2=False, @MethodConst3=0, @MethodConst4=0, @MethodConst5=0, @MethodConst6=0, @MethodConst7=0, @MethodConst8=0, @MethodConst9=0, @MethodConst10=0, @MethodConst11=0, @MethodConst12=False, @MethodConst13=0, @MethodConst14=0, @MethodConst15=0, @MethodConst16=0, @MethodConst17=0, @MethodConst18=0, @MethodConst19=0]
[2025-09-07 20:42:21.858 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `d`.`id` AS `Id` , `d`.`map_id` AS `MapId` , `c`.`map_name` AS `MapName` , IFNULL(`d`.`limit_growth`,@MethodConst10) AS `LimitGrowth` , IFNULL(`d`.`limit_level`,@MethodConst11) AS `LimitLevel` , IFNULL(CAST(`d`.`limit_key` as SIGNED),@MethodConst12) AS `LimitKey` , IFNULL(`d`.`min_gold`,@MethodConst13) AS `MinGold` , IFNULL(`d`.`max_gold`,@MethodConst14) AS `MaxGold` , IFNULL(`d`.`min_yuanbao`,@MethodConst15) AS `MinYuanbao` , IFNULL(`d`.`max_yuanbao`,@MethodConst16) AS `MaxYuanbao` , IFNULL(`d`.`min_drop`,@MethodConst17) AS `MinDrop` , IFNULL(`d`.`max_drop`,@MethodConst18) AS `MaxDrop` , `d`.`icon` AS `Icon` , IFNULL(`d`.`type`,@MethodConst19) AS `Type`  FROM `map_detail` `d` Left JOIN `map_config` `c` ON ( `d`.`map_id` = `c`.`map_id` )     ORDER BY `d`.`id` ASC LIMIT 0,10
[2025-09-07 20:42:26.509 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 20:42:26.511 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 20:42:26.513 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 20:42:26.560 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 20:42:26.561 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 20:42:26.564 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`map_id`,`map_name`,`map_desc`,`map_size`,`map_type`,`atlast_name`,`background`,`bgm`,`bgm_loop`,`bgm_volume`,`bgm_play`,`bgm_mute`,`bgm_pause`,`ico`,`type` FROM `map_config` ORDER BY `map_id` ASC 
[2025-09-07 20:42:26.611 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`map_id`,`map_name`,`map_desc`,`map_size`,`map_type`,`atlast_name`,`background`,`bgm`,`bgm_loop`,`bgm_volume`,`bgm_play`,`bgm_mute`,`bgm_pause`,`ico`,`type` FROM `map_config` ORDER BY `map_id` ASC 
[2025-09-07 20:42:26.676 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 20:42:26.677 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 20:42:26.678 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 20:42:26.725 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 20:42:26.726 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 20:42:26.728 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`map_id`,`map_name`,`map_desc`,`map_size`,`map_type`,`atlast_name`,`background`,`bgm`,`bgm_loop`,`bgm_volume`,`bgm_play`,`bgm_mute`,`bgm_pause`,`ico`,`type` FROM `map_config` ORDER BY `map_id` ASC 
[2025-09-07 20:42:26.775 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`map_id`,`map_name`,`map_desc`,`map_size`,`map_type`,`atlast_name`,`background`,`bgm`,`bgm_loop`,`bgm_volume`,`bgm_play`,`bgm_mute`,`bgm_pause`,`ico`,`type` FROM `map_config` ORDER BY `map_id` ASC 
[2025-09-07 20:42:26.805 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 20:42:26.810 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 20:42:26.812 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 20:42:26.860 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 20:42:26.863 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 20:42:26.866 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`monster_no`,`skill`,`name`,`attribute` FROM `monster_config` ORDER BY `monster_no` ASC 
[2025-09-07 20:42:26.917 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`monster_no`,`skill`,`name`,`attribute` FROM `monster_config` ORDER BY `monster_no` ASC 
[2025-09-07 20:42:26.934 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 20:42:26.939 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 20:42:26.942 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 20:42:26.989 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 20:42:26.993 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 20:42:26.997 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `map_monster`  
[2025-09-07 20:42:27.044 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `map_monster`  
[2025-09-07 20:42:27.049 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`map_id`,`monster_id`,`monster_name`,`growth`,`element`,`max_level`,`min_level`,`max_drop`,`exp`,`hp`,`mp`,`max_hp`,`max_mp`,`atk`,`def`,`dodge`,`spd`,`hit`,`deepen`,`offset`,`vamp`,`vamp_mp`,`skill_list`,`drop_items` FROM `map_monster`    ORDER BY `map_id` ASC,`monster_id` ASC LIMIT 0,10
[2025-09-07 20:42:27.096 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`map_id`,`monster_id`,`monster_name`,`growth`,`element`,`max_level`,`min_level`,`max_drop`,`exp`,`hp`,`mp`,`max_hp`,`max_mp`,`atk`,`def`,`dodge`,`spd`,`hit`,`deepen`,`offset`,`vamp`,`vamp_mp`,`skill_list`,`drop_items` FROM `map_monster`    ORDER BY `map_id` ASC,`monster_id` ASC LIMIT 0,10
[2025-09-07 20:42:27.109 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`map_id`,`map_name`,`map_desc`,`map_size`,`map_type`,`atlast_name`,`background`,`bgm`,`bgm_loop`,`bgm_volume`,`bgm_play`,`bgm_mute`,`bgm_pause`,`ico`,`type` FROM `map_config`  WHERE  (`map_id` IN (100,101,102,103,104,105,106,107,108,109))  
[2025-09-07 20:42:27.165 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`map_id`,`map_name`,`map_desc`,`map_size`,`map_type`,`atlast_name`,`background`,`bgm`,`bgm_loop`,`bgm_volume`,`bgm_play`,`bgm_mute`,`bgm_pause`,`ico`,`type` FROM `map_config`  WHERE  (`map_id` IN (100,101,102,103,104,105,106,107,108,109))  
[2025-09-07 20:43:19.669 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 20:43:19.748 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 20:43:19.754 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 20:43:19.801 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 20:43:19.803 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 20:43:19.805 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`map_id`,`map_name`,`map_desc`,`map_size`,`map_type`,`atlast_name`,`background`,`bgm`,`bgm_loop`,`bgm_volume`,`bgm_play`,`bgm_mute`,`bgm_pause`,`ico`,`type` FROM `map_config` ORDER BY `map_id` ASC 
[2025-09-07 20:43:19.851 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`map_id`,`map_name`,`map_desc`,`map_size`,`map_type`,`atlast_name`,`background`,`bgm`,`bgm_loop`,`bgm_volume`,`bgm_play`,`bgm_mute`,`bgm_pause`,`ico`,`type` FROM `map_config` ORDER BY `map_id` ASC 
[2025-09-07 20:43:19.895 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 20:43:19.936 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 20:43:19.939 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 20:43:19.986 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 20:43:19.992 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 20:43:19.995 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`map_id`,`map_name`,`map_desc`,`map_size`,`map_type`,`atlast_name`,`background`,`bgm`,`bgm_loop`,`bgm_volume`,`bgm_play`,`bgm_mute`,`bgm_pause`,`ico`,`type` FROM `map_config` ORDER BY `map_id` ASC 
[2025-09-07 20:43:20.043 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`map_id`,`map_name`,`map_desc`,`map_size`,`map_type`,`atlast_name`,`background`,`bgm`,`bgm_loop`,`bgm_volume`,`bgm_play`,`bgm_mute`,`bgm_pause`,`ico`,`type` FROM `map_config` ORDER BY `map_id` ASC 
[2025-09-07 20:43:20.056 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 20:43:20.060 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 20:43:20.062 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 20:43:20.107 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 20:43:20.110 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 20:43:20.114 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`monster_no`,`skill`,`name`,`attribute` FROM `monster_config` ORDER BY `monster_no` ASC 
[2025-09-07 20:43:20.163 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`monster_no`,`skill`,`name`,`attribute` FROM `monster_config` ORDER BY `monster_no` ASC 
[2025-09-07 20:43:20.178 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 20:43:20.180 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 20:43:20.183 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 20:43:20.230 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 20:43:20.232 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 20:43:20.233 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `map_monster`  
[2025-09-07 20:43:20.280 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `map_monster`  
[2025-09-07 20:43:20.297 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`map_id`,`monster_id`,`monster_name`,`growth`,`element`,`max_level`,`min_level`,`max_drop`,`exp`,`hp`,`mp`,`max_hp`,`max_mp`,`atk`,`def`,`dodge`,`spd`,`hit`,`deepen`,`offset`,`vamp`,`vamp_mp`,`skill_list`,`drop_items` FROM `map_monster`    ORDER BY `map_id` ASC,`monster_id` ASC LIMIT 0,10
[2025-09-07 20:43:20.344 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`map_id`,`monster_id`,`monster_name`,`growth`,`element`,`max_level`,`min_level`,`max_drop`,`exp`,`hp`,`mp`,`max_hp`,`max_mp`,`atk`,`def`,`dodge`,`spd`,`hit`,`deepen`,`offset`,`vamp`,`vamp_mp`,`skill_list`,`drop_items` FROM `map_monster`    ORDER BY `map_id` ASC,`monster_id` ASC LIMIT 0,10
[2025-09-07 20:43:20.347 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`map_id`,`map_name`,`map_desc`,`map_size`,`map_type`,`atlast_name`,`background`,`bgm`,`bgm_loop`,`bgm_volume`,`bgm_play`,`bgm_mute`,`bgm_pause`,`ico`,`type` FROM `map_config`  WHERE  (`map_id` IN (100,101,102,103,104,105,106,107,108,109))  
[2025-09-07 20:43:20.391 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`map_id`,`map_name`,`map_desc`,`map_size`,`map_type`,`atlast_name`,`background`,`bgm`,`bgm_loop`,`bgm_volume`,`bgm_play`,`bgm_mute`,`bgm_pause`,`ico`,`type` FROM `map_config`  WHERE  (`map_id` IN (100,101,102,103,104,105,106,107,108,109))  
[2025-09-07 20:44:27.379 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 20:44:27.380 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 20:44:27.382 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 20:44:27.434 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 20:44:27.435 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 20:44:27.500 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 20:44:27.502 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 20:44:27.505 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 20:44:27.552 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 20:44:27.554 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 20:44:27.569 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `monster_config`  
[2025-09-07 20:44:27.617 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `monster_config`  
[2025-09-07 20:44:27.619 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`monster_no`,`skill`,`name`,`attribute` FROM `monster_config`    ORDER BY `monster_no` ASC LIMIT 0,10
[2025-09-07 20:44:27.665 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`monster_no`,`skill`,`name`,`attribute` FROM `monster_config`    ORDER BY `monster_no` ASC LIMIT 0,10
[2025-09-07 20:44:33.176 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 20:44:33.178 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 20:44:33.182 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 20:44:33.244 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 20:44:33.253 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 20:44:33.264 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  DISTINCT `type` FROM `item_config`  WHERE NOT( `type` IS NULL   OR `type`='') 
[2025-09-07 20:44:33.326 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  DISTINCT `type` FROM `item_config`  WHERE NOT( `type` IS NULL   OR `type`='') 
[2025-09-07 20:44:33.335 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  DISTINCT `quality` FROM `item_config`  WHERE NOT( `quality` IS NULL   OR `quality`='') 
[2025-09-07 20:44:33.386 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  DISTINCT `quality` FROM `item_config`  WHERE NOT( `quality` IS NULL   OR `quality`='') 
[2025-09-07 20:44:33.459 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 20:44:33.460 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 20:44:33.463 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 20:44:33.516 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 20:44:33.517 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 20:44:33.545 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-09-07 20:44:33.775 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-09-07 20:44:33.778 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 0,10
[2025-09-07 20:44:34.574 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 0,10
[2025-09-07 20:47:00.998 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 20:47:01.001 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 20:47:01.003 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 20:47:01.483 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 20:47:01.488 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 20:47:01.490 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `equip_type_id` AS `EquipTypeId` , `type_name` AS `TypeName`  FROM `equipment_type` ORDER BY `id` ASC 
[2025-09-07 20:47:01.556 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `equip_type_id` AS `EquipTypeId` , `type_name` AS `TypeName`  FROM `equipment_type` ORDER BY `id` ASC 
[2025-09-07 20:47:01.618 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 20:47:01.619 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 20:47:01.621 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 20:47:01.656 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 20:47:01.659 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 20:47:01.666 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  
[2025-09-07 20:47:01.701 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  
[2025-09-07 20:47:01.714 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment_detail`  
[2025-09-07 20:47:01.753 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment_detail`  
[2025-09-07 20:47:01.756 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment_type`  
[2025-09-07 20:47:01.791 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment_type`  
[2025-09-07 20:47:01.807 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )   
[2025-09-07 20:47:01.850 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )   
[2025-09-07 20:47:01.855 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `e`.`equip_id` AS `EquipId` , @constant14 AS `ClassId` , `e`.`name` AS `Name` , `e`.`icon` AS `Icon` , `e`.`equip_type_id` AS `EquipTypeId` , `et`.`type_name` AS `EquipTypeName` , `e`.`element` AS `Element` , IFNULL(`e`.`slot`,@MethodConst15) AS `Slot` , IFNULL(`e`.`strengthen_level`,@MethodConst16) AS `StrengthenLevel` , `e`.`suit_id` AS `SuitId` , IFNULL(`ed`.`atk`,@MethodConst17) AS `Atk` , IFNULL(`ed`.`hit`,@MethodConst18) AS `Hit` , IFNULL(`ed`.`def`,@MethodConst19) AS `Def` , IFNULL(`ed`.`spd`,@MethodConst20) AS `Spd` , IFNULL(`ed`.`dodge`,@MethodConst21) AS `Dodge` , IFNULL(`ed`.`hp`,@MethodConst22) AS `Hp` , IFNULL(`ed`.`mp`,@MethodConst23) AS `Mp` , IFNULL(`ed`.`deepen`,@MethodConst24) AS `Deepen` , IFNULL(`ed`.`offset`,@MethodConst25) AS `Offset` , IFNULL(`ed`.`vamp`,@MethodConst26) AS `Vamp` , IFNULL(`ed`.`vamp_mp`,@MethodConst27) AS `VampMp` , `ed`.`description` AS `Description` , `ed`.`main_attr` AS `MainAttr` , `ed`.`main_attr_value` AS `MainAttrValue` , `ed`.`sub_attr` AS `SubAttr` , `ed`.`sub_attr_value` AS `SubAttrValue` , `ed`.`element_limit` AS `ElementLimit` , `ed`.`equip_name` AS `EquipName`  FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )     ORDER BY `e`.`equip_id` ASC LIMIT 0,10 | 参数: [@constant0=, @MethodConst1=0, @MethodConst2=0, @MethodConst3=0, @MethodConst4=0, @MethodConst5=0, @MethodConst6=0, @MethodConst7=0, @MethodConst8=0, @MethodConst9=0, @MethodConst10=0, @MethodConst11=0, @MethodConst12=0, @MethodConst13=0, @constant14=, @MethodConst15=0, @MethodConst16=0, @MethodConst17=0, @MethodConst18=0, @MethodConst19=0, @MethodConst20=0, @MethodConst21=0, @MethodConst22=0, @MethodConst23=0, @MethodConst24=0, @MethodConst25=0, @MethodConst26=0, @MethodConst27=0]
[2025-09-07 20:47:01.908 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `e`.`equip_id` AS `EquipId` , @constant14 AS `ClassId` , `e`.`name` AS `Name` , `e`.`icon` AS `Icon` , `e`.`equip_type_id` AS `EquipTypeId` , `et`.`type_name` AS `EquipTypeName` , `e`.`element` AS `Element` , IFNULL(`e`.`slot`,@MethodConst15) AS `Slot` , IFNULL(`e`.`strengthen_level`,@MethodConst16) AS `StrengthenLevel` , `e`.`suit_id` AS `SuitId` , IFNULL(`ed`.`atk`,@MethodConst17) AS `Atk` , IFNULL(`ed`.`hit`,@MethodConst18) AS `Hit` , IFNULL(`ed`.`def`,@MethodConst19) AS `Def` , IFNULL(`ed`.`spd`,@MethodConst20) AS `Spd` , IFNULL(`ed`.`dodge`,@MethodConst21) AS `Dodge` , IFNULL(`ed`.`hp`,@MethodConst22) AS `Hp` , IFNULL(`ed`.`mp`,@MethodConst23) AS `Mp` , IFNULL(`ed`.`deepen`,@MethodConst24) AS `Deepen` , IFNULL(`ed`.`offset`,@MethodConst25) AS `Offset` , IFNULL(`ed`.`vamp`,@MethodConst26) AS `Vamp` , IFNULL(`ed`.`vamp_mp`,@MethodConst27) AS `VampMp` , `ed`.`description` AS `Description` , `ed`.`main_attr` AS `MainAttr` , `ed`.`main_attr_value` AS `MainAttrValue` , `ed`.`sub_attr` AS `SubAttr` , `ed`.`sub_attr_value` AS `SubAttrValue` , `ed`.`element_limit` AS `ElementLimit` , `ed`.`equip_name` AS `EquipName`  FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )     ORDER BY `e`.`equip_id` ASC LIMIT 0,10
[2025-09-07 20:47:01.929 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 20:47:01.931 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 20:47:01.934 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 20:47:01.968 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 20:47:01.970 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 20:47:01.972 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment_type`  
[2025-09-07 20:47:02.006 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment_type`  
[2025-09-07 20:47:02.009 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `id` AS `Id` , `equip_type_id` AS `EquipTypeId` , `type_name` AS `TypeName` , @constant6 AS `Description` , @constant7 AS `SortOrder` , @constant8 AS `IsActive` , @constant9 AS `CreateTime` , @constant10 AS `UpdateTime` , @constant11 AS `EquipmentCount`  FROM `equipment_type`    ORDER BY `id` ASC LIMIT 0,10 | 参数: [@constant0=, @constant1=0, @constant2=True, @constant3=, @constant4=, @constant5=0, @constant6=, @constant7=0, @constant8=True, @constant9=, @constant10=, @constant11=0]
[2025-09-07 20:47:02.045 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `id` AS `Id` , `equip_type_id` AS `EquipTypeId` , `type_name` AS `TypeName` , @constant6 AS `Description` , @constant7 AS `SortOrder` , @constant8 AS `IsActive` , @constant9 AS `CreateTime` , @constant10 AS `UpdateTime` , @constant11 AS `EquipmentCount`  FROM `equipment_type`    ORDER BY `id` ASC LIMIT 0,10
[2025-09-07 20:47:02.050 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=1]
[2025-09-07 20:47:02.084 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-09-07 20:47:02.087 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=2]
[2025-09-07 20:47:02.119 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-09-07 20:47:02.122 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=3]
[2025-09-07 20:47:02.156 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-09-07 20:47:02.159 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=4]
[2025-09-07 20:47:02.193 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-09-07 20:47:02.196 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=5]
[2025-09-07 20:47:02.233 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-09-07 20:47:02.239 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=6]
[2025-09-07 20:47:02.274 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-09-07 20:47:02.276 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=7]
[2025-09-07 20:47:02.310 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-09-07 20:47:02.312 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=8]
[2025-09-07 20:47:02.347 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-09-07 20:47:02.358 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=9]
[2025-09-07 20:47:02.393 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-09-07 20:47:02.396 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=10]
[2025-09-07 20:47:02.437 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-09-07 20:47:12.586 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 20:47:12.591 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 20:47:12.596 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 20:47:12.638 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 20:47:12.641 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 20:47:12.643 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`map_id`,`map_name`,`map_desc`,`map_size`,`map_type`,`atlast_name`,`background`,`bgm`,`bgm_loop`,`bgm_volume`,`bgm_play`,`bgm_mute`,`bgm_pause`,`ico`,`type` FROM `map_config` ORDER BY `map_id` ASC 
[2025-09-07 20:47:12.684 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`map_id`,`map_name`,`map_desc`,`map_size`,`map_type`,`atlast_name`,`background`,`bgm`,`bgm_loop`,`bgm_volume`,`bgm_play`,`bgm_mute`,`bgm_pause`,`ico`,`type` FROM `map_config` ORDER BY `map_id` ASC 
[2025-09-07 20:47:12.736 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 20:47:12.738 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 20:47:12.738 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 20:47:12.739 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 20:47:12.741 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 20:47:12.743 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 20:47:12.777 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 20:47:12.780 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 20:47:12.783 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`map_id`,`map_name`,`map_desc`,`map_size`,`map_type`,`atlast_name`,`background`,`bgm`,`bgm_loop`,`bgm_volume`,`bgm_play`,`bgm_mute`,`bgm_pause`,`ico`,`type` FROM `map_config` ORDER BY `map_id` ASC 
[2025-09-07 20:47:12.822 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`map_id`,`map_name`,`map_desc`,`map_size`,`map_type`,`atlast_name`,`background`,`bgm`,`bgm_loop`,`bgm_volume`,`bgm_play`,`bgm_mute`,`bgm_pause`,`ico`,`type` FROM `map_config` ORDER BY `map_id` ASC 
[2025-09-07 20:47:12.971 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 20:47:12.999 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 20:47:13.043 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM `map_detail` `d` Left JOIN `map_config` `c` ON ( `d`.`map_id` = `c`.`map_id` )    | 参数: [@MethodConst0=0, @MethodConst1=0, @MethodConst2=False, @MethodConst3=0, @MethodConst4=0, @MethodConst5=0, @MethodConst6=0, @MethodConst7=0, @MethodConst8=0, @MethodConst9=0]
[2025-09-07 20:47:13.105 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM `map_detail` `d` Left JOIN `map_config` `c` ON ( `d`.`map_id` = `c`.`map_id` )   
[2025-09-07 20:47:13.114 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `d`.`id` AS `Id` , `d`.`map_id` AS `MapId` , `c`.`map_name` AS `MapName` , IFNULL(`d`.`limit_growth`,@MethodConst10) AS `LimitGrowth` , IFNULL(`d`.`limit_level`,@MethodConst11) AS `LimitLevel` , IFNULL(CAST(`d`.`limit_key` as SIGNED),@MethodConst12) AS `LimitKey` , IFNULL(`d`.`min_gold`,@MethodConst13) AS `MinGold` , IFNULL(`d`.`max_gold`,@MethodConst14) AS `MaxGold` , IFNULL(`d`.`min_yuanbao`,@MethodConst15) AS `MinYuanbao` , IFNULL(`d`.`max_yuanbao`,@MethodConst16) AS `MaxYuanbao` , IFNULL(`d`.`min_drop`,@MethodConst17) AS `MinDrop` , IFNULL(`d`.`max_drop`,@MethodConst18) AS `MaxDrop` , `d`.`icon` AS `Icon` , IFNULL(`d`.`type`,@MethodConst19) AS `Type`  FROM `map_detail` `d` Left JOIN `map_config` `c` ON ( `d`.`map_id` = `c`.`map_id` )     ORDER BY `d`.`id` ASC LIMIT 0,10 | 参数: [@MethodConst0=0, @MethodConst1=0, @MethodConst2=False, @MethodConst3=0, @MethodConst4=0, @MethodConst5=0, @MethodConst6=0, @MethodConst7=0, @MethodConst8=0, @MethodConst9=0, @MethodConst10=0, @MethodConst11=0, @MethodConst12=False, @MethodConst13=0, @MethodConst14=0, @MethodConst15=0, @MethodConst16=0, @MethodConst17=0, @MethodConst18=0, @MethodConst19=0]
[2025-09-07 20:47:13.155 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `d`.`id` AS `Id` , `d`.`map_id` AS `MapId` , `c`.`map_name` AS `MapName` , IFNULL(`d`.`limit_growth`,@MethodConst10) AS `LimitGrowth` , IFNULL(`d`.`limit_level`,@MethodConst11) AS `LimitLevel` , IFNULL(CAST(`d`.`limit_key` as SIGNED),@MethodConst12) AS `LimitKey` , IFNULL(`d`.`min_gold`,@MethodConst13) AS `MinGold` , IFNULL(`d`.`max_gold`,@MethodConst14) AS `MaxGold` , IFNULL(`d`.`min_yuanbao`,@MethodConst15) AS `MinYuanbao` , IFNULL(`d`.`max_yuanbao`,@MethodConst16) AS `MaxYuanbao` , IFNULL(`d`.`min_drop`,@MethodConst17) AS `MinDrop` , IFNULL(`d`.`max_drop`,@MethodConst18) AS `MaxDrop` , `d`.`icon` AS `Icon` , IFNULL(`d`.`type`,@MethodConst19) AS `Type`  FROM `map_detail` `d` Left JOIN `map_config` `c` ON ( `d`.`map_id` = `c`.`map_id` )     ORDER BY `d`.`id` ASC LIMIT 0,10
[2025-09-07 20:53:26.674 +08:00 INF] BMS: 🚀 应用程序启动，日志系统已配置
[2025-09-07 20:53:26.708 +08:00 DBG] BMS: 🔧 调试级别日志测试
[2025-09-07 20:53:26.725 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 20:53:26.730 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 20:53:26.814 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 20:53:27.371 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 20:53:27.381 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 20:53:27.383 +08:00 INF] BMS: ✅ DbContext 初始化成功，日志文件位置: logs/
[2025-09-07 20:53:28.542 +08:00 WRN] Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware: Failed to determine the https port for redirect.
[2025-09-07 20:53:33.253 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 20:53:33.255 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 20:53:33.261 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 20:53:33.303 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 20:53:33.304 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 20:53:33.334 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`map_id`,`map_name`,`map_desc`,`map_size`,`map_type`,`atlast_name`,`background`,`bgm`,`bgm_loop`,`bgm_volume`,`bgm_play`,`bgm_mute`,`bgm_pause`,`ico`,`type` FROM `map_config` ORDER BY `map_id` ASC 
[2025-09-07 20:53:33.380 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`map_id`,`map_name`,`map_desc`,`map_size`,`map_type`,`atlast_name`,`background`,`bgm`,`bgm_loop`,`bgm_volume`,`bgm_play`,`bgm_mute`,`bgm_pause`,`ico`,`type` FROM `map_config` ORDER BY `map_id` ASC 
[2025-09-07 20:53:33.509 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 20:53:33.511 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 20:53:33.513 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 20:53:33.554 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 20:53:33.588 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 20:53:33.591 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`map_id`,`map_name`,`map_desc`,`map_size`,`map_type`,`atlast_name`,`background`,`bgm`,`bgm_loop`,`bgm_volume`,`bgm_play`,`bgm_mute`,`bgm_pause`,`ico`,`type` FROM `map_config` ORDER BY `map_id` ASC 
[2025-09-07 20:53:33.637 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`map_id`,`map_name`,`map_desc`,`map_size`,`map_type`,`atlast_name`,`background`,`bgm`,`bgm_loop`,`bgm_volume`,`bgm_play`,`bgm_mute`,`bgm_pause`,`ico`,`type` FROM `map_config` ORDER BY `map_id` ASC 
[2025-09-07 20:53:33.660 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 20:53:33.662 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 20:53:33.666 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 20:53:33.714 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 20:53:33.716 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 20:53:33.719 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`monster_no`,`skill`,`name`,`attribute` FROM `monster_config` ORDER BY `monster_no` ASC 
[2025-09-07 20:53:33.759 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`monster_no`,`skill`,`name`,`attribute` FROM `monster_config` ORDER BY `monster_no` ASC 
[2025-09-07 20:53:33.785 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 20:53:33.787 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 20:53:33.789 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 20:53:33.828 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 20:53:33.830 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 20:53:33.919 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `map_monster`  
[2025-09-07 20:53:33.958 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `map_monster`  
[2025-09-07 20:53:33.967 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`map_id`,`monster_id`,`monster_name`,`growth`,`element`,`max_level`,`min_level`,`max_drop`,`exp`,`hp`,`mp`,`max_hp`,`max_mp`,`atk`,`def`,`dodge`,`spd`,`hit`,`deepen`,`offset`,`vamp`,`vamp_mp`,`skill_list`,`drop_items` FROM `map_monster`    ORDER BY `map_id` ASC,`monster_id` ASC LIMIT 0,10
[2025-09-07 20:53:34.007 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`map_id`,`monster_id`,`monster_name`,`growth`,`element`,`max_level`,`min_level`,`max_drop`,`exp`,`hp`,`mp`,`max_hp`,`max_mp`,`atk`,`def`,`dodge`,`spd`,`hit`,`deepen`,`offset`,`vamp`,`vamp_mp`,`skill_list`,`drop_items` FROM `map_monster`    ORDER BY `map_id` ASC,`monster_id` ASC LIMIT 0,10
[2025-09-07 20:53:34.036 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`map_id`,`map_name`,`map_desc`,`map_size`,`map_type`,`atlast_name`,`background`,`bgm`,`bgm_loop`,`bgm_volume`,`bgm_play`,`bgm_mute`,`bgm_pause`,`ico`,`type` FROM `map_config`  WHERE  (`map_id` IN (100,101,102,103,104,105,106,107,108,109))  
[2025-09-07 20:53:34.082 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`map_id`,`map_name`,`map_desc`,`map_size`,`map_type`,`atlast_name`,`background`,`bgm`,`bgm_loop`,`bgm_volume`,`bgm_play`,`bgm_mute`,`bgm_pause`,`ico`,`type` FROM `map_config`  WHERE  (`map_id` IN (100,101,102,103,104,105,106,107,108,109))  
[2025-09-07 21:03:01.221 +08:00 INF] BMS: 🚀 应用程序启动，日志系统已配置
[2025-09-07 21:03:01.263 +08:00 DBG] BMS: 🔧 调试级别日志测试
[2025-09-07 21:03:01.299 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 21:03:01.308 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 21:03:01.393 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 21:03:02.015 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 21:03:02.032 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 21:03:02.034 +08:00 INF] BMS: ✅ DbContext 初始化成功，日志文件位置: logs/
[2025-09-07 21:03:03.997 +08:00 WRN] Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware: Failed to determine the https port for redirect.
[2025-09-07 21:03:11.659 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 21:03:11.672 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 21:03:11.685 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 21:03:11.790 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 21:03:11.973 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 21:03:12.018 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`map_id`,`map_name`,`map_desc`,`map_size`,`map_type`,`atlast_name`,`background`,`bgm`,`bgm_loop`,`bgm_volume`,`bgm_play`,`bgm_mute`,`bgm_pause`,`ico`,`type` FROM `map_config` ORDER BY `map_id` ASC 
[2025-09-07 21:03:12.113 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`map_id`,`map_name`,`map_desc`,`map_size`,`map_type`,`atlast_name`,`background`,`bgm`,`bgm_loop`,`bgm_volume`,`bgm_play`,`bgm_mute`,`bgm_pause`,`ico`,`type` FROM `map_config` ORDER BY `map_id` ASC 
[2025-09-07 21:03:12.365 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 21:03:12.369 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 21:03:12.372 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 21:03:12.403 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 21:03:12.410 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 21:03:12.414 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`map_id`,`map_name`,`map_desc`,`map_size`,`map_type`,`atlast_name`,`background`,`bgm`,`bgm_loop`,`bgm_volume`,`bgm_play`,`bgm_mute`,`bgm_pause`,`ico`,`type` FROM `map_config` ORDER BY `map_id` ASC 
[2025-09-07 21:03:12.440 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`map_id`,`map_name`,`map_desc`,`map_size`,`map_type`,`atlast_name`,`background`,`bgm`,`bgm_loop`,`bgm_volume`,`bgm_play`,`bgm_mute`,`bgm_pause`,`ico`,`type` FROM `map_config` ORDER BY `map_id` ASC 
[2025-09-07 21:03:12.463 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 21:03:12.465 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 21:03:12.466 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 21:03:12.494 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 21:03:12.496 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 21:03:12.499 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`monster_no`,`skill`,`name`,`attribute` FROM `monster_config` ORDER BY `monster_no` ASC 
[2025-09-07 21:03:12.533 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`monster_no`,`skill`,`name`,`attribute` FROM `monster_config` ORDER BY `monster_no` ASC 
[2025-09-07 21:03:12.552 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 21:03:12.553 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 21:03:12.556 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 21:03:12.583 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 21:03:12.585 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 21:03:12.703 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `map_monster`  
[2025-09-07 21:03:12.729 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `map_monster`  
[2025-09-07 21:03:12.738 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`map_id`,`monster_id`,`monster_name`,`growth`,`element`,`max_level`,`min_level`,`max_drop`,`exp`,`hp`,`mp`,`max_hp`,`max_mp`,`atk`,`def`,`dodge`,`spd`,`hit`,`deepen`,`offset`,`vamp`,`vamp_mp`,`skill_list`,`drop_items` FROM `map_monster`    ORDER BY `map_id` ASC,`monster_id` ASC LIMIT 0,10
[2025-09-07 21:03:12.764 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`map_id`,`monster_id`,`monster_name`,`growth`,`element`,`max_level`,`min_level`,`max_drop`,`exp`,`hp`,`mp`,`max_hp`,`max_mp`,`atk`,`def`,`dodge`,`spd`,`hit`,`deepen`,`offset`,`vamp`,`vamp_mp`,`skill_list`,`drop_items` FROM `map_monster`    ORDER BY `map_id` ASC,`monster_id` ASC LIMIT 0,10
[2025-09-07 21:03:12.791 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`map_id`,`map_name`,`map_desc`,`map_size`,`map_type`,`atlast_name`,`background`,`bgm`,`bgm_loop`,`bgm_volume`,`bgm_play`,`bgm_mute`,`bgm_pause`,`ico`,`type` FROM `map_config`  WHERE  (`map_id` IN (100,101,102,103,104,105,106,107,108,109))  
[2025-09-07 21:03:12.819 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`map_id`,`map_name`,`map_desc`,`map_size`,`map_type`,`atlast_name`,`background`,`bgm`,`bgm_loop`,`bgm_volume`,`bgm_play`,`bgm_mute`,`bgm_pause`,`ico`,`type` FROM `map_config`  WHERE  (`map_id` IN (100,101,102,103,104,105,106,107,108,109))  
[2025-09-07 21:04:22.435 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 21:04:22.535 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 21:04:22.597 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 21:04:22.683 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 21:04:22.715 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 21:04:22.766 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT 1 FROM `map_monster`   WHERE (( `map_id` = @map_id0 ) AND ( `monster_id` = @monster_id1 ))   LIMIT 0,1 | 参数: [@map_id0=100, @monster_id1=111]
[2025-09-07 21:04:22.834 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT 1 FROM `map_monster`   WHERE (( `map_id` = @map_id0 ) AND ( `monster_id` = @monster_id1 ))   LIMIT 0,1
[2025-09-07 21:04:22.886 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: INSERT INTO `map_monster`  
           (`map_id`,`monster_id`,`monster_name`,`growth`,`element`,`max_level`,`min_level`,`max_drop`,`exp`,`hp`,`mp`,`max_hp`,`max_mp`,`atk`,`def`,`dodge`,`spd`,`hit`,`deepen`,`offset`,`vamp`,`vamp_mp`,`skill_list`,`drop_items`)
     VALUES
           (@map_id,@monster_id,@monster_name,@growth,@element,@max_level,@min_level,@max_drop,@exp,@hp,@mp,@max_hp,@max_mp,@atk,@def,@dodge,@spd,@hit,@deepen,@offset,@vamp,@vamp_mp,@skill_list,@drop_items) ;SELECT LAST_INSERT_ID(); | 参数: [@map_id=100, @monster_id=111, @monster_name=§龙蛇玄武§, @growth=10, @element=金, @max_level=10, @min_level=10, @max_drop=10, @exp=10, @hp=10, @mp=10, @max_hp=10, @max_mp=10, @atk=10, @def=10, @dodge=10, @spd=10, @hit=, @deepen=, @offset=, @vamp=, @vamp_mp=, @skill_list=, @drop_items=]
[2025-09-07 21:04:23.001 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: INSERT INTO `map_monster`  
           (`map_id`,`monster_id`,`monster_name`,`growth`,`element`,`max_level`,`min_level`,`max_drop`,`exp`,`hp`,`mp`,`max_hp`,`max_mp`,`atk`,`def`,`dodge`,`spd`,`hit`,`deepen`,`offset`,`vamp`,`vamp_mp`,`skill_list`,`drop_items`)
     VALUES
           (@map_id,@monster_id,@monster_name,@growth,@element,@max_level,@min_level,@max_drop,@exp,@hp,@mp,@max_hp,@max_mp,@atk,@def,@dodge,@spd,@hit,@deepen,@offset,@vamp,@vamp_mp,@skill_list,@drop_items) ;SELECT LAST_INSERT_ID();
[2025-09-07 21:04:24.609 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 21:04:24.611 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 21:04:24.613 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 21:04:24.641 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 21:04:24.642 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 21:04:24.644 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `map_monster`  
[2025-09-07 21:04:24.670 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `map_monster`  
[2025-09-07 21:04:24.672 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`map_id`,`monster_id`,`monster_name`,`growth`,`element`,`max_level`,`min_level`,`max_drop`,`exp`,`hp`,`mp`,`max_hp`,`max_mp`,`atk`,`def`,`dodge`,`spd`,`hit`,`deepen`,`offset`,`vamp`,`vamp_mp`,`skill_list`,`drop_items` FROM `map_monster`    ORDER BY `map_id` ASC,`monster_id` ASC LIMIT 0,10
[2025-09-07 21:04:24.700 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`map_id`,`monster_id`,`monster_name`,`growth`,`element`,`max_level`,`min_level`,`max_drop`,`exp`,`hp`,`mp`,`max_hp`,`max_mp`,`atk`,`def`,`dodge`,`spd`,`hit`,`deepen`,`offset`,`vamp`,`vamp_mp`,`skill_list`,`drop_items` FROM `map_monster`    ORDER BY `map_id` ASC,`monster_id` ASC LIMIT 0,10
[2025-09-07 21:04:24.704 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`map_id`,`map_name`,`map_desc`,`map_size`,`map_type`,`atlast_name`,`background`,`bgm`,`bgm_loop`,`bgm_volume`,`bgm_play`,`bgm_mute`,`bgm_pause`,`ico`,`type` FROM `map_config`  WHERE  (`map_id` IN (100,101,102,103,104,105,106,107,108))  
[2025-09-07 21:04:24.729 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`map_id`,`map_name`,`map_desc`,`map_size`,`map_type`,`atlast_name`,`background`,`bgm`,`bgm_loop`,`bgm_volume`,`bgm_play`,`bgm_mute`,`bgm_pause`,`ico`,`type` FROM `map_config`  WHERE  (`map_id` IN (100,101,102,103,104,105,106,107,108))  
[2025-09-07 21:06:59.745 +08:00 INF] BMS: 🚀 应用程序启动，日志系统已配置
[2025-09-07 21:06:59.772 +08:00 DBG] BMS: 🔧 调试级别日志测试
[2025-09-07 21:06:59.787 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 21:06:59.789 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 21:06:59.870 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 21:07:00.404 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 21:07:00.591 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 21:07:00.710 +08:00 INF] BMS: ✅ DbContext 初始化成功，日志文件位置: logs/
[2025-09-07 21:07:01.579 +08:00 WRN] Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware: Failed to determine the https port for redirect.
[2025-09-07 21:07:09.560 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 21:07:09.575 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 21:07:09.600 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 21:07:09.645 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 21:07:09.650 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 21:07:09.701 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  DISTINCT `type` FROM `item_config`  WHERE NOT( `type` IS NULL   OR `type`='') 
[2025-09-07 21:07:10.145 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  DISTINCT `type` FROM `item_config`  WHERE NOT( `type` IS NULL   OR `type`='') 
[2025-09-07 21:07:10.259 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  DISTINCT `quality` FROM `item_config`  WHERE NOT( `quality` IS NULL   OR `quality`='') 
[2025-09-07 21:07:10.337 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  DISTINCT `quality` FROM `item_config`  WHERE NOT( `quality` IS NULL   OR `quality`='') 
[2025-09-07 21:07:10.435 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 21:07:10.438 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 21:07:10.440 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 21:07:10.485 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 21:07:10.489 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 21:07:10.615 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-09-07 21:07:10.828 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-09-07 21:07:10.830 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 0,10
[2025-09-07 21:07:11.559 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 0,10
[2025-09-07 21:07:17.159 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 21:07:17.161 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 21:07:17.163 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 21:07:17.204 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 21:07:17.206 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 21:07:17.212 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `equip_type_id` AS `EquipTypeId` , `type_name` AS `TypeName`  FROM `equipment_type` ORDER BY `id` ASC 
[2025-09-07 21:07:17.254 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `equip_type_id` AS `EquipTypeId` , `type_name` AS `TypeName`  FROM `equipment_type` ORDER BY `id` ASC 
[2025-09-07 21:07:17.337 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 21:07:17.340 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 21:07:17.342 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 21:07:17.386 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 21:07:17.388 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 21:07:17.404 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  
[2025-09-07 21:07:17.446 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  
[2025-09-07 21:07:17.457 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment_detail`  
[2025-09-07 21:07:17.501 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment_detail`  
[2025-09-07 21:07:17.511 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment_type`  
[2025-09-07 21:07:17.562 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment_type`  
[2025-09-07 21:07:17.573 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )   
[2025-09-07 21:07:17.624 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )   
[2025-09-07 21:07:17.634 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `e`.`equip_id` AS `EquipId` , @constant14 AS `ClassId` , `e`.`name` AS `Name` , `e`.`icon` AS `Icon` , `e`.`equip_type_id` AS `EquipTypeId` , `et`.`type_name` AS `EquipTypeName` , `e`.`element` AS `Element` , IFNULL(`e`.`slot`,@MethodConst15) AS `Slot` , IFNULL(`e`.`strengthen_level`,@MethodConst16) AS `StrengthenLevel` , `e`.`suit_id` AS `SuitId` , IFNULL(`ed`.`atk`,@MethodConst17) AS `Atk` , IFNULL(`ed`.`hit`,@MethodConst18) AS `Hit` , IFNULL(`ed`.`def`,@MethodConst19) AS `Def` , IFNULL(`ed`.`spd`,@MethodConst20) AS `Spd` , IFNULL(`ed`.`dodge`,@MethodConst21) AS `Dodge` , IFNULL(`ed`.`hp`,@MethodConst22) AS `Hp` , IFNULL(`ed`.`mp`,@MethodConst23) AS `Mp` , IFNULL(`ed`.`deepen`,@MethodConst24) AS `Deepen` , IFNULL(`ed`.`offset`,@MethodConst25) AS `Offset` , IFNULL(`ed`.`vamp`,@MethodConst26) AS `Vamp` , IFNULL(`ed`.`vamp_mp`,@MethodConst27) AS `VampMp` , `ed`.`description` AS `Description` , `ed`.`main_attr` AS `MainAttr` , `ed`.`main_attr_value` AS `MainAttrValue` , `ed`.`sub_attr` AS `SubAttr` , `ed`.`sub_attr_value` AS `SubAttrValue` , `ed`.`element_limit` AS `ElementLimit` , `ed`.`equip_name` AS `EquipName`  FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )     ORDER BY `e`.`equip_id` ASC LIMIT 0,10 | 参数: [@constant0=, @MethodConst1=0, @MethodConst2=0, @MethodConst3=0, @MethodConst4=0, @MethodConst5=0, @MethodConst6=0, @MethodConst7=0, @MethodConst8=0, @MethodConst9=0, @MethodConst10=0, @MethodConst11=0, @MethodConst12=0, @MethodConst13=0, @constant14=, @MethodConst15=0, @MethodConst16=0, @MethodConst17=0, @MethodConst18=0, @MethodConst19=0, @MethodConst20=0, @MethodConst21=0, @MethodConst22=0, @MethodConst23=0, @MethodConst24=0, @MethodConst25=0, @MethodConst26=0, @MethodConst27=0]
[2025-09-07 21:07:17.703 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `e`.`equip_id` AS `EquipId` , @constant14 AS `ClassId` , `e`.`name` AS `Name` , `e`.`icon` AS `Icon` , `e`.`equip_type_id` AS `EquipTypeId` , `et`.`type_name` AS `EquipTypeName` , `e`.`element` AS `Element` , IFNULL(`e`.`slot`,@MethodConst15) AS `Slot` , IFNULL(`e`.`strengthen_level`,@MethodConst16) AS `StrengthenLevel` , `e`.`suit_id` AS `SuitId` , IFNULL(`ed`.`atk`,@MethodConst17) AS `Atk` , IFNULL(`ed`.`hit`,@MethodConst18) AS `Hit` , IFNULL(`ed`.`def`,@MethodConst19) AS `Def` , IFNULL(`ed`.`spd`,@MethodConst20) AS `Spd` , IFNULL(`ed`.`dodge`,@MethodConst21) AS `Dodge` , IFNULL(`ed`.`hp`,@MethodConst22) AS `Hp` , IFNULL(`ed`.`mp`,@MethodConst23) AS `Mp` , IFNULL(`ed`.`deepen`,@MethodConst24) AS `Deepen` , IFNULL(`ed`.`offset`,@MethodConst25) AS `Offset` , IFNULL(`ed`.`vamp`,@MethodConst26) AS `Vamp` , IFNULL(`ed`.`vamp_mp`,@MethodConst27) AS `VampMp` , `ed`.`description` AS `Description` , `ed`.`main_attr` AS `MainAttr` , `ed`.`main_attr_value` AS `MainAttrValue` , `ed`.`sub_attr` AS `SubAttr` , `ed`.`sub_attr_value` AS `SubAttrValue` , `ed`.`element_limit` AS `ElementLimit` , `ed`.`equip_name` AS `EquipName`  FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )     ORDER BY `e`.`equip_id` ASC LIMIT 0,10
[2025-09-07 21:07:17.735 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 21:07:17.832 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 21:07:17.836 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 21:07:17.882 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 21:07:17.895 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 21:07:17.899 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment_type`  
[2025-09-07 21:07:17.941 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment_type`  
[2025-09-07 21:07:17.948 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `id` AS `Id` , `equip_type_id` AS `EquipTypeId` , `type_name` AS `TypeName` , @constant6 AS `Description` , @constant7 AS `SortOrder` , @constant8 AS `IsActive` , @constant9 AS `CreateTime` , @constant10 AS `UpdateTime` , @constant11 AS `EquipmentCount`  FROM `equipment_type`    ORDER BY `id` ASC LIMIT 0,10 | 参数: [@constant0=, @constant1=0, @constant2=True, @constant3=, @constant4=, @constant5=0, @constant6=, @constant7=0, @constant8=True, @constant9=, @constant10=, @constant11=0]
[2025-09-07 21:07:17.991 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `id` AS `Id` , `equip_type_id` AS `EquipTypeId` , `type_name` AS `TypeName` , @constant6 AS `Description` , @constant7 AS `SortOrder` , @constant8 AS `IsActive` , @constant9 AS `CreateTime` , @constant10 AS `UpdateTime` , @constant11 AS `EquipmentCount`  FROM `equipment_type`    ORDER BY `id` ASC LIMIT 0,10
[2025-09-07 21:07:17.999 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=1]
[2025-09-07 21:07:18.044 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-09-07 21:07:18.062 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=2]
[2025-09-07 21:07:18.104 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-09-07 21:07:18.106 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=3]
[2025-09-07 21:07:18.149 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-09-07 21:07:18.154 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=4]
[2025-09-07 21:07:18.196 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-09-07 21:07:18.198 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=5]
[2025-09-07 21:07:18.239 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-09-07 21:07:18.241 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=6]
[2025-09-07 21:07:18.281 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-09-07 21:07:18.286 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=7]
[2025-09-07 21:07:18.326 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-09-07 21:07:18.328 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=8]
[2025-09-07 21:07:18.369 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-09-07 21:07:18.372 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=9]
[2025-09-07 21:07:18.413 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-09-07 21:07:18.422 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=10]
[2025-09-07 21:07:18.465 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-09-07 21:19:45.910 +08:00 INF] BMS: 🚀 应用程序启动，日志系统已配置
[2025-09-07 21:19:45.993 +08:00 DBG] BMS: 🔧 调试级别日志测试
[2025-09-07 21:19:46.056 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 21:19:46.059 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 21:19:46.182 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 21:19:46.687 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 21:19:46.696 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 21:19:46.701 +08:00 INF] BMS: ✅ DbContext 初始化成功，日志文件位置: logs/
[2025-09-07 21:19:47.724 +08:00 WRN] Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware: Failed to determine the https port for redirect.
[2025-09-07 21:19:51.526 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 21:19:51.528 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 21:19:51.534 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 21:19:51.574 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 21:19:51.583 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 21:19:51.611 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `equip_type_id` AS `EquipTypeId` , `type_name` AS `TypeName`  FROM `equipment_type` ORDER BY `id` ASC 
[2025-09-07 21:19:51.665 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `equip_type_id` AS `EquipTypeId` , `type_name` AS `TypeName`  FROM `equipment_type` ORDER BY `id` ASC 
[2025-09-07 21:19:51.862 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 21:19:51.898 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 21:19:51.901 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 21:19:51.945 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 21:19:51.991 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 21:19:52.072 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  
[2025-09-07 21:19:52.114 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  
[2025-09-07 21:19:52.125 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment_detail`  
[2025-09-07 21:19:52.164 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment_detail`  
[2025-09-07 21:19:52.175 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment_type`  
[2025-09-07 21:19:52.231 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment_type`  
[2025-09-07 21:19:52.262 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )   
[2025-09-07 21:19:52.331 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )   
[2025-09-07 21:19:52.349 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `e`.`equip_id` AS `EquipId` , @constant14 AS `ClassId` , `e`.`name` AS `Name` , `e`.`icon` AS `Icon` , `e`.`equip_type_id` AS `EquipTypeId` , `et`.`type_name` AS `EquipTypeName` , `e`.`element` AS `Element` , IFNULL(`e`.`slot`,@MethodConst15) AS `Slot` , IFNULL(`e`.`strengthen_level`,@MethodConst16) AS `StrengthenLevel` , `e`.`suit_id` AS `SuitId` , IFNULL(`ed`.`atk`,@MethodConst17) AS `Atk` , IFNULL(`ed`.`hit`,@MethodConst18) AS `Hit` , IFNULL(`ed`.`def`,@MethodConst19) AS `Def` , IFNULL(`ed`.`spd`,@MethodConst20) AS `Spd` , IFNULL(`ed`.`dodge`,@MethodConst21) AS `Dodge` , IFNULL(`ed`.`hp`,@MethodConst22) AS `Hp` , IFNULL(`ed`.`mp`,@MethodConst23) AS `Mp` , IFNULL(`ed`.`deepen`,@MethodConst24) AS `Deepen` , IFNULL(`ed`.`offset`,@MethodConst25) AS `Offset` , IFNULL(`ed`.`vamp`,@MethodConst26) AS `Vamp` , IFNULL(`ed`.`vamp_mp`,@MethodConst27) AS `VampMp` , `ed`.`description` AS `Description` , `ed`.`main_attr` AS `MainAttr` , `ed`.`main_attr_value` AS `MainAttrValue` , `ed`.`sub_attr` AS `SubAttr` , `ed`.`sub_attr_value` AS `SubAttrValue` , `ed`.`element_limit` AS `ElementLimit` , `ed`.`equip_name` AS `EquipName`  FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )     ORDER BY `e`.`equip_id` ASC LIMIT 0,10 | 参数: [@constant0=, @MethodConst1=0, @MethodConst2=0, @MethodConst3=0, @MethodConst4=0, @MethodConst5=0, @MethodConst6=0, @MethodConst7=0, @MethodConst8=0, @MethodConst9=0, @MethodConst10=0, @MethodConst11=0, @MethodConst12=0, @MethodConst13=0, @constant14=, @MethodConst15=0, @MethodConst16=0, @MethodConst17=0, @MethodConst18=0, @MethodConst19=0, @MethodConst20=0, @MethodConst21=0, @MethodConst22=0, @MethodConst23=0, @MethodConst24=0, @MethodConst25=0, @MethodConst26=0, @MethodConst27=0]
[2025-09-07 21:19:52.412 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `e`.`equip_id` AS `EquipId` , @constant14 AS `ClassId` , `e`.`name` AS `Name` , `e`.`icon` AS `Icon` , `e`.`equip_type_id` AS `EquipTypeId` , `et`.`type_name` AS `EquipTypeName` , `e`.`element` AS `Element` , IFNULL(`e`.`slot`,@MethodConst15) AS `Slot` , IFNULL(`e`.`strengthen_level`,@MethodConst16) AS `StrengthenLevel` , `e`.`suit_id` AS `SuitId` , IFNULL(`ed`.`atk`,@MethodConst17) AS `Atk` , IFNULL(`ed`.`hit`,@MethodConst18) AS `Hit` , IFNULL(`ed`.`def`,@MethodConst19) AS `Def` , IFNULL(`ed`.`spd`,@MethodConst20) AS `Spd` , IFNULL(`ed`.`dodge`,@MethodConst21) AS `Dodge` , IFNULL(`ed`.`hp`,@MethodConst22) AS `Hp` , IFNULL(`ed`.`mp`,@MethodConst23) AS `Mp` , IFNULL(`ed`.`deepen`,@MethodConst24) AS `Deepen` , IFNULL(`ed`.`offset`,@MethodConst25) AS `Offset` , IFNULL(`ed`.`vamp`,@MethodConst26) AS `Vamp` , IFNULL(`ed`.`vamp_mp`,@MethodConst27) AS `VampMp` , `ed`.`description` AS `Description` , `ed`.`main_attr` AS `MainAttr` , `ed`.`main_attr_value` AS `MainAttrValue` , `ed`.`sub_attr` AS `SubAttr` , `ed`.`sub_attr_value` AS `SubAttrValue` , `ed`.`element_limit` AS `ElementLimit` , `ed`.`equip_name` AS `EquipName`  FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )     ORDER BY `e`.`equip_id` ASC LIMIT 0,10
[2025-09-07 21:19:52.440 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 21:19:52.443 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 21:19:52.444 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 21:19:52.483 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 21:19:52.520 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 21:19:52.529 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment_type`  
[2025-09-07 21:19:52.566 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment_type`  
[2025-09-07 21:19:52.569 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `id` AS `Id` , `equip_type_id` AS `EquipTypeId` , `type_name` AS `TypeName` , @constant6 AS `Description` , @constant7 AS `SortOrder` , @constant8 AS `IsActive` , @constant9 AS `CreateTime` , @constant10 AS `UpdateTime` , @constant11 AS `EquipmentCount`  FROM `equipment_type`    ORDER BY `id` ASC LIMIT 0,10 | 参数: [@constant0=, @constant1=0, @constant2=True, @constant3=, @constant4=, @constant5=0, @constant6=, @constant7=0, @constant8=True, @constant9=, @constant10=, @constant11=0]
[2025-09-07 21:19:52.608 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `id` AS `Id` , `equip_type_id` AS `EquipTypeId` , `type_name` AS `TypeName` , @constant6 AS `Description` , @constant7 AS `SortOrder` , @constant8 AS `IsActive` , @constant9 AS `CreateTime` , @constant10 AS `UpdateTime` , @constant11 AS `EquipmentCount`  FROM `equipment_type`    ORDER BY `id` ASC LIMIT 0,10
[2025-09-07 21:19:52.625 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=1]
[2025-09-07 21:19:52.663 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-09-07 21:19:52.665 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=2]
[2025-09-07 21:19:52.702 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-09-07 21:19:52.705 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=3]
[2025-09-07 21:19:52.744 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-09-07 21:19:52.746 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=4]
[2025-09-07 21:19:52.784 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-09-07 21:19:52.786 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=5]
[2025-09-07 21:19:52.825 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-09-07 21:19:52.827 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=6]
[2025-09-07 21:19:52.873 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-09-07 21:19:52.900 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=7]
[2025-09-07 21:19:52.942 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-09-07 21:19:52.944 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=8]
[2025-09-07 21:19:52.981 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-09-07 21:19:52.984 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=9]
[2025-09-07 21:19:53.021 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-09-07 21:19:53.024 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=10]
[2025-09-07 21:19:53.060 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-09-07 21:19:56.779 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 21:19:56.782 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 21:19:56.783 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 21:19:56.821 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 21:19:56.823 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 21:19:56.834 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `e`.`equip_id` AS `EquipId` , @constant15 AS `ClassId` , `e`.`name` AS `Name` , `e`.`icon` AS `Icon` , `e`.`equip_type_id` AS `EquipTypeId` , `et`.`type_name` AS `EquipTypeName` , `e`.`element` AS `Element` , IFNULL(`e`.`slot`,@MethodConst16) AS `Slot` , IFNULL(`e`.`strengthen_level`,@MethodConst17) AS `StrengthenLevel` , `e`.`suit_id` AS `SuitId` , IFNULL(`ed`.`atk`,@MethodConst18) AS `Atk` , IFNULL(`ed`.`hit`,@MethodConst19) AS `Hit` , IFNULL(`ed`.`def`,@MethodConst20) AS `Def` , IFNULL(`ed`.`spd`,@MethodConst21) AS `Spd` , IFNULL(`ed`.`dodge`,@MethodConst22) AS `Dodge` , IFNULL(`ed`.`hp`,@MethodConst23) AS `Hp` , IFNULL(`ed`.`mp`,@MethodConst24) AS `Mp` , IFNULL(`ed`.`deepen`,@MethodConst25) AS `Deepen` , IFNULL(`ed`.`offset`,@MethodConst26) AS `Offset` , IFNULL(`ed`.`vamp`,@MethodConst27) AS `Vamp` , IFNULL(`ed`.`vamp_mp`,@MethodConst28) AS `VampMp` , `ed`.`description` AS `Description` , `ed`.`main_attr` AS `MainAttr` , `ed`.`main_attr_value` AS `MainAttrValue` , `ed`.`sub_attr` AS `SubAttr` , `ed`.`sub_attr_value` AS `SubAttrValue` , `ed`.`element_limit` AS `ElementLimit` , `ed`.`equip_name` AS `EquipName`  FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )    WHERE ( `e`.`equip_id` = @equip_id0 )   LIMIT 0,1 | 参数: [@equip_id0=000101, @constant1=, @MethodConst2=0, @MethodConst3=0, @MethodConst4=0, @MethodConst5=0, @MethodConst6=0, @MethodConst7=0, @MethodConst8=0, @MethodConst9=0, @MethodConst10=0, @MethodConst11=0, @MethodConst12=0, @MethodConst13=0, @MethodConst14=0, @constant15=, @MethodConst16=0, @MethodConst17=0, @MethodConst18=0, @MethodConst19=0, @MethodConst20=0, @MethodConst21=0, @MethodConst22=0, @MethodConst23=0, @MethodConst24=0, @MethodConst25=0, @MethodConst26=0, @MethodConst27=0, @MethodConst28=0]
[2025-09-07 21:19:56.876 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `e`.`equip_id` AS `EquipId` , @constant15 AS `ClassId` , `e`.`name` AS `Name` , `e`.`icon` AS `Icon` , `e`.`equip_type_id` AS `EquipTypeId` , `et`.`type_name` AS `EquipTypeName` , `e`.`element` AS `Element` , IFNULL(`e`.`slot`,@MethodConst16) AS `Slot` , IFNULL(`e`.`strengthen_level`,@MethodConst17) AS `StrengthenLevel` , `e`.`suit_id` AS `SuitId` , IFNULL(`ed`.`atk`,@MethodConst18) AS `Atk` , IFNULL(`ed`.`hit`,@MethodConst19) AS `Hit` , IFNULL(`ed`.`def`,@MethodConst20) AS `Def` , IFNULL(`ed`.`spd`,@MethodConst21) AS `Spd` , IFNULL(`ed`.`dodge`,@MethodConst22) AS `Dodge` , IFNULL(`ed`.`hp`,@MethodConst23) AS `Hp` , IFNULL(`ed`.`mp`,@MethodConst24) AS `Mp` , IFNULL(`ed`.`deepen`,@MethodConst25) AS `Deepen` , IFNULL(`ed`.`offset`,@MethodConst26) AS `Offset` , IFNULL(`ed`.`vamp`,@MethodConst27) AS `Vamp` , IFNULL(`ed`.`vamp_mp`,@MethodConst28) AS `VampMp` , `ed`.`description` AS `Description` , `ed`.`main_attr` AS `MainAttr` , `ed`.`main_attr_value` AS `MainAttrValue` , `ed`.`sub_attr` AS `SubAttr` , `ed`.`sub_attr_value` AS `SubAttrValue` , `ed`.`element_limit` AS `ElementLimit` , `ed`.`equip_name` AS `EquipName`  FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )    WHERE ( `e`.`equip_id` = @equip_id0 )   LIMIT 0,1
[2025-09-07 21:20:50.278 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 21:20:50.347 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 21:20:50.367 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 21:20:50.441 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 21:20:50.454 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 21:20:50.538 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 21:20:50.545 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 21:20:50.551 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 21:20:50.589 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 21:20:50.590 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 21:20:50.598 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `skill`  
[2025-09-07 21:20:50.637 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `skill`  
[2025-09-07 21:20:50.641 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `skill_id`,`skill_name`,`skill_percent`,`effect_type`,`effect_value`,`mana_cost`,`buff_info`,`element_limit`,`create_time`,`skill_type` FROM `skill`    ORDER BY `create_time` DESC LIMIT 0,10
[2025-09-07 21:20:50.679 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `skill_id`,`skill_name`,`skill_percent`,`effect_type`,`effect_value`,`mana_cost`,`buff_info`,`element_limit`,`create_time`,`skill_type` FROM `skill`    ORDER BY `create_time` DESC LIMIT 0,10
[2025-09-07 21:32:53.566 +08:00 INF] BMS: 🚀 应用程序启动，日志系统已配置
[2025-09-07 21:32:53.596 +08:00 DBG] BMS: 🔧 调试级别日志测试
[2025-09-07 21:32:53.613 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 21:32:53.628 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 21:32:53.721 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 21:32:54.265 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 21:32:54.387 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 21:32:54.394 +08:00 INF] BMS: ✅ DbContext 初始化成功，日志文件位置: logs/
[2025-09-07 21:32:55.225 +08:00 WRN] Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware: Failed to determine the https port for redirect.
[2025-09-07 21:33:01.645 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 21:33:01.647 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 21:33:01.655 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 21:33:01.692 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 21:33:01.700 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 21:33:01.857 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 21:33:01.894 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 21:33:01.898 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 21:33:01.934 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 21:33:01.936 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 21:33:02.045 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `skill`  
[2025-09-07 21:33:02.097 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `skill`  
[2025-09-07 21:33:02.146 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `skill_id`,`skill_name`,`skill_percent`,`effect_type`,`effect_value`,`mana_cost`,`buff_info`,`element_limit`,`create_time`,`skill_type` FROM `skill`    ORDER BY `create_time` DESC LIMIT 0,10
[2025-09-07 21:33:02.186 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `skill_id`,`skill_name`,`skill_percent`,`effect_type`,`effect_value`,`mana_cost`,`buff_info`,`element_limit`,`create_time`,`skill_type` FROM `skill`    ORDER BY `create_time` DESC LIMIT 0,10
[2025-09-07 21:33:04.983 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 21:33:06.960 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 21:33:07.293 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 21:33:08.175 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 21:33:08.762 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 21:33:08.940 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `skill_id`,`skill_name`,`skill_percent`,`effect_type`,`effect_value`,`mana_cost`,`buff_info`,`element_limit`,`create_time`,`skill_type` FROM `skill`   WHERE ( `skill_id` = @skill_id0 )   LIMIT 0,1 | 参数: [@skill_id0=69]
[2025-09-07 21:33:09.028 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `skill_id`,`skill_name`,`skill_percent`,`effect_type`,`effect_value`,`mana_cost`,`buff_info`,`element_limit`,`create_time`,`skill_type` FROM `skill`   WHERE ( `skill_id` = @skill_id0 )   LIMIT 0,1
[2025-09-07 21:33:09.082 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 21:33:09.125 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 21:33:09.159 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 21:33:09.221 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 21:33:09.258 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 21:33:09.273 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `skill_id`,`skill_name`,`skill_percent`,`effect_type`,`effect_value`,`mana_cost`,`buff_info`,`element_limit`,`create_time`,`skill_type` FROM `skill`   WHERE ( `skill_id` = @skill_id0 )   LIMIT 0,1 | 参数: [@skill_id0=69]
[2025-09-07 21:33:09.397 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `skill_id`,`skill_name`,`skill_percent`,`effect_type`,`effect_value`,`mana_cost`,`buff_info`,`element_limit`,`create_time`,`skill_type` FROM `skill`   WHERE ( `skill_id` = @skill_id0 )   LIMIT 0,1
[2025-09-07 21:37:39.831 +08:00 INF] BMS: 🚀 应用程序启动，日志系统已配置
[2025-09-07 21:37:39.860 +08:00 DBG] BMS: 🔧 调试级别日志测试
[2025-09-07 21:37:39.876 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 21:37:39.879 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 21:37:39.957 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 21:37:40.456 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 21:37:40.466 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 21:37:40.468 +08:00 INF] BMS: ✅ DbContext 初始化成功，日志文件位置: logs/
[2025-09-07 21:37:41.627 +08:00 WRN] Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware: Failed to determine the https port for redirect.
[2025-09-07 21:37:46.749 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 21:37:47.346 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 21:37:48.469 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 21:37:49.200 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 21:37:50.154 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 21:37:50.292 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 21:37:50.344 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 21:37:50.407 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 21:37:50.527 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 21:37:50.656 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `equip_type_id` AS `EquipTypeId` , `type_name` AS `TypeName`  FROM `equipment_type` ORDER BY `id` ASC 
[2025-09-07 21:37:50.658 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 21:37:50.861 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `equip_type_id` AS `EquipTypeId` , `type_name` AS `TypeName`  FROM `equipment_type` ORDER BY `id` ASC 
[2025-09-07 21:37:51.152 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `equip_type_id` AS `EquipTypeId` , `type_name` AS `TypeName`  FROM `equipment_type` ORDER BY `id` ASC 
[2025-09-07 21:37:51.934 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 21:37:52.619 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `equip_type_id` AS `EquipTypeId` , `type_name` AS `TypeName`  FROM `equipment_type` ORDER BY `id` ASC 
[2025-09-07 21:37:52.637 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 21:37:52.701 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 21:37:52.779 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 21:37:52.784 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 21:37:52.794 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `equip_type_id` AS `EquipTypeId` , `type_name` AS `TypeName`  FROM `equipment_type` ORDER BY `id` ASC 
[2025-09-07 21:37:52.840 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `equip_type_id` AS `EquipTypeId` , `type_name` AS `TypeName`  FROM `equipment_type` ORDER BY `id` ASC 
[2025-09-07 21:37:54.110 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 21:37:54.253 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 21:37:54.339 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 21:37:54.420 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 21:37:54.423 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 21:37:54.540 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  
[2025-09-07 21:37:54.584 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  
[2025-09-07 21:37:54.620 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment_detail`  
[2025-09-07 21:37:54.698 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment_detail`  
[2025-09-07 21:37:54.708 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment_type`  
[2025-09-07 21:37:54.747 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment_type`  
[2025-09-07 21:37:54.765 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )   
[2025-09-07 21:37:54.812 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )   
[2025-09-07 21:37:54.836 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `e`.`equip_id` AS `EquipId` , @constant14 AS `ClassId` , `e`.`name` AS `Name` , `e`.`icon` AS `Icon` , `e`.`equip_type_id` AS `EquipTypeId` , `et`.`type_name` AS `EquipTypeName` , `e`.`element` AS `Element` , IFNULL(`e`.`slot`,@MethodConst15) AS `Slot` , IFNULL(`e`.`strengthen_level`,@MethodConst16) AS `StrengthenLevel` , `e`.`suit_id` AS `SuitId` , IFNULL(`ed`.`atk`,@MethodConst17) AS `Atk` , IFNULL(`ed`.`hit`,@MethodConst18) AS `Hit` , IFNULL(`ed`.`def`,@MethodConst19) AS `Def` , IFNULL(`ed`.`spd`,@MethodConst20) AS `Spd` , IFNULL(`ed`.`dodge`,@MethodConst21) AS `Dodge` , IFNULL(`ed`.`hp`,@MethodConst22) AS `Hp` , IFNULL(`ed`.`mp`,@MethodConst23) AS `Mp` , IFNULL(`ed`.`deepen`,@MethodConst24) AS `Deepen` , IFNULL(`ed`.`offset`,@MethodConst25) AS `Offset` , IFNULL(`ed`.`vamp`,@MethodConst26) AS `Vamp` , IFNULL(`ed`.`vamp_mp`,@MethodConst27) AS `VampMp` , `ed`.`description` AS `Description` , `ed`.`main_attr` AS `MainAttr` , `ed`.`main_attr_value` AS `MainAttrValue` , `ed`.`sub_attr` AS `SubAttr` , `ed`.`sub_attr_value` AS `SubAttrValue` , `ed`.`element_limit` AS `ElementLimit` , `ed`.`equip_name` AS `EquipName`  FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )     ORDER BY `e`.`equip_id` ASC LIMIT 0,10 | 参数: [@constant0=, @MethodConst1=0, @MethodConst2=0, @MethodConst3=0, @MethodConst4=0, @MethodConst5=0, @MethodConst6=0, @MethodConst7=0, @MethodConst8=0, @MethodConst9=0, @MethodConst10=0, @MethodConst11=0, @MethodConst12=0, @MethodConst13=0, @constant14=, @MethodConst15=0, @MethodConst16=0, @MethodConst17=0, @MethodConst18=0, @MethodConst19=0, @MethodConst20=0, @MethodConst21=0, @MethodConst22=0, @MethodConst23=0, @MethodConst24=0, @MethodConst25=0, @MethodConst26=0, @MethodConst27=0]
[2025-09-07 21:37:54.900 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `e`.`equip_id` AS `EquipId` , @constant14 AS `ClassId` , `e`.`name` AS `Name` , `e`.`icon` AS `Icon` , `e`.`equip_type_id` AS `EquipTypeId` , `et`.`type_name` AS `EquipTypeName` , `e`.`element` AS `Element` , IFNULL(`e`.`slot`,@MethodConst15) AS `Slot` , IFNULL(`e`.`strengthen_level`,@MethodConst16) AS `StrengthenLevel` , `e`.`suit_id` AS `SuitId` , IFNULL(`ed`.`atk`,@MethodConst17) AS `Atk` , IFNULL(`ed`.`hit`,@MethodConst18) AS `Hit` , IFNULL(`ed`.`def`,@MethodConst19) AS `Def` , IFNULL(`ed`.`spd`,@MethodConst20) AS `Spd` , IFNULL(`ed`.`dodge`,@MethodConst21) AS `Dodge` , IFNULL(`ed`.`hp`,@MethodConst22) AS `Hp` , IFNULL(`ed`.`mp`,@MethodConst23) AS `Mp` , IFNULL(`ed`.`deepen`,@MethodConst24) AS `Deepen` , IFNULL(`ed`.`offset`,@MethodConst25) AS `Offset` , IFNULL(`ed`.`vamp`,@MethodConst26) AS `Vamp` , IFNULL(`ed`.`vamp_mp`,@MethodConst27) AS `VampMp` , `ed`.`description` AS `Description` , `ed`.`main_attr` AS `MainAttr` , `ed`.`main_attr_value` AS `MainAttrValue` , `ed`.`sub_attr` AS `SubAttr` , `ed`.`sub_attr_value` AS `SubAttrValue` , `ed`.`element_limit` AS `ElementLimit` , `ed`.`equip_name` AS `EquipName`  FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )     ORDER BY `e`.`equip_id` ASC LIMIT 0,10
[2025-09-07 21:37:54.930 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 21:37:54.931 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 21:37:54.934 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 21:37:54.977 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 21:37:54.980 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 21:37:54.989 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment_type`  
[2025-09-07 21:37:55.028 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment_type`  
[2025-09-07 21:37:55.031 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `id` AS `Id` , `equip_type_id` AS `EquipTypeId` , `type_name` AS `TypeName` , @constant6 AS `Description` , @constant7 AS `SortOrder` , @constant8 AS `IsActive` , @constant9 AS `CreateTime` , @constant10 AS `UpdateTime` , @constant11 AS `EquipmentCount`  FROM `equipment_type`    ORDER BY `id` ASC LIMIT 0,10 | 参数: [@constant0=, @constant1=0, @constant2=True, @constant3=, @constant4=, @constant5=0, @constant6=, @constant7=0, @constant8=True, @constant9=, @constant10=, @constant11=0]
[2025-09-07 21:37:55.071 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `id` AS `Id` , `equip_type_id` AS `EquipTypeId` , `type_name` AS `TypeName` , @constant6 AS `Description` , @constant7 AS `SortOrder` , @constant8 AS `IsActive` , @constant9 AS `CreateTime` , @constant10 AS `UpdateTime` , @constant11 AS `EquipmentCount`  FROM `equipment_type`    ORDER BY `id` ASC LIMIT 0,10
[2025-09-07 21:37:55.087 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=1]
[2025-09-07 21:37:55.127 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-09-07 21:37:55.131 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=2]
[2025-09-07 21:37:55.172 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-09-07 21:37:55.178 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=3]
[2025-09-07 21:37:55.224 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-09-07 21:37:55.242 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=4]
[2025-09-07 21:37:55.318 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-09-07 21:37:55.368 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=5]
[2025-09-07 21:37:55.457 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-09-07 21:37:55.701 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=6]
[2025-09-07 21:37:55.782 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-09-07 21:37:55.796 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=7]
[2025-09-07 21:37:55.837 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-09-07 21:37:55.841 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=8]
[2025-09-07 21:37:55.879 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-09-07 21:37:55.882 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=9]
[2025-09-07 21:37:55.920 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-09-07 21:37:55.923 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=10]
[2025-09-07 21:37:55.961 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-09-07 21:37:58.832 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 21:37:58.839 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 21:37:58.841 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 21:37:58.878 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 21:37:58.880 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 21:37:58.943 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 21:37:58.948 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 21:37:58.952 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 21:37:58.991 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 21:37:58.996 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 21:37:59.004 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `skill`  
[2025-09-07 21:37:59.042 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `skill`  
[2025-09-07 21:37:59.045 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `skill_id`,`skill_name`,`skill_percent`,`effect_type`,`effect_value`,`mana_cost`,`buff_info`,`element_limit`,`create_time`,`skill_type` FROM `skill`    ORDER BY `create_time` DESC LIMIT 0,10
[2025-09-07 21:37:59.089 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `skill_id`,`skill_name`,`skill_percent`,`effect_type`,`effect_value`,`mana_cost`,`buff_info`,`element_limit`,`create_time`,`skill_type` FROM `skill`    ORDER BY `create_time` DESC LIMIT 0,10
[2025-09-07 21:38:01.530 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 21:38:01.547 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 21:38:01.563 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 21:38:01.623 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 21:38:01.635 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 21:38:01.659 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `skill_id`,`skill_name`,`skill_percent`,`effect_type`,`effect_value`,`mana_cost`,`buff_info`,`element_limit`,`create_time`,`skill_type` FROM `skill`   WHERE ( `skill_id` = @skill_id0 )   LIMIT 0,1 | 参数: [@skill_id0=69]
[2025-09-07 21:38:01.806 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `skill_id`,`skill_name`,`skill_percent`,`effect_type`,`effect_value`,`mana_cost`,`buff_info`,`element_limit`,`create_time`,`skill_type` FROM `skill`   WHERE ( `skill_id` = @skill_id0 )   LIMIT 0,1
[2025-09-07 21:38:09.343 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 21:38:09.451 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 21:38:09.557 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 21:38:09.671 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 21:38:09.718 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 21:38:09.767 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `skill_id`,`skill_name`,`skill_percent`,`effect_type`,`effect_value`,`mana_cost`,`buff_info`,`element_limit`,`create_time`,`skill_type` FROM `skill`   WHERE ( `skill_id` = @skill_id0 )   LIMIT 0,1 | 参数: [@skill_id0=69]
[2025-09-07 21:38:09.806 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `skill_id`,`skill_name`,`skill_percent`,`effect_type`,`effect_value`,`mana_cost`,`buff_info`,`element_limit`,`create_time`,`skill_type` FROM `skill`   WHERE ( `skill_id` = @skill_id0 )   LIMIT 0,1
[2025-09-07 21:38:09.838 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: UPDATE `skill`  SET
           `skill_name`=@skill_name,`skill_percent`=@skill_percent,`effect_type`=@effect_type,`effect_value`=@effect_value,`mana_cost`=@mana_cost,`buff_info`=@buff_info,`element_limit`=@element_limit,`create_time`=@create_time,`skill_type`=@skill_type  WHERE `skill_id`=@skill_id | 参数: [@skill_id=69, @skill_name=魂之挽歌1, @skill_percent=1.6, @effect_type=, @effect_value="{\r\n  \"skill_percent\": 1.6\r\n}", @mana_cost=5000, @buff_info=, @element_limit=, @create_time=2025/8/4 16:42:52, @skill_type=ACTIVE]
[2025-09-07 21:38:09.967 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: UPDATE `skill`  SET
           `skill_name`=@skill_name,`skill_percent`=@skill_percent,`effect_type`=@effect_type,`effect_value`=@effect_value,`mana_cost`=@mana_cost,`buff_info`=@buff_info,`element_limit`=@element_limit,`create_time`=@create_time,`skill_type`=@skill_type  WHERE `skill_id`=@skill_id
[2025-09-07 21:38:09.977 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 21:38:09.978 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 21:38:09.980 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 21:38:10.020 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 21:38:10.021 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 21:38:10.024 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `skill`  
[2025-09-07 21:38:10.065 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `skill`  
[2025-09-07 21:38:10.067 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `skill_id`,`skill_name`,`skill_percent`,`effect_type`,`effect_value`,`mana_cost`,`buff_info`,`element_limit`,`create_time`,`skill_type` FROM `skill`    ORDER BY `create_time` DESC LIMIT 0,10
[2025-09-07 21:38:10.107 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `skill_id`,`skill_name`,`skill_percent`,`effect_type`,`effect_value`,`mana_cost`,`buff_info`,`element_limit`,`create_time`,`skill_type` FROM `skill`    ORDER BY `create_time` DESC LIMIT 0,10
[2025-09-07 21:38:12.694 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 21:38:12.697 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 21:38:12.699 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 21:38:12.737 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 21:38:12.738 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 21:38:12.739 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `skill_id`,`skill_name`,`skill_percent`,`effect_type`,`effect_value`,`mana_cost`,`buff_info`,`element_limit`,`create_time`,`skill_type` FROM `skill`   WHERE ( `skill_id` = @skill_id0 )   LIMIT 0,1 | 参数: [@skill_id0=69]
[2025-09-07 21:38:12.777 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `skill_id`,`skill_name`,`skill_percent`,`effect_type`,`effect_value`,`mana_cost`,`buff_info`,`element_limit`,`create_time`,`skill_type` FROM `skill`   WHERE ( `skill_id` = @skill_id0 )   LIMIT 0,1
[2025-09-07 21:38:17.030 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 21:38:17.043 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 21:38:17.063 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 21:38:17.139 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 21:38:17.151 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 21:38:17.171 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `skill_id`,`skill_name`,`skill_percent`,`effect_type`,`effect_value`,`mana_cost`,`buff_info`,`element_limit`,`create_time`,`skill_type` FROM `skill`   WHERE ( `skill_id` = @skill_id0 )   LIMIT 0,1 | 参数: [@skill_id0=69]
[2025-09-07 21:38:17.226 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `skill_id`,`skill_name`,`skill_percent`,`effect_type`,`effect_value`,`mana_cost`,`buff_info`,`element_limit`,`create_time`,`skill_type` FROM `skill`   WHERE ( `skill_id` = @skill_id0 )   LIMIT 0,1
[2025-09-07 21:38:17.236 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: UPDATE `skill`  SET
           `skill_name`=@skill_name,`skill_percent`=@skill_percent,`effect_type`=@effect_type,`effect_value`=@effect_value,`mana_cost`=@mana_cost,`buff_info`=@buff_info,`element_limit`=@element_limit,`create_time`=@create_time,`skill_type`=@skill_type  WHERE `skill_id`=@skill_id | 参数: [@skill_id=69, @skill_name=魂之挽歌, @skill_percent=1.6, @effect_type=, @effect_value="{\r\n  \"skill_percent\": 1.6\r\n}", @mana_cost=5000, @buff_info=, @element_limit=, @create_time=2025/8/4 16:42:52, @skill_type=ACTIVE]
[2025-09-07 21:38:17.297 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: UPDATE `skill`  SET
           `skill_name`=@skill_name,`skill_percent`=@skill_percent,`effect_type`=@effect_type,`effect_value`=@effect_value,`mana_cost`=@mana_cost,`buff_info`=@buff_info,`element_limit`=@element_limit,`create_time`=@create_time,`skill_type`=@skill_type  WHERE `skill_id`=@skill_id
[2025-09-07 21:38:17.309 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 21:38:17.311 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 21:38:17.316 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 21:38:17.357 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 21:38:17.358 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 21:38:17.361 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `skill`  
[2025-09-07 21:38:17.401 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `skill`  
[2025-09-07 21:38:17.402 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `skill_id`,`skill_name`,`skill_percent`,`effect_type`,`effect_value`,`mana_cost`,`buff_info`,`element_limit`,`create_time`,`skill_type` FROM `skill`    ORDER BY `create_time` DESC LIMIT 0,10
[2025-09-07 21:38:17.442 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `skill_id`,`skill_name`,`skill_percent`,`effect_type`,`effect_value`,`mana_cost`,`buff_info`,`element_limit`,`create_time`,`skill_type` FROM `skill`    ORDER BY `create_time` DESC LIMIT 0,10
[2025-09-07 21:38:47.893 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 21:38:47.904 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 21:38:47.907 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 21:38:47.951 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 21:38:47.977 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 21:38:47.979 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `equip_type_id` AS `EquipTypeId` , `type_name` AS `TypeName`  FROM `equipment_type` ORDER BY `id` ASC 
[2025-09-07 21:38:48.017 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `equip_type_id` AS `EquipTypeId` , `type_name` AS `TypeName`  FROM `equipment_type` ORDER BY `id` ASC 
[2025-09-07 21:38:48.074 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 21:38:48.076 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 21:38:48.078 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 21:38:48.116 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 21:38:48.118 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 21:38:48.121 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  
[2025-09-07 21:38:48.160 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  
[2025-09-07 21:38:48.163 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment_detail`  
[2025-09-07 21:38:48.201 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment_detail`  
[2025-09-07 21:38:48.204 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment_type`  
[2025-09-07 21:38:48.243 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment_type`  
[2025-09-07 21:38:48.246 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )   
[2025-09-07 21:38:48.296 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )   
[2025-09-07 21:38:48.300 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `e`.`equip_id` AS `EquipId` , @constant14 AS `ClassId` , `e`.`name` AS `Name` , `e`.`icon` AS `Icon` , `e`.`equip_type_id` AS `EquipTypeId` , `et`.`type_name` AS `EquipTypeName` , `e`.`element` AS `Element` , IFNULL(`e`.`slot`,@MethodConst15) AS `Slot` , IFNULL(`e`.`strengthen_level`,@MethodConst16) AS `StrengthenLevel` , `e`.`suit_id` AS `SuitId` , IFNULL(`ed`.`atk`,@MethodConst17) AS `Atk` , IFNULL(`ed`.`hit`,@MethodConst18) AS `Hit` , IFNULL(`ed`.`def`,@MethodConst19) AS `Def` , IFNULL(`ed`.`spd`,@MethodConst20) AS `Spd` , IFNULL(`ed`.`dodge`,@MethodConst21) AS `Dodge` , IFNULL(`ed`.`hp`,@MethodConst22) AS `Hp` , IFNULL(`ed`.`mp`,@MethodConst23) AS `Mp` , IFNULL(`ed`.`deepen`,@MethodConst24) AS `Deepen` , IFNULL(`ed`.`offset`,@MethodConst25) AS `Offset` , IFNULL(`ed`.`vamp`,@MethodConst26) AS `Vamp` , IFNULL(`ed`.`vamp_mp`,@MethodConst27) AS `VampMp` , `ed`.`description` AS `Description` , `ed`.`main_attr` AS `MainAttr` , `ed`.`main_attr_value` AS `MainAttrValue` , `ed`.`sub_attr` AS `SubAttr` , `ed`.`sub_attr_value` AS `SubAttrValue` , `ed`.`element_limit` AS `ElementLimit` , `ed`.`equip_name` AS `EquipName`  FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )     ORDER BY `e`.`equip_id` ASC LIMIT 0,10 | 参数: [@constant0=, @MethodConst1=0, @MethodConst2=0, @MethodConst3=0, @MethodConst4=0, @MethodConst5=0, @MethodConst6=0, @MethodConst7=0, @MethodConst8=0, @MethodConst9=0, @MethodConst10=0, @MethodConst11=0, @MethodConst12=0, @MethodConst13=0, @constant14=, @MethodConst15=0, @MethodConst16=0, @MethodConst17=0, @MethodConst18=0, @MethodConst19=0, @MethodConst20=0, @MethodConst21=0, @MethodConst22=0, @MethodConst23=0, @MethodConst24=0, @MethodConst25=0, @MethodConst26=0, @MethodConst27=0]
[2025-09-07 21:38:48.359 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `e`.`equip_id` AS `EquipId` , @constant14 AS `ClassId` , `e`.`name` AS `Name` , `e`.`icon` AS `Icon` , `e`.`equip_type_id` AS `EquipTypeId` , `et`.`type_name` AS `EquipTypeName` , `e`.`element` AS `Element` , IFNULL(`e`.`slot`,@MethodConst15) AS `Slot` , IFNULL(`e`.`strengthen_level`,@MethodConst16) AS `StrengthenLevel` , `e`.`suit_id` AS `SuitId` , IFNULL(`ed`.`atk`,@MethodConst17) AS `Atk` , IFNULL(`ed`.`hit`,@MethodConst18) AS `Hit` , IFNULL(`ed`.`def`,@MethodConst19) AS `Def` , IFNULL(`ed`.`spd`,@MethodConst20) AS `Spd` , IFNULL(`ed`.`dodge`,@MethodConst21) AS `Dodge` , IFNULL(`ed`.`hp`,@MethodConst22) AS `Hp` , IFNULL(`ed`.`mp`,@MethodConst23) AS `Mp` , IFNULL(`ed`.`deepen`,@MethodConst24) AS `Deepen` , IFNULL(`ed`.`offset`,@MethodConst25) AS `Offset` , IFNULL(`ed`.`vamp`,@MethodConst26) AS `Vamp` , IFNULL(`ed`.`vamp_mp`,@MethodConst27) AS `VampMp` , `ed`.`description` AS `Description` , `ed`.`main_attr` AS `MainAttr` , `ed`.`main_attr_value` AS `MainAttrValue` , `ed`.`sub_attr` AS `SubAttr` , `ed`.`sub_attr_value` AS `SubAttrValue` , `ed`.`element_limit` AS `ElementLimit` , `ed`.`equip_name` AS `EquipName`  FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )     ORDER BY `e`.`equip_id` ASC LIMIT 0,10
[2025-09-07 21:38:48.367 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 21:38:48.368 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 21:38:48.370 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 21:38:48.412 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 21:38:48.414 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 21:38:48.415 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment_type`  
[2025-09-07 21:38:48.453 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment_type`  
[2025-09-07 21:38:48.455 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `id` AS `Id` , `equip_type_id` AS `EquipTypeId` , `type_name` AS `TypeName` , @constant6 AS `Description` , @constant7 AS `SortOrder` , @constant8 AS `IsActive` , @constant9 AS `CreateTime` , @constant10 AS `UpdateTime` , @constant11 AS `EquipmentCount`  FROM `equipment_type`    ORDER BY `id` ASC LIMIT 0,10 | 参数: [@constant0=, @constant1=0, @constant2=True, @constant3=, @constant4=, @constant5=0, @constant6=, @constant7=0, @constant8=True, @constant9=, @constant10=, @constant11=0]
[2025-09-07 21:38:48.494 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `id` AS `Id` , `equip_type_id` AS `EquipTypeId` , `type_name` AS `TypeName` , @constant6 AS `Description` , @constant7 AS `SortOrder` , @constant8 AS `IsActive` , @constant9 AS `CreateTime` , @constant10 AS `UpdateTime` , @constant11 AS `EquipmentCount`  FROM `equipment_type`    ORDER BY `id` ASC LIMIT 0,10
[2025-09-07 21:38:48.496 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=1]
[2025-09-07 21:38:48.562 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-09-07 21:38:48.565 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=2]
[2025-09-07 21:38:48.604 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-09-07 21:38:48.607 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=3]
[2025-09-07 21:38:48.645 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-09-07 21:38:48.701 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=4]
[2025-09-07 21:38:48.747 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-09-07 21:38:48.755 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=5]
[2025-09-07 21:38:48.801 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-09-07 21:38:48.805 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=6]
[2025-09-07 21:38:48.845 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-09-07 21:38:48.848 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=7]
[2025-09-07 21:38:48.889 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-09-07 21:38:48.892 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=8]
[2025-09-07 21:38:48.934 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-09-07 21:38:48.937 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=9]
[2025-09-07 21:38:48.977 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-09-07 21:38:48.982 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=10]
[2025-09-07 21:38:49.034 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-09-07 21:38:54.455 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 21:38:54.457 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 21:38:54.459 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 21:38:54.497 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 21:38:54.499 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 21:38:54.502 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`map_id`,`map_name`,`map_desc`,`map_size`,`map_type`,`atlast_name`,`background`,`bgm`,`bgm_loop`,`bgm_volume`,`bgm_play`,`bgm_mute`,`bgm_pause`,`ico`,`type` FROM `map_config` ORDER BY `map_id` ASC 
[2025-09-07 21:38:54.541 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`map_id`,`map_name`,`map_desc`,`map_size`,`map_type`,`atlast_name`,`background`,`bgm`,`bgm_loop`,`bgm_volume`,`bgm_play`,`bgm_mute`,`bgm_pause`,`ico`,`type` FROM `map_config` ORDER BY `map_id` ASC 
[2025-09-07 21:38:54.605 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 21:38:54.606 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 21:38:54.606 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 21:38:54.607 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 21:38:54.609 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 21:38:54.609 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 21:38:54.646 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 21:38:54.647 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 21:38:54.650 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`map_id`,`map_name`,`map_desc`,`map_size`,`map_type`,`atlast_name`,`background`,`bgm`,`bgm_loop`,`bgm_volume`,`bgm_play`,`bgm_mute`,`bgm_pause`,`ico`,`type` FROM `map_config` ORDER BY `map_id` ASC 
[2025-09-07 21:38:54.688 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`map_id`,`map_name`,`map_desc`,`map_size`,`map_type`,`atlast_name`,`background`,`bgm`,`bgm_loop`,`bgm_volume`,`bgm_play`,`bgm_mute`,`bgm_pause`,`ico`,`type` FROM `map_config` ORDER BY `map_id` ASC 
[2025-09-07 21:38:54.811 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 21:38:54.813 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 21:38:54.830 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM `map_detail` `d` Left JOIN `map_config` `c` ON ( `d`.`map_id` = `c`.`map_id` )    | 参数: [@MethodConst0=0, @MethodConst1=0, @MethodConst2=False, @MethodConst3=0, @MethodConst4=0, @MethodConst5=0, @MethodConst6=0, @MethodConst7=0, @MethodConst8=0, @MethodConst9=0]
[2025-09-07 21:38:54.862 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM `map_detail` `d` Left JOIN `map_config` `c` ON ( `d`.`map_id` = `c`.`map_id` )   
[2025-09-07 21:38:54.865 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `d`.`id` AS `Id` , `d`.`map_id` AS `MapId` , `c`.`map_name` AS `MapName` , IFNULL(`d`.`limit_growth`,@MethodConst10) AS `LimitGrowth` , IFNULL(`d`.`limit_level`,@MethodConst11) AS `LimitLevel` , IFNULL(CAST(`d`.`limit_key` as SIGNED),@MethodConst12) AS `LimitKey` , IFNULL(`d`.`min_gold`,@MethodConst13) AS `MinGold` , IFNULL(`d`.`max_gold`,@MethodConst14) AS `MaxGold` , IFNULL(`d`.`min_yuanbao`,@MethodConst15) AS `MinYuanbao` , IFNULL(`d`.`max_yuanbao`,@MethodConst16) AS `MaxYuanbao` , IFNULL(`d`.`min_drop`,@MethodConst17) AS `MinDrop` , IFNULL(`d`.`max_drop`,@MethodConst18) AS `MaxDrop` , `d`.`icon` AS `Icon` , IFNULL(`d`.`type`,@MethodConst19) AS `Type`  FROM `map_detail` `d` Left JOIN `map_config` `c` ON ( `d`.`map_id` = `c`.`map_id` )     ORDER BY `d`.`id` ASC LIMIT 0,10 | 参数: [@MethodConst0=0, @MethodConst1=0, @MethodConst2=False, @MethodConst3=0, @MethodConst4=0, @MethodConst5=0, @MethodConst6=0, @MethodConst7=0, @MethodConst8=0, @MethodConst9=0, @MethodConst10=0, @MethodConst11=0, @MethodConst12=False, @MethodConst13=0, @MethodConst14=0, @MethodConst15=0, @MethodConst16=0, @MethodConst17=0, @MethodConst18=0, @MethodConst19=0]
[2025-09-07 21:38:54.898 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `d`.`id` AS `Id` , `d`.`map_id` AS `MapId` , `c`.`map_name` AS `MapName` , IFNULL(`d`.`limit_growth`,@MethodConst10) AS `LimitGrowth` , IFNULL(`d`.`limit_level`,@MethodConst11) AS `LimitLevel` , IFNULL(CAST(`d`.`limit_key` as SIGNED),@MethodConst12) AS `LimitKey` , IFNULL(`d`.`min_gold`,@MethodConst13) AS `MinGold` , IFNULL(`d`.`max_gold`,@MethodConst14) AS `MaxGold` , IFNULL(`d`.`min_yuanbao`,@MethodConst15) AS `MinYuanbao` , IFNULL(`d`.`max_yuanbao`,@MethodConst16) AS `MaxYuanbao` , IFNULL(`d`.`min_drop`,@MethodConst17) AS `MinDrop` , IFNULL(`d`.`max_drop`,@MethodConst18) AS `MaxDrop` , `d`.`icon` AS `Icon` , IFNULL(`d`.`type`,@MethodConst19) AS `Type`  FROM `map_detail` `d` Left JOIN `map_config` `c` ON ( `d`.`map_id` = `c`.`map_id` )     ORDER BY `d`.`id` ASC LIMIT 0,10
[2025-09-07 21:43:14.771 +08:00 INF] BMS: 🚀 应用程序启动，日志系统已配置
[2025-09-07 21:43:15.036 +08:00 DBG] BMS: 🔧 调试级别日志测试
[2025-09-07 21:43:15.068 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 21:43:15.071 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 21:43:15.227 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 21:43:15.713 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 21:43:15.723 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 21:43:15.725 +08:00 INF] BMS: ✅ DbContext 初始化成功，日志文件位置: logs/
[2025-09-07 21:43:16.787 +08:00 WRN] Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware: Failed to determine the https port for redirect.
[2025-09-07 21:43:24.118 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 21:43:24.121 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 21:43:24.125 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 21:43:24.169 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 21:43:24.174 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 21:43:24.199 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`map_id`,`map_name`,`map_desc`,`map_size`,`map_type`,`atlast_name`,`background`,`bgm`,`bgm_loop`,`bgm_volume`,`bgm_play`,`bgm_mute`,`bgm_pause`,`ico`,`type` FROM `map_config` ORDER BY `map_id` ASC 
[2025-09-07 21:43:24.245 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`map_id`,`map_name`,`map_desc`,`map_size`,`map_type`,`atlast_name`,`background`,`bgm`,`bgm_loop`,`bgm_volume`,`bgm_play`,`bgm_mute`,`bgm_pause`,`ico`,`type` FROM `map_config` ORDER BY `map_id` ASC 
[2025-09-07 21:43:24.357 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 21:43:24.361 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 21:43:24.383 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 21:43:24.385 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 21:43:24.387 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 21:43:24.389 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 21:43:24.430 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 21:43:24.432 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 21:43:24.436 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`map_id`,`map_name`,`map_desc`,`map_size`,`map_type`,`atlast_name`,`background`,`bgm`,`bgm_loop`,`bgm_volume`,`bgm_play`,`bgm_mute`,`bgm_pause`,`ico`,`type` FROM `map_config` ORDER BY `map_id` ASC 
[2025-09-07 21:43:24.476 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`map_id`,`map_name`,`map_desc`,`map_size`,`map_type`,`atlast_name`,`background`,`bgm`,`bgm_loop`,`bgm_volume`,`bgm_play`,`bgm_mute`,`bgm_pause`,`ico`,`type` FROM `map_config` ORDER BY `map_id` ASC 
[2025-09-07 21:43:24.636 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 21:43:24.638 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 21:43:24.770 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM `map_detail` `d` Left JOIN `map_config` `c` ON ( `d`.`map_id` = `c`.`map_id` )    | 参数: [@MethodConst0=0, @MethodConst1=0, @MethodConst2=False, @MethodConst3=0, @MethodConst4=0, @MethodConst5=0, @MethodConst6=0, @MethodConst7=0, @MethodConst8=0, @MethodConst9=0]
[2025-09-07 21:43:24.814 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM `map_detail` `d` Left JOIN `map_config` `c` ON ( `d`.`map_id` = `c`.`map_id` )   
[2025-09-07 21:43:24.818 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `d`.`id` AS `Id` , `d`.`map_id` AS `MapId` , `c`.`map_name` AS `MapName` , IFNULL(`d`.`limit_growth`,@MethodConst10) AS `LimitGrowth` , IFNULL(`d`.`limit_level`,@MethodConst11) AS `LimitLevel` , IFNULL(CAST(`d`.`limit_key` as SIGNED),@MethodConst12) AS `LimitKey` , IFNULL(`d`.`min_gold`,@MethodConst13) AS `MinGold` , IFNULL(`d`.`max_gold`,@MethodConst14) AS `MaxGold` , IFNULL(`d`.`min_yuanbao`,@MethodConst15) AS `MinYuanbao` , IFNULL(`d`.`max_yuanbao`,@MethodConst16) AS `MaxYuanbao` , IFNULL(`d`.`min_drop`,@MethodConst17) AS `MinDrop` , IFNULL(`d`.`max_drop`,@MethodConst18) AS `MaxDrop` , `d`.`icon` AS `Icon` , IFNULL(`d`.`type`,@MethodConst19) AS `Type`  FROM `map_detail` `d` Left JOIN `map_config` `c` ON ( `d`.`map_id` = `c`.`map_id` )     ORDER BY `d`.`id` ASC LIMIT 0,10 | 参数: [@MethodConst0=0, @MethodConst1=0, @MethodConst2=False, @MethodConst3=0, @MethodConst4=0, @MethodConst5=0, @MethodConst6=0, @MethodConst7=0, @MethodConst8=0, @MethodConst9=0, @MethodConst10=0, @MethodConst11=0, @MethodConst12=False, @MethodConst13=0, @MethodConst14=0, @MethodConst15=0, @MethodConst16=0, @MethodConst17=0, @MethodConst18=0, @MethodConst19=0]
[2025-09-07 21:43:24.861 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `d`.`id` AS `Id` , `d`.`map_id` AS `MapId` , `c`.`map_name` AS `MapName` , IFNULL(`d`.`limit_growth`,@MethodConst10) AS `LimitGrowth` , IFNULL(`d`.`limit_level`,@MethodConst11) AS `LimitLevel` , IFNULL(CAST(`d`.`limit_key` as SIGNED),@MethodConst12) AS `LimitKey` , IFNULL(`d`.`min_gold`,@MethodConst13) AS `MinGold` , IFNULL(`d`.`max_gold`,@MethodConst14) AS `MaxGold` , IFNULL(`d`.`min_yuanbao`,@MethodConst15) AS `MinYuanbao` , IFNULL(`d`.`max_yuanbao`,@MethodConst16) AS `MaxYuanbao` , IFNULL(`d`.`min_drop`,@MethodConst17) AS `MinDrop` , IFNULL(`d`.`max_drop`,@MethodConst18) AS `MaxDrop` , `d`.`icon` AS `Icon` , IFNULL(`d`.`type`,@MethodConst19) AS `Type`  FROM `map_detail` `d` Left JOIN `map_config` `c` ON ( `d`.`map_id` = `c`.`map_id` )     ORDER BY `d`.`id` ASC LIMIT 0,10
[2025-09-07 21:44:53.753 +08:00 INF] BMS: 🚀 应用程序启动，日志系统已配置
[2025-09-07 21:44:53.788 +08:00 DBG] BMS: 🔧 调试级别日志测试
[2025-09-07 21:44:53.806 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 21:44:53.836 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 21:44:53.922 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 21:44:54.496 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 21:44:54.530 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 21:44:54.539 +08:00 INF] BMS: ✅ DbContext 初始化成功，日志文件位置: logs/
[2025-09-07 21:44:55.801 +08:00 WRN] Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware: Failed to determine the https port for redirect.
[2025-09-07 21:44:58.996 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 21:44:59.012 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 21:44:59.017 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 21:44:59.092 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 21:44:59.096 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 21:44:59.123 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`map_id`,`map_name`,`map_desc`,`map_size`,`map_type`,`atlast_name`,`background`,`bgm`,`bgm_loop`,`bgm_volume`,`bgm_play`,`bgm_mute`,`bgm_pause`,`ico`,`type` FROM `map_config` ORDER BY `map_id` ASC 
[2025-09-07 21:44:59.175 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`map_id`,`map_name`,`map_desc`,`map_size`,`map_type`,`atlast_name`,`background`,`bgm`,`bgm_loop`,`bgm_volume`,`bgm_play`,`bgm_mute`,`bgm_pause`,`ico`,`type` FROM `map_config` ORDER BY `map_id` ASC 
[2025-09-07 21:44:59.362 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 21:44:59.366 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 21:44:59.378 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 21:44:59.380 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 21:44:59.384 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 21:44:59.392 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 21:44:59.442 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 21:44:59.443 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 21:44:59.446 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`map_id`,`map_name`,`map_desc`,`map_size`,`map_type`,`atlast_name`,`background`,`bgm`,`bgm_loop`,`bgm_volume`,`bgm_play`,`bgm_mute`,`bgm_pause`,`ico`,`type` FROM `map_config` ORDER BY `map_id` ASC 
[2025-09-07 21:44:59.494 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`map_id`,`map_name`,`map_desc`,`map_size`,`map_type`,`atlast_name`,`background`,`bgm`,`bgm_loop`,`bgm_volume`,`bgm_play`,`bgm_mute`,`bgm_pause`,`ico`,`type` FROM `map_config` ORDER BY `map_id` ASC 
[2025-09-07 21:44:59.629 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 21:44:59.631 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 21:44:59.745 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM `map_detail` `d` Left JOIN `map_config` `c` ON ( `d`.`map_id` = `c`.`map_id` )    | 参数: [@MethodConst0=0, @MethodConst1=0, @MethodConst2=False, @MethodConst3=0, @MethodConst4=0, @MethodConst5=0, @MethodConst6=0, @MethodConst7=0, @MethodConst8=0, @MethodConst9=0]
[2025-09-07 21:44:59.784 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM `map_detail` `d` Left JOIN `map_config` `c` ON ( `d`.`map_id` = `c`.`map_id` )   
[2025-09-07 21:44:59.790 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `d`.`id` AS `Id` , `d`.`map_id` AS `MapId` , `c`.`map_name` AS `MapName` , IFNULL(`d`.`limit_growth`,@MethodConst10) AS `LimitGrowth` , IFNULL(`d`.`limit_level`,@MethodConst11) AS `LimitLevel` , IFNULL(CAST(`d`.`limit_key` as SIGNED),@MethodConst12) AS `LimitKey` , IFNULL(`d`.`min_gold`,@MethodConst13) AS `MinGold` , IFNULL(`d`.`max_gold`,@MethodConst14) AS `MaxGold` , IFNULL(`d`.`min_yuanbao`,@MethodConst15) AS `MinYuanbao` , IFNULL(`d`.`max_yuanbao`,@MethodConst16) AS `MaxYuanbao` , IFNULL(`d`.`min_drop`,@MethodConst17) AS `MinDrop` , IFNULL(`d`.`max_drop`,@MethodConst18) AS `MaxDrop` , `d`.`icon` AS `Icon` , IFNULL(`d`.`type`,@MethodConst19) AS `Type`  FROM `map_detail` `d` Left JOIN `map_config` `c` ON ( `d`.`map_id` = `c`.`map_id` )     ORDER BY `d`.`id` ASC LIMIT 0,10 | 参数: [@MethodConst0=0, @MethodConst1=0, @MethodConst2=False, @MethodConst3=0, @MethodConst4=0, @MethodConst5=0, @MethodConst6=0, @MethodConst7=0, @MethodConst8=0, @MethodConst9=0, @MethodConst10=0, @MethodConst11=0, @MethodConst12=False, @MethodConst13=0, @MethodConst14=0, @MethodConst15=0, @MethodConst16=0, @MethodConst17=0, @MethodConst18=0, @MethodConst19=0]
[2025-09-07 21:44:59.830 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `d`.`id` AS `Id` , `d`.`map_id` AS `MapId` , `c`.`map_name` AS `MapName` , IFNULL(`d`.`limit_growth`,@MethodConst10) AS `LimitGrowth` , IFNULL(`d`.`limit_level`,@MethodConst11) AS `LimitLevel` , IFNULL(CAST(`d`.`limit_key` as SIGNED),@MethodConst12) AS `LimitKey` , IFNULL(`d`.`min_gold`,@MethodConst13) AS `MinGold` , IFNULL(`d`.`max_gold`,@MethodConst14) AS `MaxGold` , IFNULL(`d`.`min_yuanbao`,@MethodConst15) AS `MinYuanbao` , IFNULL(`d`.`max_yuanbao`,@MethodConst16) AS `MaxYuanbao` , IFNULL(`d`.`min_drop`,@MethodConst17) AS `MinDrop` , IFNULL(`d`.`max_drop`,@MethodConst18) AS `MaxDrop` , `d`.`icon` AS `Icon` , IFNULL(`d`.`type`,@MethodConst19) AS `Type`  FROM `map_detail` `d` Left JOIN `map_config` `c` ON ( `d`.`map_id` = `c`.`map_id` )     ORDER BY `d`.`id` ASC LIMIT 0,10
[2025-09-07 21:45:45.048 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 21:45:45.050 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 21:45:45.053 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 21:45:45.089 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 21:45:45.093 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 21:45:45.096 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`map_id`,`map_name`,`map_desc`,`map_size`,`map_type`,`atlast_name`,`background`,`bgm`,`bgm_loop`,`bgm_volume`,`bgm_play`,`bgm_mute`,`bgm_pause`,`ico`,`type` FROM `map_config` ORDER BY `map_id` ASC 
[2025-09-07 21:45:45.134 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`map_id`,`map_name`,`map_desc`,`map_size`,`map_type`,`atlast_name`,`background`,`bgm`,`bgm_loop`,`bgm_volume`,`bgm_play`,`bgm_mute`,`bgm_pause`,`ico`,`type` FROM `map_config` ORDER BY `map_id` ASC 
[2025-09-07 21:45:45.188 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 21:45:45.188 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 21:45:45.189 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 21:45:45.191 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 21:45:45.193 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 21:45:45.194 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 21:45:45.231 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 21:45:45.233 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 21:45:45.237 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM `map_detail` `d` Left JOIN `map_config` `c` ON ( `d`.`map_id` = `c`.`map_id` )    | 参数: [@MethodConst0=0, @MethodConst1=0, @MethodConst2=False, @MethodConst3=0, @MethodConst4=0, @MethodConst5=0, @MethodConst6=0, @MethodConst7=0, @MethodConst8=0, @MethodConst9=0]
[2025-09-07 21:45:45.247 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 21:45:45.248 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 21:45:45.250 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`map_id`,`map_name`,`map_desc`,`map_size`,`map_type`,`atlast_name`,`background`,`bgm`,`bgm_loop`,`bgm_volume`,`bgm_play`,`bgm_mute`,`bgm_pause`,`ico`,`type` FROM `map_config` ORDER BY `map_id` ASC 
[2025-09-07 21:45:45.274 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM `map_detail` `d` Left JOIN `map_config` `c` ON ( `d`.`map_id` = `c`.`map_id` )   
[2025-09-07 21:45:45.277 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `d`.`id` AS `Id` , `d`.`map_id` AS `MapId` , `c`.`map_name` AS `MapName` , IFNULL(`d`.`limit_growth`,@MethodConst10) AS `LimitGrowth` , IFNULL(`d`.`limit_level`,@MethodConst11) AS `LimitLevel` , IFNULL(CAST(`d`.`limit_key` as SIGNED),@MethodConst12) AS `LimitKey` , IFNULL(`d`.`min_gold`,@MethodConst13) AS `MinGold` , IFNULL(`d`.`max_gold`,@MethodConst14) AS `MaxGold` , IFNULL(`d`.`min_yuanbao`,@MethodConst15) AS `MinYuanbao` , IFNULL(`d`.`max_yuanbao`,@MethodConst16) AS `MaxYuanbao` , IFNULL(`d`.`min_drop`,@MethodConst17) AS `MinDrop` , IFNULL(`d`.`max_drop`,@MethodConst18) AS `MaxDrop` , `d`.`icon` AS `Icon` , IFNULL(`d`.`type`,@MethodConst19) AS `Type`  FROM `map_detail` `d` Left JOIN `map_config` `c` ON ( `d`.`map_id` = `c`.`map_id` )     ORDER BY `d`.`id` ASC LIMIT 0,10 | 参数: [@MethodConst0=0, @MethodConst1=0, @MethodConst2=False, @MethodConst3=0, @MethodConst4=0, @MethodConst5=0, @MethodConst6=0, @MethodConst7=0, @MethodConst8=0, @MethodConst9=0, @MethodConst10=0, @MethodConst11=0, @MethodConst12=False, @MethodConst13=0, @MethodConst14=0, @MethodConst15=0, @MethodConst16=0, @MethodConst17=0, @MethodConst18=0, @MethodConst19=0]
[2025-09-07 21:45:45.298 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`map_id`,`map_name`,`map_desc`,`map_size`,`map_type`,`atlast_name`,`background`,`bgm`,`bgm_loop`,`bgm_volume`,`bgm_play`,`bgm_mute`,`bgm_pause`,`ico`,`type` FROM `map_config` ORDER BY `map_id` ASC 
[2025-09-07 21:45:45.314 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `d`.`id` AS `Id` , `d`.`map_id` AS `MapId` , `c`.`map_name` AS `MapName` , IFNULL(`d`.`limit_growth`,@MethodConst10) AS `LimitGrowth` , IFNULL(`d`.`limit_level`,@MethodConst11) AS `LimitLevel` , IFNULL(CAST(`d`.`limit_key` as SIGNED),@MethodConst12) AS `LimitKey` , IFNULL(`d`.`min_gold`,@MethodConst13) AS `MinGold` , IFNULL(`d`.`max_gold`,@MethodConst14) AS `MaxGold` , IFNULL(`d`.`min_yuanbao`,@MethodConst15) AS `MinYuanbao` , IFNULL(`d`.`max_yuanbao`,@MethodConst16) AS `MaxYuanbao` , IFNULL(`d`.`min_drop`,@MethodConst17) AS `MinDrop` , IFNULL(`d`.`max_drop`,@MethodConst18) AS `MaxDrop` , `d`.`icon` AS `Icon` , IFNULL(`d`.`type`,@MethodConst19) AS `Type`  FROM `map_detail` `d` Left JOIN `map_config` `c` ON ( `d`.`map_id` = `c`.`map_id` )     ORDER BY `d`.`id` ASC LIMIT 0,10
[2025-09-07 21:46:11.640 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 21:46:11.641 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 21:46:11.643 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 21:46:11.679 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 21:46:11.683 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 21:46:11.687 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`map_id`,`map_name`,`map_desc`,`map_size`,`map_type`,`atlast_name`,`background`,`bgm`,`bgm_loop`,`bgm_volume`,`bgm_play`,`bgm_mute`,`bgm_pause`,`ico`,`type` FROM `map_config` ORDER BY `map_id` ASC 
[2025-09-07 21:46:11.724 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`map_id`,`map_name`,`map_desc`,`map_size`,`map_type`,`atlast_name`,`background`,`bgm`,`bgm_loop`,`bgm_volume`,`bgm_play`,`bgm_mute`,`bgm_pause`,`ico`,`type` FROM `map_config` ORDER BY `map_id` ASC 
[2025-09-07 21:46:11.786 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 21:46:11.787 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 21:46:11.789 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 21:46:11.825 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 21:46:11.827 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 21:46:11.829 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`map_id`,`map_name`,`map_desc`,`map_size`,`map_type`,`atlast_name`,`background`,`bgm`,`bgm_loop`,`bgm_volume`,`bgm_play`,`bgm_mute`,`bgm_pause`,`ico`,`type` FROM `map_config` ORDER BY `map_id` ASC 
[2025-09-07 21:46:11.876 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`map_id`,`map_name`,`map_desc`,`map_size`,`map_type`,`atlast_name`,`background`,`bgm`,`bgm_loop`,`bgm_volume`,`bgm_play`,`bgm_mute`,`bgm_pause`,`ico`,`type` FROM `map_config` ORDER BY `map_id` ASC 
[2025-09-07 21:46:11.888 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 21:46:11.889 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 21:46:11.891 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 21:46:11.928 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 21:46:11.977 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 21:46:11.981 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`monster_no`,`skill`,`name`,`attribute` FROM `monster_config` ORDER BY `monster_no` ASC 
[2025-09-07 21:46:12.099 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`monster_no`,`skill`,`name`,`attribute` FROM `monster_config` ORDER BY `monster_no` ASC 
[2025-09-07 21:46:12.113 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 21:46:12.115 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 21:46:12.120 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 21:46:12.165 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 21:46:12.296 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 21:46:12.319 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `map_monster`  
[2025-09-07 21:46:12.366 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `map_monster`  
[2025-09-07 21:46:12.385 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`map_id`,`monster_id`,`monster_name`,`growth`,`element`,`max_level`,`min_level`,`max_drop`,`exp`,`hp`,`mp`,`max_hp`,`max_mp`,`atk`,`def`,`dodge`,`spd`,`hit`,`deepen`,`offset`,`vamp`,`vamp_mp`,`skill_list`,`drop_items` FROM `map_monster`    ORDER BY `map_id` ASC,`monster_id` ASC LIMIT 0,10
[2025-09-07 21:46:12.428 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`map_id`,`monster_id`,`monster_name`,`growth`,`element`,`max_level`,`min_level`,`max_drop`,`exp`,`hp`,`mp`,`max_hp`,`max_mp`,`atk`,`def`,`dodge`,`spd`,`hit`,`deepen`,`offset`,`vamp`,`vamp_mp`,`skill_list`,`drop_items` FROM `map_monster`    ORDER BY `map_id` ASC,`monster_id` ASC LIMIT 0,10
[2025-09-07 21:46:12.501 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`map_id`,`map_name`,`map_desc`,`map_size`,`map_type`,`atlast_name`,`background`,`bgm`,`bgm_loop`,`bgm_volume`,`bgm_play`,`bgm_mute`,`bgm_pause`,`ico`,`type` FROM `map_config`  WHERE  (`map_id` IN (100,101,102,103,104,105,106,107,108))  
[2025-09-07 21:46:12.541 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`map_id`,`map_name`,`map_desc`,`map_size`,`map_type`,`atlast_name`,`background`,`bgm`,`bgm_loop`,`bgm_volume`,`bgm_play`,`bgm_mute`,`bgm_pause`,`ico`,`type` FROM `map_config`  WHERE  (`map_id` IN (100,101,102,103,104,105,106,107,108))  
[2025-09-07 21:46:23.121 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 21:46:23.123 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 21:46:23.126 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 21:46:23.162 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 21:46:23.183 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 21:46:23.195 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `equip_type_id` AS `EquipTypeId` , `type_name` AS `TypeName`  FROM `equipment_type` ORDER BY `id` ASC 
[2025-09-07 21:46:23.235 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `equip_type_id` AS `EquipTypeId` , `type_name` AS `TypeName`  FROM `equipment_type` ORDER BY `id` ASC 
[2025-09-07 21:46:23.339 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 21:46:23.341 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 21:46:23.344 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 21:46:23.385 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 21:46:23.388 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 21:46:23.484 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  
[2025-09-07 21:46:23.901 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  
[2025-09-07 21:46:23.928 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment_detail`  
[2025-09-07 21:46:23.970 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment_detail`  
[2025-09-07 21:46:23.973 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment_type`  
[2025-09-07 21:46:24.008 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment_type`  
[2025-09-07 21:46:24.023 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )   
[2025-09-07 21:46:24.067 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )   
[2025-09-07 21:46:24.070 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `e`.`equip_id` AS `EquipId` , @constant14 AS `ClassId` , `e`.`name` AS `Name` , `e`.`icon` AS `Icon` , `e`.`equip_type_id` AS `EquipTypeId` , `et`.`type_name` AS `EquipTypeName` , `e`.`element` AS `Element` , IFNULL(`e`.`slot`,@MethodConst15) AS `Slot` , IFNULL(`e`.`strengthen_level`,@MethodConst16) AS `StrengthenLevel` , `e`.`suit_id` AS `SuitId` , IFNULL(`ed`.`atk`,@MethodConst17) AS `Atk` , IFNULL(`ed`.`hit`,@MethodConst18) AS `Hit` , IFNULL(`ed`.`def`,@MethodConst19) AS `Def` , IFNULL(`ed`.`spd`,@MethodConst20) AS `Spd` , IFNULL(`ed`.`dodge`,@MethodConst21) AS `Dodge` , IFNULL(`ed`.`hp`,@MethodConst22) AS `Hp` , IFNULL(`ed`.`mp`,@MethodConst23) AS `Mp` , IFNULL(`ed`.`deepen`,@MethodConst24) AS `Deepen` , IFNULL(`ed`.`offset`,@MethodConst25) AS `Offset` , IFNULL(`ed`.`vamp`,@MethodConst26) AS `Vamp` , IFNULL(`ed`.`vamp_mp`,@MethodConst27) AS `VampMp` , `ed`.`description` AS `Description` , `ed`.`main_attr` AS `MainAttr` , `ed`.`main_attr_value` AS `MainAttrValue` , `ed`.`sub_attr` AS `SubAttr` , `ed`.`sub_attr_value` AS `SubAttrValue` , `ed`.`element_limit` AS `ElementLimit` , `ed`.`equip_name` AS `EquipName`  FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )     ORDER BY `e`.`equip_id` ASC LIMIT 0,10 | 参数: [@constant0=, @MethodConst1=0, @MethodConst2=0, @MethodConst3=0, @MethodConst4=0, @MethodConst5=0, @MethodConst6=0, @MethodConst7=0, @MethodConst8=0, @MethodConst9=0, @MethodConst10=0, @MethodConst11=0, @MethodConst12=0, @MethodConst13=0, @constant14=, @MethodConst15=0, @MethodConst16=0, @MethodConst17=0, @MethodConst18=0, @MethodConst19=0, @MethodConst20=0, @MethodConst21=0, @MethodConst22=0, @MethodConst23=0, @MethodConst24=0, @MethodConst25=0, @MethodConst26=0, @MethodConst27=0]
[2025-09-07 21:46:24.123 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `e`.`equip_id` AS `EquipId` , @constant14 AS `ClassId` , `e`.`name` AS `Name` , `e`.`icon` AS `Icon` , `e`.`equip_type_id` AS `EquipTypeId` , `et`.`type_name` AS `EquipTypeName` , `e`.`element` AS `Element` , IFNULL(`e`.`slot`,@MethodConst15) AS `Slot` , IFNULL(`e`.`strengthen_level`,@MethodConst16) AS `StrengthenLevel` , `e`.`suit_id` AS `SuitId` , IFNULL(`ed`.`atk`,@MethodConst17) AS `Atk` , IFNULL(`ed`.`hit`,@MethodConst18) AS `Hit` , IFNULL(`ed`.`def`,@MethodConst19) AS `Def` , IFNULL(`ed`.`spd`,@MethodConst20) AS `Spd` , IFNULL(`ed`.`dodge`,@MethodConst21) AS `Dodge` , IFNULL(`ed`.`hp`,@MethodConst22) AS `Hp` , IFNULL(`ed`.`mp`,@MethodConst23) AS `Mp` , IFNULL(`ed`.`deepen`,@MethodConst24) AS `Deepen` , IFNULL(`ed`.`offset`,@MethodConst25) AS `Offset` , IFNULL(`ed`.`vamp`,@MethodConst26) AS `Vamp` , IFNULL(`ed`.`vamp_mp`,@MethodConst27) AS `VampMp` , `ed`.`description` AS `Description` , `ed`.`main_attr` AS `MainAttr` , `ed`.`main_attr_value` AS `MainAttrValue` , `ed`.`sub_attr` AS `SubAttr` , `ed`.`sub_attr_value` AS `SubAttrValue` , `ed`.`element_limit` AS `ElementLimit` , `ed`.`equip_name` AS `EquipName`  FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )     ORDER BY `e`.`equip_id` ASC LIMIT 0,10
[2025-09-07 21:46:24.145 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 21:46:24.148 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 21:46:24.149 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 21:46:24.185 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 21:46:24.187 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 21:46:24.192 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment_type`  
[2025-09-07 21:46:24.227 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment_type`  
[2025-09-07 21:46:24.230 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `id` AS `Id` , `equip_type_id` AS `EquipTypeId` , `type_name` AS `TypeName` , @constant6 AS `Description` , @constant7 AS `SortOrder` , @constant8 AS `IsActive` , @constant9 AS `CreateTime` , @constant10 AS `UpdateTime` , @constant11 AS `EquipmentCount`  FROM `equipment_type`    ORDER BY `id` ASC LIMIT 0,10 | 参数: [@constant0=, @constant1=0, @constant2=True, @constant3=, @constant4=, @constant5=0, @constant6=, @constant7=0, @constant8=True, @constant9=, @constant10=, @constant11=0]
[2025-09-07 21:46:24.267 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `id` AS `Id` , `equip_type_id` AS `EquipTypeId` , `type_name` AS `TypeName` , @constant6 AS `Description` , @constant7 AS `SortOrder` , @constant8 AS `IsActive` , @constant9 AS `CreateTime` , @constant10 AS `UpdateTime` , @constant11 AS `EquipmentCount`  FROM `equipment_type`    ORDER BY `id` ASC LIMIT 0,10
[2025-09-07 21:46:24.278 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=1]
[2025-09-07 21:46:24.316 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-09-07 21:46:24.322 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=2]
[2025-09-07 21:46:24.360 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-09-07 21:46:24.371 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=3]
[2025-09-07 21:46:24.406 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-09-07 21:46:24.409 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=4]
[2025-09-07 21:46:24.443 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-09-07 21:46:24.450 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=5]
[2025-09-07 21:46:24.486 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-09-07 21:46:24.489 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=6]
[2025-09-07 21:46:24.526 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-09-07 21:46:24.556 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=7]
[2025-09-07 21:46:24.594 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-09-07 21:46:24.601 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=8]
[2025-09-07 21:46:24.637 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-09-07 21:46:24.641 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=9]
[2025-09-07 21:46:24.677 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-09-07 21:46:24.685 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=10]
[2025-09-07 21:46:24.720 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-09-07 21:50:16.963 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 21:50:16.968 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 21:50:16.977 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 21:50:17.268 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 21:50:17.270 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 21:50:17.277 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  
[2025-09-07 21:50:17.320 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  
[2025-09-07 21:50:17.324 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment_detail`  
[2025-09-07 21:50:17.369 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment_detail`  
[2025-09-07 21:50:17.371 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment_type`  
[2025-09-07 21:50:17.415 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment_type`  
[2025-09-07 21:50:17.423 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )   
[2025-09-07 21:50:17.474 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )   
[2025-09-07 21:50:17.479 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `e`.`equip_id` AS `EquipId` , @constant14 AS `ClassId` , `e`.`name` AS `Name` , `e`.`icon` AS `Icon` , `e`.`equip_type_id` AS `EquipTypeId` , `et`.`type_name` AS `EquipTypeName` , `e`.`element` AS `Element` , IFNULL(`e`.`slot`,@MethodConst15) AS `Slot` , IFNULL(`e`.`strengthen_level`,@MethodConst16) AS `StrengthenLevel` , `e`.`suit_id` AS `SuitId` , IFNULL(`ed`.`atk`,@MethodConst17) AS `Atk` , IFNULL(`ed`.`hit`,@MethodConst18) AS `Hit` , IFNULL(`ed`.`def`,@MethodConst19) AS `Def` , IFNULL(`ed`.`spd`,@MethodConst20) AS `Spd` , IFNULL(`ed`.`dodge`,@MethodConst21) AS `Dodge` , IFNULL(`ed`.`hp`,@MethodConst22) AS `Hp` , IFNULL(`ed`.`mp`,@MethodConst23) AS `Mp` , IFNULL(`ed`.`deepen`,@MethodConst24) AS `Deepen` , IFNULL(`ed`.`offset`,@MethodConst25) AS `Offset` , IFNULL(`ed`.`vamp`,@MethodConst26) AS `Vamp` , IFNULL(`ed`.`vamp_mp`,@MethodConst27) AS `VampMp` , `ed`.`description` AS `Description` , `ed`.`main_attr` AS `MainAttr` , `ed`.`main_attr_value` AS `MainAttrValue` , `ed`.`sub_attr` AS `SubAttr` , `ed`.`sub_attr_value` AS `SubAttrValue` , `ed`.`element_limit` AS `ElementLimit` , `ed`.`equip_name` AS `EquipName`  FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )     ORDER BY `e`.`equip_id` ASC LIMIT 10,10 | 参数: [@constant0=, @MethodConst1=0, @MethodConst2=0, @MethodConst3=0, @MethodConst4=0, @MethodConst5=0, @MethodConst6=0, @MethodConst7=0, @MethodConst8=0, @MethodConst9=0, @MethodConst10=0, @MethodConst11=0, @MethodConst12=0, @MethodConst13=0, @constant14=, @MethodConst15=0, @MethodConst16=0, @MethodConst17=0, @MethodConst18=0, @MethodConst19=0, @MethodConst20=0, @MethodConst21=0, @MethodConst22=0, @MethodConst23=0, @MethodConst24=0, @MethodConst25=0, @MethodConst26=0, @MethodConst27=0]
[2025-09-07 21:50:17.573 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `e`.`equip_id` AS `EquipId` , @constant14 AS `ClassId` , `e`.`name` AS `Name` , `e`.`icon` AS `Icon` , `e`.`equip_type_id` AS `EquipTypeId` , `et`.`type_name` AS `EquipTypeName` , `e`.`element` AS `Element` , IFNULL(`e`.`slot`,@MethodConst15) AS `Slot` , IFNULL(`e`.`strengthen_level`,@MethodConst16) AS `StrengthenLevel` , `e`.`suit_id` AS `SuitId` , IFNULL(`ed`.`atk`,@MethodConst17) AS `Atk` , IFNULL(`ed`.`hit`,@MethodConst18) AS `Hit` , IFNULL(`ed`.`def`,@MethodConst19) AS `Def` , IFNULL(`ed`.`spd`,@MethodConst20) AS `Spd` , IFNULL(`ed`.`dodge`,@MethodConst21) AS `Dodge` , IFNULL(`ed`.`hp`,@MethodConst22) AS `Hp` , IFNULL(`ed`.`mp`,@MethodConst23) AS `Mp` , IFNULL(`ed`.`deepen`,@MethodConst24) AS `Deepen` , IFNULL(`ed`.`offset`,@MethodConst25) AS `Offset` , IFNULL(`ed`.`vamp`,@MethodConst26) AS `Vamp` , IFNULL(`ed`.`vamp_mp`,@MethodConst27) AS `VampMp` , `ed`.`description` AS `Description` , `ed`.`main_attr` AS `MainAttr` , `ed`.`main_attr_value` AS `MainAttrValue` , `ed`.`sub_attr` AS `SubAttr` , `ed`.`sub_attr_value` AS `SubAttrValue` , `ed`.`element_limit` AS `ElementLimit` , `ed`.`equip_name` AS `EquipName`  FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )     ORDER BY `e`.`equip_id` ASC LIMIT 10,10
[2025-09-07 21:52:06.957 +08:00 INF] BMS: 🚀 应用程序启动，日志系统已配置
[2025-09-07 21:52:07.008 +08:00 DBG] BMS: 🔧 调试级别日志测试
[2025-09-07 21:52:07.027 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 21:52:07.030 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 21:52:07.121 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 21:52:07.578 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 21:52:07.587 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 21:52:07.589 +08:00 INF] BMS: ✅ DbContext 初始化成功，日志文件位置: logs/
[2025-09-07 21:52:08.667 +08:00 WRN] Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware: Failed to determine the https port for redirect.
[2025-09-07 21:52:13.624 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 21:52:13.625 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 21:52:13.628 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 21:52:13.660 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 21:52:13.662 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 21:52:13.692 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `equip_type_id` AS `EquipTypeId` , `type_name` AS `TypeName`  FROM `equipment_type` ORDER BY `id` ASC 
[2025-09-07 21:52:13.729 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `equip_type_id` AS `EquipTypeId` , `type_name` AS `TypeName`  FROM `equipment_type` ORDER BY `id` ASC 
[2025-09-07 21:52:13.826 +08:00 WRN] Microsoft.WebTools.BrowserLink.Net.BrowserLinkMiddleware: Unable to configure Browser Link script injection on the response. 
[2025-09-07 21:52:13.828 +08:00 WRN] Microsoft.AspNetCore.Watch.BrowserRefresh.BrowserRefreshMiddleware: Unable to configure browser refresh script injection on the response. Consider manually adding '<script src="/_framework/aspnetcore-browser-refresh.js"></script>' to the body of the page.
[2025-09-07 21:52:13.883 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 21:52:13.886 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 21:52:13.888 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 21:52:13.920 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 21:52:13.922 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 21:52:14.020 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  
[2025-09-07 21:52:14.056 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  
[2025-09-07 21:52:14.066 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment_detail`  
[2025-09-07 21:52:14.104 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment_detail`  
[2025-09-07 21:52:14.181 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment_type`  
[2025-09-07 21:52:14.305 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment_type`  
[2025-09-07 21:52:14.432 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )   
[2025-09-07 21:52:14.485 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )   
[2025-09-07 21:52:14.505 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `e`.`equip_id` AS `EquipId` , @constant14 AS `ClassId` , `e`.`name` AS `Name` , `e`.`icon` AS `Icon` , `e`.`equip_type_id` AS `EquipTypeId` , `et`.`type_name` AS `EquipTypeName` , `e`.`element` AS `Element` , IFNULL(`e`.`slot`,@MethodConst15) AS `Slot` , IFNULL(`e`.`strengthen_level`,@MethodConst16) AS `StrengthenLevel` , `e`.`suit_id` AS `SuitId` , IFNULL(`ed`.`atk`,@MethodConst17) AS `Atk` , IFNULL(`ed`.`hit`,@MethodConst18) AS `Hit` , IFNULL(`ed`.`def`,@MethodConst19) AS `Def` , IFNULL(`ed`.`spd`,@MethodConst20) AS `Spd` , IFNULL(`ed`.`dodge`,@MethodConst21) AS `Dodge` , IFNULL(`ed`.`hp`,@MethodConst22) AS `Hp` , IFNULL(`ed`.`mp`,@MethodConst23) AS `Mp` , IFNULL(`ed`.`deepen`,@MethodConst24) AS `Deepen` , IFNULL(`ed`.`offset`,@MethodConst25) AS `Offset` , IFNULL(`ed`.`vamp`,@MethodConst26) AS `Vamp` , IFNULL(`ed`.`vamp_mp`,@MethodConst27) AS `VampMp` , `ed`.`description` AS `Description` , `ed`.`main_attr` AS `MainAttr` , `ed`.`main_attr_value` AS `MainAttrValue` , `ed`.`sub_attr` AS `SubAttr` , `ed`.`sub_attr_value` AS `SubAttrValue` , `ed`.`element_limit` AS `ElementLimit` , `ed`.`equip_name` AS `EquipName`  FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )     ORDER BY `e`.`equip_id` ASC LIMIT 0,10 | 参数: [@constant0=, @MethodConst1=0, @MethodConst2=0, @MethodConst3=0, @MethodConst4=0, @MethodConst5=0, @MethodConst6=0, @MethodConst7=0, @MethodConst8=0, @MethodConst9=0, @MethodConst10=0, @MethodConst11=0, @MethodConst12=0, @MethodConst13=0, @constant14=, @MethodConst15=0, @MethodConst16=0, @MethodConst17=0, @MethodConst18=0, @MethodConst19=0, @MethodConst20=0, @MethodConst21=0, @MethodConst22=0, @MethodConst23=0, @MethodConst24=0, @MethodConst25=0, @MethodConst26=0, @MethodConst27=0]
[2025-09-07 21:52:14.561 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `e`.`equip_id` AS `EquipId` , @constant14 AS `ClassId` , `e`.`name` AS `Name` , `e`.`icon` AS `Icon` , `e`.`equip_type_id` AS `EquipTypeId` , `et`.`type_name` AS `EquipTypeName` , `e`.`element` AS `Element` , IFNULL(`e`.`slot`,@MethodConst15) AS `Slot` , IFNULL(`e`.`strengthen_level`,@MethodConst16) AS `StrengthenLevel` , `e`.`suit_id` AS `SuitId` , IFNULL(`ed`.`atk`,@MethodConst17) AS `Atk` , IFNULL(`ed`.`hit`,@MethodConst18) AS `Hit` , IFNULL(`ed`.`def`,@MethodConst19) AS `Def` , IFNULL(`ed`.`spd`,@MethodConst20) AS `Spd` , IFNULL(`ed`.`dodge`,@MethodConst21) AS `Dodge` , IFNULL(`ed`.`hp`,@MethodConst22) AS `Hp` , IFNULL(`ed`.`mp`,@MethodConst23) AS `Mp` , IFNULL(`ed`.`deepen`,@MethodConst24) AS `Deepen` , IFNULL(`ed`.`offset`,@MethodConst25) AS `Offset` , IFNULL(`ed`.`vamp`,@MethodConst26) AS `Vamp` , IFNULL(`ed`.`vamp_mp`,@MethodConst27) AS `VampMp` , `ed`.`description` AS `Description` , `ed`.`main_attr` AS `MainAttr` , `ed`.`main_attr_value` AS `MainAttrValue` , `ed`.`sub_attr` AS `SubAttr` , `ed`.`sub_attr_value` AS `SubAttrValue` , `ed`.`element_limit` AS `ElementLimit` , `ed`.`equip_name` AS `EquipName`  FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )     ORDER BY `e`.`equip_id` ASC LIMIT 0,10
[2025-09-07 21:52:14.602 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 21:52:14.604 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 21:52:14.605 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 21:52:14.636 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 21:52:14.638 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 21:52:14.646 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment_type`  
[2025-09-07 21:52:14.677 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment_type`  
[2025-09-07 21:52:14.680 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `id` AS `Id` , `equip_type_id` AS `EquipTypeId` , `type_name` AS `TypeName` , @constant6 AS `Description` , @constant7 AS `SortOrder` , @constant8 AS `IsActive` , @constant9 AS `CreateTime` , @constant10 AS `UpdateTime` , @constant11 AS `EquipmentCount`  FROM `equipment_type`    ORDER BY `id` ASC LIMIT 0,10 | 参数: [@constant0=, @constant1=0, @constant2=True, @constant3=, @constant4=, @constant5=0, @constant6=, @constant7=0, @constant8=True, @constant9=, @constant10=, @constant11=0]
[2025-09-07 21:52:14.713 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `id` AS `Id` , `equip_type_id` AS `EquipTypeId` , `type_name` AS `TypeName` , @constant6 AS `Description` , @constant7 AS `SortOrder` , @constant8 AS `IsActive` , @constant9 AS `CreateTime` , @constant10 AS `UpdateTime` , @constant11 AS `EquipmentCount`  FROM `equipment_type`    ORDER BY `id` ASC LIMIT 0,10
[2025-09-07 21:52:14.727 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=1]
[2025-09-07 21:52:14.759 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-09-07 21:52:14.766 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=2]
[2025-09-07 21:52:14.801 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-09-07 21:52:14.806 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=3]
[2025-09-07 21:52:14.840 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-09-07 21:52:14.843 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=4]
[2025-09-07 21:52:14.874 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-09-07 21:52:14.877 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=5]
[2025-09-07 21:52:14.909 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-09-07 21:52:14.912 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=6]
[2025-09-07 21:52:14.943 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-09-07 21:52:14.946 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=7]
[2025-09-07 21:52:14.977 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-09-07 21:52:14.980 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=8]
[2025-09-07 21:52:15.011 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-09-07 21:52:15.013 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=9]
[2025-09-07 21:52:15.043 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-09-07 21:52:15.045 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=10]
[2025-09-07 21:52:15.077 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-09-07 22:00:14.617 +08:00 INF] BMS: 🚀 应用程序启动，日志系统已配置
[2025-09-07 22:00:14.650 +08:00 DBG] BMS: 🔧 调试级别日志测试
[2025-09-07 22:00:14.668 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 22:00:14.672 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 22:00:14.753 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 22:00:15.282 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 22:00:15.291 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 22:00:15.293 +08:00 INF] BMS: ✅ DbContext 初始化成功，日志文件位置: logs/
[2025-09-07 22:00:16.459 +08:00 WRN] Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware: Failed to determine the https port for redirect.
[2025-09-07 22:00:21.750 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 22:00:21.753 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 22:00:21.758 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 22:00:21.794 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 22:00:21.797 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 22:00:21.827 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `equip_type_id` AS `EquipTypeId` , `type_name` AS `TypeName`  FROM `equipment_type` ORDER BY `id` ASC 
[2025-09-07 22:00:21.863 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `equip_type_id` AS `EquipTypeId` , `type_name` AS `TypeName`  FROM `equipment_type` ORDER BY `id` ASC 
[2025-09-07 22:00:22.021 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 22:00:22.024 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 22:00:22.025 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 22:00:22.059 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 22:00:22.062 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 22:00:22.173 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  
[2025-09-07 22:00:22.206 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  
[2025-09-07 22:00:22.215 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment_detail`  
[2025-09-07 22:00:22.247 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment_detail`  
[2025-09-07 22:00:22.250 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment_type`  
[2025-09-07 22:00:22.291 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment_type`  
[2025-09-07 22:00:22.308 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )   
[2025-09-07 22:00:22.354 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )   
[2025-09-07 22:00:22.376 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `e`.`equip_id` AS `EquipId` , @constant14 AS `ClassId` , `e`.`name` AS `Name` , `e`.`icon` AS `Icon` , `e`.`equip_type_id` AS `EquipTypeId` , `et`.`type_name` AS `EquipTypeName` , `e`.`element` AS `Element` , IFNULL(`e`.`slot`,@MethodConst15) AS `Slot` , IFNULL(`e`.`strengthen_level`,@MethodConst16) AS `StrengthenLevel` , `e`.`suit_id` AS `SuitId` , IFNULL(`ed`.`atk`,@MethodConst17) AS `Atk` , IFNULL(`ed`.`hit`,@MethodConst18) AS `Hit` , IFNULL(`ed`.`def`,@MethodConst19) AS `Def` , IFNULL(`ed`.`spd`,@MethodConst20) AS `Spd` , IFNULL(`ed`.`dodge`,@MethodConst21) AS `Dodge` , IFNULL(`ed`.`hp`,@MethodConst22) AS `Hp` , IFNULL(`ed`.`mp`,@MethodConst23) AS `Mp` , IFNULL(`ed`.`deepen`,@MethodConst24) AS `Deepen` , IFNULL(`ed`.`offset`,@MethodConst25) AS `Offset` , IFNULL(`ed`.`vamp`,@MethodConst26) AS `Vamp` , IFNULL(`ed`.`vamp_mp`,@MethodConst27) AS `VampMp` , `ed`.`description` AS `Description` , `ed`.`main_attr` AS `MainAttr` , `ed`.`main_attr_value` AS `MainAttrValue` , `ed`.`sub_attr` AS `SubAttr` , `ed`.`sub_attr_value` AS `SubAttrValue` , `ed`.`element_limit` AS `ElementLimit` , `ed`.`equip_name` AS `EquipName`  FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )     ORDER BY `e`.`equip_id` ASC LIMIT 0,10 | 参数: [@constant0=, @MethodConst1=0, @MethodConst2=0, @MethodConst3=0, @MethodConst4=0, @MethodConst5=0, @MethodConst6=0, @MethodConst7=0, @MethodConst8=0, @MethodConst9=0, @MethodConst10=0, @MethodConst11=0, @MethodConst12=0, @MethodConst13=0, @constant14=, @MethodConst15=0, @MethodConst16=0, @MethodConst17=0, @MethodConst18=0, @MethodConst19=0, @MethodConst20=0, @MethodConst21=0, @MethodConst22=0, @MethodConst23=0, @MethodConst24=0, @MethodConst25=0, @MethodConst26=0, @MethodConst27=0]
[2025-09-07 22:00:22.442 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `e`.`equip_id` AS `EquipId` , @constant14 AS `ClassId` , `e`.`name` AS `Name` , `e`.`icon` AS `Icon` , `e`.`equip_type_id` AS `EquipTypeId` , `et`.`type_name` AS `EquipTypeName` , `e`.`element` AS `Element` , IFNULL(`e`.`slot`,@MethodConst15) AS `Slot` , IFNULL(`e`.`strengthen_level`,@MethodConst16) AS `StrengthenLevel` , `e`.`suit_id` AS `SuitId` , IFNULL(`ed`.`atk`,@MethodConst17) AS `Atk` , IFNULL(`ed`.`hit`,@MethodConst18) AS `Hit` , IFNULL(`ed`.`def`,@MethodConst19) AS `Def` , IFNULL(`ed`.`spd`,@MethodConst20) AS `Spd` , IFNULL(`ed`.`dodge`,@MethodConst21) AS `Dodge` , IFNULL(`ed`.`hp`,@MethodConst22) AS `Hp` , IFNULL(`ed`.`mp`,@MethodConst23) AS `Mp` , IFNULL(`ed`.`deepen`,@MethodConst24) AS `Deepen` , IFNULL(`ed`.`offset`,@MethodConst25) AS `Offset` , IFNULL(`ed`.`vamp`,@MethodConst26) AS `Vamp` , IFNULL(`ed`.`vamp_mp`,@MethodConst27) AS `VampMp` , `ed`.`description` AS `Description` , `ed`.`main_attr` AS `MainAttr` , `ed`.`main_attr_value` AS `MainAttrValue` , `ed`.`sub_attr` AS `SubAttr` , `ed`.`sub_attr_value` AS `SubAttrValue` , `ed`.`element_limit` AS `ElementLimit` , `ed`.`equip_name` AS `EquipName`  FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )     ORDER BY `e`.`equip_id` ASC LIMIT 0,10
[2025-09-07 22:00:22.476 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 22:00:22.478 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 22:00:22.480 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 22:00:22.512 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 22:00:22.514 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 22:00:22.526 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment_type`  
[2025-09-07 22:00:22.559 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment_type`  
[2025-09-07 22:00:22.580 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `id` AS `Id` , `equip_type_id` AS `EquipTypeId` , `type_name` AS `TypeName` , @constant6 AS `Description` , @constant7 AS `SortOrder` , @constant8 AS `IsActive` , @constant9 AS `CreateTime` , @constant10 AS `UpdateTime` , @constant11 AS `EquipmentCount`  FROM `equipment_type`    ORDER BY `id` ASC LIMIT 0,10 | 参数: [@constant0=, @constant1=0, @constant2=True, @constant3=, @constant4=, @constant5=0, @constant6=, @constant7=0, @constant8=True, @constant9=, @constant10=, @constant11=0]
[2025-09-07 22:00:23.159 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `id` AS `Id` , `equip_type_id` AS `EquipTypeId` , `type_name` AS `TypeName` , @constant6 AS `Description` , @constant7 AS `SortOrder` , @constant8 AS `IsActive` , @constant9 AS `CreateTime` , @constant10 AS `UpdateTime` , @constant11 AS `EquipmentCount`  FROM `equipment_type`    ORDER BY `id` ASC LIMIT 0,10
[2025-09-07 22:00:23.184 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=1]
[2025-09-07 22:00:23.219 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-09-07 22:00:23.246 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=2]
[2025-09-07 22:00:23.298 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-09-07 22:00:23.300 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=3]
[2025-09-07 22:00:23.332 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-09-07 22:00:23.335 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=4]
[2025-09-07 22:00:23.368 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-09-07 22:00:23.373 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=5]
[2025-09-07 22:00:23.410 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-09-07 22:00:23.413 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=6]
[2025-09-07 22:00:23.444 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-09-07 22:00:23.473 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=7]
[2025-09-07 22:00:23.503 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-09-07 22:00:23.505 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=8]
[2025-09-07 22:00:23.536 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-09-07 22:00:23.541 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=9]
[2025-09-07 22:00:23.573 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-09-07 22:00:23.575 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=10]
[2025-09-07 22:00:23.606 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-09-07 22:05:12.181 +08:00 INF] BMS: 🚀 应用程序启动，日志系统已配置
[2025-09-07 22:05:12.208 +08:00 DBG] BMS: 🔧 调试级别日志测试
[2025-09-07 22:05:12.229 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 22:05:12.231 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 22:05:12.374 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 22:05:12.872 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 22:05:12.883 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 22:05:12.885 +08:00 INF] BMS: ✅ DbContext 初始化成功，日志文件位置: logs/
[2025-09-07 22:05:14.320 +08:00 WRN] Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware: Failed to determine the https port for redirect.
[2025-09-07 22:05:21.003 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 22:05:21.128 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 22:05:21.133 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 22:05:21.173 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 22:05:21.209 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 22:05:21.254 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `equip_type_id` AS `EquipTypeId` , `type_name` AS `TypeName`  FROM `equipment_type` ORDER BY `id` ASC 
[2025-09-07 22:05:21.299 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `equip_type_id` AS `EquipTypeId` , `type_name` AS `TypeName`  FROM `equipment_type` ORDER BY `id` ASC 
[2025-09-07 22:05:21.456 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 22:05:21.623 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 22:05:21.699 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 22:05:21.741 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 22:05:21.744 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 22:05:21.833 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  
[2025-09-07 22:05:21.874 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  
[2025-09-07 22:05:21.883 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment_detail`  
[2025-09-07 22:05:21.924 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment_detail`  
[2025-09-07 22:05:21.927 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment_type`  
[2025-09-07 22:05:21.966 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment_type`  
[2025-09-07 22:05:21.986 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )   
[2025-09-07 22:05:22.031 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )   
[2025-09-07 22:05:22.049 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `e`.`equip_id` AS `EquipId` , @constant14 AS `ClassId` , `e`.`name` AS `Name` , `e`.`icon` AS `Icon` , `e`.`equip_type_id` AS `EquipTypeId` , `et`.`type_name` AS `EquipTypeName` , `e`.`element` AS `Element` , IFNULL(`e`.`slot`,@MethodConst15) AS `Slot` , IFNULL(`e`.`strengthen_level`,@MethodConst16) AS `StrengthenLevel` , `e`.`suit_id` AS `SuitId` , IFNULL(`ed`.`atk`,@MethodConst17) AS `Atk` , IFNULL(`ed`.`hit`,@MethodConst18) AS `Hit` , IFNULL(`ed`.`def`,@MethodConst19) AS `Def` , IFNULL(`ed`.`spd`,@MethodConst20) AS `Spd` , IFNULL(`ed`.`dodge`,@MethodConst21) AS `Dodge` , IFNULL(`ed`.`hp`,@MethodConst22) AS `Hp` , IFNULL(`ed`.`mp`,@MethodConst23) AS `Mp` , IFNULL(`ed`.`deepen`,@MethodConst24) AS `Deepen` , IFNULL(`ed`.`offset`,@MethodConst25) AS `Offset` , IFNULL(`ed`.`vamp`,@MethodConst26) AS `Vamp` , IFNULL(`ed`.`vamp_mp`,@MethodConst27) AS `VampMp` , `ed`.`description` AS `Description` , `ed`.`main_attr` AS `MainAttr` , `ed`.`main_attr_value` AS `MainAttrValue` , `ed`.`sub_attr` AS `SubAttr` , `ed`.`sub_attr_value` AS `SubAttrValue` , `ed`.`element_limit` AS `ElementLimit` , `ed`.`equip_name` AS `EquipName`  FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )     ORDER BY `e`.`equip_id` ASC LIMIT 0,10 | 参数: [@constant0=, @MethodConst1=0, @MethodConst2=0, @MethodConst3=0, @MethodConst4=0, @MethodConst5=0, @MethodConst6=0, @MethodConst7=0, @MethodConst8=0, @MethodConst9=0, @MethodConst10=0, @MethodConst11=0, @MethodConst12=0, @MethodConst13=0, @constant14=, @MethodConst15=0, @MethodConst16=0, @MethodConst17=0, @MethodConst18=0, @MethodConst19=0, @MethodConst20=0, @MethodConst21=0, @MethodConst22=0, @MethodConst23=0, @MethodConst24=0, @MethodConst25=0, @MethodConst26=0, @MethodConst27=0]
[2025-09-07 22:05:22.111 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `e`.`equip_id` AS `EquipId` , @constant14 AS `ClassId` , `e`.`name` AS `Name` , `e`.`icon` AS `Icon` , `e`.`equip_type_id` AS `EquipTypeId` , `et`.`type_name` AS `EquipTypeName` , `e`.`element` AS `Element` , IFNULL(`e`.`slot`,@MethodConst15) AS `Slot` , IFNULL(`e`.`strengthen_level`,@MethodConst16) AS `StrengthenLevel` , `e`.`suit_id` AS `SuitId` , IFNULL(`ed`.`atk`,@MethodConst17) AS `Atk` , IFNULL(`ed`.`hit`,@MethodConst18) AS `Hit` , IFNULL(`ed`.`def`,@MethodConst19) AS `Def` , IFNULL(`ed`.`spd`,@MethodConst20) AS `Spd` , IFNULL(`ed`.`dodge`,@MethodConst21) AS `Dodge` , IFNULL(`ed`.`hp`,@MethodConst22) AS `Hp` , IFNULL(`ed`.`mp`,@MethodConst23) AS `Mp` , IFNULL(`ed`.`deepen`,@MethodConst24) AS `Deepen` , IFNULL(`ed`.`offset`,@MethodConst25) AS `Offset` , IFNULL(`ed`.`vamp`,@MethodConst26) AS `Vamp` , IFNULL(`ed`.`vamp_mp`,@MethodConst27) AS `VampMp` , `ed`.`description` AS `Description` , `ed`.`main_attr` AS `MainAttr` , `ed`.`main_attr_value` AS `MainAttrValue` , `ed`.`sub_attr` AS `SubAttr` , `ed`.`sub_attr_value` AS `SubAttrValue` , `ed`.`element_limit` AS `ElementLimit` , `ed`.`equip_name` AS `EquipName`  FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )     ORDER BY `e`.`equip_id` ASC LIMIT 0,10
[2025-09-07 22:05:22.136 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 22:05:22.140 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 22:05:22.142 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 22:05:22.179 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 22:05:22.181 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 22:05:22.192 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment_type`  
[2025-09-07 22:05:22.252 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment_type`  
[2025-09-07 22:05:22.258 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `id` AS `Id` , `equip_type_id` AS `EquipTypeId` , `type_name` AS `TypeName` , @constant6 AS `Description` , @constant7 AS `SortOrder` , @constant8 AS `IsActive` , @constant9 AS `CreateTime` , @constant10 AS `UpdateTime` , @constant11 AS `EquipmentCount`  FROM `equipment_type`    ORDER BY `id` ASC LIMIT 0,10 | 参数: [@constant0=, @constant1=0, @constant2=True, @constant3=, @constant4=, @constant5=0, @constant6=, @constant7=0, @constant8=True, @constant9=, @constant10=, @constant11=0]
[2025-09-07 22:05:22.298 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `id` AS `Id` , `equip_type_id` AS `EquipTypeId` , `type_name` AS `TypeName` , @constant6 AS `Description` , @constant7 AS `SortOrder` , @constant8 AS `IsActive` , @constant9 AS `CreateTime` , @constant10 AS `UpdateTime` , @constant11 AS `EquipmentCount`  FROM `equipment_type`    ORDER BY `id` ASC LIMIT 0,10
[2025-09-07 22:05:22.312 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=1]
[2025-09-07 22:05:22.350 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-09-07 22:05:22.353 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=2]
[2025-09-07 22:05:22.389 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-09-07 22:05:22.424 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=3]
[2025-09-07 22:05:22.460 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-09-07 22:05:22.482 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=4]
[2025-09-07 22:05:22.518 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-09-07 22:05:22.521 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=5]
[2025-09-07 22:05:22.558 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-09-07 22:05:22.562 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=6]
[2025-09-07 22:05:22.599 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-09-07 22:05:22.604 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=7]
[2025-09-07 22:05:22.640 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-09-07 22:05:22.644 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=8]
[2025-09-07 22:05:22.711 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-09-07 22:05:22.724 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=9]
[2025-09-07 22:05:22.788 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-09-07 22:05:23.315 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=10]
[2025-09-07 22:05:23.412 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-09-07 22:05:27.665 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 22:05:27.667 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 22:05:27.670 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 22:05:27.708 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 22:05:27.713 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 22:05:27.726 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `id` AS `Id` , `equip_type_id` AS `EquipTypeId` , `type_name` AS `TypeName` , @constant6 AS `Description` , @constant7 AS `SortOrder` , @constant8 AS `IsActive` , @constant9 AS `CreateTime` , @constant10 AS `UpdateTime`  FROM `equipment_type`   WHERE ( `equip_type_id` = @equip_type_id0 )   LIMIT 0,1 | 参数: [@equip_type_id0=1, @constant1=, @constant2=0, @constant3=True, @constant4=, @constant5=, @constant6=, @constant7=0, @constant8=True, @constant9=, @constant10=]
[2025-09-07 22:05:27.767 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `id` AS `Id` , `equip_type_id` AS `EquipTypeId` , `type_name` AS `TypeName` , @constant6 AS `Description` , @constant7 AS `SortOrder` , @constant8 AS `IsActive` , @constant9 AS `CreateTime` , @constant10 AS `UpdateTime`  FROM `equipment_type`   WHERE ( `equip_type_id` = @equip_type_id0 )   LIMIT 0,1
[2025-09-07 22:05:27.775 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=1]
[2025-09-07 22:05:27.813 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-09-07 22:05:34.173 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 22:05:34.176 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 22:05:34.179 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 22:05:34.216 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 22:05:34.223 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 22:05:34.226 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `equip_type_id` AS `EquipTypeId` , `type_name` AS `TypeName`  FROM `equipment_type` ORDER BY `id` ASC 
[2025-09-07 22:05:34.265 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `equip_type_id` AS `EquipTypeId` , `type_name` AS `TypeName`  FROM `equipment_type` ORDER BY `id` ASC 
[2025-09-07 22:05:34.313 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 22:05:34.314 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 22:05:34.316 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 22:05:34.353 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 22:05:34.356 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 22:05:34.360 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  
[2025-09-07 22:05:34.398 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  
[2025-09-07 22:05:34.400 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment_detail`  
[2025-09-07 22:05:34.438 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment_detail`  
[2025-09-07 22:05:34.680 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment_type`  
[2025-09-07 22:05:34.730 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment_type`  
[2025-09-07 22:05:34.750 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )   
[2025-09-07 22:05:34.795 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )   
[2025-09-07 22:05:34.967 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `e`.`equip_id` AS `EquipId` , @constant14 AS `ClassId` , `e`.`name` AS `Name` , `e`.`icon` AS `Icon` , `e`.`equip_type_id` AS `EquipTypeId` , `et`.`type_name` AS `EquipTypeName` , `e`.`element` AS `Element` , IFNULL(`e`.`slot`,@MethodConst15) AS `Slot` , IFNULL(`e`.`strengthen_level`,@MethodConst16) AS `StrengthenLevel` , `e`.`suit_id` AS `SuitId` , IFNULL(`ed`.`atk`,@MethodConst17) AS `Atk` , IFNULL(`ed`.`hit`,@MethodConst18) AS `Hit` , IFNULL(`ed`.`def`,@MethodConst19) AS `Def` , IFNULL(`ed`.`spd`,@MethodConst20) AS `Spd` , IFNULL(`ed`.`dodge`,@MethodConst21) AS `Dodge` , IFNULL(`ed`.`hp`,@MethodConst22) AS `Hp` , IFNULL(`ed`.`mp`,@MethodConst23) AS `Mp` , IFNULL(`ed`.`deepen`,@MethodConst24) AS `Deepen` , IFNULL(`ed`.`offset`,@MethodConst25) AS `Offset` , IFNULL(`ed`.`vamp`,@MethodConst26) AS `Vamp` , IFNULL(`ed`.`vamp_mp`,@MethodConst27) AS `VampMp` , `ed`.`description` AS `Description` , `ed`.`main_attr` AS `MainAttr` , `ed`.`main_attr_value` AS `MainAttrValue` , `ed`.`sub_attr` AS `SubAttr` , `ed`.`sub_attr_value` AS `SubAttrValue` , `ed`.`element_limit` AS `ElementLimit` , `ed`.`equip_name` AS `EquipName`  FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )     ORDER BY `e`.`equip_id` ASC LIMIT 0,10 | 参数: [@constant0=, @MethodConst1=0, @MethodConst2=0, @MethodConst3=0, @MethodConst4=0, @MethodConst5=0, @MethodConst6=0, @MethodConst7=0, @MethodConst8=0, @MethodConst9=0, @MethodConst10=0, @MethodConst11=0, @MethodConst12=0, @MethodConst13=0, @constant14=, @MethodConst15=0, @MethodConst16=0, @MethodConst17=0, @MethodConst18=0, @MethodConst19=0, @MethodConst20=0, @MethodConst21=0, @MethodConst22=0, @MethodConst23=0, @MethodConst24=0, @MethodConst25=0, @MethodConst26=0, @MethodConst27=0]
[2025-09-07 22:05:35.032 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `e`.`equip_id` AS `EquipId` , @constant14 AS `ClassId` , `e`.`name` AS `Name` , `e`.`icon` AS `Icon` , `e`.`equip_type_id` AS `EquipTypeId` , `et`.`type_name` AS `EquipTypeName` , `e`.`element` AS `Element` , IFNULL(`e`.`slot`,@MethodConst15) AS `Slot` , IFNULL(`e`.`strengthen_level`,@MethodConst16) AS `StrengthenLevel` , `e`.`suit_id` AS `SuitId` , IFNULL(`ed`.`atk`,@MethodConst17) AS `Atk` , IFNULL(`ed`.`hit`,@MethodConst18) AS `Hit` , IFNULL(`ed`.`def`,@MethodConst19) AS `Def` , IFNULL(`ed`.`spd`,@MethodConst20) AS `Spd` , IFNULL(`ed`.`dodge`,@MethodConst21) AS `Dodge` , IFNULL(`ed`.`hp`,@MethodConst22) AS `Hp` , IFNULL(`ed`.`mp`,@MethodConst23) AS `Mp` , IFNULL(`ed`.`deepen`,@MethodConst24) AS `Deepen` , IFNULL(`ed`.`offset`,@MethodConst25) AS `Offset` , IFNULL(`ed`.`vamp`,@MethodConst26) AS `Vamp` , IFNULL(`ed`.`vamp_mp`,@MethodConst27) AS `VampMp` , `ed`.`description` AS `Description` , `ed`.`main_attr` AS `MainAttr` , `ed`.`main_attr_value` AS `MainAttrValue` , `ed`.`sub_attr` AS `SubAttr` , `ed`.`sub_attr_value` AS `SubAttrValue` , `ed`.`element_limit` AS `ElementLimit` , `ed`.`equip_name` AS `EquipName`  FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )     ORDER BY `e`.`equip_id` ASC LIMIT 0,10
[2025-09-07 22:05:35.087 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 22:05:35.122 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 22:05:35.127 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 22:05:35.169 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 22:05:35.172 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 22:05:35.176 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment_type`  
[2025-09-07 22:05:35.214 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment_type`  
[2025-09-07 22:05:35.216 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `id` AS `Id` , `equip_type_id` AS `EquipTypeId` , `type_name` AS `TypeName` , @constant6 AS `Description` , @constant7 AS `SortOrder` , @constant8 AS `IsActive` , @constant9 AS `CreateTime` , @constant10 AS `UpdateTime` , @constant11 AS `EquipmentCount`  FROM `equipment_type`    ORDER BY `id` ASC LIMIT 0,10 | 参数: [@constant0=, @constant1=0, @constant2=True, @constant3=, @constant4=, @constant5=0, @constant6=, @constant7=0, @constant8=True, @constant9=, @constant10=, @constant11=0]
[2025-09-07 22:05:35.259 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `id` AS `Id` , `equip_type_id` AS `EquipTypeId` , `type_name` AS `TypeName` , @constant6 AS `Description` , @constant7 AS `SortOrder` , @constant8 AS `IsActive` , @constant9 AS `CreateTime` , @constant10 AS `UpdateTime` , @constant11 AS `EquipmentCount`  FROM `equipment_type`    ORDER BY `id` ASC LIMIT 0,10
[2025-09-07 22:05:35.266 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=1]
[2025-09-07 22:05:35.308 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-09-07 22:05:35.310 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=2]
[2025-09-07 22:05:35.348 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-09-07 22:05:35.351 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=3]
[2025-09-07 22:05:35.389 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-09-07 22:05:35.392 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=4]
[2025-09-07 22:05:35.431 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-09-07 22:05:35.434 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=5]
[2025-09-07 22:05:35.476 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-09-07 22:05:35.479 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=6]
[2025-09-07 22:05:35.514 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-09-07 22:05:35.517 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=7]
[2025-09-07 22:05:35.560 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-09-07 22:05:35.563 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=8]
[2025-09-07 22:05:35.602 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-09-07 22:05:35.605 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=9]
[2025-09-07 22:05:35.643 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-09-07 22:05:35.646 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=10]
[2025-09-07 22:05:35.685 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-09-07 22:05:37.911 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 22:05:37.914 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 22:05:37.915 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 22:05:37.954 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 22:05:37.956 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 22:05:37.962 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `e`.`equip_id` AS `EquipId` , @constant15 AS `ClassId` , `e`.`name` AS `Name` , `e`.`icon` AS `Icon` , `e`.`equip_type_id` AS `EquipTypeId` , `et`.`type_name` AS `EquipTypeName` , `e`.`element` AS `Element` , IFNULL(`e`.`slot`,@MethodConst16) AS `Slot` , IFNULL(`e`.`strengthen_level`,@MethodConst17) AS `StrengthenLevel` , `e`.`suit_id` AS `SuitId` , IFNULL(`ed`.`atk`,@MethodConst18) AS `Atk` , IFNULL(`ed`.`hit`,@MethodConst19) AS `Hit` , IFNULL(`ed`.`def`,@MethodConst20) AS `Def` , IFNULL(`ed`.`spd`,@MethodConst21) AS `Spd` , IFNULL(`ed`.`dodge`,@MethodConst22) AS `Dodge` , IFNULL(`ed`.`hp`,@MethodConst23) AS `Hp` , IFNULL(`ed`.`mp`,@MethodConst24) AS `Mp` , IFNULL(`ed`.`deepen`,@MethodConst25) AS `Deepen` , IFNULL(`ed`.`offset`,@MethodConst26) AS `Offset` , IFNULL(`ed`.`vamp`,@MethodConst27) AS `Vamp` , IFNULL(`ed`.`vamp_mp`,@MethodConst28) AS `VampMp` , `ed`.`description` AS `Description` , `ed`.`main_attr` AS `MainAttr` , `ed`.`main_attr_value` AS `MainAttrValue` , `ed`.`sub_attr` AS `SubAttr` , `ed`.`sub_attr_value` AS `SubAttrValue` , `ed`.`element_limit` AS `ElementLimit` , `ed`.`equip_name` AS `EquipName`  FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )    WHERE ( `e`.`equip_id` = @equip_id0 )   LIMIT 0,1 | 参数: [@equip_id0=000101, @constant1=, @MethodConst2=0, @MethodConst3=0, @MethodConst4=0, @MethodConst5=0, @MethodConst6=0, @MethodConst7=0, @MethodConst8=0, @MethodConst9=0, @MethodConst10=0, @MethodConst11=0, @MethodConst12=0, @MethodConst13=0, @MethodConst14=0, @constant15=, @MethodConst16=0, @MethodConst17=0, @MethodConst18=0, @MethodConst19=0, @MethodConst20=0, @MethodConst21=0, @MethodConst22=0, @MethodConst23=0, @MethodConst24=0, @MethodConst25=0, @MethodConst26=0, @MethodConst27=0, @MethodConst28=0]
[2025-09-07 22:05:38.006 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `e`.`equip_id` AS `EquipId` , @constant15 AS `ClassId` , `e`.`name` AS `Name` , `e`.`icon` AS `Icon` , `e`.`equip_type_id` AS `EquipTypeId` , `et`.`type_name` AS `EquipTypeName` , `e`.`element` AS `Element` , IFNULL(`e`.`slot`,@MethodConst16) AS `Slot` , IFNULL(`e`.`strengthen_level`,@MethodConst17) AS `StrengthenLevel` , `e`.`suit_id` AS `SuitId` , IFNULL(`ed`.`atk`,@MethodConst18) AS `Atk` , IFNULL(`ed`.`hit`,@MethodConst19) AS `Hit` , IFNULL(`ed`.`def`,@MethodConst20) AS `Def` , IFNULL(`ed`.`spd`,@MethodConst21) AS `Spd` , IFNULL(`ed`.`dodge`,@MethodConst22) AS `Dodge` , IFNULL(`ed`.`hp`,@MethodConst23) AS `Hp` , IFNULL(`ed`.`mp`,@MethodConst24) AS `Mp` , IFNULL(`ed`.`deepen`,@MethodConst25) AS `Deepen` , IFNULL(`ed`.`offset`,@MethodConst26) AS `Offset` , IFNULL(`ed`.`vamp`,@MethodConst27) AS `Vamp` , IFNULL(`ed`.`vamp_mp`,@MethodConst28) AS `VampMp` , `ed`.`description` AS `Description` , `ed`.`main_attr` AS `MainAttr` , `ed`.`main_attr_value` AS `MainAttrValue` , `ed`.`sub_attr` AS `SubAttr` , `ed`.`sub_attr_value` AS `SubAttrValue` , `ed`.`element_limit` AS `ElementLimit` , `ed`.`equip_name` AS `EquipName`  FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )    WHERE ( `e`.`equip_id` = @equip_id0 )   LIMIT 0,1
[2025-09-07 22:07:55.325 +08:00 INF] BMS: 🚀 应用程序启动，日志系统已配置
[2025-09-07 22:07:55.361 +08:00 DBG] BMS: 🔧 调试级别日志测试
[2025-09-07 22:07:55.383 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 22:07:55.386 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 22:07:55.528 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 22:07:56.015 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 22:07:56.024 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 22:07:56.042 +08:00 INF] BMS: ✅ DbContext 初始化成功，日志文件位置: logs/
[2025-09-07 22:07:57.060 +08:00 WRN] Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware: Failed to determine the https port for redirect.
[2025-09-07 22:08:01.907 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 22:08:01.909 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 22:08:01.916 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 22:08:01.958 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 22:08:01.960 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 22:08:01.993 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `equip_type_id` AS `EquipTypeId` , `type_name` AS `TypeName`  FROM `equipment_type` ORDER BY `id` ASC 
[2025-09-07 22:08:02.036 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `equip_type_id` AS `EquipTypeId` , `type_name` AS `TypeName`  FROM `equipment_type` ORDER BY `id` ASC 
[2025-09-07 22:08:02.184 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 22:08:02.186 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 22:08:02.188 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 22:08:02.226 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 22:08:02.228 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 22:08:02.331 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  
[2025-09-07 22:08:02.389 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  
[2025-09-07 22:08:02.448 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment_detail`  
[2025-09-07 22:08:02.490 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment_detail`  
[2025-09-07 22:08:02.577 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment_type`  
[2025-09-07 22:08:02.662 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment_type`  
[2025-09-07 22:08:02.677 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )   
[2025-09-07 22:08:02.732 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )   
[2025-09-07 22:08:02.753 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `e`.`equip_id` AS `EquipId` , @constant14 AS `ClassId` , `e`.`name` AS `Name` , `e`.`icon` AS `Icon` , `e`.`equip_type_id` AS `EquipTypeId` , `et`.`type_name` AS `EquipTypeName` , `e`.`element` AS `Element` , IFNULL(`e`.`slot`,@MethodConst15) AS `Slot` , IFNULL(`e`.`strengthen_level`,@MethodConst16) AS `StrengthenLevel` , `e`.`suit_id` AS `SuitId` , IFNULL(`ed`.`atk`,@MethodConst17) AS `Atk` , IFNULL(`ed`.`hit`,@MethodConst18) AS `Hit` , IFNULL(`ed`.`def`,@MethodConst19) AS `Def` , IFNULL(`ed`.`spd`,@MethodConst20) AS `Spd` , IFNULL(`ed`.`dodge`,@MethodConst21) AS `Dodge` , IFNULL(`ed`.`hp`,@MethodConst22) AS `Hp` , IFNULL(`ed`.`mp`,@MethodConst23) AS `Mp` , IFNULL(`ed`.`deepen`,@MethodConst24) AS `Deepen` , IFNULL(`ed`.`offset`,@MethodConst25) AS `Offset` , IFNULL(`ed`.`vamp`,@MethodConst26) AS `Vamp` , IFNULL(`ed`.`vamp_mp`,@MethodConst27) AS `VampMp` , `ed`.`description` AS `Description` , `ed`.`main_attr` AS `MainAttr` , `ed`.`main_attr_value` AS `MainAttrValue` , `ed`.`sub_attr` AS `SubAttr` , `ed`.`sub_attr_value` AS `SubAttrValue` , `ed`.`element_limit` AS `ElementLimit` , `ed`.`equip_name` AS `EquipName`  FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )     ORDER BY `e`.`equip_id` ASC LIMIT 0,10 | 参数: [@constant0=, @MethodConst1=0, @MethodConst2=0, @MethodConst3=0, @MethodConst4=0, @MethodConst5=0, @MethodConst6=0, @MethodConst7=0, @MethodConst8=0, @MethodConst9=0, @MethodConst10=0, @MethodConst11=0, @MethodConst12=0, @MethodConst13=0, @constant14=, @MethodConst15=0, @MethodConst16=0, @MethodConst17=0, @MethodConst18=0, @MethodConst19=0, @MethodConst20=0, @MethodConst21=0, @MethodConst22=0, @MethodConst23=0, @MethodConst24=0, @MethodConst25=0, @MethodConst26=0, @MethodConst27=0]
[2025-09-07 22:08:02.816 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `e`.`equip_id` AS `EquipId` , @constant14 AS `ClassId` , `e`.`name` AS `Name` , `e`.`icon` AS `Icon` , `e`.`equip_type_id` AS `EquipTypeId` , `et`.`type_name` AS `EquipTypeName` , `e`.`element` AS `Element` , IFNULL(`e`.`slot`,@MethodConst15) AS `Slot` , IFNULL(`e`.`strengthen_level`,@MethodConst16) AS `StrengthenLevel` , `e`.`suit_id` AS `SuitId` , IFNULL(`ed`.`atk`,@MethodConst17) AS `Atk` , IFNULL(`ed`.`hit`,@MethodConst18) AS `Hit` , IFNULL(`ed`.`def`,@MethodConst19) AS `Def` , IFNULL(`ed`.`spd`,@MethodConst20) AS `Spd` , IFNULL(`ed`.`dodge`,@MethodConst21) AS `Dodge` , IFNULL(`ed`.`hp`,@MethodConst22) AS `Hp` , IFNULL(`ed`.`mp`,@MethodConst23) AS `Mp` , IFNULL(`ed`.`deepen`,@MethodConst24) AS `Deepen` , IFNULL(`ed`.`offset`,@MethodConst25) AS `Offset` , IFNULL(`ed`.`vamp`,@MethodConst26) AS `Vamp` , IFNULL(`ed`.`vamp_mp`,@MethodConst27) AS `VampMp` , `ed`.`description` AS `Description` , `ed`.`main_attr` AS `MainAttr` , `ed`.`main_attr_value` AS `MainAttrValue` , `ed`.`sub_attr` AS `SubAttr` , `ed`.`sub_attr_value` AS `SubAttrValue` , `ed`.`element_limit` AS `ElementLimit` , `ed`.`equip_name` AS `EquipName`  FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )     ORDER BY `e`.`equip_id` ASC LIMIT 0,10
[2025-09-07 22:08:02.850 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 22:08:02.851 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 22:08:02.853 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 22:08:02.893 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 22:08:02.896 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 22:08:02.903 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment_type`  
[2025-09-07 22:08:02.942 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment_type`  
[2025-09-07 22:08:02.946 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `id` AS `Id` , `equip_type_id` AS `EquipTypeId` , `type_name` AS `TypeName` , @constant6 AS `Description` , @constant7 AS `SortOrder` , @constant8 AS `IsActive` , @constant9 AS `CreateTime` , @constant10 AS `UpdateTime` , @constant11 AS `EquipmentCount`  FROM `equipment_type`    ORDER BY `id` ASC LIMIT 0,10 | 参数: [@constant0=, @constant1=0, @constant2=True, @constant3=, @constant4=, @constant5=0, @constant6=, @constant7=0, @constant8=True, @constant9=, @constant10=, @constant11=0]
[2025-09-07 22:08:03.004 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `id` AS `Id` , `equip_type_id` AS `EquipTypeId` , `type_name` AS `TypeName` , @constant6 AS `Description` , @constant7 AS `SortOrder` , @constant8 AS `IsActive` , @constant9 AS `CreateTime` , @constant10 AS `UpdateTime` , @constant11 AS `EquipmentCount`  FROM `equipment_type`    ORDER BY `id` ASC LIMIT 0,10
[2025-09-07 22:08:03.017 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=1]
[2025-09-07 22:08:03.056 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-09-07 22:08:03.060 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=2]
[2025-09-07 22:08:03.099 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-09-07 22:08:03.102 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=3]
[2025-09-07 22:08:03.141 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-09-07 22:08:03.143 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=4]
[2025-09-07 22:08:03.182 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-09-07 22:08:03.185 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=5]
[2025-09-07 22:08:03.222 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-09-07 22:08:03.225 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=6]
[2025-09-07 22:08:03.263 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-09-07 22:08:03.266 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=7]
[2025-09-07 22:08:03.304 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-09-07 22:08:03.306 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=8]
[2025-09-07 22:08:03.343 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-09-07 22:08:03.348 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=9]
[2025-09-07 22:08:03.386 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-09-07 22:08:03.389 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=10]
[2025-09-07 22:08:03.426 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-09-07 22:08:05.352 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 22:08:05.354 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 22:08:05.355 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 22:08:05.391 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 22:08:05.393 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 22:08:05.402 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `e`.`equip_id` AS `EquipId` , @constant15 AS `ClassId` , `e`.`name` AS `Name` , `e`.`icon` AS `Icon` , `e`.`equip_type_id` AS `EquipTypeId` , `et`.`type_name` AS `EquipTypeName` , `e`.`element` AS `Element` , IFNULL(`e`.`slot`,@MethodConst16) AS `Slot` , IFNULL(`e`.`strengthen_level`,@MethodConst17) AS `StrengthenLevel` , `e`.`suit_id` AS `SuitId` , IFNULL(`ed`.`atk`,@MethodConst18) AS `Atk` , IFNULL(`ed`.`hit`,@MethodConst19) AS `Hit` , IFNULL(`ed`.`def`,@MethodConst20) AS `Def` , IFNULL(`ed`.`spd`,@MethodConst21) AS `Spd` , IFNULL(`ed`.`dodge`,@MethodConst22) AS `Dodge` , IFNULL(`ed`.`hp`,@MethodConst23) AS `Hp` , IFNULL(`ed`.`mp`,@MethodConst24) AS `Mp` , IFNULL(`ed`.`deepen`,@MethodConst25) AS `Deepen` , IFNULL(`ed`.`offset`,@MethodConst26) AS `Offset` , IFNULL(`ed`.`vamp`,@MethodConst27) AS `Vamp` , IFNULL(`ed`.`vamp_mp`,@MethodConst28) AS `VampMp` , `ed`.`description` AS `Description` , `ed`.`main_attr` AS `MainAttr` , `ed`.`main_attr_value` AS `MainAttrValue` , `ed`.`sub_attr` AS `SubAttr` , `ed`.`sub_attr_value` AS `SubAttrValue` , `ed`.`element_limit` AS `ElementLimit` , `ed`.`equip_name` AS `EquipName`  FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )    WHERE ( `e`.`equip_id` = @equip_id0 )   LIMIT 0,1 | 参数: [@equip_id0=000101, @constant1=, @MethodConst2=0, @MethodConst3=0, @MethodConst4=0, @MethodConst5=0, @MethodConst6=0, @MethodConst7=0, @MethodConst8=0, @MethodConst9=0, @MethodConst10=0, @MethodConst11=0, @MethodConst12=0, @MethodConst13=0, @MethodConst14=0, @constant15=, @MethodConst16=0, @MethodConst17=0, @MethodConst18=0, @MethodConst19=0, @MethodConst20=0, @MethodConst21=0, @MethodConst22=0, @MethodConst23=0, @MethodConst24=0, @MethodConst25=0, @MethodConst26=0, @MethodConst27=0, @MethodConst28=0]
[2025-09-07 22:08:05.441 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `e`.`equip_id` AS `EquipId` , @constant15 AS `ClassId` , `e`.`name` AS `Name` , `e`.`icon` AS `Icon` , `e`.`equip_type_id` AS `EquipTypeId` , `et`.`type_name` AS `EquipTypeName` , `e`.`element` AS `Element` , IFNULL(`e`.`slot`,@MethodConst16) AS `Slot` , IFNULL(`e`.`strengthen_level`,@MethodConst17) AS `StrengthenLevel` , `e`.`suit_id` AS `SuitId` , IFNULL(`ed`.`atk`,@MethodConst18) AS `Atk` , IFNULL(`ed`.`hit`,@MethodConst19) AS `Hit` , IFNULL(`ed`.`def`,@MethodConst20) AS `Def` , IFNULL(`ed`.`spd`,@MethodConst21) AS `Spd` , IFNULL(`ed`.`dodge`,@MethodConst22) AS `Dodge` , IFNULL(`ed`.`hp`,@MethodConst23) AS `Hp` , IFNULL(`ed`.`mp`,@MethodConst24) AS `Mp` , IFNULL(`ed`.`deepen`,@MethodConst25) AS `Deepen` , IFNULL(`ed`.`offset`,@MethodConst26) AS `Offset` , IFNULL(`ed`.`vamp`,@MethodConst27) AS `Vamp` , IFNULL(`ed`.`vamp_mp`,@MethodConst28) AS `VampMp` , `ed`.`description` AS `Description` , `ed`.`main_attr` AS `MainAttr` , `ed`.`main_attr_value` AS `MainAttrValue` , `ed`.`sub_attr` AS `SubAttr` , `ed`.`sub_attr_value` AS `SubAttrValue` , `ed`.`element_limit` AS `ElementLimit` , `ed`.`equip_name` AS `EquipName`  FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )    WHERE ( `e`.`equip_id` = @equip_id0 )   LIMIT 0,1
[2025-09-07 22:12:10.876 +08:00 INF] BMS: 🚀 应用程序启动，日志系统已配置
[2025-09-07 22:12:10.903 +08:00 DBG] BMS: 🔧 调试级别日志测试
[2025-09-07 22:12:10.921 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 22:12:10.924 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 22:12:11.007 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 22:12:11.533 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 22:12:11.678 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 22:12:11.706 +08:00 INF] BMS: ✅ DbContext 初始化成功，日志文件位置: logs/
[2025-09-07 22:12:12.753 +08:00 WRN] Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware: Failed to determine the https port for redirect.
[2025-09-07 22:12:16.348 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 22:12:16.523 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 22:12:16.607 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 22:12:16.641 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 22:12:16.647 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 22:12:16.677 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `equip_type_id` AS `EquipTypeId` , `type_name` AS `TypeName`  FROM `equipment_type` ORDER BY `id` ASC 
[2025-09-07 22:12:16.712 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `equip_type_id` AS `EquipTypeId` , `type_name` AS `TypeName`  FROM `equipment_type` ORDER BY `id` ASC 
[2025-09-07 22:12:16.861 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 22:12:16.862 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 22:12:16.863 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 22:12:16.893 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 22:12:16.896 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 22:12:16.994 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  
[2025-09-07 22:12:17.024 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  
[2025-09-07 22:12:17.034 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment_detail`  
[2025-09-07 22:12:17.064 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment_detail`  
[2025-09-07 22:12:17.066 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment_type`  
[2025-09-07 22:12:17.106 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment_type`  
[2025-09-07 22:12:17.121 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )   
[2025-09-07 22:12:17.164 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )   
[2025-09-07 22:12:17.181 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `e`.`equip_id` AS `EquipId` , @constant14 AS `ClassId` , `e`.`name` AS `Name` , `e`.`icon` AS `Icon` , `e`.`equip_type_id` AS `EquipTypeId` , `et`.`type_name` AS `EquipTypeName` , `e`.`element` AS `Element` , IFNULL(`e`.`slot`,@MethodConst15) AS `Slot` , IFNULL(`e`.`strengthen_level`,@MethodConst16) AS `StrengthenLevel` , `e`.`suit_id` AS `SuitId` , IFNULL(`ed`.`atk`,@MethodConst17) AS `Atk` , IFNULL(`ed`.`hit`,@MethodConst18) AS `Hit` , IFNULL(`ed`.`def`,@MethodConst19) AS `Def` , IFNULL(`ed`.`spd`,@MethodConst20) AS `Spd` , IFNULL(`ed`.`dodge`,@MethodConst21) AS `Dodge` , IFNULL(`ed`.`hp`,@MethodConst22) AS `Hp` , IFNULL(`ed`.`mp`,@MethodConst23) AS `Mp` , IFNULL(`ed`.`deepen`,@MethodConst24) AS `Deepen` , IFNULL(`ed`.`offset`,@MethodConst25) AS `Offset` , IFNULL(`ed`.`vamp`,@MethodConst26) AS `Vamp` , IFNULL(`ed`.`vamp_mp`,@MethodConst27) AS `VampMp` , `ed`.`description` AS `Description` , `ed`.`main_attr` AS `MainAttr` , `ed`.`main_attr_value` AS `MainAttrValue` , `ed`.`sub_attr` AS `SubAttr` , `ed`.`sub_attr_value` AS `SubAttrValue` , `ed`.`element_limit` AS `ElementLimit` , `ed`.`equip_name` AS `EquipName`  FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )     ORDER BY `e`.`equip_id` ASC LIMIT 0,10 | 参数: [@constant0=, @MethodConst1=0, @MethodConst2=0, @MethodConst3=0, @MethodConst4=0, @MethodConst5=0, @MethodConst6=0, @MethodConst7=0, @MethodConst8=0, @MethodConst9=0, @MethodConst10=0, @MethodConst11=0, @MethodConst12=0, @MethodConst13=0, @constant14=, @MethodConst15=0, @MethodConst16=0, @MethodConst17=0, @MethodConst18=0, @MethodConst19=0, @MethodConst20=0, @MethodConst21=0, @MethodConst22=0, @MethodConst23=0, @MethodConst24=0, @MethodConst25=0, @MethodConst26=0, @MethodConst27=0]
[2025-09-07 22:12:17.233 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `e`.`equip_id` AS `EquipId` , @constant14 AS `ClassId` , `e`.`name` AS `Name` , `e`.`icon` AS `Icon` , `e`.`equip_type_id` AS `EquipTypeId` , `et`.`type_name` AS `EquipTypeName` , `e`.`element` AS `Element` , IFNULL(`e`.`slot`,@MethodConst15) AS `Slot` , IFNULL(`e`.`strengthen_level`,@MethodConst16) AS `StrengthenLevel` , `e`.`suit_id` AS `SuitId` , IFNULL(`ed`.`atk`,@MethodConst17) AS `Atk` , IFNULL(`ed`.`hit`,@MethodConst18) AS `Hit` , IFNULL(`ed`.`def`,@MethodConst19) AS `Def` , IFNULL(`ed`.`spd`,@MethodConst20) AS `Spd` , IFNULL(`ed`.`dodge`,@MethodConst21) AS `Dodge` , IFNULL(`ed`.`hp`,@MethodConst22) AS `Hp` , IFNULL(`ed`.`mp`,@MethodConst23) AS `Mp` , IFNULL(`ed`.`deepen`,@MethodConst24) AS `Deepen` , IFNULL(`ed`.`offset`,@MethodConst25) AS `Offset` , IFNULL(`ed`.`vamp`,@MethodConst26) AS `Vamp` , IFNULL(`ed`.`vamp_mp`,@MethodConst27) AS `VampMp` , `ed`.`description` AS `Description` , `ed`.`main_attr` AS `MainAttr` , `ed`.`main_attr_value` AS `MainAttrValue` , `ed`.`sub_attr` AS `SubAttr` , `ed`.`sub_attr_value` AS `SubAttrValue` , `ed`.`element_limit` AS `ElementLimit` , `ed`.`equip_name` AS `EquipName`  FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )     ORDER BY `e`.`equip_id` ASC LIMIT 0,10
[2025-09-07 22:12:17.259 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 22:12:17.260 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 22:12:17.261 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 22:12:17.291 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 22:12:17.294 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 22:12:17.300 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment_type`  
[2025-09-07 22:12:17.328 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment_type`  
[2025-09-07 22:12:17.332 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `id` AS `Id` , `equip_type_id` AS `EquipTypeId` , `type_name` AS `TypeName` , @constant6 AS `Description` , @constant7 AS `SortOrder` , @constant8 AS `IsActive` , @constant9 AS `CreateTime` , @constant10 AS `UpdateTime` , @constant11 AS `EquipmentCount`  FROM `equipment_type`    ORDER BY `id` ASC LIMIT 0,10 | 参数: [@constant0=, @constant1=0, @constant2=True, @constant3=, @constant4=, @constant5=0, @constant6=, @constant7=0, @constant8=True, @constant9=, @constant10=, @constant11=0]
[2025-09-07 22:12:17.362 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `id` AS `Id` , `equip_type_id` AS `EquipTypeId` , `type_name` AS `TypeName` , @constant6 AS `Description` , @constant7 AS `SortOrder` , @constant8 AS `IsActive` , @constant9 AS `CreateTime` , @constant10 AS `UpdateTime` , @constant11 AS `EquipmentCount`  FROM `equipment_type`    ORDER BY `id` ASC LIMIT 0,10
[2025-09-07 22:12:17.384 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=1]
[2025-09-07 22:12:17.414 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-09-07 22:12:17.417 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=2]
[2025-09-07 22:12:17.446 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-09-07 22:12:17.451 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=3]
[2025-09-07 22:12:17.479 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-09-07 22:12:17.482 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=4]
[2025-09-07 22:12:17.512 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-09-07 22:12:17.515 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=5]
[2025-09-07 22:12:17.544 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-09-07 22:12:17.547 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=6]
[2025-09-07 22:12:17.576 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-09-07 22:12:17.579 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=7]
[2025-09-07 22:12:17.611 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-09-07 22:12:17.637 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=8]
[2025-09-07 22:12:17.669 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-09-07 22:12:17.675 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=9]
[2025-09-07 22:12:17.704 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-09-07 22:12:17.709 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=10]
[2025-09-07 22:12:17.744 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-09-07 22:12:21.881 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 22:12:21.883 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 22:12:21.884 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 22:12:21.911 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 22:12:21.913 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 22:12:21.922 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `e`.`equip_id` AS `EquipId` , @constant15 AS `ClassId` , `e`.`name` AS `Name` , `e`.`icon` AS `Icon` , `e`.`equip_type_id` AS `EquipTypeId` , `et`.`type_name` AS `EquipTypeName` , `e`.`element` AS `Element` , IFNULL(`e`.`slot`,@MethodConst16) AS `Slot` , IFNULL(`e`.`strengthen_level`,@MethodConst17) AS `StrengthenLevel` , `e`.`suit_id` AS `SuitId` , IFNULL(`ed`.`atk`,@MethodConst18) AS `Atk` , IFNULL(`ed`.`hit`,@MethodConst19) AS `Hit` , IFNULL(`ed`.`def`,@MethodConst20) AS `Def` , IFNULL(`ed`.`spd`,@MethodConst21) AS `Spd` , IFNULL(`ed`.`dodge`,@MethodConst22) AS `Dodge` , IFNULL(`ed`.`hp`,@MethodConst23) AS `Hp` , IFNULL(`ed`.`mp`,@MethodConst24) AS `Mp` , IFNULL(`ed`.`deepen`,@MethodConst25) AS `Deepen` , IFNULL(`ed`.`offset`,@MethodConst26) AS `Offset` , IFNULL(`ed`.`vamp`,@MethodConst27) AS `Vamp` , IFNULL(`ed`.`vamp_mp`,@MethodConst28) AS `VampMp` , `ed`.`description` AS `Description` , `ed`.`main_attr` AS `MainAttr` , `ed`.`main_attr_value` AS `MainAttrValue` , `ed`.`sub_attr` AS `SubAttr` , `ed`.`sub_attr_value` AS `SubAttrValue` , `ed`.`element_limit` AS `ElementLimit` , `ed`.`equip_name` AS `EquipName`  FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )    WHERE ( `e`.`equip_id` = @equip_id0 )   LIMIT 0,1 | 参数: [@equip_id0=000101, @constant1=, @MethodConst2=0, @MethodConst3=0, @MethodConst4=0, @MethodConst5=0, @MethodConst6=0, @MethodConst7=0, @MethodConst8=0, @MethodConst9=0, @MethodConst10=0, @MethodConst11=0, @MethodConst12=0, @MethodConst13=0, @MethodConst14=0, @constant15=, @MethodConst16=0, @MethodConst17=0, @MethodConst18=0, @MethodConst19=0, @MethodConst20=0, @MethodConst21=0, @MethodConst22=0, @MethodConst23=0, @MethodConst24=0, @MethodConst25=0, @MethodConst26=0, @MethodConst27=0, @MethodConst28=0]
[2025-09-07 22:12:21.952 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `e`.`equip_id` AS `EquipId` , @constant15 AS `ClassId` , `e`.`name` AS `Name` , `e`.`icon` AS `Icon` , `e`.`equip_type_id` AS `EquipTypeId` , `et`.`type_name` AS `EquipTypeName` , `e`.`element` AS `Element` , IFNULL(`e`.`slot`,@MethodConst16) AS `Slot` , IFNULL(`e`.`strengthen_level`,@MethodConst17) AS `StrengthenLevel` , `e`.`suit_id` AS `SuitId` , IFNULL(`ed`.`atk`,@MethodConst18) AS `Atk` , IFNULL(`ed`.`hit`,@MethodConst19) AS `Hit` , IFNULL(`ed`.`def`,@MethodConst20) AS `Def` , IFNULL(`ed`.`spd`,@MethodConst21) AS `Spd` , IFNULL(`ed`.`dodge`,@MethodConst22) AS `Dodge` , IFNULL(`ed`.`hp`,@MethodConst23) AS `Hp` , IFNULL(`ed`.`mp`,@MethodConst24) AS `Mp` , IFNULL(`ed`.`deepen`,@MethodConst25) AS `Deepen` , IFNULL(`ed`.`offset`,@MethodConst26) AS `Offset` , IFNULL(`ed`.`vamp`,@MethodConst27) AS `Vamp` , IFNULL(`ed`.`vamp_mp`,@MethodConst28) AS `VampMp` , `ed`.`description` AS `Description` , `ed`.`main_attr` AS `MainAttr` , `ed`.`main_attr_value` AS `MainAttrValue` , `ed`.`sub_attr` AS `SubAttr` , `ed`.`sub_attr_value` AS `SubAttrValue` , `ed`.`element_limit` AS `ElementLimit` , `ed`.`equip_name` AS `EquipName`  FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )    WHERE ( `e`.`equip_id` = @equip_id0 )   LIMIT 0,1
[2025-09-07 22:12:29.514 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 22:12:29.518 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 22:12:29.519 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 22:12:29.551 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 22:12:29.669 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 22:12:29.691 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `id` AS `Id` , `equip_type_id` AS `EquipTypeId` , `type_name` AS `TypeName` , @constant6 AS `Description` , @constant7 AS `SortOrder` , @constant8 AS `IsActive` , @constant9 AS `CreateTime` , @constant10 AS `UpdateTime`  FROM `equipment_type`   WHERE ( `equip_type_id` = @equip_type_id0 )   LIMIT 0,1 | 参数: [@equip_type_id0=1, @constant1=, @constant2=0, @constant3=True, @constant4=, @constant5=, @constant6=, @constant7=0, @constant8=True, @constant9=, @constant10=]
[2025-09-07 22:12:29.733 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `id` AS `Id` , `equip_type_id` AS `EquipTypeId` , `type_name` AS `TypeName` , @constant6 AS `Description` , @constant7 AS `SortOrder` , @constant8 AS `IsActive` , @constant9 AS `CreateTime` , @constant10 AS `UpdateTime`  FROM `equipment_type`   WHERE ( `equip_type_id` = @equip_type_id0 )   LIMIT 0,1
[2025-09-07 22:12:29.762 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=1]
[2025-09-07 22:12:29.858 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-09-07 22:12:33.573 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 22:12:33.589 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 22:12:33.591 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 22:12:33.625 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 22:12:33.627 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 22:12:33.629 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `id` AS `Id` , `equip_type_id` AS `EquipTypeId` , `type_name` AS `TypeName` , @constant6 AS `Description` , @constant7 AS `SortOrder` , @constant8 AS `IsActive` , @constant9 AS `CreateTime` , @constant10 AS `UpdateTime`  FROM `equipment_type`   WHERE ( `equip_type_id` = @equip_type_id0 )   LIMIT 0,1 | 参数: [@equip_type_id0=1, @constant1=, @constant2=0, @constant3=True, @constant4=, @constant5=, @constant6=, @constant7=0, @constant8=True, @constant9=, @constant10=]
[2025-09-07 22:12:33.666 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `id` AS `Id` , `equip_type_id` AS `EquipTypeId` , `type_name` AS `TypeName` , @constant6 AS `Description` , @constant7 AS `SortOrder` , @constant8 AS `IsActive` , @constant9 AS `CreateTime` , @constant10 AS `UpdateTime`  FROM `equipment_type`   WHERE ( `equip_type_id` = @equip_type_id0 )   LIMIT 0,1
[2025-09-07 22:12:33.668 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=1]
[2025-09-07 22:12:33.722 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-09-07 22:12:37.726 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 22:12:37.728 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 22:12:37.730 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 22:12:37.762 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 22:12:37.764 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 22:12:37.765 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment_type`  
[2025-09-07 22:12:37.795 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment_type`  
[2025-09-07 22:12:37.798 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `id` AS `Id` , `equip_type_id` AS `EquipTypeId` , `type_name` AS `TypeName` , @constant6 AS `Description` , @constant7 AS `SortOrder` , @constant8 AS `IsActive` , @constant9 AS `CreateTime` , @constant10 AS `UpdateTime` , @constant11 AS `EquipmentCount`  FROM `equipment_type`    ORDER BY `id` ASC LIMIT 10,10 | 参数: [@constant0=, @constant1=0, @constant2=True, @constant3=, @constant4=, @constant5=0, @constant6=, @constant7=0, @constant8=True, @constant9=, @constant10=, @constant11=0]
[2025-09-07 22:12:37.828 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `id` AS `Id` , `equip_type_id` AS `EquipTypeId` , `type_name` AS `TypeName` , @constant6 AS `Description` , @constant7 AS `SortOrder` , @constant8 AS `IsActive` , @constant9 AS `CreateTime` , @constant10 AS `UpdateTime` , @constant11 AS `EquipmentCount`  FROM `equipment_type`    ORDER BY `id` ASC LIMIT 10,10
[2025-09-07 22:12:37.831 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=11]
[2025-09-07 22:12:37.860 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-09-07 22:12:37.863 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=12]
[2025-09-07 22:12:37.893 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-09-07 22:12:37.896 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=13]
[2025-09-07 22:12:37.925 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-09-07 22:12:37.954 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=14]
[2025-09-07 22:12:37.989 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-09-07 22:12:38.505 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=15]
[2025-09-07 22:12:38.583 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-09-07 22:12:38.587 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=16]
[2025-09-07 22:12:38.617 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-09-07 22:12:38.622 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=17]
[2025-09-07 22:12:38.653 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-09-07 22:12:38.655 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=18]
[2025-09-07 22:12:38.686 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-09-07 22:12:38.690 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=19]
[2025-09-07 22:12:38.719 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-09-07 22:12:42.061 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 22:12:42.313 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 22:12:42.316 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 22:12:42.349 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 22:12:42.359 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 22:12:42.363 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `id` AS `Id` , `equip_type_id` AS `EquipTypeId` , `type_name` AS `TypeName` , @constant6 AS `Description` , @constant7 AS `SortOrder` , @constant8 AS `IsActive` , @constant9 AS `CreateTime` , @constant10 AS `UpdateTime`  FROM `equipment_type`   WHERE ( `equip_type_id` = @equip_type_id0 )   LIMIT 0,1 | 参数: [@equip_type_id0=17, @constant1=, @constant2=0, @constant3=True, @constant4=, @constant5=, @constant6=, @constant7=0, @constant8=True, @constant9=, @constant10=]
[2025-09-07 22:12:42.393 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `id` AS `Id` , `equip_type_id` AS `EquipTypeId` , `type_name` AS `TypeName` , @constant6 AS `Description` , @constant7 AS `SortOrder` , @constant8 AS `IsActive` , @constant9 AS `CreateTime` , @constant10 AS `UpdateTime`  FROM `equipment_type`   WHERE ( `equip_type_id` = @equip_type_id0 )   LIMIT 0,1
[2025-09-07 22:12:42.417 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=17]
[2025-09-07 22:12:42.478 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-09-07 22:14:48.155 +08:00 INF] BMS: 🚀 应用程序启动，日志系统已配置
[2025-09-07 22:14:48.525 +08:00 DBG] BMS: 🔧 调试级别日志测试
[2025-09-07 22:14:48.710 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 22:14:48.731 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 22:14:48.817 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 22:14:49.432 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 22:14:49.466 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 22:14:49.468 +08:00 INF] BMS: ✅ DbContext 初始化成功，日志文件位置: logs/
[2025-09-07 22:14:50.560 +08:00 WRN] Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware: Failed to determine the https port for redirect.
[2025-09-07 22:18:54.042 +08:00 INF] BMS: 🚀 应用程序启动，日志系统已配置
[2025-09-07 22:18:54.094 +08:00 DBG] BMS: 🔧 调试级别日志测试
[2025-09-07 22:18:54.121 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 22:18:54.125 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 22:18:54.205 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 22:18:54.723 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 22:18:55.017 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 22:18:55.090 +08:00 INF] BMS: ✅ DbContext 初始化成功，日志文件位置: logs/
[2025-09-07 22:18:55.857 +08:00 WRN] Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware: Failed to determine the https port for redirect.
[2025-09-07 22:20:03.048 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 22:20:03.061 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 22:20:03.070 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 22:20:03.111 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 22:20:03.113 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 22:20:03.185 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 22:20:03.186 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 22:20:03.188 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 22:20:03.228 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 22:20:03.242 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 22:20:03.343 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `map_config`  
[2025-09-07 22:20:03.387 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `map_config`  
[2025-09-07 22:20:03.409 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`map_id`,`map_name`,`map_desc`,`map_size`,`map_type`,`atlast_name`,`background`,`bgm`,`bgm_loop`,`bgm_volume`,`bgm_play`,`bgm_mute`,`bgm_pause`,`ico`,`type` FROM `map_config`    ORDER BY `id` ASC LIMIT 0,10
[2025-09-07 22:20:03.611 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`map_id`,`map_name`,`map_desc`,`map_size`,`map_type`,`atlast_name`,`background`,`bgm`,`bgm_loop`,`bgm_volume`,`bgm_play`,`bgm_mute`,`bgm_pause`,`ico`,`type` FROM `map_config`    ORDER BY `id` ASC LIMIT 0,10
[2025-09-07 22:20:09.816 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 22:20:09.921 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 22:20:09.980 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 22:20:10.019 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 22:20:10.021 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 22:20:10.029 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`map_id`,`map_name`,`map_desc`,`map_size`,`map_type`,`atlast_name`,`background`,`bgm`,`bgm_loop`,`bgm_volume`,`bgm_play`,`bgm_mute`,`bgm_pause`,`ico`,`type` FROM `map_config` ORDER BY `map_id` ASC 
[2025-09-07 22:20:10.073 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`map_id`,`map_name`,`map_desc`,`map_size`,`map_type`,`atlast_name`,`background`,`bgm`,`bgm_loop`,`bgm_volume`,`bgm_play`,`bgm_mute`,`bgm_pause`,`ico`,`type` FROM `map_config` ORDER BY `map_id` ASC 
[2025-09-07 22:20:10.201 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 22:20:10.202 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 22:20:10.209 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 22:20:10.211 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 22:20:10.213 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 22:20:10.214 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 22:20:10.252 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 22:20:10.267 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 22:20:10.271 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`map_id`,`map_name`,`map_desc`,`map_size`,`map_type`,`atlast_name`,`background`,`bgm`,`bgm_loop`,`bgm_volume`,`bgm_play`,`bgm_mute`,`bgm_pause`,`ico`,`type` FROM `map_config` ORDER BY `map_id` ASC 
[2025-09-07 22:20:10.309 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`map_id`,`map_name`,`map_desc`,`map_size`,`map_type`,`atlast_name`,`background`,`bgm`,`bgm_loop`,`bgm_volume`,`bgm_play`,`bgm_mute`,`bgm_pause`,`ico`,`type` FROM `map_config` ORDER BY `map_id` ASC 
[2025-09-07 22:20:10.444 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 22:20:10.448 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 22:20:10.497 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM `map_detail` `d` Left JOIN `map_config` `c` ON ( `d`.`map_id` = `c`.`map_id` )    | 参数: [@MethodConst0=0, @MethodConst1=0, @MethodConst2=False, @MethodConst3=0, @MethodConst4=0, @MethodConst5=0, @MethodConst6=0, @MethodConst7=0, @MethodConst8=0, @MethodConst9=0]
[2025-09-07 22:20:10.537 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM `map_detail` `d` Left JOIN `map_config` `c` ON ( `d`.`map_id` = `c`.`map_id` )   
[2025-09-07 22:20:10.540 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `d`.`id` AS `Id` , `d`.`map_id` AS `MapId` , `c`.`map_name` AS `MapName` , IFNULL(`d`.`limit_growth`,@MethodConst10) AS `LimitGrowth` , IFNULL(`d`.`limit_level`,@MethodConst11) AS `LimitLevel` , IFNULL(CAST(`d`.`limit_key` as SIGNED),@MethodConst12) AS `LimitKey` , IFNULL(`d`.`min_gold`,@MethodConst13) AS `MinGold` , IFNULL(`d`.`max_gold`,@MethodConst14) AS `MaxGold` , IFNULL(`d`.`min_yuanbao`,@MethodConst15) AS `MinYuanbao` , IFNULL(`d`.`max_yuanbao`,@MethodConst16) AS `MaxYuanbao` , IFNULL(`d`.`min_drop`,@MethodConst17) AS `MinDrop` , IFNULL(`d`.`max_drop`,@MethodConst18) AS `MaxDrop` , `d`.`icon` AS `Icon` , IFNULL(`d`.`type`,@MethodConst19) AS `Type`  FROM `map_detail` `d` Left JOIN `map_config` `c` ON ( `d`.`map_id` = `c`.`map_id` )     ORDER BY `d`.`id` ASC LIMIT 0,10 | 参数: [@MethodConst0=0, @MethodConst1=0, @MethodConst2=False, @MethodConst3=0, @MethodConst4=0, @MethodConst5=0, @MethodConst6=0, @MethodConst7=0, @MethodConst8=0, @MethodConst9=0, @MethodConst10=0, @MethodConst11=0, @MethodConst12=False, @MethodConst13=0, @MethodConst14=0, @MethodConst15=0, @MethodConst16=0, @MethodConst17=0, @MethodConst18=0, @MethodConst19=0]
[2025-09-07 22:20:10.579 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `d`.`id` AS `Id` , `d`.`map_id` AS `MapId` , `c`.`map_name` AS `MapName` , IFNULL(`d`.`limit_growth`,@MethodConst10) AS `LimitGrowth` , IFNULL(`d`.`limit_level`,@MethodConst11) AS `LimitLevel` , IFNULL(CAST(`d`.`limit_key` as SIGNED),@MethodConst12) AS `LimitKey` , IFNULL(`d`.`min_gold`,@MethodConst13) AS `MinGold` , IFNULL(`d`.`max_gold`,@MethodConst14) AS `MaxGold` , IFNULL(`d`.`min_yuanbao`,@MethodConst15) AS `MinYuanbao` , IFNULL(`d`.`max_yuanbao`,@MethodConst16) AS `MaxYuanbao` , IFNULL(`d`.`min_drop`,@MethodConst17) AS `MinDrop` , IFNULL(`d`.`max_drop`,@MethodConst18) AS `MaxDrop` , `d`.`icon` AS `Icon` , IFNULL(`d`.`type`,@MethodConst19) AS `Type`  FROM `map_detail` `d` Left JOIN `map_config` `c` ON ( `d`.`map_id` = `c`.`map_id` )     ORDER BY `d`.`id` ASC LIMIT 0,10
[2025-09-07 22:21:07.306 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 22:21:07.307 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 22:21:07.309 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 22:21:07.342 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 22:21:07.344 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 22:21:07.405 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 22:21:07.406 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 22:21:07.408 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 22:21:07.441 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 22:21:07.444 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 22:21:07.457 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 22:21:07.460 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 22:21:07.462 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 22:21:07.498 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 22:21:07.502 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 22:21:07.509 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `task_id` AS `Value` , `task_name` AS `Label`  FROM `task_config`  WHERE ( `is_active` = @is_active0 )ORDER BY `sort_order` ASC  | 参数: [@is_active0=1]
[2025-09-07 22:21:07.543 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `task_id` AS `Value` , `task_name` AS `Label`  FROM `task_config`  WHERE ( `is_active` = @is_active0 )ORDER BY `sort_order` ASC 
[2025-09-07 22:21:07.560 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 22:21:07.562 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 22:21:07.566 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 22:21:07.600 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 22:21:07.602 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 22:21:07.628 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM `task_config`  
[2025-09-07 22:21:07.662 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM `task_config`  
[2025-09-07 22:21:07.665 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`    ORDER BY `sort_order` ASC,`created_at` DESC LIMIT 0,10
[2025-09-07 22:21:07.700 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`    ORDER BY `sort_order` ASC,`created_at` DESC LIMIT 0,10
[2025-09-07 22:21:07.711 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_001]
[2025-09-07 22:21:07.750 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-09-07 22:21:07.755 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_002]
[2025-09-07 22:21:07.789 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-09-07 22:21:07.792 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_003]
[2025-09-07 22:21:07.826 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-09-07 22:21:07.829 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_004]
[2025-09-07 22:21:07.866 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-09-07 22:21:07.881 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_005]
[2025-09-07 22:21:07.917 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-09-07 22:21:07.920 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_010]
[2025-09-07 22:21:07.953 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-09-07 22:21:25.474 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 22:21:25.476 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 22:21:25.478 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 22:21:25.512 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 22:21:25.513 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 22:21:25.584 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 22:21:25.586 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 22:21:25.587 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 22:21:25.632 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 22:21:25.633 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 22:21:25.652 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 22:21:25.655 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 22:21:25.658 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 22:21:25.698 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 22:21:25.703 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 22:21:25.707 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `task_id` AS `Value` , `task_name` AS `Label`  FROM `task_config`  WHERE ( `is_active` = @is_active0 )ORDER BY `sort_order` ASC  | 参数: [@is_active0=1]
[2025-09-07 22:21:25.746 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `task_id` AS `Value` , `task_name` AS `Label`  FROM `task_config`  WHERE ( `is_active` = @is_active0 )ORDER BY `sort_order` ASC 
[2025-09-07 22:21:25.755 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 22:21:25.756 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 22:21:25.758 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 22:21:25.792 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 22:21:25.794 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 22:21:25.797 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM `task_config`  
[2025-09-07 22:21:25.831 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM `task_config`  
[2025-09-07 22:21:25.833 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`    ORDER BY `sort_order` ASC,`created_at` DESC LIMIT 0,10
[2025-09-07 22:21:25.868 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`    ORDER BY `sort_order` ASC,`created_at` DESC LIMIT 0,10
[2025-09-07 22:21:25.871 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_001]
[2025-09-07 22:21:25.906 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-09-07 22:21:25.918 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_002]
[2025-09-07 22:21:25.952 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-09-07 22:21:26.396 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_003]
[2025-09-07 22:21:26.598 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-09-07 22:21:26.603 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_004]
[2025-09-07 22:21:26.638 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-09-07 22:21:26.640 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_005]
[2025-09-07 22:21:26.676 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-09-07 22:21:26.686 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_010]
[2025-09-07 22:21:26.727 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-09-07 22:21:33.222 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 22:21:33.226 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 22:21:33.228 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 22:21:33.263 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 22:21:33.264 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 22:21:33.266 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`map_id`,`map_name`,`map_desc`,`map_size`,`map_type`,`atlast_name`,`background`,`bgm`,`bgm_loop`,`bgm_volume`,`bgm_play`,`bgm_mute`,`bgm_pause`,`ico`,`type` FROM `map_config` ORDER BY `map_id` ASC 
[2025-09-07 22:21:33.303 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`map_id`,`map_name`,`map_desc`,`map_size`,`map_type`,`atlast_name`,`background`,`bgm`,`bgm_loop`,`bgm_volume`,`bgm_play`,`bgm_mute`,`bgm_pause`,`ico`,`type` FROM `map_config` ORDER BY `map_id` ASC 
[2025-09-07 22:21:33.365 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 22:21:33.366 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 22:21:33.367 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 22:21:33.404 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 22:21:33.407 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 22:21:33.410 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`map_id`,`map_name`,`map_desc`,`map_size`,`map_type`,`atlast_name`,`background`,`bgm`,`bgm_loop`,`bgm_volume`,`bgm_play`,`bgm_mute`,`bgm_pause`,`ico`,`type` FROM `map_config` ORDER BY `map_id` ASC 
[2025-09-07 22:21:33.447 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`map_id`,`map_name`,`map_desc`,`map_size`,`map_type`,`atlast_name`,`background`,`bgm`,`bgm_loop`,`bgm_volume`,`bgm_play`,`bgm_mute`,`bgm_pause`,`ico`,`type` FROM `map_config` ORDER BY `map_id` ASC 
[2025-09-07 22:21:33.461 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 22:21:33.462 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 22:21:33.463 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 22:21:33.497 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 22:21:33.498 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 22:21:33.501 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`monster_no`,`skill`,`name`,`attribute` FROM `monster_config` ORDER BY `monster_no` ASC 
[2025-09-07 22:21:33.537 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`monster_no`,`skill`,`name`,`attribute` FROM `monster_config` ORDER BY `monster_no` ASC 
[2025-09-07 22:21:33.551 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 22:21:33.552 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 22:21:33.554 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 22:21:33.590 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 22:21:33.592 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 22:21:33.597 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `map_monster`  
[2025-09-07 22:21:33.630 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `map_monster`  
[2025-09-07 22:21:33.632 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`map_id`,`monster_id`,`monster_name`,`growth`,`element`,`max_level`,`min_level`,`max_drop`,`exp`,`hp`,`mp`,`max_hp`,`max_mp`,`atk`,`def`,`dodge`,`spd`,`hit`,`deepen`,`offset`,`vamp`,`vamp_mp`,`skill_list`,`drop_items` FROM `map_monster`    ORDER BY `map_id` ASC,`monster_id` ASC LIMIT 0,10
[2025-09-07 22:21:33.666 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`map_id`,`monster_id`,`monster_name`,`growth`,`element`,`max_level`,`min_level`,`max_drop`,`exp`,`hp`,`mp`,`max_hp`,`max_mp`,`atk`,`def`,`dodge`,`spd`,`hit`,`deepen`,`offset`,`vamp`,`vamp_mp`,`skill_list`,`drop_items` FROM `map_monster`    ORDER BY `map_id` ASC,`monster_id` ASC LIMIT 0,10
[2025-09-07 22:21:33.678 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`map_id`,`map_name`,`map_desc`,`map_size`,`map_type`,`atlast_name`,`background`,`bgm`,`bgm_loop`,`bgm_volume`,`bgm_play`,`bgm_mute`,`bgm_pause`,`ico`,`type` FROM `map_config`  WHERE  (`map_id` IN (100,101,102,103,104,105,106,107,108))  
[2025-09-07 22:21:33.713 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`map_id`,`map_name`,`map_desc`,`map_size`,`map_type`,`atlast_name`,`background`,`bgm`,`bgm_loop`,`bgm_volume`,`bgm_play`,`bgm_mute`,`bgm_pause`,`ico`,`type` FROM `map_config`  WHERE  (`map_id` IN (100,101,102,103,104,105,106,107,108))  
[2025-09-07 22:22:17.963 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 22:22:17.965 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 22:22:17.967 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 22:22:18.006 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 22:22:18.008 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 22:22:18.032 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`   WHERE ( `task_id` = @task_id0 )   LIMIT 0,1 | 参数: [@task_id0=TASK_002]
[2025-09-07 22:22:18.067 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`   WHERE ( `task_id` = @task_id0 )   LIMIT 0,1
[2025-09-07 22:22:18.101 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: UPDATE `task_config`  SET
           `task_name`=@task_name,`task_description`=@task_description,`task_type`=@task_type,`is_repeatable`=@is_repeatable,`prerequisite_task`=@prerequisite_task,`required_pet`=@required_pet,`reward_config`=@reward_config,`is_network_task`=@is_network_task,`is_active`=@is_active,`sort_order`=@sort_order,`created_at`=@created_at,`updated_at`=@updated_at  WHERE `task_id`=@task_id | 参数: [@task_id=TASK_002, @task_name=清理怪物, @task_description=击杀指定数量的史莱姆, @task_type=1, @is_repeatable=1, @prerequisite_task=, @required_pet=, @reward_config=[{"Type":"金币","Id":"gold","Amount":500},{"Type":"经验","Id":"exp","Amount":100}], @is_network_task=0, @is_active=1, @sort_order=2, @created_at=2025/7/24 0:00:00, @updated_at=2025/9/7 22:22:18]
[2025-09-07 22:22:18.227 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: UPDATE `task_config`  SET
           `task_name`=@task_name,`task_description`=@task_description,`task_type`=@task_type,`is_repeatable`=@is_repeatable,`prerequisite_task`=@prerequisite_task,`required_pet`=@required_pet,`reward_config`=@reward_config,`is_network_task`=@is_network_task,`is_active`=@is_active,`sort_order`=@sort_order,`created_at`=@created_at,`updated_at`=@updated_at  WHERE `task_id`=@task_id
[2025-09-07 22:22:18.236 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: DELETE FROM task_objective WHERE task_id = @taskId | 参数: [@taskId=TASK_002]
[2025-09-07 22:22:18.287 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: DELETE FROM task_objective WHERE task_id = @taskId
[2025-09-07 22:22:18.321 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: INSERT INTO task_objective
                           (task_id, objective_type, target_id, target_amount, objective_order, objective_description, created_at)
                           VALUES (@taskId0, @objectiveType0, @targetId0, @targetAmount0, @objectiveOrder0, @objectiveDescription0, @createdAt0), (@taskId1, @objectiveType1, @targetId1, @targetAmount1, @objectiveOrder1, @objectiveDescription1, @createdAt1) | 参数: [@taskId0=TASK_002, @objectiveType0=KILL_MONSTER, @targetId0=111, @targetAmount0=10, @objectiveOrder0=1, @objectiveDescription0=击杀史莱姆10个, @createdAt0=2025/9/7 22:22:18, @taskId1=TASK_002, @objectiveType1=KILL_MONSTER, @targetId1=1, @targetAmount1=10, @objectiveOrder1=1, @objectiveDescription1=, @createdAt1=2025/9/7 22:22:18]
[2025-09-07 22:22:18.372 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: INSERT INTO task_objective
                           (task_id, objective_type, target_id, target_amount, objective_order, objective_description, created_at)
                           VALUES (@taskId0, @objectiveType0, @targetId0, @targetAmount0, @objectiveOrder0, @objectiveDescription0, @createdAt0), (@taskId1, @objectiveType1, @targetId1, @targetAmount1, @objectiveOrder1, @objectiveDescription1, @createdAt1)
[2025-09-07 22:22:18.388 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`   WHERE ( `task_id` = @task_id0 )   LIMIT 0,1 | 参数: [@task_id0=TASK_002]
[2025-09-07 22:22:18.424 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`   WHERE ( `task_id` = @task_id0 )   LIMIT 0,1
[2025-09-07 22:22:18.431 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_002]
[2025-09-07 22:22:18.753 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-09-07 22:22:18.901 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 22:22:18.919 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 22:22:18.922 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 22:22:18.959 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 22:22:19.073 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 22:22:19.076 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM `task_config`  
[2025-09-07 22:22:19.114 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM `task_config`  
[2025-09-07 22:22:19.117 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`    ORDER BY `sort_order` ASC,`created_at` DESC LIMIT 0,10
[2025-09-07 22:22:19.154 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`    ORDER BY `sort_order` ASC,`created_at` DESC LIMIT 0,10
[2025-09-07 22:22:19.157 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_001]
[2025-09-07 22:22:19.192 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-09-07 22:22:19.204 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_002]
[2025-09-07 22:22:19.239 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-09-07 22:22:19.247 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_003]
[2025-09-07 22:22:19.290 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-09-07 22:22:19.292 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_004]
[2025-09-07 22:22:19.331 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-09-07 22:22:19.334 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_005]
[2025-09-07 22:22:19.370 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-09-07 22:22:19.383 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_010]
[2025-09-07 22:22:19.422 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-09-07 22:22:55.744 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 22:22:55.745 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 22:22:55.747 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 22:22:55.781 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 22:22:55.783 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 22:22:55.788 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`   WHERE ( `task_id` = @task_id0 )   LIMIT 0,1 | 参数: [@task_id0=TASK_002]
[2025-09-07 22:22:55.824 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`   WHERE ( `task_id` = @task_id0 )   LIMIT 0,1
[2025-09-07 22:22:55.831 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: UPDATE `task_config`  SET
           `task_name`=@task_name,`task_description`=@task_description,`task_type`=@task_type,`is_repeatable`=@is_repeatable,`prerequisite_task`=@prerequisite_task,`required_pet`=@required_pet,`reward_config`=@reward_config,`is_network_task`=@is_network_task,`is_active`=@is_active,`sort_order`=@sort_order,`created_at`=@created_at,`updated_at`=@updated_at  WHERE `task_id`=@task_id | 参数: [@task_id=TASK_002, @task_name=清理怪物, @task_description=击杀指定数量的史莱姆, @task_type=1, @is_repeatable=1, @prerequisite_task=, @required_pet=, @reward_config=[{"Type":"金币","Id":"gold","Amount":50000},{"Type":"经验","Id":"exp","Amount":10000}], @is_network_task=0, @is_active=1, @sort_order=2, @created_at=2025/7/24 0:00:00, @updated_at=2025/9/7 22:22:55]
[2025-09-07 22:22:55.936 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: UPDATE `task_config`  SET
           `task_name`=@task_name,`task_description`=@task_description,`task_type`=@task_type,`is_repeatable`=@is_repeatable,`prerequisite_task`=@prerequisite_task,`required_pet`=@required_pet,`reward_config`=@reward_config,`is_network_task`=@is_network_task,`is_active`=@is_active,`sort_order`=@sort_order,`created_at`=@created_at,`updated_at`=@updated_at  WHERE `task_id`=@task_id
[2025-09-07 22:22:55.940 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: DELETE FROM task_objective WHERE task_id = @taskId | 参数: [@taskId=TASK_002]
[2025-09-07 22:22:55.996 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: DELETE FROM task_objective WHERE task_id = @taskId
[2025-09-07 22:22:56.082 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: INSERT INTO task_objective
                           (task_id, objective_type, target_id, target_amount, objective_order, objective_description, created_at)
                           VALUES (@taskId0, @objectiveType0, @targetId0, @targetAmount0, @objectiveOrder0, @objectiveDescription0, @createdAt0), (@taskId1, @objectiveType1, @targetId1, @targetAmount1, @objectiveOrder1, @objectiveDescription1, @createdAt1) | 参数: [@taskId0=TASK_002, @objectiveType0=KILL_MONSTER, @targetId0=111, @targetAmount0=10, @objectiveOrder0=1, @objectiveDescription0=击杀史莱姆10个, @createdAt0=2025/9/7 22:22:56, @taskId1=TASK_002, @objectiveType1=KILL_MONSTER, @targetId1=1, @targetAmount1=10, @objectiveOrder1=1, @objectiveDescription1=, @createdAt1=2025/9/7 22:22:56]
[2025-09-07 22:22:56.130 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: INSERT INTO task_objective
                           (task_id, objective_type, target_id, target_amount, objective_order, objective_description, created_at)
                           VALUES (@taskId0, @objectiveType0, @targetId0, @targetAmount0, @objectiveOrder0, @objectiveDescription0, @createdAt0), (@taskId1, @objectiveType1, @targetId1, @targetAmount1, @objectiveOrder1, @objectiveDescription1, @createdAt1)
[2025-09-07 22:22:56.134 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`   WHERE ( `task_id` = @task_id0 )   LIMIT 0,1 | 参数: [@task_id0=TASK_002]
[2025-09-07 22:22:56.169 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`   WHERE ( `task_id` = @task_id0 )   LIMIT 0,1
[2025-09-07 22:22:56.174 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_002]
[2025-09-07 22:22:56.210 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-09-07 22:22:56.218 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 22:22:56.223 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 22:22:56.225 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 22:22:56.260 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 22:22:56.276 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 22:22:56.281 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM `task_config`  
[2025-09-07 22:22:56.324 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM `task_config`  
[2025-09-07 22:22:56.328 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`    ORDER BY `sort_order` ASC,`created_at` DESC LIMIT 0,10
[2025-09-07 22:22:56.673 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`    ORDER BY `sort_order` ASC,`created_at` DESC LIMIT 0,10
[2025-09-07 22:22:56.728 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_001]
[2025-09-07 22:22:56.762 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-09-07 22:22:56.807 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_002]
[2025-09-07 22:22:56.842 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-09-07 22:22:56.909 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_003]
[2025-09-07 22:22:56.943 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-09-07 22:22:56.949 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_004]
[2025-09-07 22:22:56.989 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-09-07 22:22:56.998 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_005]
[2025-09-07 22:22:57.035 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-09-07 22:22:57.038 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_010]
[2025-09-07 22:22:57.075 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-09-07 22:27:05.607 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 22:27:05.610 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 22:27:05.613 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 22:27:05.869 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 22:27:05.876 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 22:27:05.880 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`   WHERE ( `task_id` = @task_id0 )   LIMIT 0,1 | 参数: [@task_id0=TASK_002]
[2025-09-07 22:27:05.920 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`   WHERE ( `task_id` = @task_id0 )   LIMIT 0,1
[2025-09-07 22:27:05.924 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: UPDATE `task_config`  SET
           `task_name`=@task_name,`task_description`=@task_description,`task_type`=@task_type,`is_repeatable`=@is_repeatable,`prerequisite_task`=@prerequisite_task,`required_pet`=@required_pet,`reward_config`=@reward_config,`is_network_task`=@is_network_task,`is_active`=@is_active,`sort_order`=@sort_order,`created_at`=@created_at,`updated_at`=@updated_at  WHERE `task_id`=@task_id | 参数: [@task_id=TASK_002, @task_name=新手地图击杀任务, @task_description=亲爱的口袋勇士！我这里有一些精美礼品送给你们哦，但是也不能不劳而获，来帮助自然女神做几件事情吧，完成后这些精美礼品就属于你的啦..., @task_type=1, @is_repeatable=1, @prerequisite_task=, @required_pet=, @reward_config=[{"Type":"金币","Id":"gold","Amount":50000},{"Type":"经验","Id":"exp","Amount":10000}], @is_network_task=0, @is_active=1, @sort_order=1, @created_at=2025/7/24 0:00:00, @updated_at=2025/9/7 22:27:05]
[2025-09-07 22:27:06.035 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: UPDATE `task_config`  SET
           `task_name`=@task_name,`task_description`=@task_description,`task_type`=@task_type,`is_repeatable`=@is_repeatable,`prerequisite_task`=@prerequisite_task,`required_pet`=@required_pet,`reward_config`=@reward_config,`is_network_task`=@is_network_task,`is_active`=@is_active,`sort_order`=@sort_order,`created_at`=@created_at,`updated_at`=@updated_at  WHERE `task_id`=@task_id
[2025-09-07 22:27:06.044 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: DELETE FROM task_objective WHERE task_id = @taskId | 参数: [@taskId=TASK_002]
[2025-09-07 22:27:06.104 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: DELETE FROM task_objective WHERE task_id = @taskId
[2025-09-07 22:27:06.106 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: INSERT INTO task_objective
                           (task_id, objective_type, target_id, target_amount, objective_order, objective_description, created_at)
                           VALUES (@taskId0, @objectiveType0, @targetId0, @targetAmount0, @objectiveOrder0, @objectiveDescription0, @createdAt0), (@taskId1, @objectiveType1, @targetId1, @targetAmount1, @objectiveOrder1, @objectiveDescription1, @createdAt1) | 参数: [@taskId0=TASK_002, @objectiveType0=KILL_MONSTER, @targetId0=111, @targetAmount0=10, @objectiveOrder0=1, @objectiveDescription0=击杀史莱姆10个, @createdAt0=2025/9/7 22:27:06, @taskId1=TASK_002, @objectiveType1=KILL_MONSTER, @targetId1=1, @targetAmount1=10, @objectiveOrder1=1, @objectiveDescription1=, @createdAt1=2025/9/7 22:27:06]
[2025-09-07 22:27:06.151 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: INSERT INTO task_objective
                           (task_id, objective_type, target_id, target_amount, objective_order, objective_description, created_at)
                           VALUES (@taskId0, @objectiveType0, @targetId0, @targetAmount0, @objectiveOrder0, @objectiveDescription0, @createdAt0), (@taskId1, @objectiveType1, @targetId1, @targetAmount1, @objectiveOrder1, @objectiveDescription1, @createdAt1)
[2025-09-07 22:27:06.154 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`   WHERE ( `task_id` = @task_id0 )   LIMIT 0,1 | 参数: [@task_id0=TASK_002]
[2025-09-07 22:27:06.195 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`   WHERE ( `task_id` = @task_id0 )   LIMIT 0,1
[2025-09-07 22:27:06.204 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_002]
[2025-09-07 22:27:06.256 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-09-07 22:27:06.264 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-07 22:27:06.266 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-07 22:27:06.268 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 22:27:06.308 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-07 22:27:06.314 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-07 22:27:06.318 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM `task_config`  
[2025-09-07 22:27:06.357 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM `task_config`  
[2025-09-07 22:27:06.358 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`    ORDER BY `sort_order` ASC,`created_at` DESC LIMIT 0,10
[2025-09-07 22:27:06.398 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`    ORDER BY `sort_order` ASC,`created_at` DESC LIMIT 0,10
[2025-09-07 22:27:06.402 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_001]
[2025-09-07 22:27:06.441 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-09-07 22:27:06.444 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_002]
[2025-09-07 22:27:06.484 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-09-07 22:27:06.487 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_003]
[2025-09-07 22:27:06.526 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-09-07 22:27:06.528 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_004]
[2025-09-07 22:27:06.566 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-09-07 22:27:06.587 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_005]
[2025-09-07 22:27:06.627 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-09-07 22:27:06.661 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_010]
[2025-09-07 22:27:06.707 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
