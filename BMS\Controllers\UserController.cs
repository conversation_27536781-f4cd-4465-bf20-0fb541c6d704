using BMS.Models.Common;
using BMS.Models.DTOs;
using BMS.Services.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace BMS.Controllers
{
    /// <summary>
    /// 用户管理控制器
    /// </summary>
    [Authorize]
    [Route("[controller]")]
    public class UserController : Controller
    {
        private readonly IUserService _userService;

        public UserController(IUserService userService)
        {
            _userService = userService;
        }

        /// <summary>
        /// 用户管理首页
        /// </summary>
        /// <returns>用户管理页面</returns>
        [HttpGet]
        public IActionResult Index()
        {
            return View();
        }

        /// <summary>
        /// 获取用户列表（分页）
        /// </summary>
        /// <param name="queryDto">查询条件</param>
        /// <returns>分页结果</returns>
        [HttpPost("GetList")]
        public async Task<IActionResult> GetList([FromBody] UserQueryDto queryDto)
        {
            try
            {
                var result = await _userService.GetUsersAsync(queryDto);
                return Json(ApiResult.Ok(result));
            }
            catch (Exception ex)
            {
                return Json(ApiResult.Fail($"获取用户列表失败：{ex.Message}"));
            }
        }

        /// <summary>
        /// 根据ID获取用户信息
        /// </summary>
        /// <param name="id">用户ID</param>
        /// <returns>用户信息</returns>
        [HttpGet("GetById/{id}")]
        public async Task<IActionResult> GetById(int id)
        {
            try
            {
                var user = await _userService.GetUserByIdAsync(id);
                if (user == null)
                {
                    return Json(ApiResult.Fail("用户不存在"));
                }

                return Json(ApiResult.Ok(user));
            }
            catch (Exception ex)
            {
                return Json(ApiResult.Fail($"获取用户信息失败：{ex.Message}"));
            }
        }

        /// <summary>
        /// 创建用户
        /// </summary>
        /// <param name="createDto">创建用户DTO</param>
        /// <returns>创建结果</returns>
        [HttpPost("Create")]
        public async Task<IActionResult> Create([FromBody] UserCreateDto createDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    var errors = ModelState.Values
                        .SelectMany(v => v.Errors)
                        .Select(e => e.ErrorMessage);
                    return Json(ApiResult.Fail($"数据验证失败：{string.Join(",", errors)}"));
                }

                var result = await _userService.CreateUserAsync(createDto);
                return Json(result.Success ? ApiResult.Ok(result.Data, result.Message) : ApiResult.Fail(result.Message));
            }
            catch (Exception ex)
            {
                return Json(ApiResult.Fail($"创建用户失败：{ex.Message}"));
            }
        }

        /// <summary>
        /// 更新用户信息
        /// </summary>
        /// <param name="updateDto">更新用户DTO</param>
        /// <returns>更新结果</returns>
        [HttpPost("Update")]
        public async Task<IActionResult> Update([FromBody] UserUpdateDto updateDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    var errors = ModelState.Values
                        .SelectMany(v => v.Errors)
                        .Select(e => e.ErrorMessage);
                    return Json(ApiResult.Fail($"数据验证失败：{string.Join(",", errors)}"));
                }

                var result = await _userService.UpdateUserAsync(updateDto);
                return Json(result.Success ? ApiResult.Ok(result.Data, result.Message) : ApiResult.Fail(result.Message));
            }
            catch (Exception ex)
            {
                return Json(ApiResult.Fail($"更新用户失败：{ex.Message}"));
            }
        }

        /// <summary>
        /// 更新用户资产
        /// </summary>
        /// <param name="assetDto">用户资产更新DTO</param>
        /// <returns>更新结果</returns>
        [HttpPost("UpdateAsset")]
        public async Task<IActionResult> UpdateAsset([FromBody] UserAssetUpdateDto assetDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    var errors = ModelState.Values
                        .SelectMany(v => v.Errors)
                        .Select(e => e.ErrorMessage);
                    return Json(ApiResult.Fail($"数据验证失败：{string.Join(",", errors)}"));
                }

                var result = await _userService.UpdateUserAssetAsync(assetDto);
                return Json(result.Success ? ApiResult.Ok(result.Data, result.Message) : ApiResult.Fail(result.Message));
            }
            catch (Exception ex)
            {
                return Json(ApiResult.Fail($"更新用户资产失败：{ex.Message}"));
            }
        }

        /// <summary>
        /// 重置用户密码
        /// </summary>
        /// <param name="resetDto">重置密码DTO</param>
        /// <returns>重置结果</returns>
        [HttpPost("ResetPassword")]
        public async Task<IActionResult> ResetPassword([FromBody] UserResetPasswordDto resetDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    var errors = ModelState.Values
                        .SelectMany(v => v.Errors)
                        .Select(e => e.ErrorMessage);
                    return Json(ApiResult.Fail($"数据验证失败：{string.Join(",", errors)}"));
                }

                var result = await _userService.ResetPasswordAsync(resetDto);
                return Json(result.Success ? ApiResult.Ok(result.Data, result.Message) : ApiResult.Fail(result.Message));
            }
            catch (Exception ex)
            {
                return Json(ApiResult.Fail($"重置密码失败：{ex.Message}"));
            }
        }

        /// <summary>
        /// 删除用户
        /// </summary>
        /// <param name="deleteDto">删除用户DTO</param>
        /// <returns>删除结果</returns>
        [HttpPost("Delete")]
        public async Task<IActionResult> Delete([FromBody] UserDeleteDto deleteDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    var errors = ModelState.Values
                        .SelectMany(v => v.Errors)
                        .Select(e => e.ErrorMessage);
                    return Json(ApiResult.Fail($"数据验证失败：{string.Join(",", errors)}"));
                }

                var result = await _userService.DeleteUserAsync(deleteDto.Id);
                return Json(result.Success ? ApiResult.Ok(result.Data, result.Message) : ApiResult.Fail(result.Message));
            }
            catch (Exception ex)
            {
                return Json(ApiResult.Fail($"删除用户失败：{ex.Message}"));
            }
        }

        /// <summary>
        /// 检查用户名是否存在
        /// </summary>
        /// <param name="username">用户名</param>
        /// <param name="excludeId">排除的用户ID</param>
        /// <returns>检查结果</returns>
        [HttpGet("CheckUsername")]
        public async Task<IActionResult> CheckUsername(string username, int? excludeId = null)
        {
            try
            {
                var exists = await _userService.CheckUsernameExistsAsync(username, excludeId);
                return Json(ApiResult.Ok(exists));
            }
            catch (Exception ex)
            {
                return Json(ApiResult.Fail($"检查用户名失败：{ex.Message}"));
            }
        }

        /// <summary>
        /// 获取用户统计信息
        /// </summary>
        /// <returns>用户统计信息</returns>
        [HttpGet("GetStatistics")]
        public async Task<IActionResult> GetStatistics()
        {
            try
            {
                var statistics = await _userService.GetUserStatisticsAsync();
                return Json(ApiResult.Ok(statistics));
            }
            catch (Exception ex)
            {
                return Json(ApiResult.Fail($"获取用户统计信息失败：{ex.Message}"));
            }
        }
    }
} 