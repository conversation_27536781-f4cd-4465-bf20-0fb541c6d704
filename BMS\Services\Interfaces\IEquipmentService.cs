using BMS.Models.DTOs;
using BMS.Models.Common;

namespace BMS.Services.Interfaces
{
    /// <summary>
    /// 装备服务接口
    /// </summary>
    public interface IEquipmentService
    {
        /// <summary>
        /// 分页查询装备列表
        /// </summary>
        /// <param name="queryDto">查询条件</param>
        /// <returns>分页结果</returns>
        Task<PagedResult<EquipmentDto>> GetPagedListAsync(EquipmentQueryDto queryDto);

        /// <summary>
        /// 根据装备ID获取装备详情
        /// </summary>
        /// <param name="equipId">装备ID</param>
        /// <returns>装备详情</returns>
        Task<EquipmentDto?> GetByIdAsync(string equipId);

        /// <summary>
        /// 创建装备
        /// </summary>
        /// <param name="createDto">创建DTO</param>
        /// <returns>操作结果</returns>
        Task<ApiResult<bool>> CreateAsync(EquipmentCreateDto createDto);

        /// <summary>
        /// 更新装备
        /// </summary>
        /// <param name="updateDto">更新DTO</param>
        /// <returns>操作结果</returns>
        Task<ApiResult<bool>> UpdateAsync(EquipmentUpdateDto updateDto);

        /// <summary>
        /// 删除装备
        /// </summary>
        /// <param name="deleteDto">删除DTO</param>
        /// <returns>操作结果</returns>
        Task<ApiResult<bool>> DeleteAsync(EquipmentDeleteDto deleteDto);

        /// <summary>
        /// 检查装备ID是否存在
        /// </summary>
        /// <param name="equipId">装备ID</param>
        /// <returns>是否存在</returns>
        Task<bool> CheckEquipIdExistsAsync(string equipId);

        /// <summary>
        /// 获取装备类型列表
        /// </summary>
        /// <returns>装备类型列表</returns>
        Task<List<EquipmentTypeDto>> GetEquipmentTypesAsync();

        /// <summary>
        /// 获取五行属性列表
        /// </summary>
        /// <returns>五行属性列表</returns>
        Task<List<string>> GetElementsAsync();

        /// <summary>
        /// 获取五行限制列表
        /// </summary>
        /// <returns>五行限制列表</returns>
        Task<List<string>> GetElementLimitsAsync();

        // ==================== 装备类型管理 ====================

        /// <summary>
        /// 分页查询装备类型列表
        /// </summary>
        /// <param name="queryDto">查询条件</param>
        /// <returns>分页结果</returns>
        Task<PagedResult<EquipmentTypeDto>> GetEquipmentTypesPagedAsync(EquipmentTypeQueryDto queryDto);

        /// <summary>
        /// 根据装备类型ID获取装备类型详情
        /// </summary>
        /// <param name="equipTypeId">装备类型ID</param>
        /// <returns>装备类型详情</returns>
        Task<EquipmentTypeDto?> GetEquipmentTypeByIdAsync(string equipTypeId);

        /// <summary>
        /// 创建装备类型
        /// </summary>
        /// <param name="createDto">创建DTO</param>
        /// <returns>操作结果</returns>
        Task<ApiResult<bool>> CreateEquipmentTypeAsync(EquipmentTypeCreateDto createDto);

        /// <summary>
        /// 更新装备类型
        /// </summary>
        /// <param name="updateDto">更新DTO</param>
        /// <returns>操作结果</returns>
        Task<ApiResult<bool>> UpdateEquipmentTypeAsync(EquipmentTypeUpdateDto updateDto);

        /// <summary>
        /// 删除装备类型
        /// </summary>
        /// <param name="deleteDto">删除DTO</param>
        /// <returns>操作结果</returns>
        Task<ApiResult<bool>> DeleteEquipmentTypeAsync(EquipmentTypeDeleteDto deleteDto);

        /// <summary>
        /// 检查装备类型ID是否存在
        /// </summary>
        /// <param name="equipTypeId">装备类型ID</param>
        /// <returns>是否存在</returns>
        Task<bool> CheckEquipmentTypeIdExistsAsync(string equipTypeId);

        /// <summary>
        /// 测试装备数据
        /// </summary>
        /// <returns>测试结果</returns>
        Task<object> TestEquipmentDataAsync();
    }
}