using BMS.Models.DTOs;
using BMS.Models.Common;
using BMS.Models.Entities;
using BMS.Services.Interfaces;
using SqlSugar;

namespace BMS.Services.Implementations
{
    /// <summary>
    /// 道具配置服务实现
    /// </summary>
    public class ItemConfigService : IItemConfigService
    {
        private readonly IDbService _dbService;

        public ItemConfigService(IDbService dbService)
        {
            _dbService = dbService;
        }

        /// <summary>
        /// 分页获取道具配置列表
        /// </summary>
        /// <param name="queryDto">查询条件</param>
        /// <returns></returns>
        public async Task<PagedResult<ItemConfigDto>> GetPagedListAsync(ItemConfigQueryDto queryDto)
        {
            var query = _dbService.Queryable<item_config>();

            // 条件过滤
            if (queryDto.ItemNo.HasValue)
            {
                query = query.Where(x => x.item_no == queryDto.ItemNo.Value);
            }

            if (!string.IsNullOrWhiteSpace(queryDto.Name))
            {
                query = query.Where(x => x.name.Contains(queryDto.Name));
            }

            if (!string.IsNullOrWhiteSpace(queryDto.Type))
            {
                query = query.Where(x => x.type == queryDto.Type);
            }

            if (!string.IsNullOrWhiteSpace(queryDto.Quality))
            {
                query = query.Where(x => x.quality == queryDto.Quality);
            }

            // 分页查询
            RefAsync<int> totalCount = 0;
            var items = await query.OrderBy(x => x.item_no)
                .ToPageListAsync(queryDto.Page, queryDto.PageSize, totalCount);

            var dtoList = items.Select(MapToDto).ToList();

            return new PagedResult<ItemConfigDto>(dtoList, queryDto.Page, queryDto.PageSize, totalCount);
        }

        /// <summary>
        /// 根据ID获取道具配置
        /// </summary>
        /// <param name="id">道具配置ID</param>
        /// <returns></returns>
        public async Task<ItemConfigDto?> GetByIdAsync(int id)
        {
            var entity = await _dbService.Queryable<item_config>()
                .Where(x => x.id == id)
                .FirstAsync();

            return entity == null ? null : MapToDto(entity);
        }

        /// <summary>
        /// 根据道具编号获取道具配置
        /// </summary>
        /// <param name="itemNo">道具编号</param>
        /// <returns></returns>
        public async Task<ItemConfigDto?> GetByItemNoAsync(int itemNo)
        {
            var entity = await _dbService.Queryable<item_config>()
                .Where(x => x.item_no == itemNo)
                .FirstAsync();

            return entity == null ? null : MapToDto(entity);
        }

        /// <summary>
        /// 创建道具配置
        /// </summary>
        /// <param name="createDto">创建DTO</param>
        /// <returns></returns>
        public async Task<ApiResult<int>> CreateAsync(ItemConfigCreateDto createDto)
        {
            try
            {
                // 检查道具编号是否存在
                var exists = await IsItemNoExistsAsync(createDto.ItemNo);
                if (exists)
                {
                    return ApiResult<int>.Fail($"道具编号 {createDto.ItemNo} 已存在");
                }

                var entity = new item_config
                {
                    item_no = createDto.ItemNo,
                    name = createDto.Name,
                    type = createDto.Type,
                    description = createDto.Description,
                    quality = createDto.Quality,
                    icon = createDto.Icon,
                    price = createDto.Price ?? 0,
                    use_limit = createDto.UseLimit,
                    extra = null,
                    create_time = DateTime.Now
                };

                var result = await _dbService.Insertable(entity).ExecuteReturnIdentityAsync();
                return ApiResult<int>.Ok(result, "创建成功");
            }
            catch (Exception ex)
            {
                return ApiResult<int>.Fail($"创建失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 更新道具配置
        /// </summary>
        /// <param name="updateDto">更新DTO</param>
        /// <returns></returns>
        public async Task<ApiResult<bool>> UpdateAsync(ItemConfigUpdateDto updateDto)
        {
            try
            {
                // 检查记录是否存在
                var entity = await _dbService.Queryable<item_config>()
                    .Where(x => x.id == updateDto.Id)
                    .FirstAsync();

                if (entity == null)
                {
                    return ApiResult<bool>.Fail("道具配置不存在");
                }

                // 检查道具编号是否重复
                var exists = await IsItemNoExistsAsync(updateDto.ItemNo, updateDto.Id);
                if (exists)
                {
                    return ApiResult<bool>.Fail($"道具编号 {updateDto.ItemNo} 已存在");
                }

                entity.item_no = updateDto.ItemNo;
                entity.name = updateDto.Name;
                entity.type = updateDto.Type;
                entity.description = updateDto.Description;
                entity.quality = updateDto.Quality;
                entity.icon = updateDto.Icon;
                entity.price = updateDto.Price ?? 0;
                entity.use_limit = updateDto.UseLimit;
                entity.extra = null;

                var result = await _dbService.Updateable(entity).ExecuteCommandAsync();
                return result > 0 ? ApiResult<bool>.Ok(true, "更新成功") : ApiResult<bool>.Fail("更新失败");
            }
            catch (Exception ex)
            {
                return ApiResult<bool>.Fail($"更新失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 删除道具配置
        /// </summary>
        /// <param name="id">道具配置ID</param>
        /// <returns></returns>
        public async Task<ApiResult<bool>> DeleteAsync(int id)
        {
            try
            {
                // 检查记录是否存在
                var entity = await _dbService.Queryable<item_config>()
                    .Where(x => x.id == id)
                    .FirstAsync();

                if (entity == null)
                {
                    return ApiResult<bool>.Fail("道具配置不存在");
                }

                // 检查是否有用户拥有此道具
                var userItemExists = await _dbService.Queryable<user_item>()
                    .Where(x => x.item_id == entity.item_no.ToString())
                    .AnyAsync();

                if (userItemExists)
                {
                    return ApiResult<bool>.Fail("该道具已被用户拥有，无法删除");
                }

                var result = await _dbService.Deleteable<item_config>()
                    .Where(x => x.id == id)
                    .ExecuteCommandAsync();

                return result > 0 ? ApiResult<bool>.Ok(true, "删除成功") : ApiResult<bool>.Fail("删除失败");
            }
            catch (Exception ex)
            {
                return ApiResult<bool>.Fail($"删除失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 获取所有道具类型
        /// </summary>
        /// <returns></returns>
        public async Task<List<string>> GetAllTypesAsync()
        {
            var types = await _dbService.Queryable<item_config>()
                .Where(x => !string.IsNullOrEmpty(x.type))
                .Select(x => x.type)
                .Distinct()
                .ToListAsync();

            return types.Where(x => !string.IsNullOrWhiteSpace(x)).ToList();
        }

        /// <summary>
        /// 获取所有道具品质
        /// </summary>
        /// <returns></returns>
        public async Task<List<string>> GetAllQualitiesAsync()
        {
            var qualities = await _dbService.Queryable<item_config>()
                .Where(x => !string.IsNullOrEmpty(x.quality))
                .Select(x => x.quality)
                .Distinct()
                .ToListAsync();

            return qualities.Where(x => !string.IsNullOrWhiteSpace(x)).ToList();
        }

        /// <summary>
        /// 检查道具编号是否存在
        /// </summary>
        /// <param name="itemNo">道具编号</param>
        /// <param name="excludeId">排除的ID（用于更新时检查）</param>
        /// <returns></returns>
        public async Task<bool> IsItemNoExistsAsync(int itemNo, int? excludeId = null)
        {
            var query = _dbService.Queryable<item_config>().Where(x => x.item_no == itemNo);
            
            if (excludeId.HasValue)
            {
                query = query.Where(x => x.id != excludeId.Value);
            }

            return await query.AnyAsync();
        }

        /// <summary>
        /// 分页获取道具配置和脚本关联列表
        /// </summary>
        /// <param name="queryDto">查询条件</param>
        /// <returns></returns>
        public async Task<PagedResult<ItemConfigWithScriptDto>> GetPagedListWithScriptAsync(ItemConfigWithScriptQueryDto queryDto)
        {
            try
            {
                // 构建关联查询
                var query = _dbService.Queryable<item_config>()
                    .LeftJoin<item_script>((config, script) => config.item_no == script.item_no)
                    .Select((config, script) => new
                    {
                                            // 道具配置字段
                    id = config.id,
                    item_no = config.item_no,
                    name = config.name,
                    type = config.type,
                    description = config.description,
                    quality = config.quality,
                    icon = config.icon,
                    price = config.price,
                    use_limit = config.use_limit,
                    extra = config.extra,
                    create_time = config.create_time,
                    is_active = config.is_active,
                        // 脚本字段（使用别名避免冲突）
                        script_id = script.id,
                        script_item_no = script.item_no,
                        script_script = script.script,
                        script_description = script.description,
                        script_create_time = script.create_time
                    })
                    .MergeTable(); // 将多表结果集转换为单表

                // 添加查询条件
                if (queryDto.ItemNo.HasValue)
                {
                    query = query.Where(x => x.item_no == queryDto.ItemNo.Value);
                }

                if (!string.IsNullOrWhiteSpace(queryDto.Name))
                {
                    query = query.Where(x => x.name.Contains(queryDto.Name));
                }

                if (!string.IsNullOrWhiteSpace(queryDto.Type))
                {
                    query = query.Where(x => x.type == queryDto.Type);
                }

                if (!string.IsNullOrWhiteSpace(queryDto.Quality))
                {
                    query = query.Where(x => x.quality == queryDto.Quality);
                }

                // 根据启用状态筛选
                if (queryDto.IsActive.HasValue)
                {
                    query = query.Where(x => x.is_active == queryDto.IsActive.Value);
                }

                // 根据脚本内容筛选
                if (!string.IsNullOrWhiteSpace(queryDto.ScriptContent))
                {
                    query = query.Where(x => 
                        x.script_script.Contains(queryDto.ScriptContent) || 
                        x.script_description.Contains(queryDto.ScriptContent));
                }

                // 分页查询
                RefAsync<int> totalCount = 0;
                var items = await query.OrderBy(x => x.item_no)
                    .ToPageListAsync(queryDto.Page, queryDto.PageSize, totalCount);

                // 转换为DTO
                var dtoList = new List<ItemConfigWithScriptDto>();
                foreach (var item in items)
                {
                    // SqlSugar的JOIN查询返回匿名对象，需要动态访问属性
                    var config = new item_config
                    {
                        id = item.id,
                        item_no = item.item_no,
                        name = item.name,
                        type = item.type,
                        description = item.description,
                        quality = item.quality,
                        icon = item.icon,
                        price = item.price,
                        use_limit = item.use_limit,
                        extra = item.extra,
                        create_time = item.create_time,
                        is_active = item.is_active
                    };

                    // 检查是否有脚本数据
                    item_script? script = null;
                    if (item.script_id != null) // 如果脚本的id不为null，说明有关联的脚本
                    {
                        script = new item_script
                        {
                            id = item.script_id,
                            item_no = item.script_item_no,
                            script = item.script_script,
                            description = item.script_description,
                            create_time = item.script_create_time
                        };
                    }

                    dtoList.Add(MapToDtoWithScript(config, script));
                }

                return new PagedResult<ItemConfigWithScriptDto>(dtoList, queryDto.Page, queryDto.PageSize, totalCount);
            }
            catch (Exception ex)
            {
                // 记录异常信息
                Console.WriteLine($"GetPagedListWithScriptAsync Error: {ex.Message}");
                Console.WriteLine($"StackTrace: {ex.StackTrace}");
                throw; // 重新抛出异常，让上层处理
            }
        }

        /// <summary>
        /// 根据ID获取道具配置和脚本关联信息
        /// </summary>
        /// <param name="id">道具配置ID</param>
        /// <returns></returns>
        public async Task<ItemConfigWithScriptDto?> GetByIdWithScriptAsync(int id)
        {
            try
            {
                // 查询道具配置
                var config = await _dbService.Queryable<item_config>()
                    .Where(x => x.id == id)
                    .FirstAsync();

                if (config == null)
                {
                    return null;
                }

                // 查询对应的脚本
                var script = await _dbService.Queryable<item_script>()
                    .Where(x => x.item_no == config.item_no)
                    .FirstAsync();

                return MapToDtoWithScript(config, script);
            }
            catch (Exception ex)
            {
                // 记录异常信息
                Console.WriteLine($"GetByIdWithScriptAsync Error: {ex.Message}");
                Console.WriteLine($"StackTrace: {ex.StackTrace}");
                throw; // 重新抛出异常，让上层处理
            }
        }

        /// <summary>
        /// 创建道具配置和脚本关联
        /// </summary>
        /// <param name="createDto">创建DTO</param>
        /// <returns></returns>
        public async Task<ApiResult<int>> CreateWithScriptAsync(ItemConfigWithScriptCreateDto createDto)
        {
            try
            {
                // 检查道具编号是否存在
                var exists = await IsItemNoExistsAsync(createDto.Config.ItemNo);
                if (exists)
                {
                    return ApiResult<int>.Fail($"道具编号 {createDto.Config.ItemNo} 已存在");
                }

                // 开启事务
                var result = await _dbService.GetClient().Ado.UseTranAsync(async () =>
                {
                    // 创建道具配置
                    var configEntity = new item_config
                    {
                        item_no = createDto.Config.ItemNo,
                        name = createDto.Config.Name,
                        type = createDto.Config.Type,
                        description = createDto.Config.Description,
                        quality = createDto.Config.Quality,
                        icon = createDto.Config.Icon,
                        price = createDto.Config.Price ?? 0,
                        use_limit = createDto.Config.UseLimit,
                        extra = createDto.Config.Extra?.ToString(),
                        is_active = createDto.Config.IsActive,
                        create_time = DateTime.Now
                    };

                    var configId = await _dbService.Insertable(configEntity).ExecuteReturnIdentityAsync();

                    // 如果有脚本信息，创建脚本
                    if (createDto.Script != null && !string.IsNullOrWhiteSpace(createDto.Script.Script))
                    {
                        var scriptEntity = new item_script
                        {
                            item_no = createDto.Config.ItemNo,
                            script = createDto.Script.Script,
                            description = createDto.Script.Description,
                            create_time = DateTime.Now
                        };

                        await _dbService.Insertable(scriptEntity).ExecuteCommandAsync();
                    }

                    return configId;
                });

                return ApiResult<int>.Ok(result.Data, "创建成功");
            }
            catch (Exception ex)
            {
                return ApiResult<int>.Fail($"创建失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 更新道具配置和脚本关联
        /// </summary>
        /// <param name="updateDto">更新DTO</param>
        /// <returns></returns>
        public async Task<ApiResult<bool>> UpdateWithScriptAsync(ItemConfigWithScriptUpdateDto updateDto)
        {
            try
            {
                Console.WriteLine($"UpdateWithScriptAsync 开始，ID: {updateDto.Config.Id}");
                Console.WriteLine($"更新数据: {System.Text.Json.JsonSerializer.Serialize(updateDto)}");

                // 检查记录是否存在
                var configEntity = await _dbService.Queryable<item_config>()
                    .Where(x => x.id == updateDto.Config.Id)
                    .FirstAsync();

                if (configEntity == null)
                {
                    Console.WriteLine($"道具配置不存在，ID: {updateDto.Config.Id}");
                    return ApiResult<bool>.Fail("道具配置不存在");
                }

                Console.WriteLine($"找到现有配置: ID={configEntity.id}, Name={configEntity.name}");
                Console.WriteLine($"更新前数据: ItemNo={configEntity.item_no}, Name={configEntity.name}, IsActive={configEntity.is_active}");
                Console.WriteLine($"更新目标数据: ItemNo={updateDto.Config.ItemNo}, Name={updateDto.Config.Name}, IsActive={updateDto.Config.IsActive}");

                // 检查道具编号是否重复
                var exists = await IsItemNoExistsAsync(updateDto.Config.ItemNo, updateDto.Config.Id);
                if (exists)
                {
                    Console.WriteLine($"道具编号重复检查失败: {updateDto.Config.ItemNo}");
                    return ApiResult<bool>.Fail($"道具编号 {updateDto.Config.ItemNo} 已存在");
                }

                // 开启事务
                await _dbService.GetClient().Ado.UseTranAsync(async () =>
                {

                    // 更新道具配置，保留原始的create_time
                    var updateConfig = new item_config
                    {
                        id = updateDto.Config.Id,
                        item_no = updateDto.Config.ItemNo,
                        name = updateDto.Config.Name,
                        type = updateDto.Config.Type,
                        description = updateDto.Config.Description,
                        quality = updateDto.Config.Quality,
                        icon = updateDto.Config.Icon,
                        price = updateDto.Config.Price ?? 0,
                        use_limit = updateDto.Config.UseLimit,
                        extra = updateDto.Config.Extra?.ToString(),
                        is_active = updateDto.Config.IsActive,
                        create_time = configEntity.create_time, // 保留原始创建时间
                        update_time = DateTime.Now
                    };

                    Console.WriteLine($"准备更新配置: {System.Text.Json.JsonSerializer.Serialize(updateConfig)}");

                    // 方法1：使用实体更新（推荐）
                    var updateResult = await _dbService.Updateable(updateConfig)
                        .Where(x => x.id == updateDto.Config.Id)
                        .ExecuteCommandAsync();

                    // 方法2：如果方法1不行，使用字段更新
                    if (updateResult <= 0)
                    {
                        Console.WriteLine("实体更新失败，尝试字段更新");

                        // 预处理可能为空的值
                        var priceValue = updateDto.Config.Price ?? 0;
                        var extraValue = updateDto.Config.Extra?.ToString() ?? "";

                        updateResult = await _dbService.Updateable<item_config>()
                            .SetColumns(x => new item_config
                            {
                                item_no = updateDto.Config.ItemNo,
                                name = updateDto.Config.Name,
                                type = updateDto.Config.Type,
                                description = updateDto.Config.Description,
                                quality = updateDto.Config.Quality,
                                icon = updateDto.Config.Icon,
                                price = priceValue,
                                use_limit = updateDto.Config.UseLimit,
                                extra = extraValue,
                                is_active = updateDto.Config.IsActive,
                                update_time = DateTime.Now
                            })
                            .Where(x => x.id == updateDto.Config.Id)
                            .ExecuteCommandAsync();
                    }

                    Console.WriteLine($"配置更新结果: {updateResult} 行受影响");

                    if (updateResult <= 0)
                    {
                        throw new Exception($"道具配置更新失败，没有行受影响。ID: {updateDto.Config.Id}");
                    }

                    // 验证更新结果
                    var updatedEntity = await _dbService.Queryable<item_config>()
                        .Where(x => x.id == updateDto.Config.Id)
                        .FirstAsync();

                    if (updatedEntity != null)
                    {
                        Console.WriteLine($"更新后验证: Name={updatedEntity.name}, IsActive={updatedEntity.is_active}, UpdateTime={updatedEntity.update_time}");
                    }
                    else
                    {
                        throw new Exception($"更新后验证失败，找不到记录。ID: {updateDto.Config.Id}");
                    }

                    // 处理脚本信息
                    if (updateDto.Script != null)
                    {
                        // 检查是否已存在脚本
                        var existingScript = await _dbService.Queryable<item_script>()
                            .Where(x => x.item_no == updateDto.Config.ItemNo)
                            .FirstAsync();

                        if (existingScript != null)
                        {
                            // 更新现有脚本
                            var updateScript = new item_script
                            {
                                id = existingScript.id,
                                item_no = updateDto.Config.ItemNo,
                                script = updateDto.Script.Script,
                                description = updateDto.Script.Description,
                                create_time = existingScript.create_time
                            };

                            await _dbService.Updateable(updateScript).ExecuteCommandAsync();
                        }
                        else if (!string.IsNullOrWhiteSpace(updateDto.Script.Script))
                        {
                            // 创建新脚本
                            var newScript = new item_script
                            {
                                item_no = updateDto.Config.ItemNo,
                                script = updateDto.Script.Script,
                                description = updateDto.Script.Description,
                                create_time = DateTime.Now
                            };

                            await _dbService.Insertable(newScript).ExecuteCommandAsync();
                        }
                    }
                });

                Console.WriteLine("UpdateWithScriptAsync 事务执行完成");
                return ApiResult<bool>.Ok(true, "更新成功");
            }
            catch (Exception ex)
            {
                return ApiResult<bool>.Fail($"更新失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 批量更新道具状态
        /// </summary>
        /// <param name="batchDto">批量更新DTO</param>
        /// <returns></returns>
        public async Task<ApiResult<bool>> BatchUpdateStatusAsync(ItemConfigBatchUpdateStatusDto batchDto)
        {
            try
            {
                if (batchDto.Ids == null || batchDto.Ids.Count == 0)
                {
                    return ApiResult<bool>.Fail("道具ID列表不能为空");
                }

                var updateCount = await  _dbService.Updateable<item_config>()
                    .SetColumns(x => x.is_active == batchDto.IsActive)
                    .Where(x => batchDto.Ids.Contains(x.id))
                    .ExecuteCommandAsync();

                return ApiResult<bool>.Ok(true, $"成功更新 {updateCount} 个道具的状态");
            }
            catch (Exception ex)
            {
                return ApiResult<bool>.Fail($"批量更新失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 实体转DTO
        /// </summary>
        /// <param name="entity">实体</param>
        /// <returns></returns>
        private static ItemConfigDto MapToDto(item_config entity)
        {
            return new ItemConfigDto
            {
                Id = entity.id,
                ItemNo = entity.item_no,
                Name = entity.name,
                Type = entity.type,
                Description = entity.description,
                Quality = entity.quality,
                Icon = entity.icon,
                Price = entity.price,
                UseLimit = entity.use_limit,
                Extra = entity.extra,
                IsActive = entity.is_active ?? true,
                CreateTime = entity.create_time
            };
        }

        /// <summary>
        /// 映射到关联DTO
        /// </summary>
        /// <param name="config">道具配置实体</param>
        /// <param name="script">脚本实体</param>
        /// <returns></returns>
        private static ItemConfigWithScriptDto MapToDtoWithScript(item_config config, item_script? script)
        {
            var scriptDto = script == null ? null : new ItemScriptDto
            {
                Id = script.id,
                ItemNo = script.item_no,
                Script = script.script,
                Description = script.description,
                CreateTime = script.create_time
            };

            return new ItemConfigWithScriptDto
            {
                Id = config.id,
                ItemNo = config.item_no,
                Name = config.name,
                Type = config.type,
                Description = config.description,
                Quality = config.quality,
                Icon = config.icon,
                Price = config.price,
                UseLimit = config.use_limit,
                Extra = config.extra,
                CreateTime = config.create_time,
                IsActive = config.is_active ?? true,
                Script = scriptDto
            };
        }
    }
} 