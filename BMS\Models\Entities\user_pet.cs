﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace BMS.Models.Entities
{
    ///<summary>
    ///用户宠物表
    ///</summary>
    [SugarTable("user_pet")]
    public partial class user_pet
    {
           public user_pet(){
               // 设置默认值
               deepen = 0.00m;
               offset = 0.00m;
               vamp = 0.00m;
               vamp_mp = 0.00m;
               realm = "元神初具";
               level = 0;
           }
           /// <summary>
           /// Desc:自增主键
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true,IsIdentity=true)]
           public int id {get;set;}

           /// <summary>
           /// Desc:所属用户ID（需与用户表关联）
           /// Default:
           /// Nullable:False
           /// </summary>           
           public int user_id {get;set;}

           /// <summary>
           /// Desc:宠物序号
           /// Default:
           /// Nullable:False
           /// </summary>           
           public int pet_no {get;set;}

           /// <summary>
           /// Desc:形象编号
           /// Default:
           /// Nullable:True
           /// </summary>           
           public int? image {get;set;}

           /// <summary>
           /// Desc:当前经验
           /// Default:0
           /// Nullable:True
           /// </summary>           
           public long? exp {get;set;}

           /// <summary>
           /// Desc:生命值
           /// Default:0
           /// Nullable:True
           /// </summary>           
           public long? hp {get;set;}

           /// <summary>
           /// Desc:魔法值
           /// Default:0
           /// Nullable:True
           /// </summary>           
           public long? mp {get;set;}

           /// <summary>
           /// Desc:攻击力
           /// Default:0
           /// Nullable:True
           /// </summary>           
           public long? atk {get;set;}

           /// <summary>
           /// Desc:防御力
           /// Default:0
           /// Nullable:True
           /// </summary>           
           public long? def {get;set;}

           /// <summary>
           /// Desc:速度
           /// Default:0
           /// Nullable:True
           /// </summary>           
           public long? spd {get;set;}

           /// <summary>
           /// Desc:状态
           /// Default:0
           /// Nullable:True
           /// </summary>           
           public int? state {get;set;}

           /// <summary>
           /// Desc:闪避
           /// Default:0
           /// Nullable:True
           /// </summary>           
           public long? dodge {get;set;}

           /// <summary>
           /// Desc:自定义宠物名称
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? name {get;set;}

           /// <summary>
           /// Desc:成长值
           /// Default:0.000000
           /// Nullable:True
           /// </summary>           
           public decimal? growth {get;set;}

           /// <summary>
           /// Desc:命中
           /// Default:0
           /// Nullable:True
           /// </summary>           
           public long? hit {get;set;}

           /// <summary>
           /// Desc:加深伤害
           /// Default:0.00
           /// Nullable:True
           /// </summary>           
           public decimal? deepen {get;set;}

           /// <summary>
           /// Desc:抵消伤害
           /// Default:0.00
           /// Nullable:True
           /// </summary>           
           public decimal? offset {get;set;}

           /// <summary>
           /// Desc:吸血比例
           /// Default:0.00
           /// Nullable:True
           /// </summary>           
           public decimal? vamp {get;set;}

           /// <summary>
           /// Desc:吸魔比例
           /// Default:0.00
           /// Nullable:True
           /// </summary>           
           public decimal? vamp_mp {get;set;}

           /// <summary>
           /// Desc:自定义宠物名称
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? custom_name {get;set;}

           /// <summary>
           /// Desc:法宝状态
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? talisman_state {get;set;}

           /// <summary>
           /// Desc:境界
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? realm {get;set;}

           /// <summary>
           /// Desc:已进化次数
           /// Default:0
           /// Nullable:True
           /// </summary>           
           public int? evolve_count {get;set;}

           /// <summary>
           /// Desc:合成次数
           /// Default:0
           /// Nullable:True
           /// </summary>           
           public int? synthesis_count {get;set;}

           /// <summary>
           /// Desc:转生次数
           /// Default:0
           /// Nullable:True
           /// </summary>           
           public int? nirvana_count {get;set;}

           /// <summary>
           /// Desc:最后进化时间
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? last_evolution_time {get;set;}

           /// <summary>
           /// Desc:最后合成时间
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? last_synthesis_time {get;set;}

           /// <summary>
           /// Desc:最后转生时间
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? last_nirvana_time {get;set;}

           /// <summary>
           /// Desc:原始宠物编号（用于追溯）
           /// Default:
           /// Nullable:True
           /// </summary>           
           public int? original_pet_no {get;set;}

           /// <summary>
           /// Desc:父代主宠ID（合成/转生来源）
           /// Default:
           /// Nullable:True
           /// </summary>           
           public int? parent_main_pet_id {get;set;}

           /// <summary>
           /// Desc:父代副宠ID（合成/转生来源）
           /// Default:
           /// Nullable:True
           /// </summary>           
           public int? parent_sub_pet_id {get;set;}

           /// <summary>
           /// Desc:指定五行
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? element {get;set;}

           /// <summary>
           /// Desc:创建时间
           /// Default:CURRENT_TIMESTAMP
           /// Nullable:True
           /// </summary>           
           public DateTime? create_time {get;set;}

           /// <summary>
           /// Desc:宠物状态（牧场、携带、丢弃）
           /// Default:牧场
           /// Nullable:False
           /// </summary>           
           public string status {get;set;}

           /// <summary>
           /// Desc:是否主战宠物
           /// Default:true
           /// Nullable:False
           /// </summary>           
           public bool is_main {get;set;}

           /// <summary>
           /// Desc:等级
           /// Default:
           /// Nullable:True
           /// </summary>           
           public int? level {get;set;}

           /// <summary>
           /// Desc:最大生命值
           /// Default:
           /// Nullable:True
           /// </summary>           
           public long? max_hp {get;set;}

           /// <summary>
           /// Desc:最大魔法值
           /// Default:
           /// Nullable:True
           /// </summary>
           public long? max_mp {get;set;}

           /// <summary>
           /// Desc:当前魔法值（战斗中消耗）
           /// Default:
           /// Nullable:True
           /// </summary>
           public long? current_mp {get;set;}

    }
}
