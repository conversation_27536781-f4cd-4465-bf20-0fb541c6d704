using BMS.Models.DTOs;
using BMS.Models.Common;

namespace BMS.Services.Interfaces
{
    /// <summary>
    /// 地图配置服务接口
    /// </summary>
    public interface IMapConfigService
    {
        /// <summary>
        /// 分页获取地图配置列表
        /// </summary>
        /// <param name="queryDto">查询条件</param>
        /// <returns>分页结果</returns>
        Task<PagedResult<MapConfigDto>> GetPagedListAsync(MapConfigQueryDto queryDto);

        /// <summary>
        /// 根据ID获取地图配置
        /// </summary>
        /// <param name="id">地图配置ID</param>
        /// <returns>地图配置信息</returns>
        Task<MapConfigDto?> GetByIdAsync(int id);

        /// <summary>
        /// 根据地图ID获取地图配置
        /// </summary>
        /// <param name="mapId">地图ID</param>
        /// <returns>地图配置信息</returns>
        Task<MapConfigDto?> GetByMapIdAsync(int mapId);

        /// <summary>
        /// 创建地图配置
        /// </summary>
        /// <param name="createDto">创建DTO</param>
        /// <returns>操作结果</returns>
        Task<ApiResult<bool>> CreateAsync(MapConfigCreateDto createDto);

        /// <summary>
        /// 更新地图配置
        /// </summary>
        /// <param name="updateDto">更新DTO</param>
        /// <returns>操作结果</returns>
        Task<ApiResult<bool>> UpdateAsync(MapConfigUpdateDto updateDto);

        /// <summary>
        /// 删除地图配置
        /// </summary>
        /// <param name="id">地图配置ID</param>
        /// <returns>操作结果</returns>
        Task<ApiResult<bool>> DeleteAsync(int id);

        /// <summary>
        /// 检查地图ID是否存在
        /// </summary>
        /// <param name="mapId">地图ID</param>
        /// <param name="id">排除的ID（编辑时使用）</param>
        /// <returns>是否存在</returns>
        Task<bool> CheckMapIdExistsAsync(int mapId, int? id = null);

        /// <summary>
        /// 获取所有地图配置（用于下拉选择）
        /// </summary>
        /// <returns>地图配置列表</returns>
        Task<List<MapConfigDto>> GetAllAsync();
    }
} 