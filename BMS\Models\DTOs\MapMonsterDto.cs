using System.ComponentModel.DataAnnotations;

namespace BMS.Models.DTOs
{
    /// <summary>
    /// 地图怪物DTO
    /// </summary>
    public class MapMonsterDto
    {
        /// <summary>
        /// 自增主键
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 地图ID
        /// </summary>
        public int MapId { get; set; }

        /// <summary>
        /// 地图名称
        /// </summary>
        public string MapName { get; set; } = string.Empty;

        /// <summary>
        /// 怪物序号
        /// </summary>
        public int MonsterId { get; set; }

        /// <summary>
        /// 怪物名字
        /// </summary>
        public string MonsterName { get; set; } = string.Empty;

        /// <summary>
        /// 怪物成长
        /// </summary>
        public long Growth { get; set; }

        /// <summary>
        /// 怪物五行
        /// </summary>
        public string? Element { get; set; }

        /// <summary>
        /// 最大等级
        /// </summary>
        public int MaxLevel { get; set; }

        /// <summary>
        /// 最小等级
        /// </summary>
        public int MinLevel { get; set; }

        /// <summary>
        /// 最大掉落
        /// </summary>
        public int MaxDrop { get; set; }

        /// <summary>
        /// 经验值
        /// </summary>
        public long Exp { get; set; }

        /// <summary>
        /// 生命
        /// </summary>
        public long Hp { get; set; }

        /// <summary>
        /// 魔法
        /// </summary>
        public long Mp { get; set; }

        /// <summary>
        /// 最大生命
        /// </summary>
        public long MaxHp { get; set; }

        /// <summary>
        /// 最大魔法
        /// </summary>
        public long MaxMp { get; set; }

        /// <summary>
        /// 攻击
        /// </summary>
        public long Atk { get; set; }

        /// <summary>
        /// 防御
        /// </summary>
        public long Def { get; set; }

        /// <summary>
        /// 闪避
        /// </summary>
        public long Dodge { get; set; }

        /// <summary>
        /// 速度
        /// </summary>
        public long Spd { get; set; }
    }

    /// <summary>
    /// 地图怪物查询DTO
    /// </summary>
    public class MapMonsterQueryDto
    {
        /// <summary>
        /// 地图ID
        /// </summary>
        public int? MapId { get; set; }

        /// <summary>
        /// 怪物名称（支持模糊查询）
        /// </summary>
        public string? MonsterName { get; set; }

        /// <summary>
        /// 怪物五行
        /// </summary>
        public string? Element { get; set; }

        /// <summary>
        /// 页码
        /// </summary>
        public int Page { get; set; } = 1;

        /// <summary>
        /// 每页数量
        /// </summary>
        public int PageSize { get; set; } = 10;
    }

    /// <summary>
    /// 创建地图怪物DTO
    /// </summary>
    public class MapMonsterCreateDto
    {
        /// <summary>
        /// 地图ID
        /// </summary>
        [Required(ErrorMessage = "地图ID不能为空")]
        [Range(1, int.MaxValue, ErrorMessage = "地图ID必须大于0")]
        public int MapId { get; set; }

        /// <summary>
        /// 怪物序号
        /// </summary>
        [Required(ErrorMessage = "怪物序号不能为空")]
        [Range(1, int.MaxValue, ErrorMessage = "怪物序号必须大于0")]
        public int MonsterId { get; set; }

        /// <summary>
        /// 怪物名字
        /// </summary>
        [Required(ErrorMessage = "怪物名字不能为空")]
        [StringLength(50, ErrorMessage = "怪物名字长度不能超过50个字符")]
        public string MonsterName { get; set; } = string.Empty;

        /// <summary>
        /// 怪物成长
        /// </summary>
        [Range(0, long.MaxValue, ErrorMessage = "怪物成长不能为负数")]
        public long Growth { get; set; }

        /// <summary>
        /// 怪物五行
        /// </summary>
        [StringLength(10, ErrorMessage = "怪物五行长度不能超过10个字符")]
        public string? Element { get; set; }

        /// <summary>
        /// 最大等级
        /// </summary>
        [Range(0, int.MaxValue, ErrorMessage = "最大等级不能为负数")]
        public int MaxLevel { get; set; }

        /// <summary>
        /// 最小等级
        /// </summary>
        [Range(0, int.MaxValue, ErrorMessage = "最小等级不能为负数")]
        public int MinLevel { get; set; }

        /// <summary>
        /// 最大掉落
        /// </summary>
        [Range(0, int.MaxValue, ErrorMessage = "最大掉落不能为负数")]
        public int MaxDrop { get; set; }

        /// <summary>
        /// 经验值
        /// </summary>
        [Range(0, long.MaxValue, ErrorMessage = "经验值不能为负数")]
        public long Exp { get; set; }

        /// <summary>
        /// 生命
        /// </summary>
        [Range(0, long.MaxValue, ErrorMessage = "生命不能为负数")]
        public long Hp { get; set; }

        /// <summary>
        /// 魔法
        /// </summary>
        [Range(0, long.MaxValue, ErrorMessage = "魔法不能为负数")]
        public long Mp { get; set; }

        /// <summary>
        /// 最大生命
        /// </summary>
        [Range(0, long.MaxValue, ErrorMessage = "最大生命不能为负数")]
        public long MaxHp { get; set; }

        /// <summary>
        /// 最大魔法
        /// </summary>
        [Range(0, long.MaxValue, ErrorMessage = "最大魔法不能为负数")]
        public long MaxMp { get; set; }

        /// <summary>
        /// 攻击
        /// </summary>
        [Range(0, long.MaxValue, ErrorMessage = "攻击不能为负数")]
        public long Atk { get; set; }

        /// <summary>
        /// 防御
        /// </summary>
        [Range(0, long.MaxValue, ErrorMessage = "防御不能为负数")]
        public long Def { get; set; }

        /// <summary>
        /// 闪避
        /// </summary>
        [Range(0, long.MaxValue, ErrorMessage = "闪避不能为负数")]
        public long Dodge { get; set; }

        /// <summary>
        /// 速度
        /// </summary>
        [Range(0, long.MaxValue, ErrorMessage = "速度不能为负数")]
        public long Spd { get; set; }
    }

    /// <summary>
    /// 更新地图怪物DTO
    /// </summary>
    public class MapMonsterUpdateDto
    {
        /// <summary>
        /// 自增主键
        /// </summary>
        [Required(ErrorMessage = "ID不能为空")]
        public int Id { get; set; }

        /// <summary>
        /// 地图ID
        /// </summary>
        [Required(ErrorMessage = "地图ID不能为空")]
        [Range(1, int.MaxValue, ErrorMessage = "地图ID必须大于0")]
        public int MapId { get; set; }

        /// <summary>
        /// 怪物序号
        /// </summary>
        [Required(ErrorMessage = "怪物序号不能为空")]
        [Range(1, int.MaxValue, ErrorMessage = "怪物序号必须大于0")]
        public int MonsterId { get; set; }

        /// <summary>
        /// 怪物名字
        /// </summary>
        [Required(ErrorMessage = "怪物名字不能为空")]
        [StringLength(50, ErrorMessage = "怪物名字长度不能超过50个字符")]
        public string MonsterName { get; set; } = string.Empty;

        /// <summary>
        /// 怪物成长
        /// </summary>
        [Range(0, long.MaxValue, ErrorMessage = "怪物成长不能为负数")]
        public long Growth { get; set; }

        /// <summary>
        /// 怪物五行
        /// </summary>
        [StringLength(10, ErrorMessage = "怪物五行长度不能超过10个字符")]
        public string? Element { get; set; }

        /// <summary>
        /// 最大等级
        /// </summary>
        [Range(0, int.MaxValue, ErrorMessage = "最大等级不能为负数")]
        public int MaxLevel { get; set; }

        /// <summary>
        /// 最小等级
        /// </summary>
        [Range(0, int.MaxValue, ErrorMessage = "最小等级不能为负数")]
        public int MinLevel { get; set; }

        /// <summary>
        /// 最大掉落
        /// </summary>
        [Range(0, int.MaxValue, ErrorMessage = "最大掉落不能为负数")]
        public int MaxDrop { get; set; }

        /// <summary>
        /// 经验值
        /// </summary>
        [Range(0, long.MaxValue, ErrorMessage = "经验值不能为负数")]
        public long Exp { get; set; }

        /// <summary>
        /// 生命
        /// </summary>
        [Range(0, long.MaxValue, ErrorMessage = "生命不能为负数")]
        public long Hp { get; set; }

        /// <summary>
        /// 魔法
        /// </summary>
        [Range(0, long.MaxValue, ErrorMessage = "魔法不能为负数")]
        public long Mp { get; set; }

        /// <summary>
        /// 最大生命
        /// </summary>
        [Range(0, long.MaxValue, ErrorMessage = "最大生命不能为负数")]
        public long MaxHp { get; set; }

        /// <summary>
        /// 最大魔法
        /// </summary>
        [Range(0, long.MaxValue, ErrorMessage = "最大魔法不能为负数")]
        public long MaxMp { get; set; }

        /// <summary>
        /// 攻击
        /// </summary>
        [Range(0, long.MaxValue, ErrorMessage = "攻击不能为负数")]
        public long Atk { get; set; }

        /// <summary>
        /// 防御
        /// </summary>
        [Range(0, long.MaxValue, ErrorMessage = "防御不能为负数")]
        public long Def { get; set; }

        /// <summary>
        /// 闪避
        /// </summary>
        [Range(0, long.MaxValue, ErrorMessage = "闪避不能为负数")]
        public long Dodge { get; set; }

        /// <summary>
        /// 速度
        /// </summary>
        [Range(0, long.MaxValue, ErrorMessage = "速度不能为负数")]
        public long Spd { get; set; }
    }

    /// <summary>
    /// 删除地图怪物DTO
    /// </summary>
    public class MapMonsterDeleteDto
    {
        /// <summary>
        /// 要删除的地图怪物ID
        /// </summary>
        [Required(ErrorMessage = "ID不能为空")]
        [Range(1, int.MaxValue, ErrorMessage = "ID必须大于0")]
        public int Id { get; set; }
    }
} 