@{
    ViewData["Title"] = "用户装备管理";
}

<!-- 科幻背景 -->
<div class="cyber-bg"></div>

<!-- 科幻用户装备管理应用容器 -->
<div id="userEquipmentApp">
    <!-- 科幻页面标题 -->
    <div class="container-fluid py-4">
        <div class="row mb-4">
            <div class="col-12">
                <div class="cyber-card">
                    <div class="cyber-card-header">
                        <div class="cyber-icon">🛡️</div>
                        <h1 class="cyber-card-title">用户装备管理</h1>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 科幻主要内容 -->
    <div class="container-fluid">

        <!-- 科幻搜索条件 -->
        <div class="cyber-card">
            <div class="cyber-card-header">
                <div class="cyber-icon">🔍</div>
                <h3 class="cyber-card-title">搜索条件</h3>
            </div>
            <div class="cyber-card-body">
                <div class="row">
                    <div class="col-md-3">
                        <div class="cyber-form-group">
                            <label class="cyber-form-label" for="searchUserId">用户ID</label>
                            <input type="number" class="cyber-form-control" id="searchUserId" v-model="queryForm.userId" placeholder="请输入用户ID">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="cyber-form-group">
                            <label class="cyber-form-label" for="searchUserName">用户名</label>
                            <input type="text" class="cyber-form-control" id="searchUserName" v-model="queryForm.userName" placeholder="请输入用户名">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="cyber-form-group">
                            <label class="cyber-form-label" for="searchEquipmentName">装备名称</label>
                            <input type="text" class="cyber-form-control" id="searchEquipmentName" v-model="queryForm.name" placeholder="请输入装备名称">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="cyber-form-group">
                            <label class="cyber-form-label" for="searchEquipmentType">装备类型</label>
                            <div class="cyber-select-wrapper">
                                <select class="cyber-form-control" id="searchEquipmentType" v-model="queryForm.equipTypeId">
                                    <option value="">全部类型</option>
                                    <option v-for="option in equipmentTypeOptions" v-bind:key="option.value" v-bind:value="option.value">{{ option.label }}</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-3">
                        <div class="cyber-form-group">
                            <label class="cyber-form-label" for="searchEquipmentStatus">装备状态</label>
                            <div class="cyber-select-wrapper">
                                <select class="cyber-form-control" id="searchEquipmentStatus" v-model="queryForm.isEquipped">
                                    <option value="">全部状态</option>
                                    <option value="true">已装备</option>
                                    <option value="false">未装备</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="cyber-form-group">
                            <label class="cyber-form-label" for="searchMinLevel">最低强化等级</label>
                            <input type="number" class="cyber-form-control" id="searchMinLevel" v-model="queryForm.minStrengthenLevel" placeholder="最低等级" min="0" max="20">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="cyber-form-group">
                            <label class="cyber-form-label" for="searchMaxLevel">最高强化等级</label>
                            <input type="number" class="cyber-form-control" id="searchMaxLevel" v-model="queryForm.maxStrengthenLevel" placeholder="最高等级" min="0" max="20">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="cyber-form-group">
                            <label class="cyber-form-label">&nbsp;</label>
                            <div class="d-flex gap-2 flex-wrap">
                                <button type="button" class="cyber-btn cyber-btn-primary" v-on:click="searchEquipments">
                                    <i class="fas fa-search"></i> 搜索
                                </button>
                                <button type="button" class="cyber-btn cyber-btn-outline" v-on:click="resetSearch">
                                    <i class="fas fa-redo"></i> 重置
                                </button>
                                <button type="button" class="cyber-btn cyber-btn-success" v-on:click="showAddModal">
                                    <i class="fas fa-plus"></i> 新增装备
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 科幻装备列表 -->
        <div class="cyber-card">
            <div class="cyber-card-header">
                <div class="cyber-icon">📋</div>
                <h3 class="cyber-card-title">用户装备列表</h3>
            </div>
            <div class="cyber-card-body">
                <div class="cyber-table-container">
                    <table class="cyber-table">
                        <thead class="cyber-thead">
                            <tr>
                                <th class="cyber-th">ID</th>
                                <th class="cyber-th">用户</th>
                                <th class="cyber-th">装备名称</th>
                                <th class="cyber-th">装备类型</th>
                                <th class="cyber-th">强化等级</th>
                                <th class="cyber-th">槽位</th>
                                <th class="cyber-th">位置</th>
                                <th class="cyber-th">状态</th>
                                <th class="cyber-th">创建时间</th>
                                <th class="cyber-th">操作</th>
                            </tr>
                        </thead>
                        <tbody class="cyber-tbody" v-if="loading">
                            <tr class="cyber-tr">
                                <td colspan="10" class="cyber-td text-center py-4">
                                    <i class="fas fa-spinner fa-spin me-2"></i>加载中...
                                </td>
                            </tr>
                        </tbody>
                        <tbody class="cyber-tbody" v-else-if="equipmentsList.length === 0">
                            <tr class="cyber-tr">
                                <td colspan="10" class="cyber-td text-center py-4 text-muted">
                                    <i class="fas fa-inbox me-2"></i>暂无数据
                                </td>
                            </tr>
                        </tbody>
                        <tbody class="cyber-tbody" v-else>
                            <tr class="cyber-tr" v-for="equipment in equipmentsList" v-bind:key="equipment.id">
                                <td class="cyber-td">{{ equipment.id }}</td>
                                <td class="cyber-td">
                                    <span class="text-muted">#{{ equipment.userId }}</span><br>
                                    <strong>{{ equipment.userName }}</strong>
                                </td>
                                <td class="cyber-td">
                                    <i class="fas fa-shield-alt me-1" style="color: var(--cyber-blue);"></i>
                                    {{ equipment.name }}
                                </td>
                                <td class="cyber-td">{{ equipment.equipTypeName }}</td>
                                <td class="cyber-td">
                                    <span v-bind:class="getStrengthenBadgeClass(equipment.strengthenLevel)">
                                        +{{ equipment.strengthenLevel }}
                                    </span>
                                </td>
                                <td class="cyber-td">{{ equipment.slot }}</td>
                                <td class="cyber-td">{{ equipment.position }}</td>
                                <td class="cyber-td">
                                    <span v-if="equipment.isEquipped" class="badge badge-success">
                                        <i class="fas fa-check me-1"></i>已装备
                                    </span>
                                    <span v-else class="badge badge-secondary">
                                        <i class="fas fa-times me-1"></i>未装备
                                    </span>
                                </td>
                                <td class="cyber-td">{{ formatDateTime(equipment.createTime) }}</td>
                                <td class="cyber-td">
                                    <div class="d-flex gap-1 flex-wrap">
                                        <button type="button" class="cyber-btn cyber-btn-sm" v-on:click="viewDetail(equipment)" title="查看详情" style="background: linear-gradient(135deg, var(--cyber-blue), var(--cyber-purple));">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button type="button" class="cyber-btn cyber-btn-sm cyber-btn-warning" v-on:click="showEditModal(equipment)" title="编辑">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button type="button" class="cyber-btn cyber-btn-sm" v-on:click="showStrengthenModal(equipment)" title="强化" style="background: linear-gradient(135deg, var(--cyber-orange), var(--cyber-pink));">
                                            <i class="fas fa-hammer"></i>
                                        </button>
                                        <button v-if="!equipment.isEquipped" type="button" class="cyber-btn cyber-btn-sm cyber-btn-success" v-on:click="wearEquipment(equipment)" title="穿戴">
                                            <i class="fas fa-plus-circle"></i>
                                        </button>
                                        <button v-else type="button" class="cyber-btn cyber-btn-sm cyber-btn-outline" v-on:click="unwearEquipment(equipment)" title="卸下">
                                            <i class="fas fa-minus-circle"></i>
                                        </button>
                                        <button type="button" class="cyber-btn cyber-btn-sm cyber-btn-danger" v-on:click="deleteEquipment(equipment)" v-bind:disabled="equipment.isEquipped" title="删除">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- 科幻分页 -->
            <div class="mt-3" v-if="equipmentsList.length > 0">
                <div class="row align-items-center">
                    <div class="col-sm-6">
                        <div class="text-muted">
                            显示第 {{ (currentPage - 1) * pageSize + 1 }} 到 {{ Math.min(currentPage * pageSize, totalCount) }} 条记录，共 {{ totalCount }} 条记录
                        </div>
                    </div>
                    <div class="col-sm-6">
                        <ul class="cyber-pagination justify-content-end">
                            <li class="page-item" v-bind:class="{ disabled: currentPage === 1 }">
                                <a href="#" class="page-link" v-on:click.prevent="changePage(currentPage - 1)">上一页</a>
                            </li>
                            <li v-for="page in visiblePages" v-bind:key="page" class="page-item" v-bind:class="{ active: page === currentPage }">
                                <a href="#" class="page-link" v-on:click.prevent="changePage(page)">{{ page }}</a>
                            </li>
                            <li class="page-item" v-bind:class="{ disabled: currentPage === totalPages }">
                                <a href="#" class="page-link" v-on:click.prevent="changePage(currentPage + 1)">下一页</a>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

    </div>

    <!-- 科幻新增/编辑装备模态框 -->
    <div class="modal fade cyber-modal" id="equipmentModal" tabindex="-1" role="dialog" aria-labelledby="equipmentModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="equipmentModalLabel">
                        <i class="fas fa-shield-alt me-2"></i>{{ isEdit ? '编辑装备' : '新增装备' }}
                    </h5>
                    <button type="button" class="btn-close" v-on:click="closeModal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="cyber-form-group">
                                    <label class="cyber-form-label" for="modalUserId">所属用户 <span class="text-danger">*</span></label>
                                    <div class="cyber-select-wrapper">
                                        <select class="cyber-form-control" id="modalUserId" v-model="equipmentForm.userId" required>
                                            <option value="">请选择用户</option>
                                            <option v-for="option in userOptions" v-bind:key="option.value" v-bind:value="option.value">{{ option.label }}</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="cyber-form-group">
                                    <label class="cyber-form-label" for="modalEquipId">装备配置 <span class="text-danger">*</span></label>
                                    <div class="custom-select-container" v-bind:class="{ open: showEquipmentDropdown }" style="position: relative; width: 100%;">
                                        <input
                                            type="text"
                                            class="cyber-form-control"
                                            v-bind:value="selectedEquipmentDisplay"
                                            v-on:click="toggleEquipmentDropdown"
                                            placeholder="请选择装备配置"
                                            readonly
                                            style="cursor: pointer;"
                                        >
                                        <i class="fas fa-chevron-down" style="position: absolute; right: 12px; top: 50%; transform: translateY(-50%); color: var(--cyber-blue); pointer-events: none;"></i>
                                        <div class="custom-select-dropdown" v-bind:class="{ show: showEquipmentDropdown }" style="position: absolute; top: 100%; left: 0; right: 0; background: var(--dark-card); border: 1px solid rgba(0, 212, 255, 0.3); border-radius: 8px; box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3); max-height: 300px; overflow-y: auto; z-index: 1000; display: none;">
                                            <input
                                                type="text"
                                                class="cyber-form-control"
                                                v-model="equipmentSearchText"
                                                placeholder="搜索装备名称或编号..."
                                                v-on:input="filterEquipments"
                                                style="margin: 8px; width: calc(100% - 16px); border-radius: 6px;"
                                            >
                                            <div
                                                v-for="equipment in filteredEquipments"
                                                v-bind:key="equipment.value"
                                                class="custom-select-option"
                                                v-bind:class="{ selected: equipmentForm.equipId == equipment.value }"
                                                v-on:click="selectEquipment(equipment)"
                                                style="padding: 12px; cursor: pointer; border-bottom: 1px solid rgba(255, 255, 255, 0.1); transition: var(--transition);"
                                            >
                                                <div class="item-name" style="font-weight: 600; color: var(--text-primary);">{{ equipment.label }}</div>
                                                <div class="item-details" style="font-size: 0.875rem; color: var(--text-muted); margin-top: 4px;">
                                                    类型: {{ getEquipmentTypeName(equipment.equipTypeId) }} | 编号: {{ equipment.value }}
                                                </div>
                                            </div>
                                            <div v-if="!filteredEquipments || filteredEquipments.length === 0" class="custom-select-option" style="color: var(--text-muted); text-align: center; padding: 20px;">
                                                未找到匹配的装备
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="cyber-form-group">
                                    <label class="cyber-form-label" for="modalStrengthenLevel">强化等级</label>
                                    <input type="number" class="cyber-form-control" id="modalStrengthenLevel" v-model="equipmentForm.strengthenLevel" min="0" max="20" placeholder="0-20">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="cyber-form-group">
                                    <label class="cyber-form-label" for="modalSlot">槽位</label>
                                    <input type="number" class="cyber-form-control" id="modalSlot" v-model="equipmentForm.slot" min="1" placeholder="槽位编号">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="cyber-form-group">
                                    <label class="cyber-form-label" for="modalPosition">位置</label>
                                    <input type="number" class="cyber-form-control" id="modalPosition" v-model="equipmentForm.position" min="1" placeholder="位置编号">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="cyber-form-group">
                                    <label class="cyber-form-label" for="modalIsEquipped">装备状态</label>
                                    <div class="cyber-select-wrapper">
                                        <select class="cyber-form-control" id="modalIsEquipped" v-model="equipmentForm.isEquipped">
                                            <option v-bind:value="false">未装备</option>
                                            <option v-bind:value="true">已装备</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="cyber-btn cyber-btn-outline" v-on:click="closeModal">
                        <i class="fas fa-times me-1"></i>取消
                    </button>
                    <button type="button" class="cyber-btn cyber-btn-primary" v-on:click="saveEquipment" v-bind:disabled="saving">
                        <i v-if="saving" class="fas fa-spinner fa-spin me-1"></i>
                        <i v-else class="fas fa-save me-1"></i>
                        {{ saving ? '保存中...' : '保存' }}
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 科幻装备详情模态框 -->
    <div class="modal fade cyber-modal" id="equipmentDetailModal" tabindex="-1" role="dialog" aria-labelledby="equipmentDetailModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="equipmentDetailModalLabel">
                        <i class="fas fa-info-circle me-2"></i>装备详情
                    </h5>
                    <button type="button" class="btn-close" v-on:click="closeDetailModal" aria-label="Close"></button>
                </div>
                <div class="modal-body" v-if="equipmentDetail">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 style="color: var(--cyber-blue); margin-bottom: 15px;">基础信息</h6>
                            <div class="cyber-table-container">
                                <table class="cyber-table">
                                    <tr class="cyber-tr">
                                        <td class="cyber-td"><strong>装备ID:</strong></td>
                                        <td class="cyber-td">{{ equipmentDetail.id }}</td>
                                    </tr>
                                    <tr class="cyber-tr">
                                        <td class="cyber-td"><strong>用户:</strong></td>
                                        <td class="cyber-td">{{ equipmentDetail.userName }} (#{{ equipmentDetail.userId }})</td>
                                    </tr>
                                    <tr class="cyber-tr">
                                        <td class="cyber-td"><strong>装备名称:</strong></td>
                                        <td class="cyber-td">{{ equipmentDetail.name }}</td>
                                    </tr>
                                    <tr class="cyber-tr">
                                        <td class="cyber-td"><strong>装备类型:</strong></td>
                                        <td class="cyber-td">{{ equipmentDetail.equipTypeName }}</td>
                                    </tr>
                                    <tr class="cyber-tr">
                                        <td class="cyber-td"><strong>强化等级:</strong></td>
                                        <td class="cyber-td">
                                            <span v-bind:class="getStrengthenBadgeClass(equipmentDetail.strengthenLevel)">
                                                +{{ equipmentDetail.strengthenLevel }}
                                            </span>
                                        </td>
                                    </tr>
                                    <tr class="cyber-tr">
                                        <td class="cyber-td"><strong>槽位:</strong></td>
                                        <td class="cyber-td">{{ equipmentDetail.slot }}</td>
                                    </tr>
                                    <tr class="cyber-tr">
                                        <td class="cyber-td"><strong>位置:</strong></td>
                                        <td class="cyber-td">{{ equipmentDetail.position }}</td>
                                    </tr>
                                    <tr class="cyber-tr">
                                        <td class="cyber-td"><strong>状态:</strong></td>
                                        <td class="cyber-td">
                                            <span v-if="equipmentDetail.isEquipped" class="badge badge-success">已装备</span>
                                            <span v-else class="badge badge-secondary">未装备</span>
                                        </td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                        <div class="col-md-6" v-if="equipmentDetail.detail">
                            <h6 style="color: var(--cyber-green); margin-bottom: 15px;">装备属性</h6>
                            <div class="cyber-table-container">
                                <table class="cyber-table">
                                    <tr class="cyber-tr">
                                        <td class="cyber-td"><strong>攻击力:</strong></td>
                                        <td class="cyber-td" style="color: var(--cyber-red);">{{ equipmentDetail.detail.atk }}</td>
                                    </tr>
                                    <tr class="cyber-tr">
                                        <td class="cyber-td"><strong>命中:</strong></td>
                                        <td class="cyber-td" style="color: var(--cyber-orange);">{{ equipmentDetail.detail.hit }}</td>
                                    </tr>
                                    <tr class="cyber-tr">
                                        <td class="cyber-td"><strong>防御力:</strong></td>
                                        <td class="cyber-td" style="color: var(--cyber-blue);">{{ equipmentDetail.detail.def }}</td>
                                    </tr>
                                    <tr class="cyber-tr">
                                        <td class="cyber-td"><strong>速度:</strong></td>
                                        <td class="cyber-td" style="color: var(--cyber-purple);">{{ equipmentDetail.detail.spd }}</td>
                                    </tr>
                                    <tr class="cyber-tr">
                                        <td class="cyber-td"><strong>闪避:</strong></td>
                                        <td class="cyber-td" style="color: var(--cyber-green);">{{ equipmentDetail.detail.dodge }}</td>
                                    </tr>
                                    <tr class="cyber-tr">
                                        <td class="cyber-td"><strong>生命值:</strong></td>
                                        <td class="cyber-td" style="color: var(--cyber-red);">{{ equipmentDetail.detail.hp }}</td>
                                    </tr>
                                    <tr class="cyber-tr">
                                        <td class="cyber-td"><strong>魔法值:</strong></td>
                                        <td class="cyber-td" style="color: var(--cyber-blue);">{{ equipmentDetail.detail.mp }}</td>
                                    </tr>
                                    <tr class="cyber-tr">
                                        <td class="cyber-td"><strong>深度:</strong></td>
                                        <td class="cyber-td">{{ equipmentDetail.detail.deepen }}</td>
                                    </tr>
                                    <tr class="cyber-tr">
                                        <td class="cyber-td"><strong>偏移:</strong></td>
                                        <td class="cyber-td">{{ equipmentDetail.detail.offset }}</td>
                                    </tr>
                                    <tr class="cyber-tr">
                                        <td class="cyber-td"><strong>吸血:</strong></td>
                                        <td class="cyber-td">{{ equipmentDetail.detail.vamp }}</td>
                                    </tr>
                                    <tr class="cyber-tr">
                                        <td class="cyber-td"><strong>吸魔:</strong></td>
                                        <td class="cyber-td">{{ equipmentDetail.detail.vampMp }}</td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                    <div class="row" v-if="equipmentDetail.detail && equipmentDetail.detail.description">
                        <div class="col-12">
                            <h6 style="color: var(--cyber-purple); margin-bottom: 15px;">装备描述</h6>
                            <p class="text-muted">{{ equipmentDetail.detail.description }}</p>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="cyber-btn cyber-btn-outline" v-on:click="closeDetailModal">
                        <i class="fas fa-times me-1"></i>关闭
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 科幻强化装备模态框 -->
    <div class="modal fade cyber-modal" id="strengthenModal" tabindex="-1" role="dialog" aria-labelledby="strengthenModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="strengthenModalLabel">
                        <i class="fas fa-hammer me-2"></i>强化装备
                    </h5>
                    <button type="button" class="btn-close" v-on:click="closeStrengthenModal" aria-label="Close"></button>
                </div>
                <div class="modal-body" v-if="selectedEquipment">
                    <div style="background: rgba(0, 212, 255, 0.1); border: 1px solid rgba(0, 212, 255, 0.3); border-radius: 8px; padding: 15px; margin-bottom: 20px;">
                        <i class="fas fa-info-circle me-2" style="color: var(--cyber-blue);"></i>
                        当前装备：<strong>{{ selectedEquipment.name }}</strong><br>
                        当前强化等级：<span v-bind:class="getStrengthenBadgeClass(selectedEquipment.strengthenLevel)">+{{ selectedEquipment.strengthenLevel }}</span>
                    </div>
                    <div class="cyber-form-group">
                        <label class="cyber-form-label" for="targetLevel">目标强化等级 <span class="text-danger">*</span></label>
                        <input type="number" class="cyber-form-control" id="targetLevel" v-model="strengthenForm.targetLevel"
                               v-bind:min="selectedEquipment.strengthenLevel + 1" max="20" required>
                        <small class="text-muted" style="font-size: 12px; margin-top: 5px; display: block;">强化等级范围：{{ selectedEquipment.strengthenLevel + 1 }} - 20</small>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="cyber-btn cyber-btn-outline" v-on:click="closeStrengthenModal">
                        <i class="fas fa-times me-1"></i>取消
                    </button>
                    <button type="button" class="cyber-btn cyber-btn-warning" v-on:click="confirmStrengthen" v-bind:disabled="saving">
                        <i v-if="saving" class="fas fa-spinner fa-spin me-1"></i>
                        <i v-else class="fas fa-hammer me-1"></i>
                        {{ saving ? '强化中...' : '确认强化' }}
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
/* 自定义下拉框样式 */
.custom-select-dropdown.show {
    display: block !important;
}

.custom-select-option:hover {
    background: rgba(0, 212, 255, 0.2) !important;
}

.custom-select-option.selected {
    background: linear-gradient(135deg, var(--cyber-blue), var(--cyber-purple)) !important;
    color: white !important;
}

.custom-select-option.selected .item-name,
.custom-select-option.selected .item-details {
    color: white !important;
}
</style>

@section Scripts {
    <script>
        // 初始化Vue应用
        Vue.createApp({
            data() {
                return {
                    // 加载状态
                    loading: false,
                    saving: false,
                    
                    // 装备列表数据
                    equipmentsList: [],
                    totalCount: 0,
                    currentPage: 1,
                    pageSize: 10,
                    
                    // 表单状态
                    isEdit: false,
                    selectedEquipment: null,
                    equipmentDetail: null,
                    
                    // 下拉选项
                    userOptions: [],
                    equipmentTypeOptions: [],
                    equipmentConfigOptions: [],

                    // 装备搜索下拉框相关
                    showEquipmentDropdown: false,
                    equipmentSearchText: '',
                    filteredEquipments: [],
                    allEquipments: [], // 存储所有装备配置，用于过滤
                    
                    // 查询表单
                    queryForm: {
                        userId: null,
                        userName: '',
                        name: '',
                        equipTypeId: '',
                        isEquipped: '',
                        minStrengthenLevel: null,
                        maxStrengthenLevel: null
                    },
                    
                    // 装备表单
                    equipmentForm: {
                        id: 0,
                        userId: null,
                        equipId: null,
                        name: '',
                        icon: '',
                        equipTypeId: null,
                        strengthenLevel: 0,
                        slot: 1,
                        position: 1,
                        isEquipped: false
                    },
                    
                    // 强化表单
                    strengthenForm: {
                        id: 0,
                        targetLevel: 1
                    }
                };
            },
            
            computed: {
                // 总页数
                totalPages() {
                    return Math.ceil(this.totalCount / this.pageSize);
                },
                
                // 可见页码
                visiblePages() {
                    const total = this.totalPages;
                    const current = this.currentPage;
                    const delta = 2;
                    const pages = [];
                    
                    for (let i = Math.max(2, current - delta); i <= Math.min(total - 1, current + delta); i++) {
                        pages.push(i);
                    }
                    
                    if (current - delta > 2) {
                        pages.unshift("...");
                    }
                    if (current + delta < total - 1) {
                        pages.push("...");
                    }
                    
                    pages.unshift(1);
                    if (total > 1) {
                        pages.push(total);
                    }
                    
                    return pages.filter((page, index, arr) => arr.indexOf(page) === index);
                },

                // 装备选择下拉框的显示文本
                selectedEquipmentDisplay() {
                    if (!this.equipmentForm.equipId) {
                        return '请选择装备配置';
                    }
                    const config = this.equipmentConfigOptions.find(c => c.value == this.equipmentForm.equipId);
                    if (!config) {
                        return this.equipmentForm.equipId; // 如果没有找到，显示编号
                    }
                    return config.label;
                }
            },
            
            methods: {
                // 加载装备列表
                async loadEquipments() {
                    try {
                        this.loading = true;
                        
                        // 准备请求数据，确保字段名与后端DTO匹配
                        const requestData = {
                            UserId: this.queryForm.userId,
                            UserName: this.queryForm.userName || '',
                            Name: this.queryForm.name || '',
                            EquipTypeId: this.queryForm.equipTypeId || null,  // 保持字符串类型，不转换为整数
                            IsEquipped: this.queryForm.isEquipped === '' ? null : (this.queryForm.isEquipped === 'true'),
                            MinStrengthenLevel: this.queryForm.minStrengthenLevel,
                            MaxStrengthenLevel: this.queryForm.maxStrengthenLevel,
                            Page: this.currentPage,
                            PageSize: this.pageSize
                        };

                        // 添加调试日志
                        console.log('发送请求数据:', requestData);
                        
                        const response = await axios.post('/UserEquipment/GetList', requestData);
                        if (response.data.code === 200) {
                            this.equipmentsList = response.data.data || [];
                            this.totalCount = response.data.total || 0;
                        } else {
                            console.error('获取装备列表失败：', response.data.message);
                        }
                    } catch (error) {
                        console.error('获取装备列表失败：', error);
                    } finally {
                        this.loading = false;
                    }
                },
                
                // 搜索装备
                searchEquipments() {
                    console.log('搜索参数:', this.queryForm);
                    console.log('装备类型ID:', this.queryForm.equipTypeId, '类型:', typeof this.queryForm.equipTypeId);
                    this.currentPage = 1;
                    this.loadEquipments();
                },
                
                // 重置搜索
                resetSearch() {
                    Object.assign(this.queryForm, {
                        userId: null,
                        userName: '',
                        name: '',
                        equipTypeId: '',
                        isEquipped: '',
                        minStrengthenLevel: null,
                        maxStrengthenLevel: null
                    });
                    this.searchEquipments();
                },
                
                // 分页
                changePage(page) {
                    if (page >= 1 && page <= this.totalPages) {
                        this.currentPage = page;
                        this.loadEquipments();
                    }
                },
                
                // 显示新增模态框
                showAddModal() {
                    this.isEdit = false;
                    this.equipmentForm = {
                        id: 0,
                        userId: null,
                        equipId: null,
                        name: '',
                        icon: '',
                        equipTypeId: null,
                        strengthenLevel: 0,
                        slot: 1,
                        position: 1,
                        isEquipped: false
                    };

                    // 重置装备搜索相关状态
                    this.showEquipmentDropdown = false;
                    this.equipmentSearchText = '';
                    this.filteredEquipments = this.allEquipments;

                    $('#equipmentModal').modal('show');
                },
                
                // 显示编辑模态框
                showEditModal(equipment) {
                    this.isEdit = true;
                    this.equipmentForm = {
                        id: equipment.id,
                        userId: equipment.userId,
                        equipId: equipment.equipId,
                        name: equipment.name,
                        icon: equipment.icon,
                        equipTypeId: equipment.equipTypeId,
                        strengthenLevel: equipment.strengthenLevel,
                        slot: equipment.slot,
                        position: equipment.position,
                        isEquipped: equipment.isEquipped
                    };

                    // 重置装备搜索相关状态
                    this.showEquipmentDropdown = false;
                    this.equipmentSearchText = '';
                    this.filteredEquipments = this.allEquipments;

                    $('#equipmentModal').modal('show');
                },
                
                // 保存装备
                async saveEquipment() {
                    try {
                        this.saving = true;
                        
                        const url = this.isEdit ? '/UserEquipment/Update' : '/UserEquipment/Create';
                        const response = await axios.post(url, this.equipmentForm);
                        
                        if (response.data.code === 200) {
                            $('#equipmentModal').modal('hide');
                            this.loadEquipments();
                            alert(response.data.message || (this.isEdit ? '更新成功' : '创建成功'));
                        } else {
                            alert(response.data.message || '操作失败');
                        }
                    } catch (error) {
                        console.error('保存装备失败：', error);
                        alert('保存失败，请重试');
                    } finally {
                        this.saving = false;
                    }
                },
                
                // 删除装备
                async deleteEquipment(equipment) {
                    if (equipment.isEquipped) {
                        alert('已装备的装备无法删除，请先卸下装备');
                        return;
                    }
                    
                    if (!confirm(`确定要删除装备"${equipment.name}"吗？此操作无法撤销！`)) {
                        return;
                    }
                    
                    try {
                        const response = await axios.post('/UserEquipment/Delete', { id: equipment.id });
                        if (response.data.code === 200) {
                            this.loadEquipments();
                            alert('删除成功');
                        } else {
                            alert(response.data.message || '删除失败');
                        }
                    } catch (error) {
                        console.error('删除装备失败：', error);
                        alert('删除失败，请重试');
                    }
                },
                
                // 查看装备详情
                async viewDetail(equipment) {
                    try {
                        const response = await axios.get(`/UserEquipment/GetById/${equipment.id}`);
                        if (response.data.code === 200) {
                            this.equipmentDetail = response.data.data;
                            $('#equipmentDetailModal').modal('show');
                        } else {
                            alert(response.data.message || '获取装备详情失败');
                        }
                    } catch (error) {
                        console.error('获取装备详情失败：', error);
                        alert('获取装备详情失败，请重试');
                    }
                },
                
                // 显示强化模态框
                showStrengthenModal(equipment) {
                    this.selectedEquipment = equipment;
                    this.strengthenForm = {
                        id: equipment.id,
                        targetLevel: equipment.strengthenLevel + 1
                    };
                    $('#strengthenModal').modal('show');
                },
                
                // 确认强化
                async confirmStrengthen() {
                    if (!confirm(`确定要将装备强化到 +${this.strengthenForm.targetLevel} 级吗？`)) {
                        return;
                    }
                    
                    try {
                        this.saving = true;
                        const response = await axios.post('/UserEquipment/Strengthen', this.strengthenForm);
                        
                        if (response.data.code === 200) {
                            $('#strengthenModal').modal('hide');
                            this.loadEquipments();
                            alert(response.data.message || '强化成功');
                        } else {
                            alert(response.data.message || '强化失败');
                        }
                    } catch (error) {
                        console.error('强化装备失败：', error);
                        alert('强化失败，请重试');
                    } finally {
                        this.saving = false;
                    }
                },
                
                // 穿戴装备
                async wearEquipment(equipment) {
                    if (!confirm(`确定要穿戴装备"${equipment.name}"吗？`)) {
                        return;
                    }
                    
                    try {
                        const response = await axios.post('/UserEquipment/Wear', {
                            id: equipment.id,
                            userId: equipment.userId,
                            position: equipment.position
                        });
                        
                        if (response.data.code === 200) {
                            this.loadEquipments();
                            alert('装备穿戴成功');
                        } else {
                            alert(response.data.message || '穿戴失败');
                        }
                    } catch (error) {
                        console.error('穿戴装备失败：', error);
                        alert('穿戴失败，请重试');
                    }
                },
                
                // 卸下装备
                async unwearEquipment(equipment) {
                    if (!confirm(`确定要卸下装备"${equipment.name}"吗？`)) {
                        return;
                    }
                    
                    try {
                        const response = await axios.post('/UserEquipment/Unwear', null, {
                            params: { equipmentId: equipment.id }
                        });
                        
                        if (response.data.code === 200) {
                            this.loadEquipments();
                            alert('装备卸下成功');
                        } else {
                            alert(response.data.message || '卸下失败');
                        }
                    } catch (error) {
                        console.error('卸下装备失败：', error);
                        alert('卸下失败，请重试');
                    }
                },
                
                // 获取强化等级徽章样式
                getStrengthenBadgeClass(level) {
                    if (level >= 15) return 'badge-danger';
                    if (level >= 10) return 'badge-warning';
                    if (level >= 5) return 'badge-info';
                    return 'badge-secondary';
                },
                
                // 装备配置选择变化时
                onEquipmentConfigChange() {
                    if (!this.equipmentForm.equipId) return;
                    
                    // 从装备配置选项中找到选中的配置
                    const selectedConfig = this.equipmentConfigOptions.find(option => option.value === this.equipmentForm.equipId);
                    if (selectedConfig) {
                        // 自动填充装备信息
                        this.equipmentForm.name = selectedConfig.name;
                        this.equipmentForm.icon = selectedConfig.icon;
                        this.equipmentForm.equipTypeId = selectedConfig.equipTypeId;
                    }
                },
                
                // 格式化日期时间
                formatDateTime(dateStr) {
                    if (!dateStr) return '';
                    const date = new Date(dateStr);
                    return date.toLocaleString('zh-CN');
                },
                
                // 加载下拉选项
                async loadOptions() {
                    try {
                        // 加载用户选项
                        const userResponse = await axios.get('/UserEquipment/GetUserOptions');
                        if (userResponse.data.code === 200) {
                            this.userOptions = userResponse.data.data;
                        }

                        // 加载装备类型选项
                        const typeResponse = await axios.get('/UserEquipment/GetEquipmentTypeOptions');
                        if (typeResponse.data.code === 200) {
                            this.equipmentTypeOptions = typeResponse.data.data;
                        }

                        // 加载装备配置选项
                        const configResponse = await axios.get('/UserEquipment/GetEquipmentConfigOptions');
                        if (configResponse.data.code === 200) {
                            this.equipmentConfigOptions = configResponse.data.data;
                            this.allEquipments = configResponse.data.data; // 同时存储到allEquipments用于搜索
                            this.filteredEquipments = configResponse.data.data; // 初始化过滤列表
                        }
                    } catch (error) {
                        console.error('加载选项失败：', error);
                    }
                },
                
                // 关闭装备模态框
                closeModal() {
                    // 重置装备搜索相关状态
                    this.showEquipmentDropdown = false;
                    this.equipmentSearchText = '';

                    $('#equipmentModal').modal('hide');
                },
                
                // 关闭装备详情模态框
                closeDetailModal() {
                    $('#equipmentDetailModal').modal('hide');
                },
                
                // 关闭强化模态框
                closeStrengthenModal() {
                    $('#strengthenModal').modal('hide');
                },

                // 装备搜索下拉框相关方法
                toggleEquipmentDropdown() {
                    this.showEquipmentDropdown = !this.showEquipmentDropdown;
                    if (this.showEquipmentDropdown) {
                        this.filteredEquipments = this.allEquipments; // 显示所有装备
                    }
                },

                filterEquipments() {
                    const searchText = this.equipmentSearchText.toLowerCase();
                    this.filteredEquipments = this.allEquipments.filter(equipment =>
                        equipment.label.toLowerCase().includes(searchText) ||
                        equipment.value.toString().includes(searchText) ||
                        (equipment.name && equipment.name.toLowerCase().includes(searchText))
                    );
                },

                selectEquipment(equipment) {
                    this.equipmentForm.equipId = equipment.value;
                    this.showEquipmentDropdown = false;
                    this.equipmentSearchText = '';
                    // 触发装备配置变化事件
                    this.onEquipmentConfigChange();
                },

                // 获取装备类型名称
                getEquipmentTypeName(equipTypeId) {
                    const type = this.equipmentTypeOptions.find(t => t.value === equipTypeId);
                    return type ? type.label : '未知类型';
                },

                // 处理点击外部关闭下拉框
                handleOutsideClick(event) {
                    const container = event.target.closest('.custom-select-container');
                    if (!container) {
                        this.showEquipmentDropdown = false;
                    }
                }
            },
            
            mounted() {
                // 初始化加载
                this.loadOptions();
                this.loadEquipments();

                // 添加点击外部关闭下拉框的事件监听器
                document.addEventListener('click', this.handleOutsideClick);
            }
        }).mount('#userEquipmentApp');
    </script>
}