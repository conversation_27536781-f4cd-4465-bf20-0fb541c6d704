using System.Security.Cryptography;
using System.Text;

namespace BMS.Common.Helpers
{
    /// <summary>
    /// 密码加密帮助类
    /// </summary>
    public static class PasswordHelper
    {
        /// <summary>
        /// 生成盐值
        /// </summary>
        /// <returns>盐值</returns>
        public static string GenerateSalt()
        {
            var bytes = new byte[16];
            using (var rng = RandomNumberGenerator.Create())
            {
                rng.GetBytes(bytes);
            }
            return Convert.ToBase64String(bytes);
        }

        /// <summary>
        /// 哈希密码
        /// </summary>
        /// <param name="password">原始密码</param>
        /// <param name="salt">盐值</param>
        /// <returns>哈希后的密码</returns>
        public static string HashPassword(string password, string salt)
        {
            using (var sha256 = SHA256.Create())
            {
                var saltedPassword = password + salt;
                var bytes = Encoding.UTF8.GetBytes(saltedPassword);
                var hash = sha256.ComputeHash(bytes);
                return Convert.ToBase64String(hash);
            }
        }

        /// <summary>
        /// 验证密码
        /// </summary>
        /// <param name="password">原始密码</param>
        /// <param name="salt">盐值</param>
        /// <param name="hashedPassword">哈希后的密码</param>
        /// <returns>是否匹配</returns>
        public static bool VerifyPassword(string password, string salt, string hashedPassword)
        {
            var hash = HashPassword(password, salt);
            return hash == hashedPassword;
        }

        /// <summary>
        /// 简单MD5加密（兼容旧系统）
        /// </summary>
        /// <param name="password">原始密码</param>
        /// <returns>MD5加密后的密码</returns>
        public static string Md5Hash(string password)
        {
            using (var md5 = MD5.Create())
            {
                var bytes = Encoding.UTF8.GetBytes(password);
                var hash = md5.ComputeHash(bytes);
                return Convert.ToHexString(hash).ToLower();
            }
        }
    }
} 