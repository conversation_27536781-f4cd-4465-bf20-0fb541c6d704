<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <!-- SqlSugar ORM -->
    <PackageReference Include="SqlSugar" Version="5.1.4.195" />
    <!-- MySQL数据库驱动 -->
    <PackageReference Include="MySql.Data" Version="9.0.0" />
    <!-- 身份验证相关 -->
    <PackageReference Include="Microsoft.AspNetCore.Authentication.Cookies" Version="2.2.0" />
    <!-- JSON处理 -->
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <!-- Serilog 日志框架 -->
    <PackageReference Include="Serilog.AspNetCore" Version="8.0.0" />
    <PackageReference Include="Serilog.Sinks.Console" Version="5.0.1" />
    <PackageReference Include="Serilog.Sinks.File" Version="5.0.0" />
    <PackageReference Include="Serilog.Enrichers.Environment" Version="2.3.0" />
    <PackageReference Include="Serilog.Enrichers.Thread" Version="3.1.0" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="logs\" />
    <Folder Include="Models\ViewModels\" />
  </ItemGroup>

</Project>
