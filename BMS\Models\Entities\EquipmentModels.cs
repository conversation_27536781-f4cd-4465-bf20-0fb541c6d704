using SqlSugar;
using System.ComponentModel.DataAnnotations;

namespace BMS.Models.Entities
{
    // gemstone_config 类已移动到独立文件 gemstone_config.cs

    // equipment_gemstone 类已移动到独立文件 equipment_gemstone.cs

    // suit_config 类已移动到独立文件 suit_config.cs

    // suit_attribute 类已移动到独立文件 suit_attribute.cs

    /// <summary>
    /// 装备操作记录表
    /// </summary>
    [SugarTable("equipment_operation_log")]
    public class equipment_operation_log
    {
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public int id { get; set; }

        [SugarColumn(IsNullable = false)]
        public int user_id { get; set; }

        [SugarColumn(IsNullable = false)]
        public int user_equipment_id { get; set; }

        [SugarColumn(Length = 20, IsNullable = false)]
        public string operation_type { get; set; }

        [SugarColumn(ColumnDataType = "text", IsNullable = true)]
        public string? operation_data { get; set; }

        [SugarColumn(IsNullable = true)]
        public long? cost_money { get; set; } = 0;

        [SugarColumn(ColumnDataType = "text", IsNullable = true)]
        public string? cost_items { get; set; }

        [SugarColumn(Length = 20, IsNullable = false)]
        public string result { get; set; }

        [SugarColumn(ColumnDataType = "text", IsNullable = true)]
        public string? result_message { get; set; }

        [SugarColumn(IsNullable = true)]
        public DateTime? create_time { get; set; } = DateTime.Now;
    }
}
