-- 宠物合成公式测试数据
-- 注意：执行前请确保pet_config表中已有相应的宠物数据

-- 插入一些测试的宠物合成公式
INSERT INTO pet_synthesis_formula (
    main_pet_no, 
    sub_pet_no, 
    main_growth_min, 
    sub_growth_min, 
    result_pet_no, 
    base_success_rate, 
    required_level, 
    cost_gold, 
    formula_type, 
    is_active, 
    create_time
) VALUES 
-- 固定公式：金波姆 + 波光姆 = 金波姆王
(1, 2, 0.500000, 0.400000, 3, 75.00, 50, 100000, 'FIXED', 1, NOW()),

-- 固定公式：金波姆王 + 黄金鸟 = 金光鼠
(3, 4, 0.600000, 0.550000, 5, 60.00, 60, 200000, 'FIXED', 1, NOW()),

-- 随机神宠公式：任意两个高级宠物合成随机神宠
(4, 5, 0.700000, 0.700000, 6, 30.00, 70, 500000, 'RANDOM_GOD', 1, NOW()),

-- 随机神圣宠物公式：神宠 + 神宠 = 随机神圣宠物
(6, 6, 0.800000, 0.800000, 7, 20.00, 80, 1000000, 'RANDOM_HOLY', 1, NOW()),

-- 固定公式：波光姆 + 金波姆 = 黄金鸟（反向合成）
(2, 1, 0.450000, 0.500000, 4, 70.00, 45, 80000, 'FIXED', 1, NOW()),

-- 固定公式：低级宠物合成中级宠物
(1, 1, 0.300000, 0.300000, 2, 85.00, 30, 50000, 'FIXED', 1, NOW()),

-- 随机神宠公式：中级宠物合成
(2, 3, 0.550000, 0.600000, 6, 40.00, 65, 300000, 'RANDOM_GOD', 1, NOW()),

-- 固定公式：特殊合成路线
(4, 2, 0.500000, 0.450000, 5, 65.00, 55, 150000, 'FIXED', 0, NOW()), -- 禁用状态

-- 随机神圣宠物公式：超高级合成
(5, 6, 0.750000, 0.800000, 7, 15.00, 85, 1500000, 'RANDOM_HOLY', 1, NOW()),

-- 固定公式：经济型合成
(1, 2, 0.200000, 0.200000, 3, 90.00, 25, 30000, 'FIXED', 1, NOW());

-- 查询插入的数据
SELECT 
    psf.id,
    psf.main_pet_no,
    mpc.name as main_pet_name,
    psf.sub_pet_no,
    spc.name as sub_pet_name,
    psf.main_growth_min,
    psf.sub_growth_min,
    psf.result_pet_no,
    rpc.name as result_pet_name,
    psf.base_success_rate,
    psf.required_level,
    psf.cost_gold,
    psf.formula_type,
    CASE 
        WHEN psf.formula_type = 'FIXED' THEN '固定公式'
        WHEN psf.formula_type = 'RANDOM_GOD' THEN '随机神宠'
        WHEN psf.formula_type = 'RANDOM_HOLY' THEN '随机神圣宠物'
        ELSE psf.formula_type
    END as formula_type_text,
    psf.is_active,
    psf.create_time
FROM pet_synthesis_formula psf
LEFT JOIN pet_config mpc ON psf.main_pet_no = mpc.pet_no
LEFT JOIN pet_config spc ON psf.sub_pet_no = spc.pet_no
LEFT JOIN pet_config rpc ON psf.result_pet_no = rpc.pet_no
ORDER BY psf.id;

-- 统计信息
SELECT 
    '总公式数' as stat_name,
    COUNT(*) as stat_value
FROM pet_synthesis_formula
UNION ALL
SELECT 
    '激活公式数' as stat_name,
    COUNT(*) as stat_value
FROM pet_synthesis_formula 
WHERE is_active = 1
UNION ALL
SELECT 
    '固定公式数' as stat_name,
    COUNT(*) as stat_value
FROM pet_synthesis_formula 
WHERE formula_type = 'FIXED'
UNION ALL
SELECT 
    '随机神宠公式数' as stat_name,
    COUNT(*) as stat_value
FROM pet_synthesis_formula 
WHERE formula_type = 'RANDOM_GOD'
UNION ALL
SELECT 
    '随机神圣宠物公式数' as stat_name,
    COUNT(*) as stat_value
FROM pet_synthesis_formula 
WHERE formula_type = 'RANDOM_HOLY';

-- 按公式类型分组统计
SELECT 
    formula_type,
    CASE 
        WHEN formula_type = 'FIXED' THEN '固定公式'
        WHEN formula_type = 'RANDOM_GOD' THEN '随机神宠'
        WHEN formula_type = 'RANDOM_HOLY' THEN '随机神圣宠物'
        ELSE formula_type
    END as formula_type_text,
    COUNT(*) as formula_count,
    AVG(base_success_rate) as avg_success_rate,
    AVG(required_level) as avg_required_level,
    AVG(cost_gold) as avg_cost_gold
FROM pet_synthesis_formula
GROUP BY formula_type
ORDER BY formula_count DESC;
