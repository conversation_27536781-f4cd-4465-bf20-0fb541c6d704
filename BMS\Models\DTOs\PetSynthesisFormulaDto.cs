using System.ComponentModel.DataAnnotations;

namespace BMS.Models.DTOs
{
    /// <summary>
    /// 宠物合成公式查询DTO
    /// </summary>
    public class PetSynthesisFormulaQueryDto : PagedQueryDto
    {
        /// <summary>
        /// 主宠物编号
        /// </summary>
        public int? MainPetNo { get; set; }

        /// <summary>
        /// 主宠物名称
        /// </summary>
        public string? MainPetName { get; set; }

        /// <summary>
        /// 副宠物编号
        /// </summary>
        public int? SubPetNo { get; set; }

        /// <summary>
        /// 副宠物名称
        /// </summary>
        public string? SubPetName { get; set; }

        /// <summary>
        /// 结果宠物编号
        /// </summary>
        public int? ResultPetNo { get; set; }

        /// <summary>
        /// 结果宠物名称
        /// </summary>
        public string? ResultPetName { get; set; }

        /// <summary>
        /// 公式类型
        /// </summary>
        public string? FormulaType { get; set; }

        /// <summary>
        /// 是否激活
        /// </summary>
        public bool? IsActive { get; set; }

        /// <summary>
        /// 最小等级要求
        /// </summary>
        public int? MinRequiredLevel { get; set; }

        /// <summary>
        /// 最大等级要求
        /// </summary>
        public int? MaxRequiredLevel { get; set; }
    }

    /// <summary>
    /// 宠物合成公式详情DTO
    /// </summary>
    public class PetSynthesisFormulaDetailDto
    {
        /// <summary>
        /// ID
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 主宠物编号
        /// </summary>
        public int MainPetNo { get; set; }

        /// <summary>
        /// 主宠物名称
        /// </summary>
        public string MainPetName { get; set; } = string.Empty;

        /// <summary>
        /// 主宠物属性
        /// </summary>
        public string MainPetAttribute { get; set; } = string.Empty;

        /// <summary>
        /// 副宠物编号
        /// </summary>
        public int SubPetNo { get; set; }

        /// <summary>
        /// 副宠物名称
        /// </summary>
        public string SubPetName { get; set; } = string.Empty;

        /// <summary>
        /// 副宠物属性
        /// </summary>
        public string SubPetAttribute { get; set; } = string.Empty;

        /// <summary>
        /// 主宠最小成长要求
        /// </summary>
        public decimal MainGrowthMin { get; set; }

        /// <summary>
        /// 副宠最小成长要求
        /// </summary>
        public decimal SubGrowthMin { get; set; }

        /// <summary>
        /// 合成结果宠物编号
        /// </summary>
        public int ResultPetNo { get; set; }

        /// <summary>
        /// 合成结果宠物名称
        /// </summary>
        public string ResultPetName { get; set; } = string.Empty;

        /// <summary>
        /// 合成结果宠物属性
        /// </summary>
        public string ResultPetAttribute { get; set; } = string.Empty;

        /// <summary>
        /// 基础成功率(%)
        /// </summary>
        public decimal BaseSuccessRate { get; set; }

        /// <summary>
        /// 所需等级
        /// </summary>
        public int RequiredLevel { get; set; }

        /// <summary>
        /// 消耗金币
        /// </summary>
        public long CostGold { get; set; }

        /// <summary>
        /// 公式类型
        /// </summary>
        public string FormulaType { get; set; } = string.Empty;

        /// <summary>
        /// 公式类型显示文本
        /// </summary>
        public string FormulaTypeText => FormulaType switch
        {
            "FIXED" => "固定公式",
            "RANDOM_GOD" => "随机神宠",
            "RANDOM_HOLY" => "随机神圣宠物",
            _ => FormulaType
        };

        /// <summary>
        /// 是否激活
        /// </summary>
        public bool IsActive { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateTime { get; set; }
    }

    /// <summary>
    /// 宠物合成公式创建DTO
    /// </summary>
    public class PetSynthesisFormulaCreateDto
    {
        /// <summary>
        /// 主宠物编号
        /// </summary>
        [Required(ErrorMessage = "主宠物编号不能为空")]
        public int MainPetNo { get; set; }

        /// <summary>
        /// 副宠物编号
        /// </summary>
        [Required(ErrorMessage = "副宠物编号不能为空")]
        public int SubPetNo { get; set; }

        /// <summary>
        /// 主宠最小成长要求
        /// </summary>
        [Range(0, 999.999999, ErrorMessage = "主宠最小成长要求必须在0-999.999999之间")]
        public decimal MainGrowthMin { get; set; } = 0.000000m;

        /// <summary>
        /// 副宠最小成长要求
        /// </summary>
        [Range(0, 999.999999, ErrorMessage = "副宠最小成长要求必须在0-999.999999之间")]
        public decimal SubGrowthMin { get; set; } = 0.000000m;

        /// <summary>
        /// 合成结果宠物编号
        /// </summary>
        [Required(ErrorMessage = "合成结果宠物编号不能为空")]
        public int ResultPetNo { get; set; }

        /// <summary>
        /// 基础成功率(%)
        /// </summary>
        [Range(0, 100, ErrorMessage = "基础成功率必须在0-100之间")]
        public decimal BaseSuccessRate { get; set; } = 50.00m;

        /// <summary>
        /// 所需等级
        /// </summary>
        [Range(1, 999, ErrorMessage = "所需等级必须在1-999之间")]
        public int RequiredLevel { get; set; } = 40;

        /// <summary>
        /// 消耗金币
        /// </summary>
        [Range(0, long.MaxValue, ErrorMessage = "消耗金币不能为负数")]
        public long CostGold { get; set; } = 50000;

        /// <summary>
        /// 公式类型
        /// </summary>
        [Required(ErrorMessage = "公式类型不能为空")]
        [RegularExpression("^(FIXED|RANDOM_GOD|RANDOM_HOLY)$", ErrorMessage = "公式类型只能是FIXED、RANDOM_GOD或RANDOM_HOLY")]
        public string FormulaType { get; set; } = "FIXED";

        /// <summary>
        /// 是否激活
        /// </summary>
        public bool IsActive { get; set; } = true;
    }

    /// <summary>
    /// 宠物合成公式更新DTO
    /// </summary>
    public class PetSynthesisFormulaUpdateDto
    {
        /// <summary>
        /// ID
        /// </summary>
        [Required(ErrorMessage = "ID不能为空")]
        public int Id { get; set; }

        /// <summary>
        /// 主宠物编号
        /// </summary>
        [Required(ErrorMessage = "主宠物编号不能为空")]
        public int MainPetNo { get; set; }

        /// <summary>
        /// 副宠物编号
        /// </summary>
        [Required(ErrorMessage = "副宠物编号不能为空")]
        public int SubPetNo { get; set; }

        /// <summary>
        /// 主宠最小成长要求
        /// </summary>
        [Range(0, 999.999999, ErrorMessage = "主宠最小成长要求必须在0-999.999999之间")]
        public decimal MainGrowthMin { get; set; }

        /// <summary>
        /// 副宠最小成长要求
        /// </summary>
        [Range(0, 999.999999, ErrorMessage = "副宠最小成长要求必须在0-999.999999之间")]
        public decimal SubGrowthMin { get; set; }

        /// <summary>
        /// 合成结果宠物编号
        /// </summary>
        [Required(ErrorMessage = "合成结果宠物编号不能为空")]
        public int ResultPetNo { get; set; }

        /// <summary>
        /// 基础成功率(%)
        /// </summary>
        [Range(0, 100, ErrorMessage = "基础成功率必须在0-100之间")]
        public decimal BaseSuccessRate { get; set; }

        /// <summary>
        /// 所需等级
        /// </summary>
        [Range(1, 999, ErrorMessage = "所需等级必须在1-999之间")]
        public int RequiredLevel { get; set; }

        /// <summary>
        /// 消耗金币
        /// </summary>
        [Range(0, long.MaxValue, ErrorMessage = "消耗金币不能为负数")]
        public long CostGold { get; set; }

        /// <summary>
        /// 公式类型
        /// </summary>
        [Required(ErrorMessage = "公式类型不能为空")]
        [RegularExpression("^(FIXED|RANDOM_GOD|RANDOM_HOLY)$", ErrorMessage = "公式类型只能是FIXED、RANDOM_GOD或RANDOM_HOLY")]
        public string FormulaType { get; set; } = string.Empty;

        /// <summary>
        /// 是否激活
        /// </summary>
        public bool IsActive { get; set; }
    }

    /// <summary>
    /// 删除宠物合成公式DTO
    /// </summary>
    public class PetSynthesisFormulaDeleteDto
    {
        /// <summary>
        /// ID
        /// </summary>
        [Required(ErrorMessage = "ID不能为空")]
        public int Id { get; set; }
    }

    /// <summary>
    /// 切换激活状态DTO
    /// </summary>
    public class PetSynthesisFormulaToggleDto
    {
        /// <summary>
        /// ID
        /// </summary>
        [Required(ErrorMessage = "ID不能为空")]
        public int Id { get; set; }

        /// <summary>
        /// 是否激活
        /// </summary>
        public bool IsActive { get; set; }
    }
}
