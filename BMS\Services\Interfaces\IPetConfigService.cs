using BMS.Models.DTOs;
using BMS.Models.Common;

namespace BMS.Services.Interfaces
{
    /// <summary>
    /// 宠物配置服务接口
    /// </summary>
    public interface IPetConfigService
    {
        /// <summary>
        /// 分页获取宠物配置列表
        /// </summary>
        /// <param name="queryDto">查询条件</param>
        /// <returns>分页结果</returns>
        Task<PagedResult<PetConfigDto>> GetPagedListAsync(PetConfigQueryDto queryDto);

        /// <summary>
        /// 根据ID获取宠物配置
        /// </summary>
        /// <param name="id">宠物配置ID</param>
        /// <returns>宠物配置信息</returns>
        Task<PetConfigDto?> GetByIdAsync(int id);

        /// <summary>
        /// 根据宠物编号获取宠物配置
        /// </summary>
        /// <param name="petNo">宠物编号</param>
        /// <returns>宠物配置信息</returns>
        Task<PetConfigDto?> GetByPetNoAsync(int petNo);

        /// <summary>
        /// 获取所有宠物配置列表（不分页）
        /// </summary>
        /// <returns>宠物配置列表</returns>
        Task<List<PetConfigDto>> GetAllAsync();

        /// <summary>
        /// 创建宠物配置
        /// </summary>
        /// <param name="createDto">创建DTO</param>
        /// <returns>操作结果</returns>
        Task<ApiResult<bool>> CreateAsync(PetConfigCreateDto createDto);

        /// <summary>
        /// 更新宠物配置
        /// </summary>
        /// <param name="updateDto">更新DTO</param>
        /// <returns>操作结果</returns>
        Task<ApiResult<bool>> UpdateAsync(PetConfigUpdateDto updateDto);

        /// <summary>
        /// 删除宠物配置
        /// </summary>
        /// <param name="id">宠物配置ID</param>
        /// <returns>操作结果</returns>
        Task<ApiResult<bool>> DeleteAsync(int id);

        /// <summary>
        /// 检查宠物编号是否存在
        /// </summary>
        /// <param name="petNo">宠物编号</param>
        /// <param name="id">排除的ID（编辑时使用）</param>
        /// <returns>是否存在</returns>
        Task<bool> CheckPetNoExistsAsync(int petNo, int? id = null);
    }
} 