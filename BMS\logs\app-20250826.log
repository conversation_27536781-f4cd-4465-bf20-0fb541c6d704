[2025-08-26 20:54:50.944 +08:00 INF] BMS: 🚀 应用程序启动，日志系统已配置
[2025-08-26 20:54:50.985 +08:00 DBG] BMS: 🔧 调试级别日志测试
[2025-08-26 20:54:51.229 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-08-26 20:54:51.233 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-08-26 20:54:51.382 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-26 20:54:52.045 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-26 20:54:52.057 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-08-26 20:54:52.059 +08:00 INF] BMS: ✅ DbContext 初始化成功，日志文件位置: logs/
[2025-08-26 20:54:54.425 +08:00 WRN] Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware: Failed to determine the https port for redirect.
[2025-08-26 20:55:32.681 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-08-26 20:55:32.688 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-08-26 20:55:32.733 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-26 20:55:32.800 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-26 20:55:32.827 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-08-26 20:55:45.484 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-08-26 20:55:46.101 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-08-26 20:55:46.694 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-26 20:55:46.881 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-26 20:55:46.891 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-08-26 20:55:47.224 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`username`,`password`,`real_name`,`phone`,`email`,`status`,`create_time`,`update_time`,`last_login_time` FROM `admin_bm`   WHERE ( `username` = @username0 )   LIMIT 0,1 | 参数: [@username0=hm]
[2025-08-26 20:55:47.373 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`username`,`password`,`real_name`,`phone`,`email`,`status`,`create_time`,`update_time`,`last_login_time` FROM `admin_bm`   WHERE ( `username` = @username0 )   LIMIT 0,1
[2025-08-26 20:55:47.930 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: UPDATE `admin_bm`  SET
            `update_time` = @constant1 , `last_login_time` = @constant0   WHERE ( `id` = @id2 ) | 参数: [@constant0=2025/8/26 20:55:47, @constant1=2025/8/26 20:55:47, @id2=1, @id=0]
[2025-08-26 20:55:48.358 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: UPDATE `admin_bm`  SET
            `update_time` = @constant1 , `last_login_time` = @constant0   WHERE ( `id` = @id2 )
[2025-08-26 22:03:52.043 +08:00 INF] BMS: 🚀 应用程序启动，日志系统已配置
[2025-08-26 22:03:52.066 +08:00 DBG] BMS: 🔧 调试级别日志测试
[2025-08-26 22:03:52.070 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-08-26 22:03:52.072 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-08-26 22:03:52.117 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-26 22:03:52.495 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-26 22:03:52.503 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-08-26 22:03:52.504 +08:00 INF] BMS: ✅ DbContext 初始化成功，日志文件位置: logs/
[2025-08-26 22:06:00.725 +08:00 INF] BMS: 🚀 应用程序启动，日志系统已配置
[2025-08-26 22:06:00.749 +08:00 DBG] BMS: 🔧 调试级别日志测试
[2025-08-26 22:06:00.753 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-08-26 22:06:00.755 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-08-26 22:06:00.799 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-26 22:06:01.233 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-26 22:06:01.242 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-08-26 22:06:01.243 +08:00 INF] BMS: ✅ DbContext 初始化成功，日志文件位置: logs/
[2025-08-26 22:12:10.592 +08:00 INF] BMS: 🚀 应用程序启动，日志系统已配置
[2025-08-26 22:12:10.621 +08:00 DBG] BMS: 🔧 调试级别日志测试
[2025-08-26 22:12:10.638 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-08-26 22:12:10.640 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-08-26 22:12:10.734 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-26 22:12:11.277 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-26 22:12:11.345 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-08-26 22:12:11.360 +08:00 INF] BMS: ✅ DbContext 初始化成功，日志文件位置: logs/
[2025-08-26 22:12:12.277 +08:00 WRN] Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware: Failed to determine the https port for redirect.
[2025-08-26 22:17:51.978 +08:00 INF] BMS: 🚀 应用程序启动，日志系统已配置
[2025-08-26 22:17:52.004 +08:00 DBG] BMS: 🔧 调试级别日志测试
[2025-08-26 22:17:52.008 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-08-26 22:17:52.010 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-08-26 22:17:52.057 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-26 22:17:52.427 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-26 22:17:52.434 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-08-26 22:17:52.435 +08:00 INF] BMS: ✅ DbContext 初始化成功，日志文件位置: logs/
[2025-08-26 22:19:06.582 +08:00 INF] BMS: 🚀 应用程序启动，日志系统已配置
[2025-08-26 22:19:06.606 +08:00 DBG] BMS: 🔧 调试级别日志测试
[2025-08-26 22:19:06.610 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-08-26 22:19:06.612 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-08-26 22:19:06.658 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-26 22:19:07.130 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-26 22:19:07.137 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-08-26 22:19:07.138 +08:00 INF] BMS: ✅ DbContext 初始化成功，日志文件位置: logs/
[2025-08-26 22:20:50.702 +08:00 INF] BMS: 🚀 应用程序启动，日志系统已配置
[2025-08-26 22:20:50.726 +08:00 DBG] BMS: 🔧 调试级别日志测试
[2025-08-26 22:20:50.729 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-08-26 22:20:50.731 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-08-26 22:20:50.778 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-26 22:20:51.191 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-26 22:20:51.198 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-08-26 22:20:51.199 +08:00 INF] BMS: ✅ DbContext 初始化成功，日志文件位置: logs/
[2025-08-26 22:21:37.001 +08:00 WRN] Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware: Failed to determine the https port for redirect.
[2025-08-26 22:21:51.957 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-08-26 22:21:51.960 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-08-26 22:21:51.967 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-26 22:21:52.003 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-26 22:21:52.005 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-08-26 22:21:58.332 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-08-26 22:21:58.361 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-08-26 22:21:58.365 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-26 22:21:58.401 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-26 22:21:58.403 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-08-26 22:21:58.488 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`username`,`password`,`real_name`,`phone`,`email`,`status`,`create_time`,`update_time`,`last_login_time` FROM `admin_bm`   WHERE ( `username` = @username0 )   LIMIT 0,1 | 参数: [@username0=HM]
[2025-08-26 22:21:58.546 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`username`,`password`,`real_name`,`phone`,`email`,`status`,`create_time`,`update_time`,`last_login_time` FROM `admin_bm`   WHERE ( `username` = @username0 )   LIMIT 0,1
[2025-08-26 22:22:10.910 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-08-26 22:22:11.008 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-08-26 22:22:11.014 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-26 22:22:11.065 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-26 22:22:11.108 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-08-26 22:22:11.130 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`username`,`password`,`real_name`,`phone`,`email`,`status`,`create_time`,`update_time`,`last_login_time` FROM `admin_bm`   WHERE ( `username` = @username0 )   LIMIT 0,1 | 参数: [@username0=HM]
[2025-08-26 22:22:11.250 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`username`,`password`,`real_name`,`phone`,`email`,`status`,`create_time`,`update_time`,`last_login_time` FROM `admin_bm`   WHERE ( `username` = @username0 )   LIMIT 0,1
[2025-08-26 22:22:11.335 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: UPDATE `admin_bm`  SET
            `update_time` = @constant1 , `last_login_time` = @constant0   WHERE ( `id` = @id2 ) | 参数: [@constant0=2025/8/26 22:22:11, @constant1=2025/8/26 22:22:11, @id2=1, @id=0]
[2025-08-26 22:22:11.405 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: UPDATE `admin_bm`  SET
            `update_time` = @constant1 , `last_login_time` = @constant0   WHERE ( `id` = @id2 )
[2025-08-26 22:22:20.658 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-08-26 22:22:20.721 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-08-26 22:22:20.724 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-26 22:22:20.759 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-26 22:22:20.760 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-08-26 22:22:20.803 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-08-26 22:22:20.808 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-08-26 22:22:20.811 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-26 22:22:20.848 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-26 22:22:20.851 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-08-26 22:22:20.870 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-08-26 22:22:20.873 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-08-26 22:22:20.874 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-26 22:22:20.908 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-26 22:22:20.909 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-08-26 22:22:20.920 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `task_id` AS `Value` , `task_name` AS `Label`  FROM `task_config`  WHERE ( `is_active` = @is_active0 )ORDER BY `sort_order` ASC  | 参数: [@is_active0=1]
[2025-08-26 22:22:20.958 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `task_id` AS `Value` , `task_name` AS `Label`  FROM `task_config`  WHERE ( `is_active` = @is_active0 )ORDER BY `sort_order` ASC 
[2025-08-26 22:22:20.965 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-08-26 22:22:20.965 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-08-26 22:22:20.976 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-26 22:22:21.011 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-26 22:22:21.013 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-08-26 22:22:21.056 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM `task_config`  
[2025-08-26 22:22:21.095 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM `task_config`  
[2025-08-26 22:22:21.097 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`    ORDER BY `sort_order` ASC,`created_at` DESC LIMIT 0,10
[2025-08-26 22:22:21.136 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`    ORDER BY `sort_order` ASC,`created_at` DESC LIMIT 0,10
[2025-08-26 22:22:21.144 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_001]
[2025-08-26 22:22:21.178 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-26 22:22:21.187 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_002]
[2025-08-26 22:22:21.227 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-26 22:22:21.231 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_003]
[2025-08-26 22:22:21.281 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-26 22:22:21.286 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_004]
[2025-08-26 22:22:21.321 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-26 22:22:21.325 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_005]
[2025-08-26 22:22:21.357 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-26 22:22:21.360 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_010]
[2025-08-26 22:22:21.393 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-26 22:22:42.948 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-08-26 22:22:42.949 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-08-26 22:22:42.950 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-26 22:22:42.983 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-26 22:22:42.985 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-08-26 22:22:42.991 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`   WHERE ( `task_id` = @task_id0 )   LIMIT 0,1 | 参数: [@task_id0=TASK_001]
[2025-08-26 22:22:43.042 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`   WHERE ( `task_id` = @task_id0 )   LIMIT 0,1
[2025-08-26 22:22:43.136 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_001]
[2025-08-26 22:22:43.193 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-26 22:22:51.053 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-08-26 22:22:51.073 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-08-26 22:22:51.078 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-26 22:22:51.113 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-26 22:22:51.115 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-08-26 22:35:51.301 +08:00 INF] BMS: 🚀 应用程序启动，日志系统已配置
[2025-08-26 22:35:51.327 +08:00 DBG] BMS: 🔧 调试级别日志测试
[2025-08-26 22:35:51.331 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-08-26 22:35:51.333 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-08-26 22:35:51.376 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-26 22:35:51.785 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-26 22:35:51.793 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-08-26 22:35:51.793 +08:00 INF] BMS: ✅ DbContext 初始化成功，日志文件位置: logs/
[2025-08-26 22:50:28.567 +08:00 WRN] Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware: Failed to determine the https port for redirect.
[2025-08-26 22:50:29.251 +08:00 INF] BMS: 🚀 应用程序启动，日志系统已配置
[2025-08-26 22:50:29.349 +08:00 DBG] BMS: 🔧 调试级别日志测试
[2025-08-26 22:50:29.370 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-08-26 22:50:29.373 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-08-26 22:50:29.476 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-26 22:50:30.057 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-26 22:50:30.067 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-08-26 22:50:30.069 +08:00 INF] BMS: ✅ DbContext 初始化成功，日志文件位置: logs/
[2025-08-26 22:50:30.262 +08:00 ERR] Microsoft.Extensions.Hosting.Internal.Host: Hosting failed to start
System.IO.IOException: Failed to bind to address http://127.0.0.1:5078: address already in use.
 ---> Microsoft.AspNetCore.Connections.AddressInUseException: 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
 ---> System.Net.Sockets.SocketException (10048): 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
   at System.Net.Sockets.Socket.UpdateStatusAfterSocketErrorAndThrowException(SocketError error, Boolean disconnectOnFailure, String callerName)
   at System.Net.Sockets.Socket.DoBind(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Bind(EndPoint localEP)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportOptions.CreateDefaultBoundListenSocket(EndPoint endpoint)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportFactory.BindAsync(EndPoint endpoint, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Infrastructure.TransportManager.BindAsync(EndPoint endPoint, ConnectionDelegate connectionDelegate, EndpointConfig endpointConfig, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.<>c__DisplayClass28_0`1.<<StartAsync>g__OnBind|0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.LocalhostListenOptions.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.AddressesStrategy.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindAsync(ListenOptions[] listenOptions, AddressBindContext context, Func`2 useHttps, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.BindAsync(CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.StartAsync[TContext](IHttpApplication`1 application, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Hosting.GenericWebHostService.StartAsync(CancellationToken cancellationToken)
   at Microsoft.Extensions.Hosting.Internal.Host.<StartAsync>b__15_1(IHostedService service, CancellationToken token)
   at Microsoft.Extensions.Hosting.Internal.Host.ForeachService[T](IEnumerable`1 services, CancellationToken token, Boolean concurrent, Boolean abortOnFirstException, List`1 exceptions, Func`3 operation)
[2025-08-26 22:51:06.029 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-08-26 22:51:06.032 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-08-26 22:51:06.037 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-26 22:51:06.257 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-26 22:51:06.258 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-08-26 22:51:06.347 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-08-26 22:51:06.348 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-08-26 22:51:06.350 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-26 22:51:06.385 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-26 22:51:06.386 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-08-26 22:51:06.405 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-08-26 22:51:06.405 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-08-26 22:51:06.406 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-26 22:51:06.441 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-26 22:51:06.442 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-08-26 22:51:06.470 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `task_id` AS `Value` , `task_name` AS `Label`  FROM `task_config`  WHERE ( `is_active` = @is_active0 )ORDER BY `sort_order` ASC  | 参数: [@is_active0=1]
[2025-08-26 22:51:06.509 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `task_id` AS `Value` , `task_name` AS `Label`  FROM `task_config`  WHERE ( `is_active` = @is_active0 )ORDER BY `sort_order` ASC 
[2025-08-26 22:51:06.540 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-08-26 22:51:06.541 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-08-26 22:51:06.542 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-26 22:51:06.574 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-26 22:51:06.575 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-08-26 22:51:06.604 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM `task_config`  
[2025-08-26 22:51:06.646 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM `task_config`  
[2025-08-26 22:51:06.649 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`    ORDER BY `sort_order` ASC,`created_at` DESC LIMIT 0,10
[2025-08-26 22:51:06.685 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`    ORDER BY `sort_order` ASC,`created_at` DESC LIMIT 0,10
[2025-08-26 22:51:06.693 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_001]
[2025-08-26 22:51:06.729 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-26 22:51:06.733 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_002]
[2025-08-26 22:51:06.768 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-26 22:51:06.770 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_003]
[2025-08-26 22:51:06.803 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-26 22:51:06.805 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_004]
[2025-08-26 22:51:06.837 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-26 22:51:06.839 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_005]
[2025-08-26 22:51:06.871 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-26 22:51:06.876 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_010]
[2025-08-26 22:51:06.909 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-26 22:51:51.396 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-08-26 22:51:51.396 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-08-26 22:51:51.398 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-26 22:51:51.430 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-26 22:51:51.431 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-08-26 22:51:51.446 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`   WHERE ( `task_id` = @task_id0 )   LIMIT 0,1 | 参数: [@task_id0=TASK_123]
[2025-08-26 22:51:51.478 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`   WHERE ( `task_id` = @task_id0 )   LIMIT 0,1
[2025-08-26 22:51:51.495 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: INSERT INTO `task_config`  
           (`task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at`)
     VALUES
           (@task_id,@task_name,@task_description,@task_type,@is_repeatable,@prerequisite_task,@required_pet,@reward_config,@is_network_task,@is_active,@sort_order,@created_at,@updated_at) ; | 参数: [@task_id=TASK_123, @task_name=测试, @task_description=这是一个测试任务, @task_type=0, @is_repeatable=0, @prerequisite_task=, @required_pet=, @reward_config={"exp": 100, "gold": 50}, @is_network_task=0, @is_active=1, @sort_order=1, @created_at=2025/8/26 22:51:51, @updated_at=2025/8/26 22:51:51]
[2025-08-26 22:51:51.542 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: INSERT INTO `task_config`  
           (`task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at`)
     VALUES
           (@task_id,@task_name,@task_description,@task_type,@is_repeatable,@prerequisite_task,@required_pet,@reward_config,@is_network_task,@is_active,@sort_order,@created_at,@updated_at) ;
[2025-08-26 22:51:51.546 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`   WHERE ( `task_id` = @task_id0 )   LIMIT 0,1 | 参数: [@task_id0=TASK_123]
[2025-08-26 22:51:51.579 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`   WHERE ( `task_id` = @task_id0 )   LIMIT 0,1
[2025-08-26 22:51:51.580 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_123]
[2025-08-26 22:51:51.612 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-26 22:51:51.617 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-08-26 22:51:51.618 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-08-26 22:51:51.619 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-26 22:51:51.651 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-26 22:51:51.652 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-08-26 22:51:51.653 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM `task_config`  
[2025-08-26 22:51:51.686 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM `task_config`  
[2025-08-26 22:51:51.688 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`    ORDER BY `sort_order` ASC,`created_at` DESC LIMIT 0,10
[2025-08-26 22:51:51.721 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`    ORDER BY `sort_order` ASC,`created_at` DESC LIMIT 0,10
[2025-08-26 22:51:51.724 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_123]
[2025-08-26 22:51:51.757 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-26 22:51:51.759 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_001]
[2025-08-26 22:51:51.792 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-26 22:51:51.794 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_002]
[2025-08-26 22:51:51.829 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-26 22:51:51.830 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_003]
[2025-08-26 22:51:51.864 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-26 22:51:51.866 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_004]
[2025-08-26 22:51:51.896 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-26 22:51:51.898 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_005]
[2025-08-26 22:51:51.931 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-26 22:51:51.933 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_010]
[2025-08-26 22:51:51.968 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-26 22:52:00.394 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-08-26 22:52:00.395 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-08-26 22:52:00.396 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-26 22:52:00.435 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-26 22:52:00.436 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-08-26 22:52:00.440 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`   WHERE ( `task_id` = @task_id0 )   LIMIT 0,1 | 参数: [@task_id0=TASK_123]
[2025-08-26 22:52:00.473 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`   WHERE ( `task_id` = @task_id0 )   LIMIT 0,1
[2025-08-26 22:52:00.475 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_123]
[2025-08-26 22:52:00.508 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-26 22:52:51.224 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-08-26 22:52:51.225 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-08-26 22:52:51.226 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-26 22:52:51.260 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-26 22:52:51.261 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-08-26 22:52:51.281 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM `task_config`  WHERE  (`task_name` like concat('%',@MethodConst0,'%'))   | 参数: [@MethodConst0=测试]
[2025-08-26 22:52:51.315 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM `task_config`  WHERE  (`task_name` like concat('%',@MethodConst0,'%'))  
[2025-08-26 22:52:51.316 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`   WHERE  (`task_name` like concat('%',@MethodConst0,'%'))   ORDER BY `sort_order` ASC,`created_at` DESC LIMIT 0,10 | 参数: [@MethodConst0=测试]
[2025-08-26 22:52:51.352 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`   WHERE  (`task_name` like concat('%',@MethodConst0,'%'))   ORDER BY `sort_order` ASC,`created_at` DESC LIMIT 0,10
[2025-08-26 22:52:51.354 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_123]
[2025-08-26 22:52:51.388 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-26 22:52:57.336 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-08-26 22:52:57.337 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-08-26 22:52:57.338 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-26 22:52:57.378 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-26 22:52:57.379 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-08-26 22:52:57.381 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM `task_config`  WHERE ( `task_type` = @task_type0 )  | 参数: [@task_type0=0]
[2025-08-26 22:52:57.416 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM `task_config`  WHERE ( `task_type` = @task_type0 ) 
[2025-08-26 22:52:57.417 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`   WHERE ( `task_type` = @task_type0 )  ORDER BY `sort_order` ASC,`created_at` DESC LIMIT 0,10 | 参数: [@task_type0=0]
[2025-08-26 22:52:57.449 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`   WHERE ( `task_type` = @task_type0 )  ORDER BY `sort_order` ASC,`created_at` DESC LIMIT 0,10
[2025-08-26 22:52:57.451 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_123]
[2025-08-26 22:52:57.485 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-26 22:52:57.486 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_001]
[2025-08-26 22:52:57.518 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-26 22:52:57.520 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_004]
[2025-08-26 22:52:57.552 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-26 22:52:57.554 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_005]
[2025-08-26 22:52:57.590 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-26 22:52:57.591 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_010]
[2025-08-26 22:52:57.624 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-26 22:53:06.304 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-08-26 22:53:06.305 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-08-26 22:53:06.307 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-26 22:53:06.338 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-26 22:53:06.339 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-08-26 22:53:06.341 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM `task_config`  WHERE ( `task_type` = @task_type0 )  AND (( `is_active` = @is_active1 ) =  @Const2 )  | 参数: [@task_type0=0, @is_active1=1, @Const2=True]
[2025-08-26 22:53:06.372 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM `task_config`  WHERE ( `task_type` = @task_type0 )  AND (( `is_active` = @is_active1 ) =  @Const2 ) 
[2025-08-26 22:53:06.373 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`   WHERE ( `task_type` = @task_type0 )  AND (( `is_active` = @is_active1 ) =  @Const2 )  ORDER BY `sort_order` ASC,`created_at` DESC LIMIT 0,10 | 参数: [@task_type0=0, @is_active1=1, @Const2=True]
[2025-08-26 22:53:06.406 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`   WHERE ( `task_type` = @task_type0 )  AND (( `is_active` = @is_active1 ) =  @Const2 )  ORDER BY `sort_order` ASC,`created_at` DESC LIMIT 0,10
[2025-08-26 22:53:06.407 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_123]
[2025-08-26 22:53:06.440 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-26 22:53:06.441 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_001]
[2025-08-26 22:53:06.472 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-26 22:53:06.474 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_004]
[2025-08-26 22:53:06.506 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-26 22:53:06.508 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_005]
[2025-08-26 22:53:06.540 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-26 22:53:06.541 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_010]
[2025-08-26 22:53:06.576 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-26 22:53:07.975 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-08-26 22:53:07.976 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-08-26 22:53:07.977 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-26 22:53:08.015 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-26 22:53:08.016 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-08-26 22:53:08.018 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM `task_config`  
[2025-08-26 22:53:08.052 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM `task_config`  
[2025-08-26 22:53:08.054 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`    ORDER BY `sort_order` ASC,`created_at` DESC LIMIT 0,10
[2025-08-26 22:53:08.090 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`    ORDER BY `sort_order` ASC,`created_at` DESC LIMIT 0,10
[2025-08-26 22:53:08.092 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_123]
[2025-08-26 22:53:08.128 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-26 22:53:08.129 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_001]
[2025-08-26 22:53:08.164 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-26 22:53:08.165 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_002]
[2025-08-26 22:53:08.198 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-26 22:53:08.199 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_003]
[2025-08-26 22:53:08.234 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-26 22:53:08.235 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_004]
[2025-08-26 22:53:08.270 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-26 22:53:08.271 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_005]
[2025-08-26 22:53:08.305 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-26 22:53:08.306 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_010]
[2025-08-26 22:53:08.338 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-26 22:53:13.358 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-08-26 22:53:13.359 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-08-26 22:53:13.360 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-26 22:53:13.394 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-26 22:53:13.395 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-08-26 22:53:13.398 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM `task_config`  WHERE (( `is_active` = @is_active0 ) =  @Const1 )  | 参数: [@is_active0=1, @Const1=False]
[2025-08-26 22:53:13.430 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM `task_config`  WHERE (( `is_active` = @is_active0 ) =  @Const1 ) 
[2025-08-26 22:53:13.432 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`   WHERE (( `is_active` = @is_active0 ) =  @Const1 )  ORDER BY `sort_order` ASC,`created_at` DESC LIMIT 0,10 | 参数: [@is_active0=1, @Const1=False]
[2025-08-26 22:53:13.464 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`   WHERE (( `is_active` = @is_active0 ) =  @Const1 )  ORDER BY `sort_order` ASC,`created_at` DESC LIMIT 0,10
[2025-08-26 22:53:15.774 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-08-26 22:53:15.775 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-08-26 22:53:15.776 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-26 22:53:15.808 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-26 22:53:15.808 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-08-26 22:53:15.810 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM `task_config`  WHERE (( `is_active` = @is_active0 ) =  @Const1 )  | 参数: [@is_active0=1, @Const1=True]
[2025-08-26 22:53:15.840 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM `task_config`  WHERE (( `is_active` = @is_active0 ) =  @Const1 ) 
[2025-08-26 22:53:15.841 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`   WHERE (( `is_active` = @is_active0 ) =  @Const1 )  ORDER BY `sort_order` ASC,`created_at` DESC LIMIT 0,10 | 参数: [@is_active0=1, @Const1=True]
[2025-08-26 22:53:15.875 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`   WHERE (( `is_active` = @is_active0 ) =  @Const1 )  ORDER BY `sort_order` ASC,`created_at` DESC LIMIT 0,10
[2025-08-26 22:53:15.876 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_123]
[2025-08-26 22:53:15.908 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-26 22:53:15.909 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_001]
[2025-08-26 22:53:15.946 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-26 22:53:15.947 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_002]
[2025-08-26 22:53:15.981 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-26 22:53:15.983 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_003]
[2025-08-26 22:53:16.013 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-26 22:53:16.015 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_004]
[2025-08-26 22:53:16.048 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-26 22:53:16.049 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_005]
[2025-08-26 22:53:16.082 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-26 22:53:16.083 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_010]
[2025-08-26 22:53:16.114 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-26 23:09:29.726 +08:00 INF] BMS: 🚀 应用程序启动，日志系统已配置
[2025-08-26 23:09:29.758 +08:00 DBG] BMS: 🔧 调试级别日志测试
[2025-08-26 23:09:29.776 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-08-26 23:09:29.779 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-08-26 23:09:29.873 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-26 23:09:31.365 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-26 23:09:31.374 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-08-26 23:09:31.375 +08:00 INF] BMS: ✅ DbContext 初始化成功，日志文件位置: logs/
[2025-08-26 23:09:32.409 +08:00 WRN] Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware: Failed to determine the https port for redirect.
[2025-08-26 23:18:23.337 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-08-26 23:18:23.483 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-08-26 23:18:23.492 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-26 23:18:23.770 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-26 23:18:23.772 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-08-26 23:18:23.866 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-08-26 23:18:23.867 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-08-26 23:18:23.871 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-26 23:18:23.920 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-26 23:18:23.922 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-08-26 23:18:23.951 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-08-26 23:18:23.954 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-08-26 23:18:23.956 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-26 23:18:24.003 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-26 23:18:24.005 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-08-26 23:18:24.034 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `task_id` AS `Value` , `task_name` AS `Label`  FROM `task_config`  WHERE ( `is_active` = @is_active0 )ORDER BY `sort_order` ASC  | 参数: [@is_active0=1]
[2025-08-26 23:18:24.091 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `task_id` AS `Value` , `task_name` AS `Label`  FROM `task_config`  WHERE ( `is_active` = @is_active0 )ORDER BY `sort_order` ASC 
[2025-08-26 23:18:24.138 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-08-26 23:18:24.140 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-08-26 23:18:24.142 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-26 23:18:24.188 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-26 23:18:24.191 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-08-26 23:18:24.219 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM `task_config`  
[2025-08-26 23:18:24.273 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM `task_config`  
[2025-08-26 23:18:24.281 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`    ORDER BY `sort_order` ASC,`created_at` DESC LIMIT 0,10
[2025-08-26 23:18:24.328 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`    ORDER BY `sort_order` ASC,`created_at` DESC LIMIT 0,10
[2025-08-26 23:18:24.342 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_123]
[2025-08-26 23:18:24.390 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-26 23:18:24.402 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_001]
[2025-08-26 23:18:24.452 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-26 23:18:24.460 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_002]
[2025-08-26 23:18:24.506 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-26 23:18:24.508 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_003]
[2025-08-26 23:18:24.555 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-26 23:18:24.557 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_004]
[2025-08-26 23:18:24.603 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-26 23:18:24.607 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_005]
[2025-08-26 23:18:24.654 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-26 23:18:24.656 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_010]
[2025-08-26 23:18:24.702 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-26 23:18:36.846 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-08-26 23:18:37.494 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-08-26 23:18:37.504 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-26 23:18:37.555 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-26 23:18:37.557 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-08-26 23:37:31.152 +08:00 INF] BMS: 🚀 应用程序启动，日志系统已配置
[2025-08-26 23:37:31.177 +08:00 DBG] BMS: 🔧 调试级别日志测试
[2025-08-26 23:37:31.181 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-08-26 23:37:31.183 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-08-26 23:37:31.228 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-26 23:37:31.665 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-26 23:37:31.672 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-08-26 23:37:31.674 +08:00 INF] BMS: ✅ DbContext 初始化成功，日志文件位置: logs/
[2025-08-26 23:40:10.392 +08:00 INF] BMS: 🚀 应用程序启动，日志系统已配置
[2025-08-26 23:40:10.418 +08:00 DBG] BMS: 🔧 调试级别日志测试
[2025-08-26 23:40:10.422 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-08-26 23:40:10.423 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-08-26 23:40:10.473 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-26 23:40:10.973 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-26 23:40:10.981 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-08-26 23:40:10.983 +08:00 INF] BMS: ✅ DbContext 初始化成功，日志文件位置: logs/
[2025-08-26 23:41:03.288 +08:00 WRN] Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware: Failed to determine the https port for redirect.
[2025-08-26 23:41:03.323 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-08-26 23:41:03.325 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-08-26 23:41:03.327 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-26 23:41:03.372 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-26 23:41:03.374 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-08-26 23:41:06.030 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-08-26 23:41:06.030 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-08-26 23:41:06.032 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-26 23:41:06.075 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-26 23:41:06.076 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-08-26 23:41:06.108 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-08-26 23:41:06.109 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-08-26 23:41:06.110 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-26 23:41:06.154 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-26 23:41:06.156 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-08-26 23:41:06.187 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `task_id` AS `Value` , `task_name` AS `Label`  FROM `task_config`  WHERE ( `is_active` = @is_active0 )ORDER BY `sort_order` ASC  | 参数: [@is_active0=1]
[2025-08-26 23:41:06.239 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `task_id` AS `Value` , `task_name` AS `Label`  FROM `task_config`  WHERE ( `is_active` = @is_active0 )ORDER BY `sort_order` ASC 
[2025-08-26 23:41:06.280 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-08-26 23:41:06.281 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-08-26 23:41:06.283 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-26 23:41:06.324 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-26 23:41:06.325 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-08-26 23:41:06.357 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM `task_config`  
[2025-08-26 23:41:06.402 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM `task_config`  
[2025-08-26 23:41:06.404 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`    ORDER BY `sort_order` ASC,`created_at` DESC LIMIT 0,10
[2025-08-26 23:41:06.454 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`    ORDER BY `sort_order` ASC,`created_at` DESC LIMIT 0,10
[2025-08-26 23:41:06.462 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_123]
[2025-08-26 23:41:06.506 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-26 23:41:06.508 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_001]
[2025-08-26 23:41:06.552 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-26 23:41:06.556 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_002]
[2025-08-26 23:41:06.600 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-26 23:41:06.602 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_003]
[2025-08-26 23:41:06.645 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-26 23:41:06.647 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_004]
[2025-08-26 23:41:06.695 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-26 23:41:06.696 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_005]
[2025-08-26 23:41:06.740 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-26 23:41:06.741 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_010]
[2025-08-26 23:41:06.784 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
