using BMS.Models.DTOs;
using BMS.Models.Common;

namespace BMS.Services.Interfaces
{
    /// <summary>
    /// 怪物配置服务接口
    /// </summary>
    public interface IMonsterConfigService
    {
        /// <summary>
        /// 分页获取怪物配置列表
        /// </summary>
        /// <param name="queryDto">查询条件</param>
        /// <returns>分页结果</returns>
        Task<PagedResult<MonsterConfigDto>> GetPagedListAsync(MonsterConfigQueryDto queryDto);

        /// <summary>
        /// 根据ID获取怪物配置
        /// </summary>
        /// <param name="id">怪物配置ID</param>
        /// <returns>怪物配置信息</returns>
        Task<MonsterConfigDto?> GetByIdAsync(int id);

        /// <summary>
        /// 根据怪物编号获取怪物配置
        /// </summary>
        /// <param name="monsterNo">怪物编号</param>
        /// <returns>怪物配置信息</returns>
        Task<MonsterConfigDto?> GetByMonsterNoAsync(int monsterNo);

        /// <summary>
        /// 获取所有怪物配置列表（不分页）
        /// </summary>
        /// <returns>怪物配置列表</returns>
        Task<List<MonsterConfigDto>> GetAllAsync();

        /// <summary>
        /// 创建怪物配置
        /// </summary>
        /// <param name="createDto">创建DTO</param>
        /// <returns>操作结果</returns>
        Task<ApiResult<bool>> CreateAsync(MonsterConfigCreateDto createDto);

        /// <summary>
        /// 更新怪物配置
        /// </summary>
        /// <param name="updateDto">更新DTO</param>
        /// <returns>操作结果</returns>
        Task<ApiResult<bool>> UpdateAsync(MonsterConfigUpdateDto updateDto);

        /// <summary>
        /// 删除怪物配置
        /// </summary>
        /// <param name="id">怪物配置ID</param>
        /// <returns>操作结果</returns>
        Task<ApiResult<bool>> DeleteAsync(int id);

        /// <summary>
        /// 检查怪物编号是否存在
        /// </summary>
        /// <param name="monsterNo">怪物编号</param>
        /// <param name="id">排除的ID（编辑时使用）</param>
        /// <returns>是否存在</returns>
        Task<bool> CheckMonsterNoExistsAsync(int monsterNo, int? id = null);
    }
} 