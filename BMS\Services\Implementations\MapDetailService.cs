using BMS.Models.DTOs;
using BMS.Models.Common;
using BMS.Models.Entities;
using BMS.Services.Interfaces;
using SqlSugar;

namespace BMS.Services.Implementations
{
    /// <summary>
    /// 地图详情服务实现
    /// </summary>
    public class MapDetailService : IMapDetailService
    {
        private readonly IDbService _dbService;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="dbService">数据库服务</param>
        public MapDetailService(IDbService dbService)
        {
            _dbService = dbService;
        }

        /// <summary>
        /// 分页获取地图详情列表
        /// </summary>
        /// <param name="queryDto">查询条件</param>
        /// <returns>分页结果</returns>
        public async Task<PagedResult<MapDetailDto>> GetPagedListAsync(MapDetailQueryDto queryDto)
        {
            // 获取原生SqlSugar客户端进行多表查询
            var db = _dbService.GetClient();
            
            // 联表查询地图名称
            var query = db.Queryable<map_detail, map_config>((d, c) => new object[]
            {
                JoinType.Left, d.map_id == c.map_id
            });

            // 添加查询条件
            if (queryDto.MapId.HasValue)
            {
                query = query.Where((d, c) => d.map_id == queryDto.MapId.Value);
            }

            if (queryDto.Type.HasValue)
            {
                query = query.Where((d, c) => d.type == queryDto.Type.Value);
            }

            // 获取总数
            RefAsync<int> totalCount = 0;
            var items = await query
                .OrderBy((d, c) => d.id)
                .Select((d, c) => new MapDetailDto
                {
                    Id = d.id,
                    MapId = d.map_id,
                    MapName = c.map_name, // 地图名称
                    LimitGrowth = d.limit_growth ?? 0,
                    LimitLevel = d.limit_level ?? 0,
                    LimitKey = d.limit_key ?? false,
                    MinGold = d.min_gold ?? 0,
                    MaxGold = d.max_gold ?? 0,
                    MinYuanbao = d.min_yuanbao ?? 0,
                    MaxYuanbao = d.max_yuanbao ?? 0,
                    MinDrop = d.min_drop ?? 0,
                    MaxDrop = d.max_drop ?? 0,
                    Icon = d.icon,
                    Type = d.type ?? 0
                })
                .ToPageListAsync(queryDto.Page, queryDto.PageSize, totalCount);

            return new PagedResult<MapDetailDto>(items, queryDto.Page, queryDto.PageSize, totalCount);
        }

        /// <summary>
        /// 根据ID获取地图详情
        /// </summary>
        /// <param name="id">地图详情ID</param>
        /// <returns>地图详情信息</returns>
        public async Task<MapDetailDto?> GetByIdAsync(int id)
        {
            var entity = await _dbService.Queryable<map_detail>()
                .FirstAsync(x => x.id == id);

            return entity != null ? ConvertToDto(entity) : null;
        }

        /// <summary>
        /// 根据地图ID获取地图详情
        /// </summary>
        /// <param name="mapId">地图ID</param>
        /// <returns>地图详情信息</returns>
        public async Task<MapDetailDto?> GetByMapIdAsync(int mapId)
        {
            var entity = await _dbService.Queryable<map_detail>()
                .FirstAsync(x => x.map_id == mapId);

            return entity != null ? ConvertToDto(entity) : null;
        }

        /// <summary>
        /// 创建地图详情
        /// </summary>
        /// <param name="createDto">创建DTO</param>
        /// <returns>操作结果</returns>
        public async Task<ApiResult<bool>> CreateAsync(MapDetailCreateDto createDto)
        {
            try
            {
                // 检查地图详情是否已存在
                var exists = await _dbService.Queryable<map_detail>()
                    .AnyAsync(x => x.map_id == createDto.MapId);
                if (exists)
                {
                    return ApiResult<bool>.Fail("该地图的详情配置已存在");
                }

                // 转换为实体
                var entity = ConvertToEntity(createDto);

                // 插入数据
                var result = await _dbService.Insertable(entity)
                    .ExecuteReturnBigIdentityAsync();

                if (result > 0)
                {
                    return ApiResult<bool>.Ok(true, "地图详情创建成功");
                }
                else
                {
                    return ApiResult<bool>.Fail("地图详情创建失败");
                }
            }
            catch (Exception ex)
            {
                return ApiResult<bool>.Fail($"创建地图详情时发生错误：{ex.Message}");
            }
        }

        /// <summary>
        /// 更新地图详情
        /// </summary>
        /// <param name="updateDto">更新DTO</param>
        /// <returns>操作结果</returns>
        public async Task<ApiResult<bool>> UpdateAsync(MapDetailUpdateDto updateDto)
        {
            try
            {
                // 检查记录是否存在
                var exists = await _dbService.Queryable<map_detail>()
                    .AnyAsync(x => x.id == updateDto.Id);
                if (!exists)
                {
                    return ApiResult<bool>.Fail("地图详情不存在");
                }

                // 转换为实体
                var entity = ConvertToEntity(updateDto);

                // 更新数据
                var result = await _dbService.Updateable(entity)
                    .ExecuteCommandAsync();

                if (result > 0)
                {
                    return ApiResult<bool>.Ok(true, "地图详情更新成功");
                }
                else
                {
                    return ApiResult<bool>.Fail("地图详情更新失败");
                }
            }
            catch (Exception ex)
            {
                return ApiResult<bool>.Fail($"更新地图详情时发生错误：{ex.Message}");
            }
        }

        /// <summary>
        /// 删除地图详情
        /// </summary>
        /// <param name="id">地图详情ID</param>
        /// <returns>操作结果</returns>
        public async Task<ApiResult<bool>> DeleteAsync(int id)
        {
            try
            {
                // 检查记录是否存在
                var exists = await _dbService.Queryable<map_detail>()
                    .AnyAsync(x => x.id == id);
                if (!exists)
                {
                    return ApiResult<bool>.Fail("地图详情不存在");
                }

                // 删除数据
                var result = await _dbService.Deleteable<map_detail>()
                    .Where(x => x.id == id)
                    .ExecuteCommandAsync();

                if (result > 0)
                {
                    return ApiResult<bool>.Ok(true, "地图详情删除成功");
                }
                else
                {
                    return ApiResult<bool>.Fail("地图详情删除失败");
                }
            }
            catch (Exception ex)
            {
                return ApiResult<bool>.Fail($"删除地图详情时发生错误：{ex.Message}");
            }
        }

        /// <summary>
        /// 根据地图ID删除地图详情
        /// </summary>
        /// <param name="mapId">地图ID</param>
        /// <returns>操作结果</returns>
        public async Task<ApiResult<bool>> DeleteByMapIdAsync(int mapId)
        {
            try
            {
                // 删除数据
                var result = await _dbService.Deleteable<map_detail>()
                    .Where(x => x.map_id == mapId)
                    .ExecuteCommandAsync();

                return ApiResult<bool>.Ok(true, $"删除了{result}条地图详情记录");
            }
            catch (Exception ex)
            {
                return ApiResult<bool>.Fail($"删除地图详情时发生错误：{ex.Message}");
            }
        }

        /// <summary>
        /// 实体转换为DTO
        /// </summary>
        /// <param name="entity">实体</param>
        /// <returns>DTO</returns>
        private static MapDetailDto ConvertToDto(map_detail entity)
        {
            return new MapDetailDto
            {
                Id = entity.id,
                MapId = entity.map_id,
                LimitGrowth = entity.limit_growth ?? 0m,
                LimitLevel = entity.limit_level ?? 0,
                LimitKey = entity.limit_key ?? false,
                MinGold = entity.min_gold ?? 0,
                MinYuanbao = entity.min_yuanbao ?? 0,
                MaxGold = entity.max_gold ?? 0,
                MaxYuanbao = entity.max_yuanbao ?? 0,
                MaxDrop = entity.max_drop ?? 0,
                MinDrop = entity.min_drop ?? 0,
                Icon = entity.icon,
                Type = entity.type ?? 0
            };
        }

        /// <summary>
        /// 创建DTO转换为实体
        /// </summary>
        /// <param name="createDto">创建DTO</param>
        /// <returns>实体</returns>
        private static map_detail ConvertToEntity(MapDetailCreateDto createDto)
        {
            return new map_detail
            {
                map_id = createDto.MapId,
                limit_growth = createDto.LimitGrowth,
                limit_level = createDto.LimitLevel,
                limit_key = createDto.LimitKey,
                min_gold = createDto.MinGold,
                min_yuanbao = createDto.MinYuanbao,
                max_gold = createDto.MaxGold,
                max_yuanbao = createDto.MaxYuanbao,
                max_drop = createDto.MaxDrop,
                min_drop = createDto.MinDrop,
                icon = createDto.Icon,
                type = createDto.Type
            };
        }

        /// <summary>
        /// 更新DTO转换为实体
        /// </summary>
        /// <param name="updateDto">更新DTO</param>
        /// <returns>实体</returns>
        private static map_detail ConvertToEntity(MapDetailUpdateDto updateDto)
        {
            return new map_detail
            {
                id = updateDto.Id,
                map_id = updateDto.MapId,
                limit_growth = updateDto.LimitGrowth,
                limit_level = updateDto.LimitLevel,
                limit_key = updateDto.LimitKey,
                min_gold = updateDto.MinGold,
                min_yuanbao = updateDto.MinYuanbao,
                max_gold = updateDto.MaxGold,
                max_yuanbao = updateDto.MaxYuanbao,
                max_drop = updateDto.MaxDrop,
                min_drop = updateDto.MinDrop,
                icon = updateDto.Icon,
                type = updateDto.Type
            };
        }
    }
} 