using BMS.Models.Common;
using BMS.Models.DTOs;

namespace BMS.Services.Interfaces
{
    /// <summary>
    /// 用户宠物服务接口
    /// </summary>
    public interface IUserPetService
    {
        /// <summary>
        /// 分页查询用户宠物列表
        /// </summary>
        /// <param name="queryDto">查询条件</param>
        /// <returns>分页结果</returns>
        Task<PagedResult<UserPetDto>> GetUserPetsAsync(UserPetQueryDto queryDto);

        /// <summary>
        /// 根据ID获取用户宠物信息
        /// </summary>
        /// <param name="id">用户宠物ID</param>
        /// <returns>用户宠物信息</returns>
        Task<UserPetDto?> GetUserPetByIdAsync(int id);

        /// <summary>
        /// 根据用户ID获取该用户的所有宠物
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>用户宠物列表</returns>
        Task<List<UserPetDto>> GetPetsByUserIdAsync(int userId);

        /// <summary>
        /// 创建用户宠物
        /// </summary>
        /// <param name="createDto">创建用户宠物DTO</param>
        /// <returns>创建结果</returns>
        Task<ApiResult<int>> CreateUserPetAsync(UserPetCreateDto createDto);

        /// <summary>
        /// 更新用户宠物信息
        /// </summary>
        /// <param name="updateDto">更新用户宠物DTO</param>
        /// <returns>更新结果</returns>
        Task<ApiResult<bool>> UpdateUserPetAsync(UserPetUpdateDto updateDto);

        /// <summary>
        /// 删除用户宠物
        /// </summary>
        /// <param name="deleteDto">删除用户宠物DTO</param>
        /// <returns>删除结果</returns>
        Task<ApiResult<bool>> DeleteUserPetAsync(UserPetDeleteDto deleteDto);

        /// <summary>
        /// 检查用户是否已拥有指定宠物
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="petNo">宠物序号</param>
        /// <param name="excludeId">排除的用户宠物ID（用于更新时检查）</param>
        /// <returns>是否已拥有</returns>
        Task<bool> CheckUserPetExistsAsync(int userId, int petNo, int? excludeId = null);

        /// <summary>
        /// 获取用户宠物的技能列表
        /// </summary>
        /// <param name="userPetId">用户宠物ID</param>
        /// <returns>技能列表</returns>
        Task<List<UserPetSkillDto>> GetUserPetSkillsAsync(int userPetId);

        /// <summary>
        /// 为用户宠物添加技能
        /// </summary>
        /// <param name="manageDto">添加技能DTO</param>
        /// <returns>添加结果</returns>
        Task<ApiResult<int>> AddUserPetSkillAsync(UserPetSkillManageDto manageDto);

        /// <summary>
        /// 更新用户宠物技能等级
        /// </summary>
        /// <param name="skillId">技能记录ID</param>
        /// <param name="skillLevel">技能等级</param>
        /// <returns>更新结果</returns>
        Task<ApiResult<bool>> UpdateUserPetSkillLevelAsync(int skillId, int skillLevel);

        /// <summary>
        /// 删除用户宠物技能
        /// </summary>
        /// <param name="deleteDto">删除技能DTO</param>
        /// <returns>删除结果</returns>
        Task<ApiResult<bool>> DeleteUserPetSkillAsync(UserPetSkillDeleteDto deleteDto);

        /// <summary>
        /// 检查用户宠物是否已拥有指定技能
        /// </summary>
        /// <param name="userPetId">用户宠物ID</param>
        /// <param name="skillId">技能编号</param>
        /// <returns>是否已拥有</returns>
        Task<bool> CheckUserPetSkillExistsAsync(int userPetId, int skillId);

        /// <summary>
        /// 获取宠物配置下拉选项
        /// </summary>
        /// <returns>宠物配置选项</returns>
        Task<List<PetConfigDto>> GetPetConfigOptionsAsync();

        /// <summary>
        /// 批量为用户宠物分配技能（根据宠物配置）
        /// </summary>
        /// <param name="userPetId">用户宠物ID</param>
        /// <returns>分配结果</returns>
        Task<ApiResult<bool>> AssignSkillsByPetConfigAsync(int userPetId);

        /// <summary>
        /// 宠物进化
        /// </summary>
        /// <param name="userPetId">用户宠物ID</param>
        /// <returns>进化结果</returns>
        Task<ApiResult<bool>> EvolvePetAsync(int userPetId);

        /// <summary>
        /// 重置宠物属性
        /// </summary>
        /// <param name="userPetId">用户宠物ID</param>
        /// <returns>重置结果</returns>
        Task<ApiResult<bool>> ResetPetAttributesAsync(int userPetId);

        /// <summary>
        /// 复制用户宠物
        /// </summary>
        /// <param name="userPetId">要复制的用户宠物ID</param>
        /// <returns>复制结果</returns>
        Task<ApiResult<int>> CopyUserPetAsync(int userPetId);
    }
}