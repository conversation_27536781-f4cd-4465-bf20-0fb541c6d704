using System;
using System.Threading.Tasks;
using SqlSugar;

namespace BMS.Scripts
{
    /// <summary>
    /// 执行target_name字段迁移的脚本
    /// </summary>
    public class ExecuteTargetNameMigration
    {
        private static readonly string ConnectionString = "Server=**************;Database=kddata;User=app_user;Password=********;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;";

        public static async Task Main(string[] args)
        {
            try
            {
                var db = new SqlSugarClient(new ConnectionConfig()
                {
                    ConnectionString = ConnectionString,
                    DbType = DbType.MySql,
                    IsAutoCloseConnection = true
                });

                Console.WriteLine("开始执行数据库迁移...");

                // 检查target_name字段是否存在
                var targetNameExists = await CheckColumnExists(db, "task_objective", "target_name");
                if (!targetNameExists)
                {
                    Console.WriteLine("添加target_name字段...");
                    await db.Ado.ExecuteCommandAsync(@"
                        ALTER TABLE task_objective 
                        ADD COLUMN target_name VARCHAR(200) DEFAULT NULL COMMENT '目标名称' 
                        AFTER target_id");
                    Console.WriteLine("target_name字段添加成功");
                }
                else
                {
                    Console.WriteLine("target_name字段已存在");
                }

                // 检查complete_template字段是否存在
                var completeTemplateExists = await CheckColumnExists(db, "task_objective", "complete_template");
                if (!completeTemplateExists)
                {
                    Console.WriteLine("添加complete_template字段...");
                    await db.Ado.ExecuteCommandAsync(@"
                        ALTER TABLE task_objective 
                        ADD COLUMN complete_template VARCHAR(100) DEFAULT NULL COMMENT '进度模版' 
                        AFTER objective_description");
                    Console.WriteLine("complete_template字段添加成功");
                }
                else
                {
                    Console.WriteLine("complete_template字段已存在");
                }

                Console.WriteLine("数据库迁移完成！");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"迁移失败: {ex.Message}");
                Console.WriteLine($"详细错误: {ex}");
            }
        }

        private static async Task<bool> CheckColumnExists(SqlSugarClient db, string tableName, string columnName)
        {
            var sql = @"
                SELECT COUNT(*) 
                FROM information_schema.columns 
                WHERE table_schema = DATABASE() 
                AND table_name = @tableName 
                AND column_name = @columnName";

            var count = await db.Ado.GetIntAsync(sql, new { tableName, columnName });
            return count > 0;
        }
    }
}
