using System.ComponentModel.DataAnnotations;
using BMS.Models.Common;

namespace BMS.Models.DTOs
{
    /// <summary>
    /// 创建道具配置DTO
    /// </summary>
    public class ItemConfigCreateDto
    {
        /// <summary>
        /// 道具编号
        /// </summary>
        [Required(ErrorMessage = "道具编号不能为空")]
        [Range(1, int.MaxValue, ErrorMessage = "道具编号必须大于0")]
        public int ItemNo { get; set; }

        /// <summary>
        /// 道具名称
        /// </summary>
        [Required(ErrorMessage = "道具名称不能为空")]
        [StringLength(100, ErrorMessage = "道具名称长度不能超过100个字符")]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 道具类型
        /// </summary>
        [StringLength(50, ErrorMessage = "道具类型长度不能超过50个字符")]
        public string? Type { get; set; }

        /// <summary>
        /// 道具品质
        /// </summary>
        [StringLength(50, ErrorMessage = "道具品质长度不能超过50个字符")]
        public string? Quality { get; set; }

        /// <summary>
        /// 道具价格
        /// </summary>
        [Range(0, int.MaxValue, ErrorMessage = "道具价格不能为负数")]
        public int? Price { get; set; }

        /// <summary>
        /// 道具图标
        /// </summary>
        [StringLength(200, ErrorMessage = "道具图标路径长度不能超过200个字符")]
        public string? Icon { get; set; }

        /// <summary>
        /// 使用限制
        /// </summary>
        [StringLength(100, ErrorMessage = "使用限制长度不能超过100个字符")]
        public string? UseLimit { get; set; }

        /// <summary>
        /// 道具描述
        /// </summary>
        [StringLength(500, ErrorMessage = "道具描述长度不能超过500个字符")]
        public string? Description { get; set; }

        /// <summary>
        /// 扩展信息（JSON格式）
        /// </summary>
        public object? Extra { get; set; }

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsActive { get; set; } = true;
    }

    /// <summary>
    /// 更新道具配置DTO
    /// </summary>
    public class ItemConfigUpdateDto
    {
        /// <summary>
        /// ID
        /// </summary>
        [Required(ErrorMessage = "ID不能为空")]
        public int Id { get; set; }

        /// <summary>
        /// 道具编号
        /// </summary>
        [Required(ErrorMessage = "道具编号不能为空")]
        [Range(1, int.MaxValue, ErrorMessage = "道具编号必须大于0")]
        public int ItemNo { get; set; }

        /// <summary>
        /// 道具名称
        /// </summary>
        [Required(ErrorMessage = "道具名称不能为空")]
        [StringLength(100, ErrorMessage = "道具名称长度不能超过100个字符")]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 道具类型
        /// </summary>
        [StringLength(50, ErrorMessage = "道具类型长度不能超过50个字符")]
        public string? Type { get; set; }

        /// <summary>
        /// 道具品质
        /// </summary>
        [StringLength(50, ErrorMessage = "道具品质长度不能超过50个字符")]
        public string? Quality { get; set; }

        /// <summary>
        /// 道具价格
        /// </summary>
        [Range(0, int.MaxValue, ErrorMessage = "道具价格不能为负数")]
        public int? Price { get; set; }

        /// <summary>
        /// 道具图标
        /// </summary>
        [StringLength(200, ErrorMessage = "道具图标路径长度不能超过200个字符")]
        public string? Icon { get; set; }

        /// <summary>
        /// 使用限制
        /// </summary>
        [StringLength(100, ErrorMessage = "使用限制长度不能超过100个字符")]
        public string? UseLimit { get; set; }

        /// <summary>
        /// 道具描述
        /// </summary>
        [StringLength(500, ErrorMessage = "道具描述长度不能超过500个字符")]
        public string? Description { get; set; }

        /// <summary>
        /// 扩展信息（JSON格式）
        /// </summary>
        public object? Extra { get; set; }

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsActive { get; set; } = true;
    }

    /// <summary>
    /// 道具配置查询DTO
    /// </summary>
    public class ItemConfigQueryDto : PagedQueryDto
    {
        /// <summary>
        /// 道具编号
        /// </summary>
        public int? ItemNo { get; set; }

        /// <summary>
        /// 道具名称
        /// </summary>
        public string? Name { get; set; }

        /// <summary>
        /// 道具类型
        /// </summary>
        public string? Type { get; set; }

        /// <summary>
        /// 道具品质
        /// </summary>
        public string? Quality { get; set; }

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool? IsActive { get; set; }
    }

    /// <summary>
    /// 删除道具配置DTO
    /// </summary>
    public class ItemConfigDeleteDto
    {
        /// <summary>
        /// ID
        /// </summary>
        [Required(ErrorMessage = "ID不能为空")]
        public int Id { get; set; }
    }

    /// <summary>
    /// 道具脚本DTO
    /// </summary>
    public class ItemScriptDto
    {
        /// <summary>
        /// ID
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 道具编号
        /// </summary>
        public int ItemNo { get; set; }

        /// <summary>
        /// 脚本内容
        /// </summary>
        public string? Script { get; set; }

        /// <summary>
        /// 脚本说明
        /// </summary>
        public string? Description { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime? CreateTime { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime? UpdateTime { get; set; }
    }

    /// <summary>
    /// 道具脚本新增/更新DTO
    /// </summary>
    public class ItemScriptUpsertDto
    {
        /// <summary>
        /// ID（新增时为null）
        /// </summary>
        public int? Id { get; set; }

        /// <summary>
        /// 道具编号
        /// </summary>
        [Required(ErrorMessage = "道具编号不能为空")]
        public int ItemNo { get; set; }

        /// <summary>
        /// 脚本内容
        /// </summary>
        public string? Script { get; set; }

        /// <summary>
        /// 脚本说明
        /// </summary>
        [StringLength(200, ErrorMessage = "脚本说明长度不能超过200个字符")]
        public string? Description { get; set; }
    }

    /// <summary>
    /// 删除道具脚本DTO
    /// </summary>
    public class ItemScriptDeleteDto
    {
        /// <summary>
        /// ID
        /// </summary>
        [Required(ErrorMessage = "ID不能为空")]
        public int Id { get; set; }
    }

    /// <summary>
    /// 道具配置和脚本关联DTO
    /// </summary>
    public class ItemConfigWithScriptDto
    {
        /// <summary>
        /// 道具配置ID
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 道具编号
        /// </summary>
        public int ItemNo { get; set; }

        /// <summary>
        /// 道具名称
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 道具类型
        /// </summary>
        public string? Type { get; set; }

        /// <summary>
        /// 道具描述
        /// </summary>
        public string? Description { get; set; }

        /// <summary>
        /// 道具品质
        /// </summary>
        public string? Quality { get; set; }

        /// <summary>
        /// 道具图标
        /// </summary>
        public string? Icon { get; set; }

        /// <summary>
        /// 道具价格
        /// </summary>
        public int? Price { get; set; }

        /// <summary>
        /// 使用限制
        /// </summary>
        public string? UseLimit { get; set; }

        /// <summary>
        /// 扩展字段
        /// </summary>
        public object? Extra { get; set; }

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime? CreateTime { get; set; }

        /// <summary>
        /// 道具脚本信息
        /// </summary>
        public ItemScriptDto? Script { get; set; }
    }

    /// <summary>
    /// 道具配置和脚本关联查询DTO
    /// </summary>
    public class ItemConfigWithScriptQueryDto : PagedQueryDto
    {
        /// <summary>
        /// 道具编号
        /// </summary>
        public int? ItemNo { get; set; }

        /// <summary>
        /// 道具名称
        /// </summary>
        public string? Name { get; set; }

        /// <summary>
        /// 道具类型
        /// </summary>
        public string? Type { get; set; }

        /// <summary>
        /// 道具品质
        /// </summary>
        public string? Quality { get; set; }

        /// <summary>
        /// 脚本内容
        /// </summary>
        public string? ScriptContent { get; set; }

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool? IsActive { get; set; }
    }

    /// <summary>
    /// 道具配置和脚本关联创建DTO
    /// </summary>
    public class ItemConfigWithScriptCreateDto
    {
        /// <summary>
        /// 道具配置信息
        /// </summary>
        [Required(ErrorMessage = "道具配置信息不能为空")]
        public ItemConfigCreateDto Config { get; set; } = new();

        /// <summary>
        /// 道具脚本信息
        /// </summary>
        public ItemScriptUpsertDto? Script { get; set; }
    }

    /// <summary>
    /// 道具配置和脚本关联更新DTO
    /// </summary>
    public class ItemConfigWithScriptUpdateDto
    {
        /// <summary>
        /// 道具配置信息
        /// </summary>
        [Required(ErrorMessage = "道具配置信息不能为空")]
        public ItemConfigUpdateDto Config { get; set; } = new();

        /// <summary>
        /// 道具脚本信息
        /// </summary>
        public ItemScriptUpsertDto? Script { get; set; }
    }

    /// <summary>
    /// 批量更新状态DTO
    /// </summary>
    public class ItemConfigBatchUpdateStatusDto
    {
        /// <summary>
        /// 道具ID列表
        /// </summary>
        [Required(ErrorMessage = "道具ID列表不能为空")]
        public List<int> Ids { get; set; } = new();

        /// <summary>
        /// 是否启用
        /// </summary>
        [Required(ErrorMessage = "启用状态不能为空")]
        public bool IsActive { get; set; }
    }
}