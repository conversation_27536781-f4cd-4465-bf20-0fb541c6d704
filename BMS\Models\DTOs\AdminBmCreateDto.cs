using System.ComponentModel.DataAnnotations;

namespace BMS.Models.DTOs
{
    /// <summary>
    /// 创建管理员DTO
    /// </summary>
    public class AdminBmCreateDto
    {
        /// <summary>
        /// 用户名
        /// </summary>
        [Required(ErrorMessage = "用户名不能为空")]
        [StringLength(50, ErrorMessage = "用户名长度不能超过50个字符")]
        public string Username { get; set; } = string.Empty;

        /// <summary>
        /// 密码
        /// </summary>
        [Required(ErrorMessage = "密码不能为空")]
        [StringLength(100, MinimumLength = 6, ErrorMessage = "密码长度必须在6-100个字符之间")]
        public string Password { get; set; } = string.Empty;

        /// <summary>
        /// 确认密码
        /// </summary>
        [Required(ErrorMessage = "确认密码不能为空")]
        [Compare("Password", ErrorMessage = "两次输入的密码不一致")]
        public string ConfirmPassword { get; set; } = string.Empty;

        /// <summary>
        /// 真实姓名
        /// </summary>
        [StringLength(50, ErrorMessage = "真实姓名长度不能超过50个字符")]
        public string? RealName { get; set; }

        /// <summary>
        /// 手机号
        /// </summary>
        [StringLength(20, ErrorMessage = "手机号长度不能超过20个字符")]
        public string? Phone { get; set; }

        /// <summary>
        /// 邮箱
        /// </summary>
        [StringLength(50, ErrorMessage = "邮箱长度不能超过50个字符")]
        public string? Email { get; set; }

        /// <summary>
        /// 状态：0-禁用，1-启用
        /// </summary>
        public int Status { get; set; } = 1;
    }
} 