using System.ComponentModel.DataAnnotations;

namespace BMS.Models.DTOs
{
    /// <summary>
    /// 技能信息DTO
    /// </summary>
    public class SkillDto
    {
        /// <summary>
        /// 技能唯一标识
        /// </summary>
        public string SkillId { get; set; } = string.Empty;

        /// <summary>
        /// 技能名称
        /// </summary>
        public string SkillName { get; set; } = string.Empty;

        /// <summary>
        /// 技能百分比
        /// </summary>
        public decimal SkillPercent { get; set; }

        /// <summary>
        /// 技能效果类型
        /// </summary>
        public string? EffectType { get; set; }

        /// <summary>
        /// 效果数值（JSON格式）
        /// </summary>
        public string? EffectValue { get; set; }

        /// <summary>
        /// 耗蓝量
        /// </summary>
        public int ManaCost { get; set; }

        /// <summary>
        /// BUFF信息（JSON格式）
        /// </summary>
        public string? BuffInfo { get; set; }

        /// <summary>
        /// 五行限制
        /// </summary>
        public string? ElementLimit { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateTime { get; set; }
    }
} 