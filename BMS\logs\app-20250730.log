[2025-07-30 09:36:38.625 +08:00 INF] BMS: 🚀 应用程序启动，日志系统已配置
[2025-07-30 09:36:38.652 +08:00 DBG] BMS: 🔧 调试级别日志测试
[2025-07-30 09:36:38.668 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 09:36:38.670 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 09:36:38.755 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 09:36:39.404 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 09:36:39.441 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 09:36:39.480 +08:00 INF] BMS: ✅ DbContext 初始化成功，日志文件位置: logs/
[2025-07-30 09:36:41.402 +08:00 WRN] Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware: Failed to determine the https port for redirect.
[2025-07-30 09:36:46.382 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 09:36:46.384 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 09:36:46.392 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 09:36:46.434 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 09:36:46.436 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 09:36:46.491 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  DISTINCT `type` FROM `item_config`  WHERE NOT( `type` IS NULL   OR `type`='') 
[2025-07-30 09:36:46.553 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  DISTINCT `type` FROM `item_config`  WHERE NOT( `type` IS NULL   OR `type`='') 
[2025-07-30 09:36:46.582 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  DISTINCT `quality` FROM `item_config`  WHERE NOT( `quality` IS NULL   OR `quality`='') 
[2025-07-30 09:36:46.620 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  DISTINCT `quality` FROM `item_config`  WHERE NOT( `quality` IS NULL   OR `quality`='') 
[2025-07-30 09:37:35.711 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 09:37:35.713 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 09:37:35.715 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 09:37:35.769 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 09:37:35.771 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 09:37:35.773 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  DISTINCT `type` FROM `item_config`  WHERE NOT( `type` IS NULL   OR `type`='') 
[2025-07-30 09:37:35.821 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  DISTINCT `type` FROM `item_config`  WHERE NOT( `type` IS NULL   OR `type`='') 
[2025-07-30 09:37:35.824 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  DISTINCT `quality` FROM `item_config`  WHERE NOT( `quality` IS NULL   OR `quality`='') 
[2025-07-30 09:37:35.868 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  DISTINCT `quality` FROM `item_config`  WHERE NOT( `quality` IS NULL   OR `quality`='') 
[2025-07-30 11:06:49.323 +08:00 INF] BMS: 🚀 应用程序启动，日志系统已配置
[2025-07-30 11:06:49.351 +08:00 DBG] BMS: 🔧 调试级别日志测试
[2025-07-30 11:06:49.369 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 11:06:49.371 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 11:06:49.472 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 11:06:49.974 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 11:06:50.000 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 11:06:50.010 +08:00 INF] BMS: ✅ DbContext 初始化成功，日志文件位置: logs/
[2025-07-30 11:06:51.139 +08:00 WRN] Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware: Failed to determine the https port for redirect.
[2025-07-30 11:07:11.558 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 11:07:11.561 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 11:07:11.567 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 11:07:11.613 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 11:07:11.615 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 11:07:11.706 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  DISTINCT `type` FROM `item_config`  WHERE NOT( `type` IS NULL   OR `type`='') 
[2025-07-30 11:07:11.755 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  DISTINCT `type` FROM `item_config`  WHERE NOT( `type` IS NULL   OR `type`='') 
[2025-07-30 11:07:11.795 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  DISTINCT `quality` FROM `item_config`  WHERE NOT( `quality` IS NULL   OR `quality`='') 
[2025-07-30 11:07:11.845 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  DISTINCT `quality` FROM `item_config`  WHERE NOT( `quality` IS NULL   OR `quality`='') 
[2025-07-30 11:07:11.979 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 11:07:11.980 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 11:07:11.983 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 11:07:12.029 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 11:07:12.056 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 11:07:12.456 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-07-30 11:07:12.702 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-07-30 11:07:12.710 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 0,10
[2025-07-30 11:07:13.485 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 0,10
[2025-07-30 11:07:24.497 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 11:07:24.503 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 11:07:24.508 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 11:07:24.561 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 11:07:24.563 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 11:07:24.596 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=884]
[2025-07-30 11:07:24.740 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-07-30 11:07:24.841 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1 | 参数: [@item_no0=2]
[2025-07-30 11:07:24.912 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1
[2025-07-30 11:07:30.914 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 11:07:30.970 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 11:07:30.977 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 11:07:31.017 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 11:07:31.025 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 11:07:31.027 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=884]
[2025-07-30 11:07:31.070 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-07-30 11:07:31.083 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1 | 参数: [@item_no0=2]
[2025-07-30 11:07:31.125 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1
[2025-07-30 11:07:46.379 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 11:07:46.385 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 11:07:46.398 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 11:07:46.449 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 11:07:46.452 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 11:07:46.454 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=884]
[2025-07-30 11:07:46.497 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-07-30 11:07:46.499 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1 | 参数: [@item_no0=2]
[2025-07-30 11:07:46.552 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1
[2025-07-30 11:18:53.607 +08:00 INF] BMS: 🚀 应用程序启动，日志系统已配置
[2025-07-30 11:18:53.639 +08:00 DBG] BMS: 🔧 调试级别日志测试
[2025-07-30 11:18:53.669 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 11:18:53.687 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 11:18:53.778 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 11:18:54.287 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 11:18:54.297 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 11:18:54.299 +08:00 INF] BMS: ✅ DbContext 初始化成功，日志文件位置: logs/
[2025-07-30 11:18:55.468 +08:00 WRN] Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware: Failed to determine the https port for redirect.
[2025-07-30 11:20:00.329 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 11:20:00.404 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 11:20:00.421 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 11:20:00.574 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 11:20:00.579 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 11:20:00.658 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  DISTINCT `type` FROM `item_config`  WHERE NOT( `type` IS NULL   OR `type`='') 
[2025-07-30 11:20:00.722 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  DISTINCT `type` FROM `item_config`  WHERE NOT( `type` IS NULL   OR `type`='') 
[2025-07-30 11:20:00.761 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  DISTINCT `quality` FROM `item_config`  WHERE NOT( `quality` IS NULL   OR `quality`='') 
[2025-07-30 11:20:00.825 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  DISTINCT `quality` FROM `item_config`  WHERE NOT( `quality` IS NULL   OR `quality`='') 
[2025-07-30 11:20:01.013 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 11:20:01.016 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 11:20:01.020 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 11:20:01.090 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 11:20:01.092 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 11:20:01.299 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-07-30 11:20:01.512 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-07-30 11:20:01.516 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 0,10
[2025-07-30 11:20:02.268 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 0,10
[2025-07-30 11:20:07.769 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 11:20:07.771 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 11:20:07.773 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 11:20:07.816 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 11:20:07.818 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 11:20:16.418 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 11:20:16.424 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 11:20:16.428 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 11:20:16.471 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 11:20:16.473 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 11:20:16.475 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  DISTINCT `type` FROM `item_config`  WHERE NOT( `type` IS NULL   OR `type`='') 
[2025-07-30 11:20:16.513 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  DISTINCT `type` FROM `item_config`  WHERE NOT( `type` IS NULL   OR `type`='') 
[2025-07-30 11:20:16.515 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  DISTINCT `quality` FROM `item_config`  WHERE NOT( `quality` IS NULL   OR `quality`='') 
[2025-07-30 11:20:16.556 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  DISTINCT `quality` FROM `item_config`  WHERE NOT( `quality` IS NULL   OR `quality`='') 
[2025-07-30 11:20:16.683 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 11:20:16.684 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 11:20:16.686 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 11:20:16.732 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 11:20:16.733 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 11:20:16.738 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-07-30 11:20:16.964 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-07-30 11:20:16.977 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 0,10
[2025-07-30 11:20:17.791 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 0,10
[2025-07-30 11:20:29.095 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 11:20:29.098 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 11:20:29.100 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 11:20:29.196 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 11:20:29.223 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 11:20:29.244 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=9601]
[2025-07-30 11:20:29.326 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-07-30 11:20:41.852 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 11:20:41.856 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 11:20:41.863 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 11:20:41.904 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 11:20:41.914 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 11:20:41.921 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=848]
[2025-07-30 11:20:41.976 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-07-30 11:20:41.986 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1 | 参数: [@item_no0=9601]
[2025-07-30 11:20:42.040 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1
[2025-07-30 11:21:45.869 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 11:21:45.870 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 11:21:45.873 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 11:21:45.924 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 11:21:45.926 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 11:21:45.928 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=884]
[2025-07-30 11:21:45.965 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-07-30 11:21:45.969 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1 | 参数: [@item_no0=2]
[2025-07-30 11:21:46.011 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1
[2025-07-30 11:21:53.011 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 11:21:53.013 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 11:21:53.014 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 11:21:53.074 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 11:21:53.075 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 11:21:53.077 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=884]
[2025-07-30 11:21:53.116 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-07-30 11:21:53.122 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1 | 参数: [@item_no0=2]
[2025-07-30 11:21:53.163 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1
[2025-07-30 11:21:54.703 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 11:21:54.704 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 11:21:54.710 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 11:21:54.760 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 11:21:54.763 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 11:21:54.773 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=884]
[2025-07-30 11:21:54.815 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-07-30 11:21:54.818 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1 | 参数: [@item_no0=2]
[2025-07-30 11:21:54.859 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1
[2025-07-30 11:47:01.913 +08:00 INF] BMS: 🚀 应用程序启动，日志系统已配置
[2025-07-30 11:47:01.940 +08:00 DBG] BMS: 🔧 调试级别日志测试
[2025-07-30 11:47:01.956 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 11:47:01.959 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 11:47:02.053 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 11:47:02.562 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 11:47:02.574 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 11:47:02.581 +08:00 INF] BMS: ✅ DbContext 初始化成功，日志文件位置: logs/
[2025-07-30 11:47:03.403 +08:00 WRN] Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware: Failed to determine the https port for redirect.
[2025-07-30 11:47:12.712 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 11:47:12.739 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 11:47:12.764 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 11:47:12.885 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 11:47:12.888 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 11:47:12.939 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  DISTINCT `type` FROM `item_config`  WHERE NOT( `type` IS NULL   OR `type`='') 
[2025-07-30 11:47:13.025 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  DISTINCT `type` FROM `item_config`  WHERE NOT( `type` IS NULL   OR `type`='') 
[2025-07-30 11:47:13.049 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  DISTINCT `quality` FROM `item_config`  WHERE NOT( `quality` IS NULL   OR `quality`='') 
[2025-07-30 11:47:13.092 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  DISTINCT `quality` FROM `item_config`  WHERE NOT( `quality` IS NULL   OR `quality`='') 
[2025-07-30 11:47:13.248 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 11:47:13.250 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 11:47:13.252 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 11:47:13.294 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 11:47:13.295 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 11:47:13.413 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-07-30 11:47:13.627 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-07-30 11:47:13.630 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 0,10
[2025-07-30 11:47:14.388 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 0,10
[2025-07-30 11:47:15.967 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 11:47:15.969 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 11:47:15.970 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 11:47:16.012 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 11:47:16.013 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 11:47:16.025 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=884]
[2025-07-30 11:47:16.091 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-07-30 11:47:16.108 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1 | 参数: [@item_no0=2]
[2025-07-30 11:47:16.151 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1
[2025-07-30 11:47:28.609 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 11:47:28.610 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 11:47:28.613 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 11:47:28.656 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 11:47:28.658 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 11:47:28.660 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=848]
[2025-07-30 11:47:28.700 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-07-30 11:47:28.703 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1 | 参数: [@item_no0=9601]
[2025-07-30 11:47:28.748 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1
[2025-07-30 11:47:43.921 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 11:47:43.923 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 11:47:43.926 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 11:47:43.970 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 11:47:43.970 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 11:47:43.972 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=1280]
[2025-07-30 11:47:44.013 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-07-30 11:47:44.016 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1 | 参数: [@item_no0=10002]
[2025-07-30 11:47:44.057 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1
[2025-07-30 11:48:23.956 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 11:48:23.958 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 11:48:23.961 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 11:48:24.000 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 11:48:24.002 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 11:48:24.004 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=884]
[2025-07-30 11:48:24.046 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-07-30 11:48:24.049 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1 | 参数: [@item_no0=2]
[2025-07-30 11:48:24.091 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1
[2025-07-30 11:49:04.107 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 11:49:04.110 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 11:49:04.112 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 11:49:04.163 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 11:49:04.165 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 11:49:04.167 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=884]
[2025-07-30 11:49:04.258 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-07-30 11:49:04.268 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1 | 参数: [@item_no0=2]
[2025-07-30 11:49:04.317 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1
[2025-07-30 11:52:22.546 +08:00 INF] BMS: 🚀 应用程序启动，日志系统已配置
[2025-07-30 11:52:22.575 +08:00 DBG] BMS: 🔧 调试级别日志测试
[2025-07-30 11:52:22.595 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 11:52:22.605 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 11:52:22.707 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 11:52:23.242 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 11:52:23.254 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 11:52:23.261 +08:00 INF] BMS: ✅ DbContext 初始化成功，日志文件位置: logs/
[2025-07-30 11:52:24.490 +08:00 WRN] Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware: Failed to determine the https port for redirect.
[2025-07-30 11:52:27.422 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 11:52:27.429 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 11:52:27.439 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 11:52:27.498 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 11:52:27.506 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 11:52:27.561 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  DISTINCT `type` FROM `item_config`  WHERE NOT( `type` IS NULL   OR `type`='') 
[2025-07-30 11:52:27.609 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  DISTINCT `type` FROM `item_config`  WHERE NOT( `type` IS NULL   OR `type`='') 
[2025-07-30 11:52:27.633 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  DISTINCT `quality` FROM `item_config`  WHERE NOT( `quality` IS NULL   OR `quality`='') 
[2025-07-30 11:52:27.679 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  DISTINCT `quality` FROM `item_config`  WHERE NOT( `quality` IS NULL   OR `quality`='') 
[2025-07-30 11:52:27.852 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 11:52:27.854 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 11:52:27.857 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 11:52:27.899 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 11:52:27.901 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 11:52:28.023 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-07-30 11:52:28.252 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-07-30 11:52:28.255 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 0,10
[2025-07-30 11:52:29.033 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 0,10
[2025-07-30 11:52:33.187 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 11:52:33.190 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 11:52:33.193 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 11:52:33.237 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 11:52:33.239 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 11:52:33.261 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=1280]
[2025-07-30 11:52:33.316 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-07-30 11:52:33.336 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1 | 参数: [@item_no0=10002]
[2025-07-30 11:52:33.382 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1
[2025-07-30 11:53:43.339 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 11:53:43.341 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 11:53:43.343 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 11:53:43.398 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 11:53:43.404 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 11:53:43.407 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=1280]
[2025-07-30 11:53:43.454 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-07-30 11:53:43.458 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1 | 参数: [@item_no0=10002]
[2025-07-30 11:53:43.520 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1
[2025-07-30 11:56:47.295 +08:00 INF] BMS: 🚀 应用程序启动，日志系统已配置
[2025-07-30 11:56:47.325 +08:00 DBG] BMS: 🔧 调试级别日志测试
[2025-07-30 11:56:47.344 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 11:56:47.350 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 11:56:47.484 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 11:56:47.955 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 11:56:47.964 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 11:56:47.968 +08:00 INF] BMS: ✅ DbContext 初始化成功，日志文件位置: logs/
[2025-07-30 11:56:49.189 +08:00 WRN] Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware: Failed to determine the https port for redirect.
[2025-07-30 11:57:09.692 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 11:57:09.695 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 11:57:09.702 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 11:57:09.741 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 11:57:09.743 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 11:57:09.846 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  DISTINCT `type` FROM `item_config`  WHERE NOT( `type` IS NULL   OR `type`='') 
[2025-07-30 11:57:09.900 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  DISTINCT `type` FROM `item_config`  WHERE NOT( `type` IS NULL   OR `type`='') 
[2025-07-30 11:57:09.954 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  DISTINCT `quality` FROM `item_config`  WHERE NOT( `quality` IS NULL   OR `quality`='') 
[2025-07-30 11:57:09.995 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  DISTINCT `quality` FROM `item_config`  WHERE NOT( `quality` IS NULL   OR `quality`='') 
[2025-07-30 11:57:10.465 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 11:57:10.514 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 11:57:10.580 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 11:57:10.623 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 11:57:10.636 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 11:57:10.849 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-07-30 11:57:11.076 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-07-30 11:57:11.082 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 0,10
[2025-07-30 11:57:11.868 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 0,10
[2025-07-30 11:57:15.772 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 11:57:15.775 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 11:57:15.777 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 11:57:15.809 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 11:57:15.811 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 11:57:15.831 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=884]
[2025-07-30 11:57:15.880 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-07-30 11:57:15.920 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1 | 参数: [@item_no0=2]
[2025-07-30 11:57:15.961 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1
[2025-07-30 13:38:16.137 +08:00 INF] BMS: 🚀 应用程序启动，日志系统已配置
[2025-07-30 13:38:16.215 +08:00 DBG] BMS: 🔧 调试级别日志测试
[2025-07-30 13:38:16.234 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 13:38:16.241 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 13:38:16.327 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 13:38:16.806 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 13:38:16.816 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 13:38:16.823 +08:00 INF] BMS: ✅ DbContext 初始化成功，日志文件位置: logs/
[2025-07-30 13:38:17.810 +08:00 WRN] Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware: Failed to determine the https port for redirect.
[2025-07-30 13:38:21.373 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 13:38:21.376 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 13:38:21.383 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 13:38:21.426 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 13:38:21.433 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 13:38:21.485 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `equip_type_id` AS `EquipTypeId` , `type_name` AS `TypeName`  FROM `equipment_type`  WHERE ( `is_active` = @is_active0 )ORDER BY `sort_order` ASC,`id` ASC  | 参数: [@is_active0=True]
[2025-07-30 13:38:21.591 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `equip_type_id` AS `EquipTypeId` , `type_name` AS `TypeName`  FROM `equipment_type`  WHERE ( `is_active` = @is_active0 )ORDER BY `sort_order` ASC,`id` ASC 
[2025-07-30 13:38:21.790 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 13:38:21.792 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 13:38:21.794 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 13:38:21.828 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 13:38:21.830 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 13:38:21.950 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )   
[2025-07-30 13:38:22.004 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )   
[2025-07-30 13:38:22.023 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `e`.`equip_id` AS `EquipId` , `e`.`class_id` AS `ClassId` , `e`.`name` AS `Name` , `e`.`icon` AS `Icon` , `e`.`equip_type_id` AS `EquipTypeId` , `et`.`type_name` AS `EquipTypeName` , `e`.`element` AS `Element` , IFNULL(`e`.`slot`,@MethodConst13) AS `Slot` , IFNULL(`e`.`strengthen_level`,@MethodConst14) AS `StrengthenLevel` , `e`.`suit_id` AS `SuitId` , IFNULL(`ed`.`atk`,@MethodConst15) AS `Atk` , IFNULL(`ed`.`hit`,@MethodConst16) AS `Hit` , IFNULL(`ed`.`def`,@MethodConst17) AS `Def` , IFNULL(`ed`.`spd`,@MethodConst18) AS `Spd` , IFNULL(`ed`.`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`ed`.`hp`,@MethodConst20) AS `Hp` , IFNULL(`ed`.`mp`,@MethodConst21) AS `Mp` , IFNULL(`ed`.`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`ed`.`offset`,@MethodConst23) AS `Offset` , IFNULL(`ed`.`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`ed`.`vamp_mp`,@MethodConst25) AS `VampMp` , `ed`.`description` AS `Description` , `ed`.`main_attr` AS `MainAttr` , `ed`.`main_attr_value` AS `MainAttrValue` , `ed`.`sub_attr` AS `SubAttr` , `ed`.`sub_attr_value` AS `SubAttrValue` , `ed`.`element_limit` AS `ElementLimit` , `ed`.`equip_name` AS `EquipName`  FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )     ORDER BY `e`.`equip_id` ASC LIMIT 0,10 | 参数: [@MethodConst0=0, @MethodConst1=0, @MethodConst2=0, @MethodConst3=0, @MethodConst4=0, @MethodConst5=0, @MethodConst6=0, @MethodConst7=0, @MethodConst8=0, @MethodConst9=0, @MethodConst10=0, @MethodConst11=0, @MethodConst12=0, @MethodConst13=0, @MethodConst14=0, @MethodConst15=0, @MethodConst16=0, @MethodConst17=0, @MethodConst18=0, @MethodConst19=0, @MethodConst20=0, @MethodConst21=0, @MethodConst22=0, @MethodConst23=0, @MethodConst24=0, @MethodConst25=0]
[2025-07-30 13:38:22.067 +08:00 ERR] BMS.Sugar.DbContext: 数据库操作发生错误: Unknown column 'e.class_id' in 'field list'
SqlSugar.SqlSugarException: Unknown column 'e.class_id' in 'field list'
[2025-07-30 13:38:22.212 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 13:38:22.214 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 13:38:22.216 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 13:38:22.248 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 13:38:22.250 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 13:38:22.262 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment_type`  
[2025-07-30 13:38:22.297 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment_type`  
[2025-07-30 13:38:22.303 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `id` AS `Id` , `equip_type_id` AS `EquipTypeId` , `type_name` AS `TypeName` , `description` AS `Description` , IFNULL(`sort_order`,@MethodConst3) AS `SortOrder` , IFNULL(CAST(`is_active` as SIGNED),@MethodConst4) AS `IsActive` , `create_time` AS `CreateTime` , `update_time` AS `UpdateTime` , @constant5 AS `EquipmentCount`  FROM `equipment_type` `et`    ORDER BY `sort_order` ASC,`id` ASC LIMIT 0,10 | 参数: [@MethodConst0=0, @MethodConst1=True, @constant2=0, @MethodConst3=0, @MethodConst4=True, @constant5=0]
[2025-07-30 13:38:22.337 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `id` AS `Id` , `equip_type_id` AS `EquipTypeId` , `type_name` AS `TypeName` , `description` AS `Description` , IFNULL(`sort_order`,@MethodConst3) AS `SortOrder` , IFNULL(CAST(`is_active` as SIGNED),@MethodConst4) AS `IsActive` , `create_time` AS `CreateTime` , `update_time` AS `UpdateTime` , @constant5 AS `EquipmentCount`  FROM `equipment_type` `et`    ORDER BY `sort_order` ASC,`id` ASC LIMIT 0,10
[2025-07-30 13:38:22.350 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=1]
[2025-07-30 13:38:22.388 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-07-30 13:38:22.394 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=2]
[2025-07-30 13:38:22.426 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-07-30 13:38:22.429 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=3]
[2025-07-30 13:38:22.461 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-07-30 13:38:22.464 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=4]
[2025-07-30 13:38:22.497 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-07-30 13:38:26.666 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 13:38:26.668 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 13:38:26.672 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 13:38:26.709 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 13:38:26.712 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 13:38:26.730 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  DISTINCT `type` FROM `item_config`  WHERE NOT( `type` IS NULL   OR `type`='') 
[2025-07-30 13:38:26.769 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  DISTINCT `type` FROM `item_config`  WHERE NOT( `type` IS NULL   OR `type`='') 
[2025-07-30 13:38:26.776 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  DISTINCT `quality` FROM `item_config`  WHERE NOT( `quality` IS NULL   OR `quality`='') 
[2025-07-30 13:38:26.812 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  DISTINCT `quality` FROM `item_config`  WHERE NOT( `quality` IS NULL   OR `quality`='') 
[2025-07-30 13:38:26.897 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 13:38:26.899 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 13:38:26.902 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 13:38:26.938 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 13:38:26.940 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 13:38:26.990 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-07-30 13:38:27.201 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-07-30 13:38:27.206 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 0,10
[2025-07-30 13:38:27.916 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 0,10
[2025-07-30 13:38:29.784 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 13:38:29.786 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 13:38:29.788 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 13:38:29.820 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 13:38:29.821 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 13:38:29.835 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=884]
[2025-07-30 13:38:29.870 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-07-30 13:38:29.878 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1 | 参数: [@item_no0=2]
[2025-07-30 13:38:29.912 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1
[2025-07-30 13:38:45.464 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 13:38:45.465 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 13:38:45.468 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 13:38:45.501 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 13:38:45.502 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 13:38:45.504 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=32]
[2025-07-30 13:38:45.541 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-07-30 13:38:45.544 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1 | 参数: [@item_no0=20]
[2025-07-30 13:38:45.578 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1
[2025-07-30 13:41:55.652 +08:00 INF] BMS: 🚀 应用程序启动，日志系统已配置
[2025-07-30 13:41:55.679 +08:00 DBG] BMS: 🔧 调试级别日志测试
[2025-07-30 13:41:55.699 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 13:41:55.705 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 13:41:55.795 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 13:41:56.215 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 13:41:56.231 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 13:41:56.233 +08:00 INF] BMS: ✅ DbContext 初始化成功，日志文件位置: logs/
[2025-07-30 13:41:57.380 +08:00 WRN] Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware: Failed to determine the https port for redirect.
[2025-07-30 13:42:01.672 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 13:42:01.674 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 13:42:01.681 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 13:42:01.713 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 13:42:01.715 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 13:42:01.775 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  DISTINCT `type` FROM `item_config`  WHERE NOT( `type` IS NULL   OR `type`='') 
[2025-07-30 13:42:01.811 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  DISTINCT `type` FROM `item_config`  WHERE NOT( `type` IS NULL   OR `type`='') 
[2025-07-30 13:42:01.838 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  DISTINCT `quality` FROM `item_config`  WHERE NOT( `quality` IS NULL   OR `quality`='') 
[2025-07-30 13:42:01.872 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  DISTINCT `quality` FROM `item_config`  WHERE NOT( `quality` IS NULL   OR `quality`='') 
[2025-07-30 13:42:02.061 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 13:42:02.065 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 13:42:02.067 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 13:42:02.099 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 13:42:02.101 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 13:42:02.227 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-07-30 13:42:02.475 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-07-30 13:42:02.519 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 0,10
[2025-07-30 13:42:03.390 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 0,10
[2025-07-30 13:42:04.975 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 13:42:04.977 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 13:42:04.980 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 13:42:05.015 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 13:42:05.017 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 13:42:05.039 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=32]
[2025-07-30 13:42:05.079 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-07-30 13:42:05.131 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1 | 参数: [@item_no0=20]
[2025-07-30 13:42:05.176 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1
[2025-07-30 13:44:16.739 +08:00 INF] BMS: 🚀 应用程序启动，日志系统已配置
[2025-07-30 13:44:16.766 +08:00 DBG] BMS: 🔧 调试级别日志测试
[2025-07-30 13:44:16.782 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 13:44:16.784 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 13:44:16.885 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 13:44:17.427 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 13:44:17.437 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 13:44:17.440 +08:00 INF] BMS: ✅ DbContext 初始化成功，日志文件位置: logs/
[2025-07-30 13:44:18.585 +08:00 WRN] Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware: Failed to determine the https port for redirect.
[2025-07-30 13:44:23.476 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 13:44:23.495 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 13:44:23.522 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 13:44:23.610 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 13:44:23.611 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 13:44:23.664 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  DISTINCT `type` FROM `item_config`  WHERE NOT( `type` IS NULL   OR `type`='') 
[2025-07-30 13:44:23.711 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  DISTINCT `type` FROM `item_config`  WHERE NOT( `type` IS NULL   OR `type`='') 
[2025-07-30 13:44:23.734 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  DISTINCT `quality` FROM `item_config`  WHERE NOT( `quality` IS NULL   OR `quality`='') 
[2025-07-30 13:44:23.775 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  DISTINCT `quality` FROM `item_config`  WHERE NOT( `quality` IS NULL   OR `quality`='') 
[2025-07-30 13:44:24.022 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 13:44:24.024 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 13:44:24.025 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 13:44:24.069 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 13:44:24.072 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 13:44:24.202 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-07-30 13:44:24.416 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-07-30 13:44:24.419 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 0,10
[2025-07-30 13:44:25.147 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 0,10
[2025-07-30 13:44:26.453 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 13:44:26.455 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 13:44:26.458 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 13:44:26.498 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 13:44:26.501 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 13:44:26.515 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=32]
[2025-07-30 13:44:26.559 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-07-30 13:44:26.580 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1 | 参数: [@item_no0=20]
[2025-07-30 13:44:26.620 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1
[2025-07-30 13:55:13.194 +08:00 INF] BMS: 🚀 应用程序启动，日志系统已配置
[2025-07-30 13:55:13.221 +08:00 DBG] BMS: 🔧 调试级别日志测试
[2025-07-30 13:55:13.236 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 13:55:13.238 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 13:55:13.335 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 13:55:13.840 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 13:55:13.906 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 13:55:13.943 +08:00 INF] BMS: ✅ DbContext 初始化成功，日志文件位置: logs/
[2025-07-30 13:55:15.147 +08:00 WRN] Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware: Failed to determine the https port for redirect.
[2025-07-30 13:56:35.978 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 13:56:35.980 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 13:56:35.983 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 13:56:36.027 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 13:56:36.029 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 13:56:36.078 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  DISTINCT `type` FROM `item_config`  WHERE NOT( `type` IS NULL   OR `type`='') 
[2025-07-30 13:56:36.126 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  DISTINCT `type` FROM `item_config`  WHERE NOT( `type` IS NULL   OR `type`='') 
[2025-07-30 13:56:36.148 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  DISTINCT `quality` FROM `item_config`  WHERE NOT( `quality` IS NULL   OR `quality`='') 
[2025-07-30 13:56:36.192 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  DISTINCT `quality` FROM `item_config`  WHERE NOT( `quality` IS NULL   OR `quality`='') 
[2025-07-30 13:56:36.340 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 13:56:36.342 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 13:56:36.343 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 13:56:36.394 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 13:56:36.430 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 13:56:36.542 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-07-30 13:56:36.782 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-07-30 13:56:36.788 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 0,10
[2025-07-30 13:56:37.603 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 0,10
[2025-07-30 13:56:41.060 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 13:56:41.062 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 13:56:41.064 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 13:56:41.107 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 13:56:41.110 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 13:56:41.121 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=1280]
[2025-07-30 13:56:41.175 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-07-30 13:56:41.193 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1 | 参数: [@item_no0=10002]
[2025-07-30 13:56:41.316 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1
[2025-07-30 13:56:47.174 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 13:56:47.176 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 13:56:47.178 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 13:56:47.224 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 13:56:47.229 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 13:56:47.234 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=62]
[2025-07-30 13:56:47.276 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-07-30 13:56:47.280 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1 | 参数: [@item_no0=10000]
[2025-07-30 13:56:47.327 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1
[2025-07-30 14:28:24.523 +08:00 INF] BMS: 🚀 应用程序启动，日志系统已配置
[2025-07-30 14:28:24.558 +08:00 DBG] BMS: 🔧 调试级别日志测试
[2025-07-30 14:28:24.577 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 14:28:24.581 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 14:28:24.665 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 14:28:25.087 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 14:28:25.095 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 14:28:25.115 +08:00 INF] BMS: ✅ DbContext 初始化成功，日志文件位置: logs/
[2025-07-30 14:28:26.359 +08:00 WRN] Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware: Failed to determine the https port for redirect.
[2025-07-30 14:28:29.975 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 14:28:29.977 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 14:28:29.981 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 14:28:30.020 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 14:28:30.029 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 14:28:30.077 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  DISTINCT `type` FROM `item_config`  WHERE NOT( `type` IS NULL   OR `type`='') 
[2025-07-30 14:28:30.114 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  DISTINCT `type` FROM `item_config`  WHERE NOT( `type` IS NULL   OR `type`='') 
[2025-07-30 14:28:30.136 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  DISTINCT `quality` FROM `item_config`  WHERE NOT( `quality` IS NULL   OR `quality`='') 
[2025-07-30 14:28:30.166 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  DISTINCT `quality` FROM `item_config`  WHERE NOT( `quality` IS NULL   OR `quality`='') 
[2025-07-30 14:28:30.282 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 14:28:30.283 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 14:28:30.285 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 14:28:30.322 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 14:28:30.324 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 14:28:30.445 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-07-30 14:28:30.642 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-07-30 14:28:30.655 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 0,10
[2025-07-30 14:28:31.354 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 0,10
[2025-07-30 14:28:34.467 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 14:28:34.469 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 14:28:34.470 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 14:28:34.498 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 14:28:34.501 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 14:28:34.512 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=1280]
[2025-07-30 14:28:34.547 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-07-30 14:28:34.565 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1 | 参数: [@item_no0=10002]
[2025-07-30 14:28:34.598 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1
[2025-07-30 14:28:47.103 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 14:28:47.105 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 14:28:47.106 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 14:28:47.135 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 14:28:47.136 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 14:28:47.138 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=1280]
[2025-07-30 14:28:47.176 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-07-30 14:28:47.179 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1 | 参数: [@item_no0=10002]
[2025-07-30 14:28:47.210 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1
[2025-07-30 14:28:57.286 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 14:28:57.289 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 14:28:57.290 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 14:28:57.319 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 14:28:57.322 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 14:28:57.324 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=755]
[2025-07-30 14:28:57.356 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-07-30 14:28:57.358 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1 | 参数: [@item_no0=45451]
[2025-07-30 14:28:57.387 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1
[2025-07-30 14:39:19.295 +08:00 INF] BMS: 🚀 应用程序启动，日志系统已配置
[2025-07-30 14:39:19.323 +08:00 DBG] BMS: 🔧 调试级别日志测试
[2025-07-30 14:39:19.339 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 14:39:19.346 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 14:39:19.439 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 14:39:19.913 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 14:39:19.972 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 14:39:20.062 +08:00 INF] BMS: ✅ DbContext 初始化成功，日志文件位置: logs/
[2025-07-30 14:39:21.345 +08:00 WRN] Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware: Failed to determine the https port for redirect.
[2025-07-30 14:39:34.216 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 14:39:34.219 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 14:39:34.225 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 14:39:34.265 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 14:39:34.266 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 14:39:34.329 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  DISTINCT `type` FROM `item_config`  WHERE NOT( `type` IS NULL   OR `type`='') 
[2025-07-30 14:39:34.375 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  DISTINCT `type` FROM `item_config`  WHERE NOT( `type` IS NULL   OR `type`='') 
[2025-07-30 14:39:34.400 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  DISTINCT `quality` FROM `item_config`  WHERE NOT( `quality` IS NULL   OR `quality`='') 
[2025-07-30 14:39:34.440 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  DISTINCT `quality` FROM `item_config`  WHERE NOT( `quality` IS NULL   OR `quality`='') 
[2025-07-30 14:39:34.600 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 14:39:34.602 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 14:39:34.607 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 14:39:34.640 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 14:39:34.642 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 14:39:34.786 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-07-30 14:39:35.010 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-07-30 14:39:35.014 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 0,10
[2025-07-30 14:39:35.805 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 0,10
[2025-07-30 14:39:38.109 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 14:39:38.114 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 14:39:38.116 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 14:39:38.149 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 14:39:38.167 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 14:39:38.180 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=1280]
[2025-07-30 14:39:38.218 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-07-30 14:39:38.240 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1 | 参数: [@item_no0=10002]
[2025-07-30 14:39:38.276 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1
[2025-07-30 14:39:48.951 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 14:39:48.953 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 14:39:48.956 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 14:39:48.989 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 14:39:48.991 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 14:39:48.994 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=756]
[2025-07-30 14:39:49.029 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-07-30 14:39:49.038 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1 | 参数: [@item_no0=45452]
[2025-07-30 14:39:49.072 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1
[2025-07-30 15:08:05.339 +08:00 INF] BMS: 🚀 应用程序启动，日志系统已配置
[2025-07-30 15:08:05.407 +08:00 DBG] BMS: 🔧 调试级别日志测试
[2025-07-30 15:08:05.513 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 15:08:05.518 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 15:08:05.603 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 15:08:06.067 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 15:08:06.083 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 15:08:06.089 +08:00 INF] BMS: ✅ DbContext 初始化成功，日志文件位置: logs/
[2025-07-30 15:08:12.484 +08:00 WRN] Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware: Failed to determine the https port for redirect.
[2025-07-30 15:08:16.626 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 15:08:16.704 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 15:08:16.712 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 15:08:16.762 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 15:08:16.765 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 15:08:16.833 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  DISTINCT `type` FROM `item_config`  WHERE NOT( `type` IS NULL   OR `type`='') 
[2025-07-30 15:08:16.877 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  DISTINCT `type` FROM `item_config`  WHERE NOT( `type` IS NULL   OR `type`='') 
[2025-07-30 15:08:16.909 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  DISTINCT `quality` FROM `item_config`  WHERE NOT( `quality` IS NULL   OR `quality`='') 
[2025-07-30 15:08:16.952 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  DISTINCT `quality` FROM `item_config`  WHERE NOT( `quality` IS NULL   OR `quality`='') 
[2025-07-30 15:08:17.263 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 15:08:17.266 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 15:08:17.268 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 15:08:17.309 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 15:08:17.311 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 15:08:17.452 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-07-30 15:08:17.670 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-07-30 15:08:17.673 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 0,10
[2025-07-30 15:08:18.438 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 0,10
[2025-07-30 15:08:20.858 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 15:08:20.860 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 15:08:20.862 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 15:08:20.903 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 15:08:20.910 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 15:08:20.921 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=1280]
[2025-07-30 15:08:20.962 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-07-30 15:08:20.980 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1 | 参数: [@item_no0=10002]
[2025-07-30 15:08:21.017 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1
[2025-07-30 15:08:43.263 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 15:08:43.267 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 15:08:43.269 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 15:08:43.309 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 15:08:43.314 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 15:08:43.317 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-07-30 15:08:43.578 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-07-30 15:08:43.580 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 0,10
[2025-07-30 15:08:44.345 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 0,10
[2025-07-30 15:08:48.223 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 15:08:48.226 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 15:08:48.229 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 15:08:48.269 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 15:08:48.271 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 15:08:48.273 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=1280]
[2025-07-30 15:08:48.311 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-07-30 15:08:48.317 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1 | 参数: [@item_no0=10002]
[2025-07-30 15:08:48.355 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1
[2025-07-30 15:59:12.220 +08:00 INF] BMS: 🚀 应用程序启动，日志系统已配置
[2025-07-30 15:59:12.271 +08:00 DBG] BMS: 🔧 调试级别日志测试
[2025-07-30 15:59:12.378 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 15:59:12.403 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 15:59:12.556 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 15:59:13.171 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 15:59:13.183 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 15:59:13.185 +08:00 INF] BMS: ✅ DbContext 初始化成功，日志文件位置: logs/
[2025-07-30 15:59:14.361 +08:00 WRN] Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware: Failed to determine the https port for redirect.
[2025-07-30 15:59:17.644 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 15:59:17.647 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 15:59:17.652 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 15:59:17.708 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 15:59:17.710 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 15:59:17.761 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  DISTINCT `type` FROM `item_config`  WHERE NOT( `type` IS NULL   OR `type`='') 
[2025-07-30 15:59:17.826 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  DISTINCT `type` FROM `item_config`  WHERE NOT( `type` IS NULL   OR `type`='') 
[2025-07-30 15:59:17.847 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  DISTINCT `quality` FROM `item_config`  WHERE NOT( `quality` IS NULL   OR `quality`='') 
[2025-07-30 15:59:17.898 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  DISTINCT `quality` FROM `item_config`  WHERE NOT( `quality` IS NULL   OR `quality`='') 
[2025-07-30 15:59:18.046 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 15:59:18.052 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 15:59:18.054 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 15:59:18.103 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 15:59:18.108 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 15:59:18.227 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-07-30 15:59:18.497 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-07-30 15:59:18.499 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 0,10
[2025-07-30 15:59:19.294 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 0,10
[2025-07-30 15:59:21.314 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 15:59:21.316 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 15:59:21.318 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 15:59:21.364 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 15:59:21.365 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 15:59:21.377 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=32]
[2025-07-30 15:59:21.433 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-07-30 15:59:21.451 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1 | 参数: [@item_no0=20]
[2025-07-30 15:59:21.502 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1
[2025-07-30 15:59:24.592 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 15:59:24.594 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 15:59:24.595 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 15:59:24.640 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 15:59:24.642 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 15:59:34.725 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 15:59:34.727 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 15:59:34.729 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 15:59:34.773 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 15:59:34.774 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 15:59:34.775 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=757]
[2025-07-30 15:59:34.822 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-07-30 15:59:34.824 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1 | 参数: [@item_no0=45453]
[2025-07-30 15:59:34.869 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1
[2025-07-30 15:59:41.334 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 15:59:41.336 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 15:59:41.338 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 15:59:41.383 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 15:59:41.385 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 15:59:41.387 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=884]
[2025-07-30 15:59:41.434 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-07-30 15:59:41.437 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1 | 参数: [@item_no0=2]
[2025-07-30 15:59:41.485 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1
[2025-07-30 15:59:46.621 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 15:59:46.622 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 15:59:46.623 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 15:59:46.692 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 15:59:46.711 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 15:59:46.713 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=884]
[2025-07-30 15:59:46.775 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-07-30 15:59:46.786 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1 | 参数: [@item_no0=2]
[2025-07-30 15:59:46.843 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1
[2025-07-30 16:00:06.597 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 16:00:06.599 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 16:00:06.600 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 16:00:06.677 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 16:00:06.678 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 16:00:06.679 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=32]
[2025-07-30 16:00:06.758 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-07-30 16:00:06.762 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1 | 参数: [@item_no0=20]
[2025-07-30 16:00:06.984 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1
[2025-07-30 16:00:18.885 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 16:00:18.887 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 16:00:18.888 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 16:00:18.937 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 16:00:18.938 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 16:00:18.939 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=62]
[2025-07-30 16:00:18.985 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-07-30 16:00:18.987 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1 | 参数: [@item_no0=10000]
[2025-07-30 16:00:19.037 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1
[2025-07-30 16:00:22.796 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 16:00:22.798 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 16:00:22.799 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 16:00:22.844 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 16:00:22.845 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 16:00:22.847 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=884]
[2025-07-30 16:00:22.895 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-07-30 16:00:22.897 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1 | 参数: [@item_no0=2]
[2025-07-30 16:00:22.953 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1
[2025-07-30 16:00:27.734 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 16:00:27.738 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 16:00:27.740 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 16:00:27.798 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 16:00:27.800 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 16:00:27.802 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  DISTINCT `type` FROM `item_config`  WHERE NOT( `type` IS NULL   OR `type`='') 
[2025-07-30 16:00:27.851 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  DISTINCT `type` FROM `item_config`  WHERE NOT( `type` IS NULL   OR `type`='') 
[2025-07-30 16:00:27.861 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  DISTINCT `quality` FROM `item_config`  WHERE NOT( `quality` IS NULL   OR `quality`='') 
[2025-07-30 16:00:27.908 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  DISTINCT `quality` FROM `item_config`  WHERE NOT( `quality` IS NULL   OR `quality`='') 
[2025-07-30 16:00:27.973 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 16:00:27.975 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 16:00:27.976 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 16:00:28.022 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 16:00:28.044 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 16:00:28.047 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-07-30 16:00:28.279 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-07-30 16:00:28.281 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 0,10
[2025-07-30 16:00:29.030 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 0,10
[2025-07-30 16:00:30.957 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 16:00:30.960 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 16:00:30.963 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 16:00:31.008 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 16:00:31.010 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 16:00:31.012 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=884]
[2025-07-30 16:00:31.059 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-07-30 16:00:31.063 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1 | 参数: [@item_no0=2]
[2025-07-30 16:00:31.120 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1
[2025-07-30 16:00:34.670 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 16:00:34.714 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 16:00:34.716 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 16:00:34.786 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 16:00:34.796 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 16:00:34.802 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=884]
[2025-07-30 16:00:34.873 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-07-30 16:00:34.891 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT 1 FROM `item_config`   WHERE ( `item_no` = @item_no0 )  AND ( `id` <> @id1 )   LIMIT 0,1 | 参数: [@item_no0=2, @id1=884]
[2025-07-30 16:00:34.939 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT 1 FROM `item_config`   WHERE ( `item_no` = @item_no0 )  AND ( `id` <> @id1 )   LIMIT 0,1
[2025-07-30 16:00:35.025 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: UPDATE `item_config`  SET
           `item_no`=@item_no,`name`=@name,`type`=@type,`description`=@description,`quality`=@quality,`icon`=@icon,`price`=@price,`use_limit`=@use_limit,`extra`=@extra,`create_time`=@create_time,`script`=@script,`is_active`=@is_active  WHERE `id`=@id | 参数: [@id=884, @item_no=2, @name=测试道具, @type=未分类, @description=, @quality=普通, @icon=13, @price=1, @use_limit=无限制, @extra=, @create_time=, @script=, @is_active=True]
[2025-07-30 16:00:35.054 +08:00 ERR] BMS.Sugar.DbContext: 数据库操作发生错误: Column 'create_time' cannot be null
SqlSugar.SqlSugarException: Column 'create_time' cannot be null
[2025-07-30 16:00:35.121 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 16:00:35.123 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 16:00:35.124 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 16:00:35.170 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 16:00:35.171 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 16:00:35.173 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-07-30 16:00:35.388 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-07-30 16:00:35.391 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 0,10
[2025-07-30 16:00:36.122 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 0,10
[2025-07-30 16:00:38.470 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 16:00:38.471 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 16:00:38.472 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 16:00:38.519 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 16:00:38.520 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 16:00:38.521 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=884]
[2025-07-30 16:00:38.564 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-07-30 16:00:38.566 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1 | 参数: [@item_no0=2]
[2025-07-30 16:00:38.614 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1
[2025-07-30 16:00:43.038 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 16:00:43.038 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 16:00:43.042 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 16:00:43.041 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 16:00:43.043 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 16:00:43.044 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 16:00:43.106 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 16:00:43.109 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 16:00:43.112 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=884]
[2025-07-30 16:00:43.158 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-07-30 16:00:43.161 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT 1 FROM `item_config`   WHERE ( `item_no` = @item_no0 )  AND ( `id` <> @id1 )   LIMIT 0,1 | 参数: [@item_no0=2, @id1=884]
[2025-07-30 16:00:43.207 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT 1 FROM `item_config`   WHERE ( `item_no` = @item_no0 )  AND ( `id` <> @id1 )   LIMIT 0,1
[2025-07-30 16:00:43.273 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: UPDATE `item_config`  SET
           `item_no`=@item_no,`name`=@name,`type`=@type,`description`=@description,`quality`=@quality,`icon`=@icon,`price`=@price,`use_limit`=@use_limit,`extra`=@extra,`create_time`=@create_time,`script`=@script,`is_active`=@is_active  WHERE `id`=@id | 参数: [@id=884, @item_no=2, @name=测试道具, @type=未分类, @description=, @quality=普通, @icon=13, @price=1, @use_limit=无限制, @extra=, @create_time=, @script=, @is_active=True]
[2025-07-30 16:00:43.292 +08:00 ERR] BMS.Sugar.DbContext: 数据库操作发生错误: Column 'create_time' cannot be null
SqlSugar.SqlSugarException: Column 'create_time' cannot be null
[2025-07-30 16:00:43.318 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 16:00:43.319 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 16:00:43.321 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=884]
[2025-07-30 16:00:43.335 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 16:00:43.336 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 16:00:43.338 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 16:00:43.365 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-07-30 16:00:43.368 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT 1 FROM `item_config`   WHERE ( `item_no` = @item_no0 )  AND ( `id` <> @id1 )   LIMIT 0,1 | 参数: [@item_no0=2, @id1=884]
[2025-07-30 16:00:43.383 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 16:00:43.384 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 16:00:43.388 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-07-30 16:00:43.416 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT 1 FROM `item_config`   WHERE ( `item_no` = @item_no0 )  AND ( `id` <> @id1 )   LIMIT 0,1
[2025-07-30 16:00:43.478 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: UPDATE `item_config`  SET
           `item_no`=@item_no,`name`=@name,`type`=@type,`description`=@description,`quality`=@quality,`icon`=@icon,`price`=@price,`use_limit`=@use_limit,`extra`=@extra,`create_time`=@create_time,`script`=@script,`is_active`=@is_active  WHERE `id`=@id | 参数: [@id=884, @item_no=2, @name=测试道具, @type=未分类, @description=, @quality=普通, @icon=13, @price=1, @use_limit=无限制, @extra=, @create_time=, @script=, @is_active=True]
[2025-07-30 16:00:43.527 +08:00 ERR] BMS.Sugar.DbContext: 数据库操作发生错误: Column 'create_time' cannot be null
SqlSugar.SqlSugarException: Column 'create_time' cannot be null
[2025-07-30 16:00:43.577 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 16:00:43.579 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 16:00:43.581 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 16:00:43.597 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-07-30 16:00:43.601 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 0,10
[2025-07-30 16:00:43.639 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 16:00:43.641 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 16:00:43.643 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-07-30 16:00:43.885 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-07-30 16:00:43.886 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 0,10
[2025-07-30 16:00:44.362 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 0,10
[2025-07-30 16:00:44.631 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 0,10
[2025-07-30 16:00:48.741 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 16:00:48.742 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 16:00:48.744 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 16:00:48.786 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 16:00:48.787 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 16:00:48.788 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  DISTINCT `type` FROM `item_config`  WHERE NOT( `type` IS NULL   OR `type`='') 
[2025-07-30 16:00:48.832 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  DISTINCT `type` FROM `item_config`  WHERE NOT( `type` IS NULL   OR `type`='') 
[2025-07-30 16:00:48.834 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  DISTINCT `quality` FROM `item_config`  WHERE NOT( `quality` IS NULL   OR `quality`='') 
[2025-07-30 16:00:48.878 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  DISTINCT `quality` FROM `item_config`  WHERE NOT( `quality` IS NULL   OR `quality`='') 
[2025-07-30 16:00:48.935 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 16:00:48.936 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 16:00:48.937 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 16:00:48.980 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 16:00:48.981 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 16:00:48.983 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-07-30 16:00:49.193 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-07-30 16:00:49.195 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 0,10
[2025-07-30 16:00:49.921 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 0,10
[2025-07-30 16:00:51.613 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 16:00:51.614 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 16:00:51.616 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 16:00:51.665 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 16:00:51.666 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 16:00:51.668 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=884]
[2025-07-30 16:00:51.712 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-07-30 16:00:51.714 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1 | 参数: [@item_no0=2]
[2025-07-30 16:00:51.761 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1
[2025-07-30 16:01:20.974 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 16:01:20.975 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 16:01:20.977 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 16:01:21.021 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 16:01:21.022 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 16:01:21.024 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=884]
[2025-07-30 16:01:21.068 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-07-30 16:01:21.070 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1 | 参数: [@item_no0=2]
[2025-07-30 16:01:21.115 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1
[2025-07-30 16:01:28.535 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 16:01:28.536 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 16:01:28.540 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 16:01:28.542 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 16:01:28.544 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 16:01:28.545 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 16:01:28.588 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 16:01:28.589 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 16:01:28.591 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 16:01:28.592 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 16:01:28.594 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=884]
[2025-07-30 16:01:28.594 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=884]
[2025-07-30 16:01:28.643 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-07-30 16:01:28.646 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT 1 FROM `item_config`   WHERE ( `item_no` = @item_no0 )  AND ( `id` <> @id1 )   LIMIT 0,1 | 参数: [@item_no0=2, @id1=884]
[2025-07-30 16:01:28.648 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-07-30 16:01:28.655 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT 1 FROM `item_config`   WHERE ( `item_no` = @item_no0 )  AND ( `id` <> @id1 )   LIMIT 0,1 | 参数: [@item_no0=2, @id1=884]
[2025-07-30 16:01:28.689 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT 1 FROM `item_config`   WHERE ( `item_no` = @item_no0 )  AND ( `id` <> @id1 )   LIMIT 0,1
[2025-07-30 16:01:28.701 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT 1 FROM `item_config`   WHERE ( `item_no` = @item_no0 )  AND ( `id` <> @id1 )   LIMIT 0,1
[2025-07-30 16:01:28.755 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: UPDATE `item_config`  SET
           `item_no`=@item_no,`name`=@name,`type`=@type,`description`=@description,`quality`=@quality,`icon`=@icon,`price`=@price,`use_limit`=@use_limit,`extra`=@extra,`create_time`=@create_time,`script`=@script,`is_active`=@is_active  WHERE `id`=@id | 参数: [@id=884, @item_no=2, @name=测试道具, @type=未分类, @description=, @quality=普通, @icon=13, @price=1, @use_limit=无限制, @extra=, @create_time=, @script=, @is_active=True]
[2025-07-30 16:01:28.764 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: UPDATE `item_config`  SET
           `item_no`=@item_no,`name`=@name,`type`=@type,`description`=@description,`quality`=@quality,`icon`=@icon,`price`=@price,`use_limit`=@use_limit,`extra`=@extra,`create_time`=@create_time,`script`=@script,`is_active`=@is_active  WHERE `id`=@id | 参数: [@id=884, @item_no=2, @name=测试道具, @type=未分类, @description=, @quality=普通, @icon=13, @price=1, @use_limit=无限制, @extra=, @create_time=, @script=, @is_active=True]
[2025-07-30 16:01:28.774 +08:00 ERR] BMS.Sugar.DbContext: 数据库操作发生错误: Column 'create_time' cannot be null
SqlSugar.SqlSugarException: Column 'create_time' cannot be null
[2025-07-30 16:01:28.835 +08:00 ERR] BMS.Sugar.DbContext: 数据库操作发生错误: Column 'create_time' cannot be null
SqlSugar.SqlSugarException: Column 'create_time' cannot be null
[2025-07-30 16:01:28.836 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 16:01:28.841 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 16:01:28.843 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 16:01:28.900 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 16:01:28.902 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 16:01:28.905 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 16:01:28.937 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 16:01:28.939 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 16:01:28.943 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-07-30 16:01:28.951 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 16:01:28.952 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 16:01:28.955 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-07-30 16:01:29.162 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-07-30 16:01:29.163 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 0,10
[2025-07-30 16:01:29.170 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-07-30 16:01:29.171 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 0,10
[2025-07-30 16:01:30.172 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 0,10
[2025-07-30 16:01:30.180 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 0,10
[2025-07-30 16:02:18.345 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 16:02:18.347 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 16:02:18.348 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 16:02:18.389 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 16:02:18.392 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 16:02:18.394 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=884]
[2025-07-30 16:02:18.435 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-07-30 16:02:18.438 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1 | 参数: [@item_no0=2]
[2025-07-30 16:02:18.480 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1
[2025-07-30 16:02:24.575 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 16:02:24.575 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 16:02:24.575 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 16:02:24.582 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 16:02:24.585 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 16:02:24.589 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 16:02:24.587 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 16:02:24.589 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 16:02:24.592 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 16:02:24.636 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 16:02:24.637 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 16:02:24.636 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 16:02:24.638 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 16:02:24.639 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=884]
[2025-07-30 16:02:24.640 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=884]
[2025-07-30 16:02:24.700 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-07-30 16:02:24.700 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-07-30 16:02:24.708 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT 1 FROM `item_config`   WHERE ( `item_no` = @item_no0 )  AND ( `id` <> @id1 )   LIMIT 0,1 | 参数: [@item_no0=2, @id1=884]
[2025-07-30 16:02:24.709 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT 1 FROM `item_config`   WHERE ( `item_no` = @item_no0 )  AND ( `id` <> @id1 )   LIMIT 0,1 | 参数: [@item_no0=2, @id1=884]
[2025-07-30 16:02:24.750 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT 1 FROM `item_config`   WHERE ( `item_no` = @item_no0 )  AND ( `id` <> @id1 )   LIMIT 0,1
[2025-07-30 16:02:24.761 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT 1 FROM `item_config`   WHERE ( `item_no` = @item_no0 )  AND ( `id` <> @id1 )   LIMIT 0,1
[2025-07-30 16:02:24.807 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: UPDATE `item_config`  SET
           `item_no`=@item_no,`name`=@name,`type`=@type,`description`=@description,`quality`=@quality,`icon`=@icon,`price`=@price,`use_limit`=@use_limit,`extra`=@extra,`create_time`=@create_time,`script`=@script,`is_active`=@is_active  WHERE `id`=@id | 参数: [@id=884, @item_no=2, @name=测试道具, @type=未分类, @description=, @quality=普通, @icon=13, @price=1, @use_limit=无限制, @extra=, @create_time=, @script=, @is_active=True]
[2025-07-30 16:02:24.822 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: UPDATE `item_config`  SET
           `item_no`=@item_no,`name`=@name,`type`=@type,`description`=@description,`quality`=@quality,`icon`=@icon,`price`=@price,`use_limit`=@use_limit,`extra`=@extra,`create_time`=@create_time,`script`=@script,`is_active`=@is_active  WHERE `id`=@id | 参数: [@id=884, @item_no=2, @name=测试道具, @type=未分类, @description=, @quality=普通, @icon=13, @price=1, @use_limit=无限制, @extra=, @create_time=, @script=, @is_active=True]
[2025-07-30 16:02:24.830 +08:00 ERR] BMS.Sugar.DbContext: 数据库操作发生错误: Column 'create_time' cannot be null
SqlSugar.SqlSugarException: Column 'create_time' cannot be null
[2025-07-30 16:02:24.841 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 16:02:24.885 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 16:02:24.887 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=884]
[2025-07-30 16:02:24.929 +08:00 ERR] BMS.Sugar.DbContext: 数据库操作发生错误: Column 'create_time' cannot be null
SqlSugar.SqlSugarException: Column 'create_time' cannot be null
[2025-07-30 16:02:24.929 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 16:02:24.939 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-07-30 16:02:24.983 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 16:02:24.986 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 16:02:24.986 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT 1 FROM `item_config`   WHERE ( `item_no` = @item_no0 )  AND ( `id` <> @id1 )   LIMIT 0,1 | 参数: [@item_no0=2, @id1=884]
[2025-07-30 16:02:24.999 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 16:02:25.001 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 16:02:25.003 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 16:02:25.036 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 16:02:25.048 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 16:02:25.044 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT 1 FROM `item_config`   WHERE ( `item_no` = @item_no0 )  AND ( `id` <> @id1 )   LIMIT 0,1
[2025-07-30 16:02:25.055 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 16:02:25.064 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 16:02:25.056 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-07-30 16:02:25.067 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-07-30 16:02:25.118 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: UPDATE `item_config`  SET
           `item_no`=@item_no,`name`=@name,`type`=@type,`description`=@description,`quality`=@quality,`icon`=@icon,`price`=@price,`use_limit`=@use_limit,`extra`=@extra,`create_time`=@create_time,`script`=@script,`is_active`=@is_active  WHERE `id`=@id | 参数: [@id=884, @item_no=2, @name=测试道具, @type=未分类, @description=, @quality=普通, @icon=13, @price=1, @use_limit=无限制, @extra=, @create_time=, @script=, @is_active=True]
[2025-07-30 16:02:25.144 +08:00 ERR] BMS.Sugar.DbContext: 数据库操作发生错误: Column 'create_time' cannot be null
SqlSugar.SqlSugarException: Column 'create_time' cannot be null
[2025-07-30 16:02:25.216 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 16:02:25.221 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 16:02:25.224 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 16:02:25.269 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 16:02:25.271 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 16:02:25.274 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-07-30 16:02:25.322 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-07-30 16:02:25.324 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 0,10
[2025-07-30 16:02:25.328 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-07-30 16:02:25.329 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 0,10
[2025-07-30 16:02:25.504 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-07-30 16:02:25.506 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 0,10
[2025-07-30 16:02:26.090 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 0,10
[2025-07-30 16:02:26.107 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 0,10
[2025-07-30 16:02:26.327 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 0,10
[2025-07-30 16:10:24.949 +08:00 INF] BMS: 🚀 应用程序启动，日志系统已配置
[2025-07-30 16:10:24.975 +08:00 DBG] BMS: 🔧 调试级别日志测试
[2025-07-30 16:10:24.990 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 16:10:24.994 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 16:10:25.084 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 16:10:25.618 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 16:10:25.705 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 16:10:25.769 +08:00 INF] BMS: ✅ DbContext 初始化成功，日志文件位置: logs/
[2025-07-30 16:10:26.774 +08:00 WRN] Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware: Failed to determine the https port for redirect.
[2025-07-30 16:10:50.908 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 16:10:50.909 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 16:10:50.914 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 16:10:50.960 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 16:10:50.964 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 16:10:51.015 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  DISTINCT `type` FROM `item_config`  WHERE NOT( `type` IS NULL   OR `type`='') 
[2025-07-30 16:10:51.064 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  DISTINCT `type` FROM `item_config`  WHERE NOT( `type` IS NULL   OR `type`='') 
[2025-07-30 16:10:51.089 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  DISTINCT `quality` FROM `item_config`  WHERE NOT( `quality` IS NULL   OR `quality`='') 
[2025-07-30 16:10:51.134 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  DISTINCT `quality` FROM `item_config`  WHERE NOT( `quality` IS NULL   OR `quality`='') 
[2025-07-30 16:10:51.244 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 16:10:51.245 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 16:10:51.247 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 16:10:51.293 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 16:10:51.294 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 16:10:51.420 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-07-30 16:10:51.625 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-07-30 16:10:51.628 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 0,10
[2025-07-30 16:10:52.344 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 0,10
[2025-07-30 16:10:55.412 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 16:10:55.413 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 16:10:55.415 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 16:10:55.466 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 16:10:55.467 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 16:10:55.476 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=32]
[2025-07-30 16:10:55.530 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-07-30 16:10:55.549 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1 | 参数: [@item_no0=20]
[2025-07-30 16:10:55.610 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1
[2025-07-30 16:11:04.754 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 16:11:04.755 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 16:11:04.757 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 16:11:04.808 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 16:11:04.809 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 16:11:04.811 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  DISTINCT `type` FROM `item_config`  WHERE NOT( `type` IS NULL   OR `type`='') 
[2025-07-30 16:11:04.858 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  DISTINCT `type` FROM `item_config`  WHERE NOT( `type` IS NULL   OR `type`='') 
[2025-07-30 16:11:04.860 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  DISTINCT `quality` FROM `item_config`  WHERE NOT( `quality` IS NULL   OR `quality`='') 
[2025-07-30 16:11:04.906 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  DISTINCT `quality` FROM `item_config`  WHERE NOT( `quality` IS NULL   OR `quality`='') 
[2025-07-30 16:11:05.027 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 16:11:05.029 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 16:11:05.030 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 16:11:05.076 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 16:11:05.078 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 16:11:05.081 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-07-30 16:11:05.295 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-07-30 16:11:05.297 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 0,10
[2025-07-30 16:11:06.000 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 0,10
[2025-07-30 16:11:12.288 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 16:11:12.320 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 16:11:12.322 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 16:11:12.367 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 16:11:12.369 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 16:11:12.372 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=884]
[2025-07-30 16:11:12.416 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-07-30 16:11:12.426 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1 | 参数: [@item_no0=2]
[2025-07-30 16:11:12.497 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1
[2025-07-30 16:11:20.570 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 16:11:20.572 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 16:11:20.574 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 16:11:20.619 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 16:11:20.624 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 16:11:20.634 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=884]
[2025-07-30 16:11:20.691 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-07-30 16:11:20.697 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT 1 FROM `item_config`   WHERE ( `item_no` = @item_no0 )  AND ( `id` <> @id1 )   LIMIT 0,1 | 参数: [@item_no0=2, @id1=884]
[2025-07-30 16:11:20.744 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT 1 FROM `item_config`   WHERE ( `item_no` = @item_no0 )  AND ( `id` <> @id1 )   LIMIT 0,1
[2025-07-30 16:11:20.828 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: UPDATE `item_config`  SET
           `item_no`=@item_no,`name`=@name,`type`=@type,`description`=@description,`quality`=@quality,`icon`=@icon,`price`=@price,`use_limit`=@use_limit,`extra`=@extra,`create_time`=@create_time,`script`=@script,`is_active`=@is_active  WHERE `id`=@id | 参数: [@id=884, @item_no=2, @name=测试道具123123, @type=未分类, @description=, @quality=普通, @icon=, @price=1, @use_limit=, @extra=, @create_time=, @script=, @is_active=True]
[2025-07-30 16:11:20.852 +08:00 ERR] BMS.Sugar.DbContext: 数据库操作发生错误: Column 'create_time' cannot be null
SqlSugar.SqlSugarException: Column 'create_time' cannot be null
[2025-07-30 16:11:20.912 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 16:11:20.914 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 16:11:20.915 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 16:11:20.957 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 16:11:20.960 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 16:11:20.962 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-07-30 16:11:21.188 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-07-30 16:11:21.192 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 0,10
[2025-07-30 16:11:21.927 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 0,10
[2025-07-30 16:11:48.474 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 16:11:48.475 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 16:11:48.477 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 16:11:48.519 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 16:11:48.521 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 16:11:48.523 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  DISTINCT `type` FROM `item_config`  WHERE NOT( `type` IS NULL   OR `type`='') 
[2025-07-30 16:11:48.569 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  DISTINCT `type` FROM `item_config`  WHERE NOT( `type` IS NULL   OR `type`='') 
[2025-07-30 16:11:48.576 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  DISTINCT `quality` FROM `item_config`  WHERE NOT( `quality` IS NULL   OR `quality`='') 
[2025-07-30 16:11:48.623 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  DISTINCT `quality` FROM `item_config`  WHERE NOT( `quality` IS NULL   OR `quality`='') 
[2025-07-30 16:11:48.756 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 16:11:48.757 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 16:11:48.759 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 16:11:48.812 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 16:11:48.815 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 16:11:48.820 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-07-30 16:11:49.059 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-07-30 16:11:49.178 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 0,10
[2025-07-30 16:11:49.916 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 0,10
[2025-07-30 16:11:55.304 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 16:11:55.305 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 16:11:55.307 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 16:11:55.354 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 16:11:55.355 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 16:11:55.356 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=884]
[2025-07-30 16:11:55.409 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-07-30 16:11:55.412 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1 | 参数: [@item_no0=2]
[2025-07-30 16:11:55.458 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1
[2025-07-30 16:12:00.400 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 16:12:00.403 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 16:12:00.405 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 16:12:00.452 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 16:12:00.454 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 16:12:00.456 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=884]
[2025-07-30 16:12:00.501 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-07-30 16:12:00.504 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT 1 FROM `item_config`   WHERE ( `item_no` = @item_no0 )  AND ( `id` <> @id1 )   LIMIT 0,1 | 参数: [@item_no0=2, @id1=884]
[2025-07-30 16:12:00.547 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT 1 FROM `item_config`   WHERE ( `item_no` = @item_no0 )  AND ( `id` <> @id1 )   LIMIT 0,1
[2025-07-30 16:12:00.609 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: UPDATE `item_config`  SET
           `item_no`=@item_no,`name`=@name,`type`=@type,`description`=@description,`quality`=@quality,`icon`=@icon,`price`=@price,`use_limit`=@use_limit,`extra`=@extra,`create_time`=@create_time,`script`=@script,`is_active`=@is_active  WHERE `id`=@id | 参数: [@id=884, @item_no=2, @name=测试道具123, @type=未分类, @description=, @quality=普通, @icon=, @price=1, @use_limit=, @extra=, @create_time=, @script=, @is_active=True]
[2025-07-30 16:12:00.634 +08:00 ERR] BMS.Sugar.DbContext: 数据库操作发生错误: Column 'create_time' cannot be null
SqlSugar.SqlSugarException: Column 'create_time' cannot be null
[2025-07-30 16:12:00.707 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 16:12:00.709 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 16:12:00.711 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 16:12:00.761 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 16:12:00.765 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 16:12:00.767 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-07-30 16:12:00.977 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-07-30 16:12:00.981 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 0,10
[2025-07-30 16:12:01.684 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 0,10
[2025-07-30 16:50:07.398 +08:00 INF] BMS: 🚀 应用程序启动，日志系统已配置
[2025-07-30 16:50:07.430 +08:00 DBG] BMS: 🔧 调试级别日志测试
[2025-07-30 16:50:07.445 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 16:50:07.447 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 16:50:07.529 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 16:50:08.621 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 16:50:08.635 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 16:50:08.636 +08:00 INF] BMS: ✅ DbContext 初始化成功，日志文件位置: logs/
[2025-07-30 16:50:09.996 +08:00 WRN] Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware: Failed to determine the https port for redirect.
[2025-07-30 16:50:15.592 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 16:50:15.601 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 16:50:15.614 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 16:50:15.742 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 16:50:15.745 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 16:50:15.795 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  DISTINCT `type` FROM `item_config`  WHERE NOT( `type` IS NULL   OR `type`='') 
[2025-07-30 16:50:15.884 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  DISTINCT `type` FROM `item_config`  WHERE NOT( `type` IS NULL   OR `type`='') 
[2025-07-30 16:50:15.909 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  DISTINCT `quality` FROM `item_config`  WHERE NOT( `quality` IS NULL   OR `quality`='') 
[2025-07-30 16:50:15.958 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  DISTINCT `quality` FROM `item_config`  WHERE NOT( `quality` IS NULL   OR `quality`='') 
[2025-07-30 16:50:16.117 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 16:50:16.120 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 16:50:16.123 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 16:50:16.166 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 16:50:16.174 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 16:50:16.395 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-07-30 16:50:16.616 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-07-30 16:50:16.623 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 0,10
[2025-07-30 16:50:17.377 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 0,10
[2025-07-30 16:50:19.978 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 16:50:19.979 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 16:50:19.981 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 16:50:20.031 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 16:50:20.034 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 16:50:20.046 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=884]
[2025-07-30 16:50:20.096 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-07-30 16:50:20.115 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1 | 参数: [@item_no0=2]
[2025-07-30 16:50:20.162 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1
[2025-07-30 16:50:25.543 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 16:50:25.545 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 16:50:25.547 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 16:50:25.593 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 16:50:25.594 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 16:50:25.606 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=884]
[2025-07-30 16:50:25.654 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-07-30 16:50:25.658 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT 1 FROM `item_config`   WHERE ( `item_no` = @item_no0 )  AND ( `id` <> @id1 )   LIMIT 0,1 | 参数: [@item_no0=2, @id1=884]
[2025-07-30 16:50:25.706 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT 1 FROM `item_config`   WHERE ( `item_no` = @item_no0 )  AND ( `id` <> @id1 )   LIMIT 0,1
[2025-07-30 16:50:25.820 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: UPDATE `item_config`  SET
           `item_no`=@item_no,`name`=@name,`type`=@type,`description`=@description,`quality`=@quality,`icon`=@icon,`price`=@price,`use_limit`=@use_limit,`extra`=@extra,`create_time`=@create_time,`script`=@script,`is_active`=@is_active  WHERE `id`=@id | 参数: [@id=884, @item_no=2, @name=测试道具123, @type=未分类, @description=, @quality=普通, @icon=, @price=1, @use_limit=, @extra=, @create_time=2025/7/21 0:09:48, @script=, @is_active=True]
[2025-07-30 16:50:25.848 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: UPDATE `item_config`  SET
           `item_no`=@item_no,`name`=@name,`type`=@type,`description`=@description,`quality`=@quality,`icon`=@icon,`price`=@price,`use_limit`=@use_limit,`extra`=@extra,`create_time`=@create_time,`script`=@script,`is_active`=@is_active  WHERE `id`=@id
[2025-07-30 16:50:25.853 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1 | 参数: [@item_no0=2]
[2025-07-30 16:50:25.881 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1
[2025-07-30 16:50:25.893 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: UPDATE `item_script`  SET
           `item_no`=@item_no,`script`=@script,`description`=@description,`create_time`=@create_time  WHERE `id`=@id | 参数: [@id=882, @item_no=2, @script=, @description=, @create_time=2025/7/21 0:09:48]
[2025-07-30 16:50:25.912 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: UPDATE `item_script`  SET
           `item_no`=@item_no,`script`=@script,`description`=@description,`create_time`=@create_time  WHERE `id`=@id
[2025-07-30 16:50:25.946 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 16:50:25.956 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 16:50:25.959 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 16:50:26.007 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 16:50:26.010 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 16:50:26.013 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-07-30 16:50:26.237 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-07-30 16:50:26.239 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 0,10
[2025-07-30 16:50:26.951 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 0,10
[2025-07-30 16:50:33.775 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 16:50:33.776 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 16:50:33.778 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 16:50:33.852 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 16:50:33.853 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 16:50:33.855 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=32]
[2025-07-30 16:50:33.964 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-07-30 16:50:33.966 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1 | 参数: [@item_no0=20]
[2025-07-30 16:50:34.040 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1
[2025-07-30 16:50:38.292 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 16:50:38.294 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 16:50:38.296 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 16:50:38.368 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 16:50:38.378 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 16:50:38.380 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=884]
[2025-07-30 16:50:38.474 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-07-30 16:50:38.479 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1 | 参数: [@item_no0=2]
[2025-07-30 16:50:38.523 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1
[2025-07-30 17:06:03.864 +08:00 INF] BMS: 🚀 应用程序启动，日志系统已配置
[2025-07-30 17:06:03.896 +08:00 DBG] BMS: 🔧 调试级别日志测试
[2025-07-30 17:06:03.932 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 17:06:03.952 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 17:06:04.042 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 17:06:04.585 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 17:06:04.639 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 17:06:04.649 +08:00 INF] BMS: ✅ DbContext 初始化成功，日志文件位置: logs/
[2025-07-30 17:06:05.841 +08:00 WRN] Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware: Failed to determine the https port for redirect.
[2025-07-30 17:06:11.185 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 17:06:11.189 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 17:06:11.198 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 17:06:11.250 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 17:06:11.253 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 17:06:11.351 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  DISTINCT `type` FROM `item_config`  WHERE NOT( `type` IS NULL   OR `type`='') 
[2025-07-30 17:06:11.411 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  DISTINCT `type` FROM `item_config`  WHERE NOT( `type` IS NULL   OR `type`='') 
[2025-07-30 17:06:11.457 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  DISTINCT `quality` FROM `item_config`  WHERE NOT( `quality` IS NULL   OR `quality`='') 
[2025-07-30 17:06:11.524 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  DISTINCT `quality` FROM `item_config`  WHERE NOT( `quality` IS NULL   OR `quality`='') 
[2025-07-30 17:06:11.712 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 17:06:11.714 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 17:06:11.717 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 17:06:11.769 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 17:06:11.771 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 17:06:11.994 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-07-30 17:06:12.237 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-07-30 17:06:12.299 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 0,10
[2025-07-30 17:06:13.054 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 0,10
[2025-07-30 17:06:18.020 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 17:06:18.025 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 17:06:18.034 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 17:06:18.084 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 17:06:18.086 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 17:06:18.106 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=755]
[2025-07-30 17:06:18.169 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-07-30 17:06:18.210 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1 | 参数: [@item_no0=45451]
[2025-07-30 17:06:18.262 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1
[2025-07-30 17:06:24.838 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 17:06:24.855 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 17:06:24.859 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 17:06:24.910 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 17:06:24.913 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 17:06:24.916 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=884]
[2025-07-30 17:06:24.969 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-07-30 17:06:24.972 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1 | 参数: [@item_no0=2]
[2025-07-30 17:06:25.019 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1
[2025-07-30 17:06:31.843 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 17:06:31.886 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 17:06:31.890 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 17:06:31.939 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 17:06:31.941 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 17:06:31.978 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT 1 FROM `item_config`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1 | 参数: [@item_no0=2]
[2025-07-30 17:06:32.027 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT 1 FROM `item_config`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1
[2025-07-30 17:06:40.869 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 17:06:40.872 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 17:06:40.875 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 17:06:40.924 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 17:06:40.928 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 17:06:40.931 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT 1 FROM `item_config`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1 | 参数: [@item_no0=2]
[2025-07-30 17:06:40.978 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT 1 FROM `item_config`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1
[2025-07-30 17:06:46.597 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 17:06:46.599 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 17:06:46.601 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 17:06:46.674 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 17:06:46.675 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 17:06:46.676 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT 1 FROM `item_config`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1 | 参数: [@item_no0=2]
[2025-07-30 17:06:46.733 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT 1 FROM `item_config`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1
[2025-07-30 17:06:52.550 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 17:06:52.552 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 17:06:52.555 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 17:06:52.608 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 17:06:52.610 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 17:06:52.613 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT 1 FROM `item_config`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1 | 参数: [@item_no0=2]
[2025-07-30 17:06:52.712 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT 1 FROM `item_config`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1
[2025-07-30 17:07:08.149 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 17:07:08.150 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 17:07:08.152 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 17:07:08.197 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 17:07:08.199 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 17:07:08.200 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=884]
[2025-07-30 17:07:08.252 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-07-30 17:07:08.314 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1 | 参数: [@item_no0=2]
[2025-07-30 17:07:08.389 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1
[2025-07-30 17:07:12.639 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 17:07:12.803 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 17:07:12.844 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 17:07:12.896 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 17:07:12.897 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 17:07:12.903 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT 1 FROM `item_config`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1 | 参数: [@item_no0=2]
[2025-07-30 17:07:12.951 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT 1 FROM `item_config`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1
[2025-07-30 17:12:11.516 +08:00 INF] BMS: 🚀 应用程序启动，日志系统已配置
[2025-07-30 17:12:11.550 +08:00 DBG] BMS: 🔧 调试级别日志测试
[2025-07-30 17:12:11.567 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 17:12:11.570 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 17:12:11.653 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 17:12:12.118 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 17:12:12.213 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 17:12:12.237 +08:00 INF] BMS: ✅ DbContext 初始化成功，日志文件位置: logs/
[2025-07-30 17:12:13.495 +08:00 WRN] Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware: Failed to determine the https port for redirect.
[2025-07-30 17:12:16.319 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 17:12:16.396 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 17:12:16.444 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 17:12:16.488 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 17:12:16.489 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 17:12:16.547 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  DISTINCT `type` FROM `item_config`  WHERE NOT( `type` IS NULL   OR `type`='') 
[2025-07-30 17:12:16.590 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  DISTINCT `type` FROM `item_config`  WHERE NOT( `type` IS NULL   OR `type`='') 
[2025-07-30 17:12:16.615 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  DISTINCT `quality` FROM `item_config`  WHERE NOT( `quality` IS NULL   OR `quality`='') 
[2025-07-30 17:12:16.651 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  DISTINCT `quality` FROM `item_config`  WHERE NOT( `quality` IS NULL   OR `quality`='') 
[2025-07-30 17:12:16.799 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 17:12:16.802 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 17:12:16.805 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 17:12:16.842 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 17:12:16.848 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 17:12:16.979 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-07-30 17:12:17.222 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-07-30 17:12:17.226 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 0,10
[2025-07-30 17:12:18.052 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 0,10
[2025-07-30 17:12:24.541 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 17:12:24.543 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 17:12:24.549 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 17:12:24.580 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 17:12:24.585 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 17:12:24.609 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=32]
[2025-07-30 17:12:24.675 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-07-30 17:12:24.718 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1 | 参数: [@item_no0=20]
[2025-07-30 17:12:24.758 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1
[2025-07-30 17:12:27.829 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 17:12:27.830 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 17:12:27.832 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 17:12:27.862 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 17:12:27.863 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 17:12:27.865 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=884]
[2025-07-30 17:12:27.896 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-07-30 17:12:27.903 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1 | 参数: [@item_no0=2]
[2025-07-30 17:12:27.936 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1
[2025-07-30 17:12:32.058 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 17:12:32.061 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 17:12:32.066 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 17:12:32.103 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 17:12:32.105 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 17:12:32.129 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=884]
[2025-07-30 17:12:32.162 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-07-30 17:12:32.167 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT 1 FROM `item_config`   WHERE ( `item_no` = @item_no0 )  AND ( `id` <> @id1 )   LIMIT 0,1 | 参数: [@item_no0=2, @id1=884]
[2025-07-30 17:12:32.202 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT 1 FROM `item_config`   WHERE ( `item_no` = @item_no0 )  AND ( `id` <> @id1 )   LIMIT 0,1
[2025-07-30 17:12:32.297 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: UPDATE `item_config`  SET
           `item_no`=@item_no,`name`=@name,`type`=@type,`description`=@description,`quality`=@quality,`icon`=@icon,`price`=@price,`use_limit`=@use_limit,`extra`=@extra,`create_time`=@create_time,`script`=@script,`is_active`=@is_active  WHERE `id`=@id | 参数: [@id=884, @item_no=2, @name=测试道具1, @type=未分类, @description=, @quality=普通, @icon=, @price=1, @use_limit=, @extra=, @create_time=2025/7/21 0:09:48, @script=, @is_active=True]
[2025-07-30 17:12:32.316 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: UPDATE `item_config`  SET
           `item_no`=@item_no,`name`=@name,`type`=@type,`description`=@description,`quality`=@quality,`icon`=@icon,`price`=@price,`use_limit`=@use_limit,`extra`=@extra,`create_time`=@create_time,`script`=@script,`is_active`=@is_active  WHERE `id`=@id
[2025-07-30 17:12:32.320 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1 | 参数: [@item_no0=2]
[2025-07-30 17:12:32.332 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1
[2025-07-30 17:12:32.333 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: UPDATE `item_script`  SET
           `item_no`=@item_no,`script`=@script,`description`=@description,`create_time`=@create_time  WHERE `id`=@id | 参数: [@id=882, @item_no=2, @script=, @description=, @create_time=2025/7/21 0:09:48]
[2025-07-30 17:12:32.343 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: UPDATE `item_script`  SET
           `item_no`=@item_no,`script`=@script,`description`=@description,`create_time`=@create_time  WHERE `id`=@id
[2025-07-30 17:12:32.415 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 17:12:32.456 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 17:12:32.475 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 17:12:32.508 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 17:12:32.514 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 17:12:32.518 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-07-30 17:12:32.767 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-07-30 17:12:32.771 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 0,10
[2025-07-30 17:12:33.557 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 0,10
[2025-07-30 17:12:38.443 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 17:12:38.448 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 17:12:38.451 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 17:12:38.486 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 17:12:38.487 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 17:12:38.513 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=884]
[2025-07-30 17:12:38.549 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-07-30 17:12:38.560 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT 1 FROM `user_item`   WHERE ( `item_id` = CAST(@MethodConst0 AS CHAR))   LIMIT 0,1 | 参数: [@MethodConst0=2]
[2025-07-30 17:12:38.591 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT 1 FROM `user_item`   WHERE ( `item_id` = CAST(@MethodConst0 AS CHAR))   LIMIT 0,1
[2025-07-30 17:12:49.791 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 17:12:49.792 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 17:12:49.794 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 17:12:49.823 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 17:12:49.824 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 17:12:49.827 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-07-30 17:12:50.035 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-07-30 17:12:50.037 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 10,10
[2025-07-30 17:12:50.734 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 10,10
[2025-07-30 17:12:52.500 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 17:12:52.515 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 17:12:52.538 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 17:12:52.591 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 17:12:52.595 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 17:12:52.598 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-07-30 17:12:52.799 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-07-30 17:12:52.812 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 0,10
[2025-07-30 17:12:53.517 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 0,10
[2025-07-30 17:13:12.869 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 17:13:12.883 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 17:13:12.887 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 17:13:12.937 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 17:13:12.963 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 17:13:31.804 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 17:13:31.805 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 17:13:31.807 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 17:13:31.836 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 17:13:31.840 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 17:13:45.518 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 17:13:45.525 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 17:13:45.528 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 17:13:45.564 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 17:13:45.566 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 17:13:55.368 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 17:13:55.374 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 17:13:55.377 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 17:13:55.419 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 17:13:55.420 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 17:14:01.113 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 17:14:01.118 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 17:14:01.122 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 17:14:01.171 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 17:14:01.174 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 17:14:11.244 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 17:14:11.257 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 17:14:11.260 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 17:14:11.291 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 17:14:11.293 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 17:14:11.295 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=884]
[2025-07-30 17:14:11.324 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-07-30 17:14:11.326 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1 | 参数: [@item_no0=2]
[2025-07-30 17:14:11.357 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1
[2025-07-30 17:14:16.653 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 17:14:16.655 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 17:14:16.656 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 17:14:16.685 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 17:14:16.686 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 17:14:16.687 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=884]
[2025-07-30 17:14:16.730 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-07-30 17:14:16.733 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1 | 参数: [@item_no0=2]
[2025-07-30 17:14:16.773 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1
[2025-07-30 17:14:25.525 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 17:14:25.526 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 17:14:25.528 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 17:14:25.557 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 17:14:25.558 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 17:14:25.561 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=848]
[2025-07-30 17:14:25.594 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-07-30 17:14:25.597 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1 | 参数: [@item_no0=9601]
[2025-07-30 17:14:25.629 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1
[2025-07-30 17:14:44.325 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 17:14:44.326 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 17:14:44.328 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 17:14:44.363 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 17:14:44.365 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 17:14:44.368 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=884]
[2025-07-30 17:14:44.396 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-07-30 17:14:44.399 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1 | 参数: [@item_no0=2]
[2025-07-30 17:14:44.431 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1
[2025-07-30 17:14:54.012 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 17:14:54.014 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 17:14:54.017 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 17:14:54.046 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 17:14:54.047 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 17:14:54.049 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  DISTINCT `type` FROM `item_config`  WHERE NOT( `type` IS NULL   OR `type`='') 
[2025-07-30 17:14:54.084 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  DISTINCT `type` FROM `item_config`  WHERE NOT( `type` IS NULL   OR `type`='') 
[2025-07-30 17:14:54.090 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  DISTINCT `quality` FROM `item_config`  WHERE NOT( `quality` IS NULL   OR `quality`='') 
[2025-07-30 17:14:54.127 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  DISTINCT `quality` FROM `item_config`  WHERE NOT( `quality` IS NULL   OR `quality`='') 
[2025-07-30 17:14:54.198 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 17:14:54.200 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 17:14:54.202 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 17:14:54.251 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 17:14:54.254 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 17:14:54.258 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-07-30 17:14:54.499 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-07-30 17:14:54.599 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 0,10
[2025-07-30 17:14:55.306 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 0,10
[2025-07-30 17:15:01.528 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 17:15:01.531 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 17:15:01.541 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 17:15:01.583 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 17:15:01.584 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 17:15:01.590 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `equip_type_id` AS `EquipTypeId` , `type_name` AS `TypeName`  FROM `equipment_type`  WHERE ( `is_active` = @is_active0 )ORDER BY `sort_order` ASC,`id` ASC  | 参数: [@is_active0=True]
[2025-07-30 17:15:01.630 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `equip_type_id` AS `EquipTypeId` , `type_name` AS `TypeName`  FROM `equipment_type`  WHERE ( `is_active` = @is_active0 )ORDER BY `sort_order` ASC,`id` ASC 
[2025-07-30 17:15:01.764 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 17:15:01.766 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 17:15:01.772 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 17:15:01.812 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 17:15:01.817 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 17:15:01.839 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )   
[2025-07-30 17:15:01.901 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )   
[2025-07-30 17:15:01.912 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `e`.`equip_id` AS `EquipId` , `e`.`class_id` AS `ClassId` , `e`.`name` AS `Name` , `e`.`icon` AS `Icon` , `e`.`equip_type_id` AS `EquipTypeId` , `et`.`type_name` AS `EquipTypeName` , `e`.`element` AS `Element` , IFNULL(`e`.`slot`,@MethodConst13) AS `Slot` , IFNULL(`e`.`strengthen_level`,@MethodConst14) AS `StrengthenLevel` , `e`.`suit_id` AS `SuitId` , IFNULL(`ed`.`atk`,@MethodConst15) AS `Atk` , IFNULL(`ed`.`hit`,@MethodConst16) AS `Hit` , IFNULL(`ed`.`def`,@MethodConst17) AS `Def` , IFNULL(`ed`.`spd`,@MethodConst18) AS `Spd` , IFNULL(`ed`.`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`ed`.`hp`,@MethodConst20) AS `Hp` , IFNULL(`ed`.`mp`,@MethodConst21) AS `Mp` , IFNULL(`ed`.`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`ed`.`offset`,@MethodConst23) AS `Offset` , IFNULL(`ed`.`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`ed`.`vamp_mp`,@MethodConst25) AS `VampMp` , `ed`.`description` AS `Description` , `ed`.`main_attr` AS `MainAttr` , `ed`.`main_attr_value` AS `MainAttrValue` , `ed`.`sub_attr` AS `SubAttr` , `ed`.`sub_attr_value` AS `SubAttrValue` , `ed`.`element_limit` AS `ElementLimit` , `ed`.`equip_name` AS `EquipName`  FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )     ORDER BY `e`.`equip_id` ASC LIMIT 0,10 | 参数: [@MethodConst0=0, @MethodConst1=0, @MethodConst2=0, @MethodConst3=0, @MethodConst4=0, @MethodConst5=0, @MethodConst6=0, @MethodConst7=0, @MethodConst8=0, @MethodConst9=0, @MethodConst10=0, @MethodConst11=0, @MethodConst12=0, @MethodConst13=0, @MethodConst14=0, @MethodConst15=0, @MethodConst16=0, @MethodConst17=0, @MethodConst18=0, @MethodConst19=0, @MethodConst20=0, @MethodConst21=0, @MethodConst22=0, @MethodConst23=0, @MethodConst24=0, @MethodConst25=0]
[2025-07-30 17:15:01.961 +08:00 ERR] BMS.Sugar.DbContext: 数据库操作发生错误: Unknown column 'e.class_id' in 'field list'
SqlSugar.SqlSugarException: Unknown column 'e.class_id' in 'field list'
[2025-07-30 17:15:02.017 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 17:15:02.019 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 17:15:02.022 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 17:15:02.053 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 17:15:02.060 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 17:15:02.068 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment_type`  
[2025-07-30 17:15:02.102 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment_type`  
[2025-07-30 17:15:02.104 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `id` AS `Id` , `equip_type_id` AS `EquipTypeId` , `type_name` AS `TypeName` , `description` AS `Description` , IFNULL(`sort_order`,@MethodConst3) AS `SortOrder` , IFNULL(CAST(`is_active` as SIGNED),@MethodConst4) AS `IsActive` , `create_time` AS `CreateTime` , `update_time` AS `UpdateTime` , @constant5 AS `EquipmentCount`  FROM `equipment_type` `et`    ORDER BY `sort_order` ASC,`id` ASC LIMIT 0,10 | 参数: [@MethodConst0=0, @MethodConst1=True, @constant2=0, @MethodConst3=0, @MethodConst4=True, @constant5=0]
[2025-07-30 17:15:02.142 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `id` AS `Id` , `equip_type_id` AS `EquipTypeId` , `type_name` AS `TypeName` , `description` AS `Description` , IFNULL(`sort_order`,@MethodConst3) AS `SortOrder` , IFNULL(CAST(`is_active` as SIGNED),@MethodConst4) AS `IsActive` , `create_time` AS `CreateTime` , `update_time` AS `UpdateTime` , @constant5 AS `EquipmentCount`  FROM `equipment_type` `et`    ORDER BY `sort_order` ASC,`id` ASC LIMIT 0,10
[2025-07-30 17:15:02.145 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=1]
[2025-07-30 17:15:02.209 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-07-30 17:15:02.211 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=2]
[2025-07-30 17:15:02.250 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-07-30 17:15:02.251 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=3]
[2025-07-30 17:15:02.286 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-07-30 17:15:02.288 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=4]
[2025-07-30 17:15:02.328 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-07-30 17:15:08.774 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 17:15:08.776 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 17:15:08.777 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 17:15:08.808 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 17:15:08.809 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 17:15:08.811 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `equip_type_id` AS `EquipTypeId` , `type_name` AS `TypeName`  FROM `equipment_type`  WHERE ( `is_active` = @is_active0 )ORDER BY `sort_order` ASC,`id` ASC  | 参数: [@is_active0=True]
[2025-07-30 17:15:08.851 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `equip_type_id` AS `EquipTypeId` , `type_name` AS `TypeName`  FROM `equipment_type`  WHERE ( `is_active` = @is_active0 )ORDER BY `sort_order` ASC,`id` ASC 
[2025-07-30 17:15:08.992 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 17:15:08.994 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 17:15:08.996 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 17:15:09.027 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 17:15:09.028 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 17:15:09.032 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )   
[2025-07-30 17:15:09.073 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )   
[2025-07-30 17:15:09.076 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `e`.`equip_id` AS `EquipId` , `e`.`class_id` AS `ClassId` , `e`.`name` AS `Name` , `e`.`icon` AS `Icon` , `e`.`equip_type_id` AS `EquipTypeId` , `et`.`type_name` AS `EquipTypeName` , `e`.`element` AS `Element` , IFNULL(`e`.`slot`,@MethodConst13) AS `Slot` , IFNULL(`e`.`strengthen_level`,@MethodConst14) AS `StrengthenLevel` , `e`.`suit_id` AS `SuitId` , IFNULL(`ed`.`atk`,@MethodConst15) AS `Atk` , IFNULL(`ed`.`hit`,@MethodConst16) AS `Hit` , IFNULL(`ed`.`def`,@MethodConst17) AS `Def` , IFNULL(`ed`.`spd`,@MethodConst18) AS `Spd` , IFNULL(`ed`.`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`ed`.`hp`,@MethodConst20) AS `Hp` , IFNULL(`ed`.`mp`,@MethodConst21) AS `Mp` , IFNULL(`ed`.`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`ed`.`offset`,@MethodConst23) AS `Offset` , IFNULL(`ed`.`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`ed`.`vamp_mp`,@MethodConst25) AS `VampMp` , `ed`.`description` AS `Description` , `ed`.`main_attr` AS `MainAttr` , `ed`.`main_attr_value` AS `MainAttrValue` , `ed`.`sub_attr` AS `SubAttr` , `ed`.`sub_attr_value` AS `SubAttrValue` , `ed`.`element_limit` AS `ElementLimit` , `ed`.`equip_name` AS `EquipName`  FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )     ORDER BY `e`.`equip_id` ASC LIMIT 0,10 | 参数: [@MethodConst0=0, @MethodConst1=0, @MethodConst2=0, @MethodConst3=0, @MethodConst4=0, @MethodConst5=0, @MethodConst6=0, @MethodConst7=0, @MethodConst8=0, @MethodConst9=0, @MethodConst10=0, @MethodConst11=0, @MethodConst12=0, @MethodConst13=0, @MethodConst14=0, @MethodConst15=0, @MethodConst16=0, @MethodConst17=0, @MethodConst18=0, @MethodConst19=0, @MethodConst20=0, @MethodConst21=0, @MethodConst22=0, @MethodConst23=0, @MethodConst24=0, @MethodConst25=0]
[2025-07-30 17:15:09.113 +08:00 ERR] BMS.Sugar.DbContext: 数据库操作发生错误: Unknown column 'e.class_id' in 'field list'
SqlSugar.SqlSugarException: Unknown column 'e.class_id' in 'field list'
[2025-07-30 17:15:09.143 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-30 17:15:09.144 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-30 17:15:09.146 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 17:15:09.179 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-30 17:15:09.180 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-30 17:15:09.182 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment_type`  
[2025-07-30 17:15:09.219 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment_type`  
[2025-07-30 17:15:09.220 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `id` AS `Id` , `equip_type_id` AS `EquipTypeId` , `type_name` AS `TypeName` , `description` AS `Description` , IFNULL(`sort_order`,@MethodConst3) AS `SortOrder` , IFNULL(CAST(`is_active` as SIGNED),@MethodConst4) AS `IsActive` , `create_time` AS `CreateTime` , `update_time` AS `UpdateTime` , @constant5 AS `EquipmentCount`  FROM `equipment_type` `et`    ORDER BY `sort_order` ASC,`id` ASC LIMIT 0,10 | 参数: [@MethodConst0=0, @MethodConst1=True, @constant2=0, @MethodConst3=0, @MethodConst4=True, @constant5=0]
[2025-07-30 17:15:09.250 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `id` AS `Id` , `equip_type_id` AS `EquipTypeId` , `type_name` AS `TypeName` , `description` AS `Description` , IFNULL(`sort_order`,@MethodConst3) AS `SortOrder` , IFNULL(CAST(`is_active` as SIGNED),@MethodConst4) AS `IsActive` , `create_time` AS `CreateTime` , `update_time` AS `UpdateTime` , @constant5 AS `EquipmentCount`  FROM `equipment_type` `et`    ORDER BY `sort_order` ASC,`id` ASC LIMIT 0,10
[2025-07-30 17:15:09.252 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=1]
[2025-07-30 17:15:09.281 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-07-30 17:15:09.284 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=2]
[2025-07-30 17:15:09.311 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-07-30 17:15:09.314 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=3]
[2025-07-30 17:15:09.342 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-07-30 17:15:09.345 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=4]
[2025-07-30 17:15:09.382 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
