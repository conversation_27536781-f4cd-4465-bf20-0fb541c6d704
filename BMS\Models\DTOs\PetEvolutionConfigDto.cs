using System.ComponentModel.DataAnnotations;

namespace BMS.Models.DTOs
{
    /// <summary>
    /// 宠物进化配置查询DTO
    /// </summary>
    public class PetEvolutionConfigQueryDto : PagedQueryDto
    {
        /// <summary>
        /// 宠物编号
        /// </summary>
        public int? PetNo { get; set; }

        /// <summary>
        /// 宠物名称
        /// </summary>
        public string? PetName { get; set; }

        /// <summary>
        /// 进化类型
        /// </summary>
        public string? EvolutionType { get; set; }

        /// <summary>
        /// 目标宠物编号
        /// </summary>
        public int? TargetPetNo { get; set; }

        /// <summary>
        /// 目标宠物名称
        /// </summary>
        public string? TargetPetName { get; set; }

        /// <summary>
        /// 是否激活
        /// </summary>
        public bool? IsActive { get; set; }

        /// <summary>
        /// 最小等级要求
        /// </summary>
        public int? MinRequiredLevel { get; set; }

        /// <summary>
        /// 最大等级要求
        /// </summary>
        public int? MaxRequiredLevel { get; set; }
    }

    /// <summary>
    /// 宠物进化配置创建DTO
    /// </summary>
    public class PetEvolutionConfigCreateDto
    {
        /// <summary>
        /// 宠物编号（关联pet_config.pet_no）
        /// </summary>
        [Required(ErrorMessage = "宠物编号不能为空")]
        public int PetNo { get; set; }

        /// <summary>
        /// 进化路线类型（A/B）
        /// </summary>
        [Required(ErrorMessage = "进化类型不能为空")]
        [RegularExpression("^[AB]$", ErrorMessage = "进化类型只能是A或B")]
        public string EvolutionType { get; set; } = string.Empty;

        /// <summary>
        /// 进化后宠物编号
        /// </summary>
        [Required(ErrorMessage = "目标宠物编号不能为空")]
        public int TargetPetNo { get; set; }

        /// <summary>
        /// 所需等级
        /// </summary>
        [Range(1, 999, ErrorMessage = "所需等级必须在1-999之间")]
        public int RequiredLevel { get; set; } = 40;

        /// <summary>
        /// 所需道具ID
        /// </summary>
        public string? RequiredItemId { get; set; }

        /// <summary>
        /// 所需道具数量
        /// </summary>
        [Range(0, int.MaxValue, ErrorMessage = "所需道具数量不能为负数")]
        public int RequiredItemCount { get; set; } = 1;

        /// <summary>
        /// 消耗金币
        /// </summary>
        [Range(0, long.MaxValue, ErrorMessage = "消耗金币不能为负数")]
        public long CostGold { get; set; } = 1000;

        /// <summary>
        /// 最小成长加成
        /// </summary>
        [Range(0, 9.999, ErrorMessage = "最小成长加成必须在0-9.999之间")]
        public decimal GrowthMin { get; set; } = 0.100m;

        /// <summary>
        /// 最大成长加成
        /// </summary>
        [Range(0, 9.999, ErrorMessage = "最大成长加成必须在0-9.999之间")]
        public decimal GrowthMax { get; set; } = 0.500m;

        /// <summary>
        /// 成功率(%)
        /// </summary>
        [Range(0, 100, ErrorMessage = "成功率必须在0-100之间")]
        public decimal SuccessRate { get; set; } = 100.00m;

        /// <summary>
        /// 是否激活
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// 说明
        /// </summary>
        [MaxLength(50, ErrorMessage = "说明长度不能超过50个字符")]
        public string? Description { get; set; }
    }

    /// <summary>
    /// 宠物进化配置更新DTO
    /// </summary>
    public class PetEvolutionConfigUpdateDto : PetEvolutionConfigCreateDto
    {
        /// <summary>
        /// 配置ID
        /// </summary>
        [Required(ErrorMessage = "配置ID不能为空")]
        public int Id { get; set; }
    }

    /// <summary>
    /// 宠物进化配置详情DTO
    /// </summary>
    public class PetEvolutionConfigDetailDto
    {
        /// <summary>
        /// 配置ID
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 宠物编号
        /// </summary>
        public int PetNo { get; set; }

        /// <summary>
        /// 宠物名称
        /// </summary>
        public string PetName { get; set; } = string.Empty;

        /// <summary>
        /// 宠物属性
        /// </summary>
        public string PetAttribute { get; set; } = string.Empty;

        /// <summary>
        /// 进化类型
        /// </summary>
        public string EvolutionType { get; set; } = string.Empty;

        /// <summary>
        /// 目标宠物编号
        /// </summary>
        public int TargetPetNo { get; set; }

        /// <summary>
        /// 目标宠物名称
        /// </summary>
        public string TargetPetName { get; set; } = string.Empty;

        /// <summary>
        /// 目标宠物属性
        /// </summary>
        public string TargetPetAttribute { get; set; } = string.Empty;

        /// <summary>
        /// 所需等级
        /// </summary>
        public int RequiredLevel { get; set; }

        /// <summary>
        /// 所需道具ID
        /// </summary>
        public string? RequiredItemId { get; set; }

        /// <summary>
        /// 所需道具名称
        /// </summary>
        public string? RequiredItemName { get; set; }

        /// <summary>
        /// 所需道具数量
        /// </summary>
        public int RequiredItemCount { get; set; }

        /// <summary>
        /// 消耗金币
        /// </summary>
        public long CostGold { get; set; }

        /// <summary>
        /// 最小成长加成
        /// </summary>
        public decimal GrowthMin { get; set; }

        /// <summary>
        /// 最大成长加成
        /// </summary>
        public decimal GrowthMax { get; set; }

        /// <summary>
        /// 成功率(%)
        /// </summary>
        public decimal SuccessRate { get; set; }

        /// <summary>
        /// 是否激活
        /// </summary>
        public bool IsActive { get; set; }

        /// <summary>
        /// 说明
        /// </summary>
        public string? Description { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateTime { get; set; }
    }

    /// <summary>
    /// 宠物配置选项DTO
    /// </summary>
    public class PetConfigOptionDto
    {
        /// <summary>
        /// 宠物编号
        /// </summary>
        public int PetNo { get; set; }

        /// <summary>
        /// 宠物名称
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 宠物属性
        /// </summary>
        public string Attribute { get; set; } = string.Empty;

        /// <summary>
        /// 显示标签
        /// </summary>
        public string Label => $"{Name} ({Attribute}) - {PetNo}";

        /// <summary>
        /// 值
        /// </summary>
        public int Value => PetNo;
    }

    /// <summary>
    /// 道具配置选项DTO
    /// </summary>
    public class ItemConfigOptionDto
    {
        /// <summary>
        /// 道具ID
        /// </summary>
        public string ItemId { get; set; } = string.Empty;

        /// <summary>
        /// 道具名称
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 道具类型
        /// </summary>
        public string Type { get; set; } = string.Empty;

        /// <summary>
        /// 显示标签
        /// </summary>
        public string Label => $"{Name} ({Type}) - {ItemId}";

        /// <summary>
        /// 值
        /// </summary>
        public string Value => ItemId;
    }

    /// <summary>
    /// 删除宠物进化配置DTO
    /// </summary>
    public class PetEvolutionConfigDeleteDto
    {
        /// <summary>
        /// ID
        /// </summary>
        [Required(ErrorMessage = "ID不能为空")]
        public int Id { get; set; }
    }

    /// <summary>
    /// 切换激活状态DTO
    /// </summary>
    public class PetEvolutionConfigToggleDto
    {
        /// <summary>
        /// ID
        /// </summary>
        [Required(ErrorMessage = "ID不能为空")]
        public int Id { get; set; }

        /// <summary>
        /// 是否激活
        /// </summary>
        public bool IsActive { get; set; }
    }
}
