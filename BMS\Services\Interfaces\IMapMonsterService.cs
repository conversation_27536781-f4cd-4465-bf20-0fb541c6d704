using BMS.Models.DTOs;
using BMS.Models.Common;

namespace BMS.Services.Interfaces
{
    /// <summary>
    /// 地图怪物服务接口
    /// </summary>
    public interface IMapMonsterService
    {
        /// <summary>
        /// 分页获取地图怪物列表
        /// </summary>
        /// <param name="queryDto">查询条件</param>
        /// <returns>分页结果</returns>
        Task<PagedResult<MapMonsterDto>> GetPagedListAsync(MapMonsterQueryDto queryDto);

        /// <summary>
        /// 根据ID获取地图怪物
        /// </summary>
        /// <param name="id">地图怪物ID</param>
        /// <returns>地图怪物信息</returns>
        Task<MapMonsterDto?> GetByIdAsync(int id);

        /// <summary>
        /// 根据地图ID获取怪物列表
        /// </summary>
        /// <param name="mapId">地图ID</param>
        /// <returns>怪物列表</returns>
        Task<List<MapMonsterDto>> GetByMapIdAsync(int mapId);

        /// <summary>
        /// 创建地图怪物
        /// </summary>
        /// <param name="createDto">创建DTO</param>
        /// <returns>操作结果</returns>
        Task<ApiResult<bool>> CreateAsync(MapMonsterCreateDto createDto);

        /// <summary>
        /// 更新地图怪物
        /// </summary>
        /// <param name="updateDto">更新DTO</param>
        /// <returns>操作结果</returns>
        Task<ApiResult<bool>> UpdateAsync(MapMonsterUpdateDto updateDto);

        /// <summary>
        /// 删除地图怪物
        /// </summary>
        /// <param name="id">地图怪物ID</param>
        /// <returns>操作结果</returns>
        Task<ApiResult<bool>> DeleteAsync(int id);

        /// <summary>
        /// 根据地图ID删除所有怪物
        /// </summary>
        /// <param name="mapId">地图ID</param>
        /// <returns>操作结果</returns>
        Task<ApiResult<bool>> DeleteByMapIdAsync(int mapId);

        /// <summary>
        /// 检查地图怪物是否存在
        /// </summary>
        /// <param name="mapId">地图ID</param>
        /// <param name="monsterId">怪物序号</param>
        /// <param name="id">排除的ID（编辑时使用）</param>
        /// <returns>是否存在</returns>
        Task<bool> CheckMapMonsterExistsAsync(int mapId, int monsterId, int? id = null);
    }
} 