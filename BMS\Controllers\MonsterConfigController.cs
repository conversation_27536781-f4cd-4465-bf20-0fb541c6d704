using Microsoft.AspNetCore.Mvc;
using BMS.Models.DTOs;
using BMS.Services.Interfaces;
using Microsoft.AspNetCore.Authorization;

namespace BMS.Controllers
{
    /// <summary>
    /// 怪物配置控制器
    /// </summary>
    [Authorize]
    public class MonsterConfigController : Controller
    {
        private readonly IMonsterConfigService _monsterConfigService;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="monsterConfigService">怪物配置服务</param>
        public MonsterConfigController(IMonsterConfigService monsterConfigService)
        {
            _monsterConfigService = monsterConfigService;
        }

        /// <summary>
        /// 怪物配置列表页面
        /// </summary>
        /// <returns>视图</returns>
        public IActionResult Index()
        {
            return View();
        }

        /// <summary>
        /// 获取怪物配置列表（API）
        /// </summary>
        /// <param name="queryDto">查询条件</param>
        /// <returns>分页结果</returns>
        [HttpPost]
        public async Task<IActionResult> GetList([FromBody] MonsterConfigQueryDto queryDto)
        {
            try
            {
                var result = await _monsterConfigService.GetPagedListAsync(queryDto);
                return Json(new { success = true, data = result });
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = ex.Message });
            }
        }

        /// <summary>
        /// 获取怪物配置详情（API）
        /// </summary>
        /// <param name="id">怪物配置ID</param>
        /// <returns>怪物配置信息</returns>
        [HttpGet]
        public async Task<IActionResult> GetById(int id)
        {
            try
            {
                var result = await _monsterConfigService.GetByIdAsync(id);
                if (result != null)
                {
                    return Json(new { success = true, data = result });
                }
                else
                {
                    return Json(new { success = false, message = "怪物配置不存在" });
                }
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = ex.Message });
            }
        }

        /// <summary>
        /// 创建怪物配置（API）
        /// </summary>
        /// <param name="createDto">创建DTO</param>
        /// <returns>操作结果</returns>
        [HttpPost]
        public async Task<IActionResult> Create([FromBody] MonsterConfigCreateDto createDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    var errors = ModelState
                        .Where(x => x.Value?.Errors.Count > 0)
                        .Select(x => x.Value?.Errors.First().ErrorMessage)
                        .ToList();
                    return Json(new { success = false, message = string.Join("; ", errors) });
                }

                var result = await _monsterConfigService.CreateAsync(createDto);
                return Json(new { success = result.Success, message = result.Message });
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = ex.Message });
            }
        }

        /// <summary>
        /// 更新怪物配置（API）
        /// </summary>
        /// <param name="updateDto">更新DTO</param>
        /// <returns>操作结果</returns>
        [HttpPost]
        public async Task<IActionResult> Update([FromBody] MonsterConfigUpdateDto updateDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    var errors = ModelState
                        .Where(x => x.Value?.Errors.Count > 0)
                        .Select(x => x.Value?.Errors.First().ErrorMessage)
                        .ToList();
                    return Json(new { success = false, message = string.Join("; ", errors) });
                }

                var result = await _monsterConfigService.UpdateAsync(updateDto);
                return Json(new { success = result.Success, message = result.Message });
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = ex.Message });
            }
        }

        /// <summary>
        /// 删除怪物配置（API）
        /// </summary>
        /// <param name="deleteDto">删除DTO</param>
        /// <returns>操作结果</returns>
        [HttpPost]
        public async Task<IActionResult> Delete([FromBody] MonsterConfigDeleteDto deleteDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    var errors = ModelState
                        .Where(x => x.Value?.Errors.Count > 0)
                        .Select(x => x.Value?.Errors.First().ErrorMessage)
                        .ToList();
                    return Json(new { success = false, message = string.Join("; ", errors) });
                }

                var result = await _monsterConfigService.DeleteAsync(deleteDto.Id);
                return Json(new { success = result.Success, message = result.Message });
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = ex.Message });
            }
        }

        /// <summary>
        /// 检查怪物编号是否存在（API）
        /// </summary>
        /// <param name="monsterNo">怪物编号</param>
        /// <param name="id">排除的ID</param>
        /// <returns>检查结果</returns>
        [HttpGet]
        public async Task<IActionResult> CheckMonsterNoExists(int monsterNo, int? id = null)
        {
            try
            {
                var exists = await _monsterConfigService.CheckMonsterNoExistsAsync(monsterNo, id);
                return Json(new { success = true, exists = exists });
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = ex.Message });
            }
        }

        /// <summary>
        /// 获取所有怪物配置（API）
        /// </summary>
        /// <returns>怪物配置列表</returns>
        [HttpGet]
        public async Task<IActionResult> GetAll()
        {
            try
            {
                var result = await _monsterConfigService.GetAllAsync();
                return Json(new { success = true, data = result });
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = ex.Message });
            }
        }
    }
} 