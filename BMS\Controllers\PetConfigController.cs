using Microsoft.AspNetCore.Mvc;
using BMS.Models.DTOs;
using BMS.Services.Interfaces;
using Microsoft.AspNetCore.Authorization;

namespace BMS.Controllers
{
    /// <summary>
    /// 宠物配置控制器
    /// </summary>
    [Authorize]
    public class PetConfigController : Controller
    {
        private readonly IPetConfigService _petConfigService;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="petConfigService">宠物配置服务</param>
        public PetConfigController(IPetConfigService petConfigService)
        {
            _petConfigService = petConfigService;
        }

        /// <summary>
        /// 宠物配置列表页面
        /// </summary>
        /// <returns>视图</returns>
        public IActionResult Index()
        {
            return View();
        }

        /// <summary>
        /// 获取宠物配置列表（API）
        /// </summary>
        /// <param name="queryDto">查询条件</param>
        /// <returns>分页结果</returns>
        [HttpPost]
        public async Task<IActionResult> GetList([FromBody] PetConfigQueryDto queryDto)
        {
            try
            {
                var result = await _petConfigService.GetPagedListAsync(queryDto);
                return Json(new { success = true, data = result });
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = ex.Message });
            }
        }

        /// <summary>
        /// 获取宠物配置详情（API）
        /// </summary>
        /// <param name="id">宠物配置ID</param>
        /// <returns>宠物配置信息</returns>
        [HttpGet]
        public async Task<IActionResult> GetById(int id)
        {
            try
            {
                var result = await _petConfigService.GetByIdAsync(id);
                if (result != null)
                {
                    return Json(new { success = true, data = result });
                }
                else
                {
                    return Json(new { success = false, message = "宠物配置不存在" });
                }
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = ex.Message });
            }
        }

        /// <summary>
        /// 创建宠物配置（API）
        /// </summary>
        /// <param name="createDto">创建DTO</param>
        /// <returns>操作结果</returns>
        [HttpPost]
        public async Task<IActionResult> Create([FromBody] PetConfigCreateDto createDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    var errors = ModelState
                        .Where(x => x.Value?.Errors.Count > 0)
                        .Select(x => x.Value?.Errors.First().ErrorMessage)
                        .ToList();
                    return Json(new { success = false, message = string.Join("; ", errors) });
                }

                var result = await _petConfigService.CreateAsync(createDto);
                return Json(new { success = result.Success, message = result.Message });
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = ex.Message });
            }
        }

        /// <summary>
        /// 更新宠物配置（API）
        /// </summary>
        /// <param name="updateDto">更新DTO</param>
        /// <returns>操作结果</returns>
        [HttpPost]
        public async Task<IActionResult> Update([FromBody] PetConfigUpdateDto updateDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    var errors = ModelState
                        .Where(x => x.Value?.Errors.Count > 0)
                        .Select(x => x.Value?.Errors.First().ErrorMessage)
                        .ToList();
                    return Json(new { success = false, message = string.Join("; ", errors) });
                }

                var result = await _petConfigService.UpdateAsync(updateDto);
                return Json(new { success = result.Success, message = result.Message });
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = ex.Message });
            }
        }

        /// <summary>
        /// 删除宠物配置（API）
        /// </summary>
        /// <param name="deleteDto">删除DTO</param>
        /// <returns>操作结果</returns>
        [HttpPost]
        public async Task<IActionResult> Delete([FromBody] PetConfigDeleteDto deleteDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    var errors = ModelState
                        .Where(x => x.Value?.Errors.Count > 0)
                        .Select(x => x.Value?.Errors.First().ErrorMessage)
                        .ToList();
                    return Json(new { success = false, message = string.Join("; ", errors) });
                }

                var result = await _petConfigService.DeleteAsync(deleteDto.Id);
                return Json(new { success = result.Success, message = result.Message });
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = ex.Message });
            }
        }

        /// <summary>
        /// 检查宠物编号是否存在（API）
        /// </summary>
        /// <param name="petNo">宠物编号</param>
        /// <param name="id">排除的ID</param>
        /// <returns>检查结果</returns>
        [HttpGet]
        public async Task<IActionResult> CheckPetNoExists(int petNo, int? id = null)
        {
            try
            {
                var exists = await _petConfigService.CheckPetNoExistsAsync(petNo, id);
                return Json(new { success = true, exists = exists });
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = ex.Message });
            }
        }
    }
} 