using BMS.Models.DTOs;
using BMS.Models.Common;
using BMS.Models.Entities;
using BMS.Services.Interfaces;
using SqlSugar;

namespace BMS.Services.Implementations
{
    /// <summary>
    /// 地图配置服务实现
    /// </summary>
    public class MapConfigService : IMapConfigService
    {
        private readonly IDbService _dbService;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="dbService">数据库服务</param>
        public MapConfigService(IDbService dbService)
        {
            _dbService = dbService;
        }

        /// <summary>
        /// 分页获取地图配置列表
        /// </summary>
        /// <param name="queryDto">查询条件</param>
        /// <returns>分页结果</returns>
        public async Task<PagedResult<MapConfigDto>> GetPagedListAsync(MapConfigQueryDto queryDto)
        {
            var query = _dbService.Queryable<map_config>();

            // 添加查询条件
            if (queryDto.MapId.HasValue)
            {
                query = query.Where(x => x.map_id == queryDto.MapId.Value);
            }

            if (!string.IsNullOrEmpty(queryDto.MapName))
            {
                query = query.Where(x => x.map_name.Contains(queryDto.MapName));
            }

            if (queryDto.MapType.HasValue)
            {
                query = query.Where(x => x.map_type == queryDto.MapType.Value);
            }

            if (!string.IsNullOrEmpty(queryDto.AtlastName))
            {
                query = query.Where(x => x.atlast_name.Contains(queryDto.AtlastName));
            }

            // 获取总数
            var totalCount = await query.CountAsync();

            // 分页查询
            var items = await query
                .OrderBy(x => x.id)
                .Skip((queryDto.Page - 1) * queryDto.PageSize)
                .Take(queryDto.PageSize)
                .ToListAsync();

            // 转换为DTO
            var dtoList = items.Select(ConvertToDto).ToList();

            return new PagedResult<MapConfigDto>(dtoList, queryDto.Page, queryDto.PageSize, totalCount);
        }

        /// <summary>
        /// 根据ID获取地图配置
        /// </summary>
        /// <param name="id">地图配置ID</param>
        /// <returns>地图配置信息</returns>
        public async Task<MapConfigDto?> GetByIdAsync(int id)
        {
            var entity = await _dbService.Queryable<map_config>()
                .FirstAsync(x => x.id == id);

            return entity != null ? ConvertToDto(entity) : null;
        }

        /// <summary>
        /// 根据地图ID获取地图配置
        /// </summary>
        /// <param name="mapId">地图ID</param>
        /// <returns>地图配置信息</returns>
        public async Task<MapConfigDto?> GetByMapIdAsync(int mapId)
        {
            var entity = await _dbService.Queryable<map_config>()
                .FirstAsync(x => x.map_id == mapId);

            return entity != null ? ConvertToDto(entity) : null;
        }

        /// <summary>
        /// 创建地图配置
        /// </summary>
        /// <param name="createDto">创建DTO</param>
        /// <returns>操作结果</returns>
        public async Task<ApiResult<bool>> CreateAsync(MapConfigCreateDto createDto)
        {
            try
            {
                // 检查地图ID是否已存在
                var exists = await CheckMapIdExistsAsync(createDto.MapId);
                if (exists)
                {
                    return ApiResult<bool>.Fail("地图ID已存在");
                }

                // 转换为实体
                var entity = ConvertToEntity(createDto);

                // 插入数据
                var result = await _dbService.Insertable(entity)
                    .ExecuteReturnBigIdentityAsync();

                if (result > 0)
                {
                    return ApiResult<bool>.Ok(true, "地图配置创建成功");
                }
                else
                {
                    return ApiResult<bool>.Fail("地图配置创建失败");
                }
            }
            catch (Exception ex)
            {
                return ApiResult<bool>.Fail($"创建地图配置时发生错误：{ex.Message}");
            }
        }

        /// <summary>
        /// 更新地图配置
        /// </summary>
        /// <param name="updateDto">更新DTO</param>
        /// <returns>操作结果</returns>
        public async Task<ApiResult<bool>> UpdateAsync(MapConfigUpdateDto updateDto)
        {
            try
            {
                // 检查记录是否存在
                var exists = await _dbService.Queryable<map_config>()
                    .AnyAsync(x => x.id == updateDto.Id);
                if (!exists)
                {
                    return ApiResult<bool>.Fail("地图配置不存在");
                }

                // 检查地图ID是否被其他记录使用
                var mapIdExists = await CheckMapIdExistsAsync(updateDto.MapId, updateDto.Id);
                if (mapIdExists)
                {
                    return ApiResult<bool>.Fail("地图ID已被其他记录使用");
                }

                // 转换为实体
                var entity = ConvertToEntity(updateDto);

                // 更新数据
                var result = await _dbService.Updateable(entity)
                    .ExecuteCommandAsync();

                if (result > 0)
                {
                    return ApiResult<bool>.Ok(true, "地图配置更新成功");
                }
                else
                {
                    return ApiResult<bool>.Fail("地图配置更新失败");
                }
            }
            catch (Exception ex)
            {
                return ApiResult<bool>.Fail($"更新地图配置时发生错误：{ex.Message}");
            }
        }

        /// <summary>
        /// 删除地图配置
        /// </summary>
        /// <param name="id">地图配置ID</param>
        /// <returns>操作结果</returns>
        public async Task<ApiResult<bool>> DeleteAsync(int id)
        {
            try
            {
                // 检查记录是否存在
                var exists = await _dbService.Queryable<map_config>()
                    .AnyAsync(x => x.id == id);
                if (!exists)
                {
                    return ApiResult<bool>.Fail("地图配置不存在");
                }

                // 删除数据
                var result = await _dbService.Deleteable<map_config>()
                    .Where(x => x.id == id)
                    .ExecuteCommandAsync();

                if (result > 0)
                {
                    return ApiResult<bool>.Ok(true, "地图配置删除成功");
                }
                else
                {
                    return ApiResult<bool>.Fail("地图配置删除失败");
                }
            }
            catch (Exception ex)
            {
                return ApiResult<bool>.Fail($"删除地图配置时发生错误：{ex.Message}");
            }
        }

        /// <summary>
        /// 检查地图ID是否存在
        /// </summary>
        /// <param name="mapId">地图ID</param>
        /// <param name="id">排除的ID（编辑时使用）</param>
        /// <returns>是否存在</returns>
        public async Task<bool> CheckMapIdExistsAsync(int mapId, int? id = null)
        {
            var query = _dbService.Queryable<map_config>()
                .Where(x => x.map_id == mapId);

            if (id.HasValue)
            {
                query = query.Where(x => x.id != id.Value);
            }

            return await query.AnyAsync();
        }

        /// <summary>
        /// 获取所有地图配置（用于下拉选择）
        /// </summary>
        /// <returns>地图配置列表</returns>
        public async Task<List<MapConfigDto>> GetAllAsync()
        {
            var entities = await _dbService.Queryable<map_config>()
                .OrderBy(x => x.map_id)
                .ToListAsync();

            return entities.Select(ConvertToDto).ToList();
        }

        /// <summary>
        /// 实体转换为DTO
        /// </summary>
        /// <param name="entity">实体</param>
        /// <returns>DTO</returns>
        private static MapConfigDto ConvertToDto(map_config entity)
        {
            return new MapConfigDto
            {
                Id = entity.id,
                MapId = entity.map_id,
                MapName = entity.map_name ?? string.Empty,
                MapDesc = entity.map_desc,
                MapSize = entity.map_size ?? 0,
                MapType = entity.map_type ?? 0,
                AtlastName = entity.atlast_name,
                Background = entity.background,
                Bgm = entity.bgm,
                BgmLoop = entity.bgm_loop ?? false,
                BgmVolume = entity.bgm_volume ?? 1.00m,
                BgmPlay = entity.bgm_play ?? true,
                BgmMute = entity.bgm_mute ?? false,
                BgmPause = entity.bgm_pause ?? false,
                Ico = entity.ico,
                Type = entity.type ?? 0
            };
        }

        /// <summary>
        /// 创建DTO转换为实体
        /// </summary>
        /// <param name="createDto">创建DTO</param>
        /// <returns>实体</returns>
        private static map_config ConvertToEntity(MapConfigCreateDto createDto)
        {
            return new map_config
            {
                map_id = createDto.MapId,
                map_name = createDto.MapName,
                map_desc = createDto.MapDesc,
                map_size = createDto.MapSize,
                map_type = createDto.MapType,
                atlast_name = createDto.AtlastName,
                background = createDto.Background,
                bgm = createDto.Bgm,
                bgm_loop = createDto.BgmLoop,
                bgm_volume = createDto.BgmVolume,
                bgm_play = createDto.BgmPlay,
                bgm_mute = createDto.BgmMute,
                bgm_pause = createDto.BgmPause,
                ico = createDto.Ico,
                type = createDto.Type
            };
        }

        /// <summary>
        /// 更新DTO转换为实体
        /// </summary>
        /// <param name="updateDto">更新DTO</param>
        /// <returns>实体</returns>
        private static map_config ConvertToEntity(MapConfigUpdateDto updateDto)
        {
            return new map_config
            {
                id = updateDto.Id,
                map_id = updateDto.MapId,
                map_name = updateDto.MapName,
                map_desc = updateDto.MapDesc,
                map_size = updateDto.MapSize,
                map_type = updateDto.MapType,
                atlast_name = updateDto.AtlastName,
                background = updateDto.Background,
                bgm = updateDto.Bgm,
                bgm_loop = updateDto.BgmLoop,
                bgm_volume = updateDto.BgmVolume,
                bgm_play = updateDto.BgmPlay,
                bgm_mute = updateDto.BgmMute,
                bgm_pause = updateDto.BgmPause,
                ico = updateDto.Ico,
                type = updateDto.Type
            };
        }
    }
} 