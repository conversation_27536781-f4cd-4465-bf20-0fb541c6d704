using Microsoft.AspNetCore.Mvc;
using BMS.Models.DTOs;
using BMS.Services.Interfaces;
using BMS.Models.Common;
using Microsoft.AspNetCore.Authorization;

namespace BMS.Controllers
{
    /// <summary>
    /// 装备控制器
    /// </summary>
    // [Authorize] // 临时注释掉，用于测试接口
    [Route("[controller]/[action]")]
    public class EquipmentController : Controller
    {
        private readonly IEquipmentService _equipmentService;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="equipmentService">装备服务</param>
        public EquipmentController(IEquipmentService equipmentService)
        {
            _equipmentService = equipmentService;
        }

        /// <summary>
        /// 装备管理首页
        /// </summary>
        /// <returns>首页视图</returns>
        [HttpGet]
        public async Task<IActionResult> Index()
        {
            try
            {
                // 获取装备类型和五行属性用于前端下拉选择
                var equipmentTypes = await _equipmentService.GetEquipmentTypesAsync();
                var elements = await _equipmentService.GetElementsAsync();
                var elementLimits = await _equipmentService.GetElementLimitsAsync();

                ViewBag.EquipmentTypes = equipmentTypes;
                ViewBag.Elements = elements;
                ViewBag.ElementLimits = elementLimits;

                return View();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"装备管理首页加载出错：{ex.Message}");
                return View();
            }
        }

        /// <summary>
        /// 分页查询装备列表
        /// </summary>
        /// <param name="queryDto">查询条件</param>
        /// <returns>分页结果</returns>
        [HttpPost]
        public async Task<IActionResult> GetList([FromBody] EquipmentQueryDto queryDto)
        {
            try
            {
                Console.WriteLine($"接收到的查询参数：EquipId={queryDto?.EquipId}, Name={queryDto?.Name}, EquipTypeId={queryDto?.EquipTypeId}, Element={queryDto?.Element}, Page={queryDto?.Page}, PageSize={queryDto?.PageSize}");

                if (queryDto == null)
                {
                    Console.WriteLine("查询参数为null，使用默认参数");
                    queryDto = new EquipmentQueryDto { Page = 1, PageSize = 10 };
                }

                var result = await _equipmentService.GetPagedListAsync(queryDto);
                Console.WriteLine($"服务层返回结果：Data.Count={result.Data?.Count ?? 0}, TotalCount={result.TotalCount}");

                return Json(new { code = 200, data = result.Data, total = result.TotalCount });
            }
            catch (Exception ex)
            {
                Console.WriteLine($"查询装备列表出错：{ex.Message}");
                Console.WriteLine($"异常堆栈：{ex.StackTrace}");
                return Json(new { code = 500, message = "查询装备列表失败", data = new List<EquipmentDto>(), total = 0 });
            }
        }

        /// <summary>
        /// 测试装备数据接口
        /// </summary>
        /// <returns>测试结果</returns>
        [HttpGet]
        public async Task<IActionResult> TestEquipmentData()
        {
            try
            {
                var result = await _equipmentService.TestEquipmentDataAsync();
                return Json(new { code = 200, data = result });
            }
            catch (Exception ex)
            {
                Console.WriteLine($"测试装备数据出错：{ex.Message}");
                return Json(new { code = 500, message = ex.Message });
            }
        }

        /// <summary>
        /// 根据ID获取装备详情
        /// </summary>
        /// <param name="equipId">装备ID</param>
        /// <returns>装备详情</returns>
        [HttpGet]
        public async Task<IActionResult> GetById(string equipId)
        {
            try
            {
                var equipment = await _equipmentService.GetByIdAsync(equipId);
                if (equipment == null)
                {
                    return Json(ApiResult.Fail("装备不存在"));
                }
                return Json(ApiResult.Ok(equipment, "获取装备详情成功"));
            }
            catch (Exception ex)
            {
                Console.WriteLine($"获取装备详情出错：{ex.Message}");
                return Json(ApiResult.Fail("获取装备详情失败"));
            }
        }

        /// <summary>
        /// 创建装备
        /// </summary>
        /// <param name="createDto">创建DTO</param>
        /// <returns>创建结果</returns>
        [HttpPost]
        public async Task<IActionResult> Create([FromBody] EquipmentCreateDto createDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    var errors = ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage);
                    return Json(ApiResult.Fail($"数据验证失败：{string.Join(", ", errors)}"));
                }

                var result = await _equipmentService.CreateAsync(createDto);
                return Json(result);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"创建装备出错：{ex.Message}");
                return Json(ApiResult.Fail("创建装备失败"));
            }
        }

        /// <summary>
        /// 更新装备
        /// </summary>
        /// <param name="updateDto">更新DTO</param>
        /// <returns>更新结果</returns>
        [HttpPost]
        public async Task<IActionResult> Update([FromBody] EquipmentUpdateDto updateDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    var errors = ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage);
                    return Json(ApiResult.Fail($"数据验证失败：{string.Join(", ", errors)}"));
                }

                var result = await _equipmentService.UpdateAsync(updateDto);
                return Json(result);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"更新装备出错：{ex.Message}");
                return Json(ApiResult.Fail("更新装备失败"));
            }
        }

        /// <summary>
        /// 删除装备
        /// </summary>
        /// <param name="deleteDto">删除DTO</param>
        /// <returns>删除结果</returns>
        [HttpPost]
        public async Task<IActionResult> Delete([FromBody] EquipmentDeleteDto deleteDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    var errors = ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage);
                    return Json(ApiResult.Fail($"数据验证失败：{string.Join(", ", errors)}"));
                }

                var result = await _equipmentService.DeleteAsync(deleteDto);
                return Json(result);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"删除装备出错：{ex.Message}");
                return Json(ApiResult.Fail("删除装备失败"));
            }
        }

        /// <summary>
        /// 检查装备ID是否存在
        /// </summary>
        /// <param name="equipId">装备ID</param>
        /// <returns>检查结果</returns>
        [HttpGet]
        public async Task<IActionResult> CheckEquipId(string equipId)
        {
            try
            {
                var exists = await _equipmentService.CheckEquipIdExistsAsync(equipId);
                return Json(new { exists });
            }
            catch (Exception ex)
            {
                Console.WriteLine($"检查装备ID出错：{ex.Message}");
                return Json(new { exists = false });
            }
        }

        // ==================== 装备类型管理 ====================

        /// <summary>
        /// 分页查询装备类型列表
        /// </summary>
        /// <param name="queryDto">查询条件</param>
        /// <returns>分页结果</returns>
        [HttpPost]
        public async Task<IActionResult> GetEquipmentTypeList([FromBody] EquipmentTypeQueryDto queryDto)
        {
            try
            {
                Console.WriteLine($"接收到的装备类型查询参数：EquipTypeId={queryDto?.EquipTypeId}, TypeName={queryDto?.TypeName}, Page={queryDto?.Page}, PageSize={queryDto?.PageSize}");
                
                if (queryDto == null)
                {
                    Console.WriteLine("查询参数为null，使用默认参数");
                    queryDto = new EquipmentTypeQueryDto { Page = 1, PageSize = 10 };
                }
                
                var result = await _equipmentService.GetEquipmentTypesPagedAsync(queryDto);
                return Json(new { code = 200, data = result.Data, total = result.TotalCount });
            }
            catch (Exception ex)
            {
                Console.WriteLine($"查询装备类型列表出错：{ex.Message}");
                return Json(new { code = 500, message = "查询装备类型列表失败", data = new List<EquipmentTypeDto>(), total = 0 });
            }
        }

        /// <summary>
        /// 根据ID获取装备类型详情
        /// </summary>
        /// <param name="equipTypeId">装备类型ID</param>
        /// <returns>装备类型详情</returns>
        [HttpGet]
        public async Task<IActionResult> GetEquipmentTypeById(string equipTypeId)
        {
            try
            {
                var equipmentType = await _equipmentService.GetEquipmentTypeByIdAsync(equipTypeId);
                if (equipmentType == null)
                {
                    return Json(ApiResult.Fail("装备类型不存在"));
                }
                return Json(ApiResult.Ok(equipmentType, "获取装备类型详情成功"));
            }
            catch (Exception ex)
            {
                Console.WriteLine($"获取装备类型详情出错：{ex.Message}");
                return Json(ApiResult.Fail("获取装备类型详情失败"));
            }
        }

        /// <summary>
        /// 创建装备类型
        /// </summary>
        /// <param name="createDto">创建DTO</param>
        /// <returns>创建结果</returns>
        [HttpPost]
        public async Task<IActionResult> CreateEquipmentType([FromBody] EquipmentTypeCreateDto createDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    var errors = ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage);
                    return Json(ApiResult.Fail($"数据验证失败：{string.Join(", ", errors)}"));
                }

                var result = await _equipmentService.CreateEquipmentTypeAsync(createDto);
                return Json(result);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"创建装备类型出错：{ex.Message}");
                return Json(ApiResult.Fail("创建装备类型失败"));
            }
        }

        /// <summary>
        /// 更新装备类型
        /// </summary>
        /// <param name="updateDto">更新DTO</param>
        /// <returns>更新结果</returns>
        [HttpPost]
        public async Task<IActionResult> UpdateEquipmentType([FromBody] EquipmentTypeUpdateDto updateDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    var errors = ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage);
                    return Json(ApiResult.Fail($"数据验证失败：{string.Join(", ", errors)}"));
                }

                var result = await _equipmentService.UpdateEquipmentTypeAsync(updateDto);
                return Json(result);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"更新装备类型出错：{ex.Message}");
                return Json(ApiResult.Fail("更新装备类型失败"));
            }
        }

        /// <summary>
        /// 删除装备类型
        /// </summary>
        /// <param name="deleteDto">删除DTO</param>
        /// <returns>删除结果</returns>
        [HttpPost]
        public async Task<IActionResult> DeleteEquipmentType([FromBody] EquipmentTypeDeleteDto deleteDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    var errors = ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage);
                    return Json(ApiResult.Fail($"数据验证失败：{string.Join(", ", errors)}"));
                }

                var result = await _equipmentService.DeleteEquipmentTypeAsync(deleteDto);
                return Json(result);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"删除装备类型出错：{ex.Message}");
                return Json(ApiResult.Fail("删除装备类型失败"));
            }
        }

        /// <summary>
        /// 检查装备类型ID是否存在
        /// </summary>
        /// <param name="equipTypeId">装备类型ID</param>
        /// <returns>检查结果</returns>
        [HttpGet]
        public async Task<IActionResult> CheckEquipmentTypeId(string equipTypeId)
        {
            try
            {
                var exists = await _equipmentService.CheckEquipmentTypeIdExistsAsync(equipTypeId);
                return Json(new { exists });
            }
            catch (Exception ex)
            {
                Console.WriteLine($"检查装备类型ID出错：{ex.Message}");
                return Json(new { exists = false });
            }
        }
    }
} 