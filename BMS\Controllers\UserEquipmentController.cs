using BMS.Models.Common;
using BMS.Models.DTOs;
using BMS.Services.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace BMS.Controllers
{
    /// <summary>
    /// 用户装备管理控制器
    /// </summary>
    [Authorize]
    [Route("[controller]")]
    public class UserEquipmentController : Controller
    {
        private readonly IUserEquipmentService _userEquipmentService;
        private readonly IUserService _userService;

        public UserEquipmentController(IUserEquipmentService userEquipmentService, IUserService userService)
        {
            _userEquipmentService = userEquipmentService;
            _userService = userService;
        }

        /// <summary>
        /// 用户装备管理首页
        /// </summary>
        /// <returns>用户装备管理页面</returns>
        [HttpGet]
        public IActionResult Index()
        {
            return View();
        }

        /// <summary>
        /// 获取用户装备列表（分页）
        /// </summary>
        /// <param name="queryDto">查询条件</param>
        /// <returns>分页结果</returns>
        [HttpPost("GetList")]
        public async Task<IActionResult> GetList([FromBody] UserEquipmentQueryDto queryDto)
        {
            try
            {
                Console.WriteLine($"UserEquipmentController.GetList 开始，参数: {System.Text.Json.JsonSerializer.Serialize(queryDto)}");

                if (queryDto == null)
                {
                    Console.WriteLine("查询参数为空，使用默认参数");
                    queryDto = new UserEquipmentQueryDto { Page = 1, PageSize = 10 };
                }

                // 详细输出各个参数
                Console.WriteLine($"参数详情 - UserId: {queryDto.UserId}, UserName: '{queryDto.UserName}', Name: '{queryDto.Name}'");
                Console.WriteLine($"参数详情 - EquipTypeId: '{queryDto.EquipTypeId}', IsEquipped: {queryDto.IsEquipped}");
                Console.WriteLine($"参数详情 - MinLevel: {queryDto.MinStrengthenLevel}, MaxLevel: {queryDto.MaxStrengthenLevel}");
                Console.WriteLine($"参数详情 - Page: {queryDto.Page}, PageSize: {queryDto.PageSize}");

                var result = await _userEquipmentService.GetUserEquipmentsAsync(queryDto);

                Console.WriteLine($"查询成功，返回 {result.Data?.Count ?? 0} 条记录");
                return Json(new { code = 200, data = result.Data, total = result.TotalCount });
            }
            catch (Exception ex)
            {
                Console.WriteLine($"UserEquipmentController.GetList 异常: {ex.Message}");
                Console.WriteLine($"异常堆栈: {ex.StackTrace}");
                return Json(new { code = 500, message = $"获取用户装备列表失败：{ex.Message}", detail = ex.StackTrace });
            }
        }

        /// <summary>
        /// 根据ID获取用户装备详情
        /// </summary>
        /// <param name="id">装备ID</param>
        /// <returns>装备详情</returns>
        [HttpGet("GetById/{id}")]
        public async Task<IActionResult> GetById(int id)
        {
            try
            {
                var equipment = await _userEquipmentService.GetUserEquipmentByIdAsync(id);
                if (equipment == null)
                {
                    return Json(ApiResult.Fail("装备不存在"));
                }

                return Json(ApiResult.Ok(equipment));
            }
            catch (Exception ex)
            {
                return Json(ApiResult.Fail($"获取装备详情失败：{ex.Message}"));
            }
        }

        /// <summary>
        /// 创建用户装备
        /// </summary>
        /// <param name="createDto">创建装备DTO</param>
        /// <returns>创建结果</returns>
        [HttpPost("Create")]
        public async Task<IActionResult> Create([FromBody] UserEquipmentCreateDto createDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    var errors = ModelState.Values
                        .SelectMany(v => v.Errors)
                        .Select(e => e.ErrorMessage);
                    return Json(ApiResult.Fail($"数据验证失败：{string.Join(",", errors)}"));
                }

                var result = await _userEquipmentService.CreateUserEquipmentAsync(createDto);
                return Json(result.Success ? ApiResult.Ok(result.Data, result.Message) : ApiResult.Fail(result.Message));
            }
            catch (Exception ex)
            {
                return Json(ApiResult.Fail($"创建装备失败：{ex.Message}"));
            }
        }

        /// <summary>
        /// 更新用户装备
        /// </summary>
        /// <param name="updateDto">更新装备DTO</param>
        /// <returns>更新结果</returns>
        [HttpPost("Update")]
        public async Task<IActionResult> Update([FromBody] UserEquipmentUpdateDto updateDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    var errors = ModelState.Values
                        .SelectMany(v => v.Errors)
                        .Select(e => e.ErrorMessage);
                    return Json(ApiResult.Fail($"数据验证失败：{string.Join(",", errors)}"));
                }

                var result = await _userEquipmentService.UpdateUserEquipmentAsync(updateDto);
                return Json(result.Success ? ApiResult.Ok(result.Data, result.Message) : ApiResult.Fail(result.Message));
            }
            catch (Exception ex)
            {
                return Json(ApiResult.Fail($"更新装备失败：{ex.Message}"));
            }
        }

        /// <summary>
        /// 删除用户装备
        /// </summary>
        /// <param name="deleteDto">删除装备DTO</param>
        /// <returns>删除结果</returns>
        [HttpPost("Delete")]
        public async Task<IActionResult> Delete([FromBody] UserEquipmentDeleteDto deleteDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    var errors = ModelState.Values
                        .SelectMany(v => v.Errors)
                        .Select(e => e.ErrorMessage);
                    return Json(ApiResult.Fail($"数据验证失败：{string.Join(",", errors)}"));
                }

                var result = await _userEquipmentService.DeleteUserEquipmentAsync(deleteDto);
                return Json(result.Success ? ApiResult.Ok(result.Data, result.Message) : ApiResult.Fail(result.Message));
            }
            catch (Exception ex)
            {
                return Json(ApiResult.Fail($"删除装备失败：{ex.Message}"));
            }
        }

        /// <summary>
        /// 强化装备
        /// </summary>
        /// <param name="strengthenDto">强化装备DTO</param>
        /// <returns>强化结果</returns>
        [HttpPost("Strengthen")]
        public async Task<IActionResult> Strengthen([FromBody] UserEquipmentStrengthenDto strengthenDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    var errors = ModelState.Values
                        .SelectMany(v => v.Errors)
                        .Select(e => e.ErrorMessage);
                    return Json(ApiResult.Fail($"数据验证失败：{string.Join(",", errors)}"));
                }

                var result = await _userEquipmentService.StrengthenEquipmentAsync(strengthenDto);
                return Json(result.Success ? ApiResult.Ok(result.Data, result.Message) : ApiResult.Fail(result.Message));
            }
            catch (Exception ex)
            {
                return Json(ApiResult.Fail($"强化装备失败：{ex.Message}"));
            }
        }

        /// <summary>
        /// 穿戴装备
        /// </summary>
        /// <param name="wearDto">穿戴装备DTO</param>
        /// <returns>穿戴结果</returns>
        [HttpPost("Wear")]
        public async Task<IActionResult> Wear([FromBody] UserEquipmentWearDto? wearDto)
        {
            try
            {
                Console.WriteLine($"Wear接口收到请求，参数: {(wearDto == null ? "null" : System.Text.Json.JsonSerializer.Serialize(wearDto))}");

                // 检查参数是否为空
                if (wearDto == null)
                {
                    Console.WriteLine("wearDto参数为空");
                    return Json(ApiResult.Fail("请求参数不能为空"));
                }

                // 如果 Position 为空，设置默认值并记录日志
                if (!wearDto.Position.HasValue)
                {
                    Console.WriteLine($"Position为空，使用默认值1。原始参数: ID={wearDto.Id}, UserId={wearDto.UserId}");
                }

                // 检查模型验证
                if (!ModelState.IsValid)
                {
                    var errors = ModelState.Values
                        .SelectMany(v => v.Errors)
                        .Select(e => e.ErrorMessage);
                    var errorMessage = string.Join(",", errors);
                    Console.WriteLine($"模型验证失败: {errorMessage}");
                    return Json(ApiResult.Fail($"数据验证失败：{errorMessage}"));
                }

                Console.WriteLine($"开始执行穿戴装备，ID: {wearDto.Id}, Position: {wearDto.Position}");
                var result = await _userEquipmentService.WearEquipmentAsync(wearDto);

                Console.WriteLine($"穿戴装备结果: {(result.Success ? "成功" : "失败")} - {result.Message}");
                return Json(result.Success ? ApiResult.Ok(result.Data, result.Message) : ApiResult.Fail(result.Message));
            }
            catch (Exception ex)
            {
                Console.WriteLine($"穿戴装备异常: {ex.Message}");
                Console.WriteLine($"异常堆栈: {ex.StackTrace}");
                return Json(ApiResult.Fail($"穿戴装备失败：{ex.Message}"));
            }
        }

        /// <summary>
        /// 测试穿戴装备（简化版本，用于调试）
        /// </summary>
        /// <param name="id">装备ID</param>
        /// <param name="position">装备位置</param>
        /// <returns>穿戴结果</returns>
        [HttpPost("WearTest")]
        public async Task<IActionResult> WearTest(int id, int position)
        {
            try
            {
                Console.WriteLine($"WearTest接口收到请求，ID: {id}, Position: {position}");

                var wearDto = new UserEquipmentWearDto
                {
                    Id = id,
                    Position = position
                };

                var result = await _userEquipmentService.WearEquipmentAsync(wearDto);
                return Json(result.Success ? ApiResult.Ok(result.Data, result.Message) : ApiResult.Fail(result.Message));
            }
            catch (Exception ex)
            {
                Console.WriteLine($"WearTest异常: {ex.Message}");
                return Json(ApiResult.Fail($"测试穿戴装备失败：{ex.Message}"));
            }
        }

        /// <summary>
        /// 脱下装备
        /// </summary>
        /// <param name="equipmentId">装备ID</param>
        /// <returns>脱下结果</returns>
        [HttpPost("Unwear")]
        public async Task<IActionResult> Unwear(int equipmentId)
        {
            try
            {
                var result = await _userEquipmentService.UnwearEquipmentAsync(equipmentId);
                return Json(result.Success ? ApiResult.Ok(result.Data, result.Message) : ApiResult.Fail(result.Message));
            }
            catch (Exception ex)
            {
                return Json(ApiResult.Fail($"脱下装备失败：{ex.Message}"));
            }
        }

        /// <summary>
        /// 获取装备类型选项
        /// </summary>
        /// <returns>装备类型选项列表</returns>
        [HttpGet("GetEquipmentTypeOptions")]
        public async Task<IActionResult> GetEquipmentTypeOptions()
        {
            try
            {
                var options = await _userEquipmentService.GetEquipmentTypeOptionsAsync();
                return Json(ApiResult.Ok(options));
            }
            catch (Exception ex)
            {
                return Json(ApiResult.Fail($"获取装备类型选项失败：{ex.Message}"));
            }
        }

        /// <summary>
        /// 获取装备配置选项
        /// </summary>
        /// <returns>装备配置选项列表</returns>
        [HttpGet("GetEquipmentConfigOptions")]
        public async Task<IActionResult> GetEquipmentConfigOptions()
        {
            try
            {
                var options = await _userEquipmentService.GetEquipmentConfigOptionsAsync();
                return Json(ApiResult.Ok(options));
            }
            catch (Exception ex)
            {
                return Json(ApiResult.Fail($"获取装备配置选项失败：{ex.Message}"));
            }
        }

        /// <summary>
        /// 获取用户已装备的装备列表
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>已装备的装备列表</returns>
        [HttpGet("GetUserEquippedItems/{userId}")]
        public async Task<IActionResult> GetUserEquippedItems(int userId)
        {
            try
            {
                var equipments = await _userEquipmentService.GetUserEquippedItemsAsync(userId);
                return Json(ApiResult.Ok(equipments));
            }
            catch (Exception ex)
            {
                return Json(ApiResult.Fail($"获取用户已装备列表失败：{ex.Message}"));
            }
        }

        /// <summary>
        /// 获取用户选项
        /// </summary>
        /// <returns>用户选项</returns>
        [HttpGet("GetUserOptions")]
        public async Task<IActionResult> GetUserOptions()
        {
            try
            {
                var queryDto = new UserQueryDto { Page = 1, PageSize = 1000 };
                var result = await _userService.GetUsersAsync(queryDto);
                var options = result.Data.Select(u => new OptionDto { Value = u.Id.ToString(), Label = u.Username }).ToList();
                return Json(ApiResult.Ok(options));
            }
            catch (Exception ex)
            {
                return Json(ApiResult.Fail($"获取用户选项失败：{ex.Message}"));
            }
        }
    }
} 