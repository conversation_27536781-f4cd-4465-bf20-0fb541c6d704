using BMS.Models.DTOs;
using BMS.Models.Entities;
using BMS.Models.Common;
using BMS.Services.Interfaces;
using SqlSugar;

namespace BMS.Services.Implementations
{
    /// <summary>
    /// 装备服务实现
    /// </summary>
    public class EquipmentService : IEquipmentService
    {
        /// <summary>
        /// 数据库服务
        /// </summary>
        private readonly IDbService _dbService;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="dbService">数据库服务</param>
        public EquipmentService(IDbService dbService)
        {
            _dbService = dbService;
        }

        /// <summary>
        /// 分页获取装备列表
        /// </summary>
        /// <param name="queryDto">查询条件</param>
        /// <returns>装备列表</returns>
        public async Task<PagedResult<EquipmentDto>> GetPagedListAsync(EquipmentQueryDto queryDto)
        {
            try
            {
                Console.WriteLine($"EquipmentService.GetPagedListAsync 开始执行，参数: {System.Text.Json.JsonSerializer.Serialize(queryDto)}");

                // 先检查equipment表中是否有数据
                var equipmentCount = await _dbService.GetClient().Queryable<equipment>().CountAsync();
                Console.WriteLine($"equipment表总记录数: {equipmentCount}");

                // 检查equipment_detail表中是否有数据
                var detailCount = await _dbService.GetClient().Queryable<equipment_detail>().CountAsync();
                Console.WriteLine($"equipment_detail表总记录数: {detailCount}");

                // 检查equipment_type表中是否有数据
                var typeCount = await _dbService.GetClient().Queryable<equipment_type>().CountAsync();
                Console.WriteLine($"equipment_type表总记录数: {typeCount}");

                var query = _dbService.GetClient().Queryable<equipment, equipment_detail, equipment_type>((e, ed, et) => new JoinQueryInfos(
                    JoinType.Left, e.equip_id == ed.equip_id,
                    JoinType.Left, e.equip_type_id == et.equip_type_id
                ));

                // 查询条件
                if (!string.IsNullOrEmpty(queryDto.EquipId))
                {
                    query = query.Where((e, ed, et) => e.equip_id.Contains(queryDto.EquipId));
                }

                if (!string.IsNullOrEmpty(queryDto.Name))
                {
                    query = query.Where((e, ed, et) => e.name.Contains(queryDto.Name));
                }

                if (!string.IsNullOrEmpty(queryDto.EquipTypeId))
                {
                    query = query.Where((e, ed, et) => e.equip_type_id == queryDto.EquipTypeId);
                }

                if (!string.IsNullOrEmpty(queryDto.Element))
                {
                    query = query.Where((e, ed, et) => e.element == queryDto.Element);
                }

                if (!string.IsNullOrEmpty(queryDto.ElementLimit))
                {
                    query = query.Where((e, ed, et) => ed.element_limit == queryDto.ElementLimit);
                }

                if (!string.IsNullOrEmpty(queryDto.SuitId))
                {
                    query = query.Where((e, ed, et) => e.suit_id == queryDto.SuitId);
                }

                if (!string.IsNullOrEmpty(queryDto.MainAttr))
                {
                    query = query.Where((e, ed, et) => ed.main_attr.Contains(queryDto.MainAttr));
                }

                if (!string.IsNullOrEmpty(queryDto.SubAttr))
                {
                    query = query.Where((e, ed, et) => ed.sub_attr.Contains(queryDto.SubAttr));
                }

                var totalCount = await query.CountAsync();
                Console.WriteLine($"查询条件应用后的总记录数: {totalCount}");

                var equipments = await query
                    .OrderBy((e, ed, et) => e.equip_id)
                    .Select((e, ed, et) => new EquipmentDto
                    {
                        EquipId = e.equip_id,
                        ClassId = "", // equipment实体中没有class_id字段，设为空字符串
                        Name = e.name,
                        Icon = e.icon,
                        EquipTypeId = e.equip_type_id,
                        EquipTypeName = et.type_name,
                        Element = e.element,
                        Slot = e.slot ?? 0,
                        StrengthenLevel = e.strengthen_level ?? 0,
                        SuitId = e.suit_id,
                        Atk = ed.atk ?? 0,
                        Hit = ed.hit ?? 0,
                        Def = ed.def ?? 0,
                        Spd = ed.spd ?? 0,
                        Dodge = ed.dodge ?? 0,
                        Hp = ed.hp ?? 0,
                        Mp = ed.mp ?? 0,
                        Deepen = ed.deepen ?? 0,
                        Offset = ed.offset ?? 0,
                        Vamp = ed.vamp ?? 0,
                        VampMp = ed.vamp_mp ?? 0,
                        Description = ed.description,
                        MainAttr = ed.main_attr,
                        MainAttrValue = ed.main_attr_value,
                        SubAttr = ed.sub_attr,
                        SubAttrValue = ed.sub_attr_value,
                        ElementLimit = ed.element_limit,
                        EquipName = ed.equip_name
                    })
                    .ToPageListAsync(queryDto.Page, queryDto.PageSize);

                Console.WriteLine($"查询结果数量: {equipments?.Count ?? 0}");
                if (equipments?.Any() == true)
                {
                    Console.WriteLine($"第一条记录: {System.Text.Json.JsonSerializer.Serialize(equipments.First())}");
                }

                return new PagedResult<EquipmentDto>(equipments, queryDto.Page, queryDto.PageSize, totalCount);
            }
            catch (Exception ex)
            {
                // 记录日志
                Console.WriteLine($"查询装备列表出错：{ex.Message}");
                return new PagedResult<EquipmentDto>(new List<EquipmentDto>(), queryDto.Page, queryDto.PageSize, 0);
            }
        }

        /// <summary>
        /// 根据装备ID获取装备详情
        /// </summary>
        /// <param name="equipId">装备ID</param>
        /// <returns>装备详情</returns>
        public async Task<EquipmentDto?> GetByIdAsync(string equipId)
        {
            try
            {
                var equipment = await _dbService.GetClient().Queryable<equipment, equipment_detail, equipment_type>((e, ed, et) => new JoinQueryInfos(
                    JoinType.Left, e.equip_id == ed.equip_id,
                    JoinType.Left, e.equip_type_id == et.equip_type_id
                ))
                .Where((e, ed, et) => e.equip_id == equipId)
                .Select((e, ed, et) => new EquipmentDto
                {
                    EquipId = e.equip_id,
                    ClassId = "", // equipment实体中没有class_id字段，设为空字符串
                    Name = e.name,
                    Icon = e.icon,
                    EquipTypeId = e.equip_type_id,
                    EquipTypeName = et.type_name,
                    Element = e.element,
                    Slot = e.slot ?? 0,
                    StrengthenLevel = e.strengthen_level ?? 0,
                    SuitId = e.suit_id,
                    Atk = ed.atk ?? 0,
                    Hit = ed.hit ?? 0,
                    Def = ed.def ?? 0,
                    Spd = ed.spd ?? 0,
                    Dodge = ed.dodge ?? 0,
                    Hp = ed.hp ?? 0,
                    Mp = ed.mp ?? 0,
                    Deepen = ed.deepen ?? 0,
                    Offset = ed.offset ?? 0,
                    Vamp = ed.vamp ?? 0,
                    VampMp = ed.vamp_mp ?? 0,
                    Description = ed.description,
                    MainAttr = ed.main_attr,
                    MainAttrValue = ed.main_attr_value,
                    SubAttr = ed.sub_attr,
                    SubAttrValue = ed.sub_attr_value,
                    ElementLimit = ed.element_limit,
                    EquipName = ed.equip_name
                })
                .FirstAsync();

                return equipment;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"获取装备详情出错：{ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 创建装备
        /// </summary>
        /// <param name="createDto">创建DTO</param>
        /// <returns>操作结果</returns>
        public async Task<ApiResult<bool>> CreateAsync(EquipmentCreateDto createDto)
        {
            try
            {
                // 检查装备ID是否已存在
                var exists = await CheckEquipIdExistsAsync(createDto.EquipId);
                if (exists)
                {
                    return ApiResult<bool>.Fail("装备ID已存在");
                }

                // 开启事务
                await _dbService.GetClient().Ado.BeginTranAsync();
                try
                {
                    // 插入装备主表
                    var equipmentEntity = new equipment
                    {
                        equip_id = createDto.EquipId,
                        // class_id字段在实体中不存在，已移除
                        name = createDto.Name,
                        icon = createDto.Icon,
                        equip_type_id = createDto.EquipTypeId,
                        element = createDto.Element,
                        slot = createDto.Slot,
                        strengthen_level = createDto.StrengthenLevel,
                        suit_id = createDto.SuitId
                    };

                    await _dbService.Insertable(equipmentEntity).ExecuteCommandAsync();

                    // 插入装备详情表
                    var equipmentDetailEntity = new equipment_detail
                    {
                        equip_id = createDto.EquipId,
                        // name字段在equipment_detail实体中不存在，已移除
                        atk = createDto.Atk,
                        hit = createDto.Hit,
                        def = createDto.Def,
                        spd = createDto.Spd,
                        dodge = createDto.Dodge,
                        hp = createDto.Hp,
                        mp = createDto.Mp,
                        deepen = createDto.Deepen,
                        offset = createDto.Offset,
                        vamp = createDto.Vamp,
                        vamp_mp = createDto.VampMp,
                        equip_type_id = createDto.EquipTypeId,
                        description = createDto.Description,
                        main_attr = createDto.MainAttr,
                        main_attr_value = createDto.MainAttrValue,
                        sub_attr = createDto.SubAttr,
                        sub_attr_value = createDto.SubAttrValue,
                        element_limit = createDto.ElementLimit,
                        equip_name = createDto.EquipName
                    };

                    await _dbService.Insertable(equipmentDetailEntity).ExecuteCommandAsync();

                    await _dbService.GetClient().Ado.CommitTranAsync();
                    return ApiResult<bool>.Ok(true, "装备创建成功");
                }
                catch
                {
                    await _dbService.GetClient().Ado.RollbackTranAsync();
                    throw;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"创建装备出错：{ex.Message}");
                return ApiResult<bool>.Fail($"创建装备失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 更新装备
        /// </summary>
        /// <param name="updateDto">更新DTO</param>
        /// <returns>操作结果</returns>
        public async Task<ApiResult<bool>> UpdateAsync(EquipmentUpdateDto updateDto)
        {
            try
            {
                // 检查装备是否存在
                var exists = await CheckEquipIdExistsAsync(updateDto.EquipId);
                if (!exists)
                {
                    return ApiResult<bool>.Fail("装备不存在");
                }

                // 开启事务
                await _dbService.GetClient().Ado.BeginTranAsync();
                try
                {
                    // 更新装备主表
                    await _dbService.Updateable<equipment>()
                        .SetColumns(e => new equipment
                        {
                            // class_id字段在实体中不存在，已移除
                            name = updateDto.Name,
                            icon = updateDto.Icon,
                            equip_type_id = updateDto.EquipTypeId,
                            element = updateDto.Element,
                            slot = updateDto.Slot,
                            strengthen_level = updateDto.StrengthenLevel,
                            suit_id = updateDto.SuitId
                        })
                        .Where(e => e.equip_id == updateDto.EquipId)
                        .ExecuteCommandAsync();

                    // 更新装备详情表
                    await _dbService.Updateable<equipment_detail>()
                        .SetColumns(ed => new equipment_detail
                        {
                            // name字段在equipment_detail实体中不存在，已移除
                            atk = updateDto.Atk,
                            hit = updateDto.Hit,
                            def = updateDto.Def,
                            spd = updateDto.Spd,
                            dodge = updateDto.Dodge,
                            hp = updateDto.Hp,
                            mp = updateDto.Mp,
                            deepen = updateDto.Deepen,
                            offset = updateDto.Offset,
                            vamp = updateDto.Vamp,
                            vamp_mp = updateDto.VampMp,
                            equip_type_id = updateDto.EquipTypeId,
                            description = updateDto.Description,
                            main_attr = updateDto.MainAttr,
                            main_attr_value = updateDto.MainAttrValue,
                            sub_attr = updateDto.SubAttr,
                            sub_attr_value = updateDto.SubAttrValue,
                            element_limit = updateDto.ElementLimit,
                            equip_name = updateDto.EquipName
                        })
                        .Where(ed => ed.equip_id == updateDto.EquipId)
                        .ExecuteCommandAsync();

                    await _dbService.GetClient().Ado.CommitTranAsync();
                    return ApiResult<bool>.Ok(true, "装备更新成功");
                }
                catch
                {
                    await _dbService.GetClient().Ado.RollbackTranAsync();
                    throw;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"更新装备出错：{ex.Message}");
                return ApiResult<bool>.Fail($"更新装备失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 删除装备
        /// </summary>
        /// <param name="deleteDto">删除DTO</param>
        /// <returns>操作结果</returns>
        public async Task<ApiResult<bool>> DeleteAsync(EquipmentDeleteDto deleteDto)
        {
            try
            {
                // 检查装备是否存在
                var exists = await CheckEquipIdExistsAsync(deleteDto.EquipId);
                if (!exists)
                {
                    return ApiResult<bool>.Fail("装备不存在");
                }

                // 开启事务
                await _dbService.GetClient().Ado.BeginTranAsync();
                try
                {
                    // 删除装备详情表数据
                    await _dbService.Deleteable<equipment_detail>()
                        .Where(ed => ed.equip_id == deleteDto.EquipId)
                        .ExecuteCommandAsync();

                    // 删除装备主表数据
                    await _dbService.Deleteable<equipment>()
                        .Where(e => e.equip_id == deleteDto.EquipId)
                        .ExecuteCommandAsync();

                    await _dbService.GetClient().Ado.CommitTranAsync();
                    return ApiResult<bool>.Ok(true, "装备删除成功");
                }
                catch
                {
                    await _dbService.GetClient().Ado.RollbackTranAsync();
                    throw;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"删除装备出错：{ex.Message}");
                return ApiResult<bool>.Fail($"删除装备失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 检查装备ID是否存在
        /// </summary>
        /// <param name="equipId">装备ID</param>
        /// <returns>是否存在</returns>
        public async Task<bool> CheckEquipIdExistsAsync(string equipId)
        {
            try
            {
                var count = await _dbService.Queryable<equipment>()
                    .Where(e => e.equip_id == equipId)
                    .CountAsync();
                return count > 0;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"检查装备ID存在性出错：{ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 获取装备类型列表
        /// </summary>
        /// <returns>装备类型列表</returns>
        public async Task<List<EquipmentTypeDto>> GetEquipmentTypesAsync()
        {
            try
            {
                var types = await _dbService.Queryable<equipment_type>()
                    // is_active字段在实体中不存在，移除条件
                    .OrderBy(et => et.id) // sort_order字段在实体中不存在，只按id排序
                    .Select(et => new EquipmentTypeDto
                    {
                        EquipTypeId = et.equip_type_id,
                        TypeName = et.type_name
                    })
                    .ToListAsync();

                return types;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"获取装备类型列表出错：{ex.Message}");
                return new List<EquipmentTypeDto>();
            }
        }

        /// <summary>
        /// 获取五行属性列表
        /// </summary>
        /// <returns>五行属性列表</returns>
        public async Task<List<string>> GetElementsAsync()
        {
            // 预定义的五行属性
            var elements = new List<string> { "金", "木", "水", "火", "土" };
            return await Task.FromResult(elements);
        }

        /// <summary>
        /// 获取五行限制列表
        /// </summary>
        /// <returns>五行限制列表</returns>
        public async Task<List<string>> GetElementLimitsAsync()
        {
            // 预定义的五行限制
            var elementLimits = new List<string> { "金", "木", "水", "火", "土" };
            return await Task.FromResult(elementLimits);
        }

        // ==================== 装备类型管理实现 ====================

        /// <summary>
        /// 分页查询装备类型列表
        /// </summary>
        /// <param name="queryDto">查询条件</param>
        /// <returns>分页结果</returns>
        public async Task<PagedResult<EquipmentTypeDto>> GetEquipmentTypesPagedAsync(EquipmentTypeQueryDto queryDto)
        {
            try
            {
                var query = _dbService.Queryable<equipment_type>();

                // 装备类型ID过滤
                if (!string.IsNullOrWhiteSpace(queryDto.EquipTypeId))
                {
                    query = query.Where(et => et.equip_type_id.Contains(queryDto.EquipTypeId));
                }

                // 类型名称过滤
                if (!string.IsNullOrWhiteSpace(queryDto.TypeName))
                {
                    query = query.Where(et => et.type_name.Contains(queryDto.TypeName));
                }

                // 查询总数
                var totalCount = await query.CountAsync();

                // 分页查询装备类型
                var equipmentTypes = await query
                    .OrderBy(et => et.id) // sort_order字段不存在，只按id排序
                    .Skip((queryDto.Page - 1) * queryDto.PageSize)
                    .Take(queryDto.PageSize)
                    .Select(et => new EquipmentTypeDto
                    {
                        Id = et.id,
                        EquipTypeId = et.equip_type_id,
                        TypeName = et.type_name,
                        Description = "", // description字段不存在，设为空字符串
                        SortOrder = 0, // sort_order字段不存在，设为0
                        IsActive = true, // is_active字段不存在，设为true
                        CreateTime = null, // create_time字段不存在，设为null
                        UpdateTime = null, // update_time字段不存在，设为null
                        EquipmentCount = 0 // 暂时设为0，避免DataReader冲突
                    })
                    .ToListAsync();

                // 单独查询每个类型的装备数量
                foreach (var type in equipmentTypes)
                {
                    type.EquipmentCount = await _dbService.Queryable<equipment>()
                        .Where(e => e.equip_type_id == type.EquipTypeId)
                        .CountAsync();
                }

                return new PagedResult<EquipmentTypeDto>(equipmentTypes, queryDto.Page, queryDto.PageSize, totalCount);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"分页查询装备类型列表出错：{ex.Message}");
                return new PagedResult<EquipmentTypeDto>(new List<EquipmentTypeDto>(), queryDto.Page, queryDto.PageSize, 0);
            }
        }

        /// <summary>
        /// 根据装备类型ID获取装备类型详情
        /// </summary>
        /// <param name="equipTypeId">装备类型ID</param>
        /// <returns>装备类型详情</returns>
        public async Task<EquipmentTypeDto?> GetEquipmentTypeByIdAsync(string equipTypeId)
        {
            try
            {
                var equipmentType = await _dbService.Queryable<equipment_type>()
                    .Where(et => et.equip_type_id == equipTypeId)
                    .Select(et => new EquipmentTypeDto
                    {
                        Id = et.id,
                        EquipTypeId = et.equip_type_id,
                        TypeName = et.type_name,
                        Description = "", // description字段不存在，设为空字符串
                        SortOrder = 0, // sort_order字段不存在，设为0
                        IsActive = true, // is_active字段不存在，设为true
                        CreateTime = null, // create_time字段不存在，设为null
                        UpdateTime = null // update_time字段不存在，设为null
                    })
                    .FirstAsync();

                if (equipmentType != null)
                {
                    // 查询该类型的装备数量
                    equipmentType.EquipmentCount = await _dbService.Queryable<equipment>()
                        .Where(e => e.equip_type_id == equipTypeId)
                        .CountAsync();
                }

                return equipmentType;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"获取装备类型详情出错：{ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 创建装备类型
        /// </summary>
        /// <param name="createDto">创建DTO</param>
        /// <returns>操作结果</returns>
        public async Task<ApiResult<bool>> CreateEquipmentTypeAsync(EquipmentTypeCreateDto createDto)
        {
            try
            {
                // 检查装备类型ID是否已存在
                var exists = await CheckEquipmentTypeIdExistsAsync(createDto.EquipTypeId);
                if (exists)
                {
                    return ApiResult<bool>.Fail("装备类型ID已存在");
                }

                // 插入装备类型
                await _dbService.Insertable(new equipment_type
                {
                    equip_type_id = createDto.EquipTypeId,
                    type_name = createDto.TypeName
                    // description, sort_order, is_active, create_time, update_time字段在实体中不存在，已移除
                }).ExecuteCommandAsync();

                return ApiResult<bool>.Ok(true, "装备类型创建成功");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"创建装备类型出错：{ex.Message}");
                return ApiResult<bool>.Fail($"创建装备类型失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 更新装备类型
        /// </summary>
        /// <param name="updateDto">更新DTO</param>
        /// <returns>操作结果</returns>
        public async Task<ApiResult<bool>> UpdateEquipmentTypeAsync(EquipmentTypeUpdateDto updateDto)
        {
            try
            {
                // 检查装备类型是否存在
                var exists = await CheckEquipmentTypeIdExistsAsync(updateDto.EquipTypeId);
                if (!exists)
                {
                    return ApiResult<bool>.Fail("装备类型不存在");
                }

                // 更新装备类型
                await _dbService.Updateable<equipment_type>()
                    .SetColumns(et => new equipment_type
                    {
                        type_name = updateDto.TypeName
                        // description, sort_order, is_active, update_time字段在实体中不存在，已移除
                    })
                    .Where(et => et.equip_type_id == updateDto.EquipTypeId)
                    .ExecuteCommandAsync();

                return ApiResult<bool>.Ok(true, "装备类型更新成功");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"更新装备类型出错：{ex.Message}");
                return ApiResult<bool>.Fail($"更新装备类型失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 删除装备类型
        /// </summary>
        /// <param name="deleteDto">删除DTO</param>
        /// <returns>操作结果</returns>
        public async Task<ApiResult<bool>> DeleteEquipmentTypeAsync(EquipmentTypeDeleteDto deleteDto)
        {
            try
            {
                // 检查装备类型是否存在
                var exists = await CheckEquipmentTypeIdExistsAsync(deleteDto.EquipTypeId);
                if (!exists)
                {
                    return ApiResult<bool>.Fail("装备类型不存在");
                }

                // 检查该类型下是否有装备
                var equipmentCount = await _dbService.Queryable<equipment>()
                    .Where(e => e.equip_type_id == deleteDto.EquipTypeId)
                    .CountAsync();

                if (equipmentCount > 0)
                {
                    return ApiResult<bool>.Fail($"该装备类型下还有 {equipmentCount} 个装备，无法删除");
                }

                // 删除装备类型
                await _dbService.Deleteable<equipment_type>()
                    .Where(et => et.equip_type_id == deleteDto.EquipTypeId)
                    .ExecuteCommandAsync();

                return ApiResult<bool>.Ok(true, "装备类型删除成功");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"删除装备类型出错：{ex.Message}");
                return ApiResult<bool>.Fail($"删除装备类型失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 检查装备类型ID是否存在
        /// </summary>
        /// <param name="equipTypeId">装备类型ID</param>
        /// <returns>是否存在</returns>
        public async Task<bool> CheckEquipmentTypeIdExistsAsync(string equipTypeId)
        {
            try
            {
                var count = await _dbService.Queryable<equipment_type>()
                    .Where(et => et.equip_type_id == equipTypeId)
                    .CountAsync();
                return count > 0;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"检查装备类型ID存在性出错：{ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 测试装备数据
        /// </summary>
        /// <returns>测试结果</returns>
        public async Task<object> TestEquipmentDataAsync()
        {
            try
            {
                // 测试equipment表
                var equipments = await _dbService.GetClient().Queryable<equipment>()
                    .Take(5)
                    .ToListAsync();

                // 测试equipment_detail表
                var details = await _dbService.GetClient().Queryable<equipment_detail>()
                    .Take(5)
                    .ToListAsync();

                // 测试equipment_type表
                var types = await _dbService.GetClient().Queryable<equipment_type>()
                    .Take(5)
                    .ToListAsync();

                // 测试简单的JOIN查询
                var joinResult = await _dbService.GetClient().Queryable<equipment, equipment_detail>((e, ed) => new JoinQueryInfos(
                    JoinType.Left, e.equip_id == ed.equip_id
                ))
                .Take(5)
                .Select((e, ed) => new {
                    EquipId = e.equip_id,
                    Name = e.name,
                    DetailEquipId = ed.equip_id, // equipment_detail没有name字段，改为equip_id
                    HasDetail = ed.equip_id != null
                })
                .ToListAsync();

                return new
                {
                    EquipmentCount = equipments.Count,
                    EquipmentSample = equipments.Take(2),
                    DetailCount = details.Count,
                    DetailSample = details.Take(2),
                    TypeCount = types.Count,
                    TypeSample = types.Take(2),
                    JoinResultCount = joinResult.Count,
                    JoinSample = joinResult.Take(2)
                };
            }
            catch (Exception ex)
            {
                Console.WriteLine($"测试装备数据异常：{ex.Message}");
                throw;
            }
        }
    }
}