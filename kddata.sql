/*
 Navicat Premium Data Transfer

 Source Server         : **************
 Source Server Type    : MySQL
 Source Server Version : 80012 (8.0.12)
 Source Host           : **************:3306
 Source Schema         : kddata

 Target Server Type    : MySQL
 Target Server Version : 80012 (8.0.12)
 File Encoding         : 65001

 Date: 03/09/2025 11:21:21
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for admin_bm
-- ----------------------------
DROP TABLE IF EXISTS `admin_bm`;
CREATE TABLE `admin_bm`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `username` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '用户名',
  `password` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '密码',
  `real_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '真实姓名',
  `phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '手机号',
  `email` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '邮箱',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `last_login_time` datetime NULL DEFAULT NULL COMMENT '最后登录时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `username_unique`(`username` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '管理员表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for admin_bm_temp
-- ----------------------------
DROP TABLE IF EXISTS `admin_bm_temp`;
CREATE TABLE `admin_bm_temp`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `username` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '用户名',
  `password` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '密码',
  `real_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '真实姓名',
  `phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '手机号',
  `email` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '邮箱',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `last_login_time` datetime NULL DEFAULT NULL COMMENT '最后登录时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `username_unique`(`username` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '管理员表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for anti_cheat_logs
-- ----------------------------
DROP TABLE IF EXISTS `anti_cheat_logs`;
CREATE TABLE `anti_cheat_logs`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `cheat_type` enum('SPEED','SCRIPT','ABNORMAL','TIMING') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '作弊类型',
  `detection_data` json NULL COMMENT '检测数据(JSON格式)',
  `risk_score` decimal(5, 2) NULL DEFAULT 0.00 COMMENT '风险评分',
  `action_taken` enum('NONE','WARNING','TEMP_BAN','PERMANENT_BAN') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT 'NONE' COMMENT '处理动作',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_user_id`(`user_id` ASC) USING BTREE,
  INDEX `idx_cheat_type`(`cheat_type` ASC) USING BTREE,
  CONSTRAINT `anti_cheat_logs_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '反作弊日志表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for auto_battle_status
-- ----------------------------
DROP TABLE IF EXISTS `auto_battle_status`;
CREATE TABLE `auto_battle_status`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `battle_type` enum('HELL','TOWER','DUNGEON','MAP') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '自动战斗类型',
  `target_floor` int(11) NULL DEFAULT 0 COMMENT '目标层数',
  `current_floor` int(11) NULL DEFAULT 0 COMMENT '当前层数',
  `is_active` tinyint(1) NULL DEFAULT 0 COMMENT '是否激活',
  `start_time` datetime NULL DEFAULT NULL COMMENT '开始时间',
  `end_time` datetime NULL DEFAULT NULL COMMENT '结束时间',
  `battles_count` int(11) NULL DEFAULT 0 COMMENT '战斗次数',
  `success_count` int(11) NULL DEFAULT 0 COMMENT '成功次数',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_user_type`(`user_id` ASC, `battle_type` ASC) USING BTREE,
  INDEX `idx_user_id`(`user_id` ASC) USING BTREE,
  CONSTRAINT `auto_battle_status_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '自动战斗状态表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for battle_config
-- ----------------------------
DROP TABLE IF EXISTS `battle_config`;
CREATE TABLE `battle_config`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `config_key` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '配置键',
  `config_value` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '配置值',
  `config_type` enum('STRING','INTEGER','DECIMAL','BOOLEAN','JSON') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT 'STRING' COMMENT '配置类型',
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '配置描述',
  `is_enabled` tinyint(1) NULL DEFAULT 1 COMMENT '是否启用',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_config_key`(`config_key` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '战斗配置表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for battle_records
-- ----------------------------
DROP TABLE IF EXISTS `battle_records`;
CREATE TABLE `battle_records`  (
  `battle_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '战斗ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `pet_id` int(11) NOT NULL COMMENT '宠物ID',
  `monster_id` int(11) NOT NULL COMMENT '怪物ID',
  `map_id` int(11) NOT NULL COMMENT '地图ID',
  `battle_result` enum('WIN','LOSE','ESCAPE') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '战斗结果',
  `rounds` int(11) NULL DEFAULT 0 COMMENT '战斗回合数',
  `damage_dealt` bigint(20) NULL DEFAULT 0 COMMENT '造成伤害',
  `damage_received` bigint(20) NULL DEFAULT 0 COMMENT '受到伤害',
  `experience_gained` int(11) NULL DEFAULT 0 COMMENT '获得经验',
  `money_gained` bigint(20) NULL DEFAULT 0 COMMENT '获得金币',
  `yuanbao_gained` int(11) NULL DEFAULT 0 COMMENT '获得元宝',
  `items_gained` json NULL COMMENT '获得道具(JSON格式)',
  `battle_duration` int(11) NULL DEFAULT 0 COMMENT '战斗时长(毫秒)',
  `damage_amplified` bigint(20) NULL DEFAULT 0 COMMENT '加深伤害',
  `damage_reduced` bigint(20) NULL DEFAULT 0 COMMENT '抵消伤害',
  `hp_healed` bigint(20) NULL DEFAULT 0 COMMENT '吸血回复',
  `mp_healed` bigint(20) NULL DEFAULT 0 COMMENT '吸魔回复',
  `is_first_strike` tinyint(1) NULL DEFAULT 0 COMMENT '是否先手',
  `anti_cheat_score` decimal(5, 2) NULL DEFAULT 0.00 COMMENT '反作弊评分',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`battle_id`) USING BTREE,
  INDEX `idx_user_id`(`user_id` ASC) USING BTREE,
  INDEX `idx_pet_id`(`pet_id` ASC) USING BTREE,
  INDEX `idx_map_id`(`map_id` ASC) USING BTREE,
  INDEX `idx_create_time`(`create_time` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '战斗记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for drop_config
-- ----------------------------
DROP TABLE IF EXISTS `drop_config`;
CREATE TABLE `drop_config`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `map_id` int(11) NOT NULL COMMENT '地图ID',
  `drop_type` enum('地图掉落','怪物掉落') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '掉落类型',
  `monster_id` int(11) NULL DEFAULT NULL COMMENT '怪物ID（怪物掉落时填写，地图掉落时为空）',
  `item_id` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '道具ID',
  `drop_rate` decimal(6, 4) NULL DEFAULT 1.0000 COMMENT '掉落概率（0~1之间，1为必掉）',
  `min_count` int(11) NULL DEFAULT 1 COMMENT '最小掉落数量',
  `max_count` int(11) NULL DEFAULT 1 COMMENT '最大掉落数量',
  `drop_items_json` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '多道具掉落配置JSON数据',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '地图/怪物掉落配置表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for dungeon_progress
-- ----------------------------
DROP TABLE IF EXISTS `dungeon_progress`;
CREATE TABLE `dungeon_progress`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `dungeon_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '副本ID',
  `progress` int(11) NULL DEFAULT 0 COMMENT '副本进度',
  `reset_count` int(11) NULL DEFAULT 0 COMMENT '重置次数',
  `last_reset_time` datetime NULL DEFAULT NULL COMMENT '最后重置时间',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_user_dungeon`(`user_id` ASC, `dungeon_id` ASC) USING BTREE,
  INDEX `idx_user_id`(`user_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '副本进度表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for equipment
-- ----------------------------
DROP TABLE IF EXISTS `equipment`;
CREATE TABLE `equipment`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `equip_id` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '装备唯一ID',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '装备名称',
  `icon` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '装备图标',
  `equip_type_id` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '装备类型ID',
  `element` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '五行属性',
  `slot` int(11) NULL DEFAULT 0 COMMENT '扩展槽位',
  `strengthen_level` int(11) NULL DEFAULT 0 COMMENT '强化等级',
  `suit_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '套装ID',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1262 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '装备主表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for equipment_detail
-- ----------------------------
DROP TABLE IF EXISTS `equipment_detail`;
CREATE TABLE `equipment_detail`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `equip_id` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '装备ID（关联equipment.equip_id）',
  `atk` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '攻击加成',
  `hit` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '命中加成',
  `def` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '防御加成',
  `spd` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '速度加成',
  `dodge` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '闪避加成',
  `hp` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '生命加成',
  `mp` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '魔法加成',
  `deepen` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '加深加成',
  `offset` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '抵消加成',
  `vamp` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '吸血加成',
  `vamp_mp` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '吸魔加成',
  `equip_type_id` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '装备类型ID',
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '装备说明',
  `main_attr` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '主属性',
  `element_limit` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '五行限制',
  `equip_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '装备名称',
  `main_attr_value` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '主属性值',
  `sub_attr` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '副属性',
  `sub_attr_value` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '副属性值',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1022 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '装备明细表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for equipment_enhance
-- ----------------------------
DROP TABLE IF EXISTS `equipment_enhance`;
CREATE TABLE `equipment_enhance`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `equipment_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '装备ID',
  `enhance_level` int(11) NULL DEFAULT 0 COMMENT '强化等级',
  `enhance_exp` bigint(20) NULL DEFAULT 0 COMMENT '强化经验',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_user_equipment`(`user_id` ASC, `equipment_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '装备强化表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for equipment_gemstone
-- ----------------------------
DROP TABLE IF EXISTS `equipment_gemstone`;
CREATE TABLE `equipment_gemstone`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `user_equipment_id` int(11) NOT NULL COMMENT '用户装备ID（关联user_equipment表）',
  `gemstone_type_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '宝石类型名称（关联gemstone_config表）',
  `position` int(11) NOT NULL COMMENT '镶嵌位置（1-第一槽，2-第二槽，3-第三槽）',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '镶嵌时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_equipment_position`(`user_equipment_id` ASC, `position` ASC) USING BTREE,
  INDEX `idx_user_equipment_id`(`user_equipment_id` ASC) USING BTREE,
  INDEX `idx_gemstone_type_name`(`gemstone_type_name` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '装备宝石关联表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for equipment_operation_log
-- ----------------------------
DROP TABLE IF EXISTS `equipment_operation_log`;
CREATE TABLE `equipment_operation_log`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `user_equipment_id` int(11) NOT NULL COMMENT '用户装备ID',
  `operation_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '操作类型（STRENGTHEN/EMBED_GEM/TRANSFORM_ELEMENT等）',
  `operation_data` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '操作数据（JSON格式）',
  `cost_money` bigint(20) NULL DEFAULT 0 COMMENT '金币消耗',
  `cost_items` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '道具消耗（JSON格式）',
  `result` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '操作结果（SUCCESS/FAILED）',
  `result_message` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '结果消息',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_user_id`(`user_id` ASC) USING BTREE,
  INDEX `idx_user_equipment_id`(`user_equipment_id` ASC) USING BTREE,
  INDEX `idx_operation_type`(`operation_type` ASC) USING BTREE,
  INDEX `idx_create_time`(`create_time` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 218 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '装备操作记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for equipment_type
-- ----------------------------
DROP TABLE IF EXISTS `equipment_type`;
CREATE TABLE `equipment_type`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `equip_type_id` varchar(20) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL COMMENT '装备类型ID',
  `type_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL COMMENT '装备类型名称',
  `description` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci NULL DEFAULT NULL COMMENT '类型描述',
  `sort_order` int(11) NULL DEFAULT 0 COMMENT '排序号',
  `is_active` tinyint(1) NULL DEFAULT 1 COMMENT '是否启用',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_equip_type_id`(`equip_type_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 78 CHARACTER SET = utf8 COLLATE = utf8_unicode_ci COMMENT = '装备类型表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for exp_system_config
-- ----------------------------
DROP TABLE IF EXISTS `exp_system_config`;
CREATE TABLE `exp_system_config`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `system_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '系统名称',
  `max_exp` bigint(20) NOT NULL COMMENT '经验上限',
  `max_level` int(11) NOT NULL COMMENT '等级上限',
  `exp_formula` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '经验计算公式',
  `is_active` tinyint(1) NULL DEFAULT 1 COMMENT '是否启用',
  `created_at` datetime NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_system_name`(`system_name` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '经验系统配置表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for exp_transfer_log
-- ----------------------------
DROP TABLE IF EXISTS `exp_transfer_log`;
CREATE TABLE `exp_transfer_log`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `source_pet_id` int(11) NOT NULL COMMENT '源宠物ID',
  `target_pet_id` int(11) NOT NULL COMMENT '目标宠物ID',
  `transfer_exp` bigint(20) NOT NULL COMMENT '转移经验值',
  `transfer_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '转移时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_user_id`(`user_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '经验转移记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for face_change_config
-- ----------------------------
DROP TABLE IF EXISTS `face_change_config`;
CREATE TABLE `face_change_config`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `nirvana_pet_image` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '涅槃兽形象ID',
  `success_rate` decimal(5, 2) NOT NULL COMMENT '成功率',
  `attribute_ratio` decimal(5, 2) NOT NULL COMMENT '属性继承比例',
  `cost_gold` bigint(20) NULL DEFAULT 2000000000 COMMENT '消耗金币',
  `level_requirement` int(11) NULL DEFAULT 130 COMMENT '等级要求',
  `is_active` tinyint(1) NULL DEFAULT 1,
  `created_at` datetime NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_nirvana_pet`(`nirvana_pet_image` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '变脸配置表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for gemstone_config
-- ----------------------------
DROP TABLE IF EXISTS `gemstone_config`;
CREATE TABLE `gemstone_config`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `type_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '宝石类型名称（红宝石/蓝宝石/混元石等）',
  `up_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '提升属性类型（攻击/命中/防御/速度/闪避/生命/魔法等）',
  `up_num` decimal(10, 2) NOT NULL COMMENT '提升数值（具体的属性加成值）',
  `level` int(11) NOT NULL COMMENT '宝石等级（1-3级，对应一孔/二孔/三孔）',
  `color` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '宝石颜色（红色/蓝色/绿色/紫色/金色等）',
  `prop_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '对应道具ID（关联item_config表）',
  `type_class` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '宝石分类（一孔宝石/二孔宝石/三孔宝石）',
  `equip_types` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '可镶嵌装备类型（JSON数组格式）',
  `positions` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '可镶嵌位置（JSON数组，1/2/3对应槽位）',
  `order_num` int(11) NULL DEFAULT 0 COMMENT '排序号（显示顺序）',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_type_name`(`type_name` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '宝石配置表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for gemstone_synthesis_log
-- ----------------------------
DROP TABLE IF EXISTS `gemstone_synthesis_log`;
CREATE TABLE `gemstone_synthesis_log`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `gemstone_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '宝石类型',
  `synthesis_count` int(11) NOT NULL COMMENT '合成数量',
  `synthesis_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '合成时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_user_id`(`user_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '宝石合成记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for item_config
-- ----------------------------
DROP TABLE IF EXISTS `item_config`;
CREATE TABLE `item_config`  (
  `id` int(11) NOT NULL COMMENT '自增主键',
  `item_no` int(11) NOT NULL COMMENT '道具编号（业务唯一标识）',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '道具名称',
  `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '道具类型（消耗品/装备/材料/特殊等）',
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '道具描述说明',
  `quality` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '道具品质（普通/稀有/史诗/传说等）',
  `icon` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '道具图标文件名',
  `price` int(11) NULL DEFAULT NULL COMMENT '道具价格（金币）',
  `use_limit` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '使用限制条件',
  `extra` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '扩展信息（JSON格式存储额外属性）',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `script` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '道具脚本',
  `is_active` tinyint(1) NULL DEFAULT NULL COMMENT '是否启用',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '道具表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for item_discard_log
-- ----------------------------
DROP TABLE IF EXISTS `item_discard_log`;
CREATE TABLE `item_discard_log`  (
  `log_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '记录ID（主键，自增）',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `original_item_seq` int(11) NOT NULL COMMENT '原道具序号',
  `item_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '道具ID',
  `item_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '道具名称（冗余存储，方便查询）',
  `discard_count` int(11) NOT NULL DEFAULT 1 COMMENT '丢弃的道具数量',
  `discard_reason` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '用户主动丢弃' COMMENT '丢弃原因',
  `discard_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '丢弃时间',
  `is_recovered` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否已找回（0=未找回，1=已找回）',
  `recover_time` datetime NULL DEFAULT NULL COMMENT '找回时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`log_id`) USING BTREE,
  INDEX `idx_user_id`(`user_id` ASC) USING BTREE,
  INDEX `idx_discard_time`(`discard_time` ASC) USING BTREE,
  INDEX `idx_is_recovered`(`is_recovered` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 8 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '道具丢弃记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for item_script
-- ----------------------------
DROP TABLE IF EXISTS `item_script`;
CREATE TABLE `item_script`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `item_no` int(11) NOT NULL COMMENT '道具编号（关联item_config.item_no）',
  `script` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '道具脚本内容',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '道具脚本说明',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2069 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '道具脚本表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for level_change_log
-- ----------------------------
DROP TABLE IF EXISTS `level_change_log`;
CREATE TABLE `level_change_log`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `pet_id` int(11) NOT NULL COMMENT '宠物ID',
  `old_level` int(11) NOT NULL COMMENT '原等级',
  `new_level` int(11) NOT NULL COMMENT '新等级',
  `old_exp` bigint(20) NOT NULL COMMENT '原经验',
  `new_exp` bigint(20) NOT NULL COMMENT '新经验',
  `change_reason` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '变更原因',
  `change_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '变更时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_user_id`(`user_id` ASC) USING BTREE,
  INDEX `idx_pet_id`(`pet_id` ASC) USING BTREE,
  INDEX `idx_change_time`(`change_time` ASC) USING BTREE,
  INDEX `idx_user_pet`(`user_id` ASC, `pet_id` ASC) USING BTREE,
  INDEX `idx_user_time`(`user_id` ASC, `change_time` DESC) USING BTREE,
  INDEX `idx_pet_time`(`pet_id` ASC, `change_time` DESC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '等级变更日志表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for level_config
-- ----------------------------
DROP TABLE IF EXISTS `level_config`;
CREATE TABLE `level_config`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `level` int(11) NOT NULL COMMENT '等级',
  `required_exp` bigint(20) NOT NULL COMMENT '该等级所需累积经验',
  `upgrade_exp` bigint(20) NOT NULL COMMENT '升级到下一级所需经验',
  `is_active` tinyint(1) NULL DEFAULT 1 COMMENT '是否启用',
  `created_at` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_level`(`level` ASC) USING BTREE,
  INDEX `idx_required_exp`(`required_exp` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 101 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '等级配置表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for map_config
-- ----------------------------
DROP TABLE IF EXISTS `map_config`;
CREATE TABLE `map_config`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `map_id` int(11) NOT NULL COMMENT '地图ID',
  `map_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '地图名称',
  `map_desc` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '地图描述',
  `map_size` int(11) NULL DEFAULT 0 COMMENT '地图大小',
  `map_type` int(11) NULL DEFAULT 0 COMMENT '地图类型',
  `atlast_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '图集名称',
  `background` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '地图背景图片',
  `bgm` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '地图背景音乐',
  `bgm_loop` tinyint(1) NULL DEFAULT 0 COMMENT '背景音乐是否循环',
  `bgm_volume` decimal(4, 2) NULL DEFAULT 1.00 COMMENT '背景音乐音量',
  `bgm_play` tinyint(1) NULL DEFAULT 1 COMMENT '是否播放背景音乐',
  `bgm_mute` tinyint(1) NULL DEFAULT 0 COMMENT '是否静音',
  `bgm_pause` tinyint(1) NULL DEFAULT 0 COMMENT '是否暂停',
  `ico` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '地图图标',
  `type` int(11) NULL DEFAULT 0 COMMENT '地图类型',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 20 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '地图配置表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for map_detail
-- ----------------------------
DROP TABLE IF EXISTS `map_detail`;
CREATE TABLE `map_detail`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `map_id` int(11) NOT NULL COMMENT '地图ID（关联map_config.map_id）',
  `limit_growth` decimal(12, 2) NULL DEFAULT 0.00 COMMENT '限制成长',
  `limit_level` int(11) NULL DEFAULT 0 COMMENT '限制等级',
  `limit_key` tinyint(1) NULL DEFAULT 0 COMMENT '限制钥匙（1为限制，0为不限制）',
  `min_gold` bigint(20) NULL DEFAULT 0 COMMENT '最小金币',
  `min_yuanbao` int(11) NULL DEFAULT 0 COMMENT '最小元宝',
  `max_gold` bigint(20) NULL DEFAULT 0 COMMENT '最大金币',
  `max_yuanbao` int(11) NULL DEFAULT 0 COMMENT '最大元宝',
  `max_drop` int(11) NULL DEFAULT 0 COMMENT '最大掉落',
  `min_drop` int(11) NULL DEFAULT 0 COMMENT '最小掉落',
  `icon` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '地图图标',
  `type` int(11) NULL DEFAULT 0 COMMENT '地图类型',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '地图明细表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for map_monster
-- ----------------------------
DROP TABLE IF EXISTS `map_monster`;
CREATE TABLE `map_monster`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `map_id` int(11) NOT NULL COMMENT '地图ID（关联map_config.map_id）',
  `monster_id` int(11) NULL DEFAULT NULL COMMENT '怪物序号',
  `monster_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '怪物名字',
  `growth` bigint(20) NULL DEFAULT 0 COMMENT '怪物成长',
  `element` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '怪物五行',
  `max_level` int(11) NULL DEFAULT 0 COMMENT '最大等级',
  `min_level` int(11) NULL DEFAULT 0 COMMENT '最小等级',
  `max_drop` int(11) NULL DEFAULT 0 COMMENT '最大掉落',
  `exp` bigint(20) NULL DEFAULT 0 COMMENT '经验值',
  `hp` bigint(20) NULL DEFAULT 0 COMMENT '生命',
  `mp` bigint(20) NULL DEFAULT 0 COMMENT '魔法',
  `max_hp` bigint(20) NULL DEFAULT 0 COMMENT '最大生命',
  `max_mp` bigint(20) NULL DEFAULT 0 COMMENT '最大魔法',
  `atk` bigint(20) NULL DEFAULT 0 COMMENT '攻击',
  `def` bigint(20) NULL DEFAULT 0 COMMENT '防御',
  `dodge` bigint(20) NULL DEFAULT 0 COMMENT '闪避',
  `spd` bigint(20) NULL DEFAULT 0 COMMENT '速度',
  `hit` bigint(20) NULL DEFAULT 0 COMMENT '命中',
  `deepen` decimal(6, 2) NULL DEFAULT 0.00 COMMENT '加深伤害',
  `offset` decimal(6, 2) NULL DEFAULT 0.00 COMMENT '抵消伤害',
  `vamp` decimal(6, 2) NULL DEFAULT 0.00 COMMENT '吸血比例',
  `vamp_mp` decimal(6, 2) NULL DEFAULT 0.00 COMMENT '吸魔比例',
  `skill_list` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '技能列表(逗号分隔)',
  `drop_items` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '掉落道具配置',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 25 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '地图怪物表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for monster_config
-- ----------------------------
DROP TABLE IF EXISTS `monster_config`;
CREATE TABLE `monster_config`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增长主键',
  `monster_no` int(11) NOT NULL COMMENT '怪物编号（原始编号）',
  `skill` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '技能编号（可为空）',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '怪物名称',
  `attribute` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '怪物属性（如金、木、水、火、土、神等）',
  `pet_no` int(11) NULL DEFAULT NULL COMMENT '对应的宠物编号',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 287 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '怪物配置表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for pet_config
-- ----------------------------
DROP TABLE IF EXISTS `pet_config`;
CREATE TABLE `pet_config`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增长主键',
  `pet_no` int(11) NOT NULL COMMENT '宠物编号（原始编号）',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '宠物名称',
  `attribute` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '宠物属性（如金、木、水、火、土、神等）',
  `skill` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '技能编号（可为空，多个技能用英文逗号分隔）',
  `is_active` tinyint(1) NULL DEFAULT NULL COMMENT '是否启用',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 634 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '宠物配置表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for pet_evolution_config
-- ----------------------------
DROP TABLE IF EXISTS `pet_evolution_config`;
CREATE TABLE `pet_evolution_config`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `pet_no` int(11) NOT NULL COMMENT '宠物编号（关联pet_config.pet_no）',
  `evolution_type` enum('A','B') CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL COMMENT '进化路线类型',
  `target_pet_no` int(11) NOT NULL COMMENT '进化后宠物编号',
  `required_level` int(11) NOT NULL DEFAULT 40 COMMENT '所需等级',
  `required_item_id` varchar(20) CHARACTER SET utf8 COLLATE utf8_unicode_ci NULL DEFAULT NULL COMMENT '所需道具ID',
  `required_item_count` int(11) NULL DEFAULT 1 COMMENT '所需道具数量',
  `cost_gold` bigint(20) NULL DEFAULT 1000 COMMENT '消耗金币',
  `growth_min` decimal(5, 3) NULL DEFAULT 0.100 COMMENT '最小成长加成',
  `growth_max` decimal(5, 3) NULL DEFAULT 0.500 COMMENT '最大成长加成',
  `success_rate` decimal(5, 2) NULL DEFAULT 100.00 COMMENT '成功率(%)',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `is_active` int(2) NULL DEFAULT NULL COMMENT '是否激活',
  `description` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci NULL DEFAULT NULL COMMENT '说明',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_pet_evolution`(`pet_no` ASC, `evolution_type` ASC) USING BTREE,
  INDEX `idx_pet_no`(`pet_no` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 220 CHARACTER SET = utf8 COLLATE = utf8_unicode_ci COMMENT = '宠物进化配置表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for pet_evolution_log
-- ----------------------------
DROP TABLE IF EXISTS `pet_evolution_log`;
CREATE TABLE `pet_evolution_log`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `user_pet_id` int(11) NOT NULL COMMENT '用户宠物ID',
  `evolution_type` enum('A','B') CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL COMMENT '进化路线',
  `before_pet_no` int(11) NOT NULL COMMENT '进化前宠物编号',
  `after_pet_no` int(11) NOT NULL COMMENT '进化后宠物编号',
  `before_growth` decimal(20, 6) NOT NULL COMMENT '进化前成长',
  `after_growth` decimal(20, 6) NOT NULL COMMENT '进化后成长',
  `growth_increase` decimal(20, 6) NOT NULL COMMENT '成长增加值',
  `used_item_id` varchar(20) CHARACTER SET utf8 COLLATE utf8_unicode_ci NULL DEFAULT NULL COMMENT '使用的道具ID',
  `cost_gold` bigint(20) NOT NULL COMMENT '消耗金币',
  `is_success` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否成功',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '进化时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_user_pet`(`user_id` ASC, `user_pet_id` ASC) USING BTREE,
  INDEX `idx_create_time`(`create_time` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 30 CHARACTER SET = utf8 COLLATE = utf8_unicode_ci COMMENT = '宠物进化记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for pet_nirvana_config
-- ----------------------------
DROP TABLE IF EXISTS `pet_nirvana_config`;
CREATE TABLE `pet_nirvana_config`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `main_pet_no` int(11) NOT NULL COMMENT '主宠物编号',
  `sub_pet_no` int(11) NOT NULL COMMENT '副宠物编号',
  `nirvana_pet_no` int(11) NOT NULL COMMENT '涅槃兽编号',
  `result_pet_no` int(11) NOT NULL COMMENT '转生结果宠物编号',
  `required_level` int(11) NULL DEFAULT 60 COMMENT '所需等级',
  `base_success_rate` decimal(5, 2) NULL DEFAULT 30.00 COMMENT '基础成功率(%)',
  `cost_gold` bigint(20) NULL DEFAULT 500000 COMMENT '消耗金币',
  `main_growth_inherit` decimal(5, 4) NULL DEFAULT 0.2500 COMMENT '主宠成长继承比例',
  `sub_growth_inherit` decimal(5, 4) NULL DEFAULT 0.0500 COMMENT '副宠成长继承比例',
  `special_rule` text CHARACTER SET utf8 COLLATE utf8_unicode_ci NULL COMMENT '特殊规则(JSON格式)',
  `is_active` tinyint(1) NULL DEFAULT 1 COMMENT '是否启用',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_nirvana_config`(`main_pet_no` ASC, `sub_pet_no` ASC, `nirvana_pet_no` ASC) USING BTREE,
  INDEX `idx_result_pet`(`result_pet_no` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8 COLLATE = utf8_unicode_ci COMMENT = '宠物转生配置表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for pet_nirvana_log
-- ----------------------------
DROP TABLE IF EXISTS `pet_nirvana_log`;
CREATE TABLE `pet_nirvana_log`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `main_pet_id` int(11) NOT NULL COMMENT '主宠物ID',
  `sub_pet_id` int(11) NOT NULL COMMENT '副宠物ID',
  `nirvana_pet_id` int(11) NOT NULL COMMENT '涅槃兽ID',
  `main_pet_no` int(11) NOT NULL COMMENT '主宠物编号',
  `sub_pet_no` int(11) NOT NULL COMMENT '副宠物编号',
  `nirvana_pet_no` int(11) NOT NULL COMMENT '涅槃兽编号',
  `main_growth` decimal(20, 6) NOT NULL COMMENT '主宠成长',
  `sub_growth` decimal(20, 6) NOT NULL COMMENT '副宠成长',
  `main_level` int(11) NOT NULL COMMENT '主宠等级',
  `sub_level` int(11) NOT NULL COMMENT '副宠等级',
  `result_pet_no` int(11) NULL DEFAULT NULL COMMENT '转生结果宠物编号',
  `result_growth` decimal(20, 6) NULL DEFAULT NULL COMMENT '转生结果成长',
  `success_rate` decimal(5, 2) NOT NULL COMMENT '实际成功率',
  `used_item_id` varchar(20) CHARACTER SET utf8 COLLATE utf8_unicode_ci NULL DEFAULT NULL COMMENT '使用的辅助道具ID',
  `cost_gold` bigint(20) NOT NULL COMMENT '消耗金币',
  `is_success` tinyint(1) NOT NULL COMMENT '是否成功',
  `vip_bonus` decimal(5, 2) NULL DEFAULT 0.00 COMMENT 'VIP加成(%)',
  `nirvana_type` enum('NORMAL','FACE_CHANGE') CHARACTER SET utf8 COLLATE utf8_unicode_ci NULL DEFAULT 'NORMAL' COMMENT '转生类型',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '转生时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_user_id`(`user_id` ASC) USING BTREE,
  INDEX `idx_create_time`(`create_time` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8 COLLATE = utf8_unicode_ci COMMENT = '宠物转生记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for pet_synthesis_config
-- ----------------------------
DROP TABLE IF EXISTS `pet_synthesis_config`;
CREATE TABLE `pet_synthesis_config`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `config_key` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '配置键',
  `config_value` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '配置值',
  `config_type` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '配置类型 类型安全：支持INTEGER、DECIMAL、BOOLEAN',
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '配置描述',
  `is_active` tinyint(1) NULL DEFAULT 1 COMMENT '是否启用',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_config_key`(`config_key` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 94 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '宠物合成配置表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for pet_synthesis_formula
-- ----------------------------
DROP TABLE IF EXISTS `pet_synthesis_formula`;
CREATE TABLE `pet_synthesis_formula`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `main_pet_no` int(11) NOT NULL COMMENT '主宠物编号',
  `sub_pet_no` int(11) NOT NULL COMMENT '副宠物编号',
  `main_growth_min` decimal(20, 6) NULL DEFAULT 0.000000 COMMENT '主宠最小成长要求',
  `sub_growth_min` decimal(20, 6) NULL DEFAULT 0.000000 COMMENT '副宠最小成长要求',
  `result_pet_no` int(11) NOT NULL COMMENT '合成结果宠物编号',
  `base_success_rate` decimal(5, 2) NULL DEFAULT 50.00 COMMENT '基础成功率(%)',
  `required_level` int(11) NULL DEFAULT 40 COMMENT '所需等级',
  `cost_gold` bigint(20) NULL DEFAULT 50000 COMMENT '消耗金币',
  `formula_type` enum('FIXED','RANDOM_GOD','RANDOM_HOLY') CHARACTER SET utf8 COLLATE utf8_unicode_ci NULL DEFAULT 'FIXED' COMMENT '公式类型 FIXED（固定公式）、RANDOM_GOD（随机神宠）、RANDOM_HOLY（随机神圣宠物）',
  `is_active` tinyint(1) NULL DEFAULT 1 COMMENT '是否启用',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_synthesis_formula`(`main_pet_no` ASC, `sub_pet_no` ASC) USING BTREE,
  INDEX `idx_result_pet`(`result_pet_no` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 70 CHARACTER SET = utf8 COLLATE = utf8_unicode_ci COMMENT = '宠物合成公式表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for pet_synthesis_log
-- ----------------------------
DROP TABLE IF EXISTS `pet_synthesis_log`;
CREATE TABLE `pet_synthesis_log`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `main_pet_id` int(11) NOT NULL COMMENT '主宠物ID',
  `sub_pet_id` int(11) NOT NULL COMMENT '副宠物ID',
  `main_pet_no` int(11) NOT NULL COMMENT '主宠物编号',
  `sub_pet_no` int(11) NOT NULL COMMENT '副宠物编号',
  `main_growth` decimal(20, 6) NOT NULL COMMENT '主宠成长',
  `sub_growth` decimal(20, 6) NOT NULL COMMENT '副宠成长',
  `result_pet_no` int(11) NULL DEFAULT NULL COMMENT '合成结果宠物编号',
  `result_growth` decimal(20, 6) NULL DEFAULT NULL COMMENT '合成结果成长',
  `success_rate` decimal(5, 2) NOT NULL COMMENT '实际成功率',
  `used_item_id` varchar(20) CHARACTER SET utf8 COLLATE utf8_unicode_ci NULL DEFAULT NULL COMMENT '使用的辅助道具ID',
  `cost_gold` bigint(20) NOT NULL COMMENT '消耗金币',
  `is_success` tinyint(1) NOT NULL COMMENT '是否成功',
  `element_bonus` decimal(5, 2) NULL DEFAULT 0.00 COMMENT '五行加成(%)',
  `vip_bonus` decimal(5, 2) NULL DEFAULT 0.00 COMMENT 'VIP加成(%)',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '合成时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_user_id`(`user_id` ASC) USING BTREE,
  INDEX `idx_create_time`(`create_time` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 18 CHARACTER SET = utf8 COLLATE = utf8_unicode_ci COMMENT = '宠物合成记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for pet_system_config
-- ----------------------------
DROP TABLE IF EXISTS `pet_system_config`;
CREATE TABLE `pet_system_config`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `config_key` varchar(100) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL COMMENT '配置键',
  `config_value` text CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL COMMENT '配置值',
  `config_type` enum('STRING','INTEGER','DECIMAL','BOOLEAN','JSON') CHARACTER SET utf8 COLLATE utf8_unicode_ci NULL DEFAULT 'STRING' COMMENT '配置类型',
  `system_type` enum('EVOLUTION','SYNTHESIS','NIRVANA','COMMON') CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL COMMENT '系统类型',
  `description` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci NULL DEFAULT NULL COMMENT '配置描述',
  `is_active` tinyint(1) NULL DEFAULT 1 COMMENT '是否启用',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_config_key`(`config_key` ASC) USING BTREE,
  INDEX `idx_system_type`(`system_type` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8 COLLATE = utf8_unicode_ci COMMENT = '宠物系统配置表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for pet_transform_config
-- ----------------------------
DROP TABLE IF EXISTS `pet_transform_config`;
CREATE TABLE `pet_transform_config`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `transform_type` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL COMMENT '变换类型',
  `cost_gold` bigint(20) NULL DEFAULT 0 COMMENT '消耗金币',
  `required_level` int(11) NULL DEFAULT 1 COMMENT '所需等级',
  `success_rate` decimal(5, 2) NULL DEFAULT 100.00 COMMENT '成功率（百分比）',
  `cooldown_time` bigint(20) NULL DEFAULT 0 COMMENT '冷却时间（毫秒）',
  `god_pet_rate` decimal(5, 2) NULL DEFAULT 10.00 COMMENT '神宠概率（百分比）',
  `holy_pet_rate` decimal(5, 2) NULL DEFAULT 1.00 COMMENT '神圣宠物概率（百分比）',
  `is_active` tinyint(1) NULL DEFAULT 1 COMMENT '是否启用',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_transform_type`(`transform_type` ASC) USING BTREE,
  INDEX `idx_is_active`(`is_active` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8 COLLATE = utf8_unicode_ci COMMENT = '百变宠物配置表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for pet_transform_log
-- ----------------------------
DROP TABLE IF EXISTS `pet_transform_log`;
CREATE TABLE `pet_transform_log`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `source_pet_id` int(11) NULL DEFAULT NULL COMMENT '源宠物ID',
  `target_pet_no` int(11) NOT NULL COMMENT '目标宠物编号',
  `result_pet_id` int(11) NULL DEFAULT NULL COMMENT '结果宠物ID',
  `transform_type` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL COMMENT '变换类型',
  `cost_gold` bigint(20) NULL DEFAULT 0 COMMENT '消耗金币',
  `used_item_id` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci NULL DEFAULT NULL COMMENT '使用的道具ID',
  `success` tinyint(1) NOT NULL COMMENT '是否成功',
  `is_god_pet` tinyint(1) NULL DEFAULT 0 COMMENT '是否为神宠',
  `is_holy_pet` tinyint(1) NULL DEFAULT 0 COMMENT '是否为神圣宠物',
  `remark` text CHARACTER SET utf8 COLLATE utf8_unicode_ci NULL COMMENT '备注信息',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_user_id`(`user_id` ASC) USING BTREE,
  INDEX `idx_create_time`(`create_time` ASC) USING BTREE,
  INDEX `idx_transform_type`(`transform_type` ASC) USING BTREE,
  INDEX `idx_success`(`success` ASC) USING BTREE,
  INDEX `idx_user_transform_type`(`user_id` ASC, `transform_type` ASC) USING BTREE,
  INDEX `idx_user_success_time`(`user_id` ASC, `success` ASC, `create_time` ASC) USING BTREE,
  INDEX `idx_result_pet`(`result_pet_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_unicode_ci COMMENT = '百变宠物记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for realm_config
-- ----------------------------
DROP TABLE IF EXISTS `realm_config`;
CREATE TABLE `realm_config`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增主键ID',
  `realm_id` int(11) NOT NULL COMMENT '境界ID（唯一业务标识）',
  `realm_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '境界名称',
  `attribute_bonus` decimal(5, 4) NULL DEFAULT 0.0000 COMMENT '属性加成比例',
  `bonus_type` enum('PERCENTAGE','FIXED') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT 'PERCENTAGE' COMMENT '加成类型',
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '境界描述',
  `level_requirement` int(11) NOT NULL DEFAULT 0 COMMENT '等级要求',
  `hp_bonus` int(11) NOT NULL DEFAULT 0 COMMENT '生命值加成',
  `attack_bonus` int(11) NOT NULL DEFAULT 0 COMMENT '攻击力加成',
  `defense_bonus` int(11) NOT NULL DEFAULT 0 COMMENT '防御力加成',
  `realm_level` int(11) NOT NULL DEFAULT 0 COMMENT '境界等级 (对应原项目PetStates的Key)',
  `attribute_bonus_rate` decimal(20, 6) NOT NULL DEFAULT 0.000000 COMMENT '属性加成倍率',
  `upgrade_success_rate` decimal(20, 6) NOT NULL DEFAULT 0.000000 COMMENT '升级成功率(%)',
  `upgrade_cost_gold` bigint(20) NOT NULL DEFAULT 0 COMMENT '升级消耗金币',
  `upgrade_item_required` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '升级所需道具',
  `created_at` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `realm_id`(`realm_id` ASC) USING BTREE,
  INDEX `idx_realm_id`(`realm_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 29 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '境界配置表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for realm_upgrade_log
-- ----------------------------
DROP TABLE IF EXISTS `realm_upgrade_log`;
CREATE TABLE `realm_upgrade_log`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增主键ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `pet_id` int(11) NOT NULL COMMENT '宠物ID',
  `from_realm_level` int(11) NOT NULL COMMENT '升级前境界等级',
  `to_realm_level` int(11) NOT NULL COMMENT '升级后境界等级',
  `upgrade_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '升级类型 (修炼丹/玄元丹)',
  `success` tinyint(1) NOT NULL COMMENT '是否成功 (1成功 0失败)',
  `cost_gold` bigint(20) NOT NULL DEFAULT 0 COMMENT '消耗金币',
  `cost_items` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '消耗道具JSON',
  `created_at` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_user_id`(`user_id` ASC) USING BTREE,
  INDEX `idx_pet_id`(`pet_id` ASC) USING BTREE,
  INDEX `idx_created_at`(`created_at` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '境界升级记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for skill
-- ----------------------------
DROP TABLE IF EXISTS `skill`;
CREATE TABLE `skill`  (
  `skill_id` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '技能唯一标识',
  `skill_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '技能名称',
  `skill_percent` decimal(5, 2) NOT NULL DEFAULT 0.00 COMMENT '技能百分比（支持两位小数）',
  `effect_type` set('攻击','命中','防御','速度','闪避','生命','魔法','加深','抵消','吸血','吸魔') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '技能效果类型',
  `effect_value` json NULL COMMENT '效果数值（JSON格式存储不同效果的具体数值）',
  `mana_cost` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '耗蓝量',
  `buff_info` json NULL COMMENT 'BUFF信息（包含类型、持续回合、数值等）',
  `element_limit` set('金','木','水','火','土') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '五行限制',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `skill_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '技能类型（ACTIVE-主动技能，PASSIVE-被动技能）',
  PRIMARY KEY (`skill_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '技能数据表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for skill_forget_log
-- ----------------------------
DROP TABLE IF EXISTS `skill_forget_log`;
CREATE TABLE `skill_forget_log`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `pet_id` int(11) NOT NULL COMMENT '宠物ID',
  `skill_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '技能ID',
  `forget_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '遗忘时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_user_id`(`user_id` ASC) USING BTREE,
  INDEX `idx_pet_id`(`pet_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '技能遗忘记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for suit_attribute
-- ----------------------------
DROP TABLE IF EXISTS `suit_attribute`;
CREATE TABLE `suit_attribute`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `suit_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '套装ID（关联suit_config表）',
  `piece_count` int(11) NOT NULL COMMENT '激活所需件数（2件/3件/4件/5件）',
  `attribute_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '属性类型（攻击/命中/防御/速度/闪避/生命/魔法/加深/抵消/吸血/吸魔）',
  `attribute_value` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '属性值（数值或百分比字符串）',
  `is_percentage` tinyint(1) NULL DEFAULT 0 COMMENT '是否百分比（0-数值型，1-百分比型）',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_suit_id`(`suit_id` ASC) USING BTREE,
  INDEX `idx_piece_count`(`piece_count` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '套装属性表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for suit_config
-- ----------------------------
DROP TABLE IF EXISTS `suit_config`;
CREATE TABLE `suit_config`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `suit_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '套装ID（业务唯一标识）',
  `suit_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '套装名称（天魔套装/自然套装等）',
  `equipment_list` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '套装装备列表（JSON数组格式存储装备ID）',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '套装描述',
  `total_pieces` int(11) NULL DEFAULT 0 COMMENT '套装总件数',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_suit_id`(`suit_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '套装配置表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for synthesis_valid_pets
-- ----------------------------
DROP TABLE IF EXISTS `synthesis_valid_pets`;
CREATE TABLE `synthesis_valid_pets`  (
  `pet_no` int(11) NOT NULL COMMENT '宠物编号',
  `is_active` tinyint(1) NULL DEFAULT 1 COMMENT '是否启用',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`pet_no`) USING BTREE,
  INDEX `idx_active`(`is_active` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_unicode_ci COMMENT = '可合成宠物ID验证表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for task_config
-- ----------------------------
DROP TABLE IF EXISTS `task_config`;
CREATE TABLE `task_config`  (
  `task_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '任务ID',
  `task_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '任务名称',
  `task_description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '任务描述',
  `task_type` tinyint(4) NULL DEFAULT 0 COMMENT '任务类型(0=普通,1=循环,2=活动)',
  `is_repeatable` tinyint(4) NULL DEFAULT 0 COMMENT '是否可重复(0=否,1=是)',
  `prerequisite_task` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '前置任务ID',
  `required_pet` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '指定宠物ID',
  `reward_config` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '奖励配置(JSON格式)',
  `is_network_task` tinyint(4) NULL DEFAULT 0 COMMENT '是否网络任务',
  `is_active` tinyint(4) NULL DEFAULT 1 COMMENT '是否激活',
  `sort_order` int(11) NULL DEFAULT 0 COMMENT '排序顺序',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`task_id`) USING BTREE,
  INDEX `idx_task_type`(`task_type` ASC) USING BTREE,
  INDEX `idx_prerequisite`(`prerequisite_task` ASC) USING BTREE,
  INDEX `idx_active`(`is_active` ASC) USING BTREE,
  INDEX `idx_sort`(`sort_order` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '任务配置表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for task_objective
-- ----------------------------
DROP TABLE IF EXISTS `task_objective`;
CREATE TABLE `task_objective`  (
  `objective_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '目标ID',
  `task_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '任务ID',
  `objective_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '目标类型(KILL_MONSTER,COLLECT_ITEM,REACH_LEVEL等)',
  `target_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '目标ID(怪物ID/道具ID等)',
  `target_amount` int(11) NOT NULL DEFAULT 1 COMMENT '目标数量',
  `objective_order` int(11) NULL DEFAULT 0 COMMENT '目标顺序',
  `objective_description` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '目标描述',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`objective_id`) USING BTREE,
  INDEX `idx_task_id`(`task_id` ASC) USING BTREE,
  INDEX `idx_objective_type`(`objective_type` ASC) USING BTREE,
  INDEX `idx_target_id`(`target_id` ASC) USING BTREE,
  CONSTRAINT `task_objective_ibfk_1` FOREIGN KEY (`task_id`) REFERENCES `task_config` (`task_id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 12 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '任务目标表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for task_reward_log
-- ----------------------------
DROP TABLE IF EXISTS `task_reward_log`;
CREATE TABLE `task_reward_log`  (
  `log_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `task_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '任务ID',
  `reward_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '奖励类型(ITEM,CURRENCY,EQUIPMENT等)',
  `reward_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '奖励物品ID',
  `reward_amount` int(11) NOT NULL DEFAULT 1 COMMENT '奖励数量',
  `reward_description` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '奖励描述',
  `granted_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '发放时间',
  PRIMARY KEY (`log_id`) USING BTREE,
  INDEX `idx_user_id`(`user_id` ASC) USING BTREE,
  INDEX `idx_task_id`(`task_id` ASC) USING BTREE,
  INDEX `idx_reward_type`(`reward_type` ASC) USING BTREE,
  INDEX `idx_granted_at`(`granted_at` ASC) USING BTREE,
  CONSTRAINT `task_reward_log_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `task_reward_log_ibfk_2` FOREIGN KEY (`task_id`) REFERENCES `task_config` (`task_id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '任务奖励记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for task_type_config
-- ----------------------------
DROP TABLE IF EXISTS `task_type_config`;
CREATE TABLE `task_type_config`  (
  `type_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '类型ID',
  `type_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '类型名称',
  `type_description` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '类型描述',
  `handler_class` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '处理器类名',
  `is_active` tinyint(4) NULL DEFAULT 1 COMMENT '是否激活',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`type_id`) USING BTREE,
  INDEX `idx_is_active`(`is_active` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '任务类型配置表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for user
-- ----------------------------
DROP TABLE IF EXISTS `user`;
CREATE TABLE `user`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `username` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '用户名/账号',
  `password` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '密码（加密存储）',
  `nickname` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '角色昵称',
  `sex` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '性别',
  `vip_level` int(11) NULL DEFAULT 0 COMMENT 'VIP等级',
  `vip_score` int(11) NULL DEFAULT 0 COMMENT 'VIP积分',
  `gold` bigint(20) NULL DEFAULT 0 COMMENT '金币',
  `yuanbao` int(11) NULL DEFAULT 0 COMMENT '元宝',
  `crystal` int(11) NULL DEFAULT 0 COMMENT '水晶',
  `reg_time` datetime NULL DEFAULT NULL COMMENT '注册时间',
  `title` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '称号',
  `main_pet_id` int(11) NULL DEFAULT NULL COMMENT '主宠物ID',
  `main_pet_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '主宠物名称',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `evolution_cd` datetime NULL DEFAULT NULL COMMENT '进化冷却时间',
  `synthesis_cd` datetime NULL DEFAULT NULL COMMENT '合成冷却时间',
  `nirvana_cd` datetime NULL DEFAULT NULL COMMENT '转生冷却时间',
  `money` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '金币数量',
  `supreme_vip` tinyint(1) NULL DEFAULT NULL COMMENT '是否至尊VIP',
  `prop_capacity` int(11) NULL DEFAULT NULL COMMENT '道具容量',
  `pasture_capacity` int(11) NULL DEFAULT NULL COMMENT '牧场容量',
  `dragon_ball_exp` bigint(20) NULL DEFAULT NULL COMMENT '龙珠经验',
  `dragon_ball_level` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '龙珠等级',
  `status` int(11) NULL DEFAULT NULL COMMENT '用户状态 (0正常 1封禁)',
  `prestige` int(11) NULL DEFAULT 0 COMMENT '威望值',
  `auto_battle_count` int(11) NULL DEFAULT 0 COMMENT '自动战斗次数',
  `auto_nirvana_count` int(11) NULL DEFAULT 0 COMMENT '自动合成涅槃次数',
  `monster_count` int(11) NULL DEFAULT 0 COMMENT '刷怪数',
  `next_battle` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '下次战斗遭遇怪物ID',
  `open_maps` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '已开启地图列表（JSON格式）',
  `star_vip` tinyint(1) NULL DEFAULT 0 COMMENT '星辰VIP状态',
  `star_vip_expire` datetime NULL DEFAULT NULL COMMENT '星辰VIP过期时间',
  `task_helper_enabled` tinyint(1) NULL DEFAULT 0 COMMENT '任务助手开启状态',
  `title_attributes` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '称号属性加成(JSON格式)',
  `soul_pet` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '当前装备的魂宠ID',
  `hell_floor` int(11) NULL DEFAULT 0 COMMENT '地狱之门当前层数',
  `tower_floor` int(11) NULL DEFAULT 0 COMMENT '通天塔当前层数',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '用户信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for user_accessory
-- ----------------------------
DROP TABLE IF EXISTS `user_accessory`;
CREATE TABLE `user_accessory`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `accessory_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '灵饰ID',
  `accessory_level` int(11) NULL DEFAULT 1 COMMENT '灵饰等级',
  `is_equipped` tinyint(1) NULL DEFAULT 0 COMMENT '是否装备（0-未装备，1-已装备）',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '获得时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_user_accessory`(`user_id` ASC, `accessory_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '用户灵饰表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for user_equipment
-- ----------------------------
DROP TABLE IF EXISTS `user_equipment`;
CREATE TABLE `user_equipment`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `user_id` int(11) NOT NULL COMMENT '所属用户ID（关联user表）',
  `equip_id` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '装备唯一ID',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '装备名称',
  `icon` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '装备图标',
  `equip_type_id` int(11) NOT NULL COMMENT '装备类型ID（关联equipment_type表）',
  `strengthen_level` int(11) NULL DEFAULT 0 COMMENT '强化等级',
  `slot` int(11) NULL DEFAULT 0 COMMENT '扩展槽位',
  `position` int(11) NULL DEFAULT NULL COMMENT '装备位置（装备栏位置）',
  `is_equipped` tinyint(1) NULL DEFAULT 0 COMMENT '是否已装备（0-未装备，1-已装备）',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '获得时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `element` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '五行属性（金/木/水/火/土/雷/风）',
  `pet_id` int(11) NULL DEFAULT NULL COMMENT '关联宠物ID（装备到哪个宠物）',
  `gemstone_slots` int(11) NULL DEFAULT NULL COMMENT '宝石槽位数量（1-3个）',
  `suit_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '套装ID（关联套装系统）',
  `lssx` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '历史属性（灵饰装备专用）',
  `special_effect` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '特殊效果（法宝等特殊装备）',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_user_id`(`user_id` ASC) USING BTREE,
  INDEX `idx_equip_id`(`equip_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 21 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '用户装备表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for user_item
-- ----------------------------
DROP TABLE IF EXISTS `user_item`;
CREATE TABLE `user_item`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `user_id` int(11) NOT NULL COMMENT '所属用户ID',
  `item_id` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '道具ID',
  `item_count` bigint(20) NOT NULL DEFAULT 0 COMMENT '道具数量',
  `item_pos` int(11) NOT NULL COMMENT '道具位置（1.背包  2.仓库 3.丢弃）',
  `item_seq` int(11) NOT NULL COMMENT '道具序号',
  `create_time` datetime NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 25 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '用户道具表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for user_pet
-- ----------------------------
DROP TABLE IF EXISTS `user_pet`;
CREATE TABLE `user_pet`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `user_id` int(11) NOT NULL COMMENT '所属用户ID（需与用户表关联）',
  `pet_no` int(11) NOT NULL COMMENT '宠物序号',
  `image` int(11) NULL DEFAULT NULL COMMENT '形象编号',
  `exp` bigint(20) NOT NULL DEFAULT 0 COMMENT '当前经验',
  `hp` bigint(20) NOT NULL DEFAULT 0 COMMENT '生命值',
  `mp` bigint(20) NOT NULL DEFAULT 0 COMMENT '魔法值',
  `atk` bigint(20) NOT NULL DEFAULT 0 COMMENT '攻击力',
  `def` bigint(20) NOT NULL DEFAULT 0 COMMENT '防御力',
  `spd` bigint(20) NOT NULL DEFAULT 0 COMMENT '速度',
  `state` int(11) NULL DEFAULT 0 COMMENT '状态',
  `dodge` bigint(20) NOT NULL DEFAULT 0 COMMENT '闪避',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '自定义宠物名称',
  `growth` decimal(20, 2) NULL DEFAULT 0.00 COMMENT '成长值',
  `hit` bigint(20) NULL DEFAULT 0 COMMENT '命中',
  `deepen` decimal(6, 2) NULL DEFAULT 0.00 COMMENT '加深伤害',
  `offset` decimal(6, 2) NULL DEFAULT 0.00 COMMENT '抵消伤害',
  `vamp` decimal(6, 2) NULL DEFAULT 0.00 COMMENT '吸血比例',
  `vamp_mp` decimal(6, 2) NULL DEFAULT 0.00 COMMENT '吸魔比例',
  `custom_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '自定义宠物名称',
  `talisman_state` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '法宝状态',
  `realm` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '境界',
  `evolve_count` int(11) NULL DEFAULT 0 COMMENT '已进化次数',
  `synthesis_count` int(11) NULL DEFAULT 0 COMMENT '合成次数',
  `nirvana_count` int(11) NULL DEFAULT 0 COMMENT '转生次数',
  `last_evolution_time` datetime NULL DEFAULT NULL COMMENT '最后进化时间',
  `last_synthesis_time` datetime NULL DEFAULT NULL COMMENT '最后合成时间',
  `last_nirvana_time` datetime NULL DEFAULT NULL COMMENT '最后转生时间',
  `original_pet_no` int(11) NULL DEFAULT NULL COMMENT '原始宠物编号（用于追溯）',
  `parent_main_pet_id` int(11) NULL DEFAULT NULL COMMENT '父代主宠ID（合成/转生来源）',
  `parent_sub_pet_id` int(11) NULL DEFAULT NULL COMMENT '父代副宠ID（合成/转生来源）',
  `element` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '指定五行',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `status` enum('牧场','携带','丢弃') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '牧场' COMMENT '宠物状态（牧场、携带、丢弃）',
  `is_main` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否主战宠物',
  `level` int(11) NULL DEFAULT NULL COMMENT '等级',
  `max_hp` bigint(20) NULL DEFAULT NULL COMMENT '最大生命值',
  `max_mp` bigint(20) NULL DEFAULT NULL COMMENT '最大魔法值',
  `current_mp` bigint(20) NULL DEFAULT NULL COMMENT '当前魔法值（战斗中消耗）',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 23 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '用户宠物表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for user_pet_skill
-- ----------------------------
DROP TABLE IF EXISTS `user_pet_skill`;
CREATE TABLE `user_pet_skill`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `user_pet_id` int(11) NOT NULL COMMENT '用户宠物表主键ID（外键）',
  `skill_id` int(11) NOT NULL COMMENT '技能编号',
  `skill_level` int(11) NULL DEFAULT 0 COMMENT '技能等级或附加数值',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 16 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '用户宠物技能表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for user_realm
-- ----------------------------
DROP TABLE IF EXISTS `user_realm`;
CREATE TABLE `user_realm`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `realm_level` int(11) NULL DEFAULT 0 COMMENT '境界等级',
  `realm_exp` bigint(20) NULL DEFAULT 0 COMMENT '境界经验',
  `breakthrough_count` int(11) NULL DEFAULT 0 COMMENT '突破次数',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_user_id`(`user_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '用户境界表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for user_task
-- ----------------------------
DROP TABLE IF EXISTS `user_task`;
CREATE TABLE `user_task`  (
  `user_task_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '用户任务ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `task_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '任务ID',
  `task_status` tinyint(4) NULL DEFAULT 1 COMMENT '任务状态(0=已完成,1=进行中,2=已放弃)',
  `accepted_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '接取时间',
  `completed_at` timestamp NULL DEFAULT NULL COMMENT '完成时间',
  `abandoned_at` timestamp NULL DEFAULT NULL COMMENT '放弃时间',
  `completion_count` int(11) NULL DEFAULT 0 COMMENT '完成次数(用于可重复任务)',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`user_task_id`) USING BTREE,
  UNIQUE INDEX `unique_user_task`(`user_id` ASC, `task_id` ASC) USING BTREE,
  INDEX `idx_user_id`(`user_id` ASC) USING BTREE,
  INDEX `idx_task_status`(`task_status` ASC) USING BTREE,
  INDEX `idx_accepted_at`(`accepted_at` ASC) USING BTREE,
  INDEX `user_task_ibfk_2`(`task_id` ASC) USING BTREE,
  CONSTRAINT `user_task_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `user_task_ibfk_2` FOREIGN KEY (`task_id`) REFERENCES `task_config` (`task_id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '用户任务表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for user_task_progress
-- ----------------------------
DROP TABLE IF EXISTS `user_task_progress`;
CREATE TABLE `user_task_progress`  (
  `progress_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '进度ID',
  `user_task_id` int(11) NOT NULL COMMENT '用户任务ID',
  `objective_id` int(11) NOT NULL COMMENT '目标ID',
  `current_amount` int(11) NULL DEFAULT 0 COMMENT '当前进度',
  `is_completed` tinyint(4) NULL DEFAULT 0 COMMENT '是否完成',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`progress_id`) USING BTREE,
  UNIQUE INDEX `unique_progress`(`user_task_id` ASC, `objective_id` ASC) USING BTREE,
  INDEX `idx_user_task_id`(`user_task_id` ASC) USING BTREE,
  INDEX `idx_is_completed`(`is_completed` ASC) USING BTREE,
  INDEX `user_task_progress_ibfk_2`(`objective_id` ASC) USING BTREE,
  CONSTRAINT `user_task_progress_ibfk_1` FOREIGN KEY (`user_task_id`) REFERENCES `user_task` (`user_task_id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `user_task_progress_ibfk_2` FOREIGN KEY (`objective_id`) REFERENCES `task_objective` (`objective_id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '用户任务进度表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for user_treasure
-- ----------------------------
DROP TABLE IF EXISTS `user_treasure`;
CREATE TABLE `user_treasure`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `treasure_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '法宝ID',
  `treasure_level` int(11) NULL DEFAULT 1 COMMENT '法宝等级',
  `is_equipped` tinyint(1) NULL DEFAULT 0 COMMENT '是否装备（0-未装备，1-已装备）',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '获得时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_user_treasure`(`user_id` ASC, `treasure_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '用户法宝表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- View structure for v_pet_transform_stats
-- ----------------------------
DROP VIEW IF EXISTS `v_pet_transform_stats`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `v_pet_transform_stats` AS select `pet_transform_log`.`user_id` AS `user_id`,count(0) AS `total_transforms`,sum((case when (`pet_transform_log`.`success` = 1) then 1 else 0 end)) AS `success_count`,sum((case when (`pet_transform_log`.`success` = 0) then 1 else 0 end)) AS `failure_count`,sum((case when ((`pet_transform_log`.`success` = 1) and (`pet_transform_log`.`is_god_pet` = 1)) then 1 else 0 end)) AS `god_pet_count`,sum((case when ((`pet_transform_log`.`success` = 1) and (`pet_transform_log`.`is_holy_pet` = 1)) then 1 else 0 end)) AS `holy_pet_count`,sum(`pet_transform_log`.`cost_gold`) AS `total_cost_gold`,round((case when (count(0) > 0) then ((sum((case when (`pet_transform_log`.`success` = 1) then 1 else 0 end)) * 100.0) / count(0)) else 0 end),2) AS `success_rate`,max(`pet_transform_log`.`create_time`) AS `last_transform_time` from `pet_transform_log` group by `pet_transform_log`.`user_id`;

-- ----------------------------
-- Procedure structure for sp_cleanup_transform_logs
-- ----------------------------
DROP PROCEDURE IF EXISTS `sp_cleanup_transform_logs`;
delimiter ;;
CREATE PROCEDURE `sp_cleanup_transform_logs`(IN days_to_keep INT)
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE affected_rows INT DEFAULT 0;
    
    -- 删除指定天数之前的日志记录
    DELETE FROM pet_transform_log 
    WHERE create_time < DATE_SUB(NOW(), INTERVAL days_to_keep DAY);
    
    -- 获取影响的行数
    SET affected_rows = ROW_COUNT();
    
    -- 输出清理结果
    SELECT CONCAT('清理了 ', affected_rows, ' 条过期的百变记录') as result;
END
;;
delimiter ;

SET FOREIGN_KEY_CHECKS = 1;
