using BMS.Models.DTOs;
using BMS.Models.Common;
using BMS.Models.Entities;
using BMS.Services.Interfaces;
using SqlSugar;

namespace BMS.Services.Implementations
{
    /// <summary>
    /// 宠物合成公式服务实现
    /// </summary>
    public class PetSynthesisFormulaService : IPetSynthesisFormulaService
    {
        private readonly IDbService _dbService;

        public PetSynthesisFormulaService(IDbService dbService)
        {
            _dbService = dbService;
        }

        /// <summary>
        /// 获取宠物合成公式列表（分页）
        /// </summary>
        public async Task<PagedResult<PetSynthesisFormulaDetailDto>> GetPetSynthesisFormulasAsync(PetSynthesisFormulaQueryDto queryDto)
        {
            try
            {
                // 确保queryDto不为空
                if (queryDto == null)
                {
                    queryDto = new PetSynthesisFormulaQueryDto { Page = 1, PageSize = 10 };
                }

                var query = _dbService.Queryable<pet_synthesis_formula>()
                    .LeftJoin<pet_config>((psf, mpc) => psf.main_pet_no == mpc.pet_no)
                    .LeftJoin<pet_config>((psf, mpc, spc) => psf.sub_pet_no == spc.pet_no)
                    .LeftJoin<pet_config>((psf, mpc, spc, rpc) => psf.result_pet_no == rpc.pet_no)
                    .WhereIF(queryDto.MainPetNo.HasValue, (psf, mpc, spc, rpc) => psf.main_pet_no == queryDto.MainPetNo.Value)
                    .WhereIF(!string.IsNullOrEmpty(queryDto.MainPetName), (psf, mpc, spc, rpc) => mpc.name != null && mpc.name.Contains(queryDto.MainPetName))
                    .WhereIF(queryDto.SubPetNo.HasValue, (psf, mpc, spc, rpc) => psf.sub_pet_no == queryDto.SubPetNo.Value)
                    .WhereIF(!string.IsNullOrEmpty(queryDto.SubPetName), (psf, mpc, spc, rpc) => spc.name != null && spc.name.Contains(queryDto.SubPetName))
                    .WhereIF(queryDto.ResultPetNo.HasValue, (psf, mpc, spc, rpc) => psf.result_pet_no == queryDto.ResultPetNo.Value)
                    .WhereIF(!string.IsNullOrEmpty(queryDto.ResultPetName), (psf, mpc, spc, rpc) => rpc.name != null && rpc.name.Contains(queryDto.ResultPetName))
                    .WhereIF(!string.IsNullOrEmpty(queryDto.FormulaType), (psf, mpc, spc, rpc) => psf.formula_type == queryDto.FormulaType)
                    .WhereIF(queryDto.IsActive.HasValue, (psf, mpc, spc, rpc) => psf.is_active == queryDto.IsActive.Value)
                    .WhereIF(queryDto.MinRequiredLevel.HasValue, (psf, mpc, spc, rpc) => psf.required_level >= queryDto.MinRequiredLevel.Value)
                    .WhereIF(queryDto.MaxRequiredLevel.HasValue, (psf, mpc, spc, rpc) => psf.required_level <= queryDto.MaxRequiredLevel.Value);

                var totalCount = await query.CountAsync();

                var data = await query
                    .OrderBy((psf, mpc, spc, rpc) => psf.id, OrderByType.Desc)
                    .Select((psf, mpc, spc, rpc) => new PetSynthesisFormulaDetailDto
                    {
                        Id = psf.id,
                        MainPetNo = psf.main_pet_no,
                        MainPetName = mpc.name ?? $"宠物{psf.main_pet_no}",
                        MainPetAttribute = mpc.attribute ?? "未知",
                        SubPetNo = psf.sub_pet_no,
                        SubPetName = spc.name ?? $"宠物{psf.sub_pet_no}",
                        SubPetAttribute = spc.attribute ?? "未知",
                        MainGrowthMin = psf.main_growth_min ?? 0.000000m,
                        SubGrowthMin = psf.sub_growth_min ?? 0.000000m,
                        ResultPetNo = psf.result_pet_no,
                        ResultPetName = rpc.name ?? $"宠物{psf.result_pet_no}",
                        ResultPetAttribute = rpc.attribute ?? "未知",
                        BaseSuccessRate = psf.base_success_rate ?? 50.00m,
                        RequiredLevel = psf.required_level ?? 40,
                        CostGold = psf.cost_gold ?? 50000,
                        FormulaType = psf.formula_type ?? "FIXED",
                        IsActive = psf.is_active ?? true,
                        CreateTime = psf.create_time ?? DateTime.Now
                    })
                    .Skip((queryDto.Page - 1) * queryDto.PageSize)
                    .Take(queryDto.PageSize)
                    .ToListAsync();

                return new PagedResult<PetSynthesisFormulaDetailDto>(data, queryDto.Page, queryDto.PageSize, totalCount);
            }
            catch (Exception ex)
            {
                // 记录错误日志
                Console.WriteLine($"获取宠物合成公式列表失败: {ex.Message}");
                Console.WriteLine($"堆栈跟踪: {ex.StackTrace}");

                // 返回空结果
                return new PagedResult<PetSynthesisFormulaDetailDto>(new List<PetSynthesisFormulaDetailDto>(), 1, 10, 0);
            }
        }

        /// <summary>
        /// 根据ID获取宠物合成公式
        /// </summary>
        public async Task<ApiResult<PetSynthesisFormulaDetailDto>> GetByIdAsync(int id)
        {
            try
            {
                var formula = await _dbService.Queryable<pet_synthesis_formula>()
                    .LeftJoin<pet_config>((psf, mpc) => psf.main_pet_no == mpc.pet_no)
                    .LeftJoin<pet_config>((psf, mpc, spc) => psf.sub_pet_no == spc.pet_no)
                    .LeftJoin<pet_config>((psf, mpc, spc, rpc) => psf.result_pet_no == rpc.pet_no)
                    .Where((psf, mpc, spc, rpc) => psf.id == id)
                    .Select((psf, mpc, spc, rpc) => new PetSynthesisFormulaDetailDto
                    {
                        Id = psf.id,
                        MainPetNo = psf.main_pet_no,
                        MainPetName = mpc.name ?? $"宠物{psf.main_pet_no}",
                        MainPetAttribute = mpc.attribute ?? "未知",
                        SubPetNo = psf.sub_pet_no,
                        SubPetName = spc.name ?? $"宠物{psf.sub_pet_no}",
                        SubPetAttribute = spc.attribute ?? "未知",
                        MainGrowthMin = psf.main_growth_min ?? 0.000000m,
                        SubGrowthMin = psf.sub_growth_min ?? 0.000000m,
                        ResultPetNo = psf.result_pet_no,
                        ResultPetName = rpc.name ?? $"宠物{psf.result_pet_no}",
                        ResultPetAttribute = rpc.attribute ?? "未知",
                        BaseSuccessRate = psf.base_success_rate ?? 50.00m,
                        RequiredLevel = psf.required_level ?? 40,
                        CostGold = psf.cost_gold ?? 50000,
                        FormulaType = psf.formula_type ?? "FIXED",
                        IsActive = psf.is_active ?? true,
                        CreateTime = psf.create_time ?? DateTime.Now
                    })
                    .FirstAsync();

                return formula != null
                    ? ApiResult<PetSynthesisFormulaDetailDto>.Ok(formula)
                    : ApiResult<PetSynthesisFormulaDetailDto>.Fail("合成公式不存在");
            }
            catch (Exception ex)
            {
                return ApiResult<PetSynthesisFormulaDetailDto>.Fail($"获取合成公式失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 新增宠物合成公式
        /// </summary>
        public async Task<ApiResult<int>> CreateAsync(PetSynthesisFormulaCreateDto createDto)
        {
            // 检查主宠物是否存在
            var mainPetExists = await _dbService.Queryable<pet_config>()
                .Where(x => x.pet_no == createDto.MainPetNo)
                .AnyAsync();
            if (!mainPetExists)
            {
                return ApiResult<int>.Fail("主宠物不存在");
            }

            // 检查副宠物是否存在
            var subPetExists = await _dbService.Queryable<pet_config>()
                .Where(x => x.pet_no == createDto.SubPetNo)
                .AnyAsync();
            if (!subPetExists)
            {
                return ApiResult<int>.Fail("副宠物不存在");
            }

            // 检查结果宠物是否存在
            var resultPetExists = await _dbService.Queryable<pet_config>()
                .Where(x => x.pet_no == createDto.ResultPetNo)
                .AnyAsync();
            if (!resultPetExists)
            {
                return ApiResult<int>.Fail("结果宠物不存在");
            }

            // 检查合成公式是否已存在
            var exists = await CheckSynthesisFormulaExistsAsync(createDto.MainPetNo, createDto.SubPetNo);
            if (exists)
            {
                return ApiResult<int>.Fail("该合成公式已存在");
            }

            var entity = new pet_synthesis_formula
            {
                main_pet_no = createDto.MainPetNo,
                sub_pet_no = createDto.SubPetNo,
                main_growth_min = createDto.MainGrowthMin,
                sub_growth_min = createDto.SubGrowthMin,
                result_pet_no = createDto.ResultPetNo,
                base_success_rate = createDto.BaseSuccessRate,
                required_level = createDto.RequiredLevel,
                cost_gold = createDto.CostGold,
                formula_type = createDto.FormulaType,
                is_active = createDto.IsActive,
                create_time = DateTime.Now
            };

            var result = await _dbService.Insertable(entity).ExecuteReturnIdentityAsync();
            return result > 0 
                ? ApiResult<int>.Ok(result, "新增合成公式成功")
                : ApiResult<int>.Fail("新增合成公式失败");
        }

        /// <summary>
        /// 更新宠物合成公式
        /// </summary>
        public async Task<ApiResult<bool>> UpdateAsync(PetSynthesisFormulaUpdateDto updateDto)
        {
            // 检查公式是否存在
            var exists = await _dbService.Queryable<pet_synthesis_formula>()
                .Where(x => x.id == updateDto.Id)
                .AnyAsync();
            if (!exists)
            {
                return ApiResult<bool>.Fail("合成公式不存在");
            }

            // 检查主宠物是否存在
            var mainPetExists = await _dbService.Queryable<pet_config>()
                .Where(x => x.pet_no == updateDto.MainPetNo)
                .AnyAsync();
            if (!mainPetExists)
            {
                return ApiResult<bool>.Fail("主宠物不存在");
            }

            // 检查副宠物是否存在
            var subPetExists = await _dbService.Queryable<pet_config>()
                .Where(x => x.pet_no == updateDto.SubPetNo)
                .AnyAsync();
            if (!subPetExists)
            {
                return ApiResult<bool>.Fail("副宠物不存在");
            }

            // 检查结果宠物是否存在
            var resultPetExists = await _dbService.Queryable<pet_config>()
                .Where(x => x.pet_no == updateDto.ResultPetNo)
                .AnyAsync();
            if (!resultPetExists)
            {
                return ApiResult<bool>.Fail("结果宠物不存在");
            }

            // 检查合成公式是否已存在（排除当前记录）
            var duplicateExists = await CheckSynthesisFormulaExistsAsync(updateDto.MainPetNo, updateDto.SubPetNo, updateDto.Id);
            if (duplicateExists)
            {
                return ApiResult<bool>.Fail("该合成公式已存在");
            }

            var result = await _dbService.Updateable<pet_synthesis_formula>()
                .SetColumns(x => new pet_synthesis_formula
                {
                    main_pet_no = updateDto.MainPetNo,
                    sub_pet_no = updateDto.SubPetNo,
                    main_growth_min = updateDto.MainGrowthMin,
                    sub_growth_min = updateDto.SubGrowthMin,
                    result_pet_no = updateDto.ResultPetNo,
                    base_success_rate = updateDto.BaseSuccessRate,
                    required_level = updateDto.RequiredLevel,
                    cost_gold = updateDto.CostGold,
                    formula_type = updateDto.FormulaType,
                    is_active = updateDto.IsActive
                })
                .Where(x => x.id == updateDto.Id)
                .ExecuteCommandAsync();

            return result > 0 
                ? ApiResult<bool>.Ok(true, "更新合成公式成功")
                : ApiResult<bool>.Fail("更新合成公式失败");
        }

        /// <summary>
        /// 删除宠物合成公式
        /// </summary>
        public async Task<ApiResult<bool>> DeleteAsync(int id)
        {
            var result = await _dbService.Deleteable<pet_synthesis_formula>()
                .Where(x => x.id == id)
                .ExecuteCommandAsync();

            return result > 0 
                ? ApiResult<bool>.Ok(true, "删除合成公式成功")
                : ApiResult<bool>.Fail("删除合成公式失败");
        }

        /// <summary>
        /// 批量删除宠物合成公式
        /// </summary>
        public async Task<ApiResult<bool>> BatchDeleteAsync(List<int> ids)
        {
            if (ids == null || !ids.Any())
            {
                return ApiResult<bool>.Fail("请选择要删除的合成公式");
            }

            var result = await _dbService.Deleteable<pet_synthesis_formula>()
                .Where(x => ids.Contains(x.id))
                .ExecuteCommandAsync();

            return result > 0 
                ? ApiResult<bool>.Ok(true, $"成功删除{result}个合成公式")
                : ApiResult<bool>.Fail("批量删除合成公式失败");
        }

        /// <summary>
        /// 切换激活状态
        /// </summary>
        public async Task<ApiResult<bool>> ToggleActiveAsync(int id, bool isActive)
        {
            var result = await _dbService.Updateable<pet_synthesis_formula>()
                .SetColumns(x => new pet_synthesis_formula { is_active = isActive })
                .Where(x => x.id == id)
                .ExecuteCommandAsync();

            return result > 0 
                ? ApiResult<bool>.Ok(true, $"已{(isActive ? "激活" : "禁用")}合成公式")
                : ApiResult<bool>.Fail("操作失败");
        }

        /// <summary>
        /// 获取宠物配置选项
        /// </summary>
        public async Task<List<PetConfigOptionDto>> GetPetConfigOptionsAsync()
        {
            return await _dbService.Queryable<pet_config>()
                .OrderBy(x => x.pet_no)
                .Select(x => new PetConfigOptionDto
                {
                    PetNo = x.pet_no,
                    Name = x.name ?? "",
                    Attribute = x.attribute ?? ""
                })
                .ToListAsync();
        }

        /// <summary>
        /// 检查合成公式是否存在
        /// </summary>
        public async Task<bool> CheckSynthesisFormulaExistsAsync(int mainPetNo, int subPetNo, int? id = null)
        {
            var query = _dbService.Queryable<pet_synthesis_formula>()
                .Where(x => x.main_pet_no == mainPetNo && x.sub_pet_no == subPetNo);

            if (id.HasValue)
            {
                query = query.Where(x => x.id != id.Value);
            }

            return await query.AnyAsync();
        }

        /// <summary>
        /// 根据宠物编号获取合成公式
        /// </summary>
        public async Task<List<PetSynthesisFormulaDetailDto>> GetSynthesisFormulasByPetNoAsync(int mainPetNo, int subPetNo)
        {
            try
            {
                return await _dbService.Queryable<pet_synthesis_formula>()
                    .LeftJoin<pet_config>((psf, mpc) => psf.main_pet_no == mpc.pet_no)
                    .LeftJoin<pet_config>((psf, mpc, spc) => psf.sub_pet_no == spc.pet_no)
                    .LeftJoin<pet_config>((psf, mpc, spc, rpc) => psf.result_pet_no == rpc.pet_no)
                    .Where((psf, mpc, spc, rpc) => psf.main_pet_no == mainPetNo && psf.sub_pet_no == subPetNo && (psf.is_active ?? true) == true)
                    .Select((psf, mpc, spc, rpc) => new PetSynthesisFormulaDetailDto
                    {
                        Id = psf.id,
                        MainPetNo = psf.main_pet_no,
                        MainPetName = mpc.name ?? $"宠物{psf.main_pet_no}",
                        MainPetAttribute = mpc.attribute ?? "未知",
                        SubPetNo = psf.sub_pet_no,
                        SubPetName = spc.name ?? $"宠物{psf.sub_pet_no}",
                        SubPetAttribute = spc.attribute ?? "未知",
                        MainGrowthMin = psf.main_growth_min ?? 0.000000m,
                        SubGrowthMin = psf.sub_growth_min ?? 0.000000m,
                        ResultPetNo = psf.result_pet_no,
                        ResultPetName = rpc.name ?? $"宠物{psf.result_pet_no}",
                        ResultPetAttribute = rpc.attribute ?? "未知",
                        BaseSuccessRate = psf.base_success_rate ?? 50.00m,
                        RequiredLevel = psf.required_level ?? 40,
                        CostGold = psf.cost_gold ?? 50000,
                        FormulaType = psf.formula_type ?? "FIXED",
                        IsActive = psf.is_active ?? true,
                        CreateTime = psf.create_time ?? DateTime.Now
                    })
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"根据宠物编号获取合成公式失败: {ex.Message}");
                return new List<PetSynthesisFormulaDetailDto>();
            }
        }

        /// <summary>
        /// 验证合成条件
        /// </summary>
        public async Task<ApiResult<bool>> ValidateSynthesisConditionsAsync(int mainPetId, int subPetId)
        {
            try
            {
                // 获取主宠物信息
                var mainPet = await _dbService.Queryable<user_pet>()
                    .Where(x => x.id == mainPetId)
                    .FirstAsync();

                if (mainPet == null)
                {
                    return ApiResult<bool>.Fail("主宠物不存在");
                }

                // 获取副宠物信息
                var subPet = await _dbService.Queryable<user_pet>()
                    .Where(x => x.id == subPetId)
                    .FirstAsync();

                if (subPet == null)
                {
                    return ApiResult<bool>.Fail("副宠物不存在");
                }

                // 获取合成公式
                var formula = await _dbService.Queryable<pet_synthesis_formula>()
                    .Where(x => x.main_pet_no == mainPet.pet_no && x.sub_pet_no == subPet.pet_no && (x.is_active ?? true) == true)
                    .FirstAsync();

                if (formula == null)
                {
                    return ApiResult<bool>.Fail("合成公式不存在或未激活");
                }

                // 检查等级要求 - 使用level字段或通过exp计算
                var mainLevel = mainPet.level ?? CalculateLevel(mainPet.exp ?? 0);
                var subLevel = subPet.level ?? CalculateLevel(subPet.exp ?? 0);

                if (mainLevel < formula.required_level || subLevel < formula.required_level)
                {
                    return ApiResult<bool>.Fail($"宠物等级不足，需要{formula.required_level}级");
                }

                // 检查成长要求 - 使用growth字段
                if ((mainPet.growth ?? 0) < formula.main_growth_min)
                {
                    return ApiResult<bool>.Fail($"主宠物成长不足，需要{formula.main_growth_min}");
                }

                if ((subPet.growth ?? 0) < formula.sub_growth_min)
                {
                    return ApiResult<bool>.Fail($"副宠物成长不足，需要{formula.sub_growth_min}");
                }

                // 检查金币要求
                var user = await _dbService.Queryable<user>()
                    .Where(x => x.id == mainPet.user_id)
                    .FirstAsync();

                if (user == null || (user.gold ?? 0) < formula.cost_gold)
                {
                    return ApiResult<bool>.Fail($"金币不足，需要{formula.cost_gold}金币");
                }

                return ApiResult<bool>.Ok(true, "合成条件满足");
            }
            catch (Exception ex)
            {
                return ApiResult<bool>.Fail($"验证合成条件失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 计算宠物等级
        /// </summary>
        private int CalculateLevel(long exp)
        {
            // 简单的等级计算公式，可根据实际需求调整
            return (int)Math.Sqrt(exp / 100) + 1;
        }
    }
}
