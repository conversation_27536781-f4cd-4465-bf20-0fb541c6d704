using Microsoft.AspNetCore.Mvc;
using BMS.Models.DTOs;
using BMS.Services.Interfaces;
using BMS.Models.Common;
using Microsoft.AspNetCore.Authorization;

namespace BMS.Controllers
{
    /// <summary>
    /// 技能控制器
    /// </summary>
    // [Authorize] // 临时注释掉，用于测试接口
    [Route("[controller]/[action]")]
    public class SkillController : Controller
    {
        private readonly ISkillService _skillService;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="skillService">技能服务</param>
        public SkillController(ISkillService skillService)
        {
            _skillService = skillService;
        }

        /// <summary>
        /// 技能管理首页
        /// </summary>
        /// <returns>首页视图</returns>
        [HttpGet]
        public async Task<IActionResult> Index()
        {
            try
            {
                // 获取效果类型和五行限制用于前端下拉选择
                var effectTypes = await _skillService.GetEffectTypesAsync();
                var elementLimits = await _skillService.GetElementLimitsAsync();

                ViewBag.EffectTypes = effectTypes;
                ViewBag.ElementLimits = elementLimits;

                return View();
            }
            catch (Exception ex)
            {
                ViewBag.ErrorMessage = $"加载页面失败：{ex.Message}";
                return View();
            }
        }

        /// <summary>
        /// 分页获取技能列表
        /// </summary>
        /// <param name="queryDto">查询条件</param>
        /// <returns>分页结果</returns>
        [HttpPost]
        public async Task<IActionResult> GetList([FromBody] SkillQueryDto queryDto)
        {
            try
            {
                var result = await _skillService.GetPagedListAsync(queryDto);
                return Json(new
                {
                    code = 200,
                    message = "查询成功",
                    data = result.Data,
                    total = result.TotalCount,
                    page = result.PageIndex,
                    pageSize = result.PageSize
                });
            }
            catch (Exception ex)
            {
                return Json(new
                {
                    code = 500,
                    message = $"查询失败：{ex.Message}"
                });
            }
        }

        /// <summary>
        /// 根据ID获取技能详情
        /// </summary>
        /// <param name="skillId">技能ID</param>
        /// <returns>技能详情</returns>
        [HttpGet]
        public async Task<IActionResult> GetById(string skillId)
        {
            try
            {
                if (string.IsNullOrEmpty(skillId))
                {
                    return Json(ApiResult.Fail("技能ID不能为空"));
                }

                var skill = await _skillService.GetByIdAsync(skillId);
                if (skill == null)
                {
                    return Json(ApiResult.Fail("技能不存在"));
                }

                return Json(ApiResult.Ok(skill));
            }
            catch (Exception ex)
            {
                return Json(ApiResult.Fail($"获取技能详情失败：{ex.Message}"));
            }
        }

        /// <summary>
        /// 创建技能
        /// </summary>
        /// <param name="createDto">创建DTO</param>
        /// <returns>创建结果</returns>
        [HttpPost]
        public async Task<IActionResult> Create([FromBody] SkillCreateDto createDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    var errors = ModelState.Values
                        .SelectMany(v => v.Errors)
                        .Select(e => e.ErrorMessage);
                    return Json(ApiResult.Fail($"数据验证失败：{string.Join(",", errors)}"));
                }

                var result = await _skillService.CreateAsync(createDto);
                return Json(result.Success 
                    ? ApiResult.Ok(result.Data, result.Message) 
                    : ApiResult.Fail(result.Message));
            }
            catch (Exception ex)
            {
                return Json(ApiResult.Fail($"创建技能失败：{ex.Message}"));
            }
        }

        /// <summary>
        /// 更新技能
        /// </summary>
        /// <param name="updateDto">更新DTO</param>
        /// <returns>更新结果</returns>
        [HttpPost]
        public async Task<IActionResult> Update([FromBody] SkillUpdateDto updateDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    var errors = ModelState.Values
                        .SelectMany(v => v.Errors)
                        .Select(e => e.ErrorMessage);
                    return Json(ApiResult.Fail($"数据验证失败：{string.Join(",", errors)}"));
                }

                var result = await _skillService.UpdateAsync(updateDto);
                return Json(result.Success 
                    ? ApiResult.Ok(result.Data, result.Message) 
                    : ApiResult.Fail(result.Message));
            }
            catch (Exception ex)
            {
                return Json(ApiResult.Fail($"更新技能失败：{ex.Message}"));
            }
        }

        /// <summary>
        /// 删除技能
        /// </summary>
        /// <param name="deleteDto">删除DTO</param>
        /// <returns>删除结果</returns>
        [HttpPost]
        public async Task<IActionResult> Delete([FromBody] SkillDeleteDto deleteDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    var errors = ModelState.Values
                        .SelectMany(v => v.Errors)
                        .Select(e => e.ErrorMessage);
                    return Json(ApiResult.Fail($"数据验证失败：{string.Join(",", errors)}"));
                }

                var result = await _skillService.DeleteAsync(deleteDto);
                return Json(result.Success 
                    ? ApiResult.Ok(result.Data, result.Message) 
                    : ApiResult.Fail(result.Message));
            }
            catch (Exception ex)
            {
                return Json(ApiResult.Fail($"删除技能失败：{ex.Message}"));
            }
        }

        /// <summary>
        /// 检查技能ID是否存在
        /// </summary>
        /// <param name="skillId">技能ID</param>
        /// <returns>检查结果</returns>
        [HttpGet]
        public async Task<IActionResult> CheckSkillId(string skillId)
        {
            try
            {
                if (string.IsNullOrEmpty(skillId))
                {
                    return Json(ApiResult.Ok(false, "技能ID不能为空"));
                }

                var exists = await _skillService.CheckSkillIdExistsAsync(skillId);
                return Json(ApiResult.Ok(exists));
            }
            catch (Exception ex)
            {
                return Json(ApiResult.Fail($"检查技能ID失败：{ex.Message}"));
            }
        }
    }
} 