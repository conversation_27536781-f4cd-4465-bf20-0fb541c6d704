using System.ComponentModel.DataAnnotations;

namespace BMS.Models.DTOs
{
    /// <summary>
    /// 用户道具展示DTO
    /// </summary>
    public class UserItemDto
    {
        /// <summary>
        /// 记录ID
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 用户ID
        /// </summary>
        public int UserId { get; set; }

        /// <summary>
        /// 用户名
        /// </summary>
        public string Username { get; set; } = string.Empty;

        /// <summary>
        /// 道具ID
        /// </summary>
        public string ItemId { get; set; } = string.Empty;

        /// <summary>
        /// 道具编号
        /// </summary>
        public int ItemNo { get; set; }

        /// <summary>
        /// 道具名称
        /// </summary>
        public string ItemName { get; set; } = string.Empty;

        /// <summary>
        /// 道具类型
        /// </summary>
        public string? ItemType { get; set; }

        /// <summary>
        /// 道具品质
        /// </summary>
        public string? ItemQuality { get; set; }

        /// <summary>
        /// 道具数量
        /// </summary>
        public long ItemCount { get; set; }

        /// <summary>
        /// 道具位置
        /// </summary>
        public int? ItemPos { get; set; }

        /// <summary>
        /// 道具序号
        /// </summary>
        public int ItemSeq { get; set; }

        /// <summary>
        /// 道具描述
        /// </summary>
        public string? ItemDescription { get; set; }

        /// <summary>
        /// 道具价格
        /// </summary>
        public int? ItemPrice { get; set; }

        /// <summary>
        /// 道具图标
        /// </summary>
        public string? ItemIcon { get; set; }

        /// <summary>
        /// 品质文本
        /// </summary>
        public string QualityText => ItemQuality switch
        {
            "white" => "普通",
            "green" => "精良",
            "blue" => "稀有",
            "purple" => "史诗",
            "orange" => "传说",
            _ => ItemQuality ?? "未知"
        };

        /// <summary>
        /// 品质CSS类
        /// </summary>
        public string QualityCssClass => ItemQuality switch
        {
            "white" => "badge-secondary",
            "green" => "badge-success",
            "blue" => "badge-info",
            "purple" => "badge-primary",
            "orange" => "badge-warning",
            _ => "badge-secondary"
        };
    }

    /// <summary>
    /// 用户道具查询DTO
    /// </summary>
    public class UserItemQueryDto : PagedQueryDto
    {
        /// <summary>
        /// 用户ID
        /// </summary>
        public int? UserId { get; set; }

        /// <summary>
        /// 用户名
        /// </summary>
        public string? Username { get; set; }

        /// <summary>
        /// 道具名称
        /// </summary>
        public string? ItemName { get; set; }

        /// <summary>
        /// 道具类型
        /// </summary>
        public string? ItemType { get; set; }

        /// <summary>
        /// 道具品质
        /// </summary>
        public string? ItemQuality { get; set; }
    }

    /// <summary>
    /// 添加用户道具DTO
    /// </summary>
    public class UserItemAddDto
    {
        /// <summary>
        /// 用户ID
        /// </summary>
        [Required(ErrorMessage = "用户ID不能为空")]
        public int UserId { get; set; }

        /// <summary>
        /// 道具编号
        /// </summary>
        [Required(ErrorMessage = "道具编号不能为空")]
        public int ItemNo { get; set; }

        /// <summary>
        /// 道具数量
        /// </summary>
        [Required(ErrorMessage = "道具数量不能为空")]
        [Range(1, long.MaxValue, ErrorMessage = "道具数量必须大于0")]
        public long ItemCount { get; set; }

        /// <summary>
        /// 道具位置
        /// </summary>
        public int? ItemPos { get; set; }
    }

    /// <summary>
    /// 更新用户道具DTO
    /// </summary>
    public class UserItemUpdateDto
    {
        /// <summary>
        /// 记录ID
        /// </summary>
        [Required(ErrorMessage = "记录ID不能为空")]
        public int Id { get; set; }

        /// <summary>
        /// 道具数量
        /// </summary>
        [Required(ErrorMessage = "道具数量不能为空")]
        [Range(0, long.MaxValue, ErrorMessage = "道具数量不能为负数")]
        public long ItemCount { get; set; }

        /// <summary>
        /// 道具位置
        /// </summary>
        public int? ItemPos { get; set; }
    }

    /// <summary>
    /// 删除用户道具DTO
    /// </summary>
    public class UserItemDeleteDto
    {
        /// <summary>
        /// 记录ID
        /// </summary>
        [Required(ErrorMessage = "记录ID不能为空")]
        public int Id { get; set; }
    }

    /// <summary>
    /// 道具配置DTO
    /// </summary>
    public class ItemConfigDto
    {
        /// <summary>
        /// ID
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 道具编号
        /// </summary>
        public int ItemNo { get; set; }

        /// <summary>
        /// 道具名称
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 道具类型
        /// </summary>
        public string? Type { get; set; }

        /// <summary>
        /// 道具描述
        /// </summary>
        public string? Description { get; set; }

        /// <summary>
        /// 道具品质
        /// </summary>
        public string? Quality { get; set; }

        /// <summary>
        /// 道具图标
        /// </summary>
        public string? Icon { get; set; }

        /// <summary>
        /// 道具价格
        /// </summary>
        public int? Price { get; set; }

        /// <summary>
        /// 使用限制
        /// </summary>
        public string? UseLimit { get; set; }

        /// <summary>
        /// 扩展信息（JSON格式）
        /// </summary>
        public object? Extra { get; set; }

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime? CreateTime { get; set; }
    }

    /// <summary>
    /// 用户道具统计DTO
    /// </summary>
    public class UserItemStatisticsDto
    {
        /// <summary>
        /// 道具总数量
        /// </summary>
        public long TotalItems { get; set; }

        /// <summary>
        /// 道具种类数
        /// </summary>
        public int ItemTypes { get; set; }

        /// <summary>
        /// 普通品质道具数量
        /// </summary>
        public long WhiteItems { get; set; }

        /// <summary>
        /// 精良品质道具数量
        /// </summary>
        public long GreenItems { get; set; }

        /// <summary>
        /// 稀有品质道具数量
        /// </summary>
        public long BlueItems { get; set; }

        /// <summary>
        /// 史诗品质道具数量
        /// </summary>
        public long PurpleItems { get; set; }

        /// <summary>
        /// 传说品质道具数量
        /// </summary>
        public long OrangeItems { get; set; }

        /// <summary>
        /// 道具总价值（估算）
        /// </summary>
        public long TotalValue { get; set; }
    }
} 