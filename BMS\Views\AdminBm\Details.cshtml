@model AdminBmDto
@{
    ViewData["Title"] = "管理员详情";
}

<div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1>管理员详情</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="@Url.Action("Index", "Home")">首页</a></li>
                        <li class="breadcrumb-item"><a href="@Url.Action("Index")">管理员管理</a></li>
                        <li class="breadcrumb-item active">管理员详情</li>
                    </ol>
                </div>
            </div>
        </div>
    </section>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-8 offset-md-2">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">管理员信息</h3>
                        </div>

                        <div id="detailsApp">
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label>用户名</label>
                                            <input type="text" class="form-control" :value="adminData.username" readonly />
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label>真实姓名</label>
                                            <input type="text" class="form-control" :value="adminData.realName" readonly />
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label>手机号</label>
                                            <input type="text" class="form-control" :value="adminData.phone" readonly />
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label>邮箱</label>
                                            <input type="email" class="form-control" :value="adminData.email" readonly />
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label>状态</label>
                                            <input type="text" class="form-control" :value="adminData.status === 1 ? '启用' : '禁用'" readonly />
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label>创建时间</label>
                                            <input type="text" class="form-control" :value="formatDate(adminData.createTime)" readonly />
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label>最后登录时间</label>
                                            <input type="text" class="form-control" :value="formatDate(adminData.lastLoginTime)" readonly />
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <!-- 预留位置，以后可以添加其他字段 -->
                                    </div>
                                </div>
                            </div>

                            <div class="card-footer">
                                <a :href="'@Url.Action("Edit")/' + adminData.id" class="btn btn-primary">
                                    <i class="fas fa-edit"></i> 编辑
                                </a>
                                <a href="@Url.Action("Index")" class="btn btn-secondary">
                                    <i class="fas fa-arrow-left"></i> 返回列表
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

@section Scripts {
    <script>
        // 初始化Vue应用
        Vue.createApp({
            data() {
                return {
                    adminData: {
                        id: '@Model.Id',
                        username: '@Model.Username',
                        realName: '@Html.Raw(Model.RealName ?? "")',
                        phone: '@Html.Raw(Model.Phone ?? "")',
                        email: '@Html.Raw(Model.Email ?? "")',
                        status: '@Model.Status',
                        createTime: '@Model.CreateTime.ToString("yyyy-MM-dd HH:mm:ss")',
                        lastLoginTime: '@(Model.LastLoginTime?.ToString("yyyy-MM-dd HH:mm:ss") ?? "")'
                    }
                };
            },
            methods: {
                // 格式化日期
                formatDate(dateStr) {
                    if (!dateStr) return '暂无记录';
                    return dateStr;
                }
            }
        }).mount('#detailsApp');
    </script>
} 