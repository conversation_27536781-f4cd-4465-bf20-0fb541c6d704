# Claude CLI 新手教程

## 📖 目录
1. [什么是Claude CLI](#什么是claude-cli)
2. [安装指南](#安装指南)
3. [基础使用](#基础使用)
4. [高级功能](#高级功能)
5. [实用技巧](#实用技巧)
6. [常见问题](#常见问题)

## 🤖 什么是Claude CLI

Claude CLI (Command Line Interface) 是Anthropic公司开发的命令行工具，让您可以直接在终端中使用Claude AI的强大功能。它支持代码分析、文件处理、对话交互等多种功能。

### 主要特性
- 🚀 **交互式对话** - 直接在命令行与Claude对话
- 📁 **文件分析** - 分析代码文件、文档等
- 🔧 **代码助手** - 代码审查、重构、调试
- 🌐 **网络访问** - 搜索信息、访问API
- 🛠️ **工具集成** - 支持多种开发工具

## 📦 安装指南

### 系统要求
- Node.js 16.0 或更高版本
- npm 或 yarn 包管理器
- Windows 10/11, macOS, 或 Linux

### 安装步骤

1. **检查Node.js环境**
   ```bash
   node --version
   npm --version
   ```

2. **全局安装Claude CLI**
   ```bash
   npm install -g @anthropic-ai/claude-code
   ```

3. **验证安装**
   ```bash
   claude --version
   ```

### Windows系统特殊配置

如果遇到执行策略问题，请运行：
```powershell
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
```

## 🎯 基础使用

### 1. 启动交互式会话
```bash
claude
```
启动后，您可以直接与Claude对话，输入问题或指令。

### 2. 分析文件
```bash
claude 文件名.txt
claude 代码文件.js
claude 文档.md
```

### 3. 使用提示词
```bash
claude "帮我分析这段代码的性能问题"
claude "解释这个函数的作用"
claude "优化这个算法"
```

### 4. 非交互式输出
```bash
claude -p "生成一个Python函数"
claude --print "分析这个JSON文件"
```

## 🔧 高级功能

### 1. 配置管理
```bash
# 查看配置
claude config list

# 设置主题
claude config set -g theme dark

# 设置API密钥
claude config set -g apiKey YOUR_API_KEY
```

### 2. 会话管理
```bash
# 继续上次对话
claude -c

# 恢复特定会话
claude -r [sessionId]

# 使用特定会话ID
claude --session-id <uuid>
```

### 3. 模型选择
```bash
# 使用最新模型
claude --model sonnet

# 使用特定模型
claude --model claude-sonnet-4-20250514

# 设置备用模型
claude --fallback-model haiku
```

### 4. 权限控制
```bash
# 允许特定工具
claude --allowedTools "Bash(git:*) Edit"

# 禁用特定工具
claude --disallowedTools "Web"

# 绕过权限检查（仅限沙盒环境）
claude --dangerously-skip-permissions
```

### 5. 输入输出格式
```bash
# JSON格式输出
claude -p --output-format json "分析这个函数"

# 流式JSON输出
claude -p --output-format stream-json "实时分析"

# 流式输入
claude -p --input-format stream-json
```

## 💡 实用技巧

### 1. 代码分析
```bash
# 分析整个项目
claude --add-dir ./src ./tests

# 代码审查
claude "请审查这个Python文件的代码质量"

# 性能优化
claude "分析这个函数的性能瓶颈"
```

### 2. 文档生成
```bash
# 生成API文档
claude "为这个函数生成JSDoc注释"

# 创建README
claude "为这个项目生成README.md"

# 编写测试
claude "为这个函数编写单元测试"
```

### 3. 调试帮助
```bash
# 错误分析
claude "分析这个错误信息"

# 代码修复
claude "修复这个代码中的bug"

# 最佳实践建议
claude "这个代码如何改进？"
```

### 4. 学习辅助
```bash
# 概念解释
claude "解释什么是闭包"

# 代码示例
claude "给我一个Promise的示例"

# 学习路径
claude "推荐一个学习React的路径"
```

## 🔍 常见问题

### Q1: 命令未找到
**问题：** `claude: command not found`
**解决：**
1. 检查PATH环境变量是否包含npm全局安装目录
2. 重新安装：`npm install -g @anthropic-ai/claude-code`
3. 重启终端

### Q2: 执行策略错误
**问题：** PowerShell执行策略限制
**解决：**
```powershell
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
```

### Q3: API密钥问题
**问题：** 需要设置API密钥
**解决：**
```bash
claude setup-token
# 或手动设置
claude config set -g apiKey YOUR_API_KEY
```

### Q4: 网络连接问题
**问题：** 无法连接到Claude服务
**解决：**
1. 检查网络连接
2. 确认API密钥有效
3. 检查防火墙设置

### Q5: 权限被拒绝
**问题：** 工具访问被拒绝
**解决：**
```bash
# 允许特定工具
claude --allowedTools "Bash Edit"

# 或使用权限模式
claude --permission-mode acceptEdits
```

## 🎨 使用场景示例

### 场景1：代码审查
```bash
claude 代码文件.js
# 然后输入：请审查这个代码的质量和潜在问题
```

### 场景2：文档编写
```bash
claude -p "为这个API编写使用文档"
```

### 场景3：问题调试
```bash
claude "这个错误是什么意思？如何修复？"
```

### 场景4：学习新技术
```bash
claude "教我如何使用TypeScript"
```

## 📚 进阶学习

### 1. MCP服务器配置
```bash
# 查看MCP配置
claude mcp list

# 添加MCP服务器
claude mcp add <server-name> <server-path>
```

### 2. 自定义配置
```bash
# 创建配置文件
claude config set -g customPrompt "你是一个专业的代码审查员"

# 设置默认模型
claude config set -g defaultModel sonnet
```

### 3. 集成开发环境
```bash
# 自动连接IDE
claude --ide
```

## 🆘 获取帮助

### 内置帮助
```bash
claude --help
claude config --help
claude mcp --help
```

### 官方资源
- [官方文档](https://docs.anthropic.com/claude/docs/claude-code)
- [GitHub仓库](https://github.com/anthropics/anthropic-claude-code)
- [社区支持](https://community.anthropic.com/)

### 更新和维护
```bash
# 检查更新
claude update

# 诊断问题
claude doctor

# 重新安装
npm install -g @anthropic-ai/claude-code@latest
```

---

## 🎉 开始使用

现在您已经掌握了Claude CLI的基础知识！建议从简单的交互式会话开始，逐步探索更多高级功能。

**快速开始：**
```bash
claude
# 输入：你好，请介绍一下你自己
```

祝您使用愉快！🚀