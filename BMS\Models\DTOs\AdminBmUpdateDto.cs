using System.ComponentModel.DataAnnotations;

namespace BMS.Models.DTOs
{
    /// <summary>
    /// 更新管理员DTO
    /// </summary>
    public class AdminBmUpdateDto
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [Required]
        public int Id { get; set; }

        /// <summary>
        /// 用户名
        /// </summary>
        [Required(ErrorMessage = "用户名不能为空")]
        [StringLength(50, ErrorMessage = "用户名长度不能超过50个字符")]
        public string Username { get; set; } = string.Empty;

        /// <summary>
        /// 真实姓名
        /// </summary>
        [StringLength(50, ErrorMessage = "真实姓名长度不能超过50个字符")]
        public string? RealName { get; set; }

        /// <summary>
        /// 手机号
        /// </summary>
        [StringLength(20, ErrorMessage = "手机号长度不能超过20个字符")]
        [RegularExpression(@"^1[3-9]\d{9}$", ErrorMessage = "手机号格式不正确")]
        public string? Phone { get; set; }

        /// <summary>
        /// 邮箱
        /// </summary>
        [StringLength(50, ErrorMessage = "邮箱长度不能超过50个字符")]
        [EmailAddress(ErrorMessage = "邮箱格式不正确")]
        public string? Email { get; set; }

        /// <summary>
        /// 状态：0-禁用，1-启用
        /// </summary>
        public int Status { get; set; } = 1;
    }
} 