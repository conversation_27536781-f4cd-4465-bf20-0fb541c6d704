
- 我的项目使用的框架是ASP.NET Core Web 应用(模型-视图-控制器)

- 使用到的@在页面用要用两个@@，比如 @@click

- 数据库表的实体类已经手动创建在这个路径下：Models/Entities/

- 前端采用vue.global.js 版本用vue3.x

- SqlSugar分页请使用，ToPageList或者ToPageListAsync

- 前端采用vue.global.js+Bootstrap+AdminLTE模式，vue的版本是，vue3.x

- 当前使用的SqlSugar版本5.1.4.195

- css中如果要使用到@那就用两个@@这样才不会有错误

- 尽量不使用Razor语法

- GetList：使用自定义格式 {code: 200, data: [], total: 0}

- GetById, Create, Update, Delete：使用ApiResult格式 {code: 200, data: {...}, message: "..."}

- 所有on-click → v-on:click

- 所有:class → v-bind:class

- 所有:key → v-bind:key

- 所有:value → v-bind:value

- 所有:disabled → v-bind:disabled

- vue.global.js 地址：wwwroot/lib/vue/vue.global.js

- axios.min.js 地址：wwwroot/lib/axios.min.js

- @click要换成v-on:click

- 本项目是一个后台管理系统

- 开发前检查[ ] JavaScript中是否有@符号？

- 开发前检查：[ ] 正则表达式是否包含[]字符？

- 开发前检查：[ ] 注释中是否提到了@符号？

- 开发前检查：[ ] 是否使用了其他可能被Razor解析的特殊字符？

- 我用的是mysql,创建表的实体类默认都是小写

- 页面中的交互都采用vue.js，不能用Scripts或者ASP.NET原生的交互方式

- 不能对Models/Entities下的这些实体类做任何修改

- 不能执行 detnet run这个命令，需要的时候和我说我来执行

- 在控制器中创建的接口如果是HttpPost类型，那参数就必须在DTOs文件夹下创建对应的DTO实体，并且接口接收参数的格式要用FromBody。

- 添加的类、方法、代码段、字段、等必须加备注

- 项目中使用的数据库增删改查必须要用SqlSugar

- 新增任何方法，必须按照项目的架构规范来实现这个功能

- 不能随意去修改我写好的方法

- 需要保持一致的返回格式

- 我手动修改的类名称，不要帮我改回原来的

- SqlSugar技术文档地址：https://www.donet5.com/Doc/1/1180

