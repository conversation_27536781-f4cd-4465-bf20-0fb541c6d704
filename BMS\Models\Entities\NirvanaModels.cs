using SqlSugar;
using System;
using System.ComponentModel.DataAnnotations;

namespace BMS.Models.Entities
{
    /// <summary>
    /// 宠物转生配置表
    /// </summary>
    [SugarTable("pet_nirvana_config")]
    public class PetNirvanaConfig
    {
        /// <summary>
        /// 自增主键
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public int Id { get; set; }

        /// <summary>
        /// 主宠物编号
        /// </summary>
        [SugarColumn(ColumnName = "main_pet_no")]
        public int MainPetNo { get; set; }

        /// <summary>
        /// 副宠物编号
        /// </summary>
        [SugarColumn(ColumnName = "sub_pet_no")]
        public int SubPetNo { get; set; }

        /// <summary>
        /// 涅槃兽编号
        /// </summary>
        [SugarColumn(ColumnName = "nirvana_pet_no")]
        public int NirvanaPetNo { get; set; }

        /// <summary>
        /// 转生结果宠物编号
        /// </summary>
        [SugarColumn(ColumnName = "result_pet_no")]
        public int ResultPetNo { get; set; }

        /// <summary>
        /// 所需等级
        /// </summary>
        [SugarColumn(ColumnName = "required_level")]
        public int RequiredLevel { get; set; } = 60;

        /// <summary>
        /// 基础成功率(%)
        /// </summary>
        [SugarColumn(ColumnName = "base_success_rate")]
        public decimal BaseSuccessRate { get; set; } = 30.00m;

        /// <summary>
        /// 消耗金币
        /// </summary>
        [SugarColumn(ColumnName = "cost_gold")]
        public long CostGold { get; set; } = 500000;

        /// <summary>
        /// 主宠成长继承比例
        /// </summary>
        [SugarColumn(ColumnName = "main_growth_inherit")]
        public decimal MainGrowthInherit { get; set; } = 0.2500m;

        /// <summary>
        /// 副宠成长继承比例
        /// </summary>
        [SugarColumn(ColumnName = "sub_growth_inherit")]
        public decimal SubGrowthInherit { get; set; } = 0.0500m;

        /// <summary>
        /// 特殊规则(JSON格式)
        /// </summary>
        [SugarColumn(ColumnName = "special_rule")]
        public string SpecialRule { get; set; }

        /// <summary>
        /// 是否启用
        /// </summary>
        [SugarColumn(ColumnName = "is_active")]
        public int IsActive { get; set; } = 1;

        /// <summary>
        /// 创建时间
        /// </summary>
        [SugarColumn(ColumnName = "create_time")]
        public DateTime CreateTime { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// 宠物转生记录表
    /// </summary>
    [SugarTable("pet_nirvana_log")]
    public class PetNirvanaLog
    {
        /// <summary>
        /// 自增主键
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public int Id { get; set; }

        /// <summary>
        /// 用户ID
        /// </summary>
        [SugarColumn(ColumnName = "user_id")]
        public int UserId { get; set; }

        /// <summary>
        /// 主宠物ID
        /// </summary>
        [SugarColumn(ColumnName = "main_pet_id")]
        public int MainPetId { get; set; }

        /// <summary>
        /// 副宠物ID
        /// </summary>
        [SugarColumn(ColumnName = "sub_pet_id")]
        public int SubPetId { get; set; }

        /// <summary>
        /// 涅槃兽ID
        /// </summary>
        [SugarColumn(ColumnName = "nirvana_pet_id")]
        public int NirvanaPetId { get; set; }

        /// <summary>
        /// 主宠物编号
        /// </summary>
        [SugarColumn(ColumnName = "main_pet_no")]
        public int MainPetNo { get; set; }

        /// <summary>
        /// 副宠物编号
        /// </summary>
        [SugarColumn(ColumnName = "sub_pet_no")]
        public int SubPetNo { get; set; }

        /// <summary>
        /// 涅槃兽编号
        /// </summary>
        [SugarColumn(ColumnName = "nirvana_pet_no")]
        public int NirvanaPetNo { get; set; }

        /// <summary>
        /// 主宠成长
        /// </summary>
        [SugarColumn(ColumnName = "main_growth")]
        public decimal MainGrowth { get; set; }

        /// <summary>
        /// 副宠成长
        /// </summary>
        [SugarColumn(ColumnName = "sub_growth")]
        public decimal SubGrowth { get; set; }

        /// <summary>
        /// 主宠等级
        /// </summary>
        [SugarColumn(ColumnName = "main_level")]
        public int MainLevel { get; set; }

        /// <summary>
        /// 副宠等级
        /// </summary>
        [SugarColumn(ColumnName = "sub_level")]
        public int SubLevel { get; set; }

        /// <summary>
        /// 转生结果宠物编号
        /// </summary>
        [SugarColumn(ColumnName = "result_pet_no")]
        public int? ResultPetNo { get; set; }

        /// <summary>
        /// 转生结果成长
        /// </summary>
        [SugarColumn(ColumnName = "result_growth")]
        public decimal? ResultGrowth { get; set; }

        /// <summary>
        /// 实际成功率
        /// </summary>
        [SugarColumn(ColumnName = "success_rate")]
        public decimal SuccessRate { get; set; }

        /// <summary>
        /// 使用的辅助道具ID
        /// </summary>
        [SugarColumn(ColumnName = "used_item_id")]
        public string UsedItemId { get; set; }

        /// <summary>
        /// 消耗金币
        /// </summary>
        [SugarColumn(ColumnName = "cost_gold")]
        public long CostGold { get; set; }

        /// <summary>
        /// 是否成功
        /// </summary>
        [SugarColumn(ColumnName = "is_success")]
        public int IsSuccess { get; set; }

        /// <summary>
        /// VIP加成(%)
        /// </summary>
        [SugarColumn(ColumnName = "vip_bonus")]
        public decimal VipBonus { get; set; } = 0.00m;

        /// <summary>
        /// 转生类型
        /// </summary>
        [SugarColumn(ColumnName = "nirvana_type")]
        public string NirvanaType { get; set; } = "NORMAL";

        /// <summary>
        /// 转生时间
        /// </summary>
        [SugarColumn(ColumnName = "create_time")]
        public DateTime CreateTime { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// 转生类型枚举
    /// </summary>
    public enum NirvanaTypeEnum
    {
        /// <summary>
        /// 普通转生
        /// </summary>
        NORMAL,
        /// <summary>
        /// 变脸转生
        /// </summary>
        FACE_CHANGE
    }
}
