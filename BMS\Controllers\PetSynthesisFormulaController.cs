using Microsoft.AspNetCore.Mvc;
using BMS.Models.Entities;
using BMS.Services.Interfaces;
using BMS.Models.DTOs;
using BMS.Models.Common;

namespace BMS.Controllers
{
    /// <summary>
    /// 宠物合成公式管理控制器
    /// </summary>
    public class PetSynthesisFormulaController : Controller
    {
        private readonly IPetSynthesisFormulaService _petSynthesisFormulaService;

        public PetSynthesisFormulaController(IPetSynthesisFormulaService petSynthesisFormulaService)
        {
            _petSynthesisFormulaService = petSynthesisFormulaService;
        }

        /// <summary>
        /// 宠物合成公式管理首页
        /// </summary>
        /// <returns>宠物合成公式管理页面</returns>
        [HttpGet]
        public IActionResult Index()
        {
            return View();
        }

        /// <summary>
        /// 获取宠物合成公式列表（分页）
        /// </summary>
        /// <param name="queryDto">查询条件</param>
        /// <returns>分页结果</returns>
        [HttpPost]
        [ActionName("GetList")]
        public async Task<IActionResult> GetList([FromBody] PetSynthesisFormulaQueryDto queryDto)
        {
            try
            {
                // 添加详细的日志记录
                Console.WriteLine($"接收到查询请求: {System.Text.Json.JsonSerializer.Serialize(queryDto)}");

                // 确保queryDto不为空
                if (queryDto == null)
                {
                    queryDto = new PetSynthesisFormulaQueryDto { Page = 1, PageSize = 10 };
                }

                var result = await _petSynthesisFormulaService.GetPetSynthesisFormulasAsync(queryDto);

                Console.WriteLine($"查询结果: 总数={result.TotalCount}, 数据条数={result.Data?.Count ?? 0}");

                return Json(new { code = 200, data = result.Data, total = result.TotalCount });
            }
            catch (Exception ex)
            {
                Console.WriteLine($"获取宠物合成公式列表异常: {ex.Message}");
                Console.WriteLine($"异常堆栈: {ex.StackTrace}");

                return Json(new { code = 500, message = $"获取宠物合成公式列表失败：{ex.Message}", stackTrace = ex.StackTrace });
            }
        }

        /// <summary>
        /// 新增宠物合成公式
        /// </summary>
        /// <param name="createDto">新增DTO</param>
        /// <returns>操作结果</returns>
        [HttpPost]
        [ActionName("Create")]
        public async Task<IActionResult> Create([FromBody] PetSynthesisFormulaCreateDto createDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    var errors = ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage);
                    return Json(ApiResult.Fail(string.Join(", ", errors)));
                }

                var result = await _petSynthesisFormulaService.CreateAsync(createDto);
                return Json(result.Success ? ApiResult.Ok(result.Data, result.Message) : ApiResult.Fail(result.Message));
            }
            catch (Exception ex)
            {
                return Json(ApiResult.Fail($"新增宠物合成公式失败：{ex.Message}"));
            }
        }

        /// <summary>
        /// 更新宠物合成公式
        /// </summary>
        /// <param name="updateDto">更新DTO</param>
        /// <returns>操作结果</returns>
        [HttpPost]
        [ActionName("Update")]
        public async Task<IActionResult> Update([FromBody] PetSynthesisFormulaUpdateDto updateDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    var errors = ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage);
                    return Json(ApiResult.Fail(string.Join(", ", errors)));
                }

                var result = await _petSynthesisFormulaService.UpdateAsync(updateDto);
                return Json(result.Success ? ApiResult.Ok(result.Data, result.Message) : ApiResult.Fail(result.Message));
            }
            catch (Exception ex)
            {
                return Json(ApiResult.Fail($"更新宠物合成公式失败：{ex.Message}"));
            }
        }

        /// <summary>
        /// 删除宠物合成公式
        /// </summary>
        /// <param name="deleteDto">删除DTO</param>
        /// <returns>操作结果</returns>
        [HttpPost]
        [ActionName("Delete")]
        public async Task<IActionResult> Delete([FromBody] PetSynthesisFormulaDeleteDto deleteDto)
        {
            try
            {
                var result = await _petSynthesisFormulaService.DeleteAsync(deleteDto.Id);
                return Json(result.Success ? ApiResult.Ok(result.Data, result.Message) : ApiResult.Fail(result.Message));
            }
            catch (Exception ex)
            {
                return Json(ApiResult.Fail($"删除宠物合成公式失败：{ex.Message}"));
            }
        }

        /// <summary>
        /// 获取宠物合成公式详情
        /// </summary>
        /// <param name="id">公式ID</param>
        /// <returns>公式详情</returns>
        [HttpGet]
        [ActionName("GetDetail")]
        public async Task<IActionResult> GetDetail(int id)
        {
            try
            {
                var result = await _petSynthesisFormulaService.GetByIdAsync(id);
                return Json(result.Success ? ApiResult.Ok(result.Data) : ApiResult.Fail(result.Message));
            }
            catch (Exception ex)
            {
                return Json(ApiResult.Fail($"获取宠物合成公式详情失败：{ex.Message}"));
            }
        }

        /// <summary>
        /// 获取宠物配置选项
        /// </summary>
        /// <returns>宠物配置选项</returns>
        [HttpGet]
        [ActionName("GetPetConfigOptions")]
        public async Task<IActionResult> GetPetConfigOptions()
        {
            try
            {
                var result = await _petSynthesisFormulaService.GetPetConfigOptionsAsync();
                return Json(new { code = 200, data = result });
            }
            catch (Exception ex)
            {
                return Json(new { code = 500, message = $"获取宠物配置选项失败：{ex.Message}" });
            }
        }

        /// <summary>
        /// 检查合成公式是否存在
        /// </summary>
        /// <param name="mainPetNo">主宠物编号</param>
        /// <param name="subPetNo">副宠物编号</param>
        /// <param name="id">排除的ID</param>
        /// <returns>检查结果</returns>
        [HttpGet]
        [ActionName("CheckSynthesisFormulaExists")]
        public async Task<IActionResult> CheckSynthesisFormulaExists(int mainPetNo, int subPetNo, int? id = null)
        {
            try
            {
                var exists = await _petSynthesisFormulaService.CheckSynthesisFormulaExistsAsync(mainPetNo, subPetNo, id);
                return Json(new { code = 200, data = exists });
            }
            catch (Exception ex)
            {
                return Json(new { code = 500, message = $"检查合成公式失败：{ex.Message}" });
            }
        }

        /// <summary>
        /// 批量删除宠物合成公式
        /// </summary>
        /// <param name="ids">公式ID列表</param>
        /// <returns>操作结果</returns>
        [HttpPost]
        [ActionName("BatchDelete")]
        public async Task<IActionResult> BatchDelete([FromBody] List<int> ids)
        {
            try
            {
                var result = await _petSynthesisFormulaService.BatchDeleteAsync(ids);
                return Json(result.Success ? ApiResult.Ok(result.Data, result.Message) : ApiResult.Fail(result.Message));
            }
            catch (Exception ex)
            {
                return Json(ApiResult.Fail($"批量删除宠物合成公式失败：{ex.Message}"));
            }
        }

        /// <summary>
        /// 切换激活状态
        /// </summary>
        /// <param name="toggleDto">切换DTO</param>
        /// <returns>操作结果</returns>
        [HttpPost]
        [ActionName("ToggleActive")]
        public async Task<IActionResult> ToggleActive([FromBody] PetSynthesisFormulaToggleDto toggleDto)
        {
            try
            {
                var result = await _petSynthesisFormulaService.ToggleActiveAsync(toggleDto.Id, toggleDto.IsActive);
                return Json(result.Success ? ApiResult.Ok(result.Data, result.Message) : ApiResult.Fail(result.Message));
            }
            catch (Exception ex)
            {
                return Json(ApiResult.Fail($"切换激活状态失败：{ex.Message}"));
            }
        }

        /// <summary>
        /// 根据宠物编号获取合成公式
        /// </summary>
        /// <param name="mainPetNo">主宠物编号</param>
        /// <param name="subPetNo">副宠物编号</param>
        /// <returns>合成公式列表</returns>
        [HttpGet]
        [ActionName("GetSynthesisFormulasByPetNo")]
        public async Task<IActionResult> GetSynthesisFormulasByPetNo(int mainPetNo, int subPetNo)
        {
            try
            {
                var result = await _petSynthesisFormulaService.GetSynthesisFormulasByPetNoAsync(mainPetNo, subPetNo);
                return Json(new { code = 200, data = result });
            }
            catch (Exception ex)
            {
                return Json(new { code = 500, message = $"获取合成公式失败：{ex.Message}" });
            }
        }

        /// <summary>
        /// 验证合成条件
        /// </summary>
        /// <param name="mainPetId">主宠物ID</param>
        /// <param name="subPetId">副宠物ID</param>
        /// <returns>验证结果</returns>
        [HttpGet]
        [ActionName("ValidateSynthesisConditions")]
        public async Task<IActionResult> ValidateSynthesisConditions(int mainPetId, int subPetId)
        {
            try
            {
                var result = await _petSynthesisFormulaService.ValidateSynthesisConditionsAsync(mainPetId, subPetId);
                return Json(result.Success ? ApiResult.Ok(result.Data, result.Message) : ApiResult.Fail(result.Message));
            }
            catch (Exception ex)
            {
                return Json(ApiResult.Fail($"验证合成条件失败：{ex.Message}"));
            }
        }

        /// <summary>
        /// 测试数据库连接
        /// </summary>
        /// <returns>测试结果</returns>
        [HttpGet]
        [ActionName("TestConnection")]
        public async Task<IActionResult> TestConnection()
        {
            try
            {
                // 测试基本查询
                var formulaCount = await _petSynthesisFormulaService.GetPetSynthesisFormulasAsync(new PetSynthesisFormulaQueryDto { Page = 1, PageSize = 1 });
                var petCount = await _petSynthesisFormulaService.GetPetConfigOptionsAsync();

                return Json(new {
                    code = 200,
                    message = "数据库连接正常",
                    formulaCount = formulaCount.TotalCount,
                    petOptionsCount = petCount.Count
                });
            }
            catch (Exception ex)
            {
                return Json(new {
                    code = 500,
                    message = $"数据库连接测试失败：{ex.Message}",
                    stackTrace = ex.StackTrace
                });
            }
        }
    }
}
