namespace BMS.Models.DTOs
{
    /// <summary>
    /// 装备类型DTO
    /// </summary>
    public class EquipmentTypeDto
    {
        /// <summary>
        /// 自增ID
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 装备类型ID
        /// </summary>
        public string EquipTypeId { get; set; } = string.Empty;

        /// <summary>
        /// 装备类型名称
        /// </summary>
        public string TypeName { get; set; } = string.Empty;

        /// <summary>
        /// 类型描述
        /// </summary>
        public string? Description { get; set; }

        /// <summary>
        /// 排序号
        /// </summary>
        public int SortOrder { get; set; }

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime? CreateTime { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime? UpdateTime { get; set; }

        /// <summary>
        /// 关联装备数量
        /// </summary>
        public int EquipmentCount { get; set; }
    }
} 