[2025-09-06 11:48:29.110 +08:00 INF] BMS: 🚀 应用程序启动，日志系统已配置
[2025-09-06 11:48:29.148 +08:00 DBG] BMS: 🔧 调试级别日志测试
[2025-09-06 11:48:29.169 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-06 11:48:29.173 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-06 11:48:29.308 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-06 11:48:29.846 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-06 11:48:29.859 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-06 11:48:29.860 +08:00 INF] BMS: ✅ DbContext 初始化成功，日志文件位置: logs/
[2025-09-06 11:48:30.938 +08:00 WRN] Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware: Failed to determine the https port for redirect.
[2025-09-06 11:48:38.168 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-06 11:48:38.171 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-06 11:48:38.176 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-06 11:48:38.208 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-06 11:48:38.209 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-06 11:48:38.275 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-06 11:48:38.277 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-06 11:48:38.278 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-06 11:48:38.311 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-06 11:48:38.312 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-06 11:48:38.421 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `user`  
[2025-09-06 11:48:38.543 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `user`  
[2025-09-06 11:48:38.648 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`username`,`password`,`nickname`,`sex`,`vip_level`,`vip_score`,`gold`,`yuanbao`,`crystal`,`reg_time`,`title`,`main_pet_id`,`main_pet_name`,`create_time`,`evolution_cd`,`synthesis_cd`,`nirvana_cd`,`money`,`supreme_vip`,`prop_capacity`,`pasture_capacity`,`dragon_ball_exp`,`dragon_ball_level`,`status`,`prestige`,`auto_battle_count`,`auto_nirvana_count`,`monster_count`,`next_battle`,`open_maps`,`star_vip`,`star_vip_expire`,`task_helper_enabled`,`title_attributes`,`soul_pet`,`hell_floor`,`tower_floor` FROM `user`    ORDER BY `create_time` DESC LIMIT 0,1000
[2025-09-06 11:48:38.694 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`username`,`password`,`nickname`,`sex`,`vip_level`,`vip_score`,`gold`,`yuanbao`,`crystal`,`reg_time`,`title`,`main_pet_id`,`main_pet_name`,`create_time`,`evolution_cd`,`synthesis_cd`,`nirvana_cd`,`money`,`supreme_vip`,`prop_capacity`,`pasture_capacity`,`dragon_ball_exp`,`dragon_ball_level`,`status`,`prestige`,`auto_battle_count`,`auto_nirvana_count`,`monster_count`,`next_battle`,`open_maps`,`star_vip`,`star_vip_expire`,`task_helper_enabled`,`title_attributes`,`soul_pet`,`hell_floor`,`tower_floor` FROM `user`    ORDER BY `create_time` DESC LIMIT 0,1000
[2025-09-06 11:48:38.740 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-06 11:48:38.757 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-06 11:48:38.813 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-06 11:48:38.854 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-06 11:48:38.871 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-06 11:48:38.882 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `id` AS `Id` , `pet_no` AS `PetNo` , `name` AS `Name` , `attribute` AS `Attribute` , `skill` AS `Skill`  FROM `pet_config` ORDER BY `pet_no` ASC 
[2025-09-06 11:48:38.922 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `id` AS `Id` , `pet_no` AS `PetNo` , `name` AS `Name` , `attribute` AS `Attribute` , `skill` AS `Skill`  FROM `pet_config` ORDER BY `pet_no` ASC 
[2025-09-06 11:48:38.956 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-06 11:48:38.958 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-06 11:48:38.959 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-06 11:48:38.990 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-06 11:48:38.992 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-06 11:48:39.040 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )   
[2025-09-06 11:48:39.073 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )   
[2025-09-06 11:48:39.106 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )    | 参数: [@MethodConst0=, @MethodConst1=, @MethodConst2=, @MethodConst3=]
[2025-09-06 11:48:39.141 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )   
[2025-09-06 11:48:39.150 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `up`.`id` AS `Id` , `up`.`user_id` AS `UserId` , IFNULL(`u`.`username`,@MethodConst4) AS `UserName` , `up`.`pet_no` AS `PetNo` , IFNULL(`pc`.`name`,@MethodConst5) AS `PetName` , IFNULL(`pc`.`attribute`,@MethodConst6) AS `PetAttribute` , `up`.`image` AS `Image` , `up`.`exp` AS `Exp` , `up`.`hp` AS `Hp` , `up`.`mp` AS `Mp` , `up`.`atk` AS `Atk` , `up`.`def` AS `Def` , `up`.`spd` AS `Spd` , `up`.`state` AS `State` , `up`.`dodge` AS `Dodge` , `up`.`growth` AS `Growth` , `up`.`hit` AS `Hit` , `up`.`realm` AS `Realm` , `up`.`evolve_count` AS `EvolveCount` , `up`.`element` AS `Element` , `up`.`create_time` AS `CreateTime` , IFNULL(`up`.`status`,@MethodConst7) AS `Status` , `up`.`is_main` AS `IsMain`  FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )     ORDER BY `up`.`create_time` DESC LIMIT 0,10 | 参数: [@MethodConst0=, @MethodConst1=, @MethodConst2=, @MethodConst3=, @MethodConst4=, @MethodConst5=, @MethodConst6=, @MethodConst7=]
[2025-09-06 11:48:39.188 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `up`.`id` AS `Id` , `up`.`user_id` AS `UserId` , IFNULL(`u`.`username`,@MethodConst4) AS `UserName` , `up`.`pet_no` AS `PetNo` , IFNULL(`pc`.`name`,@MethodConst5) AS `PetName` , IFNULL(`pc`.`attribute`,@MethodConst6) AS `PetAttribute` , `up`.`image` AS `Image` , `up`.`exp` AS `Exp` , `up`.`hp` AS `Hp` , `up`.`mp` AS `Mp` , `up`.`atk` AS `Atk` , `up`.`def` AS `Def` , `up`.`spd` AS `Spd` , `up`.`state` AS `State` , `up`.`dodge` AS `Dodge` , `up`.`growth` AS `Growth` , `up`.`hit` AS `Hit` , `up`.`realm` AS `Realm` , `up`.`evolve_count` AS `EvolveCount` , `up`.`element` AS `Element` , `up`.`create_time` AS `CreateTime` , IFNULL(`up`.`status`,@MethodConst7) AS `Status` , `up`.`is_main` AS `IsMain`  FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )     ORDER BY `up`.`create_time` DESC LIMIT 0,10
[2025-09-06 11:48:39.210 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=31, @MethodConst1=]
[2025-09-06 11:48:39.244 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:48:39.258 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=688500, @is_active1=1]
[2025-09-06 11:48:39.292 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:48:39.298 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=30, @MethodConst1=]
[2025-09-06 11:48:39.332 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:48:39.338 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=637000, @is_active1=1]
[2025-09-06 11:48:39.369 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:48:39.373 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=24, @MethodConst1=]
[2025-09-06 11:48:39.406 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:48:39.411 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=10, @is_active1=1]
[2025-09-06 11:48:39.443 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:48:39.446 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=23, @MethodConst1=]
[2025-09-06 11:48:39.476 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:48:39.478 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=637000, @is_active1=1]
[2025-09-06 11:48:39.508 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:48:39.518 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=22, @MethodConst1=]
[2025-09-06 11:48:39.551 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:48:39.555 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=100, @is_active1=1]
[2025-09-06 11:48:39.586 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:48:39.588 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=13, @MethodConst1=]
[2025-09-06 11:48:39.618 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:48:39.620 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=11, @MethodConst1=]
[2025-09-06 11:48:39.652 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:48:39.654 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=19, @MethodConst1=]
[2025-09-06 11:48:39.686 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:48:39.688 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=451000, @is_active1=1]
[2025-09-06 11:48:39.718 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:48:39.720 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=16, @MethodConst1=]
[2025-09-06 11:48:39.750 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:48:39.753 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=451000, @is_active1=1]
[2025-09-06 11:48:39.783 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:48:39.790 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=18, @MethodConst1=]
[2025-09-06 11:48:39.820 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:48:39.831 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=451000, @is_active1=1]
[2025-09-06 11:48:39.864 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:52:17.401 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-06 11:52:17.413 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-06 11:52:17.422 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-06 11:52:17.654 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-06 11:52:17.655 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-06 11:52:17.680 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `up`.`id` AS `Id` , `up`.`user_id` AS `UserId` , IFNULL(`u`.`username`,@MethodConst5) AS `UserName` , `up`.`pet_no` AS `PetNo` , IFNULL(`pc`.`name`,@MethodConst6) AS `PetName` , IFNULL(`pc`.`attribute`,@MethodConst7) AS `PetAttribute` , `up`.`image` AS `Image` , `up`.`exp` AS `Exp` , `up`.`hp` AS `Hp` , `up`.`mp` AS `Mp` , `up`.`atk` AS `Atk` , `up`.`def` AS `Def` , `up`.`spd` AS `Spd` , `up`.`state` AS `State` , `up`.`dodge` AS `Dodge` , `up`.`growth` AS `Growth` , `up`.`hit` AS `Hit` , `up`.`realm` AS `Realm` , `up`.`evolve_count` AS `EvolveCount` , `up`.`element` AS `Element` , `up`.`create_time` AS `CreateTime` , IFNULL(`up`.`status`,@MethodConst8) AS `Status` , `up`.`is_main` AS `IsMain`  FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )    WHERE ( `up`.`id` = @id0 )   LIMIT 0,1 | 参数: [@id0=22, @MethodConst1=, @MethodConst2=, @MethodConst3=, @MethodConst4=, @MethodConst5=, @MethodConst6=, @MethodConst7=, @MethodConst8=]
[2025-09-06 11:52:17.715 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `up`.`id` AS `Id` , `up`.`user_id` AS `UserId` , IFNULL(`u`.`username`,@MethodConst5) AS `UserName` , `up`.`pet_no` AS `PetNo` , IFNULL(`pc`.`name`,@MethodConst6) AS `PetName` , IFNULL(`pc`.`attribute`,@MethodConst7) AS `PetAttribute` , `up`.`image` AS `Image` , `up`.`exp` AS `Exp` , `up`.`hp` AS `Hp` , `up`.`mp` AS `Mp` , `up`.`atk` AS `Atk` , `up`.`def` AS `Def` , `up`.`spd` AS `Spd` , `up`.`state` AS `State` , `up`.`dodge` AS `Dodge` , `up`.`growth` AS `Growth` , `up`.`hit` AS `Hit` , `up`.`realm` AS `Realm` , `up`.`evolve_count` AS `EvolveCount` , `up`.`element` AS `Element` , `up`.`create_time` AS `CreateTime` , IFNULL(`up`.`status`,@MethodConst8) AS `Status` , `up`.`is_main` AS `IsMain`  FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )    WHERE ( `up`.`id` = @id0 )   LIMIT 0,1
[2025-09-06 11:52:17.720 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=22, @MethodConst1=]
[2025-09-06 11:52:17.754 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:52:17.763 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=100, @is_active1=1]
[2025-09-06 11:52:17.797 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:52:17.805 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `up`.`id` AS `Id` , `up`.`user_id` AS `UserId` , `up`.`pet_no` AS `PetNo` , IFNULL(`pc`.`name`,@MethodConst1) AS `PetName` , IFNULL(`pc`.`attribute`,@MethodConst2) AS `PetAttribute` , `up`.`image` AS `Image` , `up`.`exp` AS `Exp` , `up`.`hp` AS `Hp` , `up`.`mp` AS `Mp` , `up`.`atk` AS `Atk` , `up`.`def` AS `Def` , `up`.`spd` AS `Spd` , `up`.`state` AS `State` , `up`.`dodge` AS `Dodge` , `up`.`growth` AS `Growth` , `up`.`hit` AS `Hit` , `up`.`realm` AS `Realm` , `up`.`evolve_count` AS `EvolveCount` , `up`.`element` AS `Element` , `up`.`create_time` AS `CreateTime` , IFNULL(`up`.`status`,@MethodConst3) AS `Status` , `up`.`is_main` AS `IsMain`  FROM `user_pet` `up` Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )   WHERE ( `up`.`user_id` = @user_id0 )ORDER BY `up`.`create_time` DESC  | 参数: [@user_id0=1, @MethodConst1=, @MethodConst2=, @MethodConst3=]
[2025-09-06 11:52:17.848 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `up`.`id` AS `Id` , `up`.`user_id` AS `UserId` , `up`.`pet_no` AS `PetNo` , IFNULL(`pc`.`name`,@MethodConst1) AS `PetName` , IFNULL(`pc`.`attribute`,@MethodConst2) AS `PetAttribute` , `up`.`image` AS `Image` , `up`.`exp` AS `Exp` , `up`.`hp` AS `Hp` , `up`.`mp` AS `Mp` , `up`.`atk` AS `Atk` , `up`.`def` AS `Def` , `up`.`spd` AS `Spd` , `up`.`state` AS `State` , `up`.`dodge` AS `Dodge` , `up`.`growth` AS `Growth` , `up`.`hit` AS `Hit` , `up`.`realm` AS `Realm` , `up`.`evolve_count` AS `EvolveCount` , `up`.`element` AS `Element` , `up`.`create_time` AS `CreateTime` , IFNULL(`up`.`status`,@MethodConst3) AS `Status` , `up`.`is_main` AS `IsMain`  FROM `user_pet` `up` Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )   WHERE ( `up`.`user_id` = @user_id0 )ORDER BY `up`.`create_time` DESC 
[2025-09-06 11:52:17.855 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=31, @MethodConst1=]
[2025-09-06 11:52:17.891 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:52:17.896 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=688500, @is_active1=1]
[2025-09-06 11:52:17.933 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:52:17.937 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=30, @MethodConst1=]
[2025-09-06 11:52:17.969 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:52:18.006 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=637000, @is_active1=1]
[2025-09-06 11:52:18.049 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:52:18.055 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=24, @MethodConst1=]
[2025-09-06 11:52:18.092 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:52:18.099 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=10, @is_active1=1]
[2025-09-06 11:52:18.131 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:52:18.145 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=23, @MethodConst1=]
[2025-09-06 11:52:18.178 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:52:18.184 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=637000, @is_active1=1]
[2025-09-06 11:52:18.223 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:52:18.229 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=22, @MethodConst1=]
[2025-09-06 11:52:18.282 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:52:18.300 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=100, @is_active1=1]
[2025-09-06 11:52:18.351 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:52:18.518 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=13, @MethodConst1=]
[2025-09-06 11:52:18.601 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:52:18.604 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=11, @MethodConst1=]
[2025-09-06 11:52:18.643 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:52:18.656 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=19, @MethodConst1=]
[2025-09-06 11:52:18.700 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:52:18.704 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=451000, @is_active1=1]
[2025-09-06 11:52:18.744 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:52:18.748 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=16, @MethodConst1=]
[2025-09-06 11:52:18.780 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:52:19.284 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=451000, @is_active1=1]
[2025-09-06 11:52:19.395 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:52:19.441 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=18, @MethodConst1=]
[2025-09-06 11:52:19.478 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:52:19.520 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=451000, @is_active1=1]
[2025-09-06 11:52:19.561 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:52:19.596 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`user_id`,`pet_no`,`image`,`exp`,`hp`,`mp`,`atk`,`def`,`spd`,`state`,`dodge`,`name`,`growth`,`hit`,`deepen`,`offset`,`vamp`,`vamp_mp`,`custom_name`,`talisman_state`,`realm`,`evolve_count`,`synthesis_count`,`nirvana_count`,`last_evolution_time`,`last_synthesis_time`,`last_nirvana_time`,`original_pet_no`,`parent_main_pet_id`,`parent_sub_pet_id`,`element`,`create_time`,`status`,`is_main`,`level`,`max_hp`,`max_mp`,`current_mp` FROM `user_pet`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=22]
[2025-09-06 11:52:19.652 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`user_id`,`pet_no`,`image`,`exp`,`hp`,`mp`,`atk`,`def`,`spd`,`state`,`dodge`,`name`,`growth`,`hit`,`deepen`,`offset`,`vamp`,`vamp_mp`,`custom_name`,`talisman_state`,`realm`,`evolve_count`,`synthesis_count`,`nirvana_count`,`last_evolution_time`,`last_synthesis_time`,`last_nirvana_time`,`original_pet_no`,`parent_main_pet_id`,`parent_sub_pet_id`,`element`,`create_time`,`status`,`is_main`,`level`,`max_hp`,`max_mp`,`current_mp` FROM `user_pet`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-09-06 11:52:19.821 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: UPDATE `user_pet`  SET
           `user_id`=@user_id,`pet_no`=@pet_no,`image`=@image,`exp`=@exp,`hp`=@hp,`mp`=@mp,`atk`=@atk,`def`=@def,`spd`=@spd,`state`=@state,`dodge`=@dodge,`name`=@name,`growth`=@growth,`hit`=@hit,`deepen`=@deepen,`offset`=@offset,`vamp`=@vamp,`vamp_mp`=@vamp_mp,`custom_name`=@custom_name,`talisman_state`=@talisman_state,`realm`=@realm,`evolve_count`=@evolve_count,`synthesis_count`=@synthesis_count,`nirvana_count`=@nirvana_count,`last_evolution_time`=@last_evolution_time,`last_synthesis_time`=@last_synthesis_time,`last_nirvana_time`=@last_nirvana_time,`original_pet_no`=@original_pet_no,`parent_main_pet_id`=@parent_main_pet_id,`parent_sub_pet_id`=@parent_sub_pet_id,`element`=@element,`create_time`=@create_time,`status`=@status,`is_main`=@is_main,`level`=@level,`max_hp`=@max_hp,`max_mp`=@max_mp,`current_mp`=@current_mp  WHERE `id`=@id | 参数: [@id=22, @user_id=1, @pet_no=79, @image=, @exp=914500, @hp=133, @mp=, @atk=13, @def=13, @spd=13, @state=, @dodge=, @name=, @growth=, @hit=, @deepen=0.00, @offset=0.00, @vamp=0.00, @vamp_mp=0.00, @custom_name=, @talisman_state=, @realm=, @evolve_count=, @synthesis_count=, @nirvana_count=, @last_evolution_time=, @last_synthesis_time=, @last_nirvana_time=, @original_pet_no=, @parent_main_pet_id=, @parent_sub_pet_id=, @element=, @create_time=2025/9/2 23:41:33, @status=牧场, @is_main=False, @level=1, @max_hp=, @max_mp=, @current_mp=]
[2025-09-06 11:52:19.979 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: UPDATE `user_pet`  SET
           `user_id`=@user_id,`pet_no`=@pet_no,`image`=@image,`exp`=@exp,`hp`=@hp,`mp`=@mp,`atk`=@atk,`def`=@def,`spd`=@spd,`state`=@state,`dodge`=@dodge,`name`=@name,`growth`=@growth,`hit`=@hit,`deepen`=@deepen,`offset`=@offset,`vamp`=@vamp,`vamp_mp`=@vamp_mp,`custom_name`=@custom_name,`talisman_state`=@talisman_state,`realm`=@realm,`evolve_count`=@evolve_count,`synthesis_count`=@synthesis_count,`nirvana_count`=@nirvana_count,`last_evolution_time`=@last_evolution_time,`last_synthesis_time`=@last_synthesis_time,`last_nirvana_time`=@last_nirvana_time,`original_pet_no`=@original_pet_no,`parent_main_pet_id`=@parent_main_pet_id,`parent_sub_pet_id`=@parent_sub_pet_id,`element`=@element,`create_time`=@create_time,`status`=@status,`is_main`=@is_main,`level`=@level,`max_hp`=@max_hp,`max_mp`=@max_mp,`current_mp`=@current_mp  WHERE `id`=@id
[2025-09-06 11:52:20.032 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-06 11:52:20.033 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-06 11:52:20.035 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-06 11:52:20.068 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-06 11:52:20.070 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-06 11:52:20.074 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )   
[2025-09-06 11:52:20.107 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )   
[2025-09-06 11:52:20.111 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )    | 参数: [@MethodConst0=, @MethodConst1=, @MethodConst2=, @MethodConst3=]
[2025-09-06 11:52:20.144 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )   
[2025-09-06 11:52:20.147 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `up`.`id` AS `Id` , `up`.`user_id` AS `UserId` , IFNULL(`u`.`username`,@MethodConst4) AS `UserName` , `up`.`pet_no` AS `PetNo` , IFNULL(`pc`.`name`,@MethodConst5) AS `PetName` , IFNULL(`pc`.`attribute`,@MethodConst6) AS `PetAttribute` , `up`.`image` AS `Image` , `up`.`exp` AS `Exp` , `up`.`hp` AS `Hp` , `up`.`mp` AS `Mp` , `up`.`atk` AS `Atk` , `up`.`def` AS `Def` , `up`.`spd` AS `Spd` , `up`.`state` AS `State` , `up`.`dodge` AS `Dodge` , `up`.`growth` AS `Growth` , `up`.`hit` AS `Hit` , `up`.`realm` AS `Realm` , `up`.`evolve_count` AS `EvolveCount` , `up`.`element` AS `Element` , `up`.`create_time` AS `CreateTime` , IFNULL(`up`.`status`,@MethodConst7) AS `Status` , `up`.`is_main` AS `IsMain`  FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )     ORDER BY `up`.`create_time` DESC LIMIT 0,10 | 参数: [@MethodConst0=, @MethodConst1=, @MethodConst2=, @MethodConst3=, @MethodConst4=, @MethodConst5=, @MethodConst6=, @MethodConst7=]
[2025-09-06 11:52:20.182 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `up`.`id` AS `Id` , `up`.`user_id` AS `UserId` , IFNULL(`u`.`username`,@MethodConst4) AS `UserName` , `up`.`pet_no` AS `PetNo` , IFNULL(`pc`.`name`,@MethodConst5) AS `PetName` , IFNULL(`pc`.`attribute`,@MethodConst6) AS `PetAttribute` , `up`.`image` AS `Image` , `up`.`exp` AS `Exp` , `up`.`hp` AS `Hp` , `up`.`mp` AS `Mp` , `up`.`atk` AS `Atk` , `up`.`def` AS `Def` , `up`.`spd` AS `Spd` , `up`.`state` AS `State` , `up`.`dodge` AS `Dodge` , `up`.`growth` AS `Growth` , `up`.`hit` AS `Hit` , `up`.`realm` AS `Realm` , `up`.`evolve_count` AS `EvolveCount` , `up`.`element` AS `Element` , `up`.`create_time` AS `CreateTime` , IFNULL(`up`.`status`,@MethodConst7) AS `Status` , `up`.`is_main` AS `IsMain`  FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )     ORDER BY `up`.`create_time` DESC LIMIT 0,10
[2025-09-06 11:52:20.186 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=31, @MethodConst1=]
[2025-09-06 11:52:20.219 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:52:20.226 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=688500, @is_active1=1]
[2025-09-06 11:52:20.259 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:52:20.262 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=30, @MethodConst1=]
[2025-09-06 11:52:20.294 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:52:20.297 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=637000, @is_active1=1]
[2025-09-06 11:52:20.330 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:52:20.335 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=24, @MethodConst1=]
[2025-09-06 11:52:20.394 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:52:20.399 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=10, @is_active1=1]
[2025-09-06 11:52:20.441 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:52:20.466 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=23, @MethodConst1=]
[2025-09-06 11:52:20.499 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:52:20.502 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=637000, @is_active1=1]
[2025-09-06 11:52:20.535 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:52:20.540 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=22, @MethodConst1=]
[2025-09-06 11:52:20.573 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:52:20.577 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=914500, @is_active1=1]
[2025-09-06 11:52:20.618 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:52:20.629 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=13, @MethodConst1=]
[2025-09-06 11:52:20.670 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:52:20.685 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=11, @MethodConst1=]
[2025-09-06 11:52:20.717 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:52:20.732 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=19, @MethodConst1=]
[2025-09-06 11:52:20.775 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:52:20.779 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=451000, @is_active1=1]
[2025-09-06 11:52:20.812 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:52:20.816 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=16, @MethodConst1=]
[2025-09-06 11:52:20.848 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:52:20.853 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=451000, @is_active1=1]
[2025-09-06 11:52:20.887 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:52:20.893 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=18, @MethodConst1=]
[2025-09-06 11:52:20.927 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:52:20.930 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=451000, @is_active1=1]
[2025-09-06 11:52:20.963 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:52:29.457 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-06 11:52:29.458 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-06 11:52:29.460 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-06 11:52:29.493 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-06 11:52:29.494 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-06 11:52:29.498 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `up`.`id` AS `Id` , `up`.`user_id` AS `UserId` , IFNULL(`u`.`username`,@MethodConst5) AS `UserName` , `up`.`pet_no` AS `PetNo` , IFNULL(`pc`.`name`,@MethodConst6) AS `PetName` , IFNULL(`pc`.`attribute`,@MethodConst7) AS `PetAttribute` , `up`.`image` AS `Image` , `up`.`exp` AS `Exp` , `up`.`hp` AS `Hp` , `up`.`mp` AS `Mp` , `up`.`atk` AS `Atk` , `up`.`def` AS `Def` , `up`.`spd` AS `Spd` , `up`.`state` AS `State` , `up`.`dodge` AS `Dodge` , `up`.`growth` AS `Growth` , `up`.`hit` AS `Hit` , `up`.`realm` AS `Realm` , `up`.`evolve_count` AS `EvolveCount` , `up`.`element` AS `Element` , `up`.`create_time` AS `CreateTime` , IFNULL(`up`.`status`,@MethodConst8) AS `Status` , `up`.`is_main` AS `IsMain`  FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )    WHERE ( `up`.`id` = @id0 )   LIMIT 0,1 | 参数: [@id0=13, @MethodConst1=, @MethodConst2=, @MethodConst3=, @MethodConst4=, @MethodConst5=, @MethodConst6=, @MethodConst7=, @MethodConst8=]
[2025-09-06 11:52:29.531 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `up`.`id` AS `Id` , `up`.`user_id` AS `UserId` , IFNULL(`u`.`username`,@MethodConst5) AS `UserName` , `up`.`pet_no` AS `PetNo` , IFNULL(`pc`.`name`,@MethodConst6) AS `PetName` , IFNULL(`pc`.`attribute`,@MethodConst7) AS `PetAttribute` , `up`.`image` AS `Image` , `up`.`exp` AS `Exp` , `up`.`hp` AS `Hp` , `up`.`mp` AS `Mp` , `up`.`atk` AS `Atk` , `up`.`def` AS `Def` , `up`.`spd` AS `Spd` , `up`.`state` AS `State` , `up`.`dodge` AS `Dodge` , `up`.`growth` AS `Growth` , `up`.`hit` AS `Hit` , `up`.`realm` AS `Realm` , `up`.`evolve_count` AS `EvolveCount` , `up`.`element` AS `Element` , `up`.`create_time` AS `CreateTime` , IFNULL(`up`.`status`,@MethodConst8) AS `Status` , `up`.`is_main` AS `IsMain`  FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )    WHERE ( `up`.`id` = @id0 )   LIMIT 0,1
[2025-09-06 11:52:29.534 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=13, @MethodConst1=]
[2025-09-06 11:52:29.567 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:52:29.573 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `up`.`id` AS `Id` , `up`.`user_id` AS `UserId` , `up`.`pet_no` AS `PetNo` , IFNULL(`pc`.`name`,@MethodConst1) AS `PetName` , IFNULL(`pc`.`attribute`,@MethodConst2) AS `PetAttribute` , `up`.`image` AS `Image` , `up`.`exp` AS `Exp` , `up`.`hp` AS `Hp` , `up`.`mp` AS `Mp` , `up`.`atk` AS `Atk` , `up`.`def` AS `Def` , `up`.`spd` AS `Spd` , `up`.`state` AS `State` , `up`.`dodge` AS `Dodge` , `up`.`growth` AS `Growth` , `up`.`hit` AS `Hit` , `up`.`realm` AS `Realm` , `up`.`evolve_count` AS `EvolveCount` , `up`.`element` AS `Element` , `up`.`create_time` AS `CreateTime` , IFNULL(`up`.`status`,@MethodConst3) AS `Status` , `up`.`is_main` AS `IsMain`  FROM `user_pet` `up` Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )   WHERE ( `up`.`user_id` = @user_id0 )ORDER BY `up`.`create_time` DESC  | 参数: [@user_id0=1, @MethodConst1=, @MethodConst2=, @MethodConst3=]
[2025-09-06 11:52:29.666 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `up`.`id` AS `Id` , `up`.`user_id` AS `UserId` , `up`.`pet_no` AS `PetNo` , IFNULL(`pc`.`name`,@MethodConst1) AS `PetName` , IFNULL(`pc`.`attribute`,@MethodConst2) AS `PetAttribute` , `up`.`image` AS `Image` , `up`.`exp` AS `Exp` , `up`.`hp` AS `Hp` , `up`.`mp` AS `Mp` , `up`.`atk` AS `Atk` , `up`.`def` AS `Def` , `up`.`spd` AS `Spd` , `up`.`state` AS `State` , `up`.`dodge` AS `Dodge` , `up`.`growth` AS `Growth` , `up`.`hit` AS `Hit` , `up`.`realm` AS `Realm` , `up`.`evolve_count` AS `EvolveCount` , `up`.`element` AS `Element` , `up`.`create_time` AS `CreateTime` , IFNULL(`up`.`status`,@MethodConst3) AS `Status` , `up`.`is_main` AS `IsMain`  FROM `user_pet` `up` Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )   WHERE ( `up`.`user_id` = @user_id0 )ORDER BY `up`.`create_time` DESC 
[2025-09-06 11:52:29.688 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=31, @MethodConst1=]
[2025-09-06 11:52:29.775 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:52:29.782 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=688500, @is_active1=1]
[2025-09-06 11:52:29.829 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:52:30.043 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=30, @MethodConst1=]
[2025-09-06 11:52:30.094 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:52:30.097 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=637000, @is_active1=1]
[2025-09-06 11:52:30.132 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:52:30.200 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=24, @MethodConst1=]
[2025-09-06 11:52:30.242 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:52:30.245 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=10, @is_active1=1]
[2025-09-06 11:52:30.279 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:52:30.293 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=23, @MethodConst1=]
[2025-09-06 11:52:30.331 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:52:30.336 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=637000, @is_active1=1]
[2025-09-06 11:52:30.369 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:52:30.378 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=22, @MethodConst1=]
[2025-09-06 11:52:30.414 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:52:30.416 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=914500, @is_active1=1]
[2025-09-06 11:52:30.452 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:52:30.457 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=13, @MethodConst1=]
[2025-09-06 11:52:30.489 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:52:30.493 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=11, @MethodConst1=]
[2025-09-06 11:52:30.528 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:52:30.531 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=19, @MethodConst1=]
[2025-09-06 11:52:30.563 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:52:30.564 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=451000, @is_active1=1]
[2025-09-06 11:52:30.596 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:52:30.603 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=16, @MethodConst1=]
[2025-09-06 11:52:30.643 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:52:30.647 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=451000, @is_active1=1]
[2025-09-06 11:52:30.687 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:52:30.689 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=18, @MethodConst1=]
[2025-09-06 11:52:30.720 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:52:30.723 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=451000, @is_active1=1]
[2025-09-06 11:52:30.756 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:52:30.758 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`user_id`,`pet_no`,`image`,`exp`,`hp`,`mp`,`atk`,`def`,`spd`,`state`,`dodge`,`name`,`growth`,`hit`,`deepen`,`offset`,`vamp`,`vamp_mp`,`custom_name`,`talisman_state`,`realm`,`evolve_count`,`synthesis_count`,`nirvana_count`,`last_evolution_time`,`last_synthesis_time`,`last_nirvana_time`,`original_pet_no`,`parent_main_pet_id`,`parent_sub_pet_id`,`element`,`create_time`,`status`,`is_main`,`level`,`max_hp`,`max_mp`,`current_mp` FROM `user_pet`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=13]
[2025-09-06 11:52:30.794 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`user_id`,`pet_no`,`image`,`exp`,`hp`,`mp`,`atk`,`def`,`spd`,`state`,`dodge`,`name`,`growth`,`hit`,`deepen`,`offset`,`vamp`,`vamp_mp`,`custom_name`,`talisman_state`,`realm`,`evolve_count`,`synthesis_count`,`nirvana_count`,`last_evolution_time`,`last_synthesis_time`,`last_nirvana_time`,`original_pet_no`,`parent_main_pet_id`,`parent_sub_pet_id`,`element`,`create_time`,`status`,`is_main`,`level`,`max_hp`,`max_mp`,`current_mp` FROM `user_pet`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-09-06 11:52:30.806 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: UPDATE `user_pet`  SET
           `user_id`=@user_id,`pet_no`=@pet_no,`image`=@image,`exp`=@exp,`hp`=@hp,`mp`=@mp,`atk`=@atk,`def`=@def,`spd`=@spd,`state`=@state,`dodge`=@dodge,`name`=@name,`growth`=@growth,`hit`=@hit,`deepen`=@deepen,`offset`=@offset,`vamp`=@vamp,`vamp_mp`=@vamp_mp,`custom_name`=@custom_name,`talisman_state`=@talisman_state,`realm`=@realm,`evolve_count`=@evolve_count,`synthesis_count`=@synthesis_count,`nirvana_count`=@nirvana_count,`last_evolution_time`=@last_evolution_time,`last_synthesis_time`=@last_synthesis_time,`last_nirvana_time`=@last_nirvana_time,`original_pet_no`=@original_pet_no,`parent_main_pet_id`=@parent_main_pet_id,`parent_sub_pet_id`=@parent_sub_pet_id,`element`=@element,`create_time`=@create_time,`status`=@status,`is_main`=@is_main,`level`=@level,`max_hp`=@max_hp,`max_mp`=@max_mp,`current_mp`=@current_mp  WHERE `id`=@id | 参数: [@id=13, @user_id=1, @pet_no=103, @image=, @exp=914500, @hp=100, @mp=, @atk=10, @def=10, @spd=10, @state=, @dodge=, @name=, @growth=, @hit=, @deepen=0.00, @offset=0.00, @vamp=0.00, @vamp_mp=0.00, @custom_name=涅槃兽, @talisman_state=, @realm=, @evolve_count=, @synthesis_count=0, @nirvana_count=0, @last_evolution_time=, @last_synthesis_time=, @last_nirvana_time=, @original_pet_no=, @parent_main_pet_id=, @parent_sub_pet_id=, @element=, @create_time=2025/8/22 21:31:19, @status=牧场, @is_main=False, @level=1, @max_hp=, @max_mp=, @current_mp=]
[2025-09-06 11:52:30.934 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: UPDATE `user_pet`  SET
           `user_id`=@user_id,`pet_no`=@pet_no,`image`=@image,`exp`=@exp,`hp`=@hp,`mp`=@mp,`atk`=@atk,`def`=@def,`spd`=@spd,`state`=@state,`dodge`=@dodge,`name`=@name,`growth`=@growth,`hit`=@hit,`deepen`=@deepen,`offset`=@offset,`vamp`=@vamp,`vamp_mp`=@vamp_mp,`custom_name`=@custom_name,`talisman_state`=@talisman_state,`realm`=@realm,`evolve_count`=@evolve_count,`synthesis_count`=@synthesis_count,`nirvana_count`=@nirvana_count,`last_evolution_time`=@last_evolution_time,`last_synthesis_time`=@last_synthesis_time,`last_nirvana_time`=@last_nirvana_time,`original_pet_no`=@original_pet_no,`parent_main_pet_id`=@parent_main_pet_id,`parent_sub_pet_id`=@parent_sub_pet_id,`element`=@element,`create_time`=@create_time,`status`=@status,`is_main`=@is_main,`level`=@level,`max_hp`=@max_hp,`max_mp`=@max_mp,`current_mp`=@current_mp  WHERE `id`=@id
[2025-09-06 11:52:30.945 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-06 11:52:30.947 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-06 11:52:30.949 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-06 11:52:30.985 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-06 11:52:30.988 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-06 11:52:30.993 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )   
[2025-09-06 11:52:31.026 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )   
[2025-09-06 11:52:31.044 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )    | 参数: [@MethodConst0=, @MethodConst1=, @MethodConst2=, @MethodConst3=]
[2025-09-06 11:52:31.080 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )   
[2025-09-06 11:52:31.083 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `up`.`id` AS `Id` , `up`.`user_id` AS `UserId` , IFNULL(`u`.`username`,@MethodConst4) AS `UserName` , `up`.`pet_no` AS `PetNo` , IFNULL(`pc`.`name`,@MethodConst5) AS `PetName` , IFNULL(`pc`.`attribute`,@MethodConst6) AS `PetAttribute` , `up`.`image` AS `Image` , `up`.`exp` AS `Exp` , `up`.`hp` AS `Hp` , `up`.`mp` AS `Mp` , `up`.`atk` AS `Atk` , `up`.`def` AS `Def` , `up`.`spd` AS `Spd` , `up`.`state` AS `State` , `up`.`dodge` AS `Dodge` , `up`.`growth` AS `Growth` , `up`.`hit` AS `Hit` , `up`.`realm` AS `Realm` , `up`.`evolve_count` AS `EvolveCount` , `up`.`element` AS `Element` , `up`.`create_time` AS `CreateTime` , IFNULL(`up`.`status`,@MethodConst7) AS `Status` , `up`.`is_main` AS `IsMain`  FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )     ORDER BY `up`.`create_time` DESC LIMIT 0,10 | 参数: [@MethodConst0=, @MethodConst1=, @MethodConst2=, @MethodConst3=, @MethodConst4=, @MethodConst5=, @MethodConst6=, @MethodConst7=]
[2025-09-06 11:52:31.119 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `up`.`id` AS `Id` , `up`.`user_id` AS `UserId` , IFNULL(`u`.`username`,@MethodConst4) AS `UserName` , `up`.`pet_no` AS `PetNo` , IFNULL(`pc`.`name`,@MethodConst5) AS `PetName` , IFNULL(`pc`.`attribute`,@MethodConst6) AS `PetAttribute` , `up`.`image` AS `Image` , `up`.`exp` AS `Exp` , `up`.`hp` AS `Hp` , `up`.`mp` AS `Mp` , `up`.`atk` AS `Atk` , `up`.`def` AS `Def` , `up`.`spd` AS `Spd` , `up`.`state` AS `State` , `up`.`dodge` AS `Dodge` , `up`.`growth` AS `Growth` , `up`.`hit` AS `Hit` , `up`.`realm` AS `Realm` , `up`.`evolve_count` AS `EvolveCount` , `up`.`element` AS `Element` , `up`.`create_time` AS `CreateTime` , IFNULL(`up`.`status`,@MethodConst7) AS `Status` , `up`.`is_main` AS `IsMain`  FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )     ORDER BY `up`.`create_time` DESC LIMIT 0,10
[2025-09-06 11:52:31.146 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=31, @MethodConst1=]
[2025-09-06 11:52:31.182 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:52:31.186 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=688500, @is_active1=1]
[2025-09-06 11:52:31.226 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:52:31.229 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=30, @MethodConst1=]
[2025-09-06 11:52:31.278 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:52:31.283 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=637000, @is_active1=1]
[2025-09-06 11:52:31.315 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:52:31.328 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=24, @MethodConst1=]
[2025-09-06 11:52:31.360 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:52:31.379 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=10, @is_active1=1]
[2025-09-06 11:52:31.450 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:52:31.455 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=23, @MethodConst1=]
[2025-09-06 11:52:31.516 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:52:31.519 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=637000, @is_active1=1]
[2025-09-06 11:52:31.555 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:52:31.577 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=22, @MethodConst1=]
[2025-09-06 11:52:31.609 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:52:31.625 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=914500, @is_active1=1]
[2025-09-06 11:52:31.661 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:52:31.665 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=13, @MethodConst1=]
[2025-09-06 11:52:31.696 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:52:31.699 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=914500, @is_active1=1]
[2025-09-06 11:52:31.731 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:52:31.734 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=11, @MethodConst1=]
[2025-09-06 11:52:31.768 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:52:31.773 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=19, @MethodConst1=]
[2025-09-06 11:52:31.805 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:52:31.809 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=451000, @is_active1=1]
[2025-09-06 11:52:31.842 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:52:31.847 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=16, @MethodConst1=]
[2025-09-06 11:52:31.886 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:52:31.890 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=451000, @is_active1=1]
[2025-09-06 11:52:31.925 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:52:31.928 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=18, @MethodConst1=]
[2025-09-06 11:52:31.963 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:52:31.976 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=451000, @is_active1=1]
[2025-09-06 11:52:32.008 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:52:41.162 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-06 11:52:41.164 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-06 11:52:41.166 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-06 11:52:41.205 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-06 11:52:41.206 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-06 11:52:41.209 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `up`.`id` AS `Id` , `up`.`user_id` AS `UserId` , IFNULL(`u`.`username`,@MethodConst5) AS `UserName` , `up`.`pet_no` AS `PetNo` , IFNULL(`pc`.`name`,@MethodConst6) AS `PetName` , IFNULL(`pc`.`attribute`,@MethodConst7) AS `PetAttribute` , `up`.`image` AS `Image` , `up`.`exp` AS `Exp` , `up`.`hp` AS `Hp` , `up`.`mp` AS `Mp` , `up`.`atk` AS `Atk` , `up`.`def` AS `Def` , `up`.`spd` AS `Spd` , `up`.`state` AS `State` , `up`.`dodge` AS `Dodge` , `up`.`growth` AS `Growth` , `up`.`hit` AS `Hit` , `up`.`realm` AS `Realm` , `up`.`evolve_count` AS `EvolveCount` , `up`.`element` AS `Element` , `up`.`create_time` AS `CreateTime` , IFNULL(`up`.`status`,@MethodConst8) AS `Status` , `up`.`is_main` AS `IsMain`  FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )    WHERE ( `up`.`id` = @id0 )   LIMIT 0,1 | 参数: [@id0=11, @MethodConst1=, @MethodConst2=, @MethodConst3=, @MethodConst4=, @MethodConst5=, @MethodConst6=, @MethodConst7=, @MethodConst8=]
[2025-09-06 11:52:41.252 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `up`.`id` AS `Id` , `up`.`user_id` AS `UserId` , IFNULL(`u`.`username`,@MethodConst5) AS `UserName` , `up`.`pet_no` AS `PetNo` , IFNULL(`pc`.`name`,@MethodConst6) AS `PetName` , IFNULL(`pc`.`attribute`,@MethodConst7) AS `PetAttribute` , `up`.`image` AS `Image` , `up`.`exp` AS `Exp` , `up`.`hp` AS `Hp` , `up`.`mp` AS `Mp` , `up`.`atk` AS `Atk` , `up`.`def` AS `Def` , `up`.`spd` AS `Spd` , `up`.`state` AS `State` , `up`.`dodge` AS `Dodge` , `up`.`growth` AS `Growth` , `up`.`hit` AS `Hit` , `up`.`realm` AS `Realm` , `up`.`evolve_count` AS `EvolveCount` , `up`.`element` AS `Element` , `up`.`create_time` AS `CreateTime` , IFNULL(`up`.`status`,@MethodConst8) AS `Status` , `up`.`is_main` AS `IsMain`  FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )    WHERE ( `up`.`id` = @id0 )   LIMIT 0,1
[2025-09-06 11:52:41.261 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=11, @MethodConst1=]
[2025-09-06 11:52:41.307 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:52:41.312 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `up`.`id` AS `Id` , `up`.`user_id` AS `UserId` , `up`.`pet_no` AS `PetNo` , IFNULL(`pc`.`name`,@MethodConst1) AS `PetName` , IFNULL(`pc`.`attribute`,@MethodConst2) AS `PetAttribute` , `up`.`image` AS `Image` , `up`.`exp` AS `Exp` , `up`.`hp` AS `Hp` , `up`.`mp` AS `Mp` , `up`.`atk` AS `Atk` , `up`.`def` AS `Def` , `up`.`spd` AS `Spd` , `up`.`state` AS `State` , `up`.`dodge` AS `Dodge` , `up`.`growth` AS `Growth` , `up`.`hit` AS `Hit` , `up`.`realm` AS `Realm` , `up`.`evolve_count` AS `EvolveCount` , `up`.`element` AS `Element` , `up`.`create_time` AS `CreateTime` , IFNULL(`up`.`status`,@MethodConst3) AS `Status` , `up`.`is_main` AS `IsMain`  FROM `user_pet` `up` Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )   WHERE ( `up`.`user_id` = @user_id0 )ORDER BY `up`.`create_time` DESC  | 参数: [@user_id0=1, @MethodConst1=, @MethodConst2=, @MethodConst3=]
[2025-09-06 11:52:41.347 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `up`.`id` AS `Id` , `up`.`user_id` AS `UserId` , `up`.`pet_no` AS `PetNo` , IFNULL(`pc`.`name`,@MethodConst1) AS `PetName` , IFNULL(`pc`.`attribute`,@MethodConst2) AS `PetAttribute` , `up`.`image` AS `Image` , `up`.`exp` AS `Exp` , `up`.`hp` AS `Hp` , `up`.`mp` AS `Mp` , `up`.`atk` AS `Atk` , `up`.`def` AS `Def` , `up`.`spd` AS `Spd` , `up`.`state` AS `State` , `up`.`dodge` AS `Dodge` , `up`.`growth` AS `Growth` , `up`.`hit` AS `Hit` , `up`.`realm` AS `Realm` , `up`.`evolve_count` AS `EvolveCount` , `up`.`element` AS `Element` , `up`.`create_time` AS `CreateTime` , IFNULL(`up`.`status`,@MethodConst3) AS `Status` , `up`.`is_main` AS `IsMain`  FROM `user_pet` `up` Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )   WHERE ( `up`.`user_id` = @user_id0 )ORDER BY `up`.`create_time` DESC 
[2025-09-06 11:52:41.349 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=31, @MethodConst1=]
[2025-09-06 11:52:41.386 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:52:41.393 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=688500, @is_active1=1]
[2025-09-06 11:52:41.433 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:52:41.438 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=30, @MethodConst1=]
[2025-09-06 11:52:41.472 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:52:41.482 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=637000, @is_active1=1]
[2025-09-06 11:52:41.519 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:52:41.522 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=24, @MethodConst1=]
[2025-09-06 11:52:41.575 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:52:41.578 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=10, @is_active1=1]
[2025-09-06 11:52:41.610 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:52:41.639 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=23, @MethodConst1=]
[2025-09-06 11:52:41.711 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:52:42.083 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=637000, @is_active1=1]
[2025-09-06 11:52:42.146 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:52:42.164 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=22, @MethodConst1=]
[2025-09-06 11:52:42.239 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:52:42.249 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=914500, @is_active1=1]
[2025-09-06 11:52:42.288 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:52:42.291 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=13, @MethodConst1=]
[2025-09-06 11:52:42.325 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:52:42.328 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=914500, @is_active1=1]
[2025-09-06 11:52:42.361 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:52:42.364 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=11, @MethodConst1=]
[2025-09-06 11:52:42.398 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:52:42.403 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=19, @MethodConst1=]
[2025-09-06 11:52:42.436 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:52:42.443 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=451000, @is_active1=1]
[2025-09-06 11:52:42.483 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:52:42.494 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=16, @MethodConst1=]
[2025-09-06 11:52:42.528 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:52:42.531 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=451000, @is_active1=1]
[2025-09-06 11:52:42.564 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:52:42.567 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=18, @MethodConst1=]
[2025-09-06 11:52:42.611 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:52:42.615 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=451000, @is_active1=1]
[2025-09-06 11:52:42.646 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:52:42.648 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`user_id`,`pet_no`,`image`,`exp`,`hp`,`mp`,`atk`,`def`,`spd`,`state`,`dodge`,`name`,`growth`,`hit`,`deepen`,`offset`,`vamp`,`vamp_mp`,`custom_name`,`talisman_state`,`realm`,`evolve_count`,`synthesis_count`,`nirvana_count`,`last_evolution_time`,`last_synthesis_time`,`last_nirvana_time`,`original_pet_no`,`parent_main_pet_id`,`parent_sub_pet_id`,`element`,`create_time`,`status`,`is_main`,`level`,`max_hp`,`max_mp`,`current_mp` FROM `user_pet`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=11]
[2025-09-06 11:52:42.680 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`user_id`,`pet_no`,`image`,`exp`,`hp`,`mp`,`atk`,`def`,`spd`,`state`,`dodge`,`name`,`growth`,`hit`,`deepen`,`offset`,`vamp`,`vamp_mp`,`custom_name`,`talisman_state`,`realm`,`evolve_count`,`synthesis_count`,`nirvana_count`,`last_evolution_time`,`last_synthesis_time`,`last_nirvana_time`,`original_pet_no`,`parent_main_pet_id`,`parent_sub_pet_id`,`element`,`create_time`,`status`,`is_main`,`level`,`max_hp`,`max_mp`,`current_mp` FROM `user_pet`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-09-06 11:52:42.682 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: UPDATE `user_pet`  SET
           `user_id`=@user_id,`pet_no`=@pet_no,`image`=@image,`exp`=@exp,`hp`=@hp,`mp`=@mp,`atk`=@atk,`def`=@def,`spd`=@spd,`state`=@state,`dodge`=@dodge,`name`=@name,`growth`=@growth,`hit`=@hit,`deepen`=@deepen,`offset`=@offset,`vamp`=@vamp,`vamp_mp`=@vamp_mp,`custom_name`=@custom_name,`talisman_state`=@talisman_state,`realm`=@realm,`evolve_count`=@evolve_count,`synthesis_count`=@synthesis_count,`nirvana_count`=@nirvana_count,`last_evolution_time`=@last_evolution_time,`last_synthesis_time`=@last_synthesis_time,`last_nirvana_time`=@last_nirvana_time,`original_pet_no`=@original_pet_no,`parent_main_pet_id`=@parent_main_pet_id,`parent_sub_pet_id`=@parent_sub_pet_id,`element`=@element,`create_time`=@create_time,`status`=@status,`is_main`=@is_main,`level`=@level,`max_hp`=@max_hp,`max_mp`=@max_mp,`current_mp`=@current_mp  WHERE `id`=@id | 参数: [@id=11, @user_id=1, @pet_no=103, @image=, @exp=914500, @hp=100, @mp=, @atk=10, @def=10, @spd=10, @state=, @dodge=, @name=, @growth=, @hit=, @deepen=0.00, @offset=0.00, @vamp=0.00, @vamp_mp=0.00, @custom_name=涅槃兽, @talisman_state=, @realm=, @evolve_count=, @synthesis_count=0, @nirvana_count=0, @last_evolution_time=, @last_synthesis_time=, @last_nirvana_time=, @original_pet_no=, @parent_main_pet_id=, @parent_sub_pet_id=, @element=, @create_time=2025/8/22 21:31:12, @status=牧场, @is_main=False, @level=1, @max_hp=, @max_mp=, @current_mp=]
[2025-09-06 11:52:42.755 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: UPDATE `user_pet`  SET
           `user_id`=@user_id,`pet_no`=@pet_no,`image`=@image,`exp`=@exp,`hp`=@hp,`mp`=@mp,`atk`=@atk,`def`=@def,`spd`=@spd,`state`=@state,`dodge`=@dodge,`name`=@name,`growth`=@growth,`hit`=@hit,`deepen`=@deepen,`offset`=@offset,`vamp`=@vamp,`vamp_mp`=@vamp_mp,`custom_name`=@custom_name,`talisman_state`=@talisman_state,`realm`=@realm,`evolve_count`=@evolve_count,`synthesis_count`=@synthesis_count,`nirvana_count`=@nirvana_count,`last_evolution_time`=@last_evolution_time,`last_synthesis_time`=@last_synthesis_time,`last_nirvana_time`=@last_nirvana_time,`original_pet_no`=@original_pet_no,`parent_main_pet_id`=@parent_main_pet_id,`parent_sub_pet_id`=@parent_sub_pet_id,`element`=@element,`create_time`=@create_time,`status`=@status,`is_main`=@is_main,`level`=@level,`max_hp`=@max_hp,`max_mp`=@max_mp,`current_mp`=@current_mp  WHERE `id`=@id
[2025-09-06 11:52:42.762 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-06 11:52:42.763 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-06 11:52:42.764 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-06 11:52:42.798 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-06 11:52:42.803 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-06 11:52:42.807 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )   
[2025-09-06 11:52:42.848 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )   
[2025-09-06 11:52:42.851 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )    | 参数: [@MethodConst0=, @MethodConst1=, @MethodConst2=, @MethodConst3=]
[2025-09-06 11:52:42.886 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )   
[2025-09-06 11:52:42.891 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `up`.`id` AS `Id` , `up`.`user_id` AS `UserId` , IFNULL(`u`.`username`,@MethodConst4) AS `UserName` , `up`.`pet_no` AS `PetNo` , IFNULL(`pc`.`name`,@MethodConst5) AS `PetName` , IFNULL(`pc`.`attribute`,@MethodConst6) AS `PetAttribute` , `up`.`image` AS `Image` , `up`.`exp` AS `Exp` , `up`.`hp` AS `Hp` , `up`.`mp` AS `Mp` , `up`.`atk` AS `Atk` , `up`.`def` AS `Def` , `up`.`spd` AS `Spd` , `up`.`state` AS `State` , `up`.`dodge` AS `Dodge` , `up`.`growth` AS `Growth` , `up`.`hit` AS `Hit` , `up`.`realm` AS `Realm` , `up`.`evolve_count` AS `EvolveCount` , `up`.`element` AS `Element` , `up`.`create_time` AS `CreateTime` , IFNULL(`up`.`status`,@MethodConst7) AS `Status` , `up`.`is_main` AS `IsMain`  FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )     ORDER BY `up`.`create_time` DESC LIMIT 0,10 | 参数: [@MethodConst0=, @MethodConst1=, @MethodConst2=, @MethodConst3=, @MethodConst4=, @MethodConst5=, @MethodConst6=, @MethodConst7=]
[2025-09-06 11:52:42.926 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `up`.`id` AS `Id` , `up`.`user_id` AS `UserId` , IFNULL(`u`.`username`,@MethodConst4) AS `UserName` , `up`.`pet_no` AS `PetNo` , IFNULL(`pc`.`name`,@MethodConst5) AS `PetName` , IFNULL(`pc`.`attribute`,@MethodConst6) AS `PetAttribute` , `up`.`image` AS `Image` , `up`.`exp` AS `Exp` , `up`.`hp` AS `Hp` , `up`.`mp` AS `Mp` , `up`.`atk` AS `Atk` , `up`.`def` AS `Def` , `up`.`spd` AS `Spd` , `up`.`state` AS `State` , `up`.`dodge` AS `Dodge` , `up`.`growth` AS `Growth` , `up`.`hit` AS `Hit` , `up`.`realm` AS `Realm` , `up`.`evolve_count` AS `EvolveCount` , `up`.`element` AS `Element` , `up`.`create_time` AS `CreateTime` , IFNULL(`up`.`status`,@MethodConst7) AS `Status` , `up`.`is_main` AS `IsMain`  FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )     ORDER BY `up`.`create_time` DESC LIMIT 0,10
[2025-09-06 11:52:42.929 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=31, @MethodConst1=]
[2025-09-06 11:52:42.959 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:52:42.961 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=688500, @is_active1=1]
[2025-09-06 11:52:42.999 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:52:43.001 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=30, @MethodConst1=]
[2025-09-06 11:52:43.036 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:52:43.043 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=637000, @is_active1=1]
[2025-09-06 11:52:43.082 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:52:43.089 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=24, @MethodConst1=]
[2025-09-06 11:52:43.122 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:52:43.125 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=10, @is_active1=1]
[2025-09-06 11:52:43.158 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:52:43.161 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=23, @MethodConst1=]
[2025-09-06 11:52:43.204 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:52:43.209 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=637000, @is_active1=1]
[2025-09-06 11:52:43.243 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:52:43.256 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=22, @MethodConst1=]
[2025-09-06 11:52:43.288 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:52:43.291 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=914500, @is_active1=1]
[2025-09-06 11:52:43.325 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:52:43.329 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=13, @MethodConst1=]
[2025-09-06 11:52:43.365 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:52:43.371 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=914500, @is_active1=1]
[2025-09-06 11:52:43.406 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:52:43.410 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=11, @MethodConst1=]
[2025-09-06 11:52:43.444 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:52:43.463 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=914500, @is_active1=1]
[2025-09-06 11:52:43.589 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:52:43.595 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=19, @MethodConst1=]
[2025-09-06 11:52:43.634 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:52:43.639 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=451000, @is_active1=1]
[2025-09-06 11:52:43.675 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:52:43.678 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=16, @MethodConst1=]
[2025-09-06 11:52:43.710 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:52:43.712 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=451000, @is_active1=1]
[2025-09-06 11:52:43.746 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:52:43.751 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=18, @MethodConst1=]
[2025-09-06 11:52:43.788 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:52:43.797 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=451000, @is_active1=1]
[2025-09-06 11:52:43.829 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:57:43.867 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-06 11:57:43.870 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-06 11:57:43.872 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-06 11:57:44.184 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-06 11:57:44.304 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-06 11:57:44.330 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`user_id`,`pet_no`,`image`,`exp`,`hp`,`mp`,`atk`,`def`,`spd`,`state`,`dodge`,`name`,`growth`,`hit`,`deepen`,`offset`,`vamp`,`vamp_mp`,`custom_name`,`talisman_state`,`realm`,`evolve_count`,`synthesis_count`,`nirvana_count`,`last_evolution_time`,`last_synthesis_time`,`last_nirvana_time`,`original_pet_no`,`parent_main_pet_id`,`parent_sub_pet_id`,`element`,`create_time`,`status`,`is_main`,`level`,`max_hp`,`max_mp`,`current_mp` FROM `user_pet`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=22]
[2025-09-06 11:57:44.408 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`user_id`,`pet_no`,`image`,`exp`,`hp`,`mp`,`atk`,`def`,`spd`,`state`,`dodge`,`name`,`growth`,`hit`,`deepen`,`offset`,`vamp`,`vamp_mp`,`custom_name`,`talisman_state`,`realm`,`evolve_count`,`synthesis_count`,`nirvana_count`,`last_evolution_time`,`last_synthesis_time`,`last_nirvana_time`,`original_pet_no`,`parent_main_pet_id`,`parent_sub_pet_id`,`element`,`create_time`,`status`,`is_main`,`level`,`max_hp`,`max_mp`,`current_mp` FROM `user_pet`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-09-06 11:57:44.412 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `up`.`id` AS `Id` , `up`.`user_id` AS `UserId` , `up`.`pet_no` AS `PetNo` , IFNULL(`pc`.`name`,@MethodConst1) AS `PetName` , IFNULL(`pc`.`attribute`,@MethodConst2) AS `PetAttribute` , `up`.`image` AS `Image` , `up`.`exp` AS `Exp` , `up`.`hp` AS `Hp` , `up`.`mp` AS `Mp` , `up`.`atk` AS `Atk` , `up`.`def` AS `Def` , `up`.`spd` AS `Spd` , `up`.`state` AS `State` , `up`.`dodge` AS `Dodge` , `up`.`growth` AS `Growth` , `up`.`hit` AS `Hit` , `up`.`realm` AS `Realm` , `up`.`evolve_count` AS `EvolveCount` , `up`.`element` AS `Element` , `up`.`create_time` AS `CreateTime` , IFNULL(`up`.`status`,@MethodConst3) AS `Status` , `up`.`is_main` AS `IsMain`  FROM `user_pet` `up` Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )   WHERE ( `up`.`user_id` = @user_id0 )ORDER BY `up`.`create_time` DESC  | 参数: [@user_id0=1, @MethodConst1=, @MethodConst2=, @MethodConst3=]
[2025-09-06 11:57:44.449 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `up`.`id` AS `Id` , `up`.`user_id` AS `UserId` , `up`.`pet_no` AS `PetNo` , IFNULL(`pc`.`name`,@MethodConst1) AS `PetName` , IFNULL(`pc`.`attribute`,@MethodConst2) AS `PetAttribute` , `up`.`image` AS `Image` , `up`.`exp` AS `Exp` , `up`.`hp` AS `Hp` , `up`.`mp` AS `Mp` , `up`.`atk` AS `Atk` , `up`.`def` AS `Def` , `up`.`spd` AS `Spd` , `up`.`state` AS `State` , `up`.`dodge` AS `Dodge` , `up`.`growth` AS `Growth` , `up`.`hit` AS `Hit` , `up`.`realm` AS `Realm` , `up`.`evolve_count` AS `EvolveCount` , `up`.`element` AS `Element` , `up`.`create_time` AS `CreateTime` , IFNULL(`up`.`status`,@MethodConst3) AS `Status` , `up`.`is_main` AS `IsMain`  FROM `user_pet` `up` Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )   WHERE ( `up`.`user_id` = @user_id0 )ORDER BY `up`.`create_time` DESC 
[2025-09-06 11:57:44.453 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=31, @MethodConst1=]
[2025-09-06 11:57:44.496 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:57:44.504 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=688500, @is_active1=1]
[2025-09-06 11:57:44.539 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:57:44.541 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=30, @MethodConst1=]
[2025-09-06 11:57:44.580 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:57:44.588 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=637000, @is_active1=1]
[2025-09-06 11:57:44.626 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:57:44.642 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=24, @MethodConst1=]
[2025-09-06 11:57:44.677 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:57:44.680 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=10, @is_active1=1]
[2025-09-06 11:57:44.720 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:57:44.723 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=23, @MethodConst1=]
[2025-09-06 11:57:44.759 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:57:44.767 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=637000, @is_active1=1]
[2025-09-06 11:57:44.803 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:57:44.807 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=22, @MethodConst1=]
[2025-09-06 11:57:44.847 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:57:44.855 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=914500, @is_active1=1]
[2025-09-06 11:57:44.893 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:57:44.922 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=13, @MethodConst1=]
[2025-09-06 11:57:44.958 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:57:44.986 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=914500, @is_active1=1]
[2025-09-06 11:57:45.019 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:57:45.022 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=11, @MethodConst1=]
[2025-09-06 11:57:45.056 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:57:45.061 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=914500, @is_active1=1]
[2025-09-06 11:57:45.094 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:57:45.097 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=19, @MethodConst1=]
[2025-09-06 11:57:45.130 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:57:45.133 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=451000, @is_active1=1]
[2025-09-06 11:57:45.167 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:57:45.171 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=16, @MethodConst1=]
[2025-09-06 11:57:45.219 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:57:45.292 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=451000, @is_active1=1]
[2025-09-06 11:57:45.415 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:57:45.709 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=18, @MethodConst1=]
[2025-09-06 11:57:45.772 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:57:45.775 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=451000, @is_active1=1]
[2025-09-06 11:57:45.811 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:57:45.907 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: INSERT INTO `user_pet`  
           (`user_id`,`pet_no`,`image`,`exp`,`hp`,`mp`,`atk`,`def`,`spd`,`state`,`dodge`,`name`,`growth`,`hit`,`deepen`,`offset`,`vamp`,`vamp_mp`,`custom_name`,`talisman_state`,`realm`,`evolve_count`,`synthesis_count`,`nirvana_count`,`last_evolution_time`,`last_synthesis_time`,`last_nirvana_time`,`original_pet_no`,`parent_main_pet_id`,`parent_sub_pet_id`,`element`,`create_time`,`status`,`is_main`,`level`,`max_hp`,`max_mp`,`current_mp`)
     VALUES
           (@user_id,@pet_no,@image,@exp,@hp,@mp,@atk,@def,@spd,@state,@dodge,@name,@growth,@hit,@deepen,@offset,@vamp,@vamp_mp,@custom_name,@talisman_state,@realm,@evolve_count,@synthesis_count,@nirvana_count,@last_evolution_time,@last_synthesis_time,@last_nirvana_time,@original_pet_no,@parent_main_pet_id,@parent_sub_pet_id,@element,@create_time,@status,@is_main,@level,@max_hp,@max_mp,@current_mp) ;SELECT LAST_INSERT_ID(); | 参数: [@user_id=1, @pet_no=79, @image=, @exp=914500, @hp=133, @mp=, @atk=13, @def=13, @spd=13, @state=, @dodge=, @name=, @growth=, @hit=, @deepen=0.00, @offset=0.00, @vamp=0.00, @vamp_mp=0.00, @custom_name=, @talisman_state=, @realm=元神初具, @evolve_count=, @synthesis_count=, @nirvana_count=, @last_evolution_time=, @last_synthesis_time=, @last_nirvana_time=, @original_pet_no=, @parent_main_pet_id=, @parent_sub_pet_id=, @element=, @create_time=2025/9/6 11:57:45, @status=牧场, @is_main=False, @level=0, @max_hp=, @max_mp=, @current_mp=]
[2025-09-06 11:57:46.041 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: INSERT INTO `user_pet`  
           (`user_id`,`pet_no`,`image`,`exp`,`hp`,`mp`,`atk`,`def`,`spd`,`state`,`dodge`,`name`,`growth`,`hit`,`deepen`,`offset`,`vamp`,`vamp_mp`,`custom_name`,`talisman_state`,`realm`,`evolve_count`,`synthesis_count`,`nirvana_count`,`last_evolution_time`,`last_synthesis_time`,`last_nirvana_time`,`original_pet_no`,`parent_main_pet_id`,`parent_sub_pet_id`,`element`,`create_time`,`status`,`is_main`,`level`,`max_hp`,`max_mp`,`current_mp`)
     VALUES
           (@user_id,@pet_no,@image,@exp,@hp,@mp,@atk,@def,@spd,@state,@dodge,@name,@growth,@hit,@deepen,@offset,@vamp,@vamp_mp,@custom_name,@talisman_state,@realm,@evolve_count,@synthesis_count,@nirvana_count,@last_evolution_time,@last_synthesis_time,@last_nirvana_time,@original_pet_no,@parent_main_pet_id,@parent_sub_pet_id,@element,@create_time,@status,@is_main,@level,@max_hp,@max_mp,@current_mp) ;SELECT LAST_INSERT_ID();
[2025-09-06 11:57:46.047 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`user_pet_id`,`skill_id`,`skill_level`,`create_time` FROM `user_pet_skill`  WHERE ( `user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=22]
[2025-09-06 11:57:46.081 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`user_pet_id`,`skill_id`,`skill_level`,`create_time` FROM `user_pet_skill`  WHERE ( `user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:57:46.091 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-06 11:57:46.092 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-06 11:57:46.095 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-06 11:57:46.129 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-06 11:57:46.132 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-06 11:57:46.135 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )   
[2025-09-06 11:57:46.173 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )   
[2025-09-06 11:57:46.174 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )    | 参数: [@MethodConst0=, @MethodConst1=, @MethodConst2=, @MethodConst3=]
[2025-09-06 11:57:46.209 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )   
[2025-09-06 11:57:46.210 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `up`.`id` AS `Id` , `up`.`user_id` AS `UserId` , IFNULL(`u`.`username`,@MethodConst4) AS `UserName` , `up`.`pet_no` AS `PetNo` , IFNULL(`pc`.`name`,@MethodConst5) AS `PetName` , IFNULL(`pc`.`attribute`,@MethodConst6) AS `PetAttribute` , `up`.`image` AS `Image` , `up`.`exp` AS `Exp` , `up`.`hp` AS `Hp` , `up`.`mp` AS `Mp` , `up`.`atk` AS `Atk` , `up`.`def` AS `Def` , `up`.`spd` AS `Spd` , `up`.`state` AS `State` , `up`.`dodge` AS `Dodge` , `up`.`growth` AS `Growth` , `up`.`hit` AS `Hit` , `up`.`realm` AS `Realm` , `up`.`evolve_count` AS `EvolveCount` , `up`.`element` AS `Element` , `up`.`create_time` AS `CreateTime` , IFNULL(`up`.`status`,@MethodConst7) AS `Status` , `up`.`is_main` AS `IsMain`  FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )     ORDER BY `up`.`create_time` DESC LIMIT 0,10 | 参数: [@MethodConst0=, @MethodConst1=, @MethodConst2=, @MethodConst3=, @MethodConst4=, @MethodConst5=, @MethodConst6=, @MethodConst7=]
[2025-09-06 11:57:46.259 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `up`.`id` AS `Id` , `up`.`user_id` AS `UserId` , IFNULL(`u`.`username`,@MethodConst4) AS `UserName` , `up`.`pet_no` AS `PetNo` , IFNULL(`pc`.`name`,@MethodConst5) AS `PetName` , IFNULL(`pc`.`attribute`,@MethodConst6) AS `PetAttribute` , `up`.`image` AS `Image` , `up`.`exp` AS `Exp` , `up`.`hp` AS `Hp` , `up`.`mp` AS `Mp` , `up`.`atk` AS `Atk` , `up`.`def` AS `Def` , `up`.`spd` AS `Spd` , `up`.`state` AS `State` , `up`.`dodge` AS `Dodge` , `up`.`growth` AS `Growth` , `up`.`hit` AS `Hit` , `up`.`realm` AS `Realm` , `up`.`evolve_count` AS `EvolveCount` , `up`.`element` AS `Element` , `up`.`create_time` AS `CreateTime` , IFNULL(`up`.`status`,@MethodConst7) AS `Status` , `up`.`is_main` AS `IsMain`  FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )     ORDER BY `up`.`create_time` DESC LIMIT 0,10
[2025-09-06 11:57:46.262 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=32, @MethodConst1=]
[2025-09-06 11:57:46.296 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:57:46.307 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=914500, @is_active1=1]
[2025-09-06 11:57:46.344 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:57:46.348 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=31, @MethodConst1=]
[2025-09-06 11:57:46.380 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:57:46.385 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=688500, @is_active1=1]
[2025-09-06 11:57:46.422 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:57:46.424 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=30, @MethodConst1=]
[2025-09-06 11:57:46.457 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:57:46.468 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=637000, @is_active1=1]
[2025-09-06 11:57:46.501 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:57:46.503 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=24, @MethodConst1=]
[2025-09-06 11:57:46.536 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:57:46.538 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=10, @is_active1=1]
[2025-09-06 11:57:46.576 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:57:46.577 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=23, @MethodConst1=]
[2025-09-06 11:57:46.609 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:57:46.611 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=637000, @is_active1=1]
[2025-09-06 11:57:46.646 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:57:46.650 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=22, @MethodConst1=]
[2025-09-06 11:57:46.689 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:57:46.691 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=914500, @is_active1=1]
[2025-09-06 11:57:46.753 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:57:46.756 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=13, @MethodConst1=]
[2025-09-06 11:57:46.790 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:57:46.791 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=914500, @is_active1=1]
[2025-09-06 11:57:46.824 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:57:46.825 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=11, @MethodConst1=]
[2025-09-06 11:57:46.857 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:57:46.858 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=914500, @is_active1=1]
[2025-09-06 11:57:46.891 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:57:46.894 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=19, @MethodConst1=]
[2025-09-06 11:57:46.928 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:57:46.929 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=451000, @is_active1=1]
[2025-09-06 11:57:46.970 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:57:46.973 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=16, @MethodConst1=]
[2025-09-06 11:57:47.006 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:57:47.014 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=451000, @is_active1=1]
[2025-09-06 11:57:47.051 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:57:51.906 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-06 11:57:51.924 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-06 11:57:51.933 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-06 11:57:51.972 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-06 11:57:51.974 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-06 11:57:51.976 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`user_id`,`pet_no`,`image`,`exp`,`hp`,`mp`,`atk`,`def`,`spd`,`state`,`dodge`,`name`,`growth`,`hit`,`deepen`,`offset`,`vamp`,`vamp_mp`,`custom_name`,`talisman_state`,`realm`,`evolve_count`,`synthesis_count`,`nirvana_count`,`last_evolution_time`,`last_synthesis_time`,`last_nirvana_time`,`original_pet_no`,`parent_main_pet_id`,`parent_sub_pet_id`,`element`,`create_time`,`status`,`is_main`,`level`,`max_hp`,`max_mp`,`current_mp` FROM `user_pet`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=32]
[2025-09-06 11:57:52.013 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`user_id`,`pet_no`,`image`,`exp`,`hp`,`mp`,`atk`,`def`,`spd`,`state`,`dodge`,`name`,`growth`,`hit`,`deepen`,`offset`,`vamp`,`vamp_mp`,`custom_name`,`talisman_state`,`realm`,`evolve_count`,`synthesis_count`,`nirvana_count`,`last_evolution_time`,`last_synthesis_time`,`last_nirvana_time`,`original_pet_no`,`parent_main_pet_id`,`parent_sub_pet_id`,`element`,`create_time`,`status`,`is_main`,`level`,`max_hp`,`max_mp`,`current_mp` FROM `user_pet`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-09-06 11:57:52.016 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `up`.`id` AS `Id` , `up`.`user_id` AS `UserId` , `up`.`pet_no` AS `PetNo` , IFNULL(`pc`.`name`,@MethodConst1) AS `PetName` , IFNULL(`pc`.`attribute`,@MethodConst2) AS `PetAttribute` , `up`.`image` AS `Image` , `up`.`exp` AS `Exp` , `up`.`hp` AS `Hp` , `up`.`mp` AS `Mp` , `up`.`atk` AS `Atk` , `up`.`def` AS `Def` , `up`.`spd` AS `Spd` , `up`.`state` AS `State` , `up`.`dodge` AS `Dodge` , `up`.`growth` AS `Growth` , `up`.`hit` AS `Hit` , `up`.`realm` AS `Realm` , `up`.`evolve_count` AS `EvolveCount` , `up`.`element` AS `Element` , `up`.`create_time` AS `CreateTime` , IFNULL(`up`.`status`,@MethodConst3) AS `Status` , `up`.`is_main` AS `IsMain`  FROM `user_pet` `up` Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )   WHERE ( `up`.`user_id` = @user_id0 )ORDER BY `up`.`create_time` DESC  | 参数: [@user_id0=1, @MethodConst1=, @MethodConst2=, @MethodConst3=]
[2025-09-06 11:57:52.054 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `up`.`id` AS `Id` , `up`.`user_id` AS `UserId` , `up`.`pet_no` AS `PetNo` , IFNULL(`pc`.`name`,@MethodConst1) AS `PetName` , IFNULL(`pc`.`attribute`,@MethodConst2) AS `PetAttribute` , `up`.`image` AS `Image` , `up`.`exp` AS `Exp` , `up`.`hp` AS `Hp` , `up`.`mp` AS `Mp` , `up`.`atk` AS `Atk` , `up`.`def` AS `Def` , `up`.`spd` AS `Spd` , `up`.`state` AS `State` , `up`.`dodge` AS `Dodge` , `up`.`growth` AS `Growth` , `up`.`hit` AS `Hit` , `up`.`realm` AS `Realm` , `up`.`evolve_count` AS `EvolveCount` , `up`.`element` AS `Element` , `up`.`create_time` AS `CreateTime` , IFNULL(`up`.`status`,@MethodConst3) AS `Status` , `up`.`is_main` AS `IsMain`  FROM `user_pet` `up` Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )   WHERE ( `up`.`user_id` = @user_id0 )ORDER BY `up`.`create_time` DESC 
[2025-09-06 11:57:52.058 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=32, @MethodConst1=]
[2025-09-06 11:57:52.091 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:57:52.093 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=914500, @is_active1=1]
[2025-09-06 11:57:52.127 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:57:52.130 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=31, @MethodConst1=]
[2025-09-06 11:57:52.185 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:57:52.188 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=688500, @is_active1=1]
[2025-09-06 11:57:52.222 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:57:52.225 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=30, @MethodConst1=]
[2025-09-06 11:57:52.257 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:57:52.258 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=637000, @is_active1=1]
[2025-09-06 11:57:52.292 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:57:52.293 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=24, @MethodConst1=]
[2025-09-06 11:57:52.325 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:57:52.326 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=10, @is_active1=1]
[2025-09-06 11:57:52.359 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:57:52.364 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=23, @MethodConst1=]
[2025-09-06 11:57:52.400 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:57:52.402 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=637000, @is_active1=1]
[2025-09-06 11:57:52.434 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:57:52.436 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=22, @MethodConst1=]
[2025-09-06 11:57:52.469 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:57:52.470 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=914500, @is_active1=1]
[2025-09-06 11:57:52.512 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:57:52.514 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=13, @MethodConst1=]
[2025-09-06 11:57:52.546 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:57:52.547 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=914500, @is_active1=1]
[2025-09-06 11:57:52.579 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:57:52.581 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=11, @MethodConst1=]
[2025-09-06 11:57:52.615 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:57:52.619 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=914500, @is_active1=1]
[2025-09-06 11:57:52.651 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:57:52.652 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=19, @MethodConst1=]
[2025-09-06 11:57:52.685 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:57:52.686 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=451000, @is_active1=1]
[2025-09-06 11:57:52.730 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:57:52.733 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=16, @MethodConst1=]
[2025-09-06 11:57:52.766 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:57:52.769 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=451000, @is_active1=1]
[2025-09-06 11:57:52.806 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:57:52.809 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=18, @MethodConst1=]
[2025-09-06 11:57:52.844 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:57:52.848 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=451000, @is_active1=1]
[2025-09-06 11:57:52.884 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:57:52.887 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: INSERT INTO `user_pet`  
           (`user_id`,`pet_no`,`image`,`exp`,`hp`,`mp`,`atk`,`def`,`spd`,`state`,`dodge`,`name`,`growth`,`hit`,`deepen`,`offset`,`vamp`,`vamp_mp`,`custom_name`,`talisman_state`,`realm`,`evolve_count`,`synthesis_count`,`nirvana_count`,`last_evolution_time`,`last_synthesis_time`,`last_nirvana_time`,`original_pet_no`,`parent_main_pet_id`,`parent_sub_pet_id`,`element`,`create_time`,`status`,`is_main`,`level`,`max_hp`,`max_mp`,`current_mp`)
     VALUES
           (@user_id,@pet_no,@image,@exp,@hp,@mp,@atk,@def,@spd,@state,@dodge,@name,@growth,@hit,@deepen,@offset,@vamp,@vamp_mp,@custom_name,@talisman_state,@realm,@evolve_count,@synthesis_count,@nirvana_count,@last_evolution_time,@last_synthesis_time,@last_nirvana_time,@original_pet_no,@parent_main_pet_id,@parent_sub_pet_id,@element,@create_time,@status,@is_main,@level,@max_hp,@max_mp,@current_mp) ;SELECT LAST_INSERT_ID(); | 参数: [@user_id=1, @pet_no=79, @image=, @exp=914500, @hp=133, @mp=, @atk=13, @def=13, @spd=13, @state=, @dodge=, @name=, @growth=, @hit=, @deepen=0.00, @offset=0.00, @vamp=0.00, @vamp_mp=0.00, @custom_name=, @talisman_state=, @realm=元神初具, @evolve_count=, @synthesis_count=, @nirvana_count=, @last_evolution_time=, @last_synthesis_time=, @last_nirvana_time=, @original_pet_no=, @parent_main_pet_id=, @parent_sub_pet_id=, @element=, @create_time=2025/9/6 11:57:52, @status=牧场, @is_main=False, @level=0, @max_hp=, @max_mp=, @current_mp=]
[2025-09-06 11:57:52.929 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: INSERT INTO `user_pet`  
           (`user_id`,`pet_no`,`image`,`exp`,`hp`,`mp`,`atk`,`def`,`spd`,`state`,`dodge`,`name`,`growth`,`hit`,`deepen`,`offset`,`vamp`,`vamp_mp`,`custom_name`,`talisman_state`,`realm`,`evolve_count`,`synthesis_count`,`nirvana_count`,`last_evolution_time`,`last_synthesis_time`,`last_nirvana_time`,`original_pet_no`,`parent_main_pet_id`,`parent_sub_pet_id`,`element`,`create_time`,`status`,`is_main`,`level`,`max_hp`,`max_mp`,`current_mp`)
     VALUES
           (@user_id,@pet_no,@image,@exp,@hp,@mp,@atk,@def,@spd,@state,@dodge,@name,@growth,@hit,@deepen,@offset,@vamp,@vamp_mp,@custom_name,@talisman_state,@realm,@evolve_count,@synthesis_count,@nirvana_count,@last_evolution_time,@last_synthesis_time,@last_nirvana_time,@original_pet_no,@parent_main_pet_id,@parent_sub_pet_id,@element,@create_time,@status,@is_main,@level,@max_hp,@max_mp,@current_mp) ;SELECT LAST_INSERT_ID();
[2025-09-06 11:57:52.933 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`user_pet_id`,`skill_id`,`skill_level`,`create_time` FROM `user_pet_skill`  WHERE ( `user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=32]
[2025-09-06 11:57:52.968 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`user_pet_id`,`skill_id`,`skill_level`,`create_time` FROM `user_pet_skill`  WHERE ( `user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:57:52.975 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-06 11:57:52.977 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-06 11:57:52.984 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-06 11:57:53.019 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-06 11:57:53.023 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-06 11:57:53.025 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )   
[2025-09-06 11:57:53.070 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )   
[2025-09-06 11:57:53.073 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )    | 参数: [@MethodConst0=, @MethodConst1=, @MethodConst2=, @MethodConst3=]
[2025-09-06 11:57:53.122 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )   
[2025-09-06 11:57:53.135 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `up`.`id` AS `Id` , `up`.`user_id` AS `UserId` , IFNULL(`u`.`username`,@MethodConst4) AS `UserName` , `up`.`pet_no` AS `PetNo` , IFNULL(`pc`.`name`,@MethodConst5) AS `PetName` , IFNULL(`pc`.`attribute`,@MethodConst6) AS `PetAttribute` , `up`.`image` AS `Image` , `up`.`exp` AS `Exp` , `up`.`hp` AS `Hp` , `up`.`mp` AS `Mp` , `up`.`atk` AS `Atk` , `up`.`def` AS `Def` , `up`.`spd` AS `Spd` , `up`.`state` AS `State` , `up`.`dodge` AS `Dodge` , `up`.`growth` AS `Growth` , `up`.`hit` AS `Hit` , `up`.`realm` AS `Realm` , `up`.`evolve_count` AS `EvolveCount` , `up`.`element` AS `Element` , `up`.`create_time` AS `CreateTime` , IFNULL(`up`.`status`,@MethodConst7) AS `Status` , `up`.`is_main` AS `IsMain`  FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )     ORDER BY `up`.`create_time` DESC LIMIT 0,10 | 参数: [@MethodConst0=, @MethodConst1=, @MethodConst2=, @MethodConst3=, @MethodConst4=, @MethodConst5=, @MethodConst6=, @MethodConst7=]
[2025-09-06 11:57:53.225 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `up`.`id` AS `Id` , `up`.`user_id` AS `UserId` , IFNULL(`u`.`username`,@MethodConst4) AS `UserName` , `up`.`pet_no` AS `PetNo` , IFNULL(`pc`.`name`,@MethodConst5) AS `PetName` , IFNULL(`pc`.`attribute`,@MethodConst6) AS `PetAttribute` , `up`.`image` AS `Image` , `up`.`exp` AS `Exp` , `up`.`hp` AS `Hp` , `up`.`mp` AS `Mp` , `up`.`atk` AS `Atk` , `up`.`def` AS `Def` , `up`.`spd` AS `Spd` , `up`.`state` AS `State` , `up`.`dodge` AS `Dodge` , `up`.`growth` AS `Growth` , `up`.`hit` AS `Hit` , `up`.`realm` AS `Realm` , `up`.`evolve_count` AS `EvolveCount` , `up`.`element` AS `Element` , `up`.`create_time` AS `CreateTime` , IFNULL(`up`.`status`,@MethodConst7) AS `Status` , `up`.`is_main` AS `IsMain`  FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )     ORDER BY `up`.`create_time` DESC LIMIT 0,10
[2025-09-06 11:57:53.258 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=33, @MethodConst1=]
[2025-09-06 11:57:53.301 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:57:53.304 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=914500, @is_active1=1]
[2025-09-06 11:57:53.344 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:57:53.348 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=32, @MethodConst1=]
[2025-09-06 11:57:53.388 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:57:53.392 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=914500, @is_active1=1]
[2025-09-06 11:57:53.429 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:57:53.433 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=31, @MethodConst1=]
[2025-09-06 11:57:53.481 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:57:53.489 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=688500, @is_active1=1]
[2025-09-06 11:57:53.524 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:57:53.527 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=30, @MethodConst1=]
[2025-09-06 11:57:53.560 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:57:53.561 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=637000, @is_active1=1]
[2025-09-06 11:57:53.593 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:57:53.595 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=24, @MethodConst1=]
[2025-09-06 11:57:53.628 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:57:53.630 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=10, @is_active1=1]
[2025-09-06 11:57:53.662 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:57:53.664 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=23, @MethodConst1=]
[2025-09-06 11:57:53.696 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:57:53.697 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=637000, @is_active1=1]
[2025-09-06 11:57:53.731 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:57:53.733 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=22, @MethodConst1=]
[2025-09-06 11:57:53.766 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:57:53.767 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=914500, @is_active1=1]
[2025-09-06 11:57:53.801 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:57:53.803 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=13, @MethodConst1=]
[2025-09-06 11:57:53.838 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:57:53.841 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=914500, @is_active1=1]
[2025-09-06 11:57:53.874 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:57:53.876 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=11, @MethodConst1=]
[2025-09-06 11:57:53.911 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:57:53.913 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=914500, @is_active1=1]
[2025-09-06 11:57:53.947 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:57:53.950 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=19, @MethodConst1=]
[2025-09-06 11:57:53.983 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:57:53.986 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=451000, @is_active1=1]
[2025-09-06 11:57:54.022 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:57:56.349 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-06 11:57:56.410 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-06 11:57:56.423 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-06 11:57:56.458 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-06 11:57:56.468 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-06 11:57:56.485 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`user_id`,`pet_no`,`image`,`exp`,`hp`,`mp`,`atk`,`def`,`spd`,`state`,`dodge`,`name`,`growth`,`hit`,`deepen`,`offset`,`vamp`,`vamp_mp`,`custom_name`,`talisman_state`,`realm`,`evolve_count`,`synthesis_count`,`nirvana_count`,`last_evolution_time`,`last_synthesis_time`,`last_nirvana_time`,`original_pet_no`,`parent_main_pet_id`,`parent_sub_pet_id`,`element`,`create_time`,`status`,`is_main`,`level`,`max_hp`,`max_mp`,`current_mp` FROM `user_pet`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=33]
[2025-09-06 11:57:56.530 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`user_id`,`pet_no`,`image`,`exp`,`hp`,`mp`,`atk`,`def`,`spd`,`state`,`dodge`,`name`,`growth`,`hit`,`deepen`,`offset`,`vamp`,`vamp_mp`,`custom_name`,`talisman_state`,`realm`,`evolve_count`,`synthesis_count`,`nirvana_count`,`last_evolution_time`,`last_synthesis_time`,`last_nirvana_time`,`original_pet_no`,`parent_main_pet_id`,`parent_sub_pet_id`,`element`,`create_time`,`status`,`is_main`,`level`,`max_hp`,`max_mp`,`current_mp` FROM `user_pet`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-09-06 11:57:56.558 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `up`.`id` AS `Id` , `up`.`user_id` AS `UserId` , `up`.`pet_no` AS `PetNo` , IFNULL(`pc`.`name`,@MethodConst1) AS `PetName` , IFNULL(`pc`.`attribute`,@MethodConst2) AS `PetAttribute` , `up`.`image` AS `Image` , `up`.`exp` AS `Exp` , `up`.`hp` AS `Hp` , `up`.`mp` AS `Mp` , `up`.`atk` AS `Atk` , `up`.`def` AS `Def` , `up`.`spd` AS `Spd` , `up`.`state` AS `State` , `up`.`dodge` AS `Dodge` , `up`.`growth` AS `Growth` , `up`.`hit` AS `Hit` , `up`.`realm` AS `Realm` , `up`.`evolve_count` AS `EvolveCount` , `up`.`element` AS `Element` , `up`.`create_time` AS `CreateTime` , IFNULL(`up`.`status`,@MethodConst3) AS `Status` , `up`.`is_main` AS `IsMain`  FROM `user_pet` `up` Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )   WHERE ( `up`.`user_id` = @user_id0 )ORDER BY `up`.`create_time` DESC  | 参数: [@user_id0=1, @MethodConst1=, @MethodConst2=, @MethodConst3=]
[2025-09-06 11:57:56.595 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `up`.`id` AS `Id` , `up`.`user_id` AS `UserId` , `up`.`pet_no` AS `PetNo` , IFNULL(`pc`.`name`,@MethodConst1) AS `PetName` , IFNULL(`pc`.`attribute`,@MethodConst2) AS `PetAttribute` , `up`.`image` AS `Image` , `up`.`exp` AS `Exp` , `up`.`hp` AS `Hp` , `up`.`mp` AS `Mp` , `up`.`atk` AS `Atk` , `up`.`def` AS `Def` , `up`.`spd` AS `Spd` , `up`.`state` AS `State` , `up`.`dodge` AS `Dodge` , `up`.`growth` AS `Growth` , `up`.`hit` AS `Hit` , `up`.`realm` AS `Realm` , `up`.`evolve_count` AS `EvolveCount` , `up`.`element` AS `Element` , `up`.`create_time` AS `CreateTime` , IFNULL(`up`.`status`,@MethodConst3) AS `Status` , `up`.`is_main` AS `IsMain`  FROM `user_pet` `up` Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )   WHERE ( `up`.`user_id` = @user_id0 )ORDER BY `up`.`create_time` DESC 
[2025-09-06 11:57:56.599 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=33, @MethodConst1=]
[2025-09-06 11:57:56.634 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:57:56.636 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=914500, @is_active1=1]
[2025-09-06 11:57:56.670 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:57:56.674 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=32, @MethodConst1=]
[2025-09-06 11:57:56.706 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:57:56.708 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=914500, @is_active1=1]
[2025-09-06 11:57:56.741 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:57:56.742 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=31, @MethodConst1=]
[2025-09-06 11:57:56.774 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:57:56.775 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=688500, @is_active1=1]
[2025-09-06 11:57:56.808 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:57:56.810 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=30, @MethodConst1=]
[2025-09-06 11:57:56.853 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:57:56.856 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=637000, @is_active1=1]
[2025-09-06 11:57:56.895 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:57:56.900 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=24, @MethodConst1=]
[2025-09-06 11:57:56.943 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:57:56.948 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=10, @is_active1=1]
[2025-09-06 11:57:56.997 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:57:57.009 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=23, @MethodConst1=]
[2025-09-06 11:57:57.043 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:57:57.045 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=637000, @is_active1=1]
[2025-09-06 11:57:57.077 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:57:57.079 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=22, @MethodConst1=]
[2025-09-06 11:57:57.119 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:57:57.122 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=914500, @is_active1=1]
[2025-09-06 11:57:57.155 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:57:57.157 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=13, @MethodConst1=]
[2025-09-06 11:57:57.191 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:57:57.208 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=914500, @is_active1=1]
[2025-09-06 11:57:57.243 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:57:57.244 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=11, @MethodConst1=]
[2025-09-06 11:57:57.279 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:57:57.281 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=914500, @is_active1=1]
[2025-09-06 11:57:57.334 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:57:57.337 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=19, @MethodConst1=]
[2025-09-06 11:57:57.373 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:57:57.374 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=451000, @is_active1=1]
[2025-09-06 11:57:57.412 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:57:57.418 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=16, @MethodConst1=]
[2025-09-06 11:57:57.452 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:57:57.455 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=451000, @is_active1=1]
[2025-09-06 11:57:57.488 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:57:57.504 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=18, @MethodConst1=]
[2025-09-06 11:57:57.541 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:57:57.545 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=451000, @is_active1=1]
[2025-09-06 11:57:57.578 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:57:57.582 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: INSERT INTO `user_pet`  
           (`user_id`,`pet_no`,`image`,`exp`,`hp`,`mp`,`atk`,`def`,`spd`,`state`,`dodge`,`name`,`growth`,`hit`,`deepen`,`offset`,`vamp`,`vamp_mp`,`custom_name`,`talisman_state`,`realm`,`evolve_count`,`synthesis_count`,`nirvana_count`,`last_evolution_time`,`last_synthesis_time`,`last_nirvana_time`,`original_pet_no`,`parent_main_pet_id`,`parent_sub_pet_id`,`element`,`create_time`,`status`,`is_main`,`level`,`max_hp`,`max_mp`,`current_mp`)
     VALUES
           (@user_id,@pet_no,@image,@exp,@hp,@mp,@atk,@def,@spd,@state,@dodge,@name,@growth,@hit,@deepen,@offset,@vamp,@vamp_mp,@custom_name,@talisman_state,@realm,@evolve_count,@synthesis_count,@nirvana_count,@last_evolution_time,@last_synthesis_time,@last_nirvana_time,@original_pet_no,@parent_main_pet_id,@parent_sub_pet_id,@element,@create_time,@status,@is_main,@level,@max_hp,@max_mp,@current_mp) ;SELECT LAST_INSERT_ID(); | 参数: [@user_id=1, @pet_no=79, @image=, @exp=914500, @hp=133, @mp=, @atk=13, @def=13, @spd=13, @state=, @dodge=, @name=, @growth=, @hit=, @deepen=0.00, @offset=0.00, @vamp=0.00, @vamp_mp=0.00, @custom_name=, @talisman_state=, @realm=元神初具, @evolve_count=, @synthesis_count=, @nirvana_count=, @last_evolution_time=, @last_synthesis_time=, @last_nirvana_time=, @original_pet_no=, @parent_main_pet_id=, @parent_sub_pet_id=, @element=, @create_time=2025/9/6 11:57:57, @status=牧场, @is_main=False, @level=0, @max_hp=, @max_mp=, @current_mp=]
[2025-09-06 11:57:57.635 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: INSERT INTO `user_pet`  
           (`user_id`,`pet_no`,`image`,`exp`,`hp`,`mp`,`atk`,`def`,`spd`,`state`,`dodge`,`name`,`growth`,`hit`,`deepen`,`offset`,`vamp`,`vamp_mp`,`custom_name`,`talisman_state`,`realm`,`evolve_count`,`synthesis_count`,`nirvana_count`,`last_evolution_time`,`last_synthesis_time`,`last_nirvana_time`,`original_pet_no`,`parent_main_pet_id`,`parent_sub_pet_id`,`element`,`create_time`,`status`,`is_main`,`level`,`max_hp`,`max_mp`,`current_mp`)
     VALUES
           (@user_id,@pet_no,@image,@exp,@hp,@mp,@atk,@def,@spd,@state,@dodge,@name,@growth,@hit,@deepen,@offset,@vamp,@vamp_mp,@custom_name,@talisman_state,@realm,@evolve_count,@synthesis_count,@nirvana_count,@last_evolution_time,@last_synthesis_time,@last_nirvana_time,@original_pet_no,@parent_main_pet_id,@parent_sub_pet_id,@element,@create_time,@status,@is_main,@level,@max_hp,@max_mp,@current_mp) ;SELECT LAST_INSERT_ID();
[2025-09-06 11:57:57.652 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`user_pet_id`,`skill_id`,`skill_level`,`create_time` FROM `user_pet_skill`  WHERE ( `user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=33]
[2025-09-06 11:57:57.703 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`user_pet_id`,`skill_id`,`skill_level`,`create_time` FROM `user_pet_skill`  WHERE ( `user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:57:57.776 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-06 11:57:57.779 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-06 11:57:57.783 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-06 11:57:57.817 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-06 11:57:57.823 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-06 11:57:57.830 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )   
[2025-09-06 11:57:57.886 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )   
[2025-09-06 11:57:57.928 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )    | 参数: [@MethodConst0=, @MethodConst1=, @MethodConst2=, @MethodConst3=]
[2025-09-06 11:57:57.968 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )   
[2025-09-06 11:57:57.975 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `up`.`id` AS `Id` , `up`.`user_id` AS `UserId` , IFNULL(`u`.`username`,@MethodConst4) AS `UserName` , `up`.`pet_no` AS `PetNo` , IFNULL(`pc`.`name`,@MethodConst5) AS `PetName` , IFNULL(`pc`.`attribute`,@MethodConst6) AS `PetAttribute` , `up`.`image` AS `Image` , `up`.`exp` AS `Exp` , `up`.`hp` AS `Hp` , `up`.`mp` AS `Mp` , `up`.`atk` AS `Atk` , `up`.`def` AS `Def` , `up`.`spd` AS `Spd` , `up`.`state` AS `State` , `up`.`dodge` AS `Dodge` , `up`.`growth` AS `Growth` , `up`.`hit` AS `Hit` , `up`.`realm` AS `Realm` , `up`.`evolve_count` AS `EvolveCount` , `up`.`element` AS `Element` , `up`.`create_time` AS `CreateTime` , IFNULL(`up`.`status`,@MethodConst7) AS `Status` , `up`.`is_main` AS `IsMain`  FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )     ORDER BY `up`.`create_time` DESC LIMIT 0,10 | 参数: [@MethodConst0=, @MethodConst1=, @MethodConst2=, @MethodConst3=, @MethodConst4=, @MethodConst5=, @MethodConst6=, @MethodConst7=]
[2025-09-06 11:57:58.013 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `up`.`id` AS `Id` , `up`.`user_id` AS `UserId` , IFNULL(`u`.`username`,@MethodConst4) AS `UserName` , `up`.`pet_no` AS `PetNo` , IFNULL(`pc`.`name`,@MethodConst5) AS `PetName` , IFNULL(`pc`.`attribute`,@MethodConst6) AS `PetAttribute` , `up`.`image` AS `Image` , `up`.`exp` AS `Exp` , `up`.`hp` AS `Hp` , `up`.`mp` AS `Mp` , `up`.`atk` AS `Atk` , `up`.`def` AS `Def` , `up`.`spd` AS `Spd` , `up`.`state` AS `State` , `up`.`dodge` AS `Dodge` , `up`.`growth` AS `Growth` , `up`.`hit` AS `Hit` , `up`.`realm` AS `Realm` , `up`.`evolve_count` AS `EvolveCount` , `up`.`element` AS `Element` , `up`.`create_time` AS `CreateTime` , IFNULL(`up`.`status`,@MethodConst7) AS `Status` , `up`.`is_main` AS `IsMain`  FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )     ORDER BY `up`.`create_time` DESC LIMIT 0,10
[2025-09-06 11:57:58.018 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=34, @MethodConst1=]
[2025-09-06 11:57:58.054 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:57:58.058 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=914500, @is_active1=1]
[2025-09-06 11:57:58.093 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:57:58.097 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=33, @MethodConst1=]
[2025-09-06 11:57:58.133 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:57:58.134 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=914500, @is_active1=1]
[2025-09-06 11:57:58.167 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:57:58.168 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=32, @MethodConst1=]
[2025-09-06 11:57:58.201 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:57:58.203 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=914500, @is_active1=1]
[2025-09-06 11:57:58.235 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:57:58.237 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=31, @MethodConst1=]
[2025-09-06 11:57:58.269 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:57:58.270 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=688500, @is_active1=1]
[2025-09-06 11:57:58.303 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:57:58.305 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=30, @MethodConst1=]
[2025-09-06 11:57:58.338 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:57:58.341 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=637000, @is_active1=1]
[2025-09-06 11:57:58.375 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:57:58.377 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=24, @MethodConst1=]
[2025-09-06 11:57:58.420 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:57:58.422 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=10, @is_active1=1]
[2025-09-06 11:57:58.454 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:57:58.456 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=23, @MethodConst1=]
[2025-09-06 11:57:58.488 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:57:58.490 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=637000, @is_active1=1]
[2025-09-06 11:57:58.524 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:57:58.527 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=22, @MethodConst1=]
[2025-09-06 11:57:58.560 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:57:58.561 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=914500, @is_active1=1]
[2025-09-06 11:57:58.594 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:57:58.595 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=13, @MethodConst1=]
[2025-09-06 11:57:58.636 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:57:58.639 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=914500, @is_active1=1]
[2025-09-06 11:57:58.677 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:57:58.680 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=11, @MethodConst1=]
[2025-09-06 11:57:58.716 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:57:58.719 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=914500, @is_active1=1]
[2025-09-06 11:57:58.764 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:58:01.614 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-06 11:58:01.621 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-06 11:58:01.622 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-06 11:58:01.658 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-06 11:58:01.659 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-06 11:58:01.661 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`user_id`,`pet_no`,`image`,`exp`,`hp`,`mp`,`atk`,`def`,`spd`,`state`,`dodge`,`name`,`growth`,`hit`,`deepen`,`offset`,`vamp`,`vamp_mp`,`custom_name`,`talisman_state`,`realm`,`evolve_count`,`synthesis_count`,`nirvana_count`,`last_evolution_time`,`last_synthesis_time`,`last_nirvana_time`,`original_pet_no`,`parent_main_pet_id`,`parent_sub_pet_id`,`element`,`create_time`,`status`,`is_main`,`level`,`max_hp`,`max_mp`,`current_mp` FROM `user_pet`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=34]
[2025-09-06 11:58:01.704 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`user_id`,`pet_no`,`image`,`exp`,`hp`,`mp`,`atk`,`def`,`spd`,`state`,`dodge`,`name`,`growth`,`hit`,`deepen`,`offset`,`vamp`,`vamp_mp`,`custom_name`,`talisman_state`,`realm`,`evolve_count`,`synthesis_count`,`nirvana_count`,`last_evolution_time`,`last_synthesis_time`,`last_nirvana_time`,`original_pet_no`,`parent_main_pet_id`,`parent_sub_pet_id`,`element`,`create_time`,`status`,`is_main`,`level`,`max_hp`,`max_mp`,`current_mp` FROM `user_pet`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-09-06 11:58:01.718 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `up`.`id` AS `Id` , `up`.`user_id` AS `UserId` , `up`.`pet_no` AS `PetNo` , IFNULL(`pc`.`name`,@MethodConst1) AS `PetName` , IFNULL(`pc`.`attribute`,@MethodConst2) AS `PetAttribute` , `up`.`image` AS `Image` , `up`.`exp` AS `Exp` , `up`.`hp` AS `Hp` , `up`.`mp` AS `Mp` , `up`.`atk` AS `Atk` , `up`.`def` AS `Def` , `up`.`spd` AS `Spd` , `up`.`state` AS `State` , `up`.`dodge` AS `Dodge` , `up`.`growth` AS `Growth` , `up`.`hit` AS `Hit` , `up`.`realm` AS `Realm` , `up`.`evolve_count` AS `EvolveCount` , `up`.`element` AS `Element` , `up`.`create_time` AS `CreateTime` , IFNULL(`up`.`status`,@MethodConst3) AS `Status` , `up`.`is_main` AS `IsMain`  FROM `user_pet` `up` Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )   WHERE ( `up`.`user_id` = @user_id0 )ORDER BY `up`.`create_time` DESC  | 参数: [@user_id0=1, @MethodConst1=, @MethodConst2=, @MethodConst3=]
[2025-09-06 11:58:01.756 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `up`.`id` AS `Id` , `up`.`user_id` AS `UserId` , `up`.`pet_no` AS `PetNo` , IFNULL(`pc`.`name`,@MethodConst1) AS `PetName` , IFNULL(`pc`.`attribute`,@MethodConst2) AS `PetAttribute` , `up`.`image` AS `Image` , `up`.`exp` AS `Exp` , `up`.`hp` AS `Hp` , `up`.`mp` AS `Mp` , `up`.`atk` AS `Atk` , `up`.`def` AS `Def` , `up`.`spd` AS `Spd` , `up`.`state` AS `State` , `up`.`dodge` AS `Dodge` , `up`.`growth` AS `Growth` , `up`.`hit` AS `Hit` , `up`.`realm` AS `Realm` , `up`.`evolve_count` AS `EvolveCount` , `up`.`element` AS `Element` , `up`.`create_time` AS `CreateTime` , IFNULL(`up`.`status`,@MethodConst3) AS `Status` , `up`.`is_main` AS `IsMain`  FROM `user_pet` `up` Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )   WHERE ( `up`.`user_id` = @user_id0 )ORDER BY `up`.`create_time` DESC 
[2025-09-06 11:58:01.757 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=34, @MethodConst1=]
[2025-09-06 11:58:01.791 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:58:01.792 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=914500, @is_active1=1]
[2025-09-06 11:58:01.825 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:58:01.827 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=33, @MethodConst1=]
[2025-09-06 11:58:01.860 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:58:01.862 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=914500, @is_active1=1]
[2025-09-06 11:58:01.895 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:58:01.902 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=32, @MethodConst1=]
[2025-09-06 11:58:01.935 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:58:01.937 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=914500, @is_active1=1]
[2025-09-06 11:58:01.969 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:58:01.971 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=31, @MethodConst1=]
[2025-09-06 11:58:02.004 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:58:02.017 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=688500, @is_active1=1]
[2025-09-06 11:58:02.053 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:58:02.057 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=30, @MethodConst1=]
[2025-09-06 11:58:02.100 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:58:02.104 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=637000, @is_active1=1]
[2025-09-06 11:58:02.139 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:58:02.146 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=24, @MethodConst1=]
[2025-09-06 11:58:02.185 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:58:02.188 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=10, @is_active1=1]
[2025-09-06 11:58:02.285 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:58:02.322 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=23, @MethodConst1=]
[2025-09-06 11:58:02.375 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:58:02.383 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=637000, @is_active1=1]
[2025-09-06 11:58:02.427 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:58:02.465 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=22, @MethodConst1=]
[2025-09-06 11:58:02.511 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:58:02.515 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=914500, @is_active1=1]
[2025-09-06 11:58:02.549 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:58:02.552 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=13, @MethodConst1=]
[2025-09-06 11:58:02.585 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:58:02.588 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=914500, @is_active1=1]
[2025-09-06 11:58:02.621 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:58:02.625 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=11, @MethodConst1=]
[2025-09-06 11:58:02.660 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:58:02.665 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=914500, @is_active1=1]
[2025-09-06 11:58:02.699 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:58:02.701 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=19, @MethodConst1=]
[2025-09-06 11:58:02.734 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:58:02.735 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=451000, @is_active1=1]
[2025-09-06 11:58:02.771 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:58:02.775 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=16, @MethodConst1=]
[2025-09-06 11:58:02.815 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:58:02.820 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=451000, @is_active1=1]
[2025-09-06 11:58:02.878 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:58:02.889 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=18, @MethodConst1=]
[2025-09-06 11:58:02.926 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:58:02.932 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=451000, @is_active1=1]
[2025-09-06 11:58:02.965 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:58:02.968 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: INSERT INTO `user_pet`  
           (`user_id`,`pet_no`,`image`,`exp`,`hp`,`mp`,`atk`,`def`,`spd`,`state`,`dodge`,`name`,`growth`,`hit`,`deepen`,`offset`,`vamp`,`vamp_mp`,`custom_name`,`talisman_state`,`realm`,`evolve_count`,`synthesis_count`,`nirvana_count`,`last_evolution_time`,`last_synthesis_time`,`last_nirvana_time`,`original_pet_no`,`parent_main_pet_id`,`parent_sub_pet_id`,`element`,`create_time`,`status`,`is_main`,`level`,`max_hp`,`max_mp`,`current_mp`)
     VALUES
           (@user_id,@pet_no,@image,@exp,@hp,@mp,@atk,@def,@spd,@state,@dodge,@name,@growth,@hit,@deepen,@offset,@vamp,@vamp_mp,@custom_name,@talisman_state,@realm,@evolve_count,@synthesis_count,@nirvana_count,@last_evolution_time,@last_synthesis_time,@last_nirvana_time,@original_pet_no,@parent_main_pet_id,@parent_sub_pet_id,@element,@create_time,@status,@is_main,@level,@max_hp,@max_mp,@current_mp) ;SELECT LAST_INSERT_ID(); | 参数: [@user_id=1, @pet_no=79, @image=, @exp=914500, @hp=133, @mp=, @atk=13, @def=13, @spd=13, @state=, @dodge=, @name=, @growth=, @hit=, @deepen=0.00, @offset=0.00, @vamp=0.00, @vamp_mp=0.00, @custom_name=, @talisman_state=, @realm=元神初具, @evolve_count=, @synthesis_count=, @nirvana_count=, @last_evolution_time=, @last_synthesis_time=, @last_nirvana_time=, @original_pet_no=, @parent_main_pet_id=, @parent_sub_pet_id=, @element=, @create_time=2025/9/6 11:58:02, @status=牧场, @is_main=False, @level=0, @max_hp=, @max_mp=, @current_mp=]
[2025-09-06 11:58:03.007 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: INSERT INTO `user_pet`  
           (`user_id`,`pet_no`,`image`,`exp`,`hp`,`mp`,`atk`,`def`,`spd`,`state`,`dodge`,`name`,`growth`,`hit`,`deepen`,`offset`,`vamp`,`vamp_mp`,`custom_name`,`talisman_state`,`realm`,`evolve_count`,`synthesis_count`,`nirvana_count`,`last_evolution_time`,`last_synthesis_time`,`last_nirvana_time`,`original_pet_no`,`parent_main_pet_id`,`parent_sub_pet_id`,`element`,`create_time`,`status`,`is_main`,`level`,`max_hp`,`max_mp`,`current_mp`)
     VALUES
           (@user_id,@pet_no,@image,@exp,@hp,@mp,@atk,@def,@spd,@state,@dodge,@name,@growth,@hit,@deepen,@offset,@vamp,@vamp_mp,@custom_name,@talisman_state,@realm,@evolve_count,@synthesis_count,@nirvana_count,@last_evolution_time,@last_synthesis_time,@last_nirvana_time,@original_pet_no,@parent_main_pet_id,@parent_sub_pet_id,@element,@create_time,@status,@is_main,@level,@max_hp,@max_mp,@current_mp) ;SELECT LAST_INSERT_ID();
[2025-09-06 11:58:03.010 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`user_pet_id`,`skill_id`,`skill_level`,`create_time` FROM `user_pet_skill`  WHERE ( `user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=34]
[2025-09-06 11:58:03.045 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`user_pet_id`,`skill_id`,`skill_level`,`create_time` FROM `user_pet_skill`  WHERE ( `user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:58:03.050 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-06 11:58:03.051 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-06 11:58:03.054 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-06 11:58:03.087 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-06 11:58:03.091 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-06 11:58:03.094 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )   
[2025-09-06 11:58:03.129 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )   
[2025-09-06 11:58:03.134 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )    | 参数: [@MethodConst0=, @MethodConst1=, @MethodConst2=, @MethodConst3=]
[2025-09-06 11:58:03.173 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )   
[2025-09-06 11:58:03.176 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `up`.`id` AS `Id` , `up`.`user_id` AS `UserId` , IFNULL(`u`.`username`,@MethodConst4) AS `UserName` , `up`.`pet_no` AS `PetNo` , IFNULL(`pc`.`name`,@MethodConst5) AS `PetName` , IFNULL(`pc`.`attribute`,@MethodConst6) AS `PetAttribute` , `up`.`image` AS `Image` , `up`.`exp` AS `Exp` , `up`.`hp` AS `Hp` , `up`.`mp` AS `Mp` , `up`.`atk` AS `Atk` , `up`.`def` AS `Def` , `up`.`spd` AS `Spd` , `up`.`state` AS `State` , `up`.`dodge` AS `Dodge` , `up`.`growth` AS `Growth` , `up`.`hit` AS `Hit` , `up`.`realm` AS `Realm` , `up`.`evolve_count` AS `EvolveCount` , `up`.`element` AS `Element` , `up`.`create_time` AS `CreateTime` , IFNULL(`up`.`status`,@MethodConst7) AS `Status` , `up`.`is_main` AS `IsMain`  FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )     ORDER BY `up`.`create_time` DESC LIMIT 0,10 | 参数: [@MethodConst0=, @MethodConst1=, @MethodConst2=, @MethodConst3=, @MethodConst4=, @MethodConst5=, @MethodConst6=, @MethodConst7=]
[2025-09-06 11:58:03.212 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `up`.`id` AS `Id` , `up`.`user_id` AS `UserId` , IFNULL(`u`.`username`,@MethodConst4) AS `UserName` , `up`.`pet_no` AS `PetNo` , IFNULL(`pc`.`name`,@MethodConst5) AS `PetName` , IFNULL(`pc`.`attribute`,@MethodConst6) AS `PetAttribute` , `up`.`image` AS `Image` , `up`.`exp` AS `Exp` , `up`.`hp` AS `Hp` , `up`.`mp` AS `Mp` , `up`.`atk` AS `Atk` , `up`.`def` AS `Def` , `up`.`spd` AS `Spd` , `up`.`state` AS `State` , `up`.`dodge` AS `Dodge` , `up`.`growth` AS `Growth` , `up`.`hit` AS `Hit` , `up`.`realm` AS `Realm` , `up`.`evolve_count` AS `EvolveCount` , `up`.`element` AS `Element` , `up`.`create_time` AS `CreateTime` , IFNULL(`up`.`status`,@MethodConst7) AS `Status` , `up`.`is_main` AS `IsMain`  FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )     ORDER BY `up`.`create_time` DESC LIMIT 0,10
[2025-09-06 11:58:03.215 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=35, @MethodConst1=]
[2025-09-06 11:58:03.249 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:58:03.252 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=914500, @is_active1=1]
[2025-09-06 11:58:03.285 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:58:03.289 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=34, @MethodConst1=]
[2025-09-06 11:58:03.325 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:58:03.326 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=914500, @is_active1=1]
[2025-09-06 11:58:03.359 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:58:03.361 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=33, @MethodConst1=]
[2025-09-06 11:58:03.397 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:58:03.399 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=914500, @is_active1=1]
[2025-09-06 11:58:03.433 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:58:03.455 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=32, @MethodConst1=]
[2025-09-06 11:58:03.516 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:58:03.827 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=914500, @is_active1=1]
[2025-09-06 11:58:03.868 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:58:03.900 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=31, @MethodConst1=]
[2025-09-06 11:58:03.934 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:58:03.938 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=688500, @is_active1=1]
[2025-09-06 11:58:03.980 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:58:03.985 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=30, @MethodConst1=]
[2025-09-06 11:58:04.023 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:58:04.030 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=637000, @is_active1=1]
[2025-09-06 11:58:04.104 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:58:04.135 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=24, @MethodConst1=]
[2025-09-06 11:58:04.171 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:58:04.174 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=10, @is_active1=1]
[2025-09-06 11:58:04.210 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:58:04.214 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=23, @MethodConst1=]
[2025-09-06 11:58:04.247 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:58:04.249 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=637000, @is_active1=1]
[2025-09-06 11:58:04.283 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:58:04.293 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=22, @MethodConst1=]
[2025-09-06 11:58:04.330 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:58:04.333 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=914500, @is_active1=1]
[2025-09-06 11:58:04.375 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:58:04.380 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=13, @MethodConst1=]
[2025-09-06 11:58:04.414 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:58:04.420 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=914500, @is_active1=1]
[2025-09-06 11:58:04.459 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:58:24.193 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-06 11:58:24.195 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-06 11:58:24.197 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-06 11:58:24.231 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-06 11:58:24.233 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-06 11:58:24.237 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `up`.`id` AS `Id` , `up`.`user_id` AS `UserId` , IFNULL(`u`.`username`,@MethodConst5) AS `UserName` , `up`.`pet_no` AS `PetNo` , IFNULL(`pc`.`name`,@MethodConst6) AS `PetName` , IFNULL(`pc`.`attribute`,@MethodConst7) AS `PetAttribute` , `up`.`image` AS `Image` , `up`.`exp` AS `Exp` , `up`.`hp` AS `Hp` , `up`.`mp` AS `Mp` , `up`.`atk` AS `Atk` , `up`.`def` AS `Def` , `up`.`spd` AS `Spd` , `up`.`state` AS `State` , `up`.`dodge` AS `Dodge` , `up`.`growth` AS `Growth` , `up`.`hit` AS `Hit` , `up`.`realm` AS `Realm` , `up`.`evolve_count` AS `EvolveCount` , `up`.`element` AS `Element` , `up`.`create_time` AS `CreateTime` , IFNULL(`up`.`status`,@MethodConst8) AS `Status` , `up`.`is_main` AS `IsMain`  FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )    WHERE ( `up`.`id` = @id0 )   LIMIT 0,1 | 参数: [@id0=33, @MethodConst1=, @MethodConst2=, @MethodConst3=, @MethodConst4=, @MethodConst5=, @MethodConst6=, @MethodConst7=, @MethodConst8=]
[2025-09-06 11:58:24.278 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `up`.`id` AS `Id` , `up`.`user_id` AS `UserId` , IFNULL(`u`.`username`,@MethodConst5) AS `UserName` , `up`.`pet_no` AS `PetNo` , IFNULL(`pc`.`name`,@MethodConst6) AS `PetName` , IFNULL(`pc`.`attribute`,@MethodConst7) AS `PetAttribute` , `up`.`image` AS `Image` , `up`.`exp` AS `Exp` , `up`.`hp` AS `Hp` , `up`.`mp` AS `Mp` , `up`.`atk` AS `Atk` , `up`.`def` AS `Def` , `up`.`spd` AS `Spd` , `up`.`state` AS `State` , `up`.`dodge` AS `Dodge` , `up`.`growth` AS `Growth` , `up`.`hit` AS `Hit` , `up`.`realm` AS `Realm` , `up`.`evolve_count` AS `EvolveCount` , `up`.`element` AS `Element` , `up`.`create_time` AS `CreateTime` , IFNULL(`up`.`status`,@MethodConst8) AS `Status` , `up`.`is_main` AS `IsMain`  FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )    WHERE ( `up`.`id` = @id0 )   LIMIT 0,1
[2025-09-06 11:58:24.284 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=33, @MethodConst1=]
[2025-09-06 11:58:24.320 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:58:24.322 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=914500, @is_active1=1]
[2025-09-06 11:58:24.368 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:58:24.371 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `up`.`id` AS `Id` , `up`.`user_id` AS `UserId` , `up`.`pet_no` AS `PetNo` , IFNULL(`pc`.`name`,@MethodConst1) AS `PetName` , IFNULL(`pc`.`attribute`,@MethodConst2) AS `PetAttribute` , `up`.`image` AS `Image` , `up`.`exp` AS `Exp` , `up`.`hp` AS `Hp` , `up`.`mp` AS `Mp` , `up`.`atk` AS `Atk` , `up`.`def` AS `Def` , `up`.`spd` AS `Spd` , `up`.`state` AS `State` , `up`.`dodge` AS `Dodge` , `up`.`growth` AS `Growth` , `up`.`hit` AS `Hit` , `up`.`realm` AS `Realm` , `up`.`evolve_count` AS `EvolveCount` , `up`.`element` AS `Element` , `up`.`create_time` AS `CreateTime` , IFNULL(`up`.`status`,@MethodConst3) AS `Status` , `up`.`is_main` AS `IsMain`  FROM `user_pet` `up` Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )   WHERE ( `up`.`user_id` = @user_id0 )ORDER BY `up`.`create_time` DESC  | 参数: [@user_id0=1, @MethodConst1=, @MethodConst2=, @MethodConst3=]
[2025-09-06 11:58:24.409 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `up`.`id` AS `Id` , `up`.`user_id` AS `UserId` , `up`.`pet_no` AS `PetNo` , IFNULL(`pc`.`name`,@MethodConst1) AS `PetName` , IFNULL(`pc`.`attribute`,@MethodConst2) AS `PetAttribute` , `up`.`image` AS `Image` , `up`.`exp` AS `Exp` , `up`.`hp` AS `Hp` , `up`.`mp` AS `Mp` , `up`.`atk` AS `Atk` , `up`.`def` AS `Def` , `up`.`spd` AS `Spd` , `up`.`state` AS `State` , `up`.`dodge` AS `Dodge` , `up`.`growth` AS `Growth` , `up`.`hit` AS `Hit` , `up`.`realm` AS `Realm` , `up`.`evolve_count` AS `EvolveCount` , `up`.`element` AS `Element` , `up`.`create_time` AS `CreateTime` , IFNULL(`up`.`status`,@MethodConst3) AS `Status` , `up`.`is_main` AS `IsMain`  FROM `user_pet` `up` Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )   WHERE ( `up`.`user_id` = @user_id0 )ORDER BY `up`.`create_time` DESC 
[2025-09-06 11:58:24.413 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=35, @MethodConst1=]
[2025-09-06 11:58:24.447 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:58:24.452 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=914500, @is_active1=1]
[2025-09-06 11:58:24.488 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:58:24.494 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=34, @MethodConst1=]
[2025-09-06 11:58:24.529 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:58:24.530 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=914500, @is_active1=1]
[2025-09-06 11:58:24.567 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:58:24.578 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=33, @MethodConst1=]
[2025-09-06 11:58:24.631 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:58:24.632 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=914500, @is_active1=1]
[2025-09-06 11:58:24.677 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:58:24.681 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=32, @MethodConst1=]
[2025-09-06 11:58:24.727 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:58:24.730 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=914500, @is_active1=1]
[2025-09-06 11:58:24.766 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:58:24.770 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=31, @MethodConst1=]
[2025-09-06 11:58:24.809 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:58:24.817 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=688500, @is_active1=1]
[2025-09-06 11:58:24.852 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:58:24.854 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=30, @MethodConst1=]
[2025-09-06 11:58:24.888 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:58:24.893 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=637000, @is_active1=1]
[2025-09-06 11:58:24.927 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:58:24.930 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=24, @MethodConst1=]
[2025-09-06 11:58:24.963 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:58:24.966 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=10, @is_active1=1]
[2025-09-06 11:58:24.999 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:58:25.002 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=23, @MethodConst1=]
[2025-09-06 11:58:25.037 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:58:25.046 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=637000, @is_active1=1]
[2025-09-06 11:58:25.082 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:58:25.086 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=22, @MethodConst1=]
[2025-09-06 11:58:25.121 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:58:25.128 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=914500, @is_active1=1]
[2025-09-06 11:58:25.162 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:58:25.165 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=13, @MethodConst1=]
[2025-09-06 11:58:25.208 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:58:25.211 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=914500, @is_active1=1]
[2025-09-06 11:58:25.247 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:58:25.252 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=11, @MethodConst1=]
[2025-09-06 11:58:25.287 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:58:25.294 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=914500, @is_active1=1]
[2025-09-06 11:58:25.343 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:58:25.356 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=19, @MethodConst1=]
[2025-09-06 11:58:25.394 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:58:25.397 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=451000, @is_active1=1]
[2025-09-06 11:58:25.434 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:58:25.438 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=16, @MethodConst1=]
[2025-09-06 11:58:25.495 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:58:25.497 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=451000, @is_active1=1]
[2025-09-06 11:58:25.532 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:58:25.535 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=18, @MethodConst1=]
[2025-09-06 11:58:25.588 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:58:25.592 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=451000, @is_active1=1]
[2025-09-06 11:58:25.627 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:58:25.634 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`user_id`,`pet_no`,`image`,`exp`,`hp`,`mp`,`atk`,`def`,`spd`,`state`,`dodge`,`name`,`growth`,`hit`,`deepen`,`offset`,`vamp`,`vamp_mp`,`custom_name`,`talisman_state`,`realm`,`evolve_count`,`synthesis_count`,`nirvana_count`,`last_evolution_time`,`last_synthesis_time`,`last_nirvana_time`,`original_pet_no`,`parent_main_pet_id`,`parent_sub_pet_id`,`element`,`create_time`,`status`,`is_main`,`level`,`max_hp`,`max_mp`,`current_mp` FROM `user_pet`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=33]
[2025-09-06 11:58:25.671 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`user_id`,`pet_no`,`image`,`exp`,`hp`,`mp`,`atk`,`def`,`spd`,`state`,`dodge`,`name`,`growth`,`hit`,`deepen`,`offset`,`vamp`,`vamp_mp`,`custom_name`,`talisman_state`,`realm`,`evolve_count`,`synthesis_count`,`nirvana_count`,`last_evolution_time`,`last_synthesis_time`,`last_nirvana_time`,`original_pet_no`,`parent_main_pet_id`,`parent_sub_pet_id`,`element`,`create_time`,`status`,`is_main`,`level`,`max_hp`,`max_mp`,`current_mp` FROM `user_pet`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-09-06 11:58:25.675 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: UPDATE `user_pet`  SET
           `user_id`=@user_id,`pet_no`=@pet_no,`image`=@image,`exp`=@exp,`hp`=@hp,`mp`=@mp,`atk`=@atk,`def`=@def,`spd`=@spd,`state`=@state,`dodge`=@dodge,`name`=@name,`growth`=@growth,`hit`=@hit,`deepen`=@deepen,`offset`=@offset,`vamp`=@vamp,`vamp_mp`=@vamp_mp,`custom_name`=@custom_name,`talisman_state`=@talisman_state,`realm`=@realm,`evolve_count`=@evolve_count,`synthesis_count`=@synthesis_count,`nirvana_count`=@nirvana_count,`last_evolution_time`=@last_evolution_time,`last_synthesis_time`=@last_synthesis_time,`last_nirvana_time`=@last_nirvana_time,`original_pet_no`=@original_pet_no,`parent_main_pet_id`=@parent_main_pet_id,`parent_sub_pet_id`=@parent_sub_pet_id,`element`=@element,`create_time`=@create_time,`status`=@status,`is_main`=@is_main,`level`=@level,`max_hp`=@max_hp,`max_mp`=@max_mp,`current_mp`=@current_mp  WHERE `id`=@id | 参数: [@id=33, @user_id=1, @pet_no=79, @image=, @exp=976000, @hp=133, @mp=, @atk=13, @def=13, @spd=13, @state=, @dodge=, @name=, @growth=, @hit=, @deepen=0.00, @offset=0.00, @vamp=0.00, @vamp_mp=0.00, @custom_name=, @talisman_state=, @realm=, @evolve_count=, @synthesis_count=, @nirvana_count=, @last_evolution_time=, @last_synthesis_time=, @last_nirvana_time=, @original_pet_no=, @parent_main_pet_id=, @parent_sub_pet_id=, @element=, @create_time=2025/9/6 11:57:53, @status=牧场, @is_main=False, @level=0, @max_hp=, @max_mp=, @current_mp=]
[2025-09-06 11:58:25.745 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: UPDATE `user_pet`  SET
           `user_id`=@user_id,`pet_no`=@pet_no,`image`=@image,`exp`=@exp,`hp`=@hp,`mp`=@mp,`atk`=@atk,`def`=@def,`spd`=@spd,`state`=@state,`dodge`=@dodge,`name`=@name,`growth`=@growth,`hit`=@hit,`deepen`=@deepen,`offset`=@offset,`vamp`=@vamp,`vamp_mp`=@vamp_mp,`custom_name`=@custom_name,`talisman_state`=@talisman_state,`realm`=@realm,`evolve_count`=@evolve_count,`synthesis_count`=@synthesis_count,`nirvana_count`=@nirvana_count,`last_evolution_time`=@last_evolution_time,`last_synthesis_time`=@last_synthesis_time,`last_nirvana_time`=@last_nirvana_time,`original_pet_no`=@original_pet_no,`parent_main_pet_id`=@parent_main_pet_id,`parent_sub_pet_id`=@parent_sub_pet_id,`element`=@element,`create_time`=@create_time,`status`=@status,`is_main`=@is_main,`level`=@level,`max_hp`=@max_hp,`max_mp`=@max_mp,`current_mp`=@current_mp  WHERE `id`=@id
[2025-09-06 11:58:25.756 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-06 11:58:25.761 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-06 11:58:25.762 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-06 11:58:25.805 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-06 11:58:25.809 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-06 11:58:25.811 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )   
[2025-09-06 11:58:25.851 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )   
[2025-09-06 11:58:25.854 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )    | 参数: [@MethodConst0=, @MethodConst1=, @MethodConst2=, @MethodConst3=]
[2025-09-06 11:58:25.896 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )   
[2025-09-06 11:58:25.901 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `up`.`id` AS `Id` , `up`.`user_id` AS `UserId` , IFNULL(`u`.`username`,@MethodConst4) AS `UserName` , `up`.`pet_no` AS `PetNo` , IFNULL(`pc`.`name`,@MethodConst5) AS `PetName` , IFNULL(`pc`.`attribute`,@MethodConst6) AS `PetAttribute` , `up`.`image` AS `Image` , `up`.`exp` AS `Exp` , `up`.`hp` AS `Hp` , `up`.`mp` AS `Mp` , `up`.`atk` AS `Atk` , `up`.`def` AS `Def` , `up`.`spd` AS `Spd` , `up`.`state` AS `State` , `up`.`dodge` AS `Dodge` , `up`.`growth` AS `Growth` , `up`.`hit` AS `Hit` , `up`.`realm` AS `Realm` , `up`.`evolve_count` AS `EvolveCount` , `up`.`element` AS `Element` , `up`.`create_time` AS `CreateTime` , IFNULL(`up`.`status`,@MethodConst7) AS `Status` , `up`.`is_main` AS `IsMain`  FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )     ORDER BY `up`.`create_time` DESC LIMIT 0,10 | 参数: [@MethodConst0=, @MethodConst1=, @MethodConst2=, @MethodConst3=, @MethodConst4=, @MethodConst5=, @MethodConst6=, @MethodConst7=]
[2025-09-06 11:58:25.938 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `up`.`id` AS `Id` , `up`.`user_id` AS `UserId` , IFNULL(`u`.`username`,@MethodConst4) AS `UserName` , `up`.`pet_no` AS `PetNo` , IFNULL(`pc`.`name`,@MethodConst5) AS `PetName` , IFNULL(`pc`.`attribute`,@MethodConst6) AS `PetAttribute` , `up`.`image` AS `Image` , `up`.`exp` AS `Exp` , `up`.`hp` AS `Hp` , `up`.`mp` AS `Mp` , `up`.`atk` AS `Atk` , `up`.`def` AS `Def` , `up`.`spd` AS `Spd` , `up`.`state` AS `State` , `up`.`dodge` AS `Dodge` , `up`.`growth` AS `Growth` , `up`.`hit` AS `Hit` , `up`.`realm` AS `Realm` , `up`.`evolve_count` AS `EvolveCount` , `up`.`element` AS `Element` , `up`.`create_time` AS `CreateTime` , IFNULL(`up`.`status`,@MethodConst7) AS `Status` , `up`.`is_main` AS `IsMain`  FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )     ORDER BY `up`.`create_time` DESC LIMIT 0,10
[2025-09-06 11:58:25.941 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=35, @MethodConst1=]
[2025-09-06 11:58:25.976 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:58:25.978 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=914500, @is_active1=1]
[2025-09-06 11:58:26.016 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:58:26.019 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=34, @MethodConst1=]
[2025-09-06 11:58:26.060 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:58:26.063 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=914500, @is_active1=1]
[2025-09-06 11:58:26.098 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:58:26.103 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=33, @MethodConst1=]
[2025-09-06 11:58:26.147 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:58:26.149 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=976000, @is_active1=1]
[2025-09-06 11:58:26.197 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:58:26.201 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=32, @MethodConst1=]
[2025-09-06 11:58:26.286 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:58:26.295 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=914500, @is_active1=1]
[2025-09-06 11:58:26.334 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:58:26.338 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=31, @MethodConst1=]
[2025-09-06 11:58:26.407 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:58:26.614 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=688500, @is_active1=1]
[2025-09-06 11:58:26.665 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:58:26.721 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=30, @MethodConst1=]
[2025-09-06 11:58:26.787 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:58:26.805 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=637000, @is_active1=1]
[2025-09-06 11:58:26.844 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:58:26.848 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=24, @MethodConst1=]
[2025-09-06 11:58:26.889 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:58:26.896 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=10, @is_active1=1]
[2025-09-06 11:58:26.933 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:58:26.936 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=23, @MethodConst1=]
[2025-09-06 11:58:26.983 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:58:26.985 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=637000, @is_active1=1]
[2025-09-06 11:58:27.020 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:58:27.025 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=22, @MethodConst1=]
[2025-09-06 11:58:27.070 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:58:27.076 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=914500, @is_active1=1]
[2025-09-06 11:58:27.110 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:58:27.113 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=13, @MethodConst1=]
[2025-09-06 11:58:27.149 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:58:27.154 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=914500, @is_active1=1]
[2025-09-06 11:58:27.190 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:58:40.568 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-06 11:58:40.572 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-06 11:58:40.574 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-06 11:58:40.610 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-06 11:58:40.611 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-06 11:58:40.615 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `up`.`id` AS `Id` , `up`.`user_id` AS `UserId` , IFNULL(`u`.`username`,@MethodConst5) AS `UserName` , `up`.`pet_no` AS `PetNo` , IFNULL(`pc`.`name`,@MethodConst6) AS `PetName` , IFNULL(`pc`.`attribute`,@MethodConst7) AS `PetAttribute` , `up`.`image` AS `Image` , `up`.`exp` AS `Exp` , `up`.`hp` AS `Hp` , `up`.`mp` AS `Mp` , `up`.`atk` AS `Atk` , `up`.`def` AS `Def` , `up`.`spd` AS `Spd` , `up`.`state` AS `State` , `up`.`dodge` AS `Dodge` , `up`.`growth` AS `Growth` , `up`.`hit` AS `Hit` , `up`.`realm` AS `Realm` , `up`.`evolve_count` AS `EvolveCount` , `up`.`element` AS `Element` , `up`.`create_time` AS `CreateTime` , IFNULL(`up`.`status`,@MethodConst8) AS `Status` , `up`.`is_main` AS `IsMain`  FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )    WHERE ( `up`.`id` = @id0 )   LIMIT 0,1 | 参数: [@id0=34, @MethodConst1=, @MethodConst2=, @MethodConst3=, @MethodConst4=, @MethodConst5=, @MethodConst6=, @MethodConst7=, @MethodConst8=]
[2025-09-06 11:58:40.651 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `up`.`id` AS `Id` , `up`.`user_id` AS `UserId` , IFNULL(`u`.`username`,@MethodConst5) AS `UserName` , `up`.`pet_no` AS `PetNo` , IFNULL(`pc`.`name`,@MethodConst6) AS `PetName` , IFNULL(`pc`.`attribute`,@MethodConst7) AS `PetAttribute` , `up`.`image` AS `Image` , `up`.`exp` AS `Exp` , `up`.`hp` AS `Hp` , `up`.`mp` AS `Mp` , `up`.`atk` AS `Atk` , `up`.`def` AS `Def` , `up`.`spd` AS `Spd` , `up`.`state` AS `State` , `up`.`dodge` AS `Dodge` , `up`.`growth` AS `Growth` , `up`.`hit` AS `Hit` , `up`.`realm` AS `Realm` , `up`.`evolve_count` AS `EvolveCount` , `up`.`element` AS `Element` , `up`.`create_time` AS `CreateTime` , IFNULL(`up`.`status`,@MethodConst8) AS `Status` , `up`.`is_main` AS `IsMain`  FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )    WHERE ( `up`.`id` = @id0 )   LIMIT 0,1
[2025-09-06 11:58:40.658 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=34, @MethodConst1=]
[2025-09-06 11:58:40.694 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:58:40.696 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=914500, @is_active1=1]
[2025-09-06 11:58:40.730 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:58:40.734 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `up`.`id` AS `Id` , `up`.`user_id` AS `UserId` , `up`.`pet_no` AS `PetNo` , IFNULL(`pc`.`name`,@MethodConst1) AS `PetName` , IFNULL(`pc`.`attribute`,@MethodConst2) AS `PetAttribute` , `up`.`image` AS `Image` , `up`.`exp` AS `Exp` , `up`.`hp` AS `Hp` , `up`.`mp` AS `Mp` , `up`.`atk` AS `Atk` , `up`.`def` AS `Def` , `up`.`spd` AS `Spd` , `up`.`state` AS `State` , `up`.`dodge` AS `Dodge` , `up`.`growth` AS `Growth` , `up`.`hit` AS `Hit` , `up`.`realm` AS `Realm` , `up`.`evolve_count` AS `EvolveCount` , `up`.`element` AS `Element` , `up`.`create_time` AS `CreateTime` , IFNULL(`up`.`status`,@MethodConst3) AS `Status` , `up`.`is_main` AS `IsMain`  FROM `user_pet` `up` Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )   WHERE ( `up`.`user_id` = @user_id0 )ORDER BY `up`.`create_time` DESC  | 参数: [@user_id0=1, @MethodConst1=, @MethodConst2=, @MethodConst3=]
[2025-09-06 11:58:40.778 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `up`.`id` AS `Id` , `up`.`user_id` AS `UserId` , `up`.`pet_no` AS `PetNo` , IFNULL(`pc`.`name`,@MethodConst1) AS `PetName` , IFNULL(`pc`.`attribute`,@MethodConst2) AS `PetAttribute` , `up`.`image` AS `Image` , `up`.`exp` AS `Exp` , `up`.`hp` AS `Hp` , `up`.`mp` AS `Mp` , `up`.`atk` AS `Atk` , `up`.`def` AS `Def` , `up`.`spd` AS `Spd` , `up`.`state` AS `State` , `up`.`dodge` AS `Dodge` , `up`.`growth` AS `Growth` , `up`.`hit` AS `Hit` , `up`.`realm` AS `Realm` , `up`.`evolve_count` AS `EvolveCount` , `up`.`element` AS `Element` , `up`.`create_time` AS `CreateTime` , IFNULL(`up`.`status`,@MethodConst3) AS `Status` , `up`.`is_main` AS `IsMain`  FROM `user_pet` `up` Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )   WHERE ( `up`.`user_id` = @user_id0 )ORDER BY `up`.`create_time` DESC 
[2025-09-06 11:58:40.780 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=35, @MethodConst1=]
[2025-09-06 11:58:40.814 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:58:40.816 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=914500, @is_active1=1]
[2025-09-06 11:58:40.851 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:58:40.857 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=34, @MethodConst1=]
[2025-09-06 11:58:40.893 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:58:40.896 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=914500, @is_active1=1]
[2025-09-06 11:58:40.941 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:58:40.943 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=33, @MethodConst1=]
[2025-09-06 11:58:40.977 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:58:40.980 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=976000, @is_active1=1]
[2025-09-06 11:58:41.018 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:58:41.020 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=32, @MethodConst1=]
[2025-09-06 11:58:41.053 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:58:41.061 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=914500, @is_active1=1]
[2025-09-06 11:58:41.100 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:58:41.108 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=31, @MethodConst1=]
[2025-09-06 11:58:41.144 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:58:41.146 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=688500, @is_active1=1]
[2025-09-06 11:58:41.179 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:58:41.182 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=30, @MethodConst1=]
[2025-09-06 11:58:41.216 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:58:41.219 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=637000, @is_active1=1]
[2025-09-06 11:58:41.256 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:58:41.259 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=24, @MethodConst1=]
[2025-09-06 11:58:41.304 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:58:41.309 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=10, @is_active1=1]
[2025-09-06 11:58:41.344 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:58:41.348 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=23, @MethodConst1=]
[2025-09-06 11:58:41.383 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:58:41.389 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=637000, @is_active1=1]
[2025-09-06 11:58:41.431 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:58:41.433 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=22, @MethodConst1=]
[2025-09-06 11:58:41.492 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:58:41.496 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=914500, @is_active1=1]
[2025-09-06 11:58:41.534 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:58:41.542 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=13, @MethodConst1=]
[2025-09-06 11:58:41.578 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:58:41.581 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=914500, @is_active1=1]
[2025-09-06 11:58:41.615 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:58:41.618 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=11, @MethodConst1=]
[2025-09-06 11:58:41.656 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:58:41.659 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=914500, @is_active1=1]
[2025-09-06 11:58:41.693 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:58:41.696 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=19, @MethodConst1=]
[2025-09-06 11:58:41.795 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:58:41.798 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=451000, @is_active1=1]
[2025-09-06 11:58:41.840 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:58:41.852 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=16, @MethodConst1=]
[2025-09-06 11:58:41.889 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:58:41.893 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=451000, @is_active1=1]
[2025-09-06 11:58:41.929 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:58:41.932 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=18, @MethodConst1=]
[2025-09-06 11:58:41.967 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:58:41.977 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=451000, @is_active1=1]
[2025-09-06 11:58:42.016 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:58:42.019 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`user_id`,`pet_no`,`image`,`exp`,`hp`,`mp`,`atk`,`def`,`spd`,`state`,`dodge`,`name`,`growth`,`hit`,`deepen`,`offset`,`vamp`,`vamp_mp`,`custom_name`,`talisman_state`,`realm`,`evolve_count`,`synthesis_count`,`nirvana_count`,`last_evolution_time`,`last_synthesis_time`,`last_nirvana_time`,`original_pet_no`,`parent_main_pet_id`,`parent_sub_pet_id`,`element`,`create_time`,`status`,`is_main`,`level`,`max_hp`,`max_mp`,`current_mp` FROM `user_pet`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=34]
[2025-09-06 11:58:42.055 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`user_id`,`pet_no`,`image`,`exp`,`hp`,`mp`,`atk`,`def`,`spd`,`state`,`dodge`,`name`,`growth`,`hit`,`deepen`,`offset`,`vamp`,`vamp_mp`,`custom_name`,`talisman_state`,`realm`,`evolve_count`,`synthesis_count`,`nirvana_count`,`last_evolution_time`,`last_synthesis_time`,`last_nirvana_time`,`original_pet_no`,`parent_main_pet_id`,`parent_sub_pet_id`,`element`,`create_time`,`status`,`is_main`,`level`,`max_hp`,`max_mp`,`current_mp` FROM `user_pet`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-09-06 11:58:42.060 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: UPDATE `user_pet`  SET
           `user_id`=@user_id,`pet_no`=@pet_no,`image`=@image,`exp`=@exp,`hp`=@hp,`mp`=@mp,`atk`=@atk,`def`=@def,`spd`=@spd,`state`=@state,`dodge`=@dodge,`name`=@name,`growth`=@growth,`hit`=@hit,`deepen`=@deepen,`offset`=@offset,`vamp`=@vamp,`vamp_mp`=@vamp_mp,`custom_name`=@custom_name,`talisman_state`=@talisman_state,`realm`=@realm,`evolve_count`=@evolve_count,`synthesis_count`=@synthesis_count,`nirvana_count`=@nirvana_count,`last_evolution_time`=@last_evolution_time,`last_synthesis_time`=@last_synthesis_time,`last_nirvana_time`=@last_nirvana_time,`original_pet_no`=@original_pet_no,`parent_main_pet_id`=@parent_main_pet_id,`parent_sub_pet_id`=@parent_sub_pet_id,`element`=@element,`create_time`=@create_time,`status`=@status,`is_main`=@is_main,`level`=@level,`max_hp`=@max_hp,`max_mp`=@max_mp,`current_mp`=@current_mp  WHERE `id`=@id | 参数: [@id=34, @user_id=1, @pet_no=79, @image=, @exp=1072000, @hp=133, @mp=, @atk=13, @def=13, @spd=13, @state=, @dodge=, @name=, @growth=, @hit=, @deepen=0.00, @offset=0.00, @vamp=0.00, @vamp_mp=0.00, @custom_name=, @talisman_state=, @realm=, @evolve_count=, @synthesis_count=, @nirvana_count=, @last_evolution_time=, @last_synthesis_time=, @last_nirvana_time=, @original_pet_no=, @parent_main_pet_id=, @parent_sub_pet_id=, @element=, @create_time=2025/9/6 11:57:58, @status=牧场, @is_main=False, @level=0, @max_hp=, @max_mp=, @current_mp=]
[2025-09-06 11:58:42.201 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: UPDATE `user_pet`  SET
           `user_id`=@user_id,`pet_no`=@pet_no,`image`=@image,`exp`=@exp,`hp`=@hp,`mp`=@mp,`atk`=@atk,`def`=@def,`spd`=@spd,`state`=@state,`dodge`=@dodge,`name`=@name,`growth`=@growth,`hit`=@hit,`deepen`=@deepen,`offset`=@offset,`vamp`=@vamp,`vamp_mp`=@vamp_mp,`custom_name`=@custom_name,`talisman_state`=@talisman_state,`realm`=@realm,`evolve_count`=@evolve_count,`synthesis_count`=@synthesis_count,`nirvana_count`=@nirvana_count,`last_evolution_time`=@last_evolution_time,`last_synthesis_time`=@last_synthesis_time,`last_nirvana_time`=@last_nirvana_time,`original_pet_no`=@original_pet_no,`parent_main_pet_id`=@parent_main_pet_id,`parent_sub_pet_id`=@parent_sub_pet_id,`element`=@element,`create_time`=@create_time,`status`=@status,`is_main`=@is_main,`level`=@level,`max_hp`=@max_hp,`max_mp`=@max_mp,`current_mp`=@current_mp  WHERE `id`=@id
[2025-09-06 11:58:42.210 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-06 11:58:42.214 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-06 11:58:42.216 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-06 11:58:42.250 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-06 11:58:42.251 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-06 11:58:42.256 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )   
[2025-09-06 11:58:42.297 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )   
[2025-09-06 11:58:42.300 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )    | 参数: [@MethodConst0=, @MethodConst1=, @MethodConst2=, @MethodConst3=]
[2025-09-06 11:58:42.346 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )   
[2025-09-06 11:58:42.349 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `up`.`id` AS `Id` , `up`.`user_id` AS `UserId` , IFNULL(`u`.`username`,@MethodConst4) AS `UserName` , `up`.`pet_no` AS `PetNo` , IFNULL(`pc`.`name`,@MethodConst5) AS `PetName` , IFNULL(`pc`.`attribute`,@MethodConst6) AS `PetAttribute` , `up`.`image` AS `Image` , `up`.`exp` AS `Exp` , `up`.`hp` AS `Hp` , `up`.`mp` AS `Mp` , `up`.`atk` AS `Atk` , `up`.`def` AS `Def` , `up`.`spd` AS `Spd` , `up`.`state` AS `State` , `up`.`dodge` AS `Dodge` , `up`.`growth` AS `Growth` , `up`.`hit` AS `Hit` , `up`.`realm` AS `Realm` , `up`.`evolve_count` AS `EvolveCount` , `up`.`element` AS `Element` , `up`.`create_time` AS `CreateTime` , IFNULL(`up`.`status`,@MethodConst7) AS `Status` , `up`.`is_main` AS `IsMain`  FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )     ORDER BY `up`.`create_time` DESC LIMIT 0,10 | 参数: [@MethodConst0=, @MethodConst1=, @MethodConst2=, @MethodConst3=, @MethodConst4=, @MethodConst5=, @MethodConst6=, @MethodConst7=]
[2025-09-06 11:58:42.388 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `up`.`id` AS `Id` , `up`.`user_id` AS `UserId` , IFNULL(`u`.`username`,@MethodConst4) AS `UserName` , `up`.`pet_no` AS `PetNo` , IFNULL(`pc`.`name`,@MethodConst5) AS `PetName` , IFNULL(`pc`.`attribute`,@MethodConst6) AS `PetAttribute` , `up`.`image` AS `Image` , `up`.`exp` AS `Exp` , `up`.`hp` AS `Hp` , `up`.`mp` AS `Mp` , `up`.`atk` AS `Atk` , `up`.`def` AS `Def` , `up`.`spd` AS `Spd` , `up`.`state` AS `State` , `up`.`dodge` AS `Dodge` , `up`.`growth` AS `Growth` , `up`.`hit` AS `Hit` , `up`.`realm` AS `Realm` , `up`.`evolve_count` AS `EvolveCount` , `up`.`element` AS `Element` , `up`.`create_time` AS `CreateTime` , IFNULL(`up`.`status`,@MethodConst7) AS `Status` , `up`.`is_main` AS `IsMain`  FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )     ORDER BY `up`.`create_time` DESC LIMIT 0,10
[2025-09-06 11:58:42.396 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=35, @MethodConst1=]
[2025-09-06 11:58:42.430 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:58:42.431 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=914500, @is_active1=1]
[2025-09-06 11:58:42.469 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:58:42.473 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=34, @MethodConst1=]
[2025-09-06 11:58:42.516 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:58:42.523 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=1072000, @is_active1=1]
[2025-09-06 11:58:42.558 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:58:42.559 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=33, @MethodConst1=]
[2025-09-06 11:58:42.596 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:58:42.598 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=976000, @is_active1=1]
[2025-09-06 11:58:42.632 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:58:42.644 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=32, @MethodConst1=]
[2025-09-06 11:58:42.681 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:58:42.682 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=914500, @is_active1=1]
[2025-09-06 11:58:42.716 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:58:42.717 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=31, @MethodConst1=]
[2025-09-06 11:58:42.778 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:58:42.781 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=688500, @is_active1=1]
[2025-09-06 11:58:42.826 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:58:42.846 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=30, @MethodConst1=]
[2025-09-06 11:58:42.882 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:58:42.885 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=637000, @is_active1=1]
[2025-09-06 11:58:42.922 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:58:42.926 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=24, @MethodConst1=]
[2025-09-06 11:58:42.960 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:58:42.963 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=10, @is_active1=1]
[2025-09-06 11:58:42.997 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:58:43.001 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=23, @MethodConst1=]
[2025-09-06 11:58:43.045 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:58:43.048 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=637000, @is_active1=1]
[2025-09-06 11:58:43.082 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:58:43.097 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=22, @MethodConst1=]
[2025-09-06 11:58:43.132 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:58:43.137 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=914500, @is_active1=1]
[2025-09-06 11:58:43.172 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:58:43.176 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=13, @MethodConst1=]
[2025-09-06 11:58:43.209 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:58:43.212 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=914500, @is_active1=1]
[2025-09-06 11:58:43.247 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:58:52.881 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-06 11:58:52.887 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-06 11:58:52.890 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-06 11:58:52.926 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-06 11:58:52.928 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-06 11:58:52.932 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `up`.`id` AS `Id` , `up`.`user_id` AS `UserId` , IFNULL(`u`.`username`,@MethodConst5) AS `UserName` , `up`.`pet_no` AS `PetNo` , IFNULL(`pc`.`name`,@MethodConst6) AS `PetName` , IFNULL(`pc`.`attribute`,@MethodConst7) AS `PetAttribute` , `up`.`image` AS `Image` , `up`.`exp` AS `Exp` , `up`.`hp` AS `Hp` , `up`.`mp` AS `Mp` , `up`.`atk` AS `Atk` , `up`.`def` AS `Def` , `up`.`spd` AS `Spd` , `up`.`state` AS `State` , `up`.`dodge` AS `Dodge` , `up`.`growth` AS `Growth` , `up`.`hit` AS `Hit` , `up`.`realm` AS `Realm` , `up`.`evolve_count` AS `EvolveCount` , `up`.`element` AS `Element` , `up`.`create_time` AS `CreateTime` , IFNULL(`up`.`status`,@MethodConst8) AS `Status` , `up`.`is_main` AS `IsMain`  FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )    WHERE ( `up`.`id` = @id0 )   LIMIT 0,1 | 参数: [@id0=35, @MethodConst1=, @MethodConst2=, @MethodConst3=, @MethodConst4=, @MethodConst5=, @MethodConst6=, @MethodConst7=, @MethodConst8=]
[2025-09-06 11:58:52.971 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `up`.`id` AS `Id` , `up`.`user_id` AS `UserId` , IFNULL(`u`.`username`,@MethodConst5) AS `UserName` , `up`.`pet_no` AS `PetNo` , IFNULL(`pc`.`name`,@MethodConst6) AS `PetName` , IFNULL(`pc`.`attribute`,@MethodConst7) AS `PetAttribute` , `up`.`image` AS `Image` , `up`.`exp` AS `Exp` , `up`.`hp` AS `Hp` , `up`.`mp` AS `Mp` , `up`.`atk` AS `Atk` , `up`.`def` AS `Def` , `up`.`spd` AS `Spd` , `up`.`state` AS `State` , `up`.`dodge` AS `Dodge` , `up`.`growth` AS `Growth` , `up`.`hit` AS `Hit` , `up`.`realm` AS `Realm` , `up`.`evolve_count` AS `EvolveCount` , `up`.`element` AS `Element` , `up`.`create_time` AS `CreateTime` , IFNULL(`up`.`status`,@MethodConst8) AS `Status` , `up`.`is_main` AS `IsMain`  FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )    WHERE ( `up`.`id` = @id0 )   LIMIT 0,1
[2025-09-06 11:58:52.974 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=35, @MethodConst1=]
[2025-09-06 11:58:53.011 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:58:53.014 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=914500, @is_active1=1]
[2025-09-06 11:58:53.050 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:58:53.055 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `up`.`id` AS `Id` , `up`.`user_id` AS `UserId` , `up`.`pet_no` AS `PetNo` , IFNULL(`pc`.`name`,@MethodConst1) AS `PetName` , IFNULL(`pc`.`attribute`,@MethodConst2) AS `PetAttribute` , `up`.`image` AS `Image` , `up`.`exp` AS `Exp` , `up`.`hp` AS `Hp` , `up`.`mp` AS `Mp` , `up`.`atk` AS `Atk` , `up`.`def` AS `Def` , `up`.`spd` AS `Spd` , `up`.`state` AS `State` , `up`.`dodge` AS `Dodge` , `up`.`growth` AS `Growth` , `up`.`hit` AS `Hit` , `up`.`realm` AS `Realm` , `up`.`evolve_count` AS `EvolveCount` , `up`.`element` AS `Element` , `up`.`create_time` AS `CreateTime` , IFNULL(`up`.`status`,@MethodConst3) AS `Status` , `up`.`is_main` AS `IsMain`  FROM `user_pet` `up` Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )   WHERE ( `up`.`user_id` = @user_id0 )ORDER BY `up`.`create_time` DESC  | 参数: [@user_id0=1, @MethodConst1=, @MethodConst2=, @MethodConst3=]
[2025-09-06 11:58:53.096 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `up`.`id` AS `Id` , `up`.`user_id` AS `UserId` , `up`.`pet_no` AS `PetNo` , IFNULL(`pc`.`name`,@MethodConst1) AS `PetName` , IFNULL(`pc`.`attribute`,@MethodConst2) AS `PetAttribute` , `up`.`image` AS `Image` , `up`.`exp` AS `Exp` , `up`.`hp` AS `Hp` , `up`.`mp` AS `Mp` , `up`.`atk` AS `Atk` , `up`.`def` AS `Def` , `up`.`spd` AS `Spd` , `up`.`state` AS `State` , `up`.`dodge` AS `Dodge` , `up`.`growth` AS `Growth` , `up`.`hit` AS `Hit` , `up`.`realm` AS `Realm` , `up`.`evolve_count` AS `EvolveCount` , `up`.`element` AS `Element` , `up`.`create_time` AS `CreateTime` , IFNULL(`up`.`status`,@MethodConst3) AS `Status` , `up`.`is_main` AS `IsMain`  FROM `user_pet` `up` Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )   WHERE ( `up`.`user_id` = @user_id0 )ORDER BY `up`.`create_time` DESC 
[2025-09-06 11:58:53.098 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=35, @MethodConst1=]
[2025-09-06 11:58:53.133 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:58:53.135 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=914500, @is_active1=1]
[2025-09-06 11:58:53.174 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:58:53.177 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=34, @MethodConst1=]
[2025-09-06 11:58:53.217 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:58:53.224 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=1072000, @is_active1=1]
[2025-09-06 11:58:53.285 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:58:53.297 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=33, @MethodConst1=]
[2025-09-06 11:58:53.342 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:58:53.345 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=976000, @is_active1=1]
[2025-09-06 11:58:53.497 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:58:53.521 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=32, @MethodConst1=]
[2025-09-06 11:58:53.658 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:58:53.662 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=914500, @is_active1=1]
[2025-09-06 11:58:53.700 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:58:53.706 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=31, @MethodConst1=]
[2025-09-06 11:58:53.759 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:58:53.776 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=688500, @is_active1=1]
[2025-09-06 11:58:53.818 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:58:53.829 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=30, @MethodConst1=]
[2025-09-06 11:58:53.889 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:58:53.977 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=637000, @is_active1=1]
[2025-09-06 11:58:54.127 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:58:54.131 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=24, @MethodConst1=]
[2025-09-06 11:58:54.174 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:58:54.178 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=10, @is_active1=1]
[2025-09-06 11:58:54.213 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:58:54.218 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=23, @MethodConst1=]
[2025-09-06 11:58:54.254 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:58:54.257 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=637000, @is_active1=1]
[2025-09-06 11:58:54.294 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:58:54.298 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=22, @MethodConst1=]
[2025-09-06 11:58:54.338 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:58:54.341 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=914500, @is_active1=1]
[2025-09-06 11:58:54.375 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:58:54.378 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=13, @MethodConst1=]
[2025-09-06 11:58:54.411 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:58:54.414 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=914500, @is_active1=1]
[2025-09-06 11:58:54.457 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:58:54.461 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=11, @MethodConst1=]
[2025-09-06 11:58:54.497 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:58:54.499 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=914500, @is_active1=1]
[2025-09-06 11:58:54.534 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:58:54.536 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=19, @MethodConst1=]
[2025-09-06 11:58:54.570 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:58:54.572 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=451000, @is_active1=1]
[2025-09-06 11:58:54.605 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:58:54.607 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=16, @MethodConst1=]
[2025-09-06 11:58:54.646 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:58:54.648 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=451000, @is_active1=1]
[2025-09-06 11:58:54.706 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:58:54.711 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=18, @MethodConst1=]
[2025-09-06 11:58:54.746 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:58:54.747 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=451000, @is_active1=1]
[2025-09-06 11:58:54.782 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:58:54.785 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`user_id`,`pet_no`,`image`,`exp`,`hp`,`mp`,`atk`,`def`,`spd`,`state`,`dodge`,`name`,`growth`,`hit`,`deepen`,`offset`,`vamp`,`vamp_mp`,`custom_name`,`talisman_state`,`realm`,`evolve_count`,`synthesis_count`,`nirvana_count`,`last_evolution_time`,`last_synthesis_time`,`last_nirvana_time`,`original_pet_no`,`parent_main_pet_id`,`parent_sub_pet_id`,`element`,`create_time`,`status`,`is_main`,`level`,`max_hp`,`max_mp`,`current_mp` FROM `user_pet`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=35]
[2025-09-06 11:58:54.821 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`user_id`,`pet_no`,`image`,`exp`,`hp`,`mp`,`atk`,`def`,`spd`,`state`,`dodge`,`name`,`growth`,`hit`,`deepen`,`offset`,`vamp`,`vamp_mp`,`custom_name`,`talisman_state`,`realm`,`evolve_count`,`synthesis_count`,`nirvana_count`,`last_evolution_time`,`last_synthesis_time`,`last_nirvana_time`,`original_pet_no`,`parent_main_pet_id`,`parent_sub_pet_id`,`element`,`create_time`,`status`,`is_main`,`level`,`max_hp`,`max_mp`,`current_mp` FROM `user_pet`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-09-06 11:58:54.824 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: UPDATE `user_pet`  SET
           `user_id`=@user_id,`pet_no`=@pet_no,`image`=@image,`exp`=@exp,`hp`=@hp,`mp`=@mp,`atk`=@atk,`def`=@def,`spd`=@spd,`state`=@state,`dodge`=@dodge,`name`=@name,`growth`=@growth,`hit`=@hit,`deepen`=@deepen,`offset`=@offset,`vamp`=@vamp,`vamp_mp`=@vamp_mp,`custom_name`=@custom_name,`talisman_state`=@talisman_state,`realm`=@realm,`evolve_count`=@evolve_count,`synthesis_count`=@synthesis_count,`nirvana_count`=@nirvana_count,`last_evolution_time`=@last_evolution_time,`last_synthesis_time`=@last_synthesis_time,`last_nirvana_time`=@last_nirvana_time,`original_pet_no`=@original_pet_no,`parent_main_pet_id`=@parent_main_pet_id,`parent_sub_pet_id`=@parent_sub_pet_id,`element`=@element,`create_time`=@create_time,`status`=@status,`is_main`=@is_main,`level`=@level,`max_hp`=@max_hp,`max_mp`=@max_mp,`current_mp`=@current_mp  WHERE `id`=@id | 参数: [@id=35, @user_id=1, @pet_no=79, @image=, @exp=1242000, @hp=133, @mp=, @atk=13, @def=13, @spd=13, @state=, @dodge=, @name=, @growth=, @hit=, @deepen=0.00, @offset=0.00, @vamp=0.00, @vamp_mp=0.00, @custom_name=, @talisman_state=, @realm=, @evolve_count=, @synthesis_count=, @nirvana_count=, @last_evolution_time=, @last_synthesis_time=, @last_nirvana_time=, @original_pet_no=, @parent_main_pet_id=, @parent_sub_pet_id=, @element=, @create_time=2025/9/6 11:58:03, @status=牧场, @is_main=False, @level=0, @max_hp=, @max_mp=, @current_mp=]
[2025-09-06 11:58:54.909 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: UPDATE `user_pet`  SET
           `user_id`=@user_id,`pet_no`=@pet_no,`image`=@image,`exp`=@exp,`hp`=@hp,`mp`=@mp,`atk`=@atk,`def`=@def,`spd`=@spd,`state`=@state,`dodge`=@dodge,`name`=@name,`growth`=@growth,`hit`=@hit,`deepen`=@deepen,`offset`=@offset,`vamp`=@vamp,`vamp_mp`=@vamp_mp,`custom_name`=@custom_name,`talisman_state`=@talisman_state,`realm`=@realm,`evolve_count`=@evolve_count,`synthesis_count`=@synthesis_count,`nirvana_count`=@nirvana_count,`last_evolution_time`=@last_evolution_time,`last_synthesis_time`=@last_synthesis_time,`last_nirvana_time`=@last_nirvana_time,`original_pet_no`=@original_pet_no,`parent_main_pet_id`=@parent_main_pet_id,`parent_sub_pet_id`=@parent_sub_pet_id,`element`=@element,`create_time`=@create_time,`status`=@status,`is_main`=@is_main,`level`=@level,`max_hp`=@max_hp,`max_mp`=@max_mp,`current_mp`=@current_mp  WHERE `id`=@id
[2025-09-06 11:58:54.918 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-06 11:58:54.921 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-06 11:58:54.925 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-06 11:58:54.978 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-06 11:58:54.979 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-06 11:58:54.981 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )   
[2025-09-06 11:58:55.023 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )   
[2025-09-06 11:58:55.024 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )    | 参数: [@MethodConst0=, @MethodConst1=, @MethodConst2=, @MethodConst3=]
[2025-09-06 11:58:55.058 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )   
[2025-09-06 11:58:55.059 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `up`.`id` AS `Id` , `up`.`user_id` AS `UserId` , IFNULL(`u`.`username`,@MethodConst4) AS `UserName` , `up`.`pet_no` AS `PetNo` , IFNULL(`pc`.`name`,@MethodConst5) AS `PetName` , IFNULL(`pc`.`attribute`,@MethodConst6) AS `PetAttribute` , `up`.`image` AS `Image` , `up`.`exp` AS `Exp` , `up`.`hp` AS `Hp` , `up`.`mp` AS `Mp` , `up`.`atk` AS `Atk` , `up`.`def` AS `Def` , `up`.`spd` AS `Spd` , `up`.`state` AS `State` , `up`.`dodge` AS `Dodge` , `up`.`growth` AS `Growth` , `up`.`hit` AS `Hit` , `up`.`realm` AS `Realm` , `up`.`evolve_count` AS `EvolveCount` , `up`.`element` AS `Element` , `up`.`create_time` AS `CreateTime` , IFNULL(`up`.`status`,@MethodConst7) AS `Status` , `up`.`is_main` AS `IsMain`  FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )     ORDER BY `up`.`create_time` DESC LIMIT 0,10 | 参数: [@MethodConst0=, @MethodConst1=, @MethodConst2=, @MethodConst3=, @MethodConst4=, @MethodConst5=, @MethodConst6=, @MethodConst7=]
[2025-09-06 11:58:55.095 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `up`.`id` AS `Id` , `up`.`user_id` AS `UserId` , IFNULL(`u`.`username`,@MethodConst4) AS `UserName` , `up`.`pet_no` AS `PetNo` , IFNULL(`pc`.`name`,@MethodConst5) AS `PetName` , IFNULL(`pc`.`attribute`,@MethodConst6) AS `PetAttribute` , `up`.`image` AS `Image` , `up`.`exp` AS `Exp` , `up`.`hp` AS `Hp` , `up`.`mp` AS `Mp` , `up`.`atk` AS `Atk` , `up`.`def` AS `Def` , `up`.`spd` AS `Spd` , `up`.`state` AS `State` , `up`.`dodge` AS `Dodge` , `up`.`growth` AS `Growth` , `up`.`hit` AS `Hit` , `up`.`realm` AS `Realm` , `up`.`evolve_count` AS `EvolveCount` , `up`.`element` AS `Element` , `up`.`create_time` AS `CreateTime` , IFNULL(`up`.`status`,@MethodConst7) AS `Status` , `up`.`is_main` AS `IsMain`  FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )     ORDER BY `up`.`create_time` DESC LIMIT 0,10
[2025-09-06 11:58:55.107 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=35, @MethodConst1=]
[2025-09-06 11:58:55.142 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:58:55.145 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=1242000, @is_active1=1]
[2025-09-06 11:58:55.205 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:58:55.208 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=34, @MethodConst1=]
[2025-09-06 11:58:55.244 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:58:55.247 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=1072000, @is_active1=1]
[2025-09-06 11:58:55.283 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:58:55.287 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=33, @MethodConst1=]
[2025-09-06 11:58:55.321 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:58:55.325 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=976000, @is_active1=1]
[2025-09-06 11:58:55.361 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:58:55.368 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=32, @MethodConst1=]
[2025-09-06 11:58:55.403 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:58:55.407 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=914500, @is_active1=1]
[2025-09-06 11:58:55.440 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:58:55.445 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=31, @MethodConst1=]
[2025-09-06 11:58:55.494 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:58:55.511 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=688500, @is_active1=1]
[2025-09-06 11:58:55.546 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:58:55.558 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=30, @MethodConst1=]
[2025-09-06 11:58:55.621 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:58:55.625 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=637000, @is_active1=1]
[2025-09-06 11:58:55.662 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:58:55.680 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=24, @MethodConst1=]
[2025-09-06 11:58:55.721 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:58:55.724 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=10, @is_active1=1]
[2025-09-06 11:58:55.781 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:58:55.805 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=23, @MethodConst1=]
[2025-09-06 11:58:55.842 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:58:55.844 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=637000, @is_active1=1]
[2025-09-06 11:58:55.879 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:58:55.884 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=22, @MethodConst1=]
[2025-09-06 11:58:55.931 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:58:55.937 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=914500, @is_active1=1]
[2025-09-06 11:58:55.985 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
[2025-09-06 11:58:55.992 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=13, @MethodConst1=]
[2025-09-06 11:58:56.036 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-09-06 11:58:56.039 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1 | 参数: [@required_exp0=914500, @is_active1=1]
[2025-09-06 11:58:56.077 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`level`,`required_exp`,`upgrade_exp`,`is_active`,`created_at`,`updated_at` FROM `level_config`   WHERE (( `required_exp` <= @required_exp0 ) AND ( `is_active` = @is_active1 ))  ORDER BY `level` DESC LIMIT 0,1
