<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - BMS后台管理系统</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.bootcdn.net/ajax/libs/bootstrap/4.6.0/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdn.bootcdn.net/ajax/libs/font-awesome/5.15.4/css/all.min.css" rel="stylesheet">
    <!-- Vue.js 3 -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <!-- Axios -->
    <script src="https://unpkg.com/axios/dist/axios.min.js"></script>
    
    <style>
        html, body {
            height: 100%;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .login-wrapper {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .login-box {
            width: 100%;
            max-width: 400px;
            margin: 20px;
        }
        
        .login-logo {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .login-logo a {
            color: #ffffff;
            font-size: 2rem;
            font-weight: bold;
            text-decoration: none;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }
        
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .login-card-body {
            padding: 40px 30px;
            background: rgba(255,255,255,0.95);
            backdrop-filter: blur(10px);
        }
        
        .login-box-msg {
            text-align: center;
            margin-bottom: 30px;
            color: #666;
            font-size: 1.1rem;
        }
        
        .form-control {
            border: none;
            border-bottom: 2px solid #e9ecef;
            border-radius: 0;
            padding: 15px 0;
            background: transparent;
            transition: border-color 0.3s ease;
        }
        
        .form-control:focus {
            box-shadow: none;
            border-color: #667eea;
            background: transparent;
        }
        
        .input-group-text {
            border: none;
            background: transparent;
            border-bottom: 2px solid #e9ecef;
            border-radius: 0;
            color: #666;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 25px;
            padding: 12px 30px;
            font-weight: bold;
            transition: transform 0.3s ease;
        }
        
        .btn-primary:hover {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
            transform: translateY(-2px);
        }
        
        .icheck-primary label {
            color: #666;
        }
        
        .alert-danger {
            border: none;
            border-radius: 10px;
            background: rgba(220, 53, 69, 0.1);
            border-left: 4px solid #dc3545;
        }
    </style>
</head>
<body>
    <div class="login-wrapper">
        @RenderBody()
    </div>

    <!-- jQuery -->
    <script src="https://cdn.bootcdn.net/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
    <!-- Bootstrap 4 -->
    <script src="https://cdn.bootcdn.net/ajax/libs/bootstrap/4.6.0/js/bootstrap.bundle.min.js"></script>
    
    @await RenderSectionAsync("Scripts", required: false)
</body>
</html> 