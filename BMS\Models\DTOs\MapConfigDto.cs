using System.ComponentModel.DataAnnotations;

namespace BMS.Models.DTOs
{
    /// <summary>
    /// 地图配置DTO
    /// </summary>
    public class MapConfigDto
    {
        /// <summary>
        /// 自增主键
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 地图ID
        /// </summary>
        public int MapId { get; set; }

        /// <summary>
        /// 地图名称
        /// </summary>
        public string MapName { get; set; } = string.Empty;

        /// <summary>
        /// 地图描述
        /// </summary>
        public string? MapDesc { get; set; }

        /// <summary>
        /// 地图大小
        /// </summary>
        public int MapSize { get; set; }

        /// <summary>
        /// 地图类型
        /// </summary>
        public int MapType { get; set; }

        /// <summary>
        /// 图集名称
        /// </summary>
        public string? AtlastName { get; set; }

        /// <summary>
        /// 地图背景图片
        /// </summary>
        public string? Background { get; set; }

        /// <summary>
        /// 地图背景音乐
        /// </summary>
        public string? Bgm { get; set; }

        /// <summary>
        /// 背景音乐是否循环
        /// </summary>
        public bool BgmLoop { get; set; }

        /// <summary>
        /// 背景音乐音量
        /// </summary>
        public decimal BgmVolume { get; set; } = 1.00m;

        /// <summary>
        /// 是否播放背景音乐
        /// </summary>
        public bool BgmPlay { get; set; } = true;

        /// <summary>
        /// 是否静音
        /// </summary>
        public bool BgmMute { get; set; }

        /// <summary>
        /// 是否暂停
        /// </summary>
        public bool BgmPause { get; set; }

        /// <summary>
        /// 地图图标
        /// </summary>
        public string? Ico { get; set; }

        /// <summary>
        /// 类型（备用字段）
        /// </summary>
        public int Type { get; set; }
    }

    /// <summary>
    /// 地图配置查询DTO
    /// </summary>
    public class MapConfigQueryDto
    {
        /// <summary>
        /// 地图ID
        /// </summary>
        public int? MapId { get; set; }

        /// <summary>
        /// 地图名称（支持模糊查询）
        /// </summary>
        public string? MapName { get; set; }

        /// <summary>
        /// 图集名称（支持模糊查询）
        /// </summary>
        public string? AtlastName { get; set; }

        /// <summary>
        /// 地图类型
        /// </summary>
        public int? MapType { get; set; }

        /// <summary>
        /// 页码
        /// </summary>
        public int Page { get; set; } = 1;

        /// <summary>
        /// 每页数量
        /// </summary>
        public int PageSize { get; set; } = 10;
    }

    /// <summary>
    /// 创建地图配置DTO
    /// </summary>
    public class MapConfigCreateDto
    {
        /// <summary>
        /// 地图ID
        /// </summary>
        [Required(ErrorMessage = "地图ID不能为空")]
        [Range(1, int.MaxValue, ErrorMessage = "地图ID必须大于0")]
        public int MapId { get; set; }

        /// <summary>
        /// 地图名称
        /// </summary>
        [Required(ErrorMessage = "地图名称不能为空")]
        [StringLength(50, ErrorMessage = "地图名称长度不能超过50个字符")]
        public string MapName { get; set; } = string.Empty;

        /// <summary>
        /// 地图描述
        /// </summary>
        [StringLength(255, ErrorMessage = "地图描述长度不能超过255个字符")]
        public string? MapDesc { get; set; }

        /// <summary>
        /// 地图大小
        /// </summary>
        [Range(0, int.MaxValue, ErrorMessage = "地图大小不能为负数")]
        public int MapSize { get; set; }

        /// <summary>
        /// 地图类型
        /// </summary>
        [Range(0, int.MaxValue, ErrorMessage = "地图类型不能为负数")]
        public int MapType { get; set; }

        /// <summary>
        /// 图集名称
        /// </summary>
        [StringLength(100, ErrorMessage = "图集名称长度不能超过100个字符")]
        public string? AtlastName { get; set; }

        /// <summary>
        /// 地图背景图片
        /// </summary>
        [StringLength(100, ErrorMessage = "背景图片路径长度不能超过100个字符")]
        public string? Background { get; set; }

        /// <summary>
        /// 地图背景音乐
        /// </summary>
        [StringLength(100, ErrorMessage = "背景音乐路径长度不能超过100个字符")]
        public string? Bgm { get; set; }

        /// <summary>
        /// 背景音乐是否循环
        /// </summary>
        public bool BgmLoop { get; set; }

        /// <summary>
        /// 背景音乐音量
        /// </summary>
        [Range(0.00, 1.00, ErrorMessage = "音量范围必须在0.00-1.00之间")]
        public decimal BgmVolume { get; set; } = 1.00m;

        /// <summary>
        /// 是否播放背景音乐
        /// </summary>
        public bool BgmPlay { get; set; } = true;

        /// <summary>
        /// 是否静音
        /// </summary>
        public bool BgmMute { get; set; }

        /// <summary>
        /// 是否暂停
        /// </summary>
        public bool BgmPause { get; set; }

        /// <summary>
        /// 地图图标
        /// </summary>
        [StringLength(100, ErrorMessage = "地图图标路径长度不能超过100个字符")]
        public string? Ico { get; set; }

        /// <summary>
        /// 类型（备用字段）
        /// </summary>
        [Range(0, int.MaxValue, ErrorMessage = "类型不能为负数")]
        public int Type { get; set; }
    }

    /// <summary>
    /// 更新地图配置DTO
    /// </summary>
    public class MapConfigUpdateDto
    {
        /// <summary>
        /// 自增主键
        /// </summary>
        [Required(ErrorMessage = "ID不能为空")]
        public int Id { get; set; }

        /// <summary>
        /// 地图ID
        /// </summary>
        [Required(ErrorMessage = "地图ID不能为空")]
        [Range(1, int.MaxValue, ErrorMessage = "地图ID必须大于0")]
        public int MapId { get; set; }

        /// <summary>
        /// 地图名称
        /// </summary>
        [Required(ErrorMessage = "地图名称不能为空")]
        [StringLength(50, ErrorMessage = "地图名称长度不能超过50个字符")]
        public string MapName { get; set; } = string.Empty;

        /// <summary>
        /// 地图描述
        /// </summary>
        [StringLength(255, ErrorMessage = "地图描述长度不能超过255个字符")]
        public string? MapDesc { get; set; }

        /// <summary>
        /// 地图大小
        /// </summary>
        [Range(0, int.MaxValue, ErrorMessage = "地图大小不能为负数")]
        public int MapSize { get; set; }

        /// <summary>
        /// 地图类型
        /// </summary>
        [Range(0, int.MaxValue, ErrorMessage = "地图类型不能为负数")]
        public int MapType { get; set; }

        /// <summary>
        /// 图集名称
        /// </summary>
        [StringLength(100, ErrorMessage = "图集名称长度不能超过100个字符")]
        public string? AtlastName { get; set; }

        /// <summary>
        /// 地图背景图片
        /// </summary>
        [StringLength(100, ErrorMessage = "背景图片路径长度不能超过100个字符")]
        public string? Background { get; set; }

        /// <summary>
        /// 地图背景音乐
        /// </summary>
        [StringLength(100, ErrorMessage = "背景音乐路径长度不能超过100个字符")]
        public string? Bgm { get; set; }

        /// <summary>
        /// 背景音乐是否循环
        /// </summary>
        public bool BgmLoop { get; set; }

        /// <summary>
        /// 背景音乐音量
        /// </summary>
        [Range(0.00, 1.00, ErrorMessage = "音量范围必须在0.00-1.00之间")]
        public decimal BgmVolume { get; set; } = 1.00m;

        /// <summary>
        /// 是否播放背景音乐
        /// </summary>
        public bool BgmPlay { get; set; } = true;

        /// <summary>
        /// 是否静音
        /// </summary>
        public bool BgmMute { get; set; }

        /// <summary>
        /// 是否暂停
        /// </summary>
        public bool BgmPause { get; set; }

        /// <summary>
        /// 地图图标
        /// </summary>
        [StringLength(100, ErrorMessage = "地图图标路径长度不能超过100个字符")]
        public string? Ico { get; set; }

        /// <summary>
        /// 类型（备用字段）
        /// </summary>
        [Range(0, int.MaxValue, ErrorMessage = "类型不能为负数")]
        public int Type { get; set; }
    }

    /// <summary>
    /// 删除地图配置DTO
    /// </summary>
    public class MapConfigDeleteDto
    {
        /// <summary>
        /// 地图配置ID
        /// </summary>
        [Required(ErrorMessage = "ID不能为空")]
        public int Id { get; set; }
    }
} 