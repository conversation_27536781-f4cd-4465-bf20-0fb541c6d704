﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace BMS.Models.Entities
{
    ///<summary>
    ///反作弊日志表
    ///</summary>
    [SugarTable("anti_cheat_logs")]
    public partial class anti_cheat_logs
    {
           public anti_cheat_logs(){


           }
           /// <summary>
           /// Desc:自增主键
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true,IsIdentity=true)]
           public int id {get;set;}

           /// <summary>
           /// Desc:用户ID
           /// Default:
           /// Nullable:False
           /// </summary>           
           public int user_id {get;set;}

           /// <summary>
           /// Desc:作弊类型
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string cheat_type {get;set;}

           /// <summary>
           /// Desc:检测数据(JSON格式)
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(IsJson=true)]           
           public object detection_data {get;set;}

           /// <summary>
           /// Desc:风险评分
           /// Default:0.00
           /// Nullable:True
           /// </summary>           
           public decimal? risk_score {get;set;}

           /// <summary>
           /// Desc:处理动作
           /// Default:NONE
           /// Nullable:True
           /// </summary>           
           public string? action_taken {get;set;}

           /// <summary>
           /// Desc:创建时间
           /// Default:CURRENT_TIMESTAMP
           /// Nullable:True
           /// </summary>           
           public DateTime? create_time {get;set;}

    }
}
