namespace BMS.Models.DTOs
{
    /// <summary>
    /// 查询管理员DTO
    /// </summary>
    public class AdminBmQueryDto
    {
        /// <summary>
        /// 用户名（模糊查询）
        /// </summary>
        public string? Username { get; set; }

        /// <summary>
        /// 真实姓名（模糊查询）
        /// </summary>
        public string? RealName { get; set; }

        /// <summary>
        /// 手机号（模糊查询）
        /// </summary>
        public string? Phone { get; set; }

        /// <summary>
        /// 状态筛选：null-全部，0-禁用，1-启用
        /// </summary>
        public int? Status { get; set; }

        /// <summary>
        /// 创建时间开始
        /// </summary>
        public DateTime? CreateTimeStart { get; set; }

        /// <summary>
        /// 创建时间结束
        /// </summary>
        public DateTime? CreateTimeEnd { get; set; }

        /// <summary>
        /// 当前页码
        /// </summary>
        public int PageIndex { get; set; } = 1;

        /// <summary>
        /// 每页数量
        /// </summary>
        public int PageSize { get; set; } = 20;
    }
} 