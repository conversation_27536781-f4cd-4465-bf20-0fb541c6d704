using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Authorization;
using System.Security.Claims;
using BMS.Services.Interfaces;
using BMS.Models.DTOs;
using BMS.Models.Common;

namespace BMS.Controllers
{
    /// <summary>
    /// 认证控制器
    /// </summary>
    public class AuthController : Controller
    {
        private readonly IAuthService _authService;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="authService">认证服务</param>
        public AuthController(IAuthService authService)
        {
            _authService = authService;
        }

        /// <summary>
        /// 登录页面
        /// </summary>
        /// <returns>登录视图</returns>
        [HttpGet]
        [AllowAnonymous]
        public IActionResult Login()
        {
            // 如果已登录，跳转到首页
            if (User.Identity?.IsAuthenticated == true)
            {
                return RedirectToAction("Index", "Home");
            }

            return View();
        }

        /// <summary>
        /// 登录处理
        /// </summary>
        /// <param name="loginDto">登录信息</param>
        /// <returns>登录结果</returns>
        [HttpPost]
        [AllowAnonymous]
        public async Task<IActionResult> Login([FromBody] LoginDto loginDto)
        {
            if (!ModelState.IsValid)
            {
                var errors = ModelState.Values
                    .SelectMany(v => v.Errors)
                    .Select(e => e.ErrorMessage)
                    .ToList();
                return Json(ApiResult.Fail(string.Join("; ", errors)));
            }

            var result = await _authService.LoginAsync(loginDto);
            if (result.Success && result.Data != null)
            {
                // 创建用户身份信息
                var claims = new List<Claim>
                {
                    new(ClaimTypes.NameIdentifier, result.Data.id.ToString()),
                    new(ClaimTypes.Name, result.Data.username),
                    new("RealName", result.Data.real_name ?? ""),
                    new("Email", result.Data.email ?? "")
                };

                var claimsIdentity = new ClaimsIdentity(claims, CookieAuthenticationDefaults.AuthenticationScheme);
                var authProperties = new AuthenticationProperties
                {
                    IsPersistent = loginDto.RememberMe,
                    ExpiresUtc = loginDto.RememberMe 
                        ? DateTimeOffset.UtcNow.AddDays(7) 
                        : DateTimeOffset.UtcNow.AddMinutes(30)
                };

                await HttpContext.SignInAsync(
                    CookieAuthenticationDefaults.AuthenticationScheme,
                    new ClaimsPrincipal(claimsIdentity),
                    authProperties);

                return Json(ApiResult.Ok("登录成功"));
            }

            return Json(result);
        }

        /// <summary>
        /// 登出
        /// </summary>
        /// <returns>登出结果或重定向</returns>
        [HttpGet, HttpPost]
        [Authorize]
        public async Task<IActionResult> Logout()
        {
            await HttpContext.SignOutAsync(CookieAuthenticationDefaults.AuthenticationScheme);
            
            // 判断请求类型
            if (HttpContext.Request.Method == "GET")
            {
                // GET请求，重定向到登录页面
                return RedirectToAction("Login");
            }
            
            // POST请求，返回JSON
            return Json(ApiResult.Ok("退出成功"));
        }

        /// <summary>
        /// 修改密码页面
        /// </summary>
        /// <returns>修改密码视图</returns>
        [HttpGet]
        [Authorize]
        public IActionResult ChangePassword()
        {
            return View();
        }

        /// <summary>
        /// 修改密码处理
        /// </summary>
        /// <param name="changePasswordDto">修改密码信息</param>
        /// <returns>修改结果</returns>
        [HttpPost]
        [Authorize]
        public async Task<IActionResult> ChangePassword([FromBody] ChangePasswordDto changePasswordDto)
        {
            if (!ModelState.IsValid)
            {
                var errors = ModelState.Values
                    .SelectMany(v => v.Errors)
                    .Select(e => e.ErrorMessage)
                    .ToList();
                return Json(ApiResult.Fail(string.Join("; ", errors)));
            }

            var adminIdClaim = User.FindFirst(ClaimTypes.NameIdentifier);
            if (adminIdClaim == null || !int.TryParse(adminIdClaim.Value, out int adminId))
            {
                return Json(ApiResult.Fail("用户身份验证失败"));
            }

            var result = await _authService.ChangePasswordAsync(adminId, changePasswordDto);
            return Json(result);
        }

        /// <summary>
        /// 获取当前用户信息
        /// </summary>
        /// <returns>用户信息</returns>
        [HttpGet]
        [Authorize]
        public async Task<IActionResult> GetCurrentUser()
        {
            var adminIdClaim = User.FindFirst(ClaimTypes.NameIdentifier);
            if (adminIdClaim == null || !int.TryParse(adminIdClaim.Value, out int adminId))
            {
                return Json(ApiResult<object>.Fail("用户身份验证失败"));
            }

            var admin = await _authService.GetAdminByIdAsync(adminId);
            if (admin == null)
            {
                return Json(ApiResult<object>.Fail("用户不存在"));
            }

            // 清除敏感信息
            admin.password = string.Empty;

            return Json(ApiResult<object>.Ok(admin));
        }
    }
} 