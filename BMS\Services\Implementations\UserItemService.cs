using BMS.Models.Common;
using BMS.Models.DTOs;
using BMS.Models.Entities;
using BMS.Services.Interfaces;
using SqlSugar;

namespace BMS.Services.Implementations
{
    /// <summary>
    /// 用户道具服务实现
    /// </summary>
    public class UserItemService : IUserItemService
    {
        private readonly IDbService _dbService;

        public UserItemService(IDbService dbService)
        {
            _dbService = dbService;
        }

        /// <summary>
        /// 分页查询用户道具列表
        /// </summary>
        /// <param name="queryDto">查询条件</param>
        /// <returns>分页结果</returns>
        public async Task<PagedResult<UserItemDto>> GetUserItemListAsync(UserItemQueryDto queryDto)
        {
            try
            {
                var query = _dbService.GetClient().Queryable<user_item, user, item_config>((ui, u, ic) => new object[]
                {
                    JoinType.Left, ui.user_id == u.id,
                    JoinType.Left, ui.item_id == ic.item_no.ToString()
                });

                // 应用查询条件
                if (queryDto.UserId.HasValue)
                {
                    query = query.Where((ui, u, ic) => ui.user_id == queryDto.UserId.Value);
                }

                if (!string.IsNullOrWhiteSpace(queryDto.Username))
                {
                    query = query.Where((ui, u, ic) => u.username.Contains(queryDto.Username));
                }

                if (!string.IsNullOrWhiteSpace(queryDto.ItemName))
                {
                    query = query.Where((ui, u, ic) => ic.name.Contains(queryDto.ItemName));
                }

                if (!string.IsNullOrWhiteSpace(queryDto.ItemType))
                {
                    query = query.Where((ui, u, ic) => ic.type == queryDto.ItemType);
                }

                if (!string.IsNullOrWhiteSpace(queryDto.ItemQuality))
                {
                    query = query.Where((ui, u, ic) => ic.quality == queryDto.ItemQuality);
                }

                // 获取总数
                var totalCount = await query.CountAsync();

                // 分页查询
                var data = await query
                    .OrderByDescending((ui, u, ic) => ui.id)
                    .Select((ui, u, ic) => new UserItemDto
                    {
                        Id = ui.id,
                        UserId = ui.user_id,
                        Username = u.username ?? "",
                        ItemId = ui.item_id,
                        ItemNo = SqlFunc.ToInt32(ui.item_id),
                        ItemName = ic.name ?? "",
                        ItemType = ic.type,
                        ItemQuality = ic.quality,
                        ItemCount = ui.item_count,
                        ItemPos = ui.item_pos,
                        ItemSeq = ui.item_seq,
                        ItemDescription = ic.description,
                        ItemPrice = ic.price,
                        ItemIcon = ic.icon
                    })
                    .Skip((queryDto.Page - 1) * queryDto.PageSize)
                    .Take(queryDto.PageSize)
                    .ToListAsync();

                return new PagedResult<UserItemDto>(
                    data,
                    queryDto.Page,
                    queryDto.PageSize,
                    totalCount
                );
            }
            catch (Exception ex)
            {
                throw new Exception($"查询用户道具列表失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 根据ID获取用户道具
        /// </summary>
        /// <param name="id">记录ID</param>
        /// <returns>用户道具信息</returns>
        public async Task<UserItemDto?> GetUserItemByIdAsync(int id)
        {
            try
            {
                var result = await _dbService.GetClient().Queryable<user_item, user, item_config>((ui, u, ic) => new object[]
                {
                    JoinType.Left, ui.user_id == u.id,
                    JoinType.Left, ui.item_id == ic.item_no.ToString()
                })
                .Where((ui, u, ic) => ui.id == id)
                .Select((ui, u, ic) => new UserItemDto
                {
                    Id = ui.id,
                    UserId = ui.user_id,
                    Username = u.username ?? "",
                    ItemId = ui.item_id,
                    ItemNo = SqlFunc.ToInt32(ui.item_id),
                    ItemName = ic.name ?? "",
                    ItemType = ic.type,
                    ItemQuality = ic.quality,
                    ItemCount = ui.item_count,
                    ItemPos = ui.item_pos,
                    ItemSeq = ui.item_seq,
                    ItemDescription = ic.description,
                    ItemPrice = ic.price,
                    ItemIcon = ic.icon
                })
                .FirstAsync();

                return result;
            }
            catch (Exception ex)
            {
                throw new Exception($"获取用户道具失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 添加用户道具
        /// </summary>
        /// <param name="addDto">添加信息</param>
        /// <returns>是否成功</returns>
        public async Task<bool> AddUserItemAsync(UserItemAddDto addDto)
        {
            try
            {
                // 验证用户是否存在
                var userExists = await _dbService.Queryable<user>().AnyAsync(x => x.id == addDto.UserId);
                if (!userExists)
                {
                    throw new Exception("用户不存在");
                }

                // 验证道具是否存在
                var itemExists = await _dbService.Queryable<item_config>().AnyAsync(x => x.item_no == addDto.ItemNo);
                if (!itemExists)
                {
                    throw new Exception("道具不存在");
                }

                // 检查用户是否已有该道具
                var existingItem = await _dbService.Queryable<user_item>()
                    .FirstAsync(x => x.user_id == addDto.UserId && x.item_id == addDto.ItemNo.ToString());

                if (existingItem != null)
                {
                    // 如果已存在，则增加数量
                    existingItem.item_count += addDto.ItemCount;
                    existingItem.item_pos = addDto.ItemPos ?? existingItem.item_pos;
                    
                    return await _dbService.Updateable(existingItem).ExecuteCommandHasChangeAsync();
                }
                else
                {
                    // 获取下一个序号
                    var maxSeq = await _dbService.Queryable<user_item>()
                        .Where(x => x.user_id == addDto.UserId)
                        .MaxAsync(x => x.item_seq);

                    var newItem = new user_item
                    {
                        user_id = addDto.UserId,
                        item_id = addDto.ItemNo.ToString(),
                        item_count = addDto.ItemCount,
                        item_pos = addDto.ItemPos ?? 1, // 默认位置为1（背包）
                        item_seq = maxSeq + 1
                    };

                    var result = await _dbService.Insertable(newItem).ExecuteCommandIdentityIntoEntityAsync();
                    return result != null;
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"添加用户道具失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 更新用户道具
        /// </summary>
        /// <param name="updateDto">更新信息</param>
        /// <returns>是否成功</returns>
        public async Task<bool> UpdateUserItemAsync(UserItemUpdateDto updateDto)
        {
            try
            {
                var existing = await _dbService.Queryable<user_item>().FirstAsync(x => x.id == updateDto.Id);
                if (existing == null)
                {
                    throw new Exception("道具记录不存在");
                }

                existing.item_count = updateDto.ItemCount;
                existing.item_pos = updateDto.ItemPos ?? existing.item_pos; // 如果为空则保持原值

                return await _dbService.Updateable(existing).ExecuteCommandHasChangeAsync();
            }
            catch (Exception ex)
            {
                throw new Exception($"更新用户道具失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 删除用户道具
        /// </summary>
        /// <param name="deleteDto">删除信息</param>
        /// <returns>是否成功</returns>
        public async Task<bool> DeleteUserItemAsync(UserItemDeleteDto deleteDto)
        {
            try
            {
                return await _dbService.Deleteable<user_item>().Where(x => x.id == deleteDto.Id).ExecuteCommandHasChangeAsync();
            }
            catch (Exception ex)
            {
                throw new Exception($"删除用户道具失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 获取用户道具统计信息
        /// </summary>
        /// <returns>统计信息</returns>
        public async Task<UserItemStatisticsDto> GetUserItemStatisticsAsync()
        {
            try
            {
                // 道具总数量
                var totalItems = await _dbService.Queryable<user_item>().SumAsync(x => x.item_count);

                // 道具种类数
                var itemTypes = await _dbService.Queryable<user_item>()
                    .GroupBy(x => x.item_id)
                    .CountAsync();

                // 各品质道具统计
                var qualityStats = await _dbService.GetClient().Queryable<user_item, item_config>((ui, ic) => new object[]
                {
                    JoinType.Left, ui.item_id == ic.item_no.ToString()
                })
                .GroupBy((ui, ic) => ic.quality)
                .Select((ui, ic) => new
                {
                    Quality = ic.quality,
                    Count = SqlFunc.AggregateSum(ui.item_count)
                })
                .ToListAsync();

                // 道具总价值
                var totalValue = await _dbService.GetClient().Queryable<user_item, item_config>((ui, ic) => new object[]
                {
                    JoinType.Left, ui.item_id == ic.item_no.ToString()
                })
                .SumAsync((ui, ic) => ui.item_count * (ic.price ?? 0));

                var statistics = new UserItemStatisticsDto
                {
                    TotalItems = totalItems,
                    ItemTypes = itemTypes,
                    TotalValue = totalValue
                };

                // 设置各品质道具数量
                foreach (var stat in qualityStats)
                {
                    switch (stat.Quality)
                    {
                        case "white":
                            statistics.WhiteItems = stat.Count;
                            break;
                        case "green":
                            statistics.GreenItems = stat.Count;
                            break;
                        case "blue":
                            statistics.BlueItems = stat.Count;
                            break;
                        case "purple":
                            statistics.PurpleItems = stat.Count;
                            break;
                        case "orange":
                            statistics.OrangeItems = stat.Count;
                            break;
                    }
                }

                return statistics;
            }
            catch (Exception ex)
            {
                throw new Exception($"获取用户道具统计信息失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 获取所有道具配置
        /// </summary>
        /// <returns>道具配置列表</returns>
        public async Task<List<ItemConfigDto>> GetAllItemConfigsAsync()
        {
            try
            {
                var configs = await _dbService.Queryable<item_config>()
                    .OrderBy(x => x.item_no)
                    .Select(x => new ItemConfigDto
                    {
                        Id = x.id,
                        ItemNo = x.item_no,
                        Name = x.name,
                        Type = x.type,
                        Description = x.description,
                        Quality = x.quality,
                        Icon = x.icon,
                        Price = x.price,
                        UseLimit = x.use_limit,
                        CreateTime = x.create_time
                    })
                    .ToListAsync();

                return configs;
            }
            catch (Exception ex)
            {
                throw new Exception($"获取道具配置失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 验证道具是否存在
        /// </summary>
        /// <param name="itemNo">道具编号</param>
        /// <returns>是否存在</returns>
        public async Task<bool> IsItemExistAsync(int itemNo)
        {
            try
            {
                return await _dbService.Queryable<item_config>().AnyAsync(x => x.item_no == itemNo);
            }
            catch (Exception ex)
            {
                throw new Exception($"验证道具是否存在失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 批量删除用户道具
        /// </summary>
        /// <param name="ids">记录ID列表</param>
        /// <returns>是否成功</returns>
        public async Task<bool> BatchDeleteUserItemsAsync(List<int> ids)
        {
            try
            {
                return await _dbService.Deleteable<user_item>().Where(x => ids.Contains(x.id)).ExecuteCommandHasChangeAsync();
            }
            catch (Exception ex)
            {
                throw new Exception($"批量删除用户道具失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 根据用户ID获取道具列表
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>道具列表</returns>
        public async Task<List<UserItemDto>> GetUserItemsByUserIdAsync(int userId)
        {
            try
            {
                var result = await _dbService.GetClient().Queryable<user_item, user, item_config>((ui, u, ic) => new object[]
                {
                    JoinType.Left, ui.user_id == u.id,
                    JoinType.Left, ui.item_id == ic.item_no.ToString()
                })
                .Where((ui, u, ic) => ui.user_id == userId)
                .OrderBy((ui, u, ic) => ui.item_seq)
                .Select((ui, u, ic) => new UserItemDto
                {
                    Id = ui.id,
                    UserId = ui.user_id,
                    Username = u.username ?? "",
                    ItemId = ui.item_id,
                    ItemNo = SqlFunc.ToInt32(ui.item_id),
                    ItemName = ic.name ?? "",
                    ItemType = ic.type,
                    ItemQuality = ic.quality,
                    ItemCount = ui.item_count,
                    ItemPos = ui.item_pos,
                    ItemSeq = ui.item_seq,
                    ItemDescription = ic.description,
                    ItemPrice = ic.price,
                    ItemIcon = ic.icon
                })
                .ToListAsync();

                return result;
            }
            catch (Exception ex)
            {
                throw new Exception($"获取用户道具列表失败: {ex.Message}", ex);
            }
        }
    }
} 