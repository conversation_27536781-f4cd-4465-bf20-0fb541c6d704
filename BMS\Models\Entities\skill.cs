﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace BMS.Models.Entities
{
    ///<summary>
    ///技能数据表
    ///</summary>
    [SugarTable("skill")]
    public partial class skill
    {
           public skill(){


           }
           /// <summary>
           /// Desc:技能唯一标识
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true)]
           public string skill_id {get;set;}

           /// <summary>
           /// Desc:技能名称
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string skill_name {get;set;}

           /// <summary>
           /// Desc:技能百分比（支持两位小数）
           /// Default:0.00
           /// Nullable:False
           /// </summary>           
           public decimal skill_percent {get;set;}

           /// <summary>
           /// Desc:技能效果类型
           /// Default:
           /// Nullable:True
           /// </summary>           
           public object effect_type {get;set;}

           /// <summary>
           /// Desc:效果数值（JSON格式存储不同效果的具体数值）
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(IsJson=true)]           
           public object effect_value {get;set;}

           /// <summary>
           /// Desc:耗蓝量
           /// Default:0
           /// Nullable:False
           /// </summary>           
           public int mana_cost {get;set;}

           /// <summary>
           /// Desc:BUFF信息（包含类型、持续回合、数值等）
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(IsJson=true)]           
           public object buff_info {get;set;}

           /// <summary>
           /// Desc:五行限制
           /// Default:
           /// Nullable:True
           /// </summary>           
           public object element_limit {get;set;}

           /// <summary>
           /// Desc:创建时间
           /// Default:CURRENT_TIMESTAMP
           /// Nullable:True
           /// </summary>           
           public DateTime? create_time {get;set;}

           /// <summary>
           /// Desc:技能类型（ACTIVE-主动技能，PASSIVE-被动技能）
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? skill_type {get;set;}

    }
}
