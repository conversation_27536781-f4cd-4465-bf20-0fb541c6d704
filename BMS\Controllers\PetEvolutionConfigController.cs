using Microsoft.AspNetCore.Mvc;
using BMS.Models.Entities;
using BMS.Services.Interfaces;
using BMS.Models.DTOs;
using BMS.Models.Common;

namespace BMS.Controllers
{
    /// <summary>
    /// 宠物进化配置管理控制器
    /// </summary>
    public class PetEvolutionConfigController : Controller
    {
        private readonly IPetEvolutionConfigService _petEvolutionConfigService;

        public PetEvolutionConfigController(IPetEvolutionConfigService petEvolutionConfigService)
        {
            _petEvolutionConfigService = petEvolutionConfigService;
        }

        /// <summary>
        /// 宠物进化配置管理首页
        /// </summary>
        /// <returns>宠物进化配置管理页面</returns>
        [HttpGet]
        public IActionResult Index()
        {
            return View();
        }

        /// <summary>
        /// 获取宠物进化配置列表（分页）
        /// </summary>
        /// <param name="queryDto">查询条件</param>
        /// <returns>分页结果</returns>
        [HttpPost]
        [ActionName("GetList")]
        public async Task<IActionResult> GetList([FromBody] PetEvolutionConfigQueryDto queryDto)
        {
            try
            {
                var result = await _petEvolutionConfigService.GetPetEvolutionConfigsAsync(queryDto);
                return Json(new { code = 200, data = result.Data, total = result.TotalCount });
            }
            catch (Exception ex)
            {
                return Json(new { code = 500, message = $"获取宠物进化配置列表失败：{ex.Message}" });
            }
        }

        /// <summary>
        /// 新增宠物进化配置
        /// </summary>
        /// <param name="createDto">新增DTO</param>
        /// <returns>操作结果</returns>
        [HttpPost]
        [ActionName("Create")]
        public async Task<IActionResult> Create([FromBody] PetEvolutionConfigCreateDto createDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    var errors = ModelState
                        .Where(x => x.Value?.Errors.Count > 0)
                        .Select(x => x.Value?.Errors.First().ErrorMessage)
                        .ToList();
                    return Json(ApiResult.Fail(string.Join("; ", errors)));
                }

                var result = await _petEvolutionConfigService.CreateAsync(createDto);
                return Json(result.Success ? ApiResult.Ok(result.Data, result.Message) : ApiResult.Fail(result.Message));
            }
            catch (Exception ex)
            {
                return Json(ApiResult.Fail($"新增宠物进化配置失败：{ex.Message}"));
            }
        }

        /// <summary>
        /// 更新宠物进化配置
        /// </summary>
        /// <param name="updateDto">更新DTO</param>
        /// <returns>操作结果</returns>
        [HttpPost]
        [ActionName("Update")]
        public async Task<IActionResult> Update([FromBody] PetEvolutionConfigUpdateDto updateDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    var errors = ModelState
                        .Where(x => x.Value?.Errors.Count > 0)
                        .Select(x => x.Value?.Errors.First().ErrorMessage)
                        .ToList();
                    return Json(ApiResult.Fail(string.Join("; ", errors)));
                }

                var result = await _petEvolutionConfigService.UpdateAsync(updateDto);
                return Json(result.Success ? ApiResult.Ok(result.Data, result.Message) : ApiResult.Fail(result.Message));
            }
            catch (Exception ex)
            {
                return Json(ApiResult.Fail($"更新宠物进化配置失败：{ex.Message}"));
            }
        }

        /// <summary>
        /// 删除宠物进化配置
        /// </summary>
        /// <param name="deleteDto">删除DTO</param>
        /// <returns>操作结果</returns>
        [HttpPost]
        [ActionName("Delete")]
        public async Task<IActionResult> Delete([FromBody] PetEvolutionConfigDeleteDto deleteDto)
        {
            try
            {
                var result = await _petEvolutionConfigService.DeleteAsync(deleteDto.Id);
                return Json(result.Success ? ApiResult.Ok(result.Data, result.Message) : ApiResult.Fail(result.Message));
            }
            catch (Exception ex)
            {
                return Json(ApiResult.Fail($"删除宠物进化配置失败：{ex.Message}"));
            }
        }

        /// <summary>
        /// 获取宠物进化配置详情
        /// </summary>
        /// <param name="id">配置ID</param>
        /// <returns>配置详情</returns>
        [HttpGet]
        [ActionName("GetDetail")]
        public async Task<IActionResult> GetDetail(int id)
        {
            try
            {
                var result = await _petEvolutionConfigService.GetByIdAsync(id);
                return Json(result.Success ? ApiResult.Ok(result.Data) : ApiResult.Fail(result.Message));
            }
            catch (Exception ex)
            {
                return Json(ApiResult.Fail($"获取宠物进化配置详情失败：{ex.Message}"));
            }
        }

        /// <summary>
        /// 获取宠物配置选项
        /// </summary>
        /// <returns>宠物配置选项</returns>
        [HttpGet]
        [ActionName("GetPetConfigOptions")]
        public async Task<IActionResult> GetPetConfigOptions()
        {
            try
            {
                var result = await _petEvolutionConfigService.GetPetConfigOptionsAsync();
                return Json(new { code = 200, data = result });
            }
            catch (Exception ex)
            {
                return Json(new { code = 500, message = $"获取宠物配置选项失败：{ex.Message}" });
            }
        }

        /// <summary>
        /// 获取道具配置选项
        /// </summary>
        /// <returns>道具配置选项</returns>
        [HttpGet]
        [ActionName("GetItemConfigOptions")]
        public async Task<IActionResult> GetItemConfigOptions()
        {
            try
            {
                var result = await _petEvolutionConfigService.GetItemConfigOptionsAsync();
                return Json(new { code = 200, data = result });
            }
            catch (Exception ex)
            {
                return Json(new { code = 500, message = $"获取道具配置选项失败：{ex.Message}" });
            }
        }

        /// <summary>
        /// 检查进化配置是否存在
        /// </summary>
        /// <param name="petNo">宠物编号</param>
        /// <param name="evolutionType">进化类型</param>
        /// <param name="id">排除的ID</param>
        /// <returns>检查结果</returns>
        [HttpGet]
        [ActionName("CheckEvolutionConfigExists")]
        public async Task<IActionResult> CheckEvolutionConfigExists(int petNo, string evolutionType, int? id = null)
        {
            try
            {
                var exists = await _petEvolutionConfigService.CheckEvolutionConfigExistsAsync(petNo, evolutionType, id);
                return Json(new { code = 200, data = exists });
            }
            catch (Exception ex)
            {
                return Json(new { code = 500, message = $"检查进化配置失败：{ex.Message}" });
            }
        }

        /// <summary>
        /// 批量删除宠物进化配置
        /// </summary>
        /// <param name="ids">配置ID列表</param>
        /// <returns>操作结果</returns>
        [HttpPost]
        [ActionName("BatchDelete")]
        public async Task<IActionResult> BatchDelete([FromBody] List<int> ids)
        {
            try
            {
                var result = await _petEvolutionConfigService.BatchDeleteAsync(ids);
                return Json(result.Success ? ApiResult.Ok(result.Data, result.Message) : ApiResult.Fail(result.Message));
            }
            catch (Exception ex)
            {
                return Json(ApiResult.Fail($"批量删除宠物进化配置失败：{ex.Message}"));
            }
        }

        /// <summary>
        /// 切换激活状态
        /// </summary>
        /// <param name="toggleDto">切换DTO</param>
        /// <returns>操作结果</returns>
        [HttpPost]
        [ActionName("ToggleActive")]
        public async Task<IActionResult> ToggleActive([FromBody] PetEvolutionConfigToggleDto toggleDto)
        {
            try
            {
                var result = await _petEvolutionConfigService.ToggleActiveAsync(toggleDto.Id, toggleDto.IsActive);
                return Json(result.Success ? ApiResult.Ok(result.Data, result.Message) : ApiResult.Fail(result.Message));
            }
            catch (Exception ex)
            {
                return Json(ApiResult.Fail($"切换激活状态失败：{ex.Message}"));
            }
        }

        /// <summary>
        /// 测试数据库连接
        /// </summary>
        /// <returns>测试结果</returns>
        [HttpGet]
        [ActionName("TestConnection")]
        public async Task<IActionResult> TestConnection()
        {
            try
            {
                // 测试基本查询
                var configCount = await _petEvolutionConfigService.GetPetEvolutionConfigsAsync(new PetEvolutionConfigQueryDto { Page = 1, PageSize = 1 });
                var petCount = await _petEvolutionConfigService.GetPetConfigOptionsAsync();
                var itemCount = await _petEvolutionConfigService.GetItemConfigOptionsAsync();

                return Json(new {
                    code = 200,
                    message = "数据库连接正常",
                    configCount = configCount.TotalCount,
                    petOptionsCount = petCount.Count,
                    itemOptionsCount = itemCount.Count
                });
            }
            catch (Exception ex)
            {
                return Json(new {
                    code = 500,
                    message = $"数据库连接测试失败：{ex.Message}",
                    stackTrace = ex.StackTrace
                });
            }
        }
    }
}
