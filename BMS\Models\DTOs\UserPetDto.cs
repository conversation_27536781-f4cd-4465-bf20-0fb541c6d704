using System.ComponentModel.DataAnnotations;

namespace BMS.Models.DTOs
{
    /// <summary>
    /// 用户宠物DTO
    /// </summary>
    public class UserPetDto
    {
        /// <summary>
        /// 自增主键
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 所属用户ID
        /// </summary>
        public int UserId { get; set; }

        /// <summary>
        /// 用户名
        /// </summary>
        public string UserName { get; set; } = string.Empty;

        /// <summary>
        /// 宠物序号
        /// </summary>
        public int PetNo { get; set; }

        /// <summary>
        /// 宠物名称（来自pet_config）
        /// </summary>
        public string PetName { get; set; } = string.Empty;

        /// <summary>
        /// 宠物属性（来自pet_config）
        /// </summary>
        public string PetAttribute { get; set; } = string.Empty;

        /// <summary>
        /// 形象编号
        /// </summary>
        public int? Image { get; set; }

        /// <summary>
        /// 当前经验
        /// </summary>
        public long? Exp { get; set; }

        /// <summary>
        /// 等级（根据经验值计算）
        /// </summary>
        public int Level { get; set; }

        /// <summary>
        /// 生命值
        /// </summary>
        public long? Hp { get; set; }

        /// <summary>
        /// 魔法值
        /// </summary>
        public long? Mp { get; set; }

        /// <summary>
        /// 攻击力
        /// </summary>
        public long? Atk { get; set; }

        /// <summary>
        /// 防御力
        /// </summary>
        public long? Def { get; set; }

        /// <summary>
        /// 速度
        /// </summary>
        public long? Spd { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        public int? State { get; set; }

        /// <summary>
        /// 闪避
        /// </summary>
        public long? Dodge { get; set; }

        /// <summary>
        /// 成长值
        /// </summary>
        public decimal? Growth { get; set; }

        /// <summary>
        /// 命中
        /// </summary>
        public long? Hit { get; set; }

        /// <summary>
        /// 境界
        /// </summary>
        public string? Realm { get; set; }

        /// <summary>
        /// 已进化次数
        /// </summary>
        public int? EvolveCount { get; set; }

        /// <summary>
        /// 指定五行
        /// </summary>
        public string? Element { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime? CreateTime { get; set; }

        /// <summary>
        /// 宠物状态（牧场,携带,丢弃）
        /// </summary>
        public string Status { get; set; } = string.Empty;

        /// <summary>
        /// 是否主战宠物
        /// </summary>
        public bool IsMain { get; set; } = false;

        /// <summary>
        /// 宠物技能列表
        /// </summary>
        public List<UserPetSkillDto> Skills { get; set; } = new List<UserPetSkillDto>();
    }

    /// <summary>
    /// 用户宠物查询DTO
    /// </summary>
    public class UserPetQueryDto : PagedQueryDto
    {
        /// <summary>
        /// 用户ID
        /// </summary>
        public int? UserId { get; set; }

        /// <summary>
        /// 用户名（支持模糊查询）
        /// </summary>
        public string? UserName { get; set; }

        /// <summary>
        /// 宠物序号
        /// </summary>
        public int? PetNo { get; set; }

        /// <summary>
        /// 宠物名称（支持模糊查询）
        /// </summary>
        public string? PetName { get; set; }

        /// <summary>
        /// 宠物属性
        /// </summary>
        public string? PetAttribute { get; set; }

        /// <summary>
        /// 境界
        /// </summary>
        public string? Realm { get; set; }

        /// <summary>
        /// 指定五行
        /// </summary>
        public string? Element { get; set; }

        /// <summary>
        /// 宠物状态（牧场,携带,丢弃）
        /// </summary>
        public string? Status { get; set; }

        /// <summary>
        /// 是否主战宠物
        /// </summary>
        public bool? IsMain { get; set; }
    }

    /// <summary>
    /// 创建用户宠物DTO
    /// </summary>
    public class UserPetCreateDto
    {
        /// <summary>
        /// 所属用户ID
        /// </summary>
        [Required(ErrorMessage = "用户ID不能为空")]
        [Range(1, int.MaxValue, ErrorMessage = "用户ID必须大于0")]
        public int UserId { get; set; }

        /// <summary>
        /// 宠物序号
        /// </summary>
        [Required(ErrorMessage = "宠物序号不能为空")]
        [Range(1, int.MaxValue, ErrorMessage = "宠物序号必须大于0")]
        public int PetNo { get; set; }

        /// <summary>
        /// 形象编号
        /// </summary>
        public int? Image { get; set; }

        /// <summary>
        /// 当前经验
        /// </summary>
        [Range(0, long.MaxValue, ErrorMessage = "经验值不能为负数")]
        public long? Exp { get; set; } = 0;

        /// <summary>
        /// 生命值
        /// </summary>
        [Range(0, long.MaxValue, ErrorMessage = "生命值不能为负数")]
        public long? Hp { get; set; } = 100;

        /// <summary>
        /// 魔法值
        /// </summary>
        [Range(0, long.MaxValue, ErrorMessage = "魔法值不能为负数")]
        public long? Mp { get; set; } = 100;

        /// <summary>
        /// 攻击力
        /// </summary>
        [Range(0, long.MaxValue, ErrorMessage = "攻击力不能为负数")]
        public long? Atk { get; set; } = 10;

        /// <summary>
        /// 防御力
        /// </summary>
        [Range(0, long.MaxValue, ErrorMessage = "防御力不能为负数")]
        public long? Def { get; set; } = 10;

        /// <summary>
        /// 速度
        /// </summary>
        [Range(0, long.MaxValue, ErrorMessage = "速度不能为负数")]
        public long? Spd { get; set; } = 10;

        /// <summary>
        /// 状态
        /// </summary>
        public int? State { get; set; } = 0;

        /// <summary>
        /// 闪避
        /// </summary>
        [Range(0, long.MaxValue, ErrorMessage = "闪避不能为负数")]
        public long? Dodge { get; set; } = 10;

        /// <summary>
        /// 成长值
        /// </summary>
        [Range(0, double.MaxValue, ErrorMessage = "成长值不能为负数")]
        public decimal? Growth { get; set; } = 1.0m;

        /// <summary>
        /// 命中
        /// </summary>
        [Range(0, long.MaxValue, ErrorMessage = "命中不能为负数")]
        public long? Hit { get; set; } = 10;

        /// <summary>
        /// 境界
        /// </summary>
        [StringLength(20, ErrorMessage = "境界长度不能超过20个字符")]
        public string? Realm { get; set; }

        /// <summary>
        /// 已进化次数
        /// </summary>
        [Range(0, int.MaxValue, ErrorMessage = "进化次数不能为负数")]
        public int? EvolveCount { get; set; } = 0;

        /// <summary>
        /// 指定五行
        /// </summary>
        [StringLength(10, ErrorMessage = "五行属性长度不能超过10个字符")]
        public string? Element { get; set; }

        /// <summary>
        /// 宠物状态（牧场,携带,丢弃）
        /// </summary>
        [Required(ErrorMessage = "宠物状态不能为空")]
        [StringLength(10, ErrorMessage = "宠物状态长度不能超过10个字符")]
        public string Status { get; set; } = "牧场";

        /// <summary>
        /// 是否主战宠物
        /// </summary>
        public bool IsMain { get; set; } = false;
    }

    /// <summary>
    /// 更新用户宠物DTO
    /// </summary>
    public class UserPetUpdateDto
    {
        /// <summary>
        /// 自增主键
        /// </summary>
        [Required(ErrorMessage = "ID不能为空")]
        [Range(1, int.MaxValue, ErrorMessage = "ID必须大于0")]
        public int Id { get; set; }

        /// <summary>
        /// 形象编号
        /// </summary>
        public int? Image { get; set; }

        /// <summary>
        /// 当前经验
        /// </summary>
        [Range(0, long.MaxValue, ErrorMessage = "经验值不能为负数")]
        public long? Exp { get; set; }

        /// <summary>
        /// 生命值
        /// </summary>
        [Range(0, long.MaxValue, ErrorMessage = "生命值不能为负数")]
        public long? Hp { get; set; }

        /// <summary>
        /// 魔法值
        /// </summary>
        [Range(0, long.MaxValue, ErrorMessage = "魔法值不能为负数")]
        public long? Mp { get; set; }

        /// <summary>
        /// 攻击力
        /// </summary>
        [Range(0, long.MaxValue, ErrorMessage = "攻击力不能为负数")]
        public long? Atk { get; set; }

        /// <summary>
        /// 防御力
        /// </summary>
        [Range(0, long.MaxValue, ErrorMessage = "防御力不能为负数")]
        public long? Def { get; set; }

        /// <summary>
        /// 速度
        /// </summary>
        [Range(0, long.MaxValue, ErrorMessage = "速度不能为负数")]
        public long? Spd { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        public int? State { get; set; }

        /// <summary>
        /// 闪避
        /// </summary>
        [Range(0, long.MaxValue, ErrorMessage = "闪避不能为负数")]
        public long? Dodge { get; set; }

        /// <summary>
        /// 成长值
        /// </summary>
        [Range(0, double.MaxValue, ErrorMessage = "成长值不能为负数")]
        public decimal? Growth { get; set; }

        /// <summary>
        /// 命中
        /// </summary>
        [Range(0, long.MaxValue, ErrorMessage = "命中不能为负数")]
        public long? Hit { get; set; }

        /// <summary>
        /// 境界
        /// </summary>
        [StringLength(20, ErrorMessage = "境界长度不能超过20个字符")]
        public string? Realm { get; set; }

        /// <summary>
        /// 已进化次数
        /// </summary>
        [Range(0, int.MaxValue, ErrorMessage = "进化次数不能为负数")]
        public int? EvolveCount { get; set; }

        /// <summary>
        /// 指定五行
        /// </summary>
        [StringLength(10, ErrorMessage = "五行属性长度不能超过10个字符")]
        public string? Element { get; set; }

        /// <summary>
        /// 宠物状态（牧场,携带,丢弃）
        /// </summary>
        [Required(ErrorMessage = "宠物状态不能为空")]
        [StringLength(10, ErrorMessage = "宠物状态长度不能超过10个字符")]
        public string Status { get; set; } = string.Empty;

        /// <summary>
        /// 是否主战宠物
        /// </summary>
        public bool IsMain { get; set; } = false;
    }

    /// <summary>
    /// 删除用户宠物DTO
    /// </summary>
    public class UserPetDeleteDto
    {
        /// <summary>
        /// 自增主键
        /// </summary>
        [Required(ErrorMessage = "ID不能为空")]
        [Range(1, int.MaxValue, ErrorMessage = "ID必须大于0")]
        public int Id { get; set; }
    }

    /// <summary>
    /// 用户宠物技能DTO
    /// </summary>
    public class UserPetSkillDto
    {
        /// <summary>
        /// 自增主键
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 用户宠物表主键ID
        /// </summary>
        public int UserPetId { get; set; }

        /// <summary>
        /// 技能编号
        /// </summary>
        public int SkillId { get; set; }

        /// <summary>
        /// 技能名称
        /// </summary>
        public string SkillName { get; set; } = string.Empty;

        /// <summary>
        /// 技能等级或附加数值
        /// </summary>
        public int? SkillLevel { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime? CreateTime { get; set; }
    }

    /// <summary>
    /// 添加/更新用户宠物技能DTO
    /// </summary>
    public class UserPetSkillManageDto
    {
        /// <summary>
        /// 用户宠物表主键ID
        /// </summary>
        [Required(ErrorMessage = "用户宠物ID不能为空")]
        [Range(1, int.MaxValue, ErrorMessage = "用户宠物ID必须大于0")]
        public int UserPetId { get; set; }

        /// <summary>
        /// 技能编号
        /// </summary>
        [Required(ErrorMessage = "技能编号不能为空")]
        [Range(1, int.MaxValue, ErrorMessage = "技能编号必须大于0")]
        public int SkillId { get; set; }

        /// <summary>
        /// 技能等级或附加数值
        /// </summary>
        [Range(0, int.MaxValue, ErrorMessage = "技能等级不能为负数")]
        public int? SkillLevel { get; set; } = 1;
    }

    /// <summary>
    /// 删除用户宠物技能DTO
    /// </summary>
    public class UserPetSkillDeleteDto
    {
        /// <summary>
        /// 自增主键
        /// </summary>
        [Required(ErrorMessage = "ID不能为空")]
        [Range(1, int.MaxValue, ErrorMessage = "ID必须大于0")]
        public int Id { get; set; }
    }
} 