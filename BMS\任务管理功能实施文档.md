# 任务管理功能实施文档

## 1. 项目概述

### 1.1 项目架构分析
基于当前 BMS 项目的架构模式，采用以下技术栈：
- **后端框架**: ASP.NET Core MVC
- **ORM**: SqlSugar
- **数据库**: MySQL
- **前端**: Vue.js + Bootstrap + AdminLTE
- **架构模式**: 三层架构（Controller -> Service -> Repository）

### 1.2 任务系统数据库设计
根据 `kddata.sql` 分析，任务系统包含以下核心表：

#### 核心表结构
1. **task_config** - 任务配置表（主表）
2. **task_objective** - 任务目标表
3. **task_type_config** - 任务类型配置表
4. **user_task** - 用户任务表
5. **user_task_progress** - 用户任务进度表
6. **task_reward_log** - 任务奖励记录表

## 2. 数据库表结构详解

### 2.1 task_config（任务配置表）
```sql
CREATE TABLE `task_config` (
  `task_id` varchar(50) NOT NULL COMMENT '任务ID',
  `task_name` varchar(200) NOT NULL COMMENT '任务名称',
  `task_description` text COMMENT '任务描述',
  `task_type` tinyint(4) DEFAULT 0 COMMENT '任务类型(0=普通,1=循环,2=活动)',
  `is_repeatable` tinyint(4) DEFAULT 0 COMMENT '是否可重复(0=否,1=是)',
  `prerequisite_task` varchar(50) DEFAULT NULL COMMENT '前置任务ID',
  `required_pet` varchar(50) DEFAULT NULL COMMENT '指定宠物ID',
  `reward_config` text COMMENT '奖励配置(JSON格式)',
  `is_network_task` tinyint(4) DEFAULT 0 COMMENT '是否网络任务',
  `is_active` tinyint(4) DEFAULT 1 COMMENT '是否激活',
  `sort_order` int(11) DEFAULT 0 COMMENT '排序顺序',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`task_id`)
);
```

### 2.2 task_objective（任务目标表）
```sql
CREATE TABLE `task_objective` (
  `objective_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '目标ID',
  `task_id` varchar(50) NOT NULL COMMENT '任务ID',
  `objective_type` varchar(50) NOT NULL COMMENT '目标类型',
  `target_id` varchar(50) DEFAULT NULL COMMENT '目标ID',
  `target_amount` int(11) NOT NULL DEFAULT 1 COMMENT '目标数量',
  `objective_order` int(11) DEFAULT 0 COMMENT '目标顺序',
  `objective_description` varchar(500) DEFAULT NULL COMMENT '目标描述',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`objective_id`),
  FOREIGN KEY (`task_id`) REFERENCES `task_config` (`task_id`)
);
```

### 2.3 user_task（用户任务表）
```sql
CREATE TABLE `user_task` (
  `user_task_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '用户任务ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `task_id` varchar(50) NOT NULL COMMENT '任务ID',
  `task_status` tinyint(4) DEFAULT 1 COMMENT '任务状态(0=已完成,1=进行中,2=已放弃)',
  `accepted_at` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '接取时间',
  `completed_at` timestamp DEFAULT NULL COMMENT '完成时间',
  `abandoned_at` timestamp DEFAULT NULL COMMENT '放弃时间',
  `completion_count` int(11) DEFAULT 0 COMMENT '完成次数',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`user_task_id`),
  UNIQUE KEY `unique_user_task` (`user_id`, `task_id`)
);
```

## 3. 实体模型设计

### 3.1 需要创建的实体类
基于项目现有的实体模式，需要在 `BMS/Models/Entities/` 目录下创建：

1. **task_config.cs** - 任务配置实体
2. **task_objective.cs** - 任务目标实体
3. **task_type_config.cs** - 任务类型配置实体
4. **user_task.cs** - 用户任务实体
5. **user_task_progress.cs** - 用户任务进度实体
6. **task_reward_log.cs** - 任务奖励记录实体

### 3.2 实体类示例（task_config.cs）
```csharp
using SqlSugar;

namespace BMS.Models.Entities
{
    [SugarTable("task_config")]
    public class task_config
    {
        [SugarColumn(IsPrimaryKey = true)]
        public string task_id { get; set; }
        
        public string task_name { get; set; }
        public string? task_description { get; set; }
        public int? task_type { get; set; }
        public bool? is_repeatable { get; set; }
        public string? prerequisite_task { get; set; }
        public string? required_pet { get; set; }
        public string? reward_config { get; set; }
        public bool? is_network_task { get; set; }
        public bool? is_active { get; set; }
        public int? sort_order { get; set; }
        public DateTime? created_at { get; set; }
        public DateTime? updated_at { get; set; }
    }
}
```

## 4. DTO 设计

### 4.1 需要创建的 DTO 类
在 `BMS/Models/DTOs/` 目录下创建：

1. **TaskConfigDto.cs** - 任务配置显示DTO
2. **TaskConfigCreateDto.cs** - 任务配置创建DTO
3. **TaskConfigUpdateDto.cs** - 任务配置更新DTO
4. **TaskConfigQueryDto.cs** - 任务配置查询DTO
5. **TaskObjectiveDto.cs** - 任务目标DTO
6. **UserTaskDto.cs** - 用户任务DTO
7. **TaskProgressDto.cs** - 任务进度DTO

### 4.2 DTO 示例（TaskConfigDto.cs）
```csharp
using System.ComponentModel.DataAnnotations;

namespace BMS.Models.DTOs
{
    public class TaskConfigDto
    {
        public string TaskId { get; set; }
        public string TaskName { get; set; }
        public string? TaskDescription { get; set; }
        public int TaskType { get; set; }
        public string TaskTypeName { get; set; }
        public bool IsRepeatable { get; set; }
        public string? PrerequisiteTask { get; set; }
        public string? RequiredPet { get; set; }
        public string? RewardConfig { get; set; }
        public bool IsNetworkTask { get; set; }
        public bool IsActive { get; set; }
        public int SortOrder { get; set; }
        public DateTime? CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
        
        // 关联数据
        public List<TaskObjectiveDto>? Objectives { get; set; }
    }
}
```

## 5. 服务层设计

### 5.1 接口定义
在 `BMS/Services/Interfaces/` 目录下创建：

1. **ITaskConfigService.cs** - 任务配置服务接口
2. **ITaskObjectiveService.cs** - 任务目标服务接口
3. **IUserTaskService.cs** - 用户任务服务接口
4. **ITaskProgressService.cs** - 任务进度服务接口

### 5.2 服务实现
在 `BMS/Services/Implementations/` 目录下创建对应的实现类。

## 6. 控制器设计

### 6.1 需要创建的控制器
在 `BMS/Controllers/` 目录下创建：

1. **TaskConfigController.cs** - 任务配置管理控制器
2. **UserTaskController.cs** - 用户任务管理控制器

### 6.2 控制器功能规划

#### TaskConfigController 功能：
- 任务配置的 CRUD 操作
- 任务目标管理
- 任务类型管理
- 任务奖励配置

#### UserTaskController 功能：
- 用户任务列表查询
- 任务接取/放弃
- 任务进度更新
- 任务完成处理
- 奖励发放

## 7. 前端页面设计

### 7.1 需要创建的视图页面
在 `BMS/Views/` 目录下创建：

1. **TaskConfig/** - 任务配置管理页面
   - Index.cshtml - 任务列表页面
   - Create.cshtml - 创建任务页面
   - Edit.cshtml - 编辑任务页面

2. **UserTask/** - 用户任务管理页面
   - Index.cshtml - 用户任务列表页面
   - Progress.cshtml - 任务进度管理页面

### 7.2 页面功能规划

#### 任务配置管理页面功能：
- 任务列表展示（支持分页、搜索、筛选）
- 任务创建/编辑（包含目标配置、奖励配置）
- 任务启用/禁用
- 任务排序管理

#### 用户任务管理页面功能：
- 用户任务列表（按状态筛选）
- 任务进度查看
- 任务操作（强制完成、重置等）
- 奖励记录查看

## 8. 实施计划

### 8.1 第一阶段：基础架构搭建
1. 创建实体模型
2. 创建 DTO 类
3. 创建服务接口和基础实现
4. 创建控制器基础结构

### 8.2 第二阶段：任务配置管理
1. 实现任务配置的 CRUD 功能
2. 实现任务目标管理
3. 创建任务配置管理前端页面
4. 测试任务配置功能

### 8.3 第三阶段：用户任务系统
1. 实现用户任务的接取逻辑
2. 实现任务进度跟踪
3. 实现任务完成和奖励发放
4. 创建用户任务管理前端页面

### 8.4 第四阶段：高级功能
1. 实现任务链和前置任务逻辑
2. 实现循环任务和活动任务
3. 实现任务统计和报表
4. 性能优化和测试

## 9. 技术要点

### 9.1 任务进度跟踪
- 使用事件驱动模式监听游戏事件
- 实现任务目标类型的可扩展设计
- 支持多种目标类型（击杀怪物、收集道具、达到等级等）

### 9.2 奖励系统集成
- 与现有的道具系统、装备系统集成
- 支持多种奖励类型（道具、货币、经验等）
- 实现奖励发放的事务性保证

### 9.3 性能考虑
- 任务进度的批量更新
- 缓存热门任务配置
- 数据库索引优化

## 10. 下一步行动

1. **立即开始**：创建基础实体模型和 DTO
2. **优先级高**：实现任务配置管理功能
3. **后续规划**：逐步实现用户任务系统
4. **持续优化**：根据使用情况调整和优化

## 11. 详细实现指南

### 11.1 实体模型创建步骤

#### 步骤1：创建 task_config.cs
```csharp
using SqlSugar;

namespace BMS.Models.Entities
{
    [SugarTable("task_config")]
    public class task_config
    {
        /// <summary>
        /// 任务ID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        public string task_id { get; set; }

        /// <summary>
        /// 任务名称
        /// </summary>
        public string task_name { get; set; }

        /// <summary>
        /// 任务描述
        /// </summary>
        public string? task_description { get; set; }

        /// <summary>
        /// 任务类型(0=普通,1=循环,2=活动)
        /// </summary>
        public int? task_type { get; set; }

        /// <summary>
        /// 是否可重复(0=否,1=是)
        /// </summary>
        public bool? is_repeatable { get; set; }

        /// <summary>
        /// 前置任务ID
        /// </summary>
        public string? prerequisite_task { get; set; }

        /// <summary>
        /// 指定宠物ID
        /// </summary>
        public string? required_pet { get; set; }

        /// <summary>
        /// 奖励配置(JSON格式)
        /// </summary>
        public string? reward_config { get; set; }

        /// <summary>
        /// 是否网络任务
        /// </summary>
        public bool? is_network_task { get; set; }

        /// <summary>
        /// 是否激活
        /// </summary>
        public bool? is_active { get; set; }

        /// <summary>
        /// 排序顺序
        /// </summary>
        public int? sort_order { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime? created_at { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime? updated_at { get; set; }
    }
}
```

#### 步骤2：创建 task_objective.cs
```csharp
using SqlSugar;

namespace BMS.Models.Entities
{
    [SugarTable("task_objective")]
    public class task_objective
    {
        /// <summary>
        /// 目标ID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public int objective_id { get; set; }

        /// <summary>
        /// 任务ID
        /// </summary>
        public string task_id { get; set; }

        /// <summary>
        /// 目标类型(KILL_MONSTER,COLLECT_ITEM,REACH_LEVEL等)
        /// </summary>
        public string objective_type { get; set; }

        /// <summary>
        /// 目标ID(怪物ID/道具ID等)
        /// </summary>
        public string? target_id { get; set; }

        /// <summary>
        /// 目标数量
        /// </summary>
        public int target_amount { get; set; }

        /// <summary>
        /// 目标顺序
        /// </summary>
        public int? objective_order { get; set; }

        /// <summary>
        /// 目标描述
        /// </summary>
        public string? objective_description { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime? created_at { get; set; }
    }
}
```

### 11.2 DTO 类创建步骤

#### 步骤1：创建 TaskConfigDto.cs
```csharp
using System.ComponentModel.DataAnnotations;

namespace BMS.Models.DTOs
{
    /// <summary>
    /// 任务配置显示DTO
    /// </summary>
    public class TaskConfigDto
    {
        /// <summary>
        /// 任务ID
        /// </summary>
        public string TaskId { get; set; }

        /// <summary>
        /// 任务名称
        /// </summary>
        public string TaskName { get; set; }

        /// <summary>
        /// 任务描述
        /// </summary>
        public string? TaskDescription { get; set; }

        /// <summary>
        /// 任务类型
        /// </summary>
        public int TaskType { get; set; }

        /// <summary>
        /// 任务类型名称
        /// </summary>
        public string TaskTypeName { get; set; }

        /// <summary>
        /// 是否可重复
        /// </summary>
        public bool IsRepeatable { get; set; }

        /// <summary>
        /// 前置任务ID
        /// </summary>
        public string? PrerequisiteTask { get; set; }

        /// <summary>
        /// 前置任务名称
        /// </summary>
        public string? PrerequisiteTaskName { get; set; }

        /// <summary>
        /// 指定宠物ID
        /// </summary>
        public string? RequiredPet { get; set; }

        /// <summary>
        /// 奖励配置
        /// </summary>
        public string? RewardConfig { get; set; }

        /// <summary>
        /// 是否网络任务
        /// </summary>
        public bool IsNetworkTask { get; set; }

        /// <summary>
        /// 是否激活
        /// </summary>
        public bool IsActive { get; set; }

        /// <summary>
        /// 排序顺序
        /// </summary>
        public int SortOrder { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime? CreatedAt { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime? UpdatedAt { get; set; }

        /// <summary>
        /// 任务目标列表
        /// </summary>
        public List<TaskObjectiveDto>? Objectives { get; set; }
    }

    /// <summary>
    /// 任务配置查询DTO
    /// </summary>
    public class TaskConfigQueryDto : PagedQueryDto
    {
        /// <summary>
        /// 任务名称
        /// </summary>
        public string? TaskName { get; set; }

        /// <summary>
        /// 任务类型
        /// </summary>
        public int? TaskType { get; set; }

        /// <summary>
        /// 是否激活
        /// </summary>
        public bool? IsActive { get; set; }

        /// <summary>
        /// 是否可重复
        /// </summary>
        public bool? IsRepeatable { get; set; }
    }

    /// <summary>
    /// 任务配置创建DTO
    /// </summary>
    public class TaskConfigCreateDto
    {
        /// <summary>
        /// 任务ID
        /// </summary>
        [Required(ErrorMessage = "任务ID不能为空")]
        [StringLength(50, ErrorMessage = "任务ID长度不能超过50个字符")]
        public string TaskId { get; set; }

        /// <summary>
        /// 任务名称
        /// </summary>
        [Required(ErrorMessage = "任务名称不能为空")]
        [StringLength(200, ErrorMessage = "任务名称长度不能超过200个字符")]
        public string TaskName { get; set; }

        /// <summary>
        /// 任务描述
        /// </summary>
        public string? TaskDescription { get; set; }

        /// <summary>
        /// 任务类型
        /// </summary>
        [Range(0, 2, ErrorMessage = "任务类型必须在0-2之间")]
        public int TaskType { get; set; } = 0;

        /// <summary>
        /// 是否可重复
        /// </summary>
        public bool IsRepeatable { get; set; } = false;

        /// <summary>
        /// 前置任务ID
        /// </summary>
        public string? PrerequisiteTask { get; set; }

        /// <summary>
        /// 指定宠物ID
        /// </summary>
        public string? RequiredPet { get; set; }

        /// <summary>
        /// 奖励配置
        /// </summary>
        public string? RewardConfig { get; set; }

        /// <summary>
        /// 是否网络任务
        /// </summary>
        public bool IsNetworkTask { get; set; } = false;

        /// <summary>
        /// 是否激活
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// 排序顺序
        /// </summary>
        public int SortOrder { get; set; } = 0;

        /// <summary>
        /// 任务目标列表
        /// </summary>
        public List<TaskObjectiveCreateDto>? Objectives { get; set; }
    }

    /// <summary>
    /// 任务目标DTO
    /// </summary>
    public class TaskObjectiveDto
    {
        /// <summary>
        /// 目标ID
        /// </summary>
        public int ObjectiveId { get; set; }

        /// <summary>
        /// 任务ID
        /// </summary>
        public string TaskId { get; set; }

        /// <summary>
        /// 目标类型
        /// </summary>
        public string ObjectiveType { get; set; }

        /// <summary>
        /// 目标类型名称
        /// </summary>
        public string ObjectiveTypeName { get; set; }

        /// <summary>
        /// 目标ID
        /// </summary>
        public string? TargetId { get; set; }

        /// <summary>
        /// 目标名称
        /// </summary>
        public string? TargetName { get; set; }

        /// <summary>
        /// 目标数量
        /// </summary>
        public int TargetAmount { get; set; }

        /// <summary>
        /// 目标顺序
        /// </summary>
        public int ObjectiveOrder { get; set; }

        /// <summary>
        /// 目标描述
        /// </summary>
        public string? ObjectiveDescription { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime? CreatedAt { get; set; }
    }

    /// <summary>
    /// 任务目标创建DTO
    /// </summary>
    public class TaskObjectiveCreateDto
    {
        /// <summary>
        /// 目标类型
        /// </summary>
        [Required(ErrorMessage = "目标类型不能为空")]
        public string ObjectiveType { get; set; }

        /// <summary>
        /// 目标ID
        /// </summary>
        public string? TargetId { get; set; }

        /// <summary>
        /// 目标数量
        /// </summary>
        [Range(1, int.MaxValue, ErrorMessage = "目标数量必须大于0")]
        public int TargetAmount { get; set; } = 1;

        /// <summary>
        /// 目标顺序
        /// </summary>
        public int ObjectiveOrder { get; set; } = 0;

        /// <summary>
        /// 目标描述
        /// </summary>
        public string? ObjectiveDescription { get; set; }
    }
}
```

### 11.3 服务层实现步骤

#### 步骤1：创建 ITaskConfigService.cs 接口
```csharp
using BMS.Models.DTOs;
using BMS.Models.Common;

namespace BMS.Services.Interfaces
{
    /// <summary>
    /// 任务配置服务接口
    /// </summary>
    public interface ITaskConfigService
    {
        /// <summary>
        /// 获取任务配置列表
        /// </summary>
        Task<PagedResult<TaskConfigDto>> GetTaskConfigsAsync(TaskConfigQueryDto queryDto);

        /// <summary>
        /// 根据ID获取任务配置
        /// </summary>
        Task<TaskConfigDto?> GetTaskConfigByIdAsync(string taskId);

        /// <summary>
        /// 创建任务配置
        /// </summary>
        Task<ApiResult<TaskConfigDto>> CreateTaskConfigAsync(TaskConfigCreateDto createDto);

        /// <summary>
        /// 更新任务配置
        /// </summary>
        Task<ApiResult<TaskConfigDto>> UpdateTaskConfigAsync(string taskId, TaskConfigCreateDto updateDto);

        /// <summary>
        /// 删除任务配置
        /// </summary>
        Task<ApiResult<bool>> DeleteTaskConfigAsync(string taskId);

        /// <summary>
        /// 启用/禁用任务
        /// </summary>
        Task<ApiResult<bool>> ToggleTaskActiveAsync(string taskId, bool isActive);

        /// <summary>
        /// 获取任务类型选项
        /// </summary>
        Task<List<OptionDto>> GetTaskTypeOptionsAsync();

        /// <summary>
        /// 获取任务选项（用于前置任务选择）
        /// </summary>
        Task<List<OptionDto>> GetTaskOptionsAsync();
    }
}
```

#### 步骤2：创建 TaskConfigService.cs 实现
```csharp
using BMS.Models.DTOs;
using BMS.Models.Entities;
using BMS.Models.Common;
using BMS.Services.Interfaces;
using SqlSugar;

namespace BMS.Services.Implementations
{
    /// <summary>
    /// 任务配置服务实现
    /// </summary>
    public class TaskConfigService : ITaskConfigService
    {
        private readonly IDbService _dbService;

        public TaskConfigService(IDbService dbService)
        {
            _dbService = dbService;
        }

        /// <summary>
        /// 获取任务配置列表
        /// </summary>
        public async Task<PagedResult<TaskConfigDto>> GetTaskConfigsAsync(TaskConfigQueryDto queryDto)
        {
            try
            {
                var query = _dbService.Queryable<task_config>()
                    .WhereIF(!string.IsNullOrEmpty(queryDto.TaskName),
                        t => t.task_name.Contains(queryDto.TaskName!))
                    .WhereIF(queryDto.TaskType.HasValue,
                        t => t.task_type == queryDto.TaskType)
                    .WhereIF(queryDto.IsActive.HasValue,
                        t => t.is_active == queryDto.IsActive)
                    .WhereIF(queryDto.IsRepeatable.HasValue,
                        t => t.is_repeatable == queryDto.IsRepeatable)
                    .OrderBy(t => t.sort_order)
                    .OrderBy(t => t.created_at, OrderByType.Desc);

                var totalCount = await query.CountAsync();
                var tasks = await query
                    .Skip((queryDto.Page - 1) * queryDto.PageSize)
                    .Take(queryDto.PageSize)
                    .ToListAsync();

                var taskDtos = tasks.Select(MapToDto).ToList();

                // 加载任务目标
                foreach (var taskDto in taskDtos)
                {
                    taskDto.Objectives = await GetTaskObjectivesAsync(taskDto.TaskId);
                }

                return new PagedResult<TaskConfigDto>
                {
                    Data = taskDtos,
                    TotalCount = totalCount
                };
            }
            catch (Exception ex)
            {
                throw new Exception($"获取任务配置列表失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 根据ID获取任务配置
        /// </summary>
        public async Task<TaskConfigDto?> GetTaskConfigByIdAsync(string taskId)
        {
            try
            {
                var task = await _dbService.Queryable<task_config>()
                    .FirstAsync(t => t.task_id == taskId);

                if (task == null) return null;

                var taskDto = MapToDto(task);
                taskDto.Objectives = await GetTaskObjectivesAsync(taskId);

                return taskDto;
            }
            catch (Exception ex)
            {
                throw new Exception($"获取任务配置失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 创建任务配置
        /// </summary>
        public async Task<ApiResult<TaskConfigDto>> CreateTaskConfigAsync(TaskConfigCreateDto createDto)
        {
            try
            {
                // 检查任务ID是否已存在
                var existingTask = await _dbService.Queryable<task_config>()
                    .FirstAsync(t => t.task_id == createDto.TaskId);

                if (existingTask != null)
                {
                    return ApiResult<TaskConfigDto>.Fail("任务ID已存在");
                }

                // 验证前置任务
                if (!string.IsNullOrEmpty(createDto.PrerequisiteTask))
                {
                    var prerequisiteExists = await _dbService.Queryable<task_config>()
                        .AnyAsync(t => t.task_id == createDto.PrerequisiteTask);

                    if (!prerequisiteExists)
                    {
                        return ApiResult<TaskConfigDto>.Fail("前置任务不存在");
                    }
                }

                var task = new task_config
                {
                    task_id = createDto.TaskId,
                    task_name = createDto.TaskName,
                    task_description = createDto.TaskDescription,
                    task_type = createDto.TaskType,
                    is_repeatable = createDto.IsRepeatable,
                    prerequisite_task = createDto.PrerequisiteTask,
                    required_pet = createDto.RequiredPet,
                    reward_config = createDto.RewardConfig,
                    is_network_task = createDto.IsNetworkTask,
                    is_active = createDto.IsActive,
                    sort_order = createDto.SortOrder,
                    created_at = DateTime.Now,
                    updated_at = DateTime.Now
                };

                await _dbService.Insertable(task).ExecuteCommandAsync();

                // 创建任务目标
                if (createDto.Objectives?.Any() == true)
                {
                    await CreateTaskObjectivesAsync(createDto.TaskId, createDto.Objectives);
                }

                var result = await GetTaskConfigByIdAsync(createDto.TaskId);
                return ApiResult<TaskConfigDto>.Ok(result!, "任务配置创建成功");
            }
            catch (Exception ex)
            {
                return ApiResult<TaskConfigDto>.Fail($"创建任务配置失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取任务类型选项
        /// </summary>
        public async Task<List<OptionDto>> GetTaskTypeOptionsAsync()
        {
            return await Task.FromResult(new List<OptionDto>
            {
                new OptionDto { Value = "0", Label = "普通任务" },
                new OptionDto { Value = "1", Label = "循环任务" },
                new OptionDto { Value = "2", Label = "活动任务" }
            });
        }

        /// <summary>
        /// 获取任务选项
        /// </summary>
        public async Task<List<OptionDto>> GetTaskOptionsAsync()
        {
            try
            {
                var tasks = await _dbService.Queryable<task_config>()
                    .Where(t => t.is_active == true)
                    .OrderBy(t => t.sort_order)
                    .Select(t => new OptionDto
                    {
                        Value = t.task_id,
                        Label = t.task_name
                    })
                    .ToListAsync();

                return tasks;
            }
            catch (Exception ex)
            {
                throw new Exception($"获取任务选项失败: {ex.Message}", ex);
            }
        }

        #region 私有方法

        /// <summary>
        /// 实体转DTO
        /// </summary>
        private TaskConfigDto MapToDto(task_config task)
        {
            return new TaskConfigDto
            {
                TaskId = task.task_id,
                TaskName = task.task_name,
                TaskDescription = task.task_description,
                TaskType = task.task_type ?? 0,
                TaskTypeName = GetTaskTypeName(task.task_type ?? 0),
                IsRepeatable = task.is_repeatable ?? false,
                PrerequisiteTask = task.prerequisite_task,
                RequiredPet = task.required_pet,
                RewardConfig = task.reward_config,
                IsNetworkTask = task.is_network_task ?? false,
                IsActive = task.is_active ?? true,
                SortOrder = task.sort_order ?? 0,
                CreatedAt = task.created_at,
                UpdatedAt = task.updated_at
            };
        }

        /// <summary>
        /// 获取任务类型名称
        /// </summary>
        private string GetTaskTypeName(int taskType)
        {
            return taskType switch
            {
                0 => "普通任务",
                1 => "循环任务",
                2 => "活动任务",
                _ => "未知类型"
            };
        }

        /// <summary>
        /// 获取任务目标列表
        /// </summary>
        private async Task<List<TaskObjectiveDto>> GetTaskObjectivesAsync(string taskId)
        {
            try
            {
                var objectives = await _dbService.Queryable<task_objective>()
                    .Where(o => o.task_id == taskId)
                    .OrderBy(o => o.objective_order)
                    .ToListAsync();

                return objectives.Select(o => new TaskObjectiveDto
                {
                    ObjectiveId = o.objective_id,
                    TaskId = o.task_id,
                    ObjectiveType = o.objective_type,
                    ObjectiveTypeName = GetObjectiveTypeName(o.objective_type),
                    TargetId = o.target_id,
                    TargetAmount = o.target_amount,
                    ObjectiveOrder = o.objective_order ?? 0,
                    ObjectiveDescription = o.objective_description,
                    CreatedAt = o.created_at
                }).ToList();
            }
            catch (Exception ex)
            {
                throw new Exception($"获取任务目标失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 创建任务目标
        /// </summary>
        private async Task CreateTaskObjectivesAsync(string taskId, List<TaskObjectiveCreateDto> objectives)
        {
            try
            {
                var objectiveEntities = objectives.Select(o => new task_objective
                {
                    task_id = taskId,
                    objective_type = o.ObjectiveType,
                    target_id = o.TargetId,
                    target_amount = o.TargetAmount,
                    objective_order = o.ObjectiveOrder,
                    objective_description = o.ObjectiveDescription,
                    created_at = DateTime.Now
                }).ToList();

                await _dbService.Insertable(objectiveEntities).ExecuteCommandAsync();
            }
            catch (Exception ex)
            {
                throw new Exception($"创建任务目标失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 获取目标类型名称
        /// </summary>
        private string GetObjectiveTypeName(string objectiveType)
        {
            return objectiveType switch
            {
                "KILL_MONSTER" => "击杀怪物",
                "COLLECT_ITEM" => "收集道具",
                "REACH_LEVEL" => "达到等级",
                "VISIT_MAP" => "访问地图",
                "USE_SKILL" => "使用技能",
                "EQUIP_ITEM" => "装备道具",
                _ => objectiveType
            };
        }

        #endregion
    }
}
```

### 11.4 控制器实现步骤

#### 步骤1：创建 TaskConfigController.cs
```csharp
using Microsoft.AspNetCore.Mvc;
using BMS.Services.Interfaces;
using BMS.Models.DTOs;
using BMS.Models.Common;

namespace BMS.Controllers
{
    /// <summary>
    /// 任务配置管理控制器
    /// </summary>
    public class TaskConfigController : Controller
    {
        private readonly ITaskConfigService _taskConfigService;

        public TaskConfigController(ITaskConfigService taskConfigService)
        {
            _taskConfigService = taskConfigService;
        }

        /// <summary>
        /// 任务配置管理页面
        /// </summary>
        public IActionResult Index()
        {
            return View();
        }

        /// <summary>
        /// 获取任务配置列表
        /// </summary>
        [HttpPost("TaskConfig/GetList")]
        public async Task<IActionResult> GetList([FromBody] TaskConfigQueryDto queryDto)
        {
            try
            {
                Console.WriteLine($"TaskConfig.GetList 开始，参数: {System.Text.Json.JsonSerializer.Serialize(queryDto)}");

                if (queryDto == null)
                {
                    queryDto = new TaskConfigQueryDto { Page = 1, PageSize = 10 };
                }

                var result = await _taskConfigService.GetTaskConfigsAsync(queryDto);

                Console.WriteLine($"查询成功，返回 {result.Data?.Count ?? 0} 条记录");
                return Json(new { code = 200, data = result.Data, total = result.TotalCount });
            }
            catch (Exception ex)
            {
                Console.WriteLine($"TaskConfig.GetList 异常: {ex.Message}");
                return Json(new { code = 500, message = $"获取任务配置列表失败：{ex.Message}" });
            }
        }

        /// <summary>
        /// 根据ID获取任务配置
        /// </summary>
        [HttpGet("TaskConfig/GetById/{taskId}")]
        public async Task<IActionResult> GetById(string taskId)
        {
            try
            {
                var result = await _taskConfigService.GetTaskConfigByIdAsync(taskId);
                if (result == null)
                {
                    return Json(new { code = 404, message = "任务配置不存在" });
                }

                return Json(new { code = 200, data = result });
            }
            catch (Exception ex)
            {
                Console.WriteLine($"TaskConfig.GetById 异常: {ex.Message}");
                return Json(new { code = 500, message = $"获取任务配置失败：{ex.Message}" });
            }
        }

        /// <summary>
        /// 创建任务配置
        /// </summary>
        [HttpPost("TaskConfig/Create")]
        public async Task<IActionResult> Create([FromBody] TaskConfigCreateDto createDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    var errors = ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage);
                    return Json(new { code = 400, message = string.Join(", ", errors) });
                }

                var result = await _taskConfigService.CreateTaskConfigAsync(createDto);
                return Json(result.Success ?
                    new { code = 200, data = result.Data, message = result.Message } :
                    new { code = 400, message = result.Message });
            }
            catch (Exception ex)
            {
                Console.WriteLine($"TaskConfig.Create 异常: {ex.Message}");
                return Json(new { code = 500, message = $"创建任务配置失败：{ex.Message}" });
            }
        }

        /// <summary>
        /// 更新任务配置
        /// </summary>
        [HttpPost("TaskConfig/Update")]
        public async Task<IActionResult> Update([FromBody] TaskConfigCreateDto updateDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    var errors = ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage);
                    return Json(new { code = 400, message = string.Join(", ", errors) });
                }

                var result = await _taskConfigService.UpdateTaskConfigAsync(updateDto.TaskId, updateDto);
                return Json(result.Success ?
                    new { code = 200, data = result.Data, message = result.Message } :
                    new { code = 400, message = result.Message });
            }
            catch (Exception ex)
            {
                Console.WriteLine($"TaskConfig.Update 异常: {ex.Message}");
                return Json(new { code = 500, message = $"更新任务配置失败：{ex.Message}" });
            }
        }

        /// <summary>
        /// 删除任务配置
        /// </summary>
        [HttpPost("TaskConfig/Delete")]
        public async Task<IActionResult> Delete([FromBody] dynamic request)
        {
            try
            {
                string taskId = request.taskId;
                if (string.IsNullOrEmpty(taskId))
                {
                    return Json(new { code = 400, message = "任务ID不能为空" });
                }

                var result = await _taskConfigService.DeleteTaskConfigAsync(taskId);
                return Json(result.Success ?
                    new { code = 200, message = result.Message } :
                    new { code = 400, message = result.Message });
            }
            catch (Exception ex)
            {
                Console.WriteLine($"TaskConfig.Delete 异常: {ex.Message}");
                return Json(new { code = 500, message = $"删除任务配置失败：{ex.Message}" });
            }
        }

        /// <summary>
        /// 启用/禁用任务
        /// </summary>
        [HttpPost("TaskConfig/ToggleActive")]
        public async Task<IActionResult> ToggleActive([FromBody] dynamic request)
        {
            try
            {
                string taskId = request.taskId;
                bool isActive = request.isActive;

                if (string.IsNullOrEmpty(taskId))
                {
                    return Json(new { code = 400, message = "任务ID不能为空" });
                }

                var result = await _taskConfigService.ToggleTaskActiveAsync(taskId, isActive);
                return Json(result.Success ?
                    new { code = 200, message = result.Message } :
                    new { code = 400, message = result.Message });
            }
            catch (Exception ex)
            {
                Console.WriteLine($"TaskConfig.ToggleActive 异常: {ex.Message}");
                return Json(new { code = 500, message = $"切换任务状态失败：{ex.Message}" });
            }
        }

        /// <summary>
        /// 获取任务类型选项
        /// </summary>
        [HttpGet("TaskConfig/GetTaskTypeOptions")]
        public async Task<IActionResult> GetTaskTypeOptions()
        {
            try
            {
                var options = await _taskConfigService.GetTaskTypeOptionsAsync();
                return Json(new { code = 200, data = options });
            }
            catch (Exception ex)
            {
                Console.WriteLine($"TaskConfig.GetTaskTypeOptions 异常: {ex.Message}");
                return Json(new { code = 500, message = $"获取任务类型选项失败：{ex.Message}" });
            }
        }

        /// <summary>
        /// 获取任务选项
        /// </summary>
        [HttpGet("TaskConfig/GetTaskOptions")]
        public async Task<IActionResult> GetTaskOptions()
        {
            try
            {
                var options = await _taskConfigService.GetTaskOptionsAsync();
                return Json(new { code = 200, data = options });
            }
            catch (Exception ex)
            {
                Console.WriteLine($"TaskConfig.GetTaskOptions 异常: {ex.Message}");
                return Json(new { code = 500, message = $"获取任务选项失败：{ex.Message}" });
            }
        }
    }
}
```

### 11.5 依赖注入配置

在 `Program.cs` 中添加服务注册：

```csharp
// 添加任务管理服务
builder.Services.AddScoped<ITaskConfigService, TaskConfigService>();
builder.Services.AddScoped<IUserTaskService, UserTaskService>();
```

### 11.6 前端页面实现步骤

#### 步骤1：创建 Views/TaskConfig/Index.cshtml
```html
@{
    ViewData["Title"] = "任务配置管理";
}

<div class="content-wrapper">
    <!-- 页面标题 -->
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0">
                        <i class="fas fa-tasks mr-2"></i>任务配置管理
                    </h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="/">首页</a></li>
                        <li class="breadcrumb-item active">任务配置管理</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- 主要内容 -->
    <div class="content">
        <div class="container-fluid">
            <div id="taskConfigApp">
                <!-- 搜索条件 -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-search mr-1"></i>搜索条件
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="searchTaskName">任务名称</label>
                                    <input type="text" class="form-control" id="searchTaskName"
                                           v-model="queryForm.taskName" placeholder="请输入任务名称">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="searchTaskType">任务类型</label>
                                    <select class="form-control" id="searchTaskType" v-model="queryForm.taskType">
                                        <option value="">全部类型</option>
                                        <option v-for="option in taskTypeOptions"
                                                :key="option.value" :value="option.value">
                                            {{ option.label }}
                                        </option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="searchIsActive">激活状态</label>
                                    <select class="form-control" id="searchIsActive" v-model="queryForm.isActive">
                                        <option value="">全部状态</option>
                                        <option value="true">已激活</option>
                                        <option value="false">已禁用</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>&nbsp;</label>
                                    <div>
                                        <button type="button" class="btn btn-primary mr-2" @click="searchTasks">
                                            <i class="fas fa-search mr-1"></i>搜索
                                        </button>
                                        <button type="button" class="btn btn-secondary mr-2" @click="resetSearch">
                                            <i class="fas fa-redo mr-1"></i>重置
                                        </button>
                                        <button type="button" class="btn btn-success" @click="showAddModal">
                                            <i class="fas fa-plus mr-1"></i>新增任务
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 任务列表 -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-list mr-1"></i>任务配置列表
                        </h3>
                    </div>
                    <div class="card-body table-responsive p-0">
                        <table class="table table-hover text-nowrap">
                            <thead>
                                <tr>
                                    <th>任务ID</th>
                                    <th>任务名称</th>
                                    <th>任务类型</th>
                                    <th>是否可重复</th>
                                    <th>前置任务</th>
                                    <th>状态</th>
                                    <th>排序</th>
                                    <th>创建时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody v-if="loading">
                                <tr>
                                    <td colspan="9" class="text-center py-4">
                                        <i class="fas fa-spinner fa-spin mr-2"></i>加载中...
                                    </td>
                                </tr>
                            </tbody>
                            <tbody v-else-if="tasksList.length === 0">
                                <tr>
                                    <td colspan="9" class="text-center py-4 text-muted">
                                        <i class="fas fa-inbox mr-2"></i>暂无数据
                                    </td>
                                </tr>
                            </tbody>
                            <tbody v-else>
                                <tr v-for="task in tasksList" :key="task.taskId">
                                    <td>{{ task.taskId }}</td>
                                    <td>
                                        <strong>{{ task.taskName }}</strong>
                                        <br>
                                        <small class="text-muted">{{ task.taskDescription }}</small>
                                    </td>
                                    <td>
                                        <span class="badge badge-info">{{ task.taskTypeName }}</span>
                                    </td>
                                    <td>
                                        <span v-if="task.isRepeatable" class="badge badge-success">
                                            <i class="fas fa-check mr-1"></i>可重复
                                        </span>
                                        <span v-else class="badge badge-secondary">
                                            <i class="fas fa-times mr-1"></i>不可重复
                                        </span>
                                    </td>
                                    <td>{{ task.prerequisiteTaskName || '-' }}</td>
                                    <td>
                                        <span v-if="task.isActive" class="badge badge-success">
                                            <i class="fas fa-check mr-1"></i>已激活
                                        </span>
                                        <span v-else class="badge badge-danger">
                                            <i class="fas fa-times mr-1"></i>已禁用
                                        </span>
                                    </td>
                                    <td>{{ task.sortOrder }}</td>
                                    <td>{{ formatDateTime(task.createdAt) }}</td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <button type="button" class="btn btn-info btn-sm"
                                                    @click="viewDetail(task)" title="查看详情">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button type="button" class="btn btn-warning btn-sm"
                                                    @click="showEditModal(task)" title="编辑">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button v-if="task.isActive" type="button" class="btn btn-secondary btn-sm"
                                                    @click="toggleActive(task, false)" title="禁用">
                                                <i class="fas fa-pause"></i>
                                            </button>
                                            <button v-else type="button" class="btn btn-success btn-sm"
                                                    @click="toggleActive(task, true)" title="启用">
                                                <i class="fas fa-play"></i>
                                            </button>
                                            <button type="button" class="btn btn-danger btn-sm"
                                                    @click="deleteTask(task)" title="删除">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <!-- 分页 -->
                    <div class="card-footer" v-if="tasksList.length > 0">
                        <div class="row align-items-center">
                            <div class="col-sm-6">
                                <div class="dataTables_info">
                                    显示第 {{ (currentPage - 1) * pageSize + 1 }} 到 {{ Math.min(currentPage * pageSize, totalCount) }} 条记录，共 {{ totalCount }} 条记录
                                </div>
                            </div>
                            <div class="col-sm-6">
                                <div class="dataTables_paginate paging_simple_numbers float-right">
                                    <ul class="pagination">
                                        <li class="paginate_button page-item previous" :class="{ disabled: currentPage === 1 }">
                                            <a href="#" class="page-link" @click.prevent="changePage(currentPage - 1)">上一页</a>
                                        </li>
                                        <li v-for="page in visiblePages" :key="page" class="paginate_button page-item"
                                            :class="{ active: page === currentPage }">
                                            <a href="#" class="page-link" @click.prevent="changePage(page)">{{ page }}</a>
                                        </li>
                                        <li class="paginate_button page-item next" :class="{ disabled: currentPage === totalPages }">
                                            <a href="#" class="page-link" @click.prevent="changePage(currentPage + 1)">下一页</a>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        // 任务配置管理Vue应用
        Vue.createApp({
            data() {
                return {
                    // 加载状态
                    loading: false,
                    saving: false,

                    // 任务列表数据
                    tasksList: [],
                    totalCount: 0,
                    currentPage: 1,
                    pageSize: 10,

                    // 表单状态
                    isEdit: false,
                    selectedTask: null,

                    // 下拉选项
                    taskTypeOptions: [],
                    taskOptions: [],

                    // 查询表单
                    queryForm: {
                        taskName: '',
                        taskType: '',
                        isActive: ''
                    },

                    // 任务表单
                    taskForm: {
                        taskId: '',
                        taskName: '',
                        taskDescription: '',
                        taskType: 0,
                        isRepeatable: false,
                        prerequisiteTask: '',
                        requiredPet: '',
                        rewardConfig: '',
                        isNetworkTask: false,
                        isActive: true,
                        sortOrder: 0,
                        objectives: []
                    }
                };
            },

            computed: {
                // 总页数
                totalPages() {
                    return Math.ceil(this.totalCount / this.pageSize);
                },

                // 可见页码
                visiblePages() {
                    const total = this.totalPages;
                    const current = this.currentPage;
                    const delta = 2;
                    const pages = [];

                    for (let i = Math.max(2, current - delta); i <= Math.min(total - 1, current + delta); i++) {
                        pages.push(i);
                    }

                    if (current - delta > 2) {
                        pages.unshift("...");
                    }
                    if (current + delta < total - 1) {
                        pages.push("...");
                    }

                    pages.unshift(1);
                    if (total > 1) {
                        pages.push(total);
                    }

                    return pages.filter((page, index, arr) => arr.indexOf(page) === index);
                }
            },

            methods: {
                // 加载任务列表
                async loadTasks() {
                    try {
                        this.loading = true;

                        const requestData = {
                            TaskName: this.queryForm.taskName || '',
                            TaskType: this.queryForm.taskType ? parseInt(this.queryForm.taskType) : null,
                            IsActive: this.queryForm.isActive === '' ? null : (this.queryForm.isActive === 'true'),
                            Page: this.currentPage,
                            PageSize: this.pageSize
                        };

                        const response = await axios.post('/TaskConfig/GetList', requestData);
                        if (response.data.code === 200) {
                            this.tasksList = response.data.data || [];
                            this.totalCount = response.data.total || 0;
                        } else {
                            console.error('获取任务列表失败：', response.data.message);
                        }
                    } catch (error) {
                        console.error('获取任务列表失败：', error);
                    } finally {
                        this.loading = false;
                    }
                },

                // 搜索任务
                searchTasks() {
                    this.currentPage = 1;
                    this.loadTasks();
                },

                // 重置搜索
                resetSearch() {
                    Object.assign(this.queryForm, {
                        taskName: '',
                        taskType: '',
                        isActive: ''
                    });
                    this.searchTasks();
                },

                // 分页
                changePage(page) {
                    if (page >= 1 && page <= this.totalPages) {
                        this.currentPage = page;
                        this.loadTasks();
                    }
                },

                // 显示新增模态框
                showAddModal() {
                    this.isEdit = false;
                    this.taskForm = {
                        taskId: '',
                        taskName: '',
                        taskDescription: '',
                        taskType: 0,
                        isRepeatable: false,
                        prerequisiteTask: '',
                        requiredPet: '',
                        rewardConfig: '',
                        isNetworkTask: false,
                        isActive: true,
                        sortOrder: 0,
                        objectives: []
                    };
                    $('#taskModal').modal('show');
                },

                // 显示编辑模态框
                showEditModal(task) {
                    this.isEdit = true;
                    this.taskForm = {
                        taskId: task.taskId,
                        taskName: task.taskName,
                        taskDescription: task.taskDescription,
                        taskType: task.taskType,
                        isRepeatable: task.isRepeatable,
                        prerequisiteTask: task.prerequisiteTask,
                        requiredPet: task.requiredPet,
                        rewardConfig: task.rewardConfig,
                        isNetworkTask: task.isNetworkTask,
                        isActive: task.isActive,
                        sortOrder: task.sortOrder,
                        objectives: task.objectives || []
                    };
                    $('#taskModal').modal('show');
                },

                // 保存任务
                async saveTask() {
                    try {
                        this.saving = true;

                        const url = this.isEdit ? '/TaskConfig/Update' : '/TaskConfig/Create';
                        const response = await axios.post(url, this.taskForm);

                        if (response.data.code === 200) {
                            $('#taskModal').modal('hide');
                            this.loadTasks();
                            alert(response.data.message || (this.isEdit ? '更新成功' : '创建成功'));
                        } else {
                            alert(response.data.message || '操作失败');
                        }
                    } catch (error) {
                        console.error('保存任务失败：', error);
                        alert('保存失败，请重试');
                    } finally {
                        this.saving = false;
                    }
                },

                // 删除任务
                async deleteTask(task) {
                    if (!confirm(`确定要删除任务"${task.taskName}"吗？此操作无法撤销！`)) {
                        return;
                    }

                    try {
                        const response = await axios.post('/TaskConfig/Delete', { taskId: task.taskId });
                        if (response.data.code === 200) {
                            this.loadTasks();
                            alert('删除成功');
                        } else {
                            alert(response.data.message || '删除失败');
                        }
                    } catch (error) {
                        console.error('删除任务失败：', error);
                        alert('删除失败，请重试');
                    }
                },

                // 切换任务状态
                async toggleActive(task, isActive) {
                    try {
                        const response = await axios.post('/TaskConfig/ToggleActive', {
                            taskId: task.taskId,
                            isActive: isActive
                        });

                        if (response.data.code === 200) {
                            this.loadTasks();
                            alert(response.data.message || '状态切换成功');
                        } else {
                            alert(response.data.message || '状态切换失败');
                        }
                    } catch (error) {
                        console.error('切换任务状态失败：', error);
                        alert('状态切换失败，请重试');
                    }
                },

                // 查看详情
                async viewDetail(task) {
                    try {
                        const response = await axios.get(`/TaskConfig/GetById/${task.taskId}`);
                        if (response.data.code === 200) {
                            this.selectedTask = response.data.data;
                            $('#taskDetailModal').modal('show');
                        } else {
                            alert(response.data.message || '获取任务详情失败');
                        }
                    } catch (error) {
                        console.error('获取任务详情失败：', error);
                        alert('获取任务详情失败，请重试');
                    }
                },

                // 格式化日期时间
                formatDateTime(dateStr) {
                    if (!dateStr) return '';
                    const date = new Date(dateStr);
                    return date.toLocaleString('zh-CN');
                },

                // 加载下拉选项
                async loadOptions() {
                    try {
                        // 加载任务类型选项
                        const typeResponse = await axios.get('/TaskConfig/GetTaskTypeOptions');
                        if (typeResponse.data.code === 200) {
                            this.taskTypeOptions = typeResponse.data.data;
                        }

                        // 加载任务选项
                        const taskResponse = await axios.get('/TaskConfig/GetTaskOptions');
                        if (taskResponse.data.code === 200) {
                            this.taskOptions = taskResponse.data.data;
                        }
                    } catch (error) {
                        console.error('加载选项失败：', error);
                    }
                }
            },

            async mounted() {
                await this.loadOptions();
                await this.loadTasks();
            }
        }).mount('#taskConfigApp');
    </script>
}
```

## 12. 实施检查清单

### 12.1 数据库准备
- [ ] 确认数据库中已存在所有任务相关表
- [ ] 检查表结构是否与文档一致
- [ ] 验证外键约束是否正确设置

### 12.2 后端开发
- [ ] 创建所有实体模型类
- [ ] 创建所有DTO类
- [ ] 实现服务接口和实现类
- [ ] 创建控制器
- [ ] 配置依赖注入
- [ ] 添加必要的验证和错误处理

### 12.3 前端开发
- [ ] 创建任务配置管理页面
- [ ] 实现Vue.js应用逻辑
- [ ] 添加必要的模态框和表单
- [ ] 实现分页和搜索功能
- [ ] 添加用户友好的交互反馈

### 12.4 测试验证
- [ ] 单元测试：服务层方法
- [ ] 集成测试：API接口
- [ ] 功能测试：前端页面操作
- [ ] 性能测试：大数据量场景
- [ ] 安全测试：权限和数据验证

### 12.5 部署上线
- [ ] 数据库迁移脚本
- [ ] 配置文件更新
- [ ] 权限和菜单配置
- [ ] 用户培训文档
- [ ] 监控和日志配置

## 13. 扩展功能规划

### 13.1 短期扩展（1-2周）
- 任务奖励系统集成
- 任务进度实时更新
- 任务统计报表

### 13.2 中期扩展（1-2月）
- 任务链和分支任务
- 动态任务生成
- 任务模板系统

### 13.3 长期扩展（3-6月）
- 任务AI推荐
- 社交任务系统
- 任务成就系统

---

*本文档提供了完整的任务管理功能实施指南，包含从数据库设计到前端实现的所有细节。建议按照实施计划分阶段进行开发，确保每个阶段都经过充分测试后再进入下一阶段。*
