﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace BMS.Models.Entities
{
    ///<summary>
    ///道具丢弃记录表
    ///</summary>
    [SugarTable("item_discard_log")]
    public partial class item_discard_log
    {
           public item_discard_log(){


           }
           /// <summary>
           /// Desc:记录ID（主键，自增）
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true,IsIdentity=true)]
           public int log_id {get;set;}

           /// <summary>
           /// Desc:用户ID
           /// Default:
           /// Nullable:False
           /// </summary>           
           public int user_id {get;set;}

           /// <summary>
           /// Desc:原道具序号
           /// Default:
           /// Nullable:False
           /// </summary>           
           public int original_item_seq {get;set;}

           /// <summary>
           /// Desc:道具ID
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string item_id {get;set;}

           /// <summary>
           /// Desc:道具名称（冗余存储，方便查询）
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string item_name {get;set;}

           /// <summary>
           /// Desc:丢弃的道具数量
           /// Default:1
           /// Nullable:False
           /// </summary>           
           public int discard_count {get;set;}

           /// <summary>
           /// Desc:丢弃原因
           /// Default:用户主动丢弃
           /// Nullable:True
           /// </summary>           
           public string? discard_reason {get;set;}

           /// <summary>
           /// Desc:丢弃时间
           /// Default:CURRENT_TIMESTAMP
           /// Nullable:False
           /// </summary>           
           public DateTime discard_time {get;set;}

           /// <summary>
           /// Desc:是否已找回（0=未找回，1=已找回）
           /// Default:true
           /// Nullable:False
           /// </summary>           
           public bool is_recovered {get;set;}

           /// <summary>
           /// Desc:找回时间
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? recover_time {get;set;}

           /// <summary>
           /// Desc:备注
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? remark {get;set;}

    }
}
