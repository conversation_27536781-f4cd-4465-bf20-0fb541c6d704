@{
    ViewData["Title"] = "宠物进化配置管理";
}

<!-- 科幻背景 -->
<div class="cyber-bg"></div>



<!-- 科幻宠物进化配置管理应用容器 -->
<div id="petEvolutionConfigApp">
    <!-- 科幻页面标题 -->
    <div class="container-fluid py-4">
        <div class="row mb-4">
            <div class="col-12">
                <div class="cyber-card">
                    <div class="cyber-card-header">
                        <div class="cyber-icon">🧬</div>
                        <h1 class="cyber-card-title">宠物进化配置管理</h1>
                        @* <small class="text-muted">Vue状态: {{ loading ? '加载中' : '已就绪' }}</small> *@
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 科幻主要内容 -->
    <div class="container-fluid">

        <!-- 科幻搜索条件 -->
        <div class="cyber-card">
            <div class="cyber-card-header">
                <div class="cyber-icon">🔍</div>
                <h3 class="cyber-card-title">搜索条件</h3>
            </div>
            <div class="cyber-card-body">
                <div class="row">
                    <div class="col-md-3">
                        <div class="cyber-form-group">
                            <label class="cyber-form-label" for="search-pet-no">宠物编号</label>
                            <input type="number" class="cyber-form-control" id="search-pet-no" v-model.number="queryForm.petNo" placeholder="请输入宠物编号">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="cyber-form-group">
                            <label class="cyber-form-label" for="search-pet-name">宠物名称</label>
                            <input type="text" class="cyber-form-control" id="search-pet-name" v-model="queryForm.petName" placeholder="请输入宠物名称">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="cyber-form-group">
                            <label class="cyber-form-label" for="search-evolution-type">进化类型</label>
                            <div class="cyber-select-wrapper">
                                <select class="cyber-form-control" id="search-evolution-type" v-model="queryForm.evolutionType">
                                    <option value="">全部类型</option>
                                    <option value="A">A路线</option>
                                    <option value="B">B路线</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="cyber-form-group">
                            <label class="cyber-form-label" for="search-target-pet-no">目标宠物编号</label>
                            <input type="number" class="cyber-form-control" id="search-target-pet-no" v-model.number="queryForm.targetPetNo" placeholder="请输入目标宠物编号">
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-3">
                        <div class="cyber-form-group">
                            <label class="cyber-form-label" for="search-target-pet-name">目标宠物名称</label>
                            <input type="text" class="cyber-form-control" id="search-target-pet-name" v-model="queryForm.targetPetName" placeholder="请输入目标宠物名称">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="cyber-form-group">
                            <label class="cyber-form-label" for="search-is-active">激活状态</label>
                            <div class="cyber-select-wrapper">
                                <select class="cyber-form-control" id="search-is-active" v-model="queryForm.isActive">
                                    <option value="">全部状态</option>
                                    <option v-bind:value="true">已激活</option>
                                    <option v-bind:value="false">已禁用</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="cyber-form-group">
                            <label class="cyber-form-label" for="search-min-level">最小等级要求</label>
                            <input type="number" class="cyber-form-control" id="search-min-level" v-model.number="queryForm.minRequiredLevel" placeholder="最小等级">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="cyber-form-group">
                            <label class="cyber-form-label" for="search-max-level">最大等级要求</label>
                            <input type="number" class="cyber-form-control" id="search-max-level" v-model.number="queryForm.maxRequiredLevel" placeholder="最大等级">
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-12">
                        <div class="cyber-form-group">
                            <label class="cyber-form-label">&nbsp;</label>
                            <div class="d-flex gap-2 flex-wrap">
                                <button type="button" class="cyber-btn cyber-btn-primary" v-on:click="searchConfigs" v-bind:disabled="loading">
                                    <i class="fas fa-search"></i> 搜索
                                </button>
                                <button type="button" class="cyber-btn cyber-btn-outline" v-on:click="resetQuery">
                                    <i class="fas fa-redo"></i> 重置
                                </button>
                                <button type="button" class="cyber-btn cyber-btn-success" v-on:click="showAddModal">
                                    <i class="fas fa-plus"></i> 新增配置
                                </button>
                                <button type="button" class="cyber-btn cyber-btn-danger" v-on:click="batchDelete" v-bind:disabled="selectedIds.length === 0">
                                    <i class="fas fa-trash"></i> 批量删除
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 科幻进化配置列表 -->
        <div class="cyber-card">
            <div class="cyber-card-header">
                <div class="cyber-icon">📋</div>
                <h3 class="cyber-card-title">进化配置列表</h3>
            </div>
            <div class="cyber-card-body">
                <div class="cyber-table-container">
                    <table class="cyber-table">
                        <thead class="cyber-thead">
                            <tr>
                                <th class="cyber-th" width="50">
                                    <input type="checkbox" v-model="selectAll" v-on:change="toggleSelectAll">
                                </th>
                                <th class="cyber-th">ID</th>
                                <th class="cyber-th">宠物信息</th>
                                <th class="cyber-th">进化类型</th>
                                <th class="cyber-th">目标宠物</th>
                                <th class="cyber-th">等级要求</th>
                                <th class="cyber-th">所需道具</th>
                                <th class="cyber-th">消耗金币</th>
                                <th class="cyber-th">成长加成</th>
                                <th class="cyber-th">成功率</th>
                                <th class="cyber-th">状态</th>
                                <th class="cyber-th">创建时间</th>
                                <th class="cyber-th">操作</th>
                            </tr>
                        </thead>
                        <tbody class="cyber-tbody" v-if="loading">
                            <tr class="cyber-tr">
                                <td colspan="13" class="cyber-td text-center py-4">
                                    <i class="fas fa-spinner fa-spin me-2"></i>加载中...
                                </td>
                            </tr>
                        </tbody>
                        <tbody class="cyber-tbody" v-else-if="configsList.length === 0">
                            <tr class="cyber-tr">
                                <td colspan="13" class="cyber-td text-center py-4 text-muted">
                                    <i class="fas fa-inbox me-2"></i>暂无数据
                                </td>
                            </tr>
                        </tbody>
                        <tbody class="cyber-tbody" v-else>
                            <tr class="cyber-tr" v-for="config in configsList" v-bind:key="config.id">
                                <td class="cyber-td">
                                    <input type="checkbox" v-model="selectedIds" v-bind:value="config.id">
                                </td>
                                <td class="cyber-td">{{ config.id }}</td>
                                <td class="cyber-td">
                                    <div>
                                        <i class="fas fa-paw me-1" v-bind:style="{ color: getAttributeColor(config.petAttribute) }"></i>
                                        <strong>{{ config.petName }}</strong> ({{ config.petAttribute }})
                                        <br>
                                        <small class="text-muted">编号: {{ config.petNo }}</small>
                                    </div>
                                </td>
                                <td class="cyber-td">
                                    <span class="badge" v-bind:style="{ background: config.evolutionType === 'A' ? 'var(--cyber-blue)' : 'var(--cyber-purple)' }">
                                        {{ config.evolutionType }}路线
                                    </span>
                                </td>
                                <td class="cyber-td">
                                    <div>
                                        <i class="fas fa-star me-1" v-bind:style="{ color: getAttributeColor(config.targetPetAttribute) }"></i>
                                        <strong>{{ config.targetPetName }}</strong> ({{ config.targetPetAttribute }})
                                        <br>
                                        <small class="text-muted">编号: {{ config.targetPetNo }}</small>
                                    </div>
                                </td>
                                <td class="cyber-td">{{ config.requiredLevel }}级</td>
                                <td class="cyber-td">
                                    <div v-if="config.requiredItemId">
                                        <i class="fas fa-gem me-1" style="color: var(--cyber-gold);"></i>
                                        {{ config.requiredItemName || config.requiredItemId }}
                                        <br>
                                        <small class="text-muted">数量: {{ config.requiredItemCount }}</small>
                                    </div>
                                    <span v-else class="text-muted">无</span>
                                </td>
                                <td class="cyber-td">
                                    <i class="fas fa-coins me-1" style="color: var(--cyber-gold);"></i>
                                    {{ config.costGold.toLocaleString() }}
                                </td>
                                <td class="cyber-td">{{ config.growthMin }} - {{ config.growthMax }}</td>
                                <td class="cyber-td">
                                    <span class="badge" v-bind:style="{ background: getSuccessRateColor(config.successRate) }">
                                        {{ config.successRate }}%
                                    </span>
                                </td>
                                <td class="cyber-td">
                                    <span class="badge" v-bind:class="config.isActive ? 'badge-success' : 'badge-secondary'">
                                        {{ config.isActive ? '已激活' : '已禁用' }}
                                    </span>
                                </td>
                                <td class="cyber-td">{{ formatDateTime(config.createTime) }}</td>
                                <td class="cyber-td">
                                    <div class="d-flex gap-1 flex-wrap">
                                        <button type="button" class="cyber-btn cyber-btn-sm" v-on:click="viewDetail(config)" title="查看详情" style="background: linear-gradient(135deg, var(--cyber-blue), var(--cyber-purple));">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button type="button" class="cyber-btn cyber-btn-sm cyber-btn-warning" v-on:click="editConfig(config)" title="编辑">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button type="button" class="cyber-btn cyber-btn-sm" v-bind:class="config.isActive ? 'cyber-btn-outline' : 'cyber-btn-success'" v-on:click="toggleActive(config)" v-bind:title="config.isActive ? '禁用' : '启用'">
                                            <i v-bind:class="config.isActive ? 'fas fa-pause' : 'fas fa-play'"></i>
                                        </button>
                                        <button type="button" class="cyber-btn cyber-btn-sm cyber-btn-danger" v-on:click="deleteConfig(config)" title="删除">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- 科幻分页 -->
                <div class="mt-3" v-if="totalCount > 0">
                    <div class="row align-items-center">
                        <div class="col-sm-6">
                            <div class="text-muted">
                                显示第 {{ (currentPage - 1) * pageSize + 1 }} 到 {{ Math.min(currentPage * pageSize, totalCount) }} 条记录，共 {{ totalCount }} 条记录
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <ul class="cyber-pagination justify-content-end">
                                <li class="page-item" v-bind:class="{ disabled: currentPage === 1 }">
                                    <a href="#" class="page-link" v-on:click.prevent="changePage(currentPage - 1)">上一页</a>
                                </li>
                                <li class="page-item" v-for="page in visiblePages" v-bind:key="page" v-bind:class="{ active: page === currentPage }">
                                    <a href="#" class="page-link" v-on:click.prevent="changePage(page)">{{ page }}</a>
                                </li>
                                <li class="page-item" v-bind:class="{ disabled: currentPage === totalPages }">
                                    <a href="#" class="page-link" v-on:click.prevent="changePage(currentPage + 1)">下一页</a>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </div>

    <!-- 科幻新增/编辑进化配置模态框 -->
    <div class="modal fade cyber-modal" id="configModal" tabindex="-1" role="dialog" aria-labelledby="configModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="configModalLabel">
                        <i class="fas fa-dna me-2"></i>{{ modalTitle }}
                    </h5>
                    <button type="button" class="btn-close" v-on:click="closeModal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="cyber-form-group">
                                    <label class="cyber-form-label" for="modalPetSelect">宠物 <span class="text-danger">*</span></label>
                                    <div class="cyber-select-wrapper">
                                        <select class="cyber-form-control" id="modalPetSelect" v-model="configForm.petNo">
                                            <option value="">请选择宠物</option>
                                            <option v-for="pet in petOptions" v-bind:key="pet.value" v-bind:value="pet.value">
                                                {{ pet.label }}
                                            </option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="cyber-form-group">
                                    <label class="cyber-form-label" for="modalEvolutionType">进化类型 <span class="text-danger">*</span></label>
                                    <div class="cyber-select-wrapper">
                                        <select class="cyber-form-control" id="modalEvolutionType" v-model="configForm.evolutionType">
                                            <option value="">请选择进化类型</option>
                                            <option value="A">A路线</option>
                                            <option value="B">B路线</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="cyber-form-group">
                                    <label class="cyber-form-label" for="modalTargetPetSelect">目标宠物 <span class="text-danger">*</span></label>
                                    <div class="cyber-select-wrapper">
                                        <select class="cyber-form-control" id="modalTargetPetSelect" v-model="configForm.targetPetNo">
                                            <option value="">请选择目标宠物</option>
                                            <option v-for="pet in petOptions" v-bind:key="pet.value" v-bind:value="pet.value">
                                                {{ pet.label }}
                                            </option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="cyber-form-group">
                                    <label class="cyber-form-label" for="modalRequiredLevel">所需等级</label>
                                    <input type="number" class="cyber-form-control" id="modalRequiredLevel" v-model="configForm.requiredLevel" min="1" max="999" placeholder="40">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="cyber-form-group">
                                    <label class="cyber-form-label" for="modalItemSelect">所需道具</label>
                                    <div class="cyber-select-wrapper">
                                        <select class="cyber-form-control" id="modalItemSelect" v-model="configForm.requiredItemId">
                                            <option value="">无需道具</option>
                                            <option v-for="item in itemOptions" v-bind:key="item.value" v-bind:value="item.value">
                                                {{ item.label }}
                                            </option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="cyber-form-group">
                                    <label class="cyber-form-label" for="modalRequiredItemCount">道具数量</label>
                                    <input type="number" class="cyber-form-control" id="modalRequiredItemCount" v-model="configForm.requiredItemCount" min="0" placeholder="1">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="cyber-form-group">
                                    <label class="cyber-form-label" for="modalCostGold">消耗金币</label>
                                    <input type="number" class="cyber-form-control" id="modalCostGold" v-model="configForm.costGold" min="0" placeholder="1000">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="cyber-form-group">
                                    <label class="cyber-form-label" for="modalSuccessRate">成功率(%)</label>
                                    <input type="number" class="cyber-form-control" id="modalSuccessRate" v-model="configForm.successRate" min="0" max="100" step="0.01" placeholder="100.00">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="cyber-form-group">
                                    <label class="cyber-form-label" for="modalGrowthMin">最小成长加成</label>
                                    <input type="number" class="cyber-form-control" id="modalGrowthMin" v-model="configForm.growthMin" min="0" max="9.999" step="0.001" placeholder="0.100">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="cyber-form-group">
                                    <label class="cyber-form-label" for="modalGrowthMax">最大成长加成</label>
                                    <input type="number" class="cyber-form-control" id="modalGrowthMax" v-model="configForm.growthMax" min="0" max="9.999" step="0.001" placeholder="0.500">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="cyber-form-group">
                                    <label class="cyber-form-label" for="modalDescription">说明</label>
                                    <input type="text" class="cyber-form-control" id="modalDescription" v-model="configForm.description" maxlength="50" placeholder="请输入说明">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-12">
                                <div class="cyber-form-group">
                                    <div class="cyber-checkbox">
                                        <input type="checkbox" id="modalIsActive" v-model="configForm.isActive">
                                        <label for="modalIsActive" class="cyber-checkbox-label">启用此进化配置</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="cyber-btn cyber-btn-outline" v-on:click="closeModal">
                        <i class="fas fa-times me-1"></i>取消
                    </button>
                    <button type="button" class="cyber-btn cyber-btn-primary" v-on:click="saveConfig" v-bind:disabled="saving">
                        <i v-if="saving" class="fas fa-spinner fa-spin me-1"></i>
                        <i v-else class="fas fa-save me-1"></i>
                        {{ saving ? '保存中...' : '保存' }}
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 科幻详情模态框 -->
    <div class="modal fade cyber-modal" id="detailModal" tabindex="-1" role="dialog" aria-labelledby="detailModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="detailModalLabel">
                        <i class="fas fa-dna me-2"></i>进化配置详情
                    </h5>
                    <button type="button" class="btn-close" v-on:click="closeDetailModal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div v-if="selectedConfig" class="cyber-detail-container">
                        <!-- 宠物信息和目标宠物信息 -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <div class="cyber-info-card">
                                    <div class="cyber-info-header">
                                        <i class="fas fa-paw me-2" v-bind:style="{ color: getAttributeColor(selectedConfig.petAttribute) }"></i>
                                        <h6 class="cyber-info-title">宠物信息</h6>
                                    </div>
                                    <div class="cyber-info-body">
                                        <div class="cyber-info-item">
                                            <span class="cyber-info-label">名称:</span>
                                            <span class="cyber-info-value">{{ selectedConfig.petName }}</span>
                                        </div>
                                        <div class="cyber-info-item">
                                            <span class="cyber-info-label">编号:</span>
                                            <span class="cyber-info-value">{{ selectedConfig.petNo }}</span>
                                        </div>
                                        <div class="cyber-info-item">
                                            <span class="cyber-info-label">属性:</span>
                                            <span class="cyber-info-value" v-bind:style="{ color: getAttributeColor(selectedConfig.petAttribute) }">
                                                {{ selectedConfig.petAttribute }}
                                            </span>
                                        </div>
                                        <div class="cyber-info-item">
                                            <span class="cyber-info-label">进化类型:</span>
                                            <span class="cyber-info-value">
                                                <span class="badge" v-bind:style="{ background: selectedConfig.evolutionType === 'A' ? 'var(--cyber-blue)' : 'var(--cyber-purple)' }">
                                                    {{ selectedConfig.evolutionType }}路线
                                                </span>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="cyber-info-card">
                                    <div class="cyber-info-header">
                                        <i class="fas fa-star me-2" v-bind:style="{ color: getAttributeColor(selectedConfig.targetPetAttribute) }"></i>
                                        <h6 class="cyber-info-title">目标宠物信息</h6>
                                    </div>
                                    <div class="cyber-info-body">
                                        <div class="cyber-info-item">
                                            <span class="cyber-info-label">名称:</span>
                                            <span class="cyber-info-value">{{ selectedConfig.targetPetName }}</span>
                                        </div>
                                        <div class="cyber-info-item">
                                            <span class="cyber-info-label">编号:</span>
                                            <span class="cyber-info-value">{{ selectedConfig.targetPetNo }}</span>
                                        </div>
                                        <div class="cyber-info-item">
                                            <span class="cyber-info-label">属性:</span>
                                            <span class="cyber-info-value" v-bind:style="{ color: getAttributeColor(selectedConfig.targetPetAttribute) }">
                                                {{ selectedConfig.targetPetAttribute }}
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 进化条件和进化效果 -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <div class="cyber-info-card cyber-warning">
                                    <div class="cyber-info-header">
                                        <i class="fas fa-exclamation-triangle me-2" style="color: var(--cyber-gold);"></i>
                                        <h6 class="cyber-info-title">进化条件</h6>
                                    </div>
                                    <div class="cyber-info-body">
                                        <div class="cyber-info-item">
                                            <span class="cyber-info-label">所需等级:</span>
                                            <span class="cyber-info-value">{{ selectedConfig.requiredLevel }}级</span>
                                        </div>
                                        <div class="cyber-info-item">
                                            <span class="cyber-info-label">所需道具:</span>
                                            <span class="cyber-info-value">
                                                <i class="fas fa-gem me-1" style="color: var(--cyber-gold);" v-if="selectedConfig.requiredItemName || selectedConfig.requiredItemId"></i>
                                                {{ selectedConfig.requiredItemName || selectedConfig.requiredItemId || '无' }}
                                            </span>
                                        </div>
                                        <div class="cyber-info-item">
                                            <span class="cyber-info-label">道具数量:</span>
                                            <span class="cyber-info-value">{{ selectedConfig.requiredItemCount }}</span>
                                        </div>
                                        <div class="cyber-info-item">
                                            <span class="cyber-info-label">消耗金币:</span>
                                            <span class="cyber-info-value">
                                                <i class="fas fa-coins me-1" style="color: var(--cyber-gold);"></i>
                                                {{ formatNumber(selectedConfig.costGold) }}
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="cyber-info-card cyber-success">
                                    <div class="cyber-info-header">
                                        <i class="fas fa-chart-line me-2" style="color: var(--cyber-green);"></i>
                                        <h6 class="cyber-info-title">进化效果</h6>
                                    </div>
                                    <div class="cyber-info-body">
                                        <div class="cyber-info-item">
                                            <span class="cyber-info-label">成功率:</span>
                                            <span class="cyber-info-value">
                                                <span class="badge" v-bind:style="{ background: getSuccessRateColor(selectedConfig.successRate) }">
                                                    {{ selectedConfig.successRate }}%
                                                </span>
                                            </span>
                                        </div>
                                        <div class="cyber-info-item">
                                            <span class="cyber-info-label">最小成长加成:</span>
                                            <span class="cyber-info-value">{{ selectedConfig.growthMin }}</span>
                                        </div>
                                        <div class="cyber-info-item">
                                            <span class="cyber-info-label">最大成长加成:</span>
                                            <span class="cyber-info-value">{{ selectedConfig.growthMax }}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 配置信息 -->
                        <div class="row">
                            <div class="col-12">
                                <div class="cyber-info-card cyber-info">
                                    <div class="cyber-info-header">
                                        <i class="fas fa-cog me-2" style="color: var(--cyber-blue);"></i>
                                        <h6 class="cyber-info-title">配置信息</h6>
                                    </div>
                                    <div class="cyber-info-body">
                                        <div class="row">
                                            <div class="col-md-3">
                                                <div class="cyber-info-item">
                                                    <span class="cyber-info-label">配置ID:</span>
                                                    <span class="cyber-info-value">{{ selectedConfig.id }}</span>
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="cyber-info-item">
                                                    <span class="cyber-info-label">激活状态:</span>
                                                    <span class="cyber-info-value">
                                                        <span class="badge" v-bind:class="selectedConfig.isActive ? 'badge-success' : 'badge-secondary'">
                                                            <i v-bind:class="selectedConfig.isActive ? 'fas fa-check' : 'fas fa-times'" class="me-1"></i>
                                                            {{ selectedConfig.isActive ? '已激活' : '已禁用' }}
                                                        </span>
                                                    </span>
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="cyber-info-item">
                                                    <span class="cyber-info-label">说明:</span>
                                                    <span class="cyber-info-value">{{ selectedConfig.description || '无' }}</span>
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="cyber-info-item">
                                                    <span class="cyber-info-label">创建时间:</span>
                                                    <span class="cyber-info-value">{{ formatDateTime(selectedConfig.createTime) }}</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="cyber-btn cyber-btn-outline" v-on:click="closeDetailModal">
                        <i class="fas fa-times me-1"></i>关闭
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    /* 科幻详情卡片样式 */
    .cyber-detail-container {
        padding: 0;
    }

    .cyber-info-card {
        background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
        border: 1px solid rgba(102, 126, 234, 0.3);
        border-radius: 12px;
        padding: 0;
        margin-bottom: 1rem;
        backdrop-filter: blur(10px);
        transition: all 0.3s ease;
        height: 100%;
    }

    .cyber-info-card:hover {
        border-color: rgba(102, 126, 234, 0.6);
        box-shadow: 0 8px 32px rgba(102, 126, 234, 0.2);
        transform: translateY(-2px);
    }

    .cyber-info-card.cyber-warning {
        background: linear-gradient(135deg, rgba(255, 193, 7, 0.1) 0%, rgba(255, 152, 0, 0.1) 100%);
        border-color: rgba(255, 193, 7, 0.3);
    }

    .cyber-info-card.cyber-warning:hover {
        border-color: rgba(255, 193, 7, 0.6);
        box-shadow: 0 8px 32px rgba(255, 193, 7, 0.2);
    }

    .cyber-info-card.cyber-success {
        background: linear-gradient(135deg, rgba(40, 167, 69, 0.1) 0%, rgba(25, 135, 84, 0.1) 100%);
        border-color: rgba(40, 167, 69, 0.3);
    }

    .cyber-info-card.cyber-success:hover {
        border-color: rgba(40, 167, 69, 0.6);
        box-shadow: 0 8px 32px rgba(40, 167, 69, 0.2);
    }

    .cyber-info-card.cyber-info {
        background: linear-gradient(135deg, rgba(13, 202, 240, 0.1) 0%, rgba(13, 110, 253, 0.1) 100%);
        border-color: rgba(13, 202, 240, 0.3);
    }

    .cyber-info-card.cyber-info:hover {
        border-color: rgba(13, 202, 240, 0.6);
        box-shadow: 0 8px 32px rgba(13, 202, 240, 0.2);
    }

    .cyber-info-header {
        background: linear-gradient(135deg, rgba(102, 126, 234, 0.2) 0%, rgba(118, 75, 162, 0.2) 100%);
        border-bottom: 1px solid rgba(102, 126, 234, 0.3);
        padding: 12px 16px;
        border-radius: 12px 12px 0 0;
        display: flex;
        align-items: center;
    }

    .cyber-info-title {
        margin: 0;
        font-size: 14px;
        font-weight: 600;
        color: var(--cyber-text);
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .cyber-info-body {
        padding: 16px;
    }

    .cyber-info-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 0;
        border-bottom: 1px solid rgba(102, 126, 234, 0.1);
    }

    .cyber-info-item:last-child {
        border-bottom: none;
    }

    .cyber-info-label {
        font-weight: 500;
        color: var(--cyber-text-muted);
        font-size: 13px;
        min-width: 80px;
        text-align: left;
    }

    .cyber-info-value {
        font-weight: 600;
        color: var(--cyber-text);
        font-size: 14px;
        text-align: right;
        flex: 1;
        margin-left: 12px;
    }

    /* 详情模态框特殊样式 */
    #detailModal .modal-dialog {
        max-width: 900px;
    }

    #detailModal .modal-header {
        background: linear-gradient(135deg, var(--cyber-blue) 0%, var(--cyber-purple) 100%);
        color: white;
        border-bottom: none;
        border-radius: 12px 12px 0 0;
    }

    #detailModal .modal-title {
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 1px;
    }

    #detailModal .btn-close {
        filter: invert(1);
        opacity: 0.8;
    }

    #detailModal .btn-close:hover {
        opacity: 1;
    }

    #detailModal .modal-body {
        background: var(--cyber-bg);
        padding: 24px;
    }

    #detailModal .modal-footer {
        background: var(--cyber-bg);
        border-top: 1px solid rgba(102, 126, 234, 0.2);
        border-radius: 0 0 12px 12px;
    }

    /* 响应式设计 */
    @@media (max-width: 768px) {
        .cyber-info-item {
            flex-direction: column;
            align-items: flex-start;
        }

        .cyber-info-label {
            margin-bottom: 4px;
        }

        .cyber-info-value {
            text-align: left;
            margin-left: 0;
        }
    }
</style>

@section Scripts {
    <script>
        // 初始化Vue应用
        Vue.createApp({
            data() {
                return {
                    // 加载状态
                    loading: false,
                    saving: false,

                    // 列表数据
                    configsList: [],
                    totalCount: 0,
                    currentPage: 1,
                    pageSize: 10,

                    // 查询条件
                    queryForm: {
                        petNo: null,
                        petName: '',
                        evolutionType: '',
                        targetPetNo: null,
                        targetPetName: '',
                        isActive: null,
                        minRequiredLevel: null,
                        maxRequiredLevel: null
                    },

                    // 表单数据
                    configForm: {
                        id: 0,
                        petNo: null,
                        evolutionType: '',
                        targetPetNo: null,
                        requiredLevel: 40,
                        requiredItemId: null,
                        requiredItemCount: 1,
                        costGold: 1000,
                        growthMin: 0.100,
                        growthMax: 0.500,
                        successRate: 100.00,
                        isActive: true,
                        description: ''
                    },

                    // 模态框状态
                    isEdit: false,
                    modalTitle: '新增进化配置',
                    selectedConfig: null,

                    // 选择状态
                    selectedIds: [],
                    selectAll: false,

                    // 下拉选项
                    petOptions: [],
                    itemOptions: []
                };
        },
        computed: {
            // 总页数
            totalPages() {
                return Math.ceil(this.totalCount / this.pageSize);
            },

            // 可见页码
            visiblePages() {
                const current = this.currentPage;
                const total = this.totalPages;
                const delta = 2;

                let start = Math.max(1, current - delta);
                let end = Math.min(total, current + delta);

                if (end - start < 2 * delta) {
                    if (start === 1) {
                        end = Math.min(total, start + 2 * delta);
                    } else if (end === total) {
                        start = Math.max(1, end - 2 * delta);
                    }
                }

                const pages = [];
                for (let i = start; i <= end; i++) {
                    pages.push(i);
                }
                return pages;
            },


        },
        methods: {
            // 获取属性颜色
            getAttributeColor(attribute) {
                const colors = {
                    '金': '#FFD700',
                    '木': '#32CD32',
                    '水': '#1E90FF',
                    '火': '#FF4500',
                    '土': '#8B4513',
                    '神': '#9370DB',
                    '光': '#FFFF00',
                    '暗': '#800080'
                };
                return colors[attribute] || 'var(--cyber-blue)';
            },

            // 获取成功率颜色
            getSuccessRateColor(rate) {
                if (rate >= 90) return 'var(--cyber-green)';
                if (rate >= 70) return 'var(--cyber-blue)';
                if (rate >= 50) return 'var(--cyber-gold)';
                if (rate >= 30) return 'var(--cyber-orange)';
                return 'var(--cyber-red)';
            },

            // 关闭模态框
            closeModal() {
                $('#configModal').modal('hide');
            },

            // 关闭详情模态框
            closeDetailModal() {
                $('#detailModal').modal('hide');
            },

            // 加载进化配置列表
            async loadConfigs() {
                this.loading = true;
                try {
                    const response = await axios.post('/PetEvolutionConfig/GetList', {
                        ...this.queryForm,
                        page: this.currentPage,
                        pageSize: this.pageSize
                    });

                    if (response.data.code === 200) {
                        this.configsList = response.data.data;
                        this.totalCount = response.data.total;
                    } else {
                        alert(response.data.message || '获取数据失败');
                    }
                } catch (error) {
                    console.error('加载进化配置列表失败:', error);
                    alert('加载数据失败，请重试');
                } finally {
                    this.loading = false;
                }
            },

            // 加载宠物配置选项
            async loadPetOptions() {
                try {
                    const response = await axios.get('/PetEvolutionConfig/GetPetConfigOptions');
                    if (response.data.code === 200) {
                        this.petOptions = response.data.data;
                    }
                } catch (error) {
                    console.error('加载宠物配置选项失败:', error);
                }
            },

            // 加载道具配置选项
            async loadItemOptions() {
                try {
                    const response = await axios.get('/PetEvolutionConfig/GetItemConfigOptions');
                    if (response.data.code === 200) {
                        this.itemOptions = response.data.data;
                    }
                } catch (error) {
                    console.error('加载道具配置选项失败:', error);
                }
            },

            // 搜索配置
            searchConfigs() {
                this.currentPage = 1;
                this.loadConfigs();
            },

            // 重置查询条件
            resetQuery() {
                this.queryForm = {
                    petNo: null,
                    petName: '',
                    evolutionType: '',
                    targetPetNo: null,
                    targetPetName: '',
                    isActive: null,
                    minRequiredLevel: null,
                    maxRequiredLevel: null
                };
                this.searchConfigs();
            },

            // 切换页码
            changePage(page) {
                if (page >= 1 && page <= this.totalPages && page !== this.currentPage) {
                    this.currentPage = page;
                    this.loadConfigs();
                }
            },

            // 显示新增模态框
            showAddModal() {
                this.isEdit = false;
                this.modalTitle = '新增进化配置';
                this.configForm = {
                    id: 0,
                    petNo: null,
                    evolutionType: '',
                    targetPetNo: null,
                    requiredLevel: 40,
                    requiredItemId: null,
                    requiredItemCount: 1,
                    costGold: 1000,
                    growthMin: 0.100,
                    growthMax: 0.500,
                    successRate: 100.00,
                    isActive: true,
                    description: ''
                };

                $('#configModal').modal('show');
            },

            // 编辑配置
            editConfig(config) {
                this.isEdit = true;
                this.modalTitle = '编辑进化配置';
                this.configForm = {
                    id: config.id,
                    petNo: config.petNo,
                    evolutionType: config.evolutionType,
                    targetPetNo: config.targetPetNo,
                    requiredLevel: config.requiredLevel,
                    requiredItemId: config.requiredItemId,
                    requiredItemCount: config.requiredItemCount,
                    costGold: config.costGold,
                    growthMin: config.growthMin,
                    growthMax: config.growthMax,
                    successRate: config.successRate,
                    isActive: config.isActive,
                    description: config.description
                };

                $('#configModal').modal('show');
            },

            // 保存配置
            async saveConfig() {
                // 验证表单
                if (!this.configForm.petNo) {
                    alert('请选择宠物');
                    return;
                }
                if (!this.configForm.evolutionType) {
                    alert('请选择进化类型');
                    return;
                }
                if (!this.configForm.targetPetNo) {
                    alert('请选择目标宠物');
                    return;
                }
                if (!this.configForm.requiredLevel || this.configForm.requiredLevel < 1) {
                    alert('请输入有效的所需等级');
                    return;
                }
                if (this.configForm.growthMin > this.configForm.growthMax) {
                    alert('最小成长加成不能大于最大成长加成');
                    return;
                }

                this.saving = true;
                try {
                    const url = this.isEdit ? '/PetEvolutionConfig/Update' : '/PetEvolutionConfig/Create';
                    const response = await axios.post(url, this.configForm);

                    if (response.data.code === 200) {
                        alert(response.data.message || '操作成功');
                        $('#configModal').modal('hide');
                        this.loadConfigs();
                    } else {
                        alert(response.data.message || '操作失败');
                    }
                } catch (error) {
                    console.error('保存配置失败:', error);
                    alert('保存失败，请重试');
                } finally {
                    this.saving = false;
                }
            },

            // 删除配置
            async deleteConfig(config) {
                if (!confirm(`确定要删除宠物${config.petName}的${config.evolutionType}路线进化配置吗？此操作无法撤销！`)) {
                    return;
                }

                try {
                    const response = await axios.post('/PetEvolutionConfig/Delete', { id: config.id });
                    if (response.data.code === 200) {
                        alert(response.data.message || '删除成功');
                        this.loadConfigs();
                    } else {
                        alert(response.data.message || '删除失败');
                    }
                } catch (error) {
                    console.error('删除配置失败:', error);
                    alert('删除失败，请重试');
                }
            },

            // 批量删除
            async batchDelete() {
                if (this.selectedIds.length === 0) {
                    alert('请选择要删除的配置');
                    return;
                }

                if (!confirm(`确定要删除选中的${this.selectedIds.length}个进化配置吗？此操作无法撤销！`)) {
                    return;
                }

                try {
                    const response = await axios.post('/PetEvolutionConfig/BatchDelete', this.selectedIds);
                    if (response.data.code === 200) {
                        alert(response.data.message || '批量删除成功');
                        this.selectedIds = [];
                        this.selectAll = false;
                        this.loadConfigs();
                    } else {
                        alert(response.data.message || '批量删除失败');
                    }
                } catch (error) {
                    console.error('批量删除失败:', error);
                    alert('批量删除失败，请重试');
                }
            },

            // 切换激活状态
            async toggleActive(config) {
                try {
                    const response = await axios.post('/PetEvolutionConfig/ToggleActive', {
                        id: config.id,
                        isActive: !config.isActive
                    });

                    if (response.data.code === 200) {
                        alert(response.data.message || '操作成功');
                        this.loadConfigs();
                    } else {
                        alert(response.data.message || '操作失败');
                    }
                } catch (error) {
                    console.error('切换激活状态失败:', error);
                    alert('操作失败，请重试');
                }
            },

            // 查看详情
            viewDetail(config) {
                this.selectedConfig = config;
                $('#detailModal').modal('show');
            },



            // 全选/取消全选
            toggleSelectAll() {
                if (this.selectAll) {
                    this.selectedIds = this.configsList.map(config => config.id);
                } else {
                    this.selectedIds = [];
                }
            },

            // 格式化日期时间
            formatDateTime(dateTime) {
                if (!dateTime) return '';
                const date = new Date(dateTime);
                return date.toLocaleString('zh-CN');
            },

            // 格式化数字
            formatNumber(number) {
                if (!number) return '0';
                return number.toLocaleString();
            }
        },
        watch: {
            // 监听选中项变化
            selectedIds() {
                this.selectAll = this.selectedIds.length === this.configsList.length && this.configsList.length > 0;
            }
        },
        mounted() {
            // 初始化加载
            this.loadPetOptions();
            this.loadItemOptions();
            this.loadConfigs();
        }
        }).mount('#petEvolutionConfigApp');
    </script>
}
