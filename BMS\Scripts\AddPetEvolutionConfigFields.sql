-- 为pet_evolution_config表添加缺失的字段
-- 执行前请备份数据库

-- 检查表是否存在
SELECT COUNT(*) as table_exists FROM information_schema.tables 
WHERE table_schema = DATABASE() AND table_name = 'pet_evolution_config';

-- 添加is_active字段（如果不存在）
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM information_schema.columns 
     WHERE table_schema = DATABASE() 
     AND table_name = 'pet_evolution_config' 
     AND column_name = 'is_active') = 0,
    'ALTER TABLE pet_evolution_config ADD COLUMN is_active TINYINT(1) DEFAULT 1 COMMENT ''是否激活''',
    'SELECT ''is_active字段已存在'' as message'
));

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加description字段（如果不存在）
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM information_schema.columns 
     WHERE table_schema = DATABASE() 
     AND table_name = 'pet_evolution_config' 
     AND column_name = 'description') = 0,
    'ALTER TABLE pet_evolution_config ADD COLUMN description VARCHAR(50) DEFAULT NULL COMMENT ''说明''',
    'SELECT ''description字段已存在'' as message'
));

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 查看表结构
DESCRIBE pet_evolution_config;

-- 更新现有记录的is_active字段为1（激活状态）
UPDATE pet_evolution_config SET is_active = 1 WHERE is_active IS NULL;

-- 验证数据
SELECT 
    id,
    pet_no,
    evolution_type,
    target_pet_no,
    required_level,
    is_active,
    description,
    create_time
FROM pet_evolution_config
ORDER BY id;
