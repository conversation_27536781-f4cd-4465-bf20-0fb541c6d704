using BMS.Models.DTOs;
using BMS.Models.Common;

namespace BMS.Services.Interfaces
{
    /// <summary>
    /// 管理员服务接口
    /// </summary>
    public interface IAdminBmService
    {
        /// <summary>
        /// 分页获取管理员列表
        /// </summary>
        /// <param name="queryDto">查询条件</param>
        /// <returns>分页结果</returns>
        Task<PagedResult<AdminBmDto>> GetPagedListAsync(AdminBmQueryDto queryDto);

        /// <summary>
        /// 根据ID获取管理员信息
        /// </summary>
        /// <param name="id">管理员ID</param>
        /// <returns>管理员信息</returns>
        Task<AdminBmDto?> GetByIdAsync(int id);

        /// <summary>
        /// 根据用户名获取管理员信息
        /// </summary>
        /// <param name="username">用户名</param>
        /// <returns>管理员信息</returns>
        Task<AdminBmDto?> GetByUsernameAsync(string username);

        /// <summary>
        /// 创建管理员
        /// </summary>
        /// <param name="createDto">创建管理员DTO</param>
        /// <returns>创建结果</returns>
        Task<ApiResult<int>> CreateAsync(AdminBmCreateDto createDto);

        /// <summary>
        /// 更新管理员信息
        /// </summary>
        /// <param name="updateDto">更新管理员DTO</param>
        /// <returns>更新结果</returns>
        Task<ApiResult<bool>> UpdateAsync(AdminBmUpdateDto updateDto);

        /// <summary>
        /// 删除管理员
        /// </summary>
        /// <param name="id">管理员ID</param>
        /// <returns>删除结果</returns>
        Task<ApiResult<bool>> DeleteAsync(int id);

        /// <summary>
        /// 更改管理员状态
        /// </summary>
        /// <param name="id">管理员ID</param>
        /// <param name="status">状态：0-禁用，1-启用</param>
        /// <returns>更新结果</returns>
        Task<ApiResult<bool>> ChangeStatusAsync(int id, int status);

        /// <summary>
        /// 重置密码
        /// </summary>
        /// <param name="id">管理员ID</param>
        /// <param name="newPassword">新密码</param>
        /// <returns>重置结果</returns>
        Task<ApiResult<bool>> ResetPasswordAsync(int id, string newPassword);

        /// <summary>
        /// 检查用户名是否已存在
        /// </summary>
        /// <param name="username">用户名</param>
        /// <param name="excludeId">排除的ID（用于编辑时检查）</param>
        /// <returns>是否存在</returns>
        Task<bool> CheckUsernameExistsAsync(string username, int? excludeId = null);
    }
} 