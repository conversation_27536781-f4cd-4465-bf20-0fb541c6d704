using BMS.Models.Common;
using BMS.Models.DTOs;

namespace BMS.Services.Interfaces
{
    /// <summary>
    /// 用户服务接口
    /// </summary>
    public interface IUserService
    {
        /// <summary>
        /// 分页查询用户列表
        /// </summary>
        /// <param name="queryDto">查询条件</param>
        /// <returns>分页结果</returns>
        Task<PagedResult<UserDto>> GetUsersAsync(UserQueryDto queryDto);

        /// <summary>
        /// 根据ID获取用户信息
        /// </summary>
        /// <param name="id">用户ID</param>
        /// <returns>用户信息</returns>
        Task<UserDto?> GetUserByIdAsync(int id);

        /// <summary>
        /// 根据用户名获取用户信息
        /// </summary>
        /// <param name="username">用户名</param>
        /// <returns>用户信息</returns>
        Task<UserDto?> GetUserByUsernameAsync(string username);

        /// <summary>
        /// 创建用户
        /// </summary>
        /// <param name="createDto">创建用户DTO</param>
        /// <returns>创建结果</returns>
        Task<ApiResult<int>> CreateUserAsync(UserCreateDto createDto);

        /// <summary>
        /// 更新用户信息
        /// </summary>
        /// <param name="updateDto">更新用户DTO</param>
        /// <returns>更新结果</returns>
        Task<ApiResult<bool>> UpdateUserAsync(UserUpdateDto updateDto);

        /// <summary>
        /// 更新用户资产
        /// </summary>
        /// <param name="assetDto">用户资产更新DTO</param>
        /// <returns>更新结果</returns>
        Task<ApiResult<bool>> UpdateUserAssetAsync(UserAssetUpdateDto assetDto);

        /// <summary>
        /// 重置用户密码
        /// </summary>
        /// <param name="resetDto">重置密码DTO</param>
        /// <returns>重置结果</returns>
        Task<ApiResult<bool>> ResetPasswordAsync(UserResetPasswordDto resetDto);

        /// <summary>
        /// 删除用户
        /// </summary>
        /// <param name="id">用户ID</param>
        /// <returns>删除结果</returns>
        Task<ApiResult<bool>> DeleteUserAsync(int id);

        /// <summary>
        /// 检查用户名是否存在
        /// </summary>
        /// <param name="username">用户名</param>
        /// <param name="excludeId">排除的用户ID（用于更新时检查）</param>
        /// <returns>是否存在</returns>
        Task<bool> CheckUsernameExistsAsync(string username, int? excludeId = null);

        /// <summary>
        /// 验证用户密码
        /// </summary>
        /// <param name="username">用户名</param>
        /// <param name="password">明文密码</param>
        /// <returns>验证结果</returns>
        Task<ApiResult<UserDto>> ValidateUserAsync(string username, string password);

        /// <summary>
        /// 获取用户统计信息
        /// </summary>
        /// <returns>用户统计信息</returns>
        Task<UserStatisticsDto> GetUserStatisticsAsync();
    }
} 