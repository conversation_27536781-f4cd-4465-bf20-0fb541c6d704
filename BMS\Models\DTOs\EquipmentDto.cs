using System.ComponentModel.DataAnnotations;

namespace BMS.Models.DTOs
{
    /// <summary>
    /// 装备信息DTO
    /// </summary>
    public class EquipmentDto
    {
        /// <summary>
        /// 装备唯一ID
        /// </summary>
        public string EquipId { get; set; } = string.Empty;

        /// <summary>
        /// 装备类ID
        /// </summary>
        public string ClassId { get; set; } = string.Empty;

        /// <summary>
        /// 装备名称
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 装备图标
        /// </summary>
        public string? Icon { get; set; }

        /// <summary>
        /// 装备类型ID
        /// </summary>
        public string EquipTypeId { get; set; } = string.Empty;

        /// <summary>
        /// 装备类型名称
        /// </summary>
        public string? EquipTypeName { get; set; }

        /// <summary>
        /// 五行属性
        /// </summary>
        public string? Element { get; set; }

        /// <summary>
        /// 扩展槽位
        /// </summary>
        public int Slot { get; set; }

        /// <summary>
        /// 强化等级
        /// </summary>
        public int StrengthenLevel { get; set; }

        /// <summary>
        /// 套装ID
        /// </summary>
        public string? SuitId { get; set; }

        /// <summary>
        /// 攻击加成
        /// </summary>
        public decimal Atk { get; set; }

        /// <summary>
        /// 命中加成
        /// </summary>
        public decimal Hit { get; set; }

        /// <summary>
        /// 防御加成
        /// </summary>
        public decimal Def { get; set; }

        /// <summary>
        /// 速度加成
        /// </summary>
        public decimal Spd { get; set; }

        /// <summary>
        /// 闪避加成
        /// </summary>
        public decimal Dodge { get; set; }

        /// <summary>
        /// 生命加成
        /// </summary>
        public decimal Hp { get; set; }

        /// <summary>
        /// 魔法加成
        /// </summary>
        public decimal Mp { get; set; }

        /// <summary>
        /// 加深加成
        /// </summary>
        public decimal Deepen { get; set; }

        /// <summary>
        /// 抵消加成
        /// </summary>
        public decimal Offset { get; set; }

        /// <summary>
        /// 吸血加成
        /// </summary>
        public decimal Vamp { get; set; }

        /// <summary>
        /// 吸魔加成
        /// </summary>
        public decimal VampMp { get; set; }

        /// <summary>
        /// 装备说明
        /// </summary>
        public string? Description { get; set; }

        /// <summary>
        /// 主属性
        /// </summary>
        public string? MainAttr { get; set; }

        /// <summary>
        /// 主属性值
        /// </summary>
        public string? MainAttrValue { get; set; }

        /// <summary>
        /// 副属性
        /// </summary>
        public string? SubAttr { get; set; }

        /// <summary>
        /// 副属性值
        /// </summary>
        public string? SubAttrValue { get; set; }

        /// <summary>
        /// 五行限制
        /// </summary>
        public string? ElementLimit { get; set; }

        /// <summary>
        /// 装备名称（详情表）
        /// </summary>
        public string? EquipName { get; set; }
    }
} 