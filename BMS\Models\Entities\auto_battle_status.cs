﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace BMS.Models.Entities
{
    ///<summary>
    ///自动战斗状态表
    ///</summary>
    [SugarTable("auto_battle_status")]
    public partial class auto_battle_status
    {
           public auto_battle_status(){


           }
           /// <summary>
           /// Desc:自增主键
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true,IsIdentity=true)]
           public int id {get;set;}

           /// <summary>
           /// Desc:用户ID
           /// Default:
           /// Nullable:False
           /// </summary>           
           public int user_id {get;set;}

           /// <summary>
           /// Desc:自动战斗类型
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string battle_type {get;set;}

           /// <summary>
           /// Desc:目标层数
           /// Default:0
           /// Nullable:True
           /// </summary>           
           public int? target_floor {get;set;}

           /// <summary>
           /// Desc:当前层数
           /// Default:0
           /// Nullable:True
           /// </summary>           
           public int? current_floor {get;set;}

           /// <summary>
           /// Desc:是否激活
           /// Default:true
           /// Nullable:True
           /// </summary>           
           public bool? is_active {get;set;}

           /// <summary>
           /// Desc:开始时间
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? start_time {get;set;}

           /// <summary>
           /// Desc:结束时间
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? end_time {get;set;}

           /// <summary>
           /// Desc:战斗次数
           /// Default:0
           /// Nullable:True
           /// </summary>           
           public int? battles_count {get;set;}

           /// <summary>
           /// Desc:成功次数
           /// Default:0
           /// Nullable:True
           /// </summary>           
           public int? success_count {get;set;}

           /// <summary>
           /// Desc:创建时间
           /// Default:CURRENT_TIMESTAMP
           /// Nullable:True
           /// </summary>           
           public DateTime? create_time {get;set;}

           /// <summary>
           /// Desc:更新时间
           /// Default:CURRENT_TIMESTAMP
           /// Nullable:True
           /// </summary>           
           public DateTime? update_time {get;set;}

    }
}
