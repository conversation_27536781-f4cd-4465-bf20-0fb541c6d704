using BMS.Models.DTOs;
using BMS.Models.Common;

namespace BMS.Services.Interfaces
{
    /// <summary>
    /// 道具配置服务接口
    /// </summary>
    public interface IItemConfigService
    {
        /// <summary>
        /// 分页获取道具配置列表
        /// </summary>
        /// <param name="queryDto">查询条件</param>
        /// <returns></returns>
        Task<PagedResult<ItemConfigDto>> GetPagedListAsync(ItemConfigQueryDto queryDto);

        /// <summary>
        /// 根据ID获取道具配置
        /// </summary>
        /// <param name="id">道具配置ID</param>
        /// <returns></returns>
        Task<ItemConfigDto?> GetByIdAsync(int id);

        /// <summary>
        /// 根据道具编号获取道具配置
        /// </summary>
        /// <param name="itemNo">道具编号</param>
        /// <returns></returns>
        Task<ItemConfigDto?> GetByItemNoAsync(int itemNo);

        /// <summary>
        /// 创建道具配置
        /// </summary>
        /// <param name="createDto">创建DTO</param>
        /// <returns></returns>
        Task<ApiResult<int>> CreateAsync(ItemConfigCreateDto createDto);

        /// <summary>
        /// 更新道具配置
        /// </summary>
        /// <param name="updateDto">更新DTO</param>
        /// <returns></returns>
        Task<ApiResult<bool>> UpdateAsync(ItemConfigUpdateDto updateDto);

        /// <summary>
        /// 删除道具配置
        /// </summary>
        /// <param name="id">道具配置ID</param>
        /// <returns></returns>
        Task<ApiResult<bool>> DeleteAsync(int id);

        /// <summary>
        /// 获取所有道具类型
        /// </summary>
        /// <returns></returns>
        Task<List<string>> GetAllTypesAsync();

        /// <summary>
        /// 获取所有道具品质
        /// </summary>
        /// <returns></returns>
        Task<List<string>> GetAllQualitiesAsync();

        /// <summary>
        /// 检查道具编号是否存在
        /// </summary>
        /// <param name="itemNo">道具编号</param>
        /// <param name="excludeId">排除的ID（用于更新时检查）</param>
        /// <returns></returns>
        Task<bool> IsItemNoExistsAsync(int itemNo, int? excludeId = null);

        /// <summary>
        /// 分页获取道具配置和脚本关联列表
        /// </summary>
        /// <param name="queryDto">查询条件</param>
        /// <returns></returns>
        Task<PagedResult<ItemConfigWithScriptDto>> GetPagedListWithScriptAsync(ItemConfigWithScriptQueryDto queryDto);

        /// <summary>
        /// 根据ID获取道具配置和脚本关联信息
        /// </summary>
        /// <param name="id">道具配置ID</param>
        /// <returns></returns>
        Task<ItemConfigWithScriptDto?> GetByIdWithScriptAsync(int id);

        /// <summary>
        /// 创建道具配置和脚本关联
        /// </summary>
        /// <param name="createDto">创建DTO</param>
        /// <returns></returns>
        Task<ApiResult<int>> CreateWithScriptAsync(ItemConfigWithScriptCreateDto createDto);

        /// <summary>
        /// 更新道具配置和脚本关联
        /// </summary>
        /// <param name="updateDto">更新DTO</param>
        /// <returns></returns>
        Task<ApiResult<bool>> UpdateWithScriptAsync(ItemConfigWithScriptUpdateDto updateDto);

        /// <summary>
        /// 批量更新道具状态
        /// </summary>
        /// <param name="batchDto">批量更新DTO</param>
        /// <returns></returns>
        Task<ApiResult<bool>> BatchUpdateStatusAsync(ItemConfigBatchUpdateStatusDto batchDto);
    }
} 