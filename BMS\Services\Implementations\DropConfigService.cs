using BMS.Models.DTOs;
using BMS.Models.Common;
using BMS.Models.Entities;
using BMS.Services.Interfaces;
using SqlSugar;
using System.Text.Json;

namespace BMS.Services.Implementations
{
    /// <summary>
    /// 掉落配置服务实现
    /// </summary>
    public class DropConfigService : IDropConfigService
    {
        private readonly IDbService _dbService;

        public DropConfigService(IDbService dbService)
        {
            _dbService = dbService;
        }

        /// <summary>
        /// 分页查询掉落配置列表
        /// </summary>
        /// <param name="queryDto">查询条件</param>
        /// <returns>分页结果</returns>
        public async Task<PagedResult<DropConfigDto>> GetPagedListAsync(DropConfigQueryDto queryDto)
        {
            try
            {
                var query = _dbService.GetClient().Queryable<drop_config, map_config, map_monster, item_config>((dc, mc, mm, ic) => new JoinQueryInfos(
                    JoinType.Left, dc.map_id == mc.map_id,
                    JoinType.Left, dc.monster_id == mm.monster_id && dc.map_id == mm.map_id,
                    JoinType.Left, dc.item_id == ic.item_no.ToString()
                ));

                // 添加查询条件
                if (queryDto.MapId.HasValue)
                {
                    query = query.Where((dc, mc, mm, ic) => dc.map_id == queryDto.MapId.Value);
                }

                if (!string.IsNullOrWhiteSpace(queryDto.MapName))
                {
                    query = query.Where((dc, mc, mm, ic) => mc.map_name.Contains(queryDto.MapName));
                }

                if (!string.IsNullOrWhiteSpace(queryDto.DropType))
                {
                    query = query.Where((dc, mc, mm, ic) => dc.drop_type == queryDto.DropType);
                }

                if (queryDto.MonsterId.HasValue)
                {
                    query = query.Where((dc, mc, mm, ic) => dc.monster_id == queryDto.MonsterId.Value);
                }

                if (!string.IsNullOrWhiteSpace(queryDto.ItemId))
                {
                    query = query.Where((dc, mc, mm, ic) => dc.item_id.Contains(queryDto.ItemId));
                }

                if (!string.IsNullOrWhiteSpace(queryDto.ItemName))
                {
                    query = query.Where((dc, mc, mm, ic) => ic.name.Contains(queryDto.ItemName));
                }

                RefAsync<int> totalCount = 0;
                var dropConfigs = await query
                    .OrderBy((dc, mc, mm, ic) => dc.create_time, OrderByType.Desc)
                    .Select((dc, mc, mm, ic) => new DropConfigDto
                    {
                        Id = dc.id,
                        MapId = dc.map_id,
                        MapName = mc.map_name ?? "",
                        DropType = dc.drop_type,
                        MonsterId = dc.monster_id,
                        MonsterName = mm.monster_name,
                        ItemId = dc.item_id,
                        ItemName = ic.name ?? "",
                        DropRate = dc.drop_rate ?? 1.0m,
                        MinCount = dc.min_count ?? 1,
                        MaxCount = dc.max_count ?? 1,
                        Remark = dc.remark,
                        CreateTime = dc.create_time ?? DateTime.Now,
                        UpdateTime = dc.update_time ?? DateTime.Now,
                        DropItemsJson = dc.drop_items_json
                    })
                    .ToPageListAsync(queryDto.Page, queryDto.PageSize, totalCount);

                return new PagedResult<DropConfigDto>(dropConfigs, queryDto.Page, queryDto.PageSize, totalCount.Value);
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"获取掉落配置列表失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 根据ID获取掉落配置详情
        /// </summary>
        /// <param name="id">掉落配置ID</param>
        /// <returns>掉落配置详情</returns>
        public async Task<DropConfigDto?> GetByIdAsync(int id)
        {
            try
            {
                return await _dbService.GetClient().Queryable<drop_config, map_config, map_monster, item_config>((dc, mc, mm, ic) => new JoinQueryInfos(
                    JoinType.Left, dc.map_id == mc.map_id,
                    JoinType.Left, dc.monster_id == mm.monster_id && dc.map_id == mm.map_id,
                    JoinType.Left, dc.item_id == ic.item_no.ToString()
                ))
                .Where((dc, mc, mm, ic) => dc.id == id)
                .Select((dc, mc, mm, ic) => new DropConfigDto
                {
                    Id = dc.id,
                    MapId = dc.map_id,
                    MapName = mc.map_name ?? "",
                    DropType = dc.drop_type,
                    MonsterId = dc.monster_id,
                    MonsterName = mm.monster_name,
                    ItemId = dc.item_id,
                    ItemName = ic.name ?? "",
                    DropRate = dc.drop_rate ?? 1.0m,
                    MinCount = dc.min_count ?? 1,
                    MaxCount = dc.max_count ?? 1,
                    Remark = dc.remark,
                    CreateTime = dc.create_time ?? DateTime.Now,
                    UpdateTime = dc.update_time ?? DateTime.Now,
                    DropItemsJson = dc.drop_items_json
                })
                .FirstAsync();
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"获取掉落配置详情失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 创建掉落配置
        /// </summary>
        /// <param name="createDto">创建DTO</param>
        /// <returns>操作结果</returns>
        public async Task<ApiResult<DropConfigDto>> CreateAsync(DropConfigCreateDto createDto)
        {
            try
            {
                // 验证地图是否存在
                var mapExists = await  _dbService.Queryable<map_config>().AnyAsync(mc => mc.map_id == createDto.MapId);
                if (!mapExists)
                {
                    return ApiResult<DropConfigDto>.Fail("地图不存在");
                }

                // 如果是怪物掉落，验证怪物是否存在
                if (createDto.DropType == "怪物掉落" && createDto.MonsterId.HasValue)
                {
                    var monsterExists = await  _dbService.Queryable<map_monster>()
                        .AnyAsync(mm => mm.monster_id == createDto.MonsterId.Value && mm.map_id == createDto.MapId);
                    if (!monsterExists)
                    {
                        return ApiResult<DropConfigDto>.Fail("怪物不存在或不在指定地图中");
                    }
                }

                // 验证道具是否存在
                var itemExists = await  _dbService.Queryable<item_config>().AnyAsync(ic => ic.item_no.ToString() == createDto.ItemId);
                if (!itemExists)
                {
                    return ApiResult<DropConfigDto>.Fail("道具不存在");
                }

                // 检查掉落配置是否已存在
                var exists = await ExistsAsync(createDto.MapId, createDto.DropType, createDto.MonsterId, createDto.ItemId);
                if (exists)
                {
                    return ApiResult<DropConfigDto>.Fail("相同的掉落配置已存在");
                }

                // 验证数量范围
                if (createDto.MinCount > createDto.MaxCount)
                {
                    return ApiResult<DropConfigDto>.Fail("最小掉落数量不能大于最大掉落数量");
                }

                // 创建掉落配置实体
                var dropConfig = new drop_config
                {
                    map_id = createDto.MapId,
                    drop_type = createDto.DropType,
                    monster_id = createDto.MonsterId,
                    item_id = createDto.ItemId,
                    drop_rate = createDto.DropRate,
                    min_count = createDto.MinCount,
                    max_count = createDto.MaxCount,
                    remark = createDto.Remark,
                    create_time = DateTime.Now,
                    update_time = DateTime.Now
                };

                // 插入数据库
                var insertedId = await  _dbService.Insertable(dropConfig).ExecuteReturnIdentityAsync();
                dropConfig.id = insertedId;

                // 返回创建的掉落配置详情
                var result = await GetByIdAsync(insertedId);
                return ApiResult<DropConfigDto>.Ok(result, "创建掉落配置成功");
            }
            catch (Exception ex)
            {
                return ApiResult<DropConfigDto>.Fail($"创建掉落配置失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 更新掉落配置
        /// </summary>
        /// <param name="updateDto">更新DTO</param>
        /// <returns>操作结果</returns>
        public async Task<ApiResult<DropConfigDto>> UpdateAsync(DropConfigUpdateDto updateDto)
        {
            try
            {
                // 检查掉落配置是否存在
                var existingConfig = await  _dbService.Queryable<drop_config>().FirstAsync(dc => dc.id == updateDto.Id);
                if (existingConfig == null)
                {
                    return ApiResult<DropConfigDto>.Fail("掉落配置不存在");
                }

                // 验证地图是否存在
                var mapExists = await  _dbService.Queryable<map_config>().AnyAsync(mc => mc.map_id == updateDto.MapId);
                if (!mapExists)
                {
                    return ApiResult<DropConfigDto>.Fail("地图不存在");
                }

                // 如果是怪物掉落，验证怪物是否存在
                if (updateDto.DropType == "怪物掉落" && updateDto.MonsterId.HasValue)
                {
                    var monsterExists = await  _dbService.Queryable<map_monster>()
                        .AnyAsync(mm => mm.monster_id == updateDto.MonsterId.Value && mm.map_id == updateDto.MapId);
                    if (!monsterExists)
                    {
                        return ApiResult<DropConfigDto>.Fail("怪物不存在或不在指定地图中");
                    }
                }

                // 验证道具是否存在
                var itemExists = await  _dbService.Queryable<item_config>().AnyAsync(ic => ic.item_no.ToString() == updateDto.ItemId);
                if (!itemExists)
                {
                    return ApiResult<DropConfigDto>.Fail("道具不存在");
                }

                // 检查掉落配置是否已存在（排除当前记录）
                var exists = await ExistsAsync(updateDto.MapId, updateDto.DropType, updateDto.MonsterId, updateDto.ItemId, updateDto.Id);
                if (exists)
                {
                    return ApiResult<DropConfigDto>.Fail("相同的掉落配置已存在");
                }

                // 验证数量范围
                if (updateDto.MinCount > updateDto.MaxCount)
                {
                    return ApiResult<DropConfigDto>.Fail("最小掉落数量不能大于最大掉落数量");
                }

                // 更新字段
                existingConfig.map_id = updateDto.MapId;
                existingConfig.drop_type = updateDto.DropType;
                existingConfig.monster_id = updateDto.MonsterId;
                existingConfig.item_id = updateDto.ItemId;
                existingConfig.drop_rate = updateDto.DropRate;
                existingConfig.min_count = updateDto.MinCount;
                existingConfig.max_count = updateDto.MaxCount;
                existingConfig.remark = updateDto.Remark;
                existingConfig.update_time = DateTime.Now;

                // 更新数据库
                await  _dbService.Updateable(existingConfig).ExecuteCommandAsync();

                // 返回更新后的掉落配置详情
                var result = await GetByIdAsync(updateDto.Id);
                return ApiResult<DropConfigDto>.Ok(result, "更新掉落配置成功");
            }
            catch (Exception ex)
            {
                return ApiResult<DropConfigDto>.Fail($"更新掉落配置失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 删除掉落配置
        /// </summary>
        /// <param name="deleteDto">删除DTO</param>
        /// <returns>操作结果</returns>
        public async Task<ApiResult<bool>> DeleteAsync(DropConfigDeleteDto deleteDto)
        {
            try
            {
                // 检查掉落配置是否存在
                var config = await  _dbService.Queryable<drop_config>().FirstAsync(dc => dc.id == deleteDto.Id);
                if (config == null)
                {
                    return ApiResult<bool>.Fail("掉落配置不存在");
                }

                // 删除掉落配置
                await  _dbService.Deleteable<drop_config>().Where(dc => dc.id == deleteDto.Id).ExecuteCommandAsync();

                return ApiResult<bool>.Ok(true, "删除掉落配置成功");
            }
            catch (Exception ex)
            {
                return ApiResult<bool>.Fail($"删除掉落配置失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 批量删除掉落配置
        /// </summary>
        /// <param name="ids">掉落配置ID数组</param>
        /// <returns>操作结果</returns>
        public async Task<ApiResult<bool>> BatchDeleteAsync(int[] ids)
        {
            try
            {
                if (ids == null || ids.Length == 0)
                {
                    return ApiResult<bool>.Fail("请选择要删除的掉落配置");
                }

                // 批量删除
                await  _dbService.Deleteable<drop_config>().Where(dc => ids.Contains(dc.id)).ExecuteCommandAsync();

                return ApiResult<bool>.Ok(true, $"成功删除 {ids.Length} 个掉落配置");
            }
            catch (Exception ex)
            {
                return ApiResult<bool>.Fail($"批量删除掉落配置失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取掉落类型选项
        /// </summary>
        /// <returns>掉落类型选项列表</returns>
        public async Task<List<DropTypeOptionDto>> GetDropTypeOptionsAsync()
        {
            try
            {
                await Task.CompletedTask;
                return new List<DropTypeOptionDto>
                {
                    new DropTypeOptionDto { Value = "地图掉落", Label = "地图掉落" },
                    new DropTypeOptionDto { Value = "怪物掉落", Label = "怪物掉落" }
                };
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"获取掉落类型选项失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 获取地图选项
        /// </summary>
        /// <returns>地图选项列表</returns>
        public async Task<List<OptionDto>> GetMapOptionsAsync()
        {
            try
            {
                return await  _dbService.Queryable<map_config>()
                    .Select(mc => new OptionDto
                    {
                        Value = mc.map_id.ToString(),
                        Label = mc.map_name
                    })
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"获取地图选项失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 根据地图ID获取怪物选项
        /// </summary>
        /// <param name="mapId">地图ID</param>
        /// <returns>怪物选项列表</returns>
        public async Task<List<OptionDto>> GetMonsterOptionsByMapIdAsync(int mapId)
        {
            try
            {
                return await  _dbService.Queryable<map_monster>()
                    .Where(mm => mm.map_id == mapId)
                    .Select(mm => new OptionDto
                    {
                        Value = mm.monster_id.ToString(),
                        Label = mm.monster_name
                    })
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"获取怪物选项失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 获取道具选项
        /// </summary>
        /// <returns>道具选项列表</returns>
        public async Task<List<OptionDto>> GetItemOptionsAsync()
        {
            try
            {
                return await  _dbService.Queryable<item_config>()
                    .Select(ic => new OptionDto
                    {
                        Value = ic.item_no.ToString(),
                        Label = ic.name
                    })
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"获取道具选项失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 检查掉落配置是否存在
        /// </summary>
        /// <param name="mapId">地图ID</param>
        /// <param name="dropType">掉落类型</param>
        /// <param name="monsterId">怪物ID</param>
        /// <param name="itemId">道具ID</param>
        /// <param name="excludeId">排除的ID（用于更新时检查）</param>
        /// <returns>是否存在</returns>
        public async Task<bool> ExistsAsync(int mapId, string dropType, int? monsterId, string itemId, int? excludeId = null)
        {
            try
            {
                var query =  _dbService.Queryable<drop_config>()
                    .Where(dc => dc.map_id == mapId && dc.drop_type == dropType && dc.item_id == itemId);

                if (monsterId.HasValue)
                {
                    query = query.Where(dc => dc.monster_id == monsterId.Value);
                }
                else
                {
                    query = query.Where(dc => dc.monster_id == null);
                }

                if (excludeId.HasValue)
                {
                    query = query.Where(dc => dc.id != excludeId.Value);
                }

                return await query.AnyAsync();
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"检查掉落配置是否存在失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 根据地图ID获取掉落配置列表
        /// </summary>
        /// <param name="mapId">地图ID</param>
        /// <returns>掉落配置列表</returns>
        public async Task<List<DropConfigDto>> GetByMapIdAsync(int mapId)
        {
            try
            {
                return await _dbService.GetClient().Queryable<drop_config, map_config, map_monster, item_config>((dc, mc, mm, ic) => new JoinQueryInfos(
                    JoinType.Left, dc.map_id == mc.map_id,
                    JoinType.Left, dc.monster_id == mm.monster_id && dc.map_id == mm.map_id,
                    JoinType.Left, dc.item_id == ic.item_no.ToString()
                ))
                .Where((dc, mc, mm, ic) => dc.map_id == mapId)
                .OrderBy((dc, mc, mm, ic) => new { dc.drop_type, dc.monster_id })
                .Select((dc, mc, mm, ic) => new DropConfigDto
                {
                    Id = dc.id,
                    MapId = dc.map_id,
                    MapName = mc.map_name ?? "",
                    DropType = dc.drop_type,
                    MonsterId = dc.monster_id,
                    MonsterName = mm.monster_name,
                    ItemId = dc.item_id,
                    ItemName = ic.name ?? "",
                    DropRate = dc.drop_rate ?? 1.0m,
                    MinCount = dc.min_count ?? 1,
                    MaxCount = dc.max_count ?? 1,
                    Remark = dc.remark,
                    CreateTime = dc.create_time ?? DateTime.Now,
                    UpdateTime = dc.update_time ?? DateTime.Now,
                    DropItemsJson = dc.drop_items_json
                })
                .ToListAsync();
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"根据地图ID获取掉落配置列表失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 根据怪物ID获取掉落配置列表
        /// </summary>
        /// <param name="monsterId">怪物ID</param>
        /// <returns>掉落配置列表</returns>
        public async Task<List<DropConfigDto>> GetByMonsterIdAsync(int monsterId)
        {
            try
            {
                return await _dbService.GetClient().Queryable<drop_config, map_config, map_monster, item_config>((dc, mc, mm, ic) => new JoinQueryInfos(
                    JoinType.Left, dc.map_id == mc.map_id,
                    JoinType.Left, dc.monster_id == mm.monster_id && dc.map_id == mm.map_id,
                    JoinType.Left, dc.item_id == ic.item_no.ToString()
                ))
                .Where((dc, mc, mm, ic) => dc.monster_id == monsterId)
                .OrderBy((dc, mc, mm, ic) => dc.map_id)
                .Select((dc, mc, mm, ic) => new DropConfigDto
                {
                    Id = dc.id,
                    MapId = dc.map_id,
                    MapName = mc.map_name ?? "",
                    DropType = dc.drop_type,
                    MonsterId = dc.monster_id,
                    MonsterName = mm.monster_name,
                    ItemId = dc.item_id,
                    ItemName = ic.name ?? "",
                    DropRate = dc.drop_rate ?? 1.0m,
                    MinCount = dc.min_count ?? 1,
                    MaxCount = dc.max_count ?? 1,
                    Remark = dc.remark,
                    CreateTime = dc.create_time ?? DateTime.Now,
                    UpdateTime = dc.update_time ?? DateTime.Now,
                    DropItemsJson = dc.drop_items_json
                })
                .ToListAsync();
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"根据怪物ID获取掉落配置列表失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 创建多道具掉落配置
        /// </summary>
        /// <param name="createDto">创建DTO</param>
        /// <returns>操作结果</returns>
        public async Task<ApiResult<bool>> CreateMultiAsync(MultiDropConfigCreateDto createDto)
        {
            try
            {
                // 验证地图是否存在
                var mapExists = await  _dbService.Queryable<map_config>().AnyAsync(mc => mc.map_id == createDto.MapId);
                if (!mapExists)
                {
                    return ApiResult<bool>.Fail("地图不存在");
                }

                // 如果是怪物掉落，验证怪物是否存在
                if (createDto.DropType == "怪物掉落" && createDto.MonsterId.HasValue)
                {
                    var monsterExists = await  _dbService.Queryable<map_monster>()
                        .AnyAsync(mm => mm.monster_id == createDto.MonsterId.Value && mm.map_id == createDto.MapId);
                    if (!monsterExists)
                    {
                        return ApiResult<bool>.Fail("怪物不存在或不在指定地图中");
                    }
                }

                // 验证道具是否存在
                foreach (var dropItem in createDto.DropItems)
                {
                    var itemExists = await  _dbService.Queryable<item_config>().AnyAsync(ic => ic.item_no.ToString() == dropItem.ItemId);
                    if (!itemExists)
                    {
                        return ApiResult<bool>.Fail($"道具 {dropItem.ItemId} 不存在");
                    }
                }

                // 将道具列表转换为JSON
                var dropItemsJson = JsonSerializer.Serialize(createDto.DropItems);

                // 创建掉落配置实体
                var dropConfig = new drop_config
                {
                    map_id = createDto.MapId,
                    drop_type = createDto.DropType,
                    monster_id = createDto.MonsterId,
                    item_id = null, // 多道具配置不使用此字段
                    drop_rate = null, // 多道具配置不使用此字段
                    min_count = null, // 多道具配置不使用此字段
                    max_count = null, // 多道具配置不使用此字段
                    drop_items_json = dropItemsJson,
                    remark = createDto.Remark,
                    create_time = DateTime.Now,
                    update_time = DateTime.Now
                };

                // 插入数据库
                await  _dbService.Insertable(dropConfig).ExecuteCommandAsync();

                return ApiResult<bool>.Ok(true, "创建多道具掉落配置成功");
            }
            catch (Exception ex)
            {
                return ApiResult<bool>.Fail($"创建多道具掉落配置失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 更新多道具掉落配置
        /// </summary>
        /// <param name="updateDto">更新DTO</param>
        /// <returns>操作结果</returns>
        public async Task<ApiResult<bool>> UpdateMultiAsync(MultiDropConfigUpdateDto updateDto)
        {
            try
            {
                // 检查掉落配置是否存在
                var existingConfig = await  _dbService.Queryable<drop_config>().FirstAsync(dc => dc.id == updateDto.Id);
                if (existingConfig == null)
                {
                    return ApiResult<bool>.Fail("掉落配置不存在");
                }

                // 验证地图是否存在
                var mapExists = await  _dbService.Queryable<map_config>().AnyAsync(mc => mc.map_id == updateDto.MapId);
                if (!mapExists)
                {
                    return ApiResult<bool>.Fail("地图不存在");
                }

                // 如果是怪物掉落，验证怪物是否存在
                if (updateDto.DropType == "怪物掉落" && updateDto.MonsterId.HasValue)
                {
                    var monsterExists = await  _dbService.Queryable<map_monster>()
                        .AnyAsync(mm => mm.monster_id == updateDto.MonsterId.Value && mm.map_id == updateDto.MapId);
                    if (!monsterExists)
                    {
                        return ApiResult<bool>.Fail("怪物不存在或不在指定地图中");
                    }
                }

                // 验证道具是否存在
                foreach (var dropItem in updateDto.DropItems)
                {
                    var itemExists = await  _dbService.Queryable<item_config>().AnyAsync(ic => ic.item_no.ToString() == dropItem.ItemId);
                    if (!itemExists)
                    {
                        return ApiResult<bool>.Fail($"道具 {dropItem.ItemId} 不存在");
                    }
                }

                // 将道具列表转换为JSON
                var dropItemsJson = JsonSerializer.Serialize(updateDto.DropItems);

                // 更新字段
                existingConfig.map_id = updateDto.MapId;
                existingConfig.drop_type = updateDto.DropType;
                existingConfig.monster_id = updateDto.MonsterId;
                existingConfig.item_id = null; // 多道具配置不使用此字段
                existingConfig.drop_rate = null; // 多道具配置不使用此字段
                existingConfig.min_count = null; // 多道具配置不使用此字段
                existingConfig.max_count = null; // 多道具配置不使用此字段
                existingConfig.drop_items_json = dropItemsJson;
                existingConfig.remark = updateDto.Remark;
                existingConfig.update_time = DateTime.Now;

                // 更新数据库
                await  _dbService.Updateable(existingConfig).ExecuteCommandAsync();

                return ApiResult<bool>.Ok(true, "更新多道具掉落配置成功");
            }
            catch (Exception ex)
            {
                return ApiResult<bool>.Fail($"更新多道具掉落配置失败: {ex.Message}");
            }
        }
    }
}