using BMS.Models.DTOs;
using BMS.Models.Common;

namespace BMS.Services.Interfaces
{
    /// <summary>
    /// 境界服务接口
    /// </summary>
    public interface IRealmService
    {
        /// <summary>
        /// 分页查询境界列表
        /// </summary>
        /// <param name="queryDto">查询条件</param>
        /// <returns>分页结果</returns>
        Task<PagedResult<RealmDto>> GetRealmsAsync(RealmQueryDto queryDto);

        /// <summary>
        /// 根据境界ID获取境界信息
        /// </summary>
        /// <param name="realmId">境界ID</param>
        /// <returns>境界信息</returns>
        Task<RealmDto?> GetRealmByIdAsync(int realmId);

        /// <summary>
        /// 创建境界
        /// </summary>
        /// <param name="createDto">创建DTO</param>
        /// <returns>操作结果</returns>
        Task<ApiResult<RealmDto>> CreateRealmAsync(RealmCreateDto createDto);

        /// <summary>
        /// 更新境界
        /// </summary>
        /// <param name="updateDto">更新DTO</param>
        /// <returns>操作结果</returns>
        Task<ApiResult<RealmDto>> UpdateRealmAsync(RealmUpdateDto updateDto);

        /// <summary>
        /// 删除境界
        /// </summary>
        /// <param name="deleteDto">删除DTO</param>
        /// <returns>操作结果</returns>
        Task<ApiResult<bool>> DeleteRealmAsync(RealmDeleteDto deleteDto);

        /// <summary>
        /// 检查境界ID是否存在
        /// </summary>
        /// <param name="realmId">境界ID</param>
        /// <returns>是否存在</returns>
        Task<bool> ExistsAsync(int realmId);

        /// <summary>
        /// 获取境界下的宠物数量
        /// </summary>
        /// <param name="realmName">境界名称</param>
        /// <returns>宠物数量</returns>
        Task<int> GetPetCountByRealmAsync(string realmName);

        /// <summary>
        /// 获取所有境界的下拉选项
        /// </summary>
        /// <returns>境界选项列表</returns>
        Task<List<RealmDto>> GetRealmOptionsAsync();
    }
} 