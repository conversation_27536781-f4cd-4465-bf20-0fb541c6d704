namespace BMS.Models.DTOs
{
    /// <summary>
    /// 下拉选项DTO
    /// </summary>
    public class OptionDto
    {
        /// <summary>
        /// 选项值
        /// </summary>
        public string Value { get; set; } = string.Empty;

        /// <summary>
        /// 选项文本
        /// </summary>
        public string Label { get; set; } = string.Empty;
    }

    /// <summary>
    /// 装备配置选项DTO
    /// </summary>
    public class EquipmentConfigOptionDto
    {
        /// <summary>
        /// 装备唯一ID
        /// </summary>
        public string Value { get; set; } = string.Empty;

        /// <summary>
        /// 装备名称
        /// </summary>
        public string Label { get; set; } = string.Empty;



        /// <summary>
        /// 装备名称
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 装备图标
        /// </summary>
        public string Icon { get; set; } = string.Empty;

        /// <summary>
        /// 装备类型ID
        /// </summary>
        public string EquipTypeId { get; set; } = string.Empty;
    }
} 