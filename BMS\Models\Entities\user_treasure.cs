﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace BMS.Models.Entities
{
    ///<summary>
    ///用户法宝表
    ///</summary>
    [SugarTable("user_treasure")]
    public partial class user_treasure
    {
           public user_treasure(){


           }
           /// <summary>
           /// Desc:自增主键
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true,IsIdentity=true)]
           public int id {get;set;}

           /// <summary>
           /// Desc:用户ID
           /// Default:
           /// Nullable:False
           /// </summary>           
           public int user_id {get;set;}

           /// <summary>
           /// Desc:法宝ID
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string treasure_id {get;set;}

           /// <summary>
           /// Desc:法宝等级
           /// Default:1
           /// Nullable:True
           /// </summary>           
           public int? treasure_level {get;set;}

           /// <summary>
           /// Desc:是否装备（0-未装备，1-已装备）
           /// Default:true
           /// Nullable:True
           /// </summary>           
           public bool? is_equipped {get;set;}

           /// <summary>
           /// Desc:获得时间
           /// Default:CURRENT_TIMESTAMP
           /// Nullable:True
           /// </summary>           
           public DateTime? create_time {get;set;}

           /// <summary>
           /// Desc:更新时间
           /// Default:CURRENT_TIMESTAMP
           /// Nullable:True
           /// </summary>           
           public DateTime? update_time {get;set;}

    }
}
