/**
 * Vue.js表单组件标准模板
 * 避免Razor解析问题的最佳实践
 */

// @section Scripts {
//     <script>
//         const { createApp } = Vue;
//         
//         createApp({
//             data() {
//                 return {
//                     formData: {
//                         // 表单字段定义
//                         username: '',
//                         email: '',
//                         phone: '',
//                         // ... 其他字段
//                     },
//                     errors: {},
//                     isSubmitting: false
//                 };
//             },
//             
//             methods: {
//                 // 表单验证 - 使用RazorSafe工具类
//                 validate() {
//                     const rules = {
//                         username: { 
//                             required: true, 
//                             minLength: 3,
//                             requiredMessage: '用户名不能为空',
//                             minLengthMessage: '用户名不能少于3位'
//                         },
//                         email: { 
//                             required: true, 
//                             type: 'email',
//                             message: '请输入有效的邮箱地址'
//                         },
//                         phone: { 
//                             type: 'phone',
//                             message: '请输入有效的手机号码'
//                         }
//                     };
//                     
//                     const result = RazorSafe.validateForm(this.formData, rules);
//                     this.errors = result.errors;
//                     return result.isValid;
//                 },
//                 
//                 // 异步提交表单
//                 async submitForm() {
//                     if (!this.validate()) return;
//                     
//                     this.isSubmitting = true;
//                     
//                     try {
//                         const response = await fetch('/Controller/ActionApi', {
//                             method: 'POST',
//                             headers: {
//                                 'Content-Type': 'application/json',
//                                 'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]').value
//                             },
//                             body: JSON.stringify(this.formData)
//                         });
//                         
//                         const result = await response.json();
//                         
//                         if (result.success) {
//                             toastr.success(result.message || '操作成功');
//                             // 成功后的处理逻辑
//                             setTimeout(() => {
//                                 window.location.href = '/RedirectUrl';
//                             }, 1500);
//                         } else {
//                             toastr.error(result.message || '操作失败');
//                         }
//                     } catch (error) {
//                         console.error('提交失败:', error);
//                         toastr.error('系统错误，请稍后重试');
//                     } finally {
//                         this.isSubmitting = false;
//                     }
//                 },
//                 
//                 // 重置表单
//                 resetForm() {
//                     this.formData = {
//                         username: '',
//                         email: '',
//                         phone: ''
//                     };
//                     this.errors = {};
//                 },
//                 
//                 // 显示成功消息
//                 showSuccess(message) {
//                     if (typeof toastr !== 'undefined') {
//                         toastr.success(message);
//                     } else {
//                         alert(message);
//                     }
//                 },
//                 
//                 // 显示错误消息
//                 showError(message) {
//                     if (typeof toastr !== 'undefined') {
//                         toastr.error(message);
//                     } else {
//                         alert(message);
//                     }
//                 }
//             },
//             
//             mounted() {
//                 // 组件挂载后的初始化逻辑
//                 console.log('Vue组件已挂载');
//             }
//         }).mount('#appId');
//     </script>
// }

/**
 * 使用说明：
 * 
 * 1. 复制上述模板到.cshtml文件的@section Scripts部分
 * 2. 修改formData中的字段定义
 * 3. 更新validate()方法中的验证规则
 * 4. 修改submitForm()方法中的URL为对应的API端点（如CreateApi、EditApi）
 * 5. 确保HTML中有对应的Vue挂载点：<div id="appId">
 * 6. 确保后端控制器有对应的API方法（使用[FromBody]参数绑定）
 * 
 * 避免的问题：
 * - 不要在JavaScript中直接使用@符号
 * - 不要在正则表达式中直接使用[]符号
 * - 不要在注释中提到@符号
 * - 使用RazorSafe工具类进行验证
 * - 使用专门的API端点而不是表单提交端点
 * 
 * 控制器端API方法示例：
 * [HttpPost]
 * [Route("Controller/ActionApi")]
 * [ValidateAntiForgeryToken]
 * public async Task<IActionResult> ActionApi([FromBody] SomeDto dto)
 * {
 *     // ... 业务逻辑
 *     return Json(ApiResult<object>.Ok(null, "操作成功"));
 * }
 */ 