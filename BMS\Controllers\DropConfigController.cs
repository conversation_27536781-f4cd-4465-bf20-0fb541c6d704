using BMS.Models.Common;
using BMS.Models.DTOs;
using BMS.Services.Interfaces;
using Microsoft.AspNetCore.Mvc;

namespace BMS.Controllers
{
    /// <summary>
    /// 掉落配置管理控制器
    /// </summary>
    public class DropConfigController : Controller
    {
        private readonly IDropConfigService _dropConfigService;

        public DropConfigController(IDropConfigService dropConfigService)
        {
            _dropConfigService = dropConfigService;
        }

        /// <summary>
        /// 掉落配置管理页面
        /// </summary>
        /// <returns>掉落配置管理页面</returns>
        public IActionResult Index()
        {
            return View();
        }

        /// <summary>
        /// 分页查询掉落配置列表（API）
        /// </summary>
        /// <param name="queryDto">查询条件</param>
        /// <returns>分页结果</returns>
        [HttpPost]
        public async Task<IActionResult> GetList([FromBody] DropConfigQueryDto queryDto)
        {
            try
            {
                var result = await _dropConfigService.GetPagedListAsync(queryDto);
                return Json(new
                {
                    code = 200,
                    data = result.Data,
                    total = result.TotalCount
                });
            }
            catch (Exception ex)
            {
                return Json(new
                {
                    code = 500,
                    message = $"获取掉落配置列表失败: {ex.Message}"
                });
            }
        }

        /// <summary>
        /// 根据ID获取掉落配置详情（API）
        /// </summary>
        /// <param name="id">掉落配置ID</param>
        /// <returns>掉落配置详情</returns>
        [HttpGet]
        public async Task<IActionResult> GetById(int id)
        {
            try
            {
                var result = await _dropConfigService.GetByIdAsync(id);
                if (result == null)
                {
                    return Json(new
                    {
                        code = 404,
                        message = "掉落配置不存在"
                    });
                }

                return Json(new
                {
                    code = 200,
                    data = result,
                    message = "获取掉落配置详情成功"
                });
            }
            catch (Exception ex)
            {
                return Json(new
                {
                    code = 500,
                    message = $"获取掉落配置详情失败: {ex.Message}"
                });
            }
        }

        /// <summary>
        /// 创建掉落配置（API）
        /// </summary>
        /// <param name="createDto">创建DTO</param>
        /// <returns>操作结果</returns>
        [HttpPost]
        public async Task<IActionResult> Create([FromBody] DropConfigCreateDto createDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return Json(new
                    {
                        code = 400,
                        message = "请求参数不正确",
                        errors = ModelState.Values.SelectMany(v => v.Errors.Select(e => e.ErrorMessage))
                    });
                }

                var result = await _dropConfigService.CreateAsync(createDto);
                return Json(new
                {
                    code = result.Success ? 200 : 400,
                    data = result.Data,
                    message = result.Message
                });
            }
            catch (Exception ex)
            {
                return Json(new
                {
                    code = 500,
                    message = $"创建掉落配置失败: {ex.Message}"
                });
            }
        }

        /// <summary>
        /// 更新掉落配置（API）
        /// </summary>
        /// <param name="updateDto">更新DTO</param>
        /// <returns>操作结果</returns>
        [HttpPost]
        public async Task<IActionResult> Update([FromBody] DropConfigUpdateDto updateDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return Json(new
                    {
                        code = 400,
                        message = "请求参数不正确",
                        errors = ModelState.Values.SelectMany(v => v.Errors.Select(e => e.ErrorMessage))
                    });
                }

                var result = await _dropConfigService.UpdateAsync(updateDto);
                return Json(new
                {
                    code = result.Success ? 200 : 400,
                    data = result.Data,
                    message = result.Message
                });
            }
            catch (Exception ex)
            {
                return Json(new
                {
                    code = 500,
                    message = $"更新掉落配置失败: {ex.Message}"
                });
            }
        }

        /// <summary>
        /// 删除掉落配置（API）
        /// </summary>
        /// <param name="deleteDto">删除DTO</param>
        /// <returns>操作结果</returns>
        [HttpPost]
        public async Task<IActionResult> Delete([FromBody] DropConfigDeleteDto deleteDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return Json(new
                    {
                        code = 400,
                        message = "请求参数不正确",
                        errors = ModelState.Values.SelectMany(v => v.Errors.Select(e => e.ErrorMessage))
                    });
                }

                var result = await _dropConfigService.DeleteAsync(deleteDto);
                return Json(new
                {
                    code = result.Success ? 200 : 400,
                    data = result.Data,
                    message = result.Message
                });
            }
            catch (Exception ex)
            {
                return Json(new
                {
                    code = 500,
                    message = $"删除掉落配置失败: {ex.Message}"
                });
            }
        }

        /// <summary>
        /// 批量删除掉落配置（API）
        /// </summary>
        /// <param name="ids">ID数组</param>
        /// <returns>操作结果</returns>
        [HttpPost]
        public async Task<IActionResult> BatchDelete([FromBody] int[] ids)
        {
            try
            {
                if (ids == null || ids.Length == 0)
                {
                    return Json(new
                    {
                        code = 400,
                        message = "请选择要删除的掉落配置"
                    });
                }

                var result = await _dropConfigService.BatchDeleteAsync(ids);
                return Json(new
                {
                    code = result.Success ? 200 : 400,
                    data = result.Data,
                    message = result.Message
                });
            }
            catch (Exception ex)
            {
                return Json(new
                {
                    code = 500,
                    message = $"批量删除掉落配置失败: {ex.Message}"
                });
            }
        }

        /// <summary>
        /// 获取掉落类型选项（API）
        /// </summary>
        /// <returns>掉落类型选项列表</returns>
        [HttpGet]
        public async Task<IActionResult> GetDropTypeOptions()
        {
            try
            {
                var result = await _dropConfigService.GetDropTypeOptionsAsync();
                return Json(new
                {
                    code = 200,
                    data = result,
                    message = "获取掉落类型选项成功"
                });
            }
            catch (Exception ex)
            {
                return Json(new
                {
                    code = 500,
                    message = $"获取掉落类型选项失败: {ex.Message}"
                });
            }
        }

        /// <summary>
        /// 获取地图选项（API）
        /// </summary>
        /// <returns>地图选项列表</returns>
        [HttpGet]
        public async Task<IActionResult> GetMapOptions()
        {
            try
            {
                var result = await _dropConfigService.GetMapOptionsAsync();
                return Json(new
                {
                    code = 200,
                    data = result,
                    message = "获取地图选项成功"
                });
            }
            catch (Exception ex)
            {
                return Json(new
                {
                    code = 500,
                    message = $"获取地图选项失败: {ex.Message}"
                });
            }
        }

        /// <summary>
        /// 根据地图ID获取怪物选项（API）
        /// </summary>
        /// <param name="mapId">地图ID</param>
        /// <returns>怪物选项列表</returns>
        [HttpGet]
        public async Task<IActionResult> GetMonsterOptions(int mapId)
        {
            try
            {
                var result = await _dropConfigService.GetMonsterOptionsByMapIdAsync(mapId);
                return Json(new
                {
                    code = 200,
                    data = result,
                    message = "获取怪物选项成功"
                });
            }
            catch (Exception ex)
            {
                return Json(new
                {
                    code = 500,
                    message = $"获取怪物选项失败: {ex.Message}"
                });
            }
        }

        /// <summary>
        /// 获取道具选项（API）
        /// </summary>
        /// <returns>道具选项列表</returns>
        [HttpGet]
        public async Task<IActionResult> GetItemOptions()
        {
            try
            {
                var result = await _dropConfigService.GetItemOptionsAsync();
                return Json(new
                {
                    code = 200,
                    data = result,
                    message = "获取道具选项成功"
                });
            }
            catch (Exception ex)
            {
                return Json(new
                {
                    code = 500,
                    message = $"获取道具选项失败: {ex.Message}"
                });
            }
        }

        /// <summary>
        /// 根据地图ID获取掉落配置列表（API）
        /// </summary>
        /// <param name="mapId">地图ID</param>
        /// <returns>掉落配置列表</returns>
        [HttpGet]
        public async Task<IActionResult> GetByMapId(int mapId)
        {
            try
            {
                var result = await _dropConfigService.GetByMapIdAsync(mapId);
                return Json(new
                {
                    code = 200,
                    data = result,
                    message = "获取地图掉落配置成功"
                });
            }
            catch (Exception ex)
            {
                return Json(new
                {
                    code = 500,
                    message = $"获取地图掉落配置失败: {ex.Message}"
                });
            }
        }

        /// <summary>
        /// 根据怪物ID获取掉落配置列表（API）
        /// </summary>
        /// <param name="monsterId">怪物ID</param>
        /// <returns>掉落配置列表</returns>
        [HttpGet]
        public async Task<IActionResult> GetByMonsterId(int monsterId)
        {
            try
            {
                var result = await _dropConfigService.GetByMonsterIdAsync(monsterId);
                return Json(new
                {
                    code = 200,
                    data = result,
                    message = "获取怪物掉落配置成功"
                });
            }
            catch (Exception ex)
            {
                return Json(new
                {
                    code = 500,
                    message = $"获取怪物掉落配置失败: {ex.Message}"
                });
            }
        }

        /// <summary>
        /// 创建多道具掉落配置（API）
        /// </summary>
        /// <param name="createDto">创建DTO</param>
        /// <returns>操作结果</returns>
        [HttpPost]
        public async Task<IActionResult> CreateMulti([FromBody] MultiDropConfigCreateDto createDto)
        {
            try
            {
                var result = await _dropConfigService.CreateMultiAsync(createDto);
                return Json(new
                {
                    code = result.Success ? 200 : 500,
                    data = result.Data,
                    message = result.Message
                });
            }
            catch (Exception ex)
            {
                return Json(new
                {
                    code = 500,
                    message = $"创建多道具掉落配置失败: {ex.Message}"
                });
            }
        }

        /// <summary>
        /// 更新多道具掉落配置（API）
        /// </summary>
        /// <param name="updateDto">更新DTO</param>
        /// <returns>操作结果</returns>
        [HttpPost]
        public async Task<IActionResult> UpdateMulti([FromBody] MultiDropConfigUpdateDto updateDto)
        {
            try
            {
                var result = await _dropConfigService.UpdateMultiAsync(updateDto);
                return Json(new
                {
                    code = result.Success ? 200 : 500,
                    data = result.Data,
                    message = result.Message
                });
            }
            catch (Exception ex)
            {
                return Json(new
                {
                    code = 500,
                    message = $"更新多道具掉落配置失败: {ex.Message}"
                });
            }
        }
    }
}