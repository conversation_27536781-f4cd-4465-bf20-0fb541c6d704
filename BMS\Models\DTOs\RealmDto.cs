using System.ComponentModel.DataAnnotations;

namespace BMS.Models.DTOs
{
    /// <summary>
    /// 境界信息DTO
    /// </summary>
    public class RealmDto
    {
        /// <summary>
        /// 自增主键ID
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 境界ID（唯一业务标识）
        /// </summary>
        public int RealmId { get; set; }

        /// <summary>
        /// 境界名称
        /// </summary>
        public string RealmName { get; set; } = string.Empty;

        /// <summary>
        /// 关联的宠物数量
        /// </summary>
        public int PetCount { get; set; }
    }

    /// <summary>
    /// 境界查询DTO
    /// </summary>
    public class RealmQueryDto : PagedQueryDto
    {
        /// <summary>
        /// 境界ID
        /// </summary>
        public int? RealmId { get; set; }

        /// <summary>
        /// 境界名称
        /// </summary>
        public string? RealmName { get; set; }
    }

    /// <summary>
    /// 境界创建DTO
    /// </summary>
    public class RealmCreateDto
    {
        /// <summary>
        /// 境界ID（唯一业务标识）
        /// </summary>
        [Required(ErrorMessage = "境界ID不能为空")]
        [Range(1, int.MaxValue, ErrorMessage = "境界ID必须大于0")]
        public int RealmId { get; set; }

        /// <summary>
        /// 境界名称
        /// </summary>
        [Required(ErrorMessage = "境界名称不能为空")]
        [StringLength(50, ErrorMessage = "境界名称长度不能超过50个字符")]
        public string RealmName { get; set; } = string.Empty;
    }

    /// <summary>
    /// 境界更新DTO
    /// </summary>
    public class RealmUpdateDto
    {
        /// <summary>
        /// 境界ID（唯一业务标识）
        /// </summary>
        [Required(ErrorMessage = "境界ID不能为空")]
        [Range(1, int.MaxValue, ErrorMessage = "境界ID必须大于0")]
        public int RealmId { get; set; }

        /// <summary>
        /// 境界名称
        /// </summary>
        [Required(ErrorMessage = "境界名称不能为空")]
        [StringLength(50, ErrorMessage = "境界名称长度不能超过50个字符")]
        public string RealmName { get; set; } = string.Empty;
    }

    /// <summary>
    /// 境界删除DTO
    /// </summary>
    public class RealmDeleteDto
    {
        /// <summary>
        /// 境界ID（唯一业务标识）
        /// </summary>
        [Required(ErrorMessage = "境界ID不能为空")]
        [Range(1, int.MaxValue, ErrorMessage = "境界ID必须大于0")]
        public int RealmId { get; set; }
    }
} 