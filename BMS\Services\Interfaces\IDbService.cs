using SqlSugar;

namespace BMS.Services.Interfaces
{
    /// <summary>
    /// 数据库服务接口
    /// </summary>
    public interface IDbService
    {
        /// <summary>
        /// 获取数据库客户端
        /// </summary>
        /// <returns>SqlSugar客户端</returns>
        ISqlSugarClient GetClient();

        /// <summary>
        /// 获取可查询对象
        /// </summary>
        /// <typeparam name="T">实体类型</typeparam>
        /// <returns>可查询对象</returns>
        ISugarQueryable<T> Queryable<T>() where T : class, new();

        /// <summary>
        /// 获取可更新对象
        /// </summary>
        /// <typeparam name="T">实体类型</typeparam>
        /// <returns>可更新对象</returns>
        IUpdateable<T> Updateable<T>() where T : class, new();

        /// <summary>
        /// 获取可更新对象(带实体参数)
        /// </summary>
        /// <typeparam name="T">实体类型</typeparam>
        /// <param name="entity">实体对象</param>
        /// <returns>可更新对象</returns>
        IUpdateable<T> Updateable<T>(T entity) where T : class, new();

        /// <summary>
        /// 获取可插入对象
        /// </summary>
        /// <typeparam name="T">实体类型</typeparam>
        /// <returns>可插入对象</returns>
        IInsertable<T> Insertable<T>() where T : class, new();

        /// <summary>
        /// 获取可插入对象(带实体参数)
        /// </summary>
        /// <typeparam name="T">实体类型</typeparam>
        /// <param name="entity">实体对象</param>
        /// <returns>可插入对象</returns>
        IInsertable<T> Insertable<T>(T entity) where T : class, new();

        /// <summary>
        /// 获取可删除对象
        /// </summary>
        /// <typeparam name="T">实体类型</typeparam>
        /// <returns>可删除对象</returns>
        IDeleteable<T> Deleteable<T>() where T : class, new();

        /// <summary>
        /// 开始事务
        /// </summary>
        /// <returns>事务对象</returns>
        ITenant BeginTran();
    }
} 