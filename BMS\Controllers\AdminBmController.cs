using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using BMS.Services.Interfaces;
using BMS.Models.DTOs;
using BMS.Models.Common;

namespace BMS.Controllers
{
    /// <summary>
    /// 管理员控制器
    /// </summary>
    [Authorize]
    public class AdminBmController : Controller
    {
        private readonly IAdminBmService _adminBmService;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="adminBmService">管理员服务</param>
        public AdminBmController(IAdminBmService adminBmService)
        {
            _adminBmService = adminBmService;
        }

        /// <summary>
        /// 管理员列表页面
        /// </summary>
        /// <param name="queryDto">查询条件</param>
        /// <returns>管理员列表视图</returns>
        public async Task<IActionResult> Index(AdminBmQueryDto? queryDto)
        {
            try
            {
                queryDto ??= new AdminBmQueryDto();
                var result = await _adminBmService.GetPagedListAsync(queryDto);
                
                ViewBag.QueryDto = queryDto;
                return View(result);
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = $"获取管理员列表失败：{ex.Message}";
                return View(new PagedResult<AdminBmDto>(new List<AdminBmDto>(), 1, 10, 0));
            }
        }

        /// <summary>
        /// 获取管理员列表API（JSON）
        /// </summary>
        /// <param name="queryDto">查询条件</param>
        /// <returns>JSON数据</returns>
        [HttpGet]
        public async Task<IActionResult> GetList([FromQuery] AdminBmQueryDto queryDto)
        {
            try
            {
                var result = await _adminBmService.GetPagedListAsync(queryDto);
                return Json(ApiResult<PagedResult<AdminBmDto>>.Ok(result));
            }
            catch (Exception ex)
            {
                return Json(ApiResult<PagedResult<AdminBmDto>>.Fail($"获取管理员列表失败：{ex.Message}"));
            }
        }

        /// <summary>
        /// 管理员详情页面
        /// </summary>
        /// <param name="id">管理员ID</param>
        /// <returns>详情视图</returns>
        public async Task<IActionResult> Details(int id)
        {
            try
            {
                var adminBm = await _adminBmService.GetByIdAsync(id);
                if (adminBm == null)
                {
                    TempData["ErrorMessage"] = "管理员不存在";
                    return RedirectToAction("Index");
                }

                return View(adminBm);
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = $"获取管理员详情失败：{ex.Message}";
                return RedirectToAction("Index");
            }
        }

        /// <summary>
        /// 创建管理员页面（GET）
        /// </summary>
        /// <returns>创建视图</returns>
        public IActionResult Create()
        {
            return View(new AdminBmCreateDto());
        }

        /// <summary>
        /// 创建管理员（POST）
        /// </summary>
        /// <param name="createDto">创建管理员DTO</param>
        /// <returns>操作结果</returns>
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create([FromForm] AdminBmCreateDto createDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return View(createDto);
                }

                var result = await _adminBmService.CreateAsync(createDto);
                
                if (result.Success)
                {
                    TempData["SuccessMessage"] = result.Message;
                    return RedirectToAction("Index");
                }
                else
                {
                    ModelState.AddModelError("", result.Message);
                    return View(createDto);
                }
            }
            catch (Exception ex)
            {
                ModelState.AddModelError("", $"创建管理员失败：{ex.Message}");
                return View(createDto);
            }
        }

        /// <summary>
        /// 创建管理员API（JSON）
        /// </summary>
        /// <param name="createDto">创建管理员DTO</param>
        /// <returns>JSON响应</returns>
        [HttpPost]
        [Route("AdminBm/CreateApi")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> CreateApi([FromBody] AdminBmCreateDto createDto)
        {
            // 异步创建管理员
            try
            {
                // 记录接收到的数据
                System.Diagnostics.Debug.WriteLine($"接收到的数据: Username={createDto?.Username}, Password={createDto?.Password}, Email={createDto?.Email}");
                
                // 验证表单数据
                if (!ModelState.IsValid)
                {
                    // 记录验证错误详情
                    System.Diagnostics.Debug.WriteLine("ModelState验证失败:");
                    foreach (var state in ModelState)
                    {
                        if (state.Value.Errors.Count > 0)
                        {
                            System.Diagnostics.Debug.WriteLine($"字段: {state.Key}, 错误: {string.Join(", ", state.Value.Errors.Select(e => e.ErrorMessage))}");
                        }
                    }
                    
                    // 获取表单验证错误信息
                    var errors = ModelState
                        .Where(x => x.Value.Errors.Count > 0)
                        .ToDictionary(x => x.Key, x => x.Value.Errors.First().ErrorMessage);
                    
                    // 返回表单验证失败信息
                    return Json(new 
                    { 
                        success = false, 
                        message = "表单验证失败", 
                        errors = errors 
                    });
                }

                // 调用服务层创建管理员
                var result = await _adminBmService.CreateAsync(createDto);
                
                // 判断创建结果
                if (result.Success)
                {
                    // 返回创建成功信息
                    return Json(new { success = true, message = result.Message });
                }
                else
                {
                    // 返回创建失败信息
                    return Json(new { success = false, message = result.Message });
                }
            }
            catch (Exception ex)
            {
                // 记录异常信息
                System.Diagnostics.Debug.WriteLine($"创建管理员异常: {ex.Message}");
                // 返回创建管理员失败信息
                return Json(new { success = false, message = $"创建管理员失败：{ex.Message}" });
            }
        }

        /// <summary>
        /// 编辑管理员页面（GET）
        /// </summary>
        /// <param name="id">管理员ID</param>
        /// <returns>编辑视图</returns>
        public async Task<IActionResult> Edit(int id)
        {
            try
            {
                var adminBm = await _adminBmService.GetByIdAsync(id);
                if (adminBm == null)
                {
                    TempData["ErrorMessage"] = "管理员不存在";
                    return RedirectToAction("Index");
                }

                var updateDto = new AdminBmUpdateDto
                {
                    Id = adminBm.Id,
                    Username = adminBm.Username,
                    RealName = adminBm.RealName,
                    Phone = adminBm.Phone,
                    Email = adminBm.Email,
                    Status = adminBm.Status
                };

                return View(updateDto);
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = $"获取管理员信息失败：{ex.Message}";
                return RedirectToAction("Index");
            }
        }

        /// <summary>
        /// 编辑管理员（POST）
        /// </summary>
        /// <param name="updateDto">更新管理员DTO</param>
        /// <returns>操作结果</returns>
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit([FromForm] AdminBmUpdateDto updateDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return View(updateDto);
                }

                var result = await _adminBmService.UpdateAsync(updateDto);
                
                if (result.Success)
                {
                    TempData["SuccessMessage"] = result.Message;
                    return RedirectToAction("Index");
                }
                else
                {
                    ModelState.AddModelError("", result.Message);
                    return View(updateDto);
                }
            }
            catch (Exception ex)
            {
                ModelState.AddModelError("", $"更新管理员失败：{ex.Message}");
                return View(updateDto);
            }
        }

        /// <summary>
        /// 编辑管理员API（JSON）
        /// </summary>
        /// <param name="updateDto">更新管理员DTO</param>
        /// <returns>JSON响应</returns>
        [HttpPost]
        [Route("AdminBm/EditApi")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> EditApi([FromBody] AdminBmUpdateDto updateDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    var errors = ModelState
                        .Where(x => x.Value.Errors.Count > 0)
                        .ToDictionary(x => x.Key, x => x.Value.Errors.First().ErrorMessage);
                    
                    return Json(new 
                    { 
                        success = false, 
                        message = "表单验证失败", 
                        errors = errors 
                    });
                }

                var result = await _adminBmService.UpdateAsync(updateDto);
                
                if (result.Success)
                {
                    return Json(new { success = true, message = result.Message });
                }
                else
                {
                    return Json(new { success = false, message = result.Message });
                }
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = $"更新管理员失败：{ex.Message}" });
            }
        }

        /// <summary>
        /// 删除管理员（AJAX）
        /// </summary>
        /// <param name="dto">删除管理员DTO</param>
        /// <returns>JSON结果</returns>
        [HttpPost]
        public async Task<IActionResult> Delete([FromBody] AdminBmDeleteDto dto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    var errors = ModelState.Values
                        .SelectMany(v => v.Errors)
                        .Select(e => e.ErrorMessage)
                        .ToList();
                    return Json(ApiResult<bool>.Fail(string.Join(", ", errors)));
                }

                var result = await _adminBmService.DeleteAsync(dto.Id);
                return Json(result);
            }
            catch (Exception ex)
            {
                return Json(ApiResult<bool>.Fail($"删除管理员失败：{ex.Message}"));
            }
        }

        /// <summary>
        /// 更改管理员状态（AJAX）
        /// </summary>
        /// <param name="dto">更改状态DTO</param>
        /// <returns>JSON结果</returns>
        [HttpPost]
        public async Task<IActionResult> ChangeStatus([FromBody] AdminBmChangeStatusDto dto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    var errors = ModelState.Values
                        .SelectMany(v => v.Errors)
                        .Select(e => e.ErrorMessage)
                        .ToList();
                    return Json(ApiResult<bool>.Fail(string.Join(", ", errors)));
                }

                var result = await _adminBmService.ChangeStatusAsync(dto.Id, dto.Status);
                return Json(result);
            }
            catch (Exception ex)
            {
                return Json(ApiResult<bool>.Fail($"更改状态失败：{ex.Message}"));
            }
        }

        /// <summary>
        /// 重置密码（AJAX）
        /// </summary>
        /// <param name="dto">重置密码请求</param>
        /// <returns>JSON结果</returns>
        [HttpPost]
        public async Task<IActionResult> ResetPassword([FromBody] ResetPasswordDto dto)
        {
            try
            {
                if (string.IsNullOrEmpty(dto.NewPassword) || dto.NewPassword.Length < 6)
                {
                    return Json(ApiResult<bool>.Fail("新密码长度不能少于6位"));
                }

                var result = await _adminBmService.ResetPasswordAsync(dto.Id, dto.NewPassword);
                return Json(result);
            }
            catch (Exception ex)
            {
                return Json(ApiResult<bool>.Fail($"重置密码失败：{ex.Message}"));
            }
        }

        /// <summary>
        /// 检查用户名是否存在（AJAX）
        /// </summary>
        /// <param name="username">用户名</param>
        /// <param name="id">排除的ID（编辑时使用）</param>
        /// <returns>JSON结果</returns>
        [HttpGet]
        public async Task<IActionResult> CheckUsername(string username, int? id = null)
        {
            try
            {
                bool exists = await _adminBmService.CheckUsernameExistsAsync(username, id);
                return Json(new { exists });
            }
            catch (Exception ex)
            {
                return Json(new { exists = false, error = ex.Message });
            }
        }
    }

    /// <summary>
    /// 重置密码DTO
    /// </summary>
    public class ResetPasswordDto
    {
        /// <summary>
        /// 管理员ID
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 新密码
        /// </summary>
        public string NewPassword { get; set; } = string.Empty;
    }
} 