using System.ComponentModel.DataAnnotations;

namespace BMS.Models.DTOs
{
    /// <summary>
    /// 地图详情DTO
    /// </summary>
    public class MapDetailDto
    {
        /// <summary>
        /// 自增主键
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 地图ID
        /// </summary>
        public int MapId { get; set; }

        /// <summary>
        /// 限制成长
        /// </summary>
        public decimal LimitGrowth { get; set; }

        /// <summary>
        /// 限制等级
        /// </summary>
        public int LimitLevel { get; set; }

        /// <summary>
        /// 限制钥匙（1为限制，0为不限制）
        /// </summary>
        public bool LimitKey { get; set; }

        /// <summary>
        /// 最小金币
        /// </summary>
        public long MinGold { get; set; }

        /// <summary>
        /// 最小元宝
        /// </summary>
        public int MinYuanbao { get; set; }

        /// <summary>
        /// 最大金币
        /// </summary>
        public long MaxGold { get; set; }

        /// <summary>
        /// 最大元宝
        /// </summary>
        public int MaxYuanbao { get; set; }

        /// <summary>
        /// 最大掉落
        /// </summary>
        public int MaxDrop { get; set; }

        /// <summary>
        /// 最小掉落
        /// </summary>
        public int MinDrop { get; set; }

        /// <summary>
        /// 地图图标
        /// </summary>
        public string? Icon { get; set; }

        /// <summary>
        /// 地图类型
        /// </summary>
        public int Type { get; set; }

        /// <summary>
        /// 地图名称
        /// </summary>
        public string? MapName { get; set; }
    }

    /// <summary>
    /// 地图详情查询DTO
    /// </summary>
    public class MapDetailQueryDto
    {
        /// <summary>
        /// 地图ID
        /// </summary>
        public int? MapId { get; set; }

        /// <summary>
        /// 地图类型
        /// </summary>
        public int? Type { get; set; }

        /// <summary>
        /// 页码
        /// </summary>
        public int Page { get; set; } = 1;

        /// <summary>
        /// 每页数量
        /// </summary>
        public int PageSize { get; set; } = 10;
    }

    /// <summary>
    /// 创建地图详情DTO
    /// </summary>
    public class MapDetailCreateDto
    {
        /// <summary>
        /// 地图ID
        /// </summary>
        [Required(ErrorMessage = "地图ID不能为空")]
        [Range(1, int.MaxValue, ErrorMessage = "地图ID必须大于0")]
        public int MapId { get; set; }

        /// <summary>
        /// 限制成长
        /// </summary>
        [Range(0, double.MaxValue, ErrorMessage = "限制成长不能为负数")]
        public decimal LimitGrowth { get; set; }

        /// <summary>
        /// 限制等级
        /// </summary>
        [Range(0, int.MaxValue, ErrorMessage = "限制等级不能为负数")]
        public int LimitLevel { get; set; }

        /// <summary>
        /// 限制钥匙（1为限制，0为不限制）
        /// </summary>
        public bool LimitKey { get; set; }

        /// <summary>
        /// 最小金币
        /// </summary>
        [Range(0, long.MaxValue, ErrorMessage = "最小金币不能为负数")]
        public long MinGold { get; set; }

        /// <summary>
        /// 最小元宝
        /// </summary>
        [Range(0, int.MaxValue, ErrorMessage = "最小元宝不能为负数")]
        public int MinYuanbao { get; set; }

        /// <summary>
        /// 最大金币
        /// </summary>
        [Range(0, long.MaxValue, ErrorMessage = "最大金币不能为负数")]
        public long MaxGold { get; set; }

        /// <summary>
        /// 最大元宝
        /// </summary>
        [Range(0, int.MaxValue, ErrorMessage = "最大元宝不能为负数")]
        public int MaxYuanbao { get; set; }

        /// <summary>
        /// 最大掉落
        /// </summary>
        [Range(0, int.MaxValue, ErrorMessage = "最大掉落不能为负数")]
        public int MaxDrop { get; set; }

        /// <summary>
        /// 最小掉落
        /// </summary>
        [Range(0, int.MaxValue, ErrorMessage = "最小掉落不能为负数")]
        public int MinDrop { get; set; }

        /// <summary>
        /// 地图图标
        /// </summary>
        [StringLength(100, ErrorMessage = "地图图标路径长度不能超过100个字符")]
        public string? Icon { get; set; }

        /// <summary>
        /// 地图类型
        /// </summary>
        [Range(0, int.MaxValue, ErrorMessage = "地图类型不能为负数")]
        public int Type { get; set; }
    }

    /// <summary>
    /// 更新地图详情DTO
    /// </summary>
    public class MapDetailUpdateDto
    {
        /// <summary>
        /// 自增主键
        /// </summary>
        [Required(ErrorMessage = "ID不能为空")]
        public int Id { get; set; }

        /// <summary>
        /// 地图ID
        /// </summary>
        [Required(ErrorMessage = "地图ID不能为空")]
        [Range(1, int.MaxValue, ErrorMessage = "地图ID必须大于0")]
        public int MapId { get; set; }

        /// <summary>
        /// 限制成长
        /// </summary>
        [Range(0, double.MaxValue, ErrorMessage = "限制成长不能为负数")]
        public decimal LimitGrowth { get; set; }

        /// <summary>
        /// 限制等级
        /// </summary>
        [Range(0, int.MaxValue, ErrorMessage = "限制等级不能为负数")]
        public int LimitLevel { get; set; }

        /// <summary>
        /// 限制钥匙（1为限制，0为不限制）
        /// </summary>
        public bool LimitKey { get; set; }

        /// <summary>
        /// 最小金币
        /// </summary>
        [Range(0, long.MaxValue, ErrorMessage = "最小金币不能为负数")]
        public long MinGold { get; set; }

        /// <summary>
        /// 最小元宝
        /// </summary>
        [Range(0, int.MaxValue, ErrorMessage = "最小元宝不能为负数")]
        public int MinYuanbao { get; set; }

        /// <summary>
        /// 最大金币
        /// </summary>
        [Range(0, long.MaxValue, ErrorMessage = "最大金币不能为负数")]
        public long MaxGold { get; set; }

        /// <summary>
        /// 最大元宝
        /// </summary>
        [Range(0, int.MaxValue, ErrorMessage = "最大元宝不能为负数")]
        public int MaxYuanbao { get; set; }

        /// <summary>
        /// 最大掉落
        /// </summary>
        [Range(0, int.MaxValue, ErrorMessage = "最大掉落不能为负数")]
        public int MaxDrop { get; set; }

        /// <summary>
        /// 最小掉落
        /// </summary>
        [Range(0, int.MaxValue, ErrorMessage = "最小掉落不能为负数")]
        public int MinDrop { get; set; }

        /// <summary>
        /// 地图图标
        /// </summary>
        [StringLength(100, ErrorMessage = "地图图标路径长度不能超过100个字符")]
        public string? Icon { get; set; }

        /// <summary>
        /// 地图类型
        /// </summary>
        [Range(0, int.MaxValue, ErrorMessage = "地图类型不能为负数")]
        public int Type { get; set; }
    }

    /// <summary>
    /// 删除地图详情DTO
    /// </summary>
    public class MapDetailDeleteDto
    {
        /// <summary>
        /// 要删除的地图详情ID
        /// </summary>
        [Required(ErrorMessage = "ID不能为空")]
        [Range(1, int.MaxValue, ErrorMessage = "ID必须大于0")]
        public int Id { get; set; }
    }
} 