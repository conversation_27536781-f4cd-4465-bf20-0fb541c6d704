using System;
using SqlSugar;

namespace BMS.Models.Entities
{
    // pet_evolution_config 类已移动到独立文件 pet_evolution_config.cs
    /*
    /// <summary>
    /// 宠物进化配置表模型
    /// </summary>
    [SugarTable("pet_evolution_config")]
    public class pet_evolution_config
    {
        /// <summary>
        /// 自增主键
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public int id { get; set; }

        /// <summary>
        /// 宠物编号（关联pet_config.pet_no）
        /// </summary>
        public int pet_no { get; set; }

        /// <summary>
        /// 进化路线类型（A/B）
        /// </summary>
        public string evolution_type { get; set; }

        /// <summary>
        /// 进化后宠物编号
        /// </summary>
        public int target_pet_no { get; set; }

        /// <summary>
        /// 所需等级
        /// </summary>
        public int required_level { get; set; } = 40;

        /// <summary>
        /// 所需道具ID
        /// </summary>
        public string required_item_id { get; set; }

        /// <summary>
        /// 所需道具数量
        /// </summary>
        public int required_item_count { get; set; } = 1;

        /// <summary>
        /// 消耗金币
        /// </summary>
        public long cost_gold { get; set; } = 1000;

        /// <summary>
        /// 最小成长加成
        /// </summary>
        public decimal growth_min { get; set; } = 0.100m;

        /// <summary>
        /// 最大成长加成
        /// </summary>
        public decimal growth_max { get; set; } = 0.500m;

        /// <summary>
        /// 成功率(%)
        /// </summary>
        public decimal success_rate { get; set; } = 100.00m;

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool is_active { get; set; } = true;

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime create_time { get; set; } = DateTime.Now;
    }
    */

    // pet_evolution_log 类已移动到独立文件 pet_evolution_log.cs
    /*
    /// <summary>
    /// 宠物进化记录表模型
    /// </summary>
    [SugarTable("pet_evolution_log")]
    public class pet_evolution_log
    {
        /// <summary>
        /// 自增主键
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public int id { get; set; }

        /// <summary>
        /// 用户ID
        /// </summary>
        public int user_id { get; set; }

        /// <summary>
        /// 用户宠物ID
        /// </summary>
        public int user_pet_id { get; set; }

        /// <summary>
        /// 进化路线（A/B）
        /// </summary>
        public string evolution_type { get; set; }

        /// <summary>
        /// 进化前宠物编号
        /// </summary>
        public int before_pet_no { get; set; }

        /// <summary>
        /// 进化后宠物编号
        /// </summary>
        public int after_pet_no { get; set; }

        /// <summary>
        /// 进化前成长
        /// </summary>
        public decimal before_growth { get; set; }

        /// <summary>
        /// 进化后成长
        /// </summary>
        public decimal after_growth { get; set; }

        /// <summary>
        /// 成长增加值
        /// </summary>
        public decimal growth_increase { get; set; }

        /// <summary>
        /// 使用的道具ID
        /// </summary>
        public string used_item_id { get; set; }

        /// <summary>
        /// 消耗金币
        /// </summary>
        public long cost_gold { get; set; }

        /// <summary>
        /// 是否成功
        /// </summary>
        public bool is_success { get; set; } = true;

        /// <summary>
        /// 进化时间
        /// </summary>
        public DateTime create_time { get; set; } = DateTime.Now;
    }
    */

    // pet_system_config 类已移动到独立文件 pet_system_config.cs
    /*
    /// <summary>
    /// 宠物系统配置表模型
    /// </summary>
    [SugarTable("pet_system_config")]
    public class pet_system_config
    {
        /// <summary>
        /// 自增主键
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public int id { get; set; }

        /// <summary>
        /// 配置键
        /// </summary>
        public string config_key { get; set; }

        /// <summary>
        /// 配置值
        /// </summary>
        public string config_value { get; set; }

        /// <summary>
        /// 配置类型
        /// </summary>
        public string config_type { get; set; } = "STRING";

        /// <summary>
        /// 系统类型
        /// </summary>
        public string system_type { get; set; }

        /// <summary>
        /// 配置描述
        /// </summary>
        public string description { get; set; }

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool is_active { get; set; } = true;

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime create_time { get; set; } = DateTime.Now;

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime update_time { get; set; } = DateTime.Now;
    }
    */
}
