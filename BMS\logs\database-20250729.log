[2025-07-29 22:47:00.522 +08:00 INF] 🚀 应用程序启动，日志系统已配置
[2025-07-29 22:47:00.549 +08:00 DBG] 🔧 调试级别日志测试
[2025-07-29 22:47:00.554 +08:00 INF] 初始化数据库连接: Server=47.113.151.220;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-29 22:47:00.556 +08:00 INF] 开始测试数据库连接...
[2025-07-29 22:47:00.617 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-29 22:47:01.195 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-29 22:47:01.205 +08:00 INF] ✅ 数据库连接测试成功
[2025-07-29 22:47:01.207 +08:00 INF] ✅ DbContext 初始化成功，日志文件位置: logs/
[2025-07-29 22:48:06.776 +08:00 INF] 🚀 应用程序启动，日志系统已配置
[2025-07-29 22:48:06.815 +08:00 DBG] 🔧 调试级别日志测试
[2025-07-29 22:48:06.833 +08:00 INF] 初始化数据库连接: Server=47.113.151.220;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-29 22:48:06.836 +08:00 INF] 开始测试数据库连接...
[2025-07-29 22:48:06.923 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-29 22:48:07.532 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-29 22:48:07.542 +08:00 INF] ✅ 数据库连接测试成功
[2025-07-29 22:48:07.543 +08:00 INF] ✅ DbContext 初始化成功，日志文件位置: logs/
[2025-07-29 22:48:13.955 +08:00 INF] 🚀 应用程序启动，日志系统已配置
[2025-07-29 22:48:13.994 +08:00 DBG] 🔧 调试级别日志测试
[2025-07-29 22:48:14.036 +08:00 INF] 初始化数据库连接: Server=47.113.151.220;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-29 22:48:14.041 +08:00 INF] 开始测试数据库连接...
[2025-07-29 22:48:14.140 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-29 22:48:14.701 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-29 22:48:14.714 +08:00 INF] ✅ 数据库连接测试成功
[2025-07-29 22:48:14.726 +08:00 INF] ✅ DbContext 初始化成功，日志文件位置: logs/
[2025-07-29 22:48:15.957 +08:00 WRN] Failed to determine the https port for redirect.
[2025-07-29 22:48:18.695 +08:00 INF] 初始化数据库连接: Server=47.113.151.220;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-29 22:48:18.697 +08:00 INF] 开始测试数据库连接...
[2025-07-29 22:48:18.702 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-29 22:48:18.763 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-29 22:48:18.774 +08:00 INF] ✅ 数据库连接测试成功
[2025-07-29 22:48:26.352 +08:00 INF] 初始化数据库连接: Server=47.113.151.220;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-29 22:48:26.357 +08:00 INF] 开始测试数据库连接...
[2025-07-29 22:48:26.363 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-29 22:48:26.412 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-29 22:48:26.417 +08:00 INF] ✅ 数据库连接测试成功
[2025-07-29 22:48:26.504 +08:00 DBG] 执行SQL: SELECT `id`,`username`,`password`,`real_name`,`phone`,`email`,`status`,`create_time`,`update_time`,`last_login_time` FROM `admin_bm`   WHERE ( `username` = @username0 )   LIMIT 0,1 | 参数: [@username0=admin]
[2025-07-29 22:48:26.580 +08:00 DBG] SQL执行完成: SELECT `id`,`username`,`password`,`real_name`,`phone`,`email`,`status`,`create_time`,`update_time`,`last_login_time` FROM `admin_bm`   WHERE ( `username` = @username0 )   LIMIT 0,1
[2025-07-29 22:48:26.707 +08:00 DBG] 执行SQL: UPDATE `admin_bm`  SET
            `update_time` = @constant1 , `last_login_time` = @constant0   WHERE ( `id` = @id2 ) | 参数: [@constant0=2025/7/29 22:48:26, @constant1=2025/7/29 22:48:26, @id2=1, @id=0]
[2025-07-29 22:48:26.779 +08:00 DBG] SQL执行完成: UPDATE `admin_bm`  SET
            `update_time` = @constant1 , `last_login_time` = @constant0   WHERE ( `id` = @id2 )
[2025-07-29 22:48:33.476 +08:00 INF] 初始化数据库连接: Server=47.113.151.220;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-29 22:48:33.478 +08:00 INF] 开始测试数据库连接...
[2025-07-29 22:48:33.481 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-29 22:48:33.530 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-29 22:48:33.533 +08:00 INF] ✅ 数据库连接测试成功
[2025-07-29 22:48:33.669 +08:00 INF] 初始化数据库连接: Server=47.113.151.220;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-29 22:48:33.669 +08:00 INF] 初始化数据库连接: Server=47.113.151.220;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-29 22:48:33.669 +08:00 INF] 初始化数据库连接: Server=47.113.151.220;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-29 22:48:33.669 +08:00 INF] 初始化数据库连接: Server=47.113.151.220;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-29 22:48:33.671 +08:00 INF] 开始测试数据库连接...
[2025-07-29 22:48:33.672 +08:00 INF] 开始测试数据库连接...
[2025-07-29 22:48:33.674 +08:00 INF] 开始测试数据库连接...
[2025-07-29 22:48:33.675 +08:00 INF] 开始测试数据库连接...
[2025-07-29 22:48:33.678 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-29 22:48:33.679 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-29 22:48:33.680 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-29 22:48:33.681 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-29 22:48:33.730 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-29 22:48:33.731 +08:00 INF] ✅ 数据库连接测试成功
[2025-07-29 22:48:33.793 +08:00 DBG] 执行SQL: SELECT  CAST(`item_no` AS CHAR) AS `Value` , `name` AS `Label`  FROM `item_config`  
[2025-07-29 22:48:33.845 +08:00 DBG] SQL执行完成: SELECT  CAST(`item_no` AS CHAR) AS `Value` , `name` AS `Label`  FROM `item_config`  
[2025-07-29 22:48:33.967 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-29 22:48:33.968 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-29 22:48:33.969 +08:00 INF] ✅ 数据库连接测试成功
[2025-07-29 22:48:33.973 +08:00 INF] ✅ 数据库连接测试成功
[2025-07-29 22:48:33.978 +08:00 DBG] 执行SQL: SELECT  CAST(`map_id` AS CHAR) AS `Value` , `map_name` AS `Label`  FROM `map_config`  
[2025-07-29 22:48:33.991 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-29 22:48:33.999 +08:00 INF] ✅ 数据库连接测试成功
[2025-07-29 22:48:34.055 +08:00 DBG] SQL执行完成: SELECT  CAST(`map_id` AS CHAR) AS `Value` , `map_name` AS `Label`  FROM `map_config`  
[2025-07-29 22:48:34.222 +08:00 DBG] 执行SQL: SELECT Count(*) FROM `drop_config` `dc` Left JOIN `map_config` `mc` ON ( `dc`.`map_id` = `mc`.`map_id` )  Left JOIN `map_monster` `mm` ON (( `dc`.`monster_id` = `mm`.`monster_id` ) AND ( `dc`.`map_id` = `mm`.`map_id` ))  Left JOIN `item_config` `ic` ON ( `dc`.`item_id` = CAST(`ic`.`item_no` AS CHAR))    | 参数: [@MethodConst0=, @MethodConst1=, @MethodConst2=1.0, @MethodConst3=1, @MethodConst4=1, @MethodConst5=2025/7/29 22:48:34, @MethodConst6=2025/7/29 22:48:34]
[2025-07-29 22:48:34.300 +08:00 DBG] SQL执行完成: SELECT Count(*) FROM `drop_config` `dc` Left JOIN `map_config` `mc` ON ( `dc`.`map_id` = `mc`.`map_id` )  Left JOIN `map_monster` `mm` ON (( `dc`.`monster_id` = `mm`.`monster_id` ) AND ( `dc`.`map_id` = `mm`.`map_id` ))  Left JOIN `item_config` `ic` ON ( `dc`.`item_id` = CAST(`ic`.`item_no` AS CHAR))   
[2025-07-29 22:48:34.306 +08:00 DBG] 执行SQL: SELECT  `dc`.`id` AS `Id` , `dc`.`map_id` AS `MapId` , IFNULL(`mc`.`map_name`,@MethodConst7) AS `MapName` , `dc`.`drop_type` AS `DropType` , `dc`.`monster_id` AS `MonsterId` , `mm`.`monster_name` AS `MonsterName` , `dc`.`item_id` AS `ItemId` , IFNULL(`ic`.`name`,@MethodConst8) AS `ItemName` , IFNULL(`dc`.`drop_rate`,@MethodConst9) AS `DropRate` , IFNULL(`dc`.`min_count`,@MethodConst10) AS `MinCount` , IFNULL(`dc`.`max_count`,@MethodConst11) AS `MaxCount` , `dc`.`remark` AS `Remark` , IFNULL(`dc`.`create_time`,@MethodConst12) AS `CreateTime` , IFNULL(`dc`.`update_time`,@MethodConst13) AS `UpdateTime` , `dc`.`drop_items_json` AS `DropItemsJson`  FROM `drop_config` `dc` Left JOIN `map_config` `mc` ON ( `dc`.`map_id` = `mc`.`map_id` )  Left JOIN `map_monster` `mm` ON (( `dc`.`monster_id` = `mm`.`monster_id` ) AND ( `dc`.`map_id` = `mm`.`map_id` ))  Left JOIN `item_config` `ic` ON ( `dc`.`item_id` = CAST(`ic`.`item_no` AS CHAR))     ORDER BY `dc`.`create_time` DESC LIMIT 0,10 | 参数: [@MethodConst0=, @MethodConst1=, @MethodConst2=1.0, @MethodConst3=1, @MethodConst4=1, @MethodConst5=2025/7/29 22:48:34, @MethodConst6=2025/7/29 22:48:34, @MethodConst7=, @MethodConst8=, @MethodConst9=1.0, @MethodConst10=1, @MethodConst11=1, @MethodConst12=2025/7/29 22:48:34, @MethodConst13=2025/7/29 22:48:34]
[2025-07-29 22:48:34.381 +08:00 DBG] SQL执行完成: SELECT  `dc`.`id` AS `Id` , `dc`.`map_id` AS `MapId` , IFNULL(`mc`.`map_name`,@MethodConst7) AS `MapName` , `dc`.`drop_type` AS `DropType` , `dc`.`monster_id` AS `MonsterId` , `mm`.`monster_name` AS `MonsterName` , `dc`.`item_id` AS `ItemId` , IFNULL(`ic`.`name`,@MethodConst8) AS `ItemName` , IFNULL(`dc`.`drop_rate`,@MethodConst9) AS `DropRate` , IFNULL(`dc`.`min_count`,@MethodConst10) AS `MinCount` , IFNULL(`dc`.`max_count`,@MethodConst11) AS `MaxCount` , `dc`.`remark` AS `Remark` , IFNULL(`dc`.`create_time`,@MethodConst12) AS `CreateTime` , IFNULL(`dc`.`update_time`,@MethodConst13) AS `UpdateTime` , `dc`.`drop_items_json` AS `DropItemsJson`  FROM `drop_config` `dc` Left JOIN `map_config` `mc` ON ( `dc`.`map_id` = `mc`.`map_id` )  Left JOIN `map_monster` `mm` ON (( `dc`.`monster_id` = `mm`.`monster_id` ) AND ( `dc`.`map_id` = `mm`.`map_id` ))  Left JOIN `item_config` `ic` ON ( `dc`.`item_id` = CAST(`ic`.`item_no` AS CHAR))     ORDER BY `dc`.`create_time` DESC LIMIT 0,10
[2025-07-29 22:48:44.325 +08:00 INF] 初始化数据库连接: Server=47.113.151.220;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-29 22:48:44.327 +08:00 INF] 开始测试数据库连接...
[2025-07-29 22:48:44.330 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-29 22:48:44.372 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-29 22:48:44.373 +08:00 INF] ✅ 数据库连接测试成功
[2025-07-29 22:48:44.401 +08:00 DBG] 执行SQL: SELECT  DISTINCT `type` FROM `item_config`  WHERE NOT( `type` IS NULL   OR `type`='') 
[2025-07-29 22:48:44.457 +08:00 DBG] SQL执行完成: SELECT  DISTINCT `type` FROM `item_config`  WHERE NOT( `type` IS NULL   OR `type`='') 
[2025-07-29 22:48:44.498 +08:00 DBG] 执行SQL: SELECT  DISTINCT `quality` FROM `item_config`  WHERE NOT( `quality` IS NULL   OR `quality`='') 
[2025-07-29 22:48:44.560 +08:00 DBG] SQL执行完成: SELECT  DISTINCT `quality` FROM `item_config`  WHERE NOT( `quality` IS NULL   OR `quality`='') 
[2025-07-29 22:49:02.621 +08:00 INF] 初始化数据库连接: Server=47.113.151.220;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-29 22:49:02.623 +08:00 INF] 开始测试数据库连接...
[2025-07-29 22:49:02.625 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-29 22:49:02.667 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-29 22:49:02.670 +08:00 INF] ✅ 数据库连接测试成功
[2025-07-29 22:49:02.673 +08:00 DBG] 执行SQL: SELECT  DISTINCT `type` FROM `item_config`  WHERE NOT( `type` IS NULL   OR `type`='') 
[2025-07-29 22:49:02.717 +08:00 DBG] SQL执行完成: SELECT  DISTINCT `type` FROM `item_config`  WHERE NOT( `type` IS NULL   OR `type`='') 
[2025-07-29 22:49:02.720 +08:00 DBG] 执行SQL: SELECT  DISTINCT `quality` FROM `item_config`  WHERE NOT( `quality` IS NULL   OR `quality`='') 
[2025-07-29 22:49:02.766 +08:00 DBG] SQL执行完成: SELECT  DISTINCT `quality` FROM `item_config`  WHERE NOT( `quality` IS NULL   OR `quality`='') 
