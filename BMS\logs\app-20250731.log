[2025-07-31 14:33:06.317 +08:00 INF] BMS: 🚀 应用程序启动，日志系统已配置
[2025-07-31 14:33:06.465 +08:00 DBG] BMS: 🔧 调试级别日志测试
[2025-07-31 14:33:06.509 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 14:33:06.541 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 14:33:06.679 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 14:33:07.260 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 14:33:07.274 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 14:33:07.279 +08:00 INF] BMS: ✅ DbContext 初始化成功，日志文件位置: logs/
[2025-07-31 14:33:08.729 +08:00 WRN] Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware: Failed to determine the https port for redirect.
[2025-07-31 14:33:50.817 +08:00 INF] BMS: 🚀 应用程序启动，日志系统已配置
[2025-07-31 14:33:50.847 +08:00 DBG] BMS: 🔧 调试级别日志测试
[2025-07-31 14:33:50.865 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 14:33:50.868 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 14:33:50.974 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 14:33:51.538 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 14:33:51.547 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 14:33:51.549 +08:00 INF] BMS: ✅ DbContext 初始化成功，日志文件位置: logs/
[2025-07-31 14:33:52.993 +08:00 WRN] Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware: Failed to determine the https port for redirect.
[2025-07-31 14:33:58.902 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 14:33:58.904 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 14:33:58.915 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 14:33:58.956 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 14:33:58.961 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 14:33:59.076 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  DISTINCT `type` FROM `item_config`  WHERE NOT( `type` IS NULL   OR `type`='') 
[2025-07-31 14:33:59.164 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  DISTINCT `type` FROM `item_config`  WHERE NOT( `type` IS NULL   OR `type`='') 
[2025-07-31 14:33:59.219 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  DISTINCT `quality` FROM `item_config`  WHERE NOT( `quality` IS NULL   OR `quality`='') 
[2025-07-31 14:33:59.344 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  DISTINCT `quality` FROM `item_config`  WHERE NOT( `quality` IS NULL   OR `quality`='') 
[2025-07-31 14:33:59.769 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 14:33:59.772 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 14:33:59.776 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 14:33:59.816 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 14:33:59.818 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 14:34:00.005 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-07-31 14:34:00.218 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-07-31 14:34:00.227 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 0,10
[2025-07-31 14:34:00.977 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 0,10
[2025-07-31 14:34:12.712 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 14:34:12.733 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 14:34:12.745 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 14:34:12.795 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 14:34:12.798 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 14:34:12.815 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `equip_type_id` AS `EquipTypeId` , `type_name` AS `TypeName`  FROM `equipment_type`  WHERE ( `is_active` = @is_active0 )ORDER BY `sort_order` ASC,`id` ASC  | 参数: [@is_active0=True]
[2025-07-31 14:34:12.890 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `equip_type_id` AS `EquipTypeId` , `type_name` AS `TypeName`  FROM `equipment_type`  WHERE ( `is_active` = @is_active0 )ORDER BY `sort_order` ASC,`id` ASC 
[2025-07-31 14:34:13.133 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 14:34:13.135 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 14:34:13.139 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 14:34:13.177 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 14:34:13.180 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 14:34:13.206 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  
[2025-07-31 14:34:13.248 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  
[2025-07-31 14:34:13.261 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment_detail`  
[2025-07-31 14:34:13.303 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment_detail`  
[2025-07-31 14:34:13.308 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment_type`  
[2025-07-31 14:34:13.349 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment_type`  
[2025-07-31 14:34:13.366 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )   
[2025-07-31 14:34:13.415 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )   
[2025-07-31 14:34:13.430 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `e`.`equip_id` AS `EquipId` , `e`.`class_id` AS `ClassId` , `e`.`name` AS `Name` , `e`.`icon` AS `Icon` , `e`.`equip_type_id` AS `EquipTypeId` , `et`.`type_name` AS `EquipTypeName` , `e`.`element` AS `Element` , IFNULL(`e`.`slot`,@MethodConst13) AS `Slot` , IFNULL(`e`.`strengthen_level`,@MethodConst14) AS `StrengthenLevel` , `e`.`suit_id` AS `SuitId` , IFNULL(`ed`.`atk`,@MethodConst15) AS `Atk` , IFNULL(`ed`.`hit`,@MethodConst16) AS `Hit` , IFNULL(`ed`.`def`,@MethodConst17) AS `Def` , IFNULL(`ed`.`spd`,@MethodConst18) AS `Spd` , IFNULL(`ed`.`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`ed`.`hp`,@MethodConst20) AS `Hp` , IFNULL(`ed`.`mp`,@MethodConst21) AS `Mp` , IFNULL(`ed`.`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`ed`.`offset`,@MethodConst23) AS `Offset` , IFNULL(`ed`.`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`ed`.`vamp_mp`,@MethodConst25) AS `VampMp` , `ed`.`description` AS `Description` , `ed`.`main_attr` AS `MainAttr` , `ed`.`main_attr_value` AS `MainAttrValue` , `ed`.`sub_attr` AS `SubAttr` , `ed`.`sub_attr_value` AS `SubAttrValue` , `ed`.`element_limit` AS `ElementLimit` , `ed`.`equip_name` AS `EquipName`  FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )     ORDER BY `e`.`equip_id` ASC LIMIT 0,10 | 参数: [@MethodConst0=0, @MethodConst1=0, @MethodConst2=0, @MethodConst3=0, @MethodConst4=0, @MethodConst5=0, @MethodConst6=0, @MethodConst7=0, @MethodConst8=0, @MethodConst9=0, @MethodConst10=0, @MethodConst11=0, @MethodConst12=0, @MethodConst13=0, @MethodConst14=0, @MethodConst15=0, @MethodConst16=0, @MethodConst17=0, @MethodConst18=0, @MethodConst19=0, @MethodConst20=0, @MethodConst21=0, @MethodConst22=0, @MethodConst23=0, @MethodConst24=0, @MethodConst25=0]
[2025-07-31 14:34:13.489 +08:00 ERR] BMS.Sugar.DbContext: 数据库操作发生错误: Unknown column 'e.class_id' in 'field list'
SqlSugar.SqlSugarException: Unknown column 'e.class_id' in 'field list'
[2025-07-31 14:34:16.775 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 14:34:16.780 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 14:34:16.782 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 14:34:16.820 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 14:34:16.823 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 14:34:16.826 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `equip_type_id` AS `EquipTypeId` , `type_name` AS `TypeName`  FROM `equipment_type`  WHERE ( `is_active` = @is_active0 )ORDER BY `sort_order` ASC,`id` ASC  | 参数: [@is_active0=True]
[2025-07-31 14:34:16.864 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `equip_type_id` AS `EquipTypeId` , `type_name` AS `TypeName`  FROM `equipment_type`  WHERE ( `is_active` = @is_active0 )ORDER BY `sort_order` ASC,`id` ASC 
[2025-07-31 14:34:16.930 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 14:34:16.932 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 14:34:16.937 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 14:34:17.006 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 14:34:17.009 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 14:34:17.012 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  
[2025-07-31 14:34:17.049 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  
[2025-07-31 14:34:17.069 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment_detail`  
[2025-07-31 14:34:17.114 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment_detail`  
[2025-07-31 14:34:17.121 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment_type`  
[2025-07-31 14:34:17.159 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment_type`  
[2025-07-31 14:34:17.164 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )   
[2025-07-31 14:34:17.207 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )   
[2025-07-31 14:34:17.213 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `e`.`equip_id` AS `EquipId` , `e`.`class_id` AS `ClassId` , `e`.`name` AS `Name` , `e`.`icon` AS `Icon` , `e`.`equip_type_id` AS `EquipTypeId` , `et`.`type_name` AS `EquipTypeName` , `e`.`element` AS `Element` , IFNULL(`e`.`slot`,@MethodConst13) AS `Slot` , IFNULL(`e`.`strengthen_level`,@MethodConst14) AS `StrengthenLevel` , `e`.`suit_id` AS `SuitId` , IFNULL(`ed`.`atk`,@MethodConst15) AS `Atk` , IFNULL(`ed`.`hit`,@MethodConst16) AS `Hit` , IFNULL(`ed`.`def`,@MethodConst17) AS `Def` , IFNULL(`ed`.`spd`,@MethodConst18) AS `Spd` , IFNULL(`ed`.`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`ed`.`hp`,@MethodConst20) AS `Hp` , IFNULL(`ed`.`mp`,@MethodConst21) AS `Mp` , IFNULL(`ed`.`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`ed`.`offset`,@MethodConst23) AS `Offset` , IFNULL(`ed`.`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`ed`.`vamp_mp`,@MethodConst25) AS `VampMp` , `ed`.`description` AS `Description` , `ed`.`main_attr` AS `MainAttr` , `ed`.`main_attr_value` AS `MainAttrValue` , `ed`.`sub_attr` AS `SubAttr` , `ed`.`sub_attr_value` AS `SubAttrValue` , `ed`.`element_limit` AS `ElementLimit` , `ed`.`equip_name` AS `EquipName`  FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )     ORDER BY `e`.`equip_id` ASC LIMIT 0,10 | 参数: [@MethodConst0=0, @MethodConst1=0, @MethodConst2=0, @MethodConst3=0, @MethodConst4=0, @MethodConst5=0, @MethodConst6=0, @MethodConst7=0, @MethodConst8=0, @MethodConst9=0, @MethodConst10=0, @MethodConst11=0, @MethodConst12=0, @MethodConst13=0, @MethodConst14=0, @MethodConst15=0, @MethodConst16=0, @MethodConst17=0, @MethodConst18=0, @MethodConst19=0, @MethodConst20=0, @MethodConst21=0, @MethodConst22=0, @MethodConst23=0, @MethodConst24=0, @MethodConst25=0]
[2025-07-31 14:34:17.257 +08:00 ERR] BMS.Sugar.DbContext: 数据库操作发生错误: Unknown column 'e.class_id' in 'field list'
SqlSugar.SqlSugarException: Unknown column 'e.class_id' in 'field list'
[2025-07-31 14:34:17.339 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 14:34:17.340 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 14:34:17.343 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 14:34:17.380 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 14:34:17.382 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 14:34:17.390 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment_type`  
[2025-07-31 14:34:17.428 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment_type`  
[2025-07-31 14:34:17.432 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `id` AS `Id` , `equip_type_id` AS `EquipTypeId` , `type_name` AS `TypeName` , `description` AS `Description` , IFNULL(`sort_order`,@MethodConst3) AS `SortOrder` , IFNULL(CAST(`is_active` as SIGNED),@MethodConst4) AS `IsActive` , `create_time` AS `CreateTime` , `update_time` AS `UpdateTime` , @constant5 AS `EquipmentCount`  FROM `equipment_type` `et`    ORDER BY `sort_order` ASC,`id` ASC LIMIT 0,10 | 参数: [@MethodConst0=0, @MethodConst1=True, @constant2=0, @MethodConst3=0, @MethodConst4=True, @constant5=0]
[2025-07-31 14:34:17.470 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `id` AS `Id` , `equip_type_id` AS `EquipTypeId` , `type_name` AS `TypeName` , `description` AS `Description` , IFNULL(`sort_order`,@MethodConst3) AS `SortOrder` , IFNULL(CAST(`is_active` as SIGNED),@MethodConst4) AS `IsActive` , `create_time` AS `CreateTime` , `update_time` AS `UpdateTime` , @constant5 AS `EquipmentCount`  FROM `equipment_type` `et`    ORDER BY `sort_order` ASC,`id` ASC LIMIT 0,10
[2025-07-31 14:34:17.484 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=1]
[2025-07-31 14:34:17.528 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-07-31 14:34:17.532 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=2]
[2025-07-31 14:34:17.570 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-07-31 14:34:17.574 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=3]
[2025-07-31 14:34:17.613 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-07-31 14:34:17.626 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=4]
[2025-07-31 14:34:17.665 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-07-31 14:34:28.927 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 14:34:28.929 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 14:34:28.932 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 14:34:28.968 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 14:34:28.977 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 14:34:28.979 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  
[2025-07-31 14:34:29.019 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  
[2025-07-31 14:34:29.028 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment_detail`  
[2025-07-31 14:34:29.070 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment_detail`  
[2025-07-31 14:34:29.076 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment_type`  
[2025-07-31 14:34:29.127 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment_type`  
[2025-07-31 14:34:29.135 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )   
[2025-07-31 14:34:29.180 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )   
[2025-07-31 14:34:29.185 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `e`.`equip_id` AS `EquipId` , `e`.`class_id` AS `ClassId` , `e`.`name` AS `Name` , `e`.`icon` AS `Icon` , `e`.`equip_type_id` AS `EquipTypeId` , `et`.`type_name` AS `EquipTypeName` , `e`.`element` AS `Element` , IFNULL(`e`.`slot`,@MethodConst13) AS `Slot` , IFNULL(`e`.`strengthen_level`,@MethodConst14) AS `StrengthenLevel` , `e`.`suit_id` AS `SuitId` , IFNULL(`ed`.`atk`,@MethodConst15) AS `Atk` , IFNULL(`ed`.`hit`,@MethodConst16) AS `Hit` , IFNULL(`ed`.`def`,@MethodConst17) AS `Def` , IFNULL(`ed`.`spd`,@MethodConst18) AS `Spd` , IFNULL(`ed`.`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`ed`.`hp`,@MethodConst20) AS `Hp` , IFNULL(`ed`.`mp`,@MethodConst21) AS `Mp` , IFNULL(`ed`.`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`ed`.`offset`,@MethodConst23) AS `Offset` , IFNULL(`ed`.`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`ed`.`vamp_mp`,@MethodConst25) AS `VampMp` , `ed`.`description` AS `Description` , `ed`.`main_attr` AS `MainAttr` , `ed`.`main_attr_value` AS `MainAttrValue` , `ed`.`sub_attr` AS `SubAttr` , `ed`.`sub_attr_value` AS `SubAttrValue` , `ed`.`element_limit` AS `ElementLimit` , `ed`.`equip_name` AS `EquipName`  FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )     ORDER BY `e`.`equip_id` ASC LIMIT 0,10 | 参数: [@MethodConst0=0, @MethodConst1=0, @MethodConst2=0, @MethodConst3=0, @MethodConst4=0, @MethodConst5=0, @MethodConst6=0, @MethodConst7=0, @MethodConst8=0, @MethodConst9=0, @MethodConst10=0, @MethodConst11=0, @MethodConst12=0, @MethodConst13=0, @MethodConst14=0, @MethodConst15=0, @MethodConst16=0, @MethodConst17=0, @MethodConst18=0, @MethodConst19=0, @MethodConst20=0, @MethodConst21=0, @MethodConst22=0, @MethodConst23=0, @MethodConst24=0, @MethodConst25=0]
[2025-07-31 14:34:29.227 +08:00 ERR] BMS.Sugar.DbContext: 数据库操作发生错误: Unknown column 'e.class_id' in 'field list'
SqlSugar.SqlSugarException: Unknown column 'e.class_id' in 'field list'
[2025-07-31 14:37:08.009 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 14:37:08.010 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 14:37:08.011 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 14:37:08.256 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 14:37:08.264 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 14:37:08.267 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  
[2025-07-31 14:37:08.308 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  
[2025-07-31 14:37:08.326 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment_detail`  
[2025-07-31 14:37:08.368 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment_detail`  
[2025-07-31 14:37:08.378 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment_type`  
[2025-07-31 14:37:08.428 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment_type`  
[2025-07-31 14:37:33.680 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )   
[2025-07-31 14:37:33.754 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )   
[2025-07-31 14:37:33.770 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `e`.`equip_id` AS `EquipId` , `e`.`class_id` AS `ClassId` , `e`.`name` AS `Name` , `e`.`icon` AS `Icon` , `e`.`equip_type_id` AS `EquipTypeId` , `et`.`type_name` AS `EquipTypeName` , `e`.`element` AS `Element` , IFNULL(`e`.`slot`,@MethodConst13) AS `Slot` , IFNULL(`e`.`strengthen_level`,@MethodConst14) AS `StrengthenLevel` , `e`.`suit_id` AS `SuitId` , IFNULL(`ed`.`atk`,@MethodConst15) AS `Atk` , IFNULL(`ed`.`hit`,@MethodConst16) AS `Hit` , IFNULL(`ed`.`def`,@MethodConst17) AS `Def` , IFNULL(`ed`.`spd`,@MethodConst18) AS `Spd` , IFNULL(`ed`.`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`ed`.`hp`,@MethodConst20) AS `Hp` , IFNULL(`ed`.`mp`,@MethodConst21) AS `Mp` , IFNULL(`ed`.`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`ed`.`offset`,@MethodConst23) AS `Offset` , IFNULL(`ed`.`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`ed`.`vamp_mp`,@MethodConst25) AS `VampMp` , `ed`.`description` AS `Description` , `ed`.`main_attr` AS `MainAttr` , `ed`.`main_attr_value` AS `MainAttrValue` , `ed`.`sub_attr` AS `SubAttr` , `ed`.`sub_attr_value` AS `SubAttrValue` , `ed`.`element_limit` AS `ElementLimit` , `ed`.`equip_name` AS `EquipName`  FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )     ORDER BY `e`.`equip_id` ASC LIMIT 0,10 | 参数: [@MethodConst0=0, @MethodConst1=0, @MethodConst2=0, @MethodConst3=0, @MethodConst4=0, @MethodConst5=0, @MethodConst6=0, @MethodConst7=0, @MethodConst8=0, @MethodConst9=0, @MethodConst10=0, @MethodConst11=0, @MethodConst12=0, @MethodConst13=0, @MethodConst14=0, @MethodConst15=0, @MethodConst16=0, @MethodConst17=0, @MethodConst18=0, @MethodConst19=0, @MethodConst20=0, @MethodConst21=0, @MethodConst22=0, @MethodConst23=0, @MethodConst24=0, @MethodConst25=0]
[2025-07-31 14:37:33.859 +08:00 ERR] BMS.Sugar.DbContext: 数据库操作发生错误: Unknown column 'e.class_id' in 'field list'
SqlSugar.SqlSugarException: Unknown column 'e.class_id' in 'field list'
[2025-07-31 14:37:46.271 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 14:37:46.272 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 14:37:46.274 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 14:37:46.308 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 14:37:46.309 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 14:37:46.312 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  
[2025-07-31 14:37:46.349 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  
[2025-07-31 14:37:46.351 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment_detail`  
[2025-07-31 14:37:46.385 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment_detail`  
[2025-07-31 14:37:46.387 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment_type`  
[2025-07-31 14:37:46.421 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment_type`  
[2025-07-31 14:37:48.874 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )   
[2025-07-31 14:37:48.927 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )   
[2025-07-31 14:37:48.946 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `e`.`equip_id` AS `EquipId` , `e`.`class_id` AS `ClassId` , `e`.`name` AS `Name` , `e`.`icon` AS `Icon` , `e`.`equip_type_id` AS `EquipTypeId` , `et`.`type_name` AS `EquipTypeName` , `e`.`element` AS `Element` , IFNULL(`e`.`slot`,@MethodConst13) AS `Slot` , IFNULL(`e`.`strengthen_level`,@MethodConst14) AS `StrengthenLevel` , `e`.`suit_id` AS `SuitId` , IFNULL(`ed`.`atk`,@MethodConst15) AS `Atk` , IFNULL(`ed`.`hit`,@MethodConst16) AS `Hit` , IFNULL(`ed`.`def`,@MethodConst17) AS `Def` , IFNULL(`ed`.`spd`,@MethodConst18) AS `Spd` , IFNULL(`ed`.`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`ed`.`hp`,@MethodConst20) AS `Hp` , IFNULL(`ed`.`mp`,@MethodConst21) AS `Mp` , IFNULL(`ed`.`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`ed`.`offset`,@MethodConst23) AS `Offset` , IFNULL(`ed`.`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`ed`.`vamp_mp`,@MethodConst25) AS `VampMp` , `ed`.`description` AS `Description` , `ed`.`main_attr` AS `MainAttr` , `ed`.`main_attr_value` AS `MainAttrValue` , `ed`.`sub_attr` AS `SubAttr` , `ed`.`sub_attr_value` AS `SubAttrValue` , `ed`.`element_limit` AS `ElementLimit` , `ed`.`equip_name` AS `EquipName`  FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )     ORDER BY `e`.`equip_id` ASC LIMIT 0,10 | 参数: [@MethodConst0=0, @MethodConst1=0, @MethodConst2=0, @MethodConst3=0, @MethodConst4=0, @MethodConst5=0, @MethodConst6=0, @MethodConst7=0, @MethodConst8=0, @MethodConst9=0, @MethodConst10=0, @MethodConst11=0, @MethodConst12=0, @MethodConst13=0, @MethodConst14=0, @MethodConst15=0, @MethodConst16=0, @MethodConst17=0, @MethodConst18=0, @MethodConst19=0, @MethodConst20=0, @MethodConst21=0, @MethodConst22=0, @MethodConst23=0, @MethodConst24=0, @MethodConst25=0]
[2025-07-31 14:37:48.995 +08:00 ERR] BMS.Sugar.DbContext: 数据库操作发生错误: Unknown column 'e.class_id' in 'field list'
SqlSugar.SqlSugarException: Unknown column 'e.class_id' in 'field list'
[2025-07-31 15:15:27.951 +08:00 INF] BMS: 🚀 应用程序启动，日志系统已配置
[2025-07-31 15:15:28.141 +08:00 DBG] BMS: 🔧 调试级别日志测试
[2025-07-31 15:15:28.203 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 15:15:28.249 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 15:15:28.401 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:15:28.981 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:15:28.998 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 15:15:29.049 +08:00 INF] BMS: ✅ DbContext 初始化成功，日志文件位置: logs/
[2025-07-31 15:15:30.299 +08:00 WRN] Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware: Failed to determine the https port for redirect.
[2025-07-31 15:15:37.503 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 15:15:37.507 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 15:15:37.518 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:15:37.571 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:15:37.573 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 15:15:37.669 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `equip_type_id` AS `EquipTypeId` , `type_name` AS `TypeName`  FROM `equipment_type` ORDER BY `id` ASC 
[2025-07-31 15:15:37.757 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `equip_type_id` AS `EquipTypeId` , `type_name` AS `TypeName`  FROM `equipment_type` ORDER BY `id` ASC 
[2025-07-31 15:15:38.223 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 15:15:38.227 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 15:15:38.232 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:15:38.305 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:15:38.307 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 15:15:38.458 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  
[2025-07-31 15:15:38.508 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  
[2025-07-31 15:15:38.523 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment_detail`  
[2025-07-31 15:15:38.572 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment_detail`  
[2025-07-31 15:15:38.578 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment_type`  
[2025-07-31 15:15:38.628 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment_type`  
[2025-07-31 15:16:03.058 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )   
[2025-07-31 15:16:03.117 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )   
[2025-07-31 15:16:18.552 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `e`.`equip_id` AS `EquipId` , @constant14 AS `ClassId` , `e`.`name` AS `Name` , `e`.`icon` AS `Icon` , `e`.`equip_type_id` AS `EquipTypeId` , `et`.`type_name` AS `EquipTypeName` , `e`.`element` AS `Element` , IFNULL(`e`.`slot`,@MethodConst15) AS `Slot` , IFNULL(`e`.`strengthen_level`,@MethodConst16) AS `StrengthenLevel` , `e`.`suit_id` AS `SuitId` , IFNULL(`ed`.`atk`,@MethodConst17) AS `Atk` , IFNULL(`ed`.`hit`,@MethodConst18) AS `Hit` , IFNULL(`ed`.`def`,@MethodConst19) AS `Def` , IFNULL(`ed`.`spd`,@MethodConst20) AS `Spd` , IFNULL(`ed`.`dodge`,@MethodConst21) AS `Dodge` , IFNULL(`ed`.`hp`,@MethodConst22) AS `Hp` , IFNULL(`ed`.`mp`,@MethodConst23) AS `Mp` , IFNULL(`ed`.`deepen`,@MethodConst24) AS `Deepen` , IFNULL(`ed`.`offset`,@MethodConst25) AS `Offset` , IFNULL(`ed`.`vamp`,@MethodConst26) AS `Vamp` , IFNULL(`ed`.`vamp_mp`,@MethodConst27) AS `VampMp` , `ed`.`description` AS `Description` , `ed`.`main_attr` AS `MainAttr` , `ed`.`main_attr_value` AS `MainAttrValue` , `ed`.`sub_attr` AS `SubAttr` , `ed`.`sub_attr_value` AS `SubAttrValue` , `ed`.`element_limit` AS `ElementLimit` , `ed`.`equip_name` AS `EquipName`  FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )     ORDER BY `e`.`equip_id` ASC LIMIT 0,10 | 参数: [@constant0=, @MethodConst1=0, @MethodConst2=0, @MethodConst3=0, @MethodConst4=0, @MethodConst5=0, @MethodConst6=0, @MethodConst7=0, @MethodConst8=0, @MethodConst9=0, @MethodConst10=0, @MethodConst11=0, @MethodConst12=0, @MethodConst13=0, @constant14=, @MethodConst15=0, @MethodConst16=0, @MethodConst17=0, @MethodConst18=0, @MethodConst19=0, @MethodConst20=0, @MethodConst21=0, @MethodConst22=0, @MethodConst23=0, @MethodConst24=0, @MethodConst25=0, @MethodConst26=0, @MethodConst27=0]
[2025-07-31 15:16:18.633 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `e`.`equip_id` AS `EquipId` , @constant14 AS `ClassId` , `e`.`name` AS `Name` , `e`.`icon` AS `Icon` , `e`.`equip_type_id` AS `EquipTypeId` , `et`.`type_name` AS `EquipTypeName` , `e`.`element` AS `Element` , IFNULL(`e`.`slot`,@MethodConst15) AS `Slot` , IFNULL(`e`.`strengthen_level`,@MethodConst16) AS `StrengthenLevel` , `e`.`suit_id` AS `SuitId` , IFNULL(`ed`.`atk`,@MethodConst17) AS `Atk` , IFNULL(`ed`.`hit`,@MethodConst18) AS `Hit` , IFNULL(`ed`.`def`,@MethodConst19) AS `Def` , IFNULL(`ed`.`spd`,@MethodConst20) AS `Spd` , IFNULL(`ed`.`dodge`,@MethodConst21) AS `Dodge` , IFNULL(`ed`.`hp`,@MethodConst22) AS `Hp` , IFNULL(`ed`.`mp`,@MethodConst23) AS `Mp` , IFNULL(`ed`.`deepen`,@MethodConst24) AS `Deepen` , IFNULL(`ed`.`offset`,@MethodConst25) AS `Offset` , IFNULL(`ed`.`vamp`,@MethodConst26) AS `Vamp` , IFNULL(`ed`.`vamp_mp`,@MethodConst27) AS `VampMp` , `ed`.`description` AS `Description` , `ed`.`main_attr` AS `MainAttr` , `ed`.`main_attr_value` AS `MainAttrValue` , `ed`.`sub_attr` AS `SubAttr` , `ed`.`sub_attr_value` AS `SubAttrValue` , `ed`.`element_limit` AS `ElementLimit` , `ed`.`equip_name` AS `EquipName`  FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )     ORDER BY `e`.`equip_id` ASC LIMIT 0,10
[2025-07-31 15:16:25.229 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 15:16:25.231 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 15:16:25.232 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:16:25.283 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:16:25.301 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 15:16:25.308 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment_type`  
[2025-07-31 15:16:25.362 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment_type`  
[2025-07-31 15:16:25.366 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `id` AS `Id` , `equip_type_id` AS `EquipTypeId` , `type_name` AS `TypeName` , @constant6 AS `Description` , @constant7 AS `SortOrder` , @constant8 AS `IsActive` , @constant9 AS `CreateTime` , @constant10 AS `UpdateTime` , @constant11 AS `EquipmentCount`  FROM `equipment_type`    ORDER BY `id` ASC LIMIT 0,10 | 参数: [@constant0=, @constant1=0, @constant2=True, @constant3=, @constant4=, @constant5=0, @constant6=, @constant7=0, @constant8=True, @constant9=, @constant10=, @constant11=0]
[2025-07-31 15:16:25.417 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `id` AS `Id` , `equip_type_id` AS `EquipTypeId` , `type_name` AS `TypeName` , @constant6 AS `Description` , @constant7 AS `SortOrder` , @constant8 AS `IsActive` , @constant9 AS `CreateTime` , @constant10 AS `UpdateTime` , @constant11 AS `EquipmentCount`  FROM `equipment_type`    ORDER BY `id` ASC LIMIT 0,10
[2025-07-31 15:16:25.428 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=1]
[2025-07-31 15:16:25.483 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-07-31 15:16:25.488 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=2]
[2025-07-31 15:16:25.537 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-07-31 15:16:25.540 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=3]
[2025-07-31 15:16:25.586 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-07-31 15:16:25.593 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=4]
[2025-07-31 15:16:25.640 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-07-31 15:16:33.785 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 15:16:33.786 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 15:16:33.789 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:16:33.838 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:16:33.840 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 15:16:33.857 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `e`.`equip_id` AS `EquipId` , @constant15 AS `ClassId` , `e`.`name` AS `Name` , `e`.`icon` AS `Icon` , `e`.`equip_type_id` AS `EquipTypeId` , `et`.`type_name` AS `EquipTypeName` , `e`.`element` AS `Element` , IFNULL(`e`.`slot`,@MethodConst16) AS `Slot` , IFNULL(`e`.`strengthen_level`,@MethodConst17) AS `StrengthenLevel` , `e`.`suit_id` AS `SuitId` , IFNULL(`ed`.`atk`,@MethodConst18) AS `Atk` , IFNULL(`ed`.`hit`,@MethodConst19) AS `Hit` , IFNULL(`ed`.`def`,@MethodConst20) AS `Def` , IFNULL(`ed`.`spd`,@MethodConst21) AS `Spd` , IFNULL(`ed`.`dodge`,@MethodConst22) AS `Dodge` , IFNULL(`ed`.`hp`,@MethodConst23) AS `Hp` , IFNULL(`ed`.`mp`,@MethodConst24) AS `Mp` , IFNULL(`ed`.`deepen`,@MethodConst25) AS `Deepen` , IFNULL(`ed`.`offset`,@MethodConst26) AS `Offset` , IFNULL(`ed`.`vamp`,@MethodConst27) AS `Vamp` , IFNULL(`ed`.`vamp_mp`,@MethodConst28) AS `VampMp` , `ed`.`description` AS `Description` , `ed`.`main_attr` AS `MainAttr` , `ed`.`main_attr_value` AS `MainAttrValue` , `ed`.`sub_attr` AS `SubAttr` , `ed`.`sub_attr_value` AS `SubAttrValue` , `ed`.`element_limit` AS `ElementLimit` , `ed`.`equip_name` AS `EquipName`  FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )    WHERE ( `e`.`equip_id` = @equip_id0 )   LIMIT 0,1 | 参数: [@equip_id0=000101, @constant1=, @MethodConst2=0, @MethodConst3=0, @MethodConst4=0, @MethodConst5=0, @MethodConst6=0, @MethodConst7=0, @MethodConst8=0, @MethodConst9=0, @MethodConst10=0, @MethodConst11=0, @MethodConst12=0, @MethodConst13=0, @MethodConst14=0, @constant15=, @MethodConst16=0, @MethodConst17=0, @MethodConst18=0, @MethodConst19=0, @MethodConst20=0, @MethodConst21=0, @MethodConst22=0, @MethodConst23=0, @MethodConst24=0, @MethodConst25=0, @MethodConst26=0, @MethodConst27=0, @MethodConst28=0]
[2025-07-31 15:16:33.908 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `e`.`equip_id` AS `EquipId` , @constant15 AS `ClassId` , `e`.`name` AS `Name` , `e`.`icon` AS `Icon` , `e`.`equip_type_id` AS `EquipTypeId` , `et`.`type_name` AS `EquipTypeName` , `e`.`element` AS `Element` , IFNULL(`e`.`slot`,@MethodConst16) AS `Slot` , IFNULL(`e`.`strengthen_level`,@MethodConst17) AS `StrengthenLevel` , `e`.`suit_id` AS `SuitId` , IFNULL(`ed`.`atk`,@MethodConst18) AS `Atk` , IFNULL(`ed`.`hit`,@MethodConst19) AS `Hit` , IFNULL(`ed`.`def`,@MethodConst20) AS `Def` , IFNULL(`ed`.`spd`,@MethodConst21) AS `Spd` , IFNULL(`ed`.`dodge`,@MethodConst22) AS `Dodge` , IFNULL(`ed`.`hp`,@MethodConst23) AS `Hp` , IFNULL(`ed`.`mp`,@MethodConst24) AS `Mp` , IFNULL(`ed`.`deepen`,@MethodConst25) AS `Deepen` , IFNULL(`ed`.`offset`,@MethodConst26) AS `Offset` , IFNULL(`ed`.`vamp`,@MethodConst27) AS `Vamp` , IFNULL(`ed`.`vamp_mp`,@MethodConst28) AS `VampMp` , `ed`.`description` AS `Description` , `ed`.`main_attr` AS `MainAttr` , `ed`.`main_attr_value` AS `MainAttrValue` , `ed`.`sub_attr` AS `SubAttr` , `ed`.`sub_attr_value` AS `SubAttrValue` , `ed`.`element_limit` AS `ElementLimit` , `ed`.`equip_name` AS `EquipName`  FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )    WHERE ( `e`.`equip_id` = @equip_id0 )   LIMIT 0,1
[2025-07-31 15:16:38.595 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 15:16:38.597 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 15:16:38.599 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:16:38.649 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:16:38.650 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 15:16:38.672 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_id` = @equip_id0 )  | 参数: [@equip_id0=000101]
[2025-07-31 15:16:38.722 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_id` = @equip_id0 ) 
[2025-07-31 15:16:38.851 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: UPDATE `equipment`  SET
            `name` = @Const0 , `icon` = @Const1 , `equip_type_id` = @Const2 , `element` = @Const3 , `slot` = @Const4 , `strengthen_level` = @Const5 , `suit_id` = @Const6   WHERE ( `equip_id` = @equip_id7 ) | 参数: [@Const0=创世铠甲1, @Const1=210, @Const2=4, @Const3=火, @Const4=0, @Const5=0, @Const6=, @equip_id7=000101, @id=0]
[2025-07-31 15:16:38.880 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: UPDATE `equipment`  SET
            `name` = @Const0 , `icon` = @Const1 , `equip_type_id` = @Const2 , `element` = @Const3 , `slot` = @Const4 , `strengthen_level` = @Const5 , `suit_id` = @Const6   WHERE ( `equip_id` = @equip_id7 )
[2025-07-31 15:16:38.892 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: UPDATE `equipment_detail`  SET
            `atk` = @Const0 , `hit` = @Const1 , `def` = @Const2 , `spd` = @Const3 , `dodge` = @Const4 , `hp` = @Const5 , `mp` = @Const6 , `deepen` = @Const7 , `offset` = @Const8 , `vamp` = @Const9 , `vamp_mp` = @Const10 , `equip_type_id` = @Const11 , `description` = @Const12 , `main_attr` = @Const13 , `element_limit` = @Const17 , `equip_name` = @Const18 , `main_attr_value` = @Const14 , `sub_attr` = @Const15 , `sub_attr_value` = @Const16   WHERE ( `equip_id` = @equip_id19 ) | 参数: [@Const0=100, @Const1=0, @Const2=0, @Const3=0, @Const4=0, @Const5=150, @Const6=0, @Const7=0, @Const8=0, @Const9=0, @Const10=0, @Const11=4, @Const12=创世神兵,据传是由自然女神创造口袋大陆时遗留下来的补天石打造而成的!, @Const13=攻击, @Const14=, @Const15=, @Const16=, @Const17=, @Const18=, @equip_id19=000101, @id=0]
[2025-07-31 15:16:38.921 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: UPDATE `equipment_detail`  SET
            `atk` = @Const0 , `hit` = @Const1 , `def` = @Const2 , `spd` = @Const3 , `dodge` = @Const4 , `hp` = @Const5 , `mp` = @Const6 , `deepen` = @Const7 , `offset` = @Const8 , `vamp` = @Const9 , `vamp_mp` = @Const10 , `equip_type_id` = @Const11 , `description` = @Const12 , `main_attr` = @Const13 , `element_limit` = @Const17 , `equip_name` = @Const18 , `main_attr_value` = @Const14 , `sub_attr` = @Const15 , `sub_attr_value` = @Const16   WHERE ( `equip_id` = @equip_id19 )
[2025-07-31 15:16:40.282 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 15:16:40.292 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 15:16:40.297 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:16:40.350 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:16:40.352 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 15:16:40.355 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  
[2025-07-31 15:16:40.428 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  
[2025-07-31 15:16:40.438 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment_detail`  
[2025-07-31 15:16:40.497 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment_detail`  
[2025-07-31 15:16:40.501 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment_type`  
[2025-07-31 15:16:40.652 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment_type`  
[2025-07-31 15:16:46.587 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )   
[2025-07-31 15:16:46.649 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )   
[2025-07-31 15:16:46.653 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `e`.`equip_id` AS `EquipId` , @constant14 AS `ClassId` , `e`.`name` AS `Name` , `e`.`icon` AS `Icon` , `e`.`equip_type_id` AS `EquipTypeId` , `et`.`type_name` AS `EquipTypeName` , `e`.`element` AS `Element` , IFNULL(`e`.`slot`,@MethodConst15) AS `Slot` , IFNULL(`e`.`strengthen_level`,@MethodConst16) AS `StrengthenLevel` , `e`.`suit_id` AS `SuitId` , IFNULL(`ed`.`atk`,@MethodConst17) AS `Atk` , IFNULL(`ed`.`hit`,@MethodConst18) AS `Hit` , IFNULL(`ed`.`def`,@MethodConst19) AS `Def` , IFNULL(`ed`.`spd`,@MethodConst20) AS `Spd` , IFNULL(`ed`.`dodge`,@MethodConst21) AS `Dodge` , IFNULL(`ed`.`hp`,@MethodConst22) AS `Hp` , IFNULL(`ed`.`mp`,@MethodConst23) AS `Mp` , IFNULL(`ed`.`deepen`,@MethodConst24) AS `Deepen` , IFNULL(`ed`.`offset`,@MethodConst25) AS `Offset` , IFNULL(`ed`.`vamp`,@MethodConst26) AS `Vamp` , IFNULL(`ed`.`vamp_mp`,@MethodConst27) AS `VampMp` , `ed`.`description` AS `Description` , `ed`.`main_attr` AS `MainAttr` , `ed`.`main_attr_value` AS `MainAttrValue` , `ed`.`sub_attr` AS `SubAttr` , `ed`.`sub_attr_value` AS `SubAttrValue` , `ed`.`element_limit` AS `ElementLimit` , `ed`.`equip_name` AS `EquipName`  FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )     ORDER BY `e`.`equip_id` ASC LIMIT 0,10 | 参数: [@constant0=, @MethodConst1=0, @MethodConst2=0, @MethodConst3=0, @MethodConst4=0, @MethodConst5=0, @MethodConst6=0, @MethodConst7=0, @MethodConst8=0, @MethodConst9=0, @MethodConst10=0, @MethodConst11=0, @MethodConst12=0, @MethodConst13=0, @constant14=, @MethodConst15=0, @MethodConst16=0, @MethodConst17=0, @MethodConst18=0, @MethodConst19=0, @MethodConst20=0, @MethodConst21=0, @MethodConst22=0, @MethodConst23=0, @MethodConst24=0, @MethodConst25=0, @MethodConst26=0, @MethodConst27=0]
[2025-07-31 15:16:46.721 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `e`.`equip_id` AS `EquipId` , @constant14 AS `ClassId` , `e`.`name` AS `Name` , `e`.`icon` AS `Icon` , `e`.`equip_type_id` AS `EquipTypeId` , `et`.`type_name` AS `EquipTypeName` , `e`.`element` AS `Element` , IFNULL(`e`.`slot`,@MethodConst15) AS `Slot` , IFNULL(`e`.`strengthen_level`,@MethodConst16) AS `StrengthenLevel` , `e`.`suit_id` AS `SuitId` , IFNULL(`ed`.`atk`,@MethodConst17) AS `Atk` , IFNULL(`ed`.`hit`,@MethodConst18) AS `Hit` , IFNULL(`ed`.`def`,@MethodConst19) AS `Def` , IFNULL(`ed`.`spd`,@MethodConst20) AS `Spd` , IFNULL(`ed`.`dodge`,@MethodConst21) AS `Dodge` , IFNULL(`ed`.`hp`,@MethodConst22) AS `Hp` , IFNULL(`ed`.`mp`,@MethodConst23) AS `Mp` , IFNULL(`ed`.`deepen`,@MethodConst24) AS `Deepen` , IFNULL(`ed`.`offset`,@MethodConst25) AS `Offset` , IFNULL(`ed`.`vamp`,@MethodConst26) AS `Vamp` , IFNULL(`ed`.`vamp_mp`,@MethodConst27) AS `VampMp` , `ed`.`description` AS `Description` , `ed`.`main_attr` AS `MainAttr` , `ed`.`main_attr_value` AS `MainAttrValue` , `ed`.`sub_attr` AS `SubAttr` , `ed`.`sub_attr_value` AS `SubAttrValue` , `ed`.`element_limit` AS `ElementLimit` , `ed`.`equip_name` AS `EquipName`  FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )     ORDER BY `e`.`equip_id` ASC LIMIT 0,10
[2025-07-31 15:16:53.935 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 15:16:53.936 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 15:16:53.943 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:16:53.995 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:16:53.997 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 15:16:54.000 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `e`.`equip_id` AS `EquipId` , @constant15 AS `ClassId` , `e`.`name` AS `Name` , `e`.`icon` AS `Icon` , `e`.`equip_type_id` AS `EquipTypeId` , `et`.`type_name` AS `EquipTypeName` , `e`.`element` AS `Element` , IFNULL(`e`.`slot`,@MethodConst16) AS `Slot` , IFNULL(`e`.`strengthen_level`,@MethodConst17) AS `StrengthenLevel` , `e`.`suit_id` AS `SuitId` , IFNULL(`ed`.`atk`,@MethodConst18) AS `Atk` , IFNULL(`ed`.`hit`,@MethodConst19) AS `Hit` , IFNULL(`ed`.`def`,@MethodConst20) AS `Def` , IFNULL(`ed`.`spd`,@MethodConst21) AS `Spd` , IFNULL(`ed`.`dodge`,@MethodConst22) AS `Dodge` , IFNULL(`ed`.`hp`,@MethodConst23) AS `Hp` , IFNULL(`ed`.`mp`,@MethodConst24) AS `Mp` , IFNULL(`ed`.`deepen`,@MethodConst25) AS `Deepen` , IFNULL(`ed`.`offset`,@MethodConst26) AS `Offset` , IFNULL(`ed`.`vamp`,@MethodConst27) AS `Vamp` , IFNULL(`ed`.`vamp_mp`,@MethodConst28) AS `VampMp` , `ed`.`description` AS `Description` , `ed`.`main_attr` AS `MainAttr` , `ed`.`main_attr_value` AS `MainAttrValue` , `ed`.`sub_attr` AS `SubAttr` , `ed`.`sub_attr_value` AS `SubAttrValue` , `ed`.`element_limit` AS `ElementLimit` , `ed`.`equip_name` AS `EquipName`  FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )    WHERE ( `e`.`equip_id` = @equip_id0 )   LIMIT 0,1 | 参数: [@equip_id0=000101, @constant1=, @MethodConst2=0, @MethodConst3=0, @MethodConst4=0, @MethodConst5=0, @MethodConst6=0, @MethodConst7=0, @MethodConst8=0, @MethodConst9=0, @MethodConst10=0, @MethodConst11=0, @MethodConst12=0, @MethodConst13=0, @MethodConst14=0, @constant15=, @MethodConst16=0, @MethodConst17=0, @MethodConst18=0, @MethodConst19=0, @MethodConst20=0, @MethodConst21=0, @MethodConst22=0, @MethodConst23=0, @MethodConst24=0, @MethodConst25=0, @MethodConst26=0, @MethodConst27=0, @MethodConst28=0]
[2025-07-31 15:16:54.053 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `e`.`equip_id` AS `EquipId` , @constant15 AS `ClassId` , `e`.`name` AS `Name` , `e`.`icon` AS `Icon` , `e`.`equip_type_id` AS `EquipTypeId` , `et`.`type_name` AS `EquipTypeName` , `e`.`element` AS `Element` , IFNULL(`e`.`slot`,@MethodConst16) AS `Slot` , IFNULL(`e`.`strengthen_level`,@MethodConst17) AS `StrengthenLevel` , `e`.`suit_id` AS `SuitId` , IFNULL(`ed`.`atk`,@MethodConst18) AS `Atk` , IFNULL(`ed`.`hit`,@MethodConst19) AS `Hit` , IFNULL(`ed`.`def`,@MethodConst20) AS `Def` , IFNULL(`ed`.`spd`,@MethodConst21) AS `Spd` , IFNULL(`ed`.`dodge`,@MethodConst22) AS `Dodge` , IFNULL(`ed`.`hp`,@MethodConst23) AS `Hp` , IFNULL(`ed`.`mp`,@MethodConst24) AS `Mp` , IFNULL(`ed`.`deepen`,@MethodConst25) AS `Deepen` , IFNULL(`ed`.`offset`,@MethodConst26) AS `Offset` , IFNULL(`ed`.`vamp`,@MethodConst27) AS `Vamp` , IFNULL(`ed`.`vamp_mp`,@MethodConst28) AS `VampMp` , `ed`.`description` AS `Description` , `ed`.`main_attr` AS `MainAttr` , `ed`.`main_attr_value` AS `MainAttrValue` , `ed`.`sub_attr` AS `SubAttr` , `ed`.`sub_attr_value` AS `SubAttrValue` , `ed`.`element_limit` AS `ElementLimit` , `ed`.`equip_name` AS `EquipName`  FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )    WHERE ( `e`.`equip_id` = @equip_id0 )   LIMIT 0,1
[2025-07-31 15:16:58.007 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 15:16:58.024 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 15:16:58.043 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:16:58.096 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:16:58.103 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 15:16:58.106 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_id` = @equip_id0 )  | 参数: [@equip_id0=000101]
[2025-07-31 15:16:58.165 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_id` = @equip_id0 ) 
[2025-07-31 15:16:58.249 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: UPDATE `equipment`  SET
            `name` = @Const0 , `icon` = @Const1 , `equip_type_id` = @Const2 , `element` = @Const3 , `slot` = @Const4 , `strengthen_level` = @Const5 , `suit_id` = @Const6   WHERE ( `equip_id` = @equip_id7 ) | 参数: [@Const0=创世铠甲, @Const1=210, @Const2=4, @Const3=火, @Const4=0, @Const5=0, @Const6=, @equip_id7=000101, @id=0]
[2025-07-31 15:16:58.270 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: UPDATE `equipment`  SET
            `name` = @Const0 , `icon` = @Const1 , `equip_type_id` = @Const2 , `element` = @Const3 , `slot` = @Const4 , `strengthen_level` = @Const5 , `suit_id` = @Const6   WHERE ( `equip_id` = @equip_id7 )
[2025-07-31 15:16:58.281 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: UPDATE `equipment_detail`  SET
            `atk` = @Const0 , `hit` = @Const1 , `def` = @Const2 , `spd` = @Const3 , `dodge` = @Const4 , `hp` = @Const5 , `mp` = @Const6 , `deepen` = @Const7 , `offset` = @Const8 , `vamp` = @Const9 , `vamp_mp` = @Const10 , `equip_type_id` = @Const11 , `description` = @Const12 , `main_attr` = @Const13 , `element_limit` = @Const17 , `equip_name` = @Const18 , `main_attr_value` = @Const14 , `sub_attr` = @Const15 , `sub_attr_value` = @Const16   WHERE ( `equip_id` = @equip_id19 ) | 参数: [@Const0=100, @Const1=0, @Const2=0, @Const3=0, @Const4=0, @Const5=150, @Const6=0, @Const7=0, @Const8=0, @Const9=0, @Const10=0, @Const11=4, @Const12=创世神兵,据传是由自然女神创造口袋大陆时遗留下来的补天石打造而成的!, @Const13=攻击, @Const14=, @Const15=, @Const16=, @Const17=, @Const18=, @equip_id19=000101, @id=0]
[2025-07-31 15:16:58.303 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: UPDATE `equipment_detail`  SET
            `atk` = @Const0 , `hit` = @Const1 , `def` = @Const2 , `spd` = @Const3 , `dodge` = @Const4 , `hp` = @Const5 , `mp` = @Const6 , `deepen` = @Const7 , `offset` = @Const8 , `vamp` = @Const9 , `vamp_mp` = @Const10 , `equip_type_id` = @Const11 , `description` = @Const12 , `main_attr` = @Const13 , `element_limit` = @Const17 , `equip_name` = @Const18 , `main_attr_value` = @Const14 , `sub_attr` = @Const15 , `sub_attr_value` = @Const16   WHERE ( `equip_id` = @equip_id19 )
[2025-07-31 15:16:59.906 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 15:16:59.908 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 15:16:59.910 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:16:59.966 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:16:59.968 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 15:16:59.970 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  
[2025-07-31 15:17:00.046 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  
[2025-07-31 15:17:00.062 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment_detail`  
[2025-07-31 15:17:00.111 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment_detail`  
[2025-07-31 15:17:00.115 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment_type`  
[2025-07-31 15:17:00.167 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment_type`  
[2025-07-31 15:17:00.173 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )   
[2025-07-31 15:17:00.234 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )   
[2025-07-31 15:17:00.245 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `e`.`equip_id` AS `EquipId` , @constant14 AS `ClassId` , `e`.`name` AS `Name` , `e`.`icon` AS `Icon` , `e`.`equip_type_id` AS `EquipTypeId` , `et`.`type_name` AS `EquipTypeName` , `e`.`element` AS `Element` , IFNULL(`e`.`slot`,@MethodConst15) AS `Slot` , IFNULL(`e`.`strengthen_level`,@MethodConst16) AS `StrengthenLevel` , `e`.`suit_id` AS `SuitId` , IFNULL(`ed`.`atk`,@MethodConst17) AS `Atk` , IFNULL(`ed`.`hit`,@MethodConst18) AS `Hit` , IFNULL(`ed`.`def`,@MethodConst19) AS `Def` , IFNULL(`ed`.`spd`,@MethodConst20) AS `Spd` , IFNULL(`ed`.`dodge`,@MethodConst21) AS `Dodge` , IFNULL(`ed`.`hp`,@MethodConst22) AS `Hp` , IFNULL(`ed`.`mp`,@MethodConst23) AS `Mp` , IFNULL(`ed`.`deepen`,@MethodConst24) AS `Deepen` , IFNULL(`ed`.`offset`,@MethodConst25) AS `Offset` , IFNULL(`ed`.`vamp`,@MethodConst26) AS `Vamp` , IFNULL(`ed`.`vamp_mp`,@MethodConst27) AS `VampMp` , `ed`.`description` AS `Description` , `ed`.`main_attr` AS `MainAttr` , `ed`.`main_attr_value` AS `MainAttrValue` , `ed`.`sub_attr` AS `SubAttr` , `ed`.`sub_attr_value` AS `SubAttrValue` , `ed`.`element_limit` AS `ElementLimit` , `ed`.`equip_name` AS `EquipName`  FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )     ORDER BY `e`.`equip_id` ASC LIMIT 0,10 | 参数: [@constant0=, @MethodConst1=0, @MethodConst2=0, @MethodConst3=0, @MethodConst4=0, @MethodConst5=0, @MethodConst6=0, @MethodConst7=0, @MethodConst8=0, @MethodConst9=0, @MethodConst10=0, @MethodConst11=0, @MethodConst12=0, @MethodConst13=0, @constant14=, @MethodConst15=0, @MethodConst16=0, @MethodConst17=0, @MethodConst18=0, @MethodConst19=0, @MethodConst20=0, @MethodConst21=0, @MethodConst22=0, @MethodConst23=0, @MethodConst24=0, @MethodConst25=0, @MethodConst26=0, @MethodConst27=0]
[2025-07-31 15:17:00.318 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `e`.`equip_id` AS `EquipId` , @constant14 AS `ClassId` , `e`.`name` AS `Name` , `e`.`icon` AS `Icon` , `e`.`equip_type_id` AS `EquipTypeId` , `et`.`type_name` AS `EquipTypeName` , `e`.`element` AS `Element` , IFNULL(`e`.`slot`,@MethodConst15) AS `Slot` , IFNULL(`e`.`strengthen_level`,@MethodConst16) AS `StrengthenLevel` , `e`.`suit_id` AS `SuitId` , IFNULL(`ed`.`atk`,@MethodConst17) AS `Atk` , IFNULL(`ed`.`hit`,@MethodConst18) AS `Hit` , IFNULL(`ed`.`def`,@MethodConst19) AS `Def` , IFNULL(`ed`.`spd`,@MethodConst20) AS `Spd` , IFNULL(`ed`.`dodge`,@MethodConst21) AS `Dodge` , IFNULL(`ed`.`hp`,@MethodConst22) AS `Hp` , IFNULL(`ed`.`mp`,@MethodConst23) AS `Mp` , IFNULL(`ed`.`deepen`,@MethodConst24) AS `Deepen` , IFNULL(`ed`.`offset`,@MethodConst25) AS `Offset` , IFNULL(`ed`.`vamp`,@MethodConst26) AS `Vamp` , IFNULL(`ed`.`vamp_mp`,@MethodConst27) AS `VampMp` , `ed`.`description` AS `Description` , `ed`.`main_attr` AS `MainAttr` , `ed`.`main_attr_value` AS `MainAttrValue` , `ed`.`sub_attr` AS `SubAttr` , `ed`.`sub_attr_value` AS `SubAttrValue` , `ed`.`element_limit` AS `ElementLimit` , `ed`.`equip_name` AS `EquipName`  FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )     ORDER BY `e`.`equip_id` ASC LIMIT 0,10
[2025-07-31 15:17:03.791 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 15:17:03.792 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 15:17:03.794 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:17:03.846 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:17:03.849 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 15:17:03.850 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  
[2025-07-31 15:17:03.900 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  
[2025-07-31 15:17:03.902 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment_detail`  
[2025-07-31 15:17:03.954 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment_detail`  
[2025-07-31 15:17:03.959 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment_type`  
[2025-07-31 15:17:04.010 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment_type`  
[2025-07-31 15:17:04.014 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )   
[2025-07-31 15:17:04.099 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )   
[2025-07-31 15:17:04.114 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `e`.`equip_id` AS `EquipId` , @constant14 AS `ClassId` , `e`.`name` AS `Name` , `e`.`icon` AS `Icon` , `e`.`equip_type_id` AS `EquipTypeId` , `et`.`type_name` AS `EquipTypeName` , `e`.`element` AS `Element` , IFNULL(`e`.`slot`,@MethodConst15) AS `Slot` , IFNULL(`e`.`strengthen_level`,@MethodConst16) AS `StrengthenLevel` , `e`.`suit_id` AS `SuitId` , IFNULL(`ed`.`atk`,@MethodConst17) AS `Atk` , IFNULL(`ed`.`hit`,@MethodConst18) AS `Hit` , IFNULL(`ed`.`def`,@MethodConst19) AS `Def` , IFNULL(`ed`.`spd`,@MethodConst20) AS `Spd` , IFNULL(`ed`.`dodge`,@MethodConst21) AS `Dodge` , IFNULL(`ed`.`hp`,@MethodConst22) AS `Hp` , IFNULL(`ed`.`mp`,@MethodConst23) AS `Mp` , IFNULL(`ed`.`deepen`,@MethodConst24) AS `Deepen` , IFNULL(`ed`.`offset`,@MethodConst25) AS `Offset` , IFNULL(`ed`.`vamp`,@MethodConst26) AS `Vamp` , IFNULL(`ed`.`vamp_mp`,@MethodConst27) AS `VampMp` , `ed`.`description` AS `Description` , `ed`.`main_attr` AS `MainAttr` , `ed`.`main_attr_value` AS `MainAttrValue` , `ed`.`sub_attr` AS `SubAttr` , `ed`.`sub_attr_value` AS `SubAttrValue` , `ed`.`element_limit` AS `ElementLimit` , `ed`.`equip_name` AS `EquipName`  FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )     ORDER BY `e`.`equip_id` ASC LIMIT 10,10 | 参数: [@constant0=, @MethodConst1=0, @MethodConst2=0, @MethodConst3=0, @MethodConst4=0, @MethodConst5=0, @MethodConst6=0, @MethodConst7=0, @MethodConst8=0, @MethodConst9=0, @MethodConst10=0, @MethodConst11=0, @MethodConst12=0, @MethodConst13=0, @constant14=, @MethodConst15=0, @MethodConst16=0, @MethodConst17=0, @MethodConst18=0, @MethodConst19=0, @MethodConst20=0, @MethodConst21=0, @MethodConst22=0, @MethodConst23=0, @MethodConst24=0, @MethodConst25=0, @MethodConst26=0, @MethodConst27=0]
[2025-07-31 15:17:04.184 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `e`.`equip_id` AS `EquipId` , @constant14 AS `ClassId` , `e`.`name` AS `Name` , `e`.`icon` AS `Icon` , `e`.`equip_type_id` AS `EquipTypeId` , `et`.`type_name` AS `EquipTypeName` , `e`.`element` AS `Element` , IFNULL(`e`.`slot`,@MethodConst15) AS `Slot` , IFNULL(`e`.`strengthen_level`,@MethodConst16) AS `StrengthenLevel` , `e`.`suit_id` AS `SuitId` , IFNULL(`ed`.`atk`,@MethodConst17) AS `Atk` , IFNULL(`ed`.`hit`,@MethodConst18) AS `Hit` , IFNULL(`ed`.`def`,@MethodConst19) AS `Def` , IFNULL(`ed`.`spd`,@MethodConst20) AS `Spd` , IFNULL(`ed`.`dodge`,@MethodConst21) AS `Dodge` , IFNULL(`ed`.`hp`,@MethodConst22) AS `Hp` , IFNULL(`ed`.`mp`,@MethodConst23) AS `Mp` , IFNULL(`ed`.`deepen`,@MethodConst24) AS `Deepen` , IFNULL(`ed`.`offset`,@MethodConst25) AS `Offset` , IFNULL(`ed`.`vamp`,@MethodConst26) AS `Vamp` , IFNULL(`ed`.`vamp_mp`,@MethodConst27) AS `VampMp` , `ed`.`description` AS `Description` , `ed`.`main_attr` AS `MainAttr` , `ed`.`main_attr_value` AS `MainAttrValue` , `ed`.`sub_attr` AS `SubAttr` , `ed`.`sub_attr_value` AS `SubAttrValue` , `ed`.`element_limit` AS `ElementLimit` , `ed`.`equip_name` AS `EquipName`  FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )     ORDER BY `e`.`equip_id` ASC LIMIT 10,10
[2025-07-31 15:17:07.774 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 15:17:07.776 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 15:17:07.780 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:17:07.830 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:17:07.831 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 15:17:07.833 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  
[2025-07-31 15:17:07.881 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  
[2025-07-31 15:17:07.883 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment_detail`  
[2025-07-31 15:17:07.933 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment_detail`  
[2025-07-31 15:17:07.939 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment_type`  
[2025-07-31 15:17:07.988 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment_type`  
[2025-07-31 15:17:07.997 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )   
[2025-07-31 15:17:08.074 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )   
[2025-07-31 15:17:08.078 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `e`.`equip_id` AS `EquipId` , @constant14 AS `ClassId` , `e`.`name` AS `Name` , `e`.`icon` AS `Icon` , `e`.`equip_type_id` AS `EquipTypeId` , `et`.`type_name` AS `EquipTypeName` , `e`.`element` AS `Element` , IFNULL(`e`.`slot`,@MethodConst15) AS `Slot` , IFNULL(`e`.`strengthen_level`,@MethodConst16) AS `StrengthenLevel` , `e`.`suit_id` AS `SuitId` , IFNULL(`ed`.`atk`,@MethodConst17) AS `Atk` , IFNULL(`ed`.`hit`,@MethodConst18) AS `Hit` , IFNULL(`ed`.`def`,@MethodConst19) AS `Def` , IFNULL(`ed`.`spd`,@MethodConst20) AS `Spd` , IFNULL(`ed`.`dodge`,@MethodConst21) AS `Dodge` , IFNULL(`ed`.`hp`,@MethodConst22) AS `Hp` , IFNULL(`ed`.`mp`,@MethodConst23) AS `Mp` , IFNULL(`ed`.`deepen`,@MethodConst24) AS `Deepen` , IFNULL(`ed`.`offset`,@MethodConst25) AS `Offset` , IFNULL(`ed`.`vamp`,@MethodConst26) AS `Vamp` , IFNULL(`ed`.`vamp_mp`,@MethodConst27) AS `VampMp` , `ed`.`description` AS `Description` , `ed`.`main_attr` AS `MainAttr` , `ed`.`main_attr_value` AS `MainAttrValue` , `ed`.`sub_attr` AS `SubAttr` , `ed`.`sub_attr_value` AS `SubAttrValue` , `ed`.`element_limit` AS `ElementLimit` , `ed`.`equip_name` AS `EquipName`  FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )     ORDER BY `e`.`equip_id` ASC LIMIT 20,10 | 参数: [@constant0=, @MethodConst1=0, @MethodConst2=0, @MethodConst3=0, @MethodConst4=0, @MethodConst5=0, @MethodConst6=0, @MethodConst7=0, @MethodConst8=0, @MethodConst9=0, @MethodConst10=0, @MethodConst11=0, @MethodConst12=0, @MethodConst13=0, @constant14=, @MethodConst15=0, @MethodConst16=0, @MethodConst17=0, @MethodConst18=0, @MethodConst19=0, @MethodConst20=0, @MethodConst21=0, @MethodConst22=0, @MethodConst23=0, @MethodConst24=0, @MethodConst25=0, @MethodConst26=0, @MethodConst27=0]
[2025-07-31 15:17:08.156 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `e`.`equip_id` AS `EquipId` , @constant14 AS `ClassId` , `e`.`name` AS `Name` , `e`.`icon` AS `Icon` , `e`.`equip_type_id` AS `EquipTypeId` , `et`.`type_name` AS `EquipTypeName` , `e`.`element` AS `Element` , IFNULL(`e`.`slot`,@MethodConst15) AS `Slot` , IFNULL(`e`.`strengthen_level`,@MethodConst16) AS `StrengthenLevel` , `e`.`suit_id` AS `SuitId` , IFNULL(`ed`.`atk`,@MethodConst17) AS `Atk` , IFNULL(`ed`.`hit`,@MethodConst18) AS `Hit` , IFNULL(`ed`.`def`,@MethodConst19) AS `Def` , IFNULL(`ed`.`spd`,@MethodConst20) AS `Spd` , IFNULL(`ed`.`dodge`,@MethodConst21) AS `Dodge` , IFNULL(`ed`.`hp`,@MethodConst22) AS `Hp` , IFNULL(`ed`.`mp`,@MethodConst23) AS `Mp` , IFNULL(`ed`.`deepen`,@MethodConst24) AS `Deepen` , IFNULL(`ed`.`offset`,@MethodConst25) AS `Offset` , IFNULL(`ed`.`vamp`,@MethodConst26) AS `Vamp` , IFNULL(`ed`.`vamp_mp`,@MethodConst27) AS `VampMp` , `ed`.`description` AS `Description` , `ed`.`main_attr` AS `MainAttr` , `ed`.`main_attr_value` AS `MainAttrValue` , `ed`.`sub_attr` AS `SubAttr` , `ed`.`sub_attr_value` AS `SubAttrValue` , `ed`.`element_limit` AS `ElementLimit` , `ed`.`equip_name` AS `EquipName`  FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )     ORDER BY `e`.`equip_id` ASC LIMIT 20,10
[2025-07-31 15:17:10.087 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 15:17:10.089 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 15:17:10.091 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:17:10.139 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:17:10.140 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 15:17:10.141 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  
[2025-07-31 15:17:10.211 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  
[2025-07-31 15:17:10.214 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment_detail`  
[2025-07-31 15:17:10.261 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment_detail`  
[2025-07-31 15:17:10.264 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment_type`  
[2025-07-31 15:17:10.314 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment_type`  
[2025-07-31 15:17:10.317 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )   
[2025-07-31 15:17:10.377 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )   
[2025-07-31 15:17:10.384 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `e`.`equip_id` AS `EquipId` , @constant14 AS `ClassId` , `e`.`name` AS `Name` , `e`.`icon` AS `Icon` , `e`.`equip_type_id` AS `EquipTypeId` , `et`.`type_name` AS `EquipTypeName` , `e`.`element` AS `Element` , IFNULL(`e`.`slot`,@MethodConst15) AS `Slot` , IFNULL(`e`.`strengthen_level`,@MethodConst16) AS `StrengthenLevel` , `e`.`suit_id` AS `SuitId` , IFNULL(`ed`.`atk`,@MethodConst17) AS `Atk` , IFNULL(`ed`.`hit`,@MethodConst18) AS `Hit` , IFNULL(`ed`.`def`,@MethodConst19) AS `Def` , IFNULL(`ed`.`spd`,@MethodConst20) AS `Spd` , IFNULL(`ed`.`dodge`,@MethodConst21) AS `Dodge` , IFNULL(`ed`.`hp`,@MethodConst22) AS `Hp` , IFNULL(`ed`.`mp`,@MethodConst23) AS `Mp` , IFNULL(`ed`.`deepen`,@MethodConst24) AS `Deepen` , IFNULL(`ed`.`offset`,@MethodConst25) AS `Offset` , IFNULL(`ed`.`vamp`,@MethodConst26) AS `Vamp` , IFNULL(`ed`.`vamp_mp`,@MethodConst27) AS `VampMp` , `ed`.`description` AS `Description` , `ed`.`main_attr` AS `MainAttr` , `ed`.`main_attr_value` AS `MainAttrValue` , `ed`.`sub_attr` AS `SubAttr` , `ed`.`sub_attr_value` AS `SubAttrValue` , `ed`.`element_limit` AS `ElementLimit` , `ed`.`equip_name` AS `EquipName`  FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )     ORDER BY `e`.`equip_id` ASC LIMIT 30,10 | 参数: [@constant0=, @MethodConst1=0, @MethodConst2=0, @MethodConst3=0, @MethodConst4=0, @MethodConst5=0, @MethodConst6=0, @MethodConst7=0, @MethodConst8=0, @MethodConst9=0, @MethodConst10=0, @MethodConst11=0, @MethodConst12=0, @MethodConst13=0, @constant14=, @MethodConst15=0, @MethodConst16=0, @MethodConst17=0, @MethodConst18=0, @MethodConst19=0, @MethodConst20=0, @MethodConst21=0, @MethodConst22=0, @MethodConst23=0, @MethodConst24=0, @MethodConst25=0, @MethodConst26=0, @MethodConst27=0]
[2025-07-31 15:17:10.456 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `e`.`equip_id` AS `EquipId` , @constant14 AS `ClassId` , `e`.`name` AS `Name` , `e`.`icon` AS `Icon` , `e`.`equip_type_id` AS `EquipTypeId` , `et`.`type_name` AS `EquipTypeName` , `e`.`element` AS `Element` , IFNULL(`e`.`slot`,@MethodConst15) AS `Slot` , IFNULL(`e`.`strengthen_level`,@MethodConst16) AS `StrengthenLevel` , `e`.`suit_id` AS `SuitId` , IFNULL(`ed`.`atk`,@MethodConst17) AS `Atk` , IFNULL(`ed`.`hit`,@MethodConst18) AS `Hit` , IFNULL(`ed`.`def`,@MethodConst19) AS `Def` , IFNULL(`ed`.`spd`,@MethodConst20) AS `Spd` , IFNULL(`ed`.`dodge`,@MethodConst21) AS `Dodge` , IFNULL(`ed`.`hp`,@MethodConst22) AS `Hp` , IFNULL(`ed`.`mp`,@MethodConst23) AS `Mp` , IFNULL(`ed`.`deepen`,@MethodConst24) AS `Deepen` , IFNULL(`ed`.`offset`,@MethodConst25) AS `Offset` , IFNULL(`ed`.`vamp`,@MethodConst26) AS `Vamp` , IFNULL(`ed`.`vamp_mp`,@MethodConst27) AS `VampMp` , `ed`.`description` AS `Description` , `ed`.`main_attr` AS `MainAttr` , `ed`.`main_attr_value` AS `MainAttrValue` , `ed`.`sub_attr` AS `SubAttr` , `ed`.`sub_attr_value` AS `SubAttrValue` , `ed`.`element_limit` AS `ElementLimit` , `ed`.`equip_name` AS `EquipName`  FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )     ORDER BY `e`.`equip_id` ASC LIMIT 30,10
[2025-07-31 15:17:26.791 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 15:17:26.792 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 15:17:26.794 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:17:26.846 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:17:26.850 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 15:17:26.854 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  
[2025-07-31 15:17:26.911 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  
[2025-07-31 15:17:26.914 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment_detail`  
[2025-07-31 15:17:26.967 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment_detail`  
[2025-07-31 15:17:26.971 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment_type`  
[2025-07-31 15:17:27.023 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment_type`  
[2025-07-31 15:17:27.027 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )   WHERE ( `e`.`element` = @element0 )  | 参数: [@element0=金]
[2025-07-31 15:17:27.085 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )   WHERE ( `e`.`element` = @element0 ) 
[2025-07-31 15:17:27.088 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `e`.`equip_id` AS `EquipId` , @constant15 AS `ClassId` , `e`.`name` AS `Name` , `e`.`icon` AS `Icon` , `e`.`equip_type_id` AS `EquipTypeId` , `et`.`type_name` AS `EquipTypeName` , `e`.`element` AS `Element` , IFNULL(`e`.`slot`,@MethodConst16) AS `Slot` , IFNULL(`e`.`strengthen_level`,@MethodConst17) AS `StrengthenLevel` , `e`.`suit_id` AS `SuitId` , IFNULL(`ed`.`atk`,@MethodConst18) AS `Atk` , IFNULL(`ed`.`hit`,@MethodConst19) AS `Hit` , IFNULL(`ed`.`def`,@MethodConst20) AS `Def` , IFNULL(`ed`.`spd`,@MethodConst21) AS `Spd` , IFNULL(`ed`.`dodge`,@MethodConst22) AS `Dodge` , IFNULL(`ed`.`hp`,@MethodConst23) AS `Hp` , IFNULL(`ed`.`mp`,@MethodConst24) AS `Mp` , IFNULL(`ed`.`deepen`,@MethodConst25) AS `Deepen` , IFNULL(`ed`.`offset`,@MethodConst26) AS `Offset` , IFNULL(`ed`.`vamp`,@MethodConst27) AS `Vamp` , IFNULL(`ed`.`vamp_mp`,@MethodConst28) AS `VampMp` , `ed`.`description` AS `Description` , `ed`.`main_attr` AS `MainAttr` , `ed`.`main_attr_value` AS `MainAttrValue` , `ed`.`sub_attr` AS `SubAttr` , `ed`.`sub_attr_value` AS `SubAttrValue` , `ed`.`element_limit` AS `ElementLimit` , `ed`.`equip_name` AS `EquipName`  FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )    WHERE ( `e`.`element` = @element0 )  ORDER BY `e`.`equip_id` ASC LIMIT 0,10 | 参数: [@element0=金, @constant1=, @MethodConst2=0, @MethodConst3=0, @MethodConst4=0, @MethodConst5=0, @MethodConst6=0, @MethodConst7=0, @MethodConst8=0, @MethodConst9=0, @MethodConst10=0, @MethodConst11=0, @MethodConst12=0, @MethodConst13=0, @MethodConst14=0, @constant15=, @MethodConst16=0, @MethodConst17=0, @MethodConst18=0, @MethodConst19=0, @MethodConst20=0, @MethodConst21=0, @MethodConst22=0, @MethodConst23=0, @MethodConst24=0, @MethodConst25=0, @MethodConst26=0, @MethodConst27=0, @MethodConst28=0]
[2025-07-31 15:17:27.145 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `e`.`equip_id` AS `EquipId` , @constant15 AS `ClassId` , `e`.`name` AS `Name` , `e`.`icon` AS `Icon` , `e`.`equip_type_id` AS `EquipTypeId` , `et`.`type_name` AS `EquipTypeName` , `e`.`element` AS `Element` , IFNULL(`e`.`slot`,@MethodConst16) AS `Slot` , IFNULL(`e`.`strengthen_level`,@MethodConst17) AS `StrengthenLevel` , `e`.`suit_id` AS `SuitId` , IFNULL(`ed`.`atk`,@MethodConst18) AS `Atk` , IFNULL(`ed`.`hit`,@MethodConst19) AS `Hit` , IFNULL(`ed`.`def`,@MethodConst20) AS `Def` , IFNULL(`ed`.`spd`,@MethodConst21) AS `Spd` , IFNULL(`ed`.`dodge`,@MethodConst22) AS `Dodge` , IFNULL(`ed`.`hp`,@MethodConst23) AS `Hp` , IFNULL(`ed`.`mp`,@MethodConst24) AS `Mp` , IFNULL(`ed`.`deepen`,@MethodConst25) AS `Deepen` , IFNULL(`ed`.`offset`,@MethodConst26) AS `Offset` , IFNULL(`ed`.`vamp`,@MethodConst27) AS `Vamp` , IFNULL(`ed`.`vamp_mp`,@MethodConst28) AS `VampMp` , `ed`.`description` AS `Description` , `ed`.`main_attr` AS `MainAttr` , `ed`.`main_attr_value` AS `MainAttrValue` , `ed`.`sub_attr` AS `SubAttr` , `ed`.`sub_attr_value` AS `SubAttrValue` , `ed`.`element_limit` AS `ElementLimit` , `ed`.`equip_name` AS `EquipName`  FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )    WHERE ( `e`.`element` = @element0 )  ORDER BY `e`.`equip_id` ASC LIMIT 0,10
[2025-07-31 15:17:30.054 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 15:17:30.066 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 15:17:30.068 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:17:30.118 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:17:30.131 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 15:17:30.138 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  
[2025-07-31 15:17:30.188 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  
[2025-07-31 15:17:30.194 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment_detail`  
[2025-07-31 15:17:30.255 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment_detail`  
[2025-07-31 15:17:30.259 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment_type`  
[2025-07-31 15:17:30.308 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment_type`  
[2025-07-31 15:17:30.310 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )   WHERE ( `e`.`element` = @element0 )  | 参数: [@element0=金]
[2025-07-31 15:17:30.361 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )   WHERE ( `e`.`element` = @element0 ) 
[2025-07-31 15:17:30.367 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `e`.`equip_id` AS `EquipId` , @constant15 AS `ClassId` , `e`.`name` AS `Name` , `e`.`icon` AS `Icon` , `e`.`equip_type_id` AS `EquipTypeId` , `et`.`type_name` AS `EquipTypeName` , `e`.`element` AS `Element` , IFNULL(`e`.`slot`,@MethodConst16) AS `Slot` , IFNULL(`e`.`strengthen_level`,@MethodConst17) AS `StrengthenLevel` , `e`.`suit_id` AS `SuitId` , IFNULL(`ed`.`atk`,@MethodConst18) AS `Atk` , IFNULL(`ed`.`hit`,@MethodConst19) AS `Hit` , IFNULL(`ed`.`def`,@MethodConst20) AS `Def` , IFNULL(`ed`.`spd`,@MethodConst21) AS `Spd` , IFNULL(`ed`.`dodge`,@MethodConst22) AS `Dodge` , IFNULL(`ed`.`hp`,@MethodConst23) AS `Hp` , IFNULL(`ed`.`mp`,@MethodConst24) AS `Mp` , IFNULL(`ed`.`deepen`,@MethodConst25) AS `Deepen` , IFNULL(`ed`.`offset`,@MethodConst26) AS `Offset` , IFNULL(`ed`.`vamp`,@MethodConst27) AS `Vamp` , IFNULL(`ed`.`vamp_mp`,@MethodConst28) AS `VampMp` , `ed`.`description` AS `Description` , `ed`.`main_attr` AS `MainAttr` , `ed`.`main_attr_value` AS `MainAttrValue` , `ed`.`sub_attr` AS `SubAttr` , `ed`.`sub_attr_value` AS `SubAttrValue` , `ed`.`element_limit` AS `ElementLimit` , `ed`.`equip_name` AS `EquipName`  FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )    WHERE ( `e`.`element` = @element0 )  ORDER BY `e`.`equip_id` ASC LIMIT 10,10 | 参数: [@element0=金, @constant1=, @MethodConst2=0, @MethodConst3=0, @MethodConst4=0, @MethodConst5=0, @MethodConst6=0, @MethodConst7=0, @MethodConst8=0, @MethodConst9=0, @MethodConst10=0, @MethodConst11=0, @MethodConst12=0, @MethodConst13=0, @MethodConst14=0, @constant15=, @MethodConst16=0, @MethodConst17=0, @MethodConst18=0, @MethodConst19=0, @MethodConst20=0, @MethodConst21=0, @MethodConst22=0, @MethodConst23=0, @MethodConst24=0, @MethodConst25=0, @MethodConst26=0, @MethodConst27=0, @MethodConst28=0]
[2025-07-31 15:17:30.437 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `e`.`equip_id` AS `EquipId` , @constant15 AS `ClassId` , `e`.`name` AS `Name` , `e`.`icon` AS `Icon` , `e`.`equip_type_id` AS `EquipTypeId` , `et`.`type_name` AS `EquipTypeName` , `e`.`element` AS `Element` , IFNULL(`e`.`slot`,@MethodConst16) AS `Slot` , IFNULL(`e`.`strengthen_level`,@MethodConst17) AS `StrengthenLevel` , `e`.`suit_id` AS `SuitId` , IFNULL(`ed`.`atk`,@MethodConst18) AS `Atk` , IFNULL(`ed`.`hit`,@MethodConst19) AS `Hit` , IFNULL(`ed`.`def`,@MethodConst20) AS `Def` , IFNULL(`ed`.`spd`,@MethodConst21) AS `Spd` , IFNULL(`ed`.`dodge`,@MethodConst22) AS `Dodge` , IFNULL(`ed`.`hp`,@MethodConst23) AS `Hp` , IFNULL(`ed`.`mp`,@MethodConst24) AS `Mp` , IFNULL(`ed`.`deepen`,@MethodConst25) AS `Deepen` , IFNULL(`ed`.`offset`,@MethodConst26) AS `Offset` , IFNULL(`ed`.`vamp`,@MethodConst27) AS `Vamp` , IFNULL(`ed`.`vamp_mp`,@MethodConst28) AS `VampMp` , `ed`.`description` AS `Description` , `ed`.`main_attr` AS `MainAttr` , `ed`.`main_attr_value` AS `MainAttrValue` , `ed`.`sub_attr` AS `SubAttr` , `ed`.`sub_attr_value` AS `SubAttrValue` , `ed`.`element_limit` AS `ElementLimit` , `ed`.`equip_name` AS `EquipName`  FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )    WHERE ( `e`.`element` = @element0 )  ORDER BY `e`.`equip_id` ASC LIMIT 10,10
[2025-07-31 15:17:31.374 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 15:17:31.376 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 15:17:31.380 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:17:31.431 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:17:31.433 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 15:17:31.436 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  
[2025-07-31 15:17:31.486 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  
[2025-07-31 15:17:31.491 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment_detail`  
[2025-07-31 15:17:31.550 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment_detail`  
[2025-07-31 15:17:31.553 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment_type`  
[2025-07-31 15:17:31.603 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment_type`  
[2025-07-31 15:17:31.606 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )   WHERE ( `e`.`element` = @element0 )  | 参数: [@element0=金]
[2025-07-31 15:17:31.654 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )   WHERE ( `e`.`element` = @element0 ) 
[2025-07-31 15:17:31.658 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `e`.`equip_id` AS `EquipId` , @constant15 AS `ClassId` , `e`.`name` AS `Name` , `e`.`icon` AS `Icon` , `e`.`equip_type_id` AS `EquipTypeId` , `et`.`type_name` AS `EquipTypeName` , `e`.`element` AS `Element` , IFNULL(`e`.`slot`,@MethodConst16) AS `Slot` , IFNULL(`e`.`strengthen_level`,@MethodConst17) AS `StrengthenLevel` , `e`.`suit_id` AS `SuitId` , IFNULL(`ed`.`atk`,@MethodConst18) AS `Atk` , IFNULL(`ed`.`hit`,@MethodConst19) AS `Hit` , IFNULL(`ed`.`def`,@MethodConst20) AS `Def` , IFNULL(`ed`.`spd`,@MethodConst21) AS `Spd` , IFNULL(`ed`.`dodge`,@MethodConst22) AS `Dodge` , IFNULL(`ed`.`hp`,@MethodConst23) AS `Hp` , IFNULL(`ed`.`mp`,@MethodConst24) AS `Mp` , IFNULL(`ed`.`deepen`,@MethodConst25) AS `Deepen` , IFNULL(`ed`.`offset`,@MethodConst26) AS `Offset` , IFNULL(`ed`.`vamp`,@MethodConst27) AS `Vamp` , IFNULL(`ed`.`vamp_mp`,@MethodConst28) AS `VampMp` , `ed`.`description` AS `Description` , `ed`.`main_attr` AS `MainAttr` , `ed`.`main_attr_value` AS `MainAttrValue` , `ed`.`sub_attr` AS `SubAttr` , `ed`.`sub_attr_value` AS `SubAttrValue` , `ed`.`element_limit` AS `ElementLimit` , `ed`.`equip_name` AS `EquipName`  FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )    WHERE ( `e`.`element` = @element0 )  ORDER BY `e`.`equip_id` ASC LIMIT 20,10 | 参数: [@element0=金, @constant1=, @MethodConst2=0, @MethodConst3=0, @MethodConst4=0, @MethodConst5=0, @MethodConst6=0, @MethodConst7=0, @MethodConst8=0, @MethodConst9=0, @MethodConst10=0, @MethodConst11=0, @MethodConst12=0, @MethodConst13=0, @MethodConst14=0, @constant15=, @MethodConst16=0, @MethodConst17=0, @MethodConst18=0, @MethodConst19=0, @MethodConst20=0, @MethodConst21=0, @MethodConst22=0, @MethodConst23=0, @MethodConst24=0, @MethodConst25=0, @MethodConst26=0, @MethodConst27=0, @MethodConst28=0]
[2025-07-31 15:17:31.741 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `e`.`equip_id` AS `EquipId` , @constant15 AS `ClassId` , `e`.`name` AS `Name` , `e`.`icon` AS `Icon` , `e`.`equip_type_id` AS `EquipTypeId` , `et`.`type_name` AS `EquipTypeName` , `e`.`element` AS `Element` , IFNULL(`e`.`slot`,@MethodConst16) AS `Slot` , IFNULL(`e`.`strengthen_level`,@MethodConst17) AS `StrengthenLevel` , `e`.`suit_id` AS `SuitId` , IFNULL(`ed`.`atk`,@MethodConst18) AS `Atk` , IFNULL(`ed`.`hit`,@MethodConst19) AS `Hit` , IFNULL(`ed`.`def`,@MethodConst20) AS `Def` , IFNULL(`ed`.`spd`,@MethodConst21) AS `Spd` , IFNULL(`ed`.`dodge`,@MethodConst22) AS `Dodge` , IFNULL(`ed`.`hp`,@MethodConst23) AS `Hp` , IFNULL(`ed`.`mp`,@MethodConst24) AS `Mp` , IFNULL(`ed`.`deepen`,@MethodConst25) AS `Deepen` , IFNULL(`ed`.`offset`,@MethodConst26) AS `Offset` , IFNULL(`ed`.`vamp`,@MethodConst27) AS `Vamp` , IFNULL(`ed`.`vamp_mp`,@MethodConst28) AS `VampMp` , `ed`.`description` AS `Description` , `ed`.`main_attr` AS `MainAttr` , `ed`.`main_attr_value` AS `MainAttrValue` , `ed`.`sub_attr` AS `SubAttr` , `ed`.`sub_attr_value` AS `SubAttrValue` , `ed`.`element_limit` AS `ElementLimit` , `ed`.`equip_name` AS `EquipName`  FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )    WHERE ( `e`.`element` = @element0 )  ORDER BY `e`.`equip_id` ASC LIMIT 20,10
[2025-07-31 15:17:32.735 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 15:17:32.933 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 15:17:33.014 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:17:33.075 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:17:33.081 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 15:17:33.085 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  
[2025-07-31 15:17:33.224 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  
[2025-07-31 15:17:33.254 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment_detail`  
[2025-07-31 15:17:33.359 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment_detail`  
[2025-07-31 15:17:33.403 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment_type`  
[2025-07-31 15:17:33.455 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment_type`  
[2025-07-31 15:17:33.459 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )   WHERE ( `e`.`element` = @element0 )  | 参数: [@element0=金]
[2025-07-31 15:17:33.518 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )   WHERE ( `e`.`element` = @element0 ) 
[2025-07-31 15:17:33.537 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `e`.`equip_id` AS `EquipId` , @constant15 AS `ClassId` , `e`.`name` AS `Name` , `e`.`icon` AS `Icon` , `e`.`equip_type_id` AS `EquipTypeId` , `et`.`type_name` AS `EquipTypeName` , `e`.`element` AS `Element` , IFNULL(`e`.`slot`,@MethodConst16) AS `Slot` , IFNULL(`e`.`strengthen_level`,@MethodConst17) AS `StrengthenLevel` , `e`.`suit_id` AS `SuitId` , IFNULL(`ed`.`atk`,@MethodConst18) AS `Atk` , IFNULL(`ed`.`hit`,@MethodConst19) AS `Hit` , IFNULL(`ed`.`def`,@MethodConst20) AS `Def` , IFNULL(`ed`.`spd`,@MethodConst21) AS `Spd` , IFNULL(`ed`.`dodge`,@MethodConst22) AS `Dodge` , IFNULL(`ed`.`hp`,@MethodConst23) AS `Hp` , IFNULL(`ed`.`mp`,@MethodConst24) AS `Mp` , IFNULL(`ed`.`deepen`,@MethodConst25) AS `Deepen` , IFNULL(`ed`.`offset`,@MethodConst26) AS `Offset` , IFNULL(`ed`.`vamp`,@MethodConst27) AS `Vamp` , IFNULL(`ed`.`vamp_mp`,@MethodConst28) AS `VampMp` , `ed`.`description` AS `Description` , `ed`.`main_attr` AS `MainAttr` , `ed`.`main_attr_value` AS `MainAttrValue` , `ed`.`sub_attr` AS `SubAttr` , `ed`.`sub_attr_value` AS `SubAttrValue` , `ed`.`element_limit` AS `ElementLimit` , `ed`.`equip_name` AS `EquipName`  FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )    WHERE ( `e`.`element` = @element0 )  ORDER BY `e`.`equip_id` ASC LIMIT 0,10 | 参数: [@element0=金, @constant1=, @MethodConst2=0, @MethodConst3=0, @MethodConst4=0, @MethodConst5=0, @MethodConst6=0, @MethodConst7=0, @MethodConst8=0, @MethodConst9=0, @MethodConst10=0, @MethodConst11=0, @MethodConst12=0, @MethodConst13=0, @MethodConst14=0, @constant15=, @MethodConst16=0, @MethodConst17=0, @MethodConst18=0, @MethodConst19=0, @MethodConst20=0, @MethodConst21=0, @MethodConst22=0, @MethodConst23=0, @MethodConst24=0, @MethodConst25=0, @MethodConst26=0, @MethodConst27=0, @MethodConst28=0]
[2025-07-31 15:17:33.603 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `e`.`equip_id` AS `EquipId` , @constant15 AS `ClassId` , `e`.`name` AS `Name` , `e`.`icon` AS `Icon` , `e`.`equip_type_id` AS `EquipTypeId` , `et`.`type_name` AS `EquipTypeName` , `e`.`element` AS `Element` , IFNULL(`e`.`slot`,@MethodConst16) AS `Slot` , IFNULL(`e`.`strengthen_level`,@MethodConst17) AS `StrengthenLevel` , `e`.`suit_id` AS `SuitId` , IFNULL(`ed`.`atk`,@MethodConst18) AS `Atk` , IFNULL(`ed`.`hit`,@MethodConst19) AS `Hit` , IFNULL(`ed`.`def`,@MethodConst20) AS `Def` , IFNULL(`ed`.`spd`,@MethodConst21) AS `Spd` , IFNULL(`ed`.`dodge`,@MethodConst22) AS `Dodge` , IFNULL(`ed`.`hp`,@MethodConst23) AS `Hp` , IFNULL(`ed`.`mp`,@MethodConst24) AS `Mp` , IFNULL(`ed`.`deepen`,@MethodConst25) AS `Deepen` , IFNULL(`ed`.`offset`,@MethodConst26) AS `Offset` , IFNULL(`ed`.`vamp`,@MethodConst27) AS `Vamp` , IFNULL(`ed`.`vamp_mp`,@MethodConst28) AS `VampMp` , `ed`.`description` AS `Description` , `ed`.`main_attr` AS `MainAttr` , `ed`.`main_attr_value` AS `MainAttrValue` , `ed`.`sub_attr` AS `SubAttr` , `ed`.`sub_attr_value` AS `SubAttrValue` , `ed`.`element_limit` AS `ElementLimit` , `ed`.`equip_name` AS `EquipName`  FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )    WHERE ( `e`.`element` = @element0 )  ORDER BY `e`.`equip_id` ASC LIMIT 0,10
[2025-07-31 15:17:43.164 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 15:17:43.166 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 15:17:43.170 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:17:43.221 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:17:43.224 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 15:17:43.251 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  DISTINCT `type` FROM `item_config`  WHERE NOT( `type` IS NULL   OR `type`='') 
[2025-07-31 15:17:43.335 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  DISTINCT `type` FROM `item_config`  WHERE NOT( `type` IS NULL   OR `type`='') 
[2025-07-31 15:17:43.346 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  DISTINCT `quality` FROM `item_config`  WHERE NOT( `quality` IS NULL   OR `quality`='') 
[2025-07-31 15:17:43.398 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  DISTINCT `quality` FROM `item_config`  WHERE NOT( `quality` IS NULL   OR `quality`='') 
[2025-07-31 15:17:43.530 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 15:17:43.532 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 15:17:43.535 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:17:43.585 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:17:43.586 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 15:17:43.640 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-07-31 15:17:43.879 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-07-31 15:17:43.882 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 0,10
[2025-07-31 15:17:44.697 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 0,10
[2025-07-31 15:17:46.674 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 15:17:46.676 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 15:17:46.679 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:17:46.732 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:17:46.735 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 15:17:46.738 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-07-31 15:17:46.968 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-07-31 15:17:46.970 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 10,10
[2025-07-31 15:17:47.788 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 10,10
[2025-07-31 15:17:49.776 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 15:17:49.778 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 15:17:49.780 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:17:49.832 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:17:49.834 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 15:17:49.839 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-07-31 15:17:50.074 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-07-31 15:17:50.076 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 0,10
[2025-07-31 15:17:50.846 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 0,10
[2025-07-31 15:17:55.183 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 15:17:55.185 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 15:17:55.187 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:17:55.241 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:17:55.243 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 15:17:55.246 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-07-31 15:17:55.473 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-07-31 15:17:55.475 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 10,10
[2025-07-31 15:17:56.265 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 10,10
[2025-07-31 15:17:57.510 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 15:17:57.512 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 15:17:57.513 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:17:57.560 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:17:57.562 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 15:17:57.566 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-07-31 15:17:57.811 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-07-31 15:17:57.813 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 30,10
[2025-07-31 15:17:58.591 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 30,10
[2025-07-31 15:18:03.001 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 15:18:03.006 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 15:18:03.018 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:18:03.080 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:18:03.082 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 15:18:03.084 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-07-31 15:18:03.321 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-07-31 15:18:03.324 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 10,10
[2025-07-31 15:18:04.089 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 10,10
[2025-07-31 15:18:11.503 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 15:18:11.505 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 15:18:11.512 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:18:11.564 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:18:11.565 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 15:18:11.568 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-07-31 15:18:11.789 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-07-31 15:18:11.791 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 0,10
[2025-07-31 15:18:12.598 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 0,10
[2025-07-31 15:18:14.083 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 15:18:14.086 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 15:18:14.090 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:18:14.138 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:18:14.142 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 15:18:14.148 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=884]
[2025-07-31 15:18:14.202 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-07-31 15:18:14.209 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1 | 参数: [@item_no0=2]
[2025-07-31 15:18:14.261 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1
[2025-07-31 15:18:33.341 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 15:18:33.456 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 15:18:33.541 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:18:33.696 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:18:33.704 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 15:18:34.308 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 15:18:34.323 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 15:18:34.327 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:18:34.381 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:18:34.387 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 15:18:34.581 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 15:18:34.731 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 15:18:34.867 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:18:35.035 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:18:35.052 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 15:18:35.086 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `map_config`  
[2025-07-31 15:18:35.176 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `map_config`  
[2025-07-31 15:18:35.179 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`map_id`,`map_name`,`map_desc`,`map_size`,`map_type`,`atlast_name`,`background`,`bgm`,`bgm_loop`,`bgm_volume`,`bgm_play`,`bgm_mute`,`bgm_pause`,`ico`,`type` FROM `map_config`    ORDER BY `id` ASC LIMIT 0,10
[2025-07-31 15:18:35.240 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`map_id`,`map_name`,`map_desc`,`map_size`,`map_type`,`atlast_name`,`background`,`bgm`,`bgm_loop`,`bgm_volume`,`bgm_play`,`bgm_mute`,`bgm_pause`,`ico`,`type` FROM `map_config`    ORDER BY `id` ASC LIMIT 0,10
[2025-07-31 15:18:37.902 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 15:18:37.968 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 15:18:38.009 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:18:38.082 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:18:38.093 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 15:18:38.098 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`map_id`,`map_name`,`map_desc`,`map_size`,`map_type`,`atlast_name`,`background`,`bgm`,`bgm_loop`,`bgm_volume`,`bgm_play`,`bgm_mute`,`bgm_pause`,`ico`,`type` FROM `map_config` ORDER BY `map_id` ASC 
[2025-07-31 15:18:38.152 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`map_id`,`map_name`,`map_desc`,`map_size`,`map_type`,`atlast_name`,`background`,`bgm`,`bgm_loop`,`bgm_volume`,`bgm_play`,`bgm_mute`,`bgm_pause`,`ico`,`type` FROM `map_config` ORDER BY `map_id` ASC 
[2025-07-31 15:18:38.329 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 15:18:38.329 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 15:18:38.331 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 15:18:38.333 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 15:18:38.338 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:18:38.336 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:18:38.392 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:18:38.393 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 15:18:38.399 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`map_id`,`map_name`,`map_desc`,`map_size`,`map_type`,`atlast_name`,`background`,`bgm`,`bgm_loop`,`bgm_volume`,`bgm_play`,`bgm_mute`,`bgm_pause`,`ico`,`type` FROM `map_config` ORDER BY `map_id` ASC 
[2025-07-31 15:18:38.451 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`map_id`,`map_name`,`map_desc`,`map_size`,`map_type`,`atlast_name`,`background`,`bgm`,`bgm_loop`,`bgm_volume`,`bgm_play`,`bgm_mute`,`bgm_pause`,`ico`,`type` FROM `map_config` ORDER BY `map_id` ASC 
[2025-07-31 15:18:38.566 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:18:38.568 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 15:18:38.578 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM `map_detail` `d` Left JOIN `map_config` `c` ON ( `d`.`map_id` = `c`.`map_id` )    | 参数: [@MethodConst0=0, @MethodConst1=0, @MethodConst2=False, @MethodConst3=0, @MethodConst4=0, @MethodConst5=0, @MethodConst6=0, @MethodConst7=0, @MethodConst8=0, @MethodConst9=0]
[2025-07-31 15:18:38.627 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM `map_detail` `d` Left JOIN `map_config` `c` ON ( `d`.`map_id` = `c`.`map_id` )   
[2025-07-31 15:18:38.632 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `d`.`id` AS `Id` , `d`.`map_id` AS `MapId` , `c`.`map_name` AS `MapName` , IFNULL(`d`.`limit_growth`,@MethodConst10) AS `LimitGrowth` , IFNULL(`d`.`limit_level`,@MethodConst11) AS `LimitLevel` , IFNULL(CAST(`d`.`limit_key` as SIGNED),@MethodConst12) AS `LimitKey` , IFNULL(`d`.`min_gold`,@MethodConst13) AS `MinGold` , IFNULL(`d`.`max_gold`,@MethodConst14) AS `MaxGold` , IFNULL(`d`.`min_yuanbao`,@MethodConst15) AS `MinYuanbao` , IFNULL(`d`.`max_yuanbao`,@MethodConst16) AS `MaxYuanbao` , IFNULL(`d`.`min_drop`,@MethodConst17) AS `MinDrop` , IFNULL(`d`.`max_drop`,@MethodConst18) AS `MaxDrop` , `d`.`icon` AS `Icon` , IFNULL(`d`.`type`,@MethodConst19) AS `Type`  FROM `map_detail` `d` Left JOIN `map_config` `c` ON ( `d`.`map_id` = `c`.`map_id` )     ORDER BY `d`.`id` ASC LIMIT 0,10 | 参数: [@MethodConst0=0, @MethodConst1=0, @MethodConst2=False, @MethodConst3=0, @MethodConst4=0, @MethodConst5=0, @MethodConst6=0, @MethodConst7=0, @MethodConst8=0, @MethodConst9=0, @MethodConst10=0, @MethodConst11=0, @MethodConst12=False, @MethodConst13=0, @MethodConst14=0, @MethodConst15=0, @MethodConst16=0, @MethodConst17=0, @MethodConst18=0, @MethodConst19=0]
[2025-07-31 15:18:38.671 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `d`.`id` AS `Id` , `d`.`map_id` AS `MapId` , `c`.`map_name` AS `MapName` , IFNULL(`d`.`limit_growth`,@MethodConst10) AS `LimitGrowth` , IFNULL(`d`.`limit_level`,@MethodConst11) AS `LimitLevel` , IFNULL(CAST(`d`.`limit_key` as SIGNED),@MethodConst12) AS `LimitKey` , IFNULL(`d`.`min_gold`,@MethodConst13) AS `MinGold` , IFNULL(`d`.`max_gold`,@MethodConst14) AS `MaxGold` , IFNULL(`d`.`min_yuanbao`,@MethodConst15) AS `MinYuanbao` , IFNULL(`d`.`max_yuanbao`,@MethodConst16) AS `MaxYuanbao` , IFNULL(`d`.`min_drop`,@MethodConst17) AS `MinDrop` , IFNULL(`d`.`max_drop`,@MethodConst18) AS `MaxDrop` , `d`.`icon` AS `Icon` , IFNULL(`d`.`type`,@MethodConst19) AS `Type`  FROM `map_detail` `d` Left JOIN `map_config` `c` ON ( `d`.`map_id` = `c`.`map_id` )     ORDER BY `d`.`id` ASC LIMIT 0,10
[2025-07-31 15:18:41.858 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 15:18:41.875 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 15:18:41.906 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:18:41.965 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:18:41.967 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 15:18:41.972 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`map_id`,`map_name`,`map_desc`,`map_size`,`map_type`,`atlast_name`,`background`,`bgm`,`bgm_loop`,`bgm_volume`,`bgm_play`,`bgm_mute`,`bgm_pause`,`ico`,`type` FROM `map_config` ORDER BY `map_id` ASC 
[2025-07-31 15:18:42.018 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`map_id`,`map_name`,`map_desc`,`map_size`,`map_type`,`atlast_name`,`background`,`bgm`,`bgm_loop`,`bgm_volume`,`bgm_play`,`bgm_mute`,`bgm_pause`,`ico`,`type` FROM `map_config` ORDER BY `map_id` ASC 
[2025-07-31 15:18:42.168 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 15:18:42.170 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 15:18:42.172 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:18:42.208 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:18:42.212 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 15:18:42.214 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`map_id`,`map_name`,`map_desc`,`map_size`,`map_type`,`atlast_name`,`background`,`bgm`,`bgm_loop`,`bgm_volume`,`bgm_play`,`bgm_mute`,`bgm_pause`,`ico`,`type` FROM `map_config` ORDER BY `map_id` ASC 
[2025-07-31 15:18:42.250 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`map_id`,`map_name`,`map_desc`,`map_size`,`map_type`,`atlast_name`,`background`,`bgm`,`bgm_loop`,`bgm_volume`,`bgm_play`,`bgm_mute`,`bgm_pause`,`ico`,`type` FROM `map_config` ORDER BY `map_id` ASC 
[2025-07-31 15:18:42.270 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 15:18:42.274 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 15:18:42.280 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:18:42.316 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:18:42.319 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 15:18:42.323 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`monster_no`,`skill`,`name`,`attribute` FROM `monster_config` ORDER BY `monster_no` ASC 
[2025-07-31 15:18:42.372 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`monster_no`,`skill`,`name`,`attribute` FROM `monster_config` ORDER BY `monster_no` ASC 
[2025-07-31 15:18:42.388 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 15:18:42.389 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 15:18:42.391 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:18:42.425 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:18:42.427 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 15:18:42.435 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `map_monster`  
[2025-07-31 15:18:42.483 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `map_monster`  
[2025-07-31 15:18:42.484 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`map_id`,`monster_id`,`monster_name`,`growth`,`element`,`max_level`,`min_level`,`max_drop`,`exp`,`hp`,`mp`,`max_hp`,`max_mp`,`atk`,`def`,`dodge`,`spd`,`hit`,`deepen`,`offset`,`vamp`,`vamp_mp`,`skill_list`,`drop_items` FROM `map_monster`    ORDER BY `map_id` ASC,`monster_id` ASC LIMIT 0,10
[2025-07-31 15:18:42.522 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`map_id`,`monster_id`,`monster_name`,`growth`,`element`,`max_level`,`min_level`,`max_drop`,`exp`,`hp`,`mp`,`max_hp`,`max_mp`,`atk`,`def`,`dodge`,`spd`,`hit`,`deepen`,`offset`,`vamp`,`vamp_mp`,`skill_list`,`drop_items` FROM `map_monster`    ORDER BY `map_id` ASC,`monster_id` ASC LIMIT 0,10
[2025-07-31 15:18:42.535 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`map_id`,`map_name`,`map_desc`,`map_size`,`map_type`,`atlast_name`,`background`,`bgm`,`bgm_loop`,`bgm_volume`,`bgm_play`,`bgm_mute`,`bgm_pause`,`ico`,`type` FROM `map_config`  WHERE  (`map_id` IN (100,101,102,103,104,105,106,107,108,109))  
[2025-07-31 15:18:42.571 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`map_id`,`map_name`,`map_desc`,`map_size`,`map_type`,`atlast_name`,`background`,`bgm`,`bgm_loop`,`bgm_volume`,`bgm_play`,`bgm_mute`,`bgm_pause`,`ico`,`type` FROM `map_config`  WHERE  (`map_id` IN (100,101,102,103,104,105,106,107,108,109))  
[2025-07-31 15:18:46.507 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 15:18:46.548 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 15:18:46.552 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:18:46.589 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:18:46.590 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 15:18:46.691 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 15:18:46.693 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 15:18:46.695 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:18:46.731 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:18:46.734 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 15:18:46.746 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `pet_config`  
[2025-07-31 15:18:46.803 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `pet_config`  
[2025-07-31 15:18:46.805 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`pet_no`,`name`,`attribute`,`skill`,`is_active`,`create_time` FROM `pet_config`    ORDER BY `pet_no` ASC LIMIT 0,10
[2025-07-31 15:18:46.840 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`pet_no`,`name`,`attribute`,`skill`,`is_active`,`create_time` FROM `pet_config`    ORDER BY `pet_no` ASC LIMIT 0,10
[2025-07-31 15:18:50.033 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 15:18:50.036 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 15:18:50.037 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:18:50.082 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:18:50.087 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 15:18:50.089 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `pet_config`  
[2025-07-31 15:18:50.130 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `pet_config`  
[2025-07-31 15:18:50.136 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`pet_no`,`name`,`attribute`,`skill`,`is_active`,`create_time` FROM `pet_config`    ORDER BY `pet_no` ASC LIMIT 10,10
[2025-07-31 15:18:50.173 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`pet_no`,`name`,`attribute`,`skill`,`is_active`,`create_time` FROM `pet_config`    ORDER BY `pet_no` ASC LIMIT 10,10
[2025-07-31 15:18:51.150 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 15:18:51.151 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 15:18:51.153 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:18:51.192 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:18:51.193 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 15:18:51.195 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `pet_config`  
[2025-07-31 15:18:51.237 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `pet_config`  
[2025-07-31 15:18:51.241 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`pet_no`,`name`,`attribute`,`skill`,`is_active`,`create_time` FROM `pet_config`    ORDER BY `pet_no` ASC LIMIT 20,10
[2025-07-31 15:18:51.278 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`pet_no`,`name`,`attribute`,`skill`,`is_active`,`create_time` FROM `pet_config`    ORDER BY `pet_no` ASC LIMIT 20,10
[2025-07-31 15:18:52.118 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 15:18:52.120 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 15:18:52.122 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:18:52.158 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:18:52.164 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 15:18:52.167 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `pet_config`  
[2025-07-31 15:18:52.205 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `pet_config`  
[2025-07-31 15:18:52.211 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`pet_no`,`name`,`attribute`,`skill`,`is_active`,`create_time` FROM `pet_config`    ORDER BY `pet_no` ASC LIMIT 30,10
[2025-07-31 15:18:52.249 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`pet_no`,`name`,`attribute`,`skill`,`is_active`,`create_time` FROM `pet_config`    ORDER BY `pet_no` ASC LIMIT 30,10
[2025-07-31 15:18:53.550 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 15:18:53.551 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 15:18:53.553 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:18:53.589 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:18:53.591 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 15:18:53.594 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `pet_config`  
[2025-07-31 15:18:53.628 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `pet_config`  
[2025-07-31 15:18:53.630 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`pet_no`,`name`,`attribute`,`skill`,`is_active`,`create_time` FROM `pet_config`    ORDER BY `pet_no` ASC LIMIT 40,10
[2025-07-31 15:18:53.668 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`pet_no`,`name`,`attribute`,`skill`,`is_active`,`create_time` FROM `pet_config`    ORDER BY `pet_no` ASC LIMIT 40,10
[2025-07-31 15:18:55.783 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 15:18:55.843 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 15:18:55.849 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:18:55.889 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:18:55.894 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 15:18:55.896 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `pet_config`  
[2025-07-31 15:18:55.939 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `pet_config`  
[2025-07-31 15:18:55.968 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`pet_no`,`name`,`attribute`,`skill`,`is_active`,`create_time` FROM `pet_config`    ORDER BY `pet_no` ASC LIMIT 60,10
[2025-07-31 15:18:56.006 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`pet_no`,`name`,`attribute`,`skill`,`is_active`,`create_time` FROM `pet_config`    ORDER BY `pet_no` ASC LIMIT 60,10
[2025-07-31 15:18:59.095 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 15:18:59.097 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 15:18:59.100 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:18:59.135 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:18:59.137 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 15:18:59.139 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `pet_config`  
[2025-07-31 15:18:59.182 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `pet_config`  
[2025-07-31 15:18:59.185 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`pet_no`,`name`,`attribute`,`skill`,`is_active`,`create_time` FROM `pet_config`    ORDER BY `pet_no` ASC LIMIT 70,10
[2025-07-31 15:18:59.220 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`pet_no`,`name`,`attribute`,`skill`,`is_active`,`create_time` FROM `pet_config`    ORDER BY `pet_no` ASC LIMIT 70,10
[2025-07-31 15:19:03.046 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 15:19:03.047 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 15:19:03.049 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:19:03.085 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:19:03.086 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 15:19:03.089 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `pet_config`  
[2025-07-31 15:19:03.128 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `pet_config`  
[2025-07-31 15:19:03.129 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`pet_no`,`name`,`attribute`,`skill`,`is_active`,`create_time` FROM `pet_config`    ORDER BY `pet_no` ASC LIMIT 60,10
[2025-07-31 15:19:03.165 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`pet_no`,`name`,`attribute`,`skill`,`is_active`,`create_time` FROM `pet_config`    ORDER BY `pet_no` ASC LIMIT 60,10
[2025-07-31 15:19:04.694 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 15:19:04.696 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 15:19:04.698 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:19:04.732 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:19:04.734 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 15:19:04.736 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `pet_config`  
[2025-07-31 15:19:04.775 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `pet_config`  
[2025-07-31 15:19:04.778 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`pet_no`,`name`,`attribute`,`skill`,`is_active`,`create_time` FROM `pet_config`    ORDER BY `pet_no` ASC LIMIT 40,10
[2025-07-31 15:19:04.815 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`pet_no`,`name`,`attribute`,`skill`,`is_active`,`create_time` FROM `pet_config`    ORDER BY `pet_no` ASC LIMIT 40,10
[2025-07-31 15:19:05.544 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 15:19:05.550 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 15:19:05.555 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:19:05.596 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:19:05.642 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 15:19:05.650 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `pet_config`  
[2025-07-31 15:19:05.692 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `pet_config`  
[2025-07-31 15:19:05.695 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`pet_no`,`name`,`attribute`,`skill`,`is_active`,`create_time` FROM `pet_config`    ORDER BY `pet_no` ASC LIMIT 20,10
[2025-07-31 15:19:05.752 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`pet_no`,`name`,`attribute`,`skill`,`is_active`,`create_time` FROM `pet_config`    ORDER BY `pet_no` ASC LIMIT 20,10
[2025-07-31 15:19:06.455 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 15:19:06.457 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 15:19:06.459 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:19:06.499 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:19:06.500 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 15:19:06.502 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `pet_config`  
[2025-07-31 15:19:06.544 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `pet_config`  
[2025-07-31 15:19:06.547 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`pet_no`,`name`,`attribute`,`skill`,`is_active`,`create_time` FROM `pet_config`    ORDER BY `pet_no` ASC LIMIT 0,10
[2025-07-31 15:19:06.584 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`pet_no`,`name`,`attribute`,`skill`,`is_active`,`create_time` FROM `pet_config`    ORDER BY `pet_no` ASC LIMIT 0,10
[2025-07-31 15:19:13.650 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 15:19:13.654 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 15:19:13.656 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:19:13.691 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:19:13.693 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 15:19:13.703 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT 1 FROM `pet_config`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=1]
[2025-07-31 15:19:13.741 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT 1 FROM `pet_config`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-07-31 15:19:13.756 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT 1 FROM `pet_config`   WHERE ( `pet_no` = @pet_no0 )  AND ( `id` <> @id1 )   LIMIT 0,1 | 参数: [@pet_no0=1, @id1=1]
[2025-07-31 15:19:13.796 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT 1 FROM `pet_config`   WHERE ( `pet_no` = @pet_no0 )  AND ( `id` <> @id1 )   LIMIT 0,1
[2025-07-31 15:19:13.815 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: UPDATE `pet_config`  SET
           `pet_no`=@pet_no,`name`=@name,`attribute`=@attribute,`skill`=@skill,`is_active`=@is_active,`create_time`=@create_time  WHERE `id`=@id | 参数: [@id=1, @pet_no=1, @name=金波姆1, @attribute=金, @skill=, @is_active=, @create_time=]
[2025-07-31 15:19:13.900 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: UPDATE `pet_config`  SET
           `pet_no`=@pet_no,`name`=@name,`attribute`=@attribute,`skill`=@skill,`is_active`=@is_active,`create_time`=@create_time  WHERE `id`=@id
[2025-07-31 15:19:15.242 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 15:19:15.246 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 15:19:15.250 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:19:15.287 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:19:15.288 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 15:19:15.290 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `pet_config`  
[2025-07-31 15:19:15.327 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `pet_config`  
[2025-07-31 15:19:15.328 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`pet_no`,`name`,`attribute`,`skill`,`is_active`,`create_time` FROM `pet_config`    ORDER BY `pet_no` ASC LIMIT 0,10
[2025-07-31 15:19:15.365 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`pet_no`,`name`,`attribute`,`skill`,`is_active`,`create_time` FROM `pet_config`    ORDER BY `pet_no` ASC LIMIT 0,10
[2025-07-31 15:19:21.503 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 15:19:21.506 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 15:19:21.508 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:19:21.542 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:19:21.543 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 15:19:21.545 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT 1 FROM `pet_config`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=1]
[2025-07-31 15:19:21.579 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT 1 FROM `pet_config`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-07-31 15:19:21.583 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT 1 FROM `pet_config`   WHERE ( `pet_no` = @pet_no0 )  AND ( `id` <> @id1 )   LIMIT 0,1 | 参数: [@pet_no0=1, @id1=1]
[2025-07-31 15:19:21.620 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT 1 FROM `pet_config`   WHERE ( `pet_no` = @pet_no0 )  AND ( `id` <> @id1 )   LIMIT 0,1
[2025-07-31 15:19:21.622 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: UPDATE `pet_config`  SET
           `pet_no`=@pet_no,`name`=@name,`attribute`=@attribute,`skill`=@skill,`is_active`=@is_active,`create_time`=@create_time  WHERE `id`=@id | 参数: [@id=1, @pet_no=1, @name=金波姆, @attribute=金, @skill=, @is_active=, @create_time=]
[2025-07-31 15:19:21.698 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: UPDATE `pet_config`  SET
           `pet_no`=@pet_no,`name`=@name,`attribute`=@attribute,`skill`=@skill,`is_active`=@is_active,`create_time`=@create_time  WHERE `id`=@id
[2025-07-31 15:19:23.703 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 15:19:24.067 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 15:19:24.073 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:19:24.117 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:19:24.119 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 15:19:24.121 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `pet_config`  
[2025-07-31 15:19:24.155 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `pet_config`  
[2025-07-31 15:19:24.157 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`pet_no`,`name`,`attribute`,`skill`,`is_active`,`create_time` FROM `pet_config`    ORDER BY `pet_no` ASC LIMIT 0,10
[2025-07-31 15:19:24.204 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`pet_no`,`name`,`attribute`,`skill`,`is_active`,`create_time` FROM `pet_config`    ORDER BY `pet_no` ASC LIMIT 0,10
[2025-07-31 15:19:30.406 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 15:19:30.408 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 15:19:30.410 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:19:30.444 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:19:30.445 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 15:19:30.448 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT 1 FROM `pet_config`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=1]
[2025-07-31 15:19:30.492 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT 1 FROM `pet_config`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-07-31 15:19:30.497 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT 1 FROM `pet_config`   WHERE ( `pet_no` = @pet_no0 )  AND ( `id` <> @id1 )   LIMIT 0,1 | 参数: [@pet_no0=1, @id1=1]
[2025-07-31 15:19:30.534 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT 1 FROM `pet_config`   WHERE ( `pet_no` = @pet_no0 )  AND ( `id` <> @id1 )   LIMIT 0,1
[2025-07-31 15:19:30.536 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: UPDATE `pet_config`  SET
           `pet_no`=@pet_no,`name`=@name,`attribute`=@attribute,`skill`=@skill,`is_active`=@is_active,`create_time`=@create_time  WHERE `id`=@id | 参数: [@id=1, @pet_no=1, @name=金波姆, @attribute=金, @skill=1, @is_active=, @create_time=]
[2025-07-31 15:19:30.616 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: UPDATE `pet_config`  SET
           `pet_no`=@pet_no,`name`=@name,`attribute`=@attribute,`skill`=@skill,`is_active`=@is_active,`create_time`=@create_time  WHERE `id`=@id
[2025-07-31 15:19:31.907 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 15:19:31.912 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 15:19:31.914 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:19:31.948 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:19:31.950 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 15:19:31.952 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `pet_config`  
[2025-07-31 15:19:31.988 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `pet_config`  
[2025-07-31 15:19:31.990 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`pet_no`,`name`,`attribute`,`skill`,`is_active`,`create_time` FROM `pet_config`    ORDER BY `pet_no` ASC LIMIT 0,10
[2025-07-31 15:19:32.026 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`pet_no`,`name`,`attribute`,`skill`,`is_active`,`create_time` FROM `pet_config`    ORDER BY `pet_no` ASC LIMIT 0,10
[2025-07-31 15:19:36.667 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 15:19:36.690 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 15:19:36.700 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:19:36.747 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:19:36.757 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 15:19:37.274 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 15:19:37.277 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 15:19:37.283 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:19:37.318 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:19:37.319 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 15:19:37.330 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `skill`  
[2025-07-31 15:19:37.382 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `skill`  
[2025-07-31 15:19:37.385 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `skill_id`,`skill_name`,`skill_percent`,`effect_type`,`effect_value`,`mana_cost`,`buff_info`,`element_limit`,`create_time`,`skill_type` FROM `skill`    ORDER BY `create_time` DESC LIMIT 0,10
[2025-07-31 15:19:37.429 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `skill_id`,`skill_name`,`skill_percent`,`effect_type`,`effect_value`,`mana_cost`,`buff_info`,`element_limit`,`create_time`,`skill_type` FROM `skill`    ORDER BY `create_time` DESC LIMIT 0,10
[2025-07-31 15:19:55.319 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 15:19:55.322 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 15:19:55.330 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:19:55.370 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:19:55.376 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 15:19:55.510 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 15:19:55.510 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 15:19:55.514 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 15:19:55.516 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 15:19:55.518 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:19:55.519 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:19:55.557 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:19:55.561 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 15:19:55.568 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `user`  
[2025-07-31 15:19:55.574 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:19:55.576 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 15:19:55.600 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM `user_equipment` `ue` Left JOIN `user` `u` ON ( `ue`.`user_id` = `u`.`id` )  Left JOIN `equipment_type` `et` ON ( `ue`.`equip_type_id` = `et`.`equip_type_id` )   WHERE ( 1 = 1 )   | 参数: [@MethodConst0=0, @MethodConst1=0, @MethodConst2=False, @MethodConst3=2025/7/31 15:19:55]
[2025-07-31 15:19:55.607 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `user`  
[2025-07-31 15:19:55.610 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`username`,`password`,`nickname`,`sex`,`vip_level`,`vip_score`,`gold`,`yuanbao`,`crystal`,`reg_time`,`title`,`main_pet_id`,`main_pet_name`,`create_time`,`evolution_cd`,`synthesis_cd`,`nirvana_cd`,`money`,`supreme_vip`,`prop_capacity`,`pasture_capacity`,`dragon_ball_exp`,`dragon_ball_level`,`status`,`prestige`,`auto_battle_count`,`auto_nirvana_count`,`monster_count`,`next_battle`,`open_maps`,`star_vip`,`star_vip_expire`,`task_helper_enabled`,`title_attributes`,`soul_pet`,`hell_floor`,`tower_floor` FROM `user`    ORDER BY `create_time` DESC LIMIT 0,1000
[2025-07-31 15:19:55.645 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`username`,`password`,`nickname`,`sex`,`vip_level`,`vip_score`,`gold`,`yuanbao`,`crystal`,`reg_time`,`title`,`main_pet_id`,`main_pet_name`,`create_time`,`evolution_cd`,`synthesis_cd`,`nirvana_cd`,`money`,`supreme_vip`,`prop_capacity`,`pasture_capacity`,`dragon_ball_exp`,`dragon_ball_level`,`status`,`prestige`,`auto_battle_count`,`auto_nirvana_count`,`monster_count`,`next_battle`,`open_maps`,`star_vip`,`star_vip_expire`,`task_helper_enabled`,`title_attributes`,`soul_pet`,`hell_floor`,`tower_floor` FROM `user`    ORDER BY `create_time` DESC LIMIT 0,1000
[2025-07-31 15:19:55.663 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 15:19:55.665 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 15:19:55.667 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:19:55.671 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM `user_equipment` `ue` Left JOIN `user` `u` ON ( `ue`.`user_id` = `u`.`id` )  Left JOIN `equipment_type` `et` ON ( `ue`.`equip_type_id` = `et`.`equip_type_id` )   WHERE ( 1 = 1 )  
[2025-07-31 15:19:55.677 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ue`.`id` AS `Id` , `ue`.`user_id` AS `UserId` , `u`.`username` AS `UserName` , `ue`.`equip_id` AS `EquipId` , `ue`.`name` AS `Name` , `ue`.`icon` AS `Icon` , `ue`.`equip_type_id` AS `EquipTypeId` , `et`.`type_name` AS `EquipTypeName` , IFNULL(`ue`.`strengthen_level`,@MethodConst4) AS `StrengthenLevel` , IFNULL(`ue`.`slot`,@MethodConst5) AS `Slot` , `ue`.`position` AS `Position` , IFNULL(CAST(`ue`.`is_equipped` as SIGNED),@MethodConst6) AS `IsEquipped` , IFNULL(`ue`.`create_time`,@MethodConst7) AS `CreateTime`  FROM `user_equipment` `ue` Left JOIN `user` `u` ON ( `ue`.`user_id` = `u`.`id` )  Left JOIN `equipment_type` `et` ON ( `ue`.`equip_type_id` = `et`.`equip_type_id` )    WHERE ( 1 = 1 )   ORDER BY `ue`.`create_time` DESC LIMIT 0,10 | 参数: [@MethodConst0=0, @MethodConst1=0, @MethodConst2=False, @MethodConst3=2025/7/31 15:19:55, @MethodConst4=0, @MethodConst5=0, @MethodConst6=False, @MethodConst7=2025/7/31 15:19:55]
[2025-07-31 15:19:55.701 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:19:55.702 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 15:19:55.710 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  CAST(`id` AS CHAR) AS `Value` , `type_name` AS `Label`  FROM `equipment_type`  
[2025-07-31 15:19:55.730 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ue`.`id` AS `Id` , `ue`.`user_id` AS `UserId` , `u`.`username` AS `UserName` , `ue`.`equip_id` AS `EquipId` , `ue`.`name` AS `Name` , `ue`.`icon` AS `Icon` , `ue`.`equip_type_id` AS `EquipTypeId` , `et`.`type_name` AS `EquipTypeName` , IFNULL(`ue`.`strengthen_level`,@MethodConst4) AS `StrengthenLevel` , IFNULL(`ue`.`slot`,@MethodConst5) AS `Slot` , `ue`.`position` AS `Position` , IFNULL(CAST(`ue`.`is_equipped` as SIGNED),@MethodConst6) AS `IsEquipped` , IFNULL(`ue`.`create_time`,@MethodConst7) AS `CreateTime`  FROM `user_equipment` `ue` Left JOIN `user` `u` ON ( `ue`.`user_id` = `u`.`id` )  Left JOIN `equipment_type` `et` ON ( `ue`.`equip_type_id` = `et`.`equip_type_id` )    WHERE ( 1 = 1 )   ORDER BY `ue`.`create_time` DESC LIMIT 0,10
[2025-07-31 15:19:55.741 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1 | 参数: [@equip_id0=2016121601, @MethodConst1=0, @MethodConst2=0, @MethodConst3=0, @MethodConst4=0, @MethodConst5=0, @MethodConst6=0, @MethodConst7=0, @MethodConst8=0, @MethodConst9=0, @MethodConst10=0, @MethodConst11=0, @MethodConst12=, @MethodConst13=, @MethodConst14=, @MethodConst15=0, @MethodConst16=0, @MethodConst17=0, @MethodConst18=0, @MethodConst19=0, @MethodConst20=0, @MethodConst21=0, @MethodConst22=0, @MethodConst23=0, @MethodConst24=0, @MethodConst25=0, @MethodConst26=, @MethodConst27=, @MethodConst28=]
[2025-07-31 15:19:55.761 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  CAST(`id` AS CHAR) AS `Value` , `type_name` AS `Label`  FROM `equipment_type`  
[2025-07-31 15:19:55.769 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 15:19:55.771 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 15:19:55.772 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:19:55.792 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1
[2025-07-31 15:19:55.799 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1 | 参数: [@equip_id0=789015, @MethodConst1=0, @MethodConst2=0, @MethodConst3=0, @MethodConst4=0, @MethodConst5=0, @MethodConst6=0, @MethodConst7=0, @MethodConst8=0, @MethodConst9=0, @MethodConst10=0, @MethodConst11=0, @MethodConst12=, @MethodConst13=, @MethodConst14=, @MethodConst15=0, @MethodConst16=0, @MethodConst17=0, @MethodConst18=0, @MethodConst19=0, @MethodConst20=0, @MethodConst21=0, @MethodConst22=0, @MethodConst23=0, @MethodConst24=0, @MethodConst25=0, @MethodConst26=, @MethodConst27=, @MethodConst28=]
[2025-07-31 15:19:55.809 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:19:55.810 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 15:19:55.813 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `equip_id` AS `Value` , `name` AS `Label` , `name` AS `Name` , `icon` AS `Icon` , `equip_type_id` AS `EquipTypeId`  FROM `equipment`  
[2025-07-31 15:19:55.859 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `equip_id` AS `Value` , `name` AS `Label` , `name` AS `Name` , `icon` AS `Icon` , `equip_type_id` AS `EquipTypeId`  FROM `equipment`  
[2025-07-31 15:19:55.860 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1
[2025-07-31 15:19:55.867 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1 | 参数: [@equip_id0=789012, @MethodConst1=0, @MethodConst2=0, @MethodConst3=0, @MethodConst4=0, @MethodConst5=0, @MethodConst6=0, @MethodConst7=0, @MethodConst8=0, @MethodConst9=0, @MethodConst10=0, @MethodConst11=0, @MethodConst12=, @MethodConst13=, @MethodConst14=, @MethodConst15=0, @MethodConst16=0, @MethodConst17=0, @MethodConst18=0, @MethodConst19=0, @MethodConst20=0, @MethodConst21=0, @MethodConst22=0, @MethodConst23=0, @MethodConst24=0, @MethodConst25=0, @MethodConst26=, @MethodConst27=, @MethodConst28=]
[2025-07-31 15:19:55.902 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  IFNULL(`atk`,@MethodConst15) AS `Atk` , IFNULL(`hit`,@MethodConst16) AS `Hit` , IFNULL(`def`,@MethodConst17) AS `Def` , IFNULL(`spd`,@MethodConst18) AS `Spd` , IFNULL(`dodge`,@MethodConst19) AS `Dodge` , IFNULL(`hp`,@MethodConst20) AS `Hp` , IFNULL(`mp`,@MethodConst21) AS `Mp` , IFNULL(`deepen`,@MethodConst22) AS `Deepen` , IFNULL(`offset`,@MethodConst23) AS `Offset` , IFNULL(`vamp`,@MethodConst24) AS `Vamp` , IFNULL(`vamp_mp`,@MethodConst25) AS `VampMp` , IFNULL(`description`,@MethodConst26) AS `Description` , IFNULL(`main_attr`,@MethodConst27) AS `MainAttr` , IFNULL(`element_limit`,@MethodConst28) AS `ElementLimit`  FROM `equipment_detail` `ed`   WHERE ( `equip_id` = @equip_id0 )   LIMIT 0,1
[2025-07-31 15:19:59.202 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 15:19:59.228 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 15:19:59.233 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:19:59.275 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:19:59.277 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 15:19:59.463 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 15:19:59.466 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 15:19:59.469 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:19:59.505 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:19:59.510 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 15:19:59.514 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `user`  
[2025-07-31 15:19:59.554 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `user`  
[2025-07-31 15:19:59.557 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`username`,`password`,`nickname`,`sex`,`vip_level`,`vip_score`,`gold`,`yuanbao`,`crystal`,`reg_time`,`title`,`main_pet_id`,`main_pet_name`,`create_time`,`evolution_cd`,`synthesis_cd`,`nirvana_cd`,`money`,`supreme_vip`,`prop_capacity`,`pasture_capacity`,`dragon_ball_exp`,`dragon_ball_level`,`status`,`prestige`,`auto_battle_count`,`auto_nirvana_count`,`monster_count`,`next_battle`,`open_maps`,`star_vip`,`star_vip_expire`,`task_helper_enabled`,`title_attributes`,`soul_pet`,`hell_floor`,`tower_floor` FROM `user`    ORDER BY `create_time` DESC LIMIT 0,1000
[2025-07-31 15:19:59.592 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`username`,`password`,`nickname`,`sex`,`vip_level`,`vip_score`,`gold`,`yuanbao`,`crystal`,`reg_time`,`title`,`main_pet_id`,`main_pet_name`,`create_time`,`evolution_cd`,`synthesis_cd`,`nirvana_cd`,`money`,`supreme_vip`,`prop_capacity`,`pasture_capacity`,`dragon_ball_exp`,`dragon_ball_level`,`status`,`prestige`,`auto_battle_count`,`auto_nirvana_count`,`monster_count`,`next_battle`,`open_maps`,`star_vip`,`star_vip_expire`,`task_helper_enabled`,`title_attributes`,`soul_pet`,`hell_floor`,`tower_floor` FROM `user`    ORDER BY `create_time` DESC LIMIT 0,1000
[2025-07-31 15:19:59.608 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 15:19:59.610 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 15:19:59.612 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:19:59.646 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:19:59.647 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 15:19:59.653 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `id` AS `Id` , `item_no` AS `ItemNo` , `name` AS `Name` , `type` AS `Type` , `description` AS `Description` , `quality` AS `Quality` , `icon` AS `Icon` , `price` AS `Price` , `use_limit` AS `UseLimit` , `create_time` AS `CreateTime`  FROM `item_config` ORDER BY `item_no` ASC 
[2025-07-31 15:19:59.692 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `id` AS `Id` , `item_no` AS `ItemNo` , `name` AS `Name` , `type` AS `Type` , `description` AS `Description` , `quality` AS `Quality` , `icon` AS `Icon` , `price` AS `Price` , `use_limit` AS `UseLimit` , `create_time` AS `CreateTime`  FROM `item_config` ORDER BY `item_no` ASC 
[2025-07-31 15:19:59.739 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 15:19:59.743 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 15:19:59.745 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:19:59.780 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:19:59.782 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 15:19:59.795 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT SUM(`item_count`) FROM `user_item`  
[2025-07-31 15:19:59.831 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT SUM(`item_count`) FROM `user_item`  
[2025-07-31 15:19:59.846 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `user_item` GROUP BY `item_id`  
[2025-07-31 15:19:59.882 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `user_item` GROUP BY `item_id`  
[2025-07-31 15:19:59.886 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ic`.`quality` AS `Quality` , SUM(`ui`.`item_count`) AS `Count`  FROM `user_item` `ui` Left JOIN `item_config` `ic` ON ( `ui`.`item_id` = CAST(`ic`.`item_no` AS CHAR))  GROUP BY `ic`.`quality`  
[2025-07-31 15:19:59.928 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ic`.`quality` AS `Quality` , SUM(`ui`.`item_count`) AS `Count`  FROM `user_item` `ui` Left JOIN `item_config` `ic` ON ( `ui`.`item_id` = CAST(`ic`.`item_no` AS CHAR))  GROUP BY `ic`.`quality`  
[2025-07-31 15:19:59.935 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT SUM(( `ui`.`item_count` *IFNULL(`ic`.`price`,@MethodConst0))) FROM `user_item` `ui` Left JOIN `item_config` `ic` ON ( `ui`.`item_id` = CAST(`ic`.`item_no` AS CHAR))    | 参数: [@MethodConst0=0]
[2025-07-31 15:19:59.977 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT SUM(( `ui`.`item_count` *IFNULL(`ic`.`price`,@MethodConst0))) FROM `user_item` `ui` Left JOIN `item_config` `ic` ON ( `ui`.`item_id` = CAST(`ic`.`item_no` AS CHAR))   
[2025-07-31 15:19:59.985 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 15:19:59.986 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 15:19:59.987 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:20:00.024 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:20:00.026 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 15:20:00.034 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `user_item` `ui` Left JOIN `user` `u` ON ( `ui`.`user_id` = `u`.`id` )  Left JOIN `item_config` `ic` ON ( `ui`.`item_id` = CAST(`ic`.`item_no` AS CHAR))   
[2025-07-31 15:20:00.077 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `user_item` `ui` Left JOIN `user` `u` ON ( `ui`.`user_id` = `u`.`id` )  Left JOIN `item_config` `ic` ON ( `ui`.`item_id` = CAST(`ic`.`item_no` AS CHAR))   
[2025-07-31 15:20:00.079 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ui`.`id` AS `Id` , `ui`.`user_id` AS `UserId` , IFNULL(`u`.`username`,@MethodConst2) AS `Username` , `ui`.`item_id` AS `ItemId` , CAST(`ui`.`item_id` AS SIGNED) AS `ItemNo` , IFNULL(`ic`.`name`,@MethodConst3) AS `ItemName` , `ic`.`type` AS `ItemType` , `ic`.`quality` AS `ItemQuality` , `ui`.`item_count` AS `ItemCount` , `ui`.`item_pos` AS `ItemPos` , `ui`.`item_seq` AS `ItemSeq` , `ic`.`description` AS `ItemDescription` , `ic`.`price` AS `ItemPrice` , `ic`.`icon` AS `ItemIcon`  FROM `user_item` `ui` Left JOIN `user` `u` ON ( `ui`.`user_id` = `u`.`id` )  Left JOIN `item_config` `ic` ON ( `ui`.`item_id` = CAST(`ic`.`item_no` AS CHAR))     ORDER BY `ui`.`id` DESC LIMIT 0,15 | 参数: [@MethodConst0=, @MethodConst1=, @MethodConst2=, @MethodConst3=]
[2025-07-31 15:20:00.131 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ui`.`id` AS `Id` , `ui`.`user_id` AS `UserId` , IFNULL(`u`.`username`,@MethodConst2) AS `Username` , `ui`.`item_id` AS `ItemId` , CAST(`ui`.`item_id` AS SIGNED) AS `ItemNo` , IFNULL(`ic`.`name`,@MethodConst3) AS `ItemName` , `ic`.`type` AS `ItemType` , `ic`.`quality` AS `ItemQuality` , `ui`.`item_count` AS `ItemCount` , `ui`.`item_pos` AS `ItemPos` , `ui`.`item_seq` AS `ItemSeq` , `ic`.`description` AS `ItemDescription` , `ic`.`price` AS `ItemPrice` , `ic`.`icon` AS `ItemIcon`  FROM `user_item` `ui` Left JOIN `user` `u` ON ( `ui`.`user_id` = `u`.`id` )  Left JOIN `item_config` `ic` ON ( `ui`.`item_id` = CAST(`ic`.`item_no` AS CHAR))     ORDER BY `ui`.`id` DESC LIMIT 0,15
[2025-07-31 15:20:04.076 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 15:20:04.078 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 15:20:04.081 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:20:04.117 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:20:04.118 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 15:20:04.273 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 15:20:04.274 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 15:20:04.277 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:20:04.320 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:20:04.323 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 15:20:04.326 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `user`  
[2025-07-31 15:20:04.364 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `user`  
[2025-07-31 15:20:04.366 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`username`,`password`,`nickname`,`sex`,`vip_level`,`vip_score`,`gold`,`yuanbao`,`crystal`,`reg_time`,`title`,`main_pet_id`,`main_pet_name`,`create_time`,`evolution_cd`,`synthesis_cd`,`nirvana_cd`,`money`,`supreme_vip`,`prop_capacity`,`pasture_capacity`,`dragon_ball_exp`,`dragon_ball_level`,`status`,`prestige`,`auto_battle_count`,`auto_nirvana_count`,`monster_count`,`next_battle`,`open_maps`,`star_vip`,`star_vip_expire`,`task_helper_enabled`,`title_attributes`,`soul_pet`,`hell_floor`,`tower_floor` FROM `user`    ORDER BY `create_time` DESC LIMIT 0,1000
[2025-07-31 15:20:04.414 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`username`,`password`,`nickname`,`sex`,`vip_level`,`vip_score`,`gold`,`yuanbao`,`crystal`,`reg_time`,`title`,`main_pet_id`,`main_pet_name`,`create_time`,`evolution_cd`,`synthesis_cd`,`nirvana_cd`,`money`,`supreme_vip`,`prop_capacity`,`pasture_capacity`,`dragon_ball_exp`,`dragon_ball_level`,`status`,`prestige`,`auto_battle_count`,`auto_nirvana_count`,`monster_count`,`next_battle`,`open_maps`,`star_vip`,`star_vip_expire`,`task_helper_enabled`,`title_attributes`,`soul_pet`,`hell_floor`,`tower_floor` FROM `user`    ORDER BY `create_time` DESC LIMIT 0,1000
[2025-07-31 15:20:04.426 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 15:20:04.428 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 15:20:04.430 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:20:04.470 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:20:04.473 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 15:20:04.477 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `id` AS `Id` , `pet_no` AS `PetNo` , `name` AS `Name` , `attribute` AS `Attribute` , `skill` AS `Skill`  FROM `pet_config` ORDER BY `pet_no` ASC 
[2025-07-31 15:20:04.518 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `id` AS `Id` , `pet_no` AS `PetNo` , `name` AS `Name` , `attribute` AS `Attribute` , `skill` AS `Skill`  FROM `pet_config` ORDER BY `pet_no` ASC 
[2025-07-31 15:20:04.531 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 15:20:04.533 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 15:20:04.534 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:20:04.586 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:20:04.588 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 15:20:04.593 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`realm_id`,`realm_name`,`attribute_bonus`,`bonus_type`,`description`,`level_requirement`,`hp_bonus`,`attack_bonus`,`defense_bonus`,`realm_level`,`attribute_bonus_rate`,`upgrade_success_rate`,`upgrade_cost_gold`,`upgrade_item_required`,`created_at`,`updated_at` FROM `realm_config` ORDER BY `realm_id` ASC 
[2025-07-31 15:20:04.646 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`realm_id`,`realm_name`,`attribute_bonus`,`bonus_type`,`description`,`level_requirement`,`hp_bonus`,`attack_bonus`,`defense_bonus`,`realm_level`,`attribute_bonus_rate`,`upgrade_success_rate`,`upgrade_cost_gold`,`upgrade_item_required`,`created_at`,`updated_at` FROM `realm_config` ORDER BY `realm_id` ASC 
[2025-07-31 15:20:04.660 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 15:20:04.661 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 15:20:04.663 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:20:04.706 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:20:04.708 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 15:20:04.717 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 15:20:04.718 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 15:20:04.719 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:20:04.756 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:20:04.757 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 15:20:04.769 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )   
[2025-07-31 15:20:04.822 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )   
[2025-07-31 15:20:04.826 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )    | 参数: [@MethodConst0=, @MethodConst1=, @MethodConst2=, @MethodConst3=]
[2025-07-31 15:20:04.862 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )   
[2025-07-31 15:20:04.866 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `up`.`id` AS `Id` , `up`.`user_id` AS `UserId` , IFNULL(`u`.`username`,@MethodConst4) AS `UserName` , `up`.`pet_no` AS `PetNo` , IFNULL(`pc`.`name`,@MethodConst5) AS `PetName` , IFNULL(`pc`.`attribute`,@MethodConst6) AS `PetAttribute` , `up`.`image` AS `Image` , `up`.`exp` AS `Exp` , `up`.`hp` AS `Hp` , `up`.`mp` AS `Mp` , `up`.`atk` AS `Atk` , `up`.`def` AS `Def` , `up`.`spd` AS `Spd` , `up`.`state` AS `State` , `up`.`dodge` AS `Dodge` , `up`.`growth` AS `Growth` , `up`.`hit` AS `Hit` , `up`.`realm` AS `Realm` , `up`.`evolve_count` AS `EvolveCount` , `up`.`element` AS `Element` , `up`.`create_time` AS `CreateTime` , IFNULL(`up`.`status`,@MethodConst7) AS `Status` , `up`.`is_main` AS `IsMain`  FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )     ORDER BY `up`.`create_time` DESC LIMIT 0,10 | 参数: [@MethodConst0=, @MethodConst1=, @MethodConst2=, @MethodConst3=, @MethodConst4=, @MethodConst5=, @MethodConst6=, @MethodConst7=]
[2025-07-31 15:20:04.903 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `up`.`id` AS `Id` , `up`.`user_id` AS `UserId` , IFNULL(`u`.`username`,@MethodConst4) AS `UserName` , `up`.`pet_no` AS `PetNo` , IFNULL(`pc`.`name`,@MethodConst5) AS `PetName` , IFNULL(`pc`.`attribute`,@MethodConst6) AS `PetAttribute` , `up`.`image` AS `Image` , `up`.`exp` AS `Exp` , `up`.`hp` AS `Hp` , `up`.`mp` AS `Mp` , `up`.`atk` AS `Atk` , `up`.`def` AS `Def` , `up`.`spd` AS `Spd` , `up`.`state` AS `State` , `up`.`dodge` AS `Dodge` , `up`.`growth` AS `Growth` , `up`.`hit` AS `Hit` , `up`.`realm` AS `Realm` , `up`.`evolve_count` AS `EvolveCount` , `up`.`element` AS `Element` , `up`.`create_time` AS `CreateTime` , IFNULL(`up`.`status`,@MethodConst7) AS `Status` , `up`.`is_main` AS `IsMain`  FROM `user_pet` `up` Left JOIN `user` `u` ON ( `up`.`user_id` = `u`.`id` )  Left JOIN `pet_config` `pc` ON ( `up`.`pet_no` = `pc`.`pet_no` )     ORDER BY `up`.`create_time` DESC LIMIT 0,10
[2025-07-31 15:20:04.917 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=8, @MethodConst1=]
[2025-07-31 15:20:05.008 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-07-31 15:20:05.012 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=7, @MethodConst1=]
[2025-07-31 15:20:05.048 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-07-31 15:20:05.057 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=6, @MethodConst1=]
[2025-07-31 15:20:05.094 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-07-31 15:20:05.100 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 )  | 参数: [@user_pet_id0=5, @MethodConst1=]
[2025-07-31 15:20:05.142 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `ups`.`id` AS `Id` , `ups`.`user_pet_id` AS `UserPetId` , `ups`.`skill_id` AS `SkillId` , IFNULL(`s`.`skill_name`,@MethodConst1) AS `SkillName` , `ups`.`skill_level` AS `SkillLevel` , `ups`.`create_time` AS `CreateTime`  FROM `user_pet_skill` `ups` Left JOIN `skill` `s` ON (CAST(`ups`.`skill_id` AS CHAR) = `s`.`skill_id` )   WHERE ( `ups`.`user_pet_id` = @user_pet_id0 ) 
[2025-07-31 15:20:05.179 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 15:20:05.182 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 15:20:05.185 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:20:05.280 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:20:05.283 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 15:20:05.292 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `realm_config`  
[2025-07-31 15:20:05.327 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `realm_config`  
[2025-07-31 15:20:05.329 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`realm_id`,`realm_name`,`attribute_bonus`,`bonus_type`,`description`,`level_requirement`,`hp_bonus`,`attack_bonus`,`defense_bonus`,`realm_level`,`attribute_bonus_rate`,`upgrade_success_rate`,`upgrade_cost_gold`,`upgrade_item_required`,`created_at`,`updated_at` FROM `realm_config`    ORDER BY `realm_id` ASC LIMIT 0,10
[2025-07-31 15:20:05.368 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`realm_id`,`realm_name`,`attribute_bonus`,`bonus_type`,`description`,`level_requirement`,`hp_bonus`,`attack_bonus`,`defense_bonus`,`realm_level`,`attribute_bonus_rate`,`upgrade_success_rate`,`upgrade_cost_gold`,`upgrade_item_required`,`created_at`,`updated_at` FROM `realm_config`    ORDER BY `realm_id` ASC LIMIT 0,10
[2025-07-31 15:20:05.375 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `user_pet`  WHERE ( `realm` = @realm0 )  | 参数: [@realm0=元神初具]
[2025-07-31 15:20:05.412 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `user_pet`  WHERE ( `realm` = @realm0 ) 
[2025-07-31 15:20:05.416 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `user_pet`  WHERE ( `realm` = @realm0 )  | 参数: [@realm0=一日千里]
[2025-07-31 15:20:05.457 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `user_pet`  WHERE ( `realm` = @realm0 ) 
[2025-07-31 15:20:05.460 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `user_pet`  WHERE ( `realm` = @realm0 )  | 参数: [@realm0=脱胎换骨]
[2025-07-31 15:20:05.498 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `user_pet`  WHERE ( `realm` = @realm0 ) 
[2025-07-31 15:20:05.501 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `user_pet`  WHERE ( `realm` = @realm0 )  | 参数: [@realm0=负海担山]
[2025-07-31 15:20:05.535 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `user_pet`  WHERE ( `realm` = @realm0 ) 
[2025-07-31 15:20:05.538 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `user_pet`  WHERE ( `realm` = @realm0 )  | 参数: [@realm0=霞举飞升]
[2025-07-31 15:20:05.578 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `user_pet`  WHERE ( `realm` = @realm0 ) 
[2025-07-31 15:20:05.581 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `user_pet`  WHERE ( `realm` = @realm0 )  | 参数: [@realm0=移星换斗]
[2025-07-31 15:20:05.628 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `user_pet`  WHERE ( `realm` = @realm0 ) 
[2025-07-31 15:20:05.633 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `user_pet`  WHERE ( `realm` = @realm0 )  | 参数: [@realm0=变幻莫测]
[2025-07-31 15:20:05.669 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `user_pet`  WHERE ( `realm` = @realm0 ) 
[2025-07-31 15:20:05.672 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `user_pet`  WHERE ( `realm` = @realm0 )  | 参数: [@realm0=擎日挽月]
[2025-07-31 15:20:05.722 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `user_pet`  WHERE ( `realm` = @realm0 ) 
[2025-07-31 15:20:05.724 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `user_pet`  WHERE ( `realm` = @realm0 )  | 参数: [@realm0=道满根归]
[2025-07-31 15:20:05.779 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `user_pet`  WHERE ( `realm` = @realm0 ) 
[2025-07-31 15:20:05.782 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `user_pet`  WHERE ( `realm` = @realm0 )  | 参数: [@realm0=不堕轮回]
[2025-07-31 15:20:05.820 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `user_pet`  WHERE ( `realm` = @realm0 ) 
[2025-07-31 15:20:21.882 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 15:20:22.033 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 15:20:22.079 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:20:22.119 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:20:22.120 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 15:20:22.212 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 15:20:22.214 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 15:20:22.216 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:20:22.254 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:20:22.256 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 15:20:22.260 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `user`  
[2025-07-31 15:20:22.298 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `user`  
[2025-07-31 15:20:22.303 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `user`  WHERE ( `create_time` >= @create_time0 )  | 参数: [@create_time0=2025/7/31 0:00:00]
[2025-07-31 15:20:22.347 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `user`  WHERE ( `create_time` >= @create_time0 ) 
[2025-07-31 15:20:22.351 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `user`  WHERE ( `vip_level` > @vip_level0 )  | 参数: [@vip_level0=0]
[2025-07-31 15:20:22.386 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `user`  WHERE ( `vip_level` > @vip_level0 ) 
[2025-07-31 15:20:22.390 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT SUM(IFNULL(`gold`,@MethodConst0)) FROM `user`   | 参数: [@MethodConst0=0]
[2025-07-31 15:20:22.424 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT SUM(IFNULL(`gold`,@MethodConst0)) FROM `user`  
[2025-07-31 15:20:22.437 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 15:20:22.439 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 15:20:22.441 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:20:22.479 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:20:22.481 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 15:20:22.489 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `user`  
[2025-07-31 15:20:22.524 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `user`  
[2025-07-31 15:20:22.526 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`username`,`password`,`nickname`,`sex`,`vip_level`,`vip_score`,`gold`,`yuanbao`,`crystal`,`reg_time`,`title`,`main_pet_id`,`main_pet_name`,`create_time`,`evolution_cd`,`synthesis_cd`,`nirvana_cd`,`money`,`supreme_vip`,`prop_capacity`,`pasture_capacity`,`dragon_ball_exp`,`dragon_ball_level`,`status`,`prestige`,`auto_battle_count`,`auto_nirvana_count`,`monster_count`,`next_battle`,`open_maps`,`star_vip`,`star_vip_expire`,`task_helper_enabled`,`title_attributes`,`soul_pet`,`hell_floor`,`tower_floor` FROM `user`    ORDER BY `create_time` DESC LIMIT 0,15
[2025-07-31 15:20:22.563 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`username`,`password`,`nickname`,`sex`,`vip_level`,`vip_score`,`gold`,`yuanbao`,`crystal`,`reg_time`,`title`,`main_pet_id`,`main_pet_name`,`create_time`,`evolution_cd`,`synthesis_cd`,`nirvana_cd`,`money`,`supreme_vip`,`prop_capacity`,`pasture_capacity`,`dragon_ball_exp`,`dragon_ball_level`,`status`,`prestige`,`auto_battle_count`,`auto_nirvana_count`,`monster_count`,`next_battle`,`open_maps`,`star_vip`,`star_vip_expire`,`task_helper_enabled`,`title_attributes`,`soul_pet`,`hell_floor`,`tower_floor` FROM `user`    ORDER BY `create_time` DESC LIMIT 0,15
[2025-07-31 15:29:58.942 +08:00 INF] BMS: 🚀 应用程序启动，日志系统已配置
[2025-07-31 15:29:58.966 +08:00 DBG] BMS: 🔧 调试级别日志测试
[2025-07-31 15:29:58.981 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 15:29:58.983 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 15:29:59.064 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:29:59.584 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:29:59.593 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 15:29:59.594 +08:00 INF] BMS: ✅ DbContext 初始化成功，日志文件位置: logs/
[2025-07-31 15:30:01.573 +08:00 WRN] Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware: Failed to determine the https port for redirect.
[2025-07-31 15:30:04.955 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 15:30:04.980 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 15:30:05.022 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:30:05.209 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:30:05.254 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 15:30:05.321 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  DISTINCT `type` FROM `item_config`  WHERE NOT( `type` IS NULL   OR `type`='') 
[2025-07-31 15:30:05.370 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  DISTINCT `type` FROM `item_config`  WHERE NOT( `type` IS NULL   OR `type`='') 
[2025-07-31 15:30:05.395 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  DISTINCT `quality` FROM `item_config`  WHERE NOT( `quality` IS NULL   OR `quality`='') 
[2025-07-31 15:30:05.440 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  DISTINCT `quality` FROM `item_config`  WHERE NOT( `quality` IS NULL   OR `quality`='') 
[2025-07-31 15:30:05.583 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 15:30:05.585 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 15:30:05.595 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:30:05.648 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:30:05.652 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 15:30:06.043 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-07-31 15:30:06.277 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-07-31 15:30:06.287 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 0,10
[2025-07-31 15:30:07.018 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 0,10
[2025-07-31 15:30:08.786 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 15:30:08.788 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 15:30:08.791 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:30:08.845 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:30:08.848 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 15:30:08.869 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=884]
[2025-07-31 15:30:08.926 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-07-31 15:30:08.944 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1 | 参数: [@item_no0=2]
[2025-07-31 15:30:08.991 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1
[2025-07-31 15:30:16.514 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 15:30:16.518 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 15:30:16.523 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:30:16.596 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:30:16.599 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 15:30:16.625 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=884]
[2025-07-31 15:30:16.674 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-07-31 15:30:16.682 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT 1 FROM `item_config`   WHERE ( `item_no` = @item_no0 )  AND ( `id` <> @id1 )   LIMIT 0,1 | 参数: [@item_no0=2, @id1=884]
[2025-07-31 15:30:16.737 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT 1 FROM `item_config`   WHERE ( `item_no` = @item_no0 )  AND ( `id` <> @id1 )   LIMIT 0,1
[2025-07-31 15:30:17.064 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 15:30:17.065 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 15:30:17.068 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:30:17.123 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:30:17.126 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 15:30:17.129 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-07-31 15:30:17.368 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-07-31 15:30:17.376 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 0,10
[2025-07-31 15:30:18.121 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 0,10
[2025-07-31 15:30:21.463 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 15:30:21.465 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 15:30:21.468 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:30:21.514 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:30:21.515 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 15:30:21.516 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=884]
[2025-07-31 15:30:21.571 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-07-31 15:30:21.577 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1 | 参数: [@item_no0=2]
[2025-07-31 15:30:21.635 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1
[2025-07-31 15:30:26.955 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 15:30:26.957 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 15:30:26.960 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:30:27.007 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:30:27.009 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 15:30:27.012 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=884]
[2025-07-31 15:30:27.059 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-07-31 15:30:27.063 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT 1 FROM `item_config`   WHERE ( `item_no` = @item_no0 )  AND ( `id` <> @id1 )   LIMIT 0,1 | 参数: [@item_no0=2, @id1=884]
[2025-07-31 15:30:27.108 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT 1 FROM `item_config`   WHERE ( `item_no` = @item_no0 )  AND ( `id` <> @id1 )   LIMIT 0,1
[2025-07-31 15:30:27.288 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 15:30:27.444 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 15:30:27.466 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:30:27.555 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:30:27.565 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 15:30:27.573 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-07-31 15:30:27.812 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-07-31 15:30:27.816 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 0,10
[2025-07-31 15:30:28.548 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 0,10
[2025-07-31 15:30:39.745 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 15:30:39.747 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 15:30:39.751 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:30:39.801 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:30:39.803 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 15:30:39.808 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=884]
[2025-07-31 15:30:39.857 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-07-31 15:30:39.861 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1 | 参数: [@item_no0=2]
[2025-07-31 15:30:39.914 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1
[2025-07-31 15:30:45.944 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 15:30:45.946 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 15:30:45.949 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:30:46.010 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:30:46.012 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 15:30:46.017 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=884]
[2025-07-31 15:30:46.065 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-07-31 15:30:46.068 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT 1 FROM `item_config`   WHERE ( `item_no` = @item_no0 )  AND ( `id` <> @id1 )   LIMIT 0,1 | 参数: [@item_no0=2, @id1=884]
[2025-07-31 15:30:46.119 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT 1 FROM `item_config`   WHERE ( `item_no` = @item_no0 )  AND ( `id` <> @id1 )   LIMIT 0,1
[2025-07-31 15:30:46.252 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 15:30:46.254 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 15:30:46.256 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:30:46.301 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:30:46.303 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 15:30:46.306 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-07-31 15:30:46.550 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-07-31 15:30:46.553 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 0,10
[2025-07-31 15:30:47.289 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 0,10
[2025-07-31 15:37:24.770 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 15:37:24.793 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 15:37:24.819 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:37:25.283 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:37:25.284 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 15:37:25.294 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `equip_type_id` AS `EquipTypeId` , `type_name` AS `TypeName`  FROM `equipment_type` ORDER BY `id` ASC 
[2025-07-31 15:37:25.342 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `equip_type_id` AS `EquipTypeId` , `type_name` AS `TypeName`  FROM `equipment_type` ORDER BY `id` ASC 
[2025-07-31 15:37:25.476 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 15:37:25.478 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 15:37:25.479 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:37:25.531 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:37:25.532 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 15:37:25.556 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  
[2025-07-31 15:37:25.597 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  
[2025-07-31 15:37:25.609 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment_detail`  
[2025-07-31 15:37:25.677 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment_detail`  
[2025-07-31 15:37:25.680 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment_type`  
[2025-07-31 15:37:25.738 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment_type`  
[2025-07-31 15:37:25.754 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )   
[2025-07-31 15:37:25.806 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )   
[2025-07-31 15:37:25.825 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `e`.`equip_id` AS `EquipId` , @constant14 AS `ClassId` , `e`.`name` AS `Name` , `e`.`icon` AS `Icon` , `e`.`equip_type_id` AS `EquipTypeId` , `et`.`type_name` AS `EquipTypeName` , `e`.`element` AS `Element` , IFNULL(`e`.`slot`,@MethodConst15) AS `Slot` , IFNULL(`e`.`strengthen_level`,@MethodConst16) AS `StrengthenLevel` , `e`.`suit_id` AS `SuitId` , IFNULL(`ed`.`atk`,@MethodConst17) AS `Atk` , IFNULL(`ed`.`hit`,@MethodConst18) AS `Hit` , IFNULL(`ed`.`def`,@MethodConst19) AS `Def` , IFNULL(`ed`.`spd`,@MethodConst20) AS `Spd` , IFNULL(`ed`.`dodge`,@MethodConst21) AS `Dodge` , IFNULL(`ed`.`hp`,@MethodConst22) AS `Hp` , IFNULL(`ed`.`mp`,@MethodConst23) AS `Mp` , IFNULL(`ed`.`deepen`,@MethodConst24) AS `Deepen` , IFNULL(`ed`.`offset`,@MethodConst25) AS `Offset` , IFNULL(`ed`.`vamp`,@MethodConst26) AS `Vamp` , IFNULL(`ed`.`vamp_mp`,@MethodConst27) AS `VampMp` , `ed`.`description` AS `Description` , `ed`.`main_attr` AS `MainAttr` , `ed`.`main_attr_value` AS `MainAttrValue` , `ed`.`sub_attr` AS `SubAttr` , `ed`.`sub_attr_value` AS `SubAttrValue` , `ed`.`element_limit` AS `ElementLimit` , `ed`.`equip_name` AS `EquipName`  FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )     ORDER BY `e`.`equip_id` ASC LIMIT 0,10 | 参数: [@constant0=, @MethodConst1=0, @MethodConst2=0, @MethodConst3=0, @MethodConst4=0, @MethodConst5=0, @MethodConst6=0, @MethodConst7=0, @MethodConst8=0, @MethodConst9=0, @MethodConst10=0, @MethodConst11=0, @MethodConst12=0, @MethodConst13=0, @constant14=, @MethodConst15=0, @MethodConst16=0, @MethodConst17=0, @MethodConst18=0, @MethodConst19=0, @MethodConst20=0, @MethodConst21=0, @MethodConst22=0, @MethodConst23=0, @MethodConst24=0, @MethodConst25=0, @MethodConst26=0, @MethodConst27=0]
[2025-07-31 15:37:25.886 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `e`.`equip_id` AS `EquipId` , @constant14 AS `ClassId` , `e`.`name` AS `Name` , `e`.`icon` AS `Icon` , `e`.`equip_type_id` AS `EquipTypeId` , `et`.`type_name` AS `EquipTypeName` , `e`.`element` AS `Element` , IFNULL(`e`.`slot`,@MethodConst15) AS `Slot` , IFNULL(`e`.`strengthen_level`,@MethodConst16) AS `StrengthenLevel` , `e`.`suit_id` AS `SuitId` , IFNULL(`ed`.`atk`,@MethodConst17) AS `Atk` , IFNULL(`ed`.`hit`,@MethodConst18) AS `Hit` , IFNULL(`ed`.`def`,@MethodConst19) AS `Def` , IFNULL(`ed`.`spd`,@MethodConst20) AS `Spd` , IFNULL(`ed`.`dodge`,@MethodConst21) AS `Dodge` , IFNULL(`ed`.`hp`,@MethodConst22) AS `Hp` , IFNULL(`ed`.`mp`,@MethodConst23) AS `Mp` , IFNULL(`ed`.`deepen`,@MethodConst24) AS `Deepen` , IFNULL(`ed`.`offset`,@MethodConst25) AS `Offset` , IFNULL(`ed`.`vamp`,@MethodConst26) AS `Vamp` , IFNULL(`ed`.`vamp_mp`,@MethodConst27) AS `VampMp` , `ed`.`description` AS `Description` , `ed`.`main_attr` AS `MainAttr` , `ed`.`main_attr_value` AS `MainAttrValue` , `ed`.`sub_attr` AS `SubAttr` , `ed`.`sub_attr_value` AS `SubAttrValue` , `ed`.`element_limit` AS `ElementLimit` , `ed`.`equip_name` AS `EquipName`  FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )     ORDER BY `e`.`equip_id` ASC LIMIT 0,10
[2025-07-31 15:37:25.928 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 15:37:25.929 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 15:37:25.931 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:37:25.970 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:37:25.978 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 15:37:25.985 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment_type`  
[2025-07-31 15:37:26.023 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment_type`  
[2025-07-31 15:37:26.028 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `id` AS `Id` , `equip_type_id` AS `EquipTypeId` , `type_name` AS `TypeName` , @constant6 AS `Description` , @constant7 AS `SortOrder` , @constant8 AS `IsActive` , @constant9 AS `CreateTime` , @constant10 AS `UpdateTime` , @constant11 AS `EquipmentCount`  FROM `equipment_type`    ORDER BY `id` ASC LIMIT 0,10 | 参数: [@constant0=, @constant1=0, @constant2=True, @constant3=, @constant4=, @constant5=0, @constant6=, @constant7=0, @constant8=True, @constant9=, @constant10=, @constant11=0]
[2025-07-31 15:37:26.074 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `id` AS `Id` , `equip_type_id` AS `EquipTypeId` , `type_name` AS `TypeName` , @constant6 AS `Description` , @constant7 AS `SortOrder` , @constant8 AS `IsActive` , @constant9 AS `CreateTime` , @constant10 AS `UpdateTime` , @constant11 AS `EquipmentCount`  FROM `equipment_type`    ORDER BY `id` ASC LIMIT 0,10
[2025-07-31 15:37:26.082 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=1]
[2025-07-31 15:37:26.137 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-07-31 15:37:26.144 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=2]
[2025-07-31 15:37:26.193 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-07-31 15:37:26.198 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=3]
[2025-07-31 15:37:26.239 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-07-31 15:37:26.299 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 )  | 参数: [@equip_type_id0=4]
[2025-07-31 15:37:26.346 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_type_id` = @equip_type_id0 ) 
[2025-07-31 15:37:31.171 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 15:37:31.173 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 15:37:31.174 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:37:31.211 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:37:31.213 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 15:37:31.221 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `e`.`equip_id` AS `EquipId` , @constant15 AS `ClassId` , `e`.`name` AS `Name` , `e`.`icon` AS `Icon` , `e`.`equip_type_id` AS `EquipTypeId` , `et`.`type_name` AS `EquipTypeName` , `e`.`element` AS `Element` , IFNULL(`e`.`slot`,@MethodConst16) AS `Slot` , IFNULL(`e`.`strengthen_level`,@MethodConst17) AS `StrengthenLevel` , `e`.`suit_id` AS `SuitId` , IFNULL(`ed`.`atk`,@MethodConst18) AS `Atk` , IFNULL(`ed`.`hit`,@MethodConst19) AS `Hit` , IFNULL(`ed`.`def`,@MethodConst20) AS `Def` , IFNULL(`ed`.`spd`,@MethodConst21) AS `Spd` , IFNULL(`ed`.`dodge`,@MethodConst22) AS `Dodge` , IFNULL(`ed`.`hp`,@MethodConst23) AS `Hp` , IFNULL(`ed`.`mp`,@MethodConst24) AS `Mp` , IFNULL(`ed`.`deepen`,@MethodConst25) AS `Deepen` , IFNULL(`ed`.`offset`,@MethodConst26) AS `Offset` , IFNULL(`ed`.`vamp`,@MethodConst27) AS `Vamp` , IFNULL(`ed`.`vamp_mp`,@MethodConst28) AS `VampMp` , `ed`.`description` AS `Description` , `ed`.`main_attr` AS `MainAttr` , `ed`.`main_attr_value` AS `MainAttrValue` , `ed`.`sub_attr` AS `SubAttr` , `ed`.`sub_attr_value` AS `SubAttrValue` , `ed`.`element_limit` AS `ElementLimit` , `ed`.`equip_name` AS `EquipName`  FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )    WHERE ( `e`.`equip_id` = @equip_id0 )   LIMIT 0,1 | 参数: [@equip_id0=000101, @constant1=, @MethodConst2=0, @MethodConst3=0, @MethodConst4=0, @MethodConst5=0, @MethodConst6=0, @MethodConst7=0, @MethodConst8=0, @MethodConst9=0, @MethodConst10=0, @MethodConst11=0, @MethodConst12=0, @MethodConst13=0, @MethodConst14=0, @constant15=, @MethodConst16=0, @MethodConst17=0, @MethodConst18=0, @MethodConst19=0, @MethodConst20=0, @MethodConst21=0, @MethodConst22=0, @MethodConst23=0, @MethodConst24=0, @MethodConst25=0, @MethodConst26=0, @MethodConst27=0, @MethodConst28=0]
[2025-07-31 15:37:31.265 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `e`.`equip_id` AS `EquipId` , @constant15 AS `ClassId` , `e`.`name` AS `Name` , `e`.`icon` AS `Icon` , `e`.`equip_type_id` AS `EquipTypeId` , `et`.`type_name` AS `EquipTypeName` , `e`.`element` AS `Element` , IFNULL(`e`.`slot`,@MethodConst16) AS `Slot` , IFNULL(`e`.`strengthen_level`,@MethodConst17) AS `StrengthenLevel` , `e`.`suit_id` AS `SuitId` , IFNULL(`ed`.`atk`,@MethodConst18) AS `Atk` , IFNULL(`ed`.`hit`,@MethodConst19) AS `Hit` , IFNULL(`ed`.`def`,@MethodConst20) AS `Def` , IFNULL(`ed`.`spd`,@MethodConst21) AS `Spd` , IFNULL(`ed`.`dodge`,@MethodConst22) AS `Dodge` , IFNULL(`ed`.`hp`,@MethodConst23) AS `Hp` , IFNULL(`ed`.`mp`,@MethodConst24) AS `Mp` , IFNULL(`ed`.`deepen`,@MethodConst25) AS `Deepen` , IFNULL(`ed`.`offset`,@MethodConst26) AS `Offset` , IFNULL(`ed`.`vamp`,@MethodConst27) AS `Vamp` , IFNULL(`ed`.`vamp_mp`,@MethodConst28) AS `VampMp` , `ed`.`description` AS `Description` , `ed`.`main_attr` AS `MainAttr` , `ed`.`main_attr_value` AS `MainAttrValue` , `ed`.`sub_attr` AS `SubAttr` , `ed`.`sub_attr_value` AS `SubAttrValue` , `ed`.`element_limit` AS `ElementLimit` , `ed`.`equip_name` AS `EquipName`  FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )    WHERE ( `e`.`equip_id` = @equip_id0 )   LIMIT 0,1
[2025-07-31 15:37:34.060 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 15:37:34.065 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 15:37:34.069 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:37:34.110 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:37:34.124 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 15:37:34.146 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_id` = @equip_id0 )  | 参数: [@equip_id0=000101]
[2025-07-31 15:37:34.184 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_id` = @equip_id0 ) 
[2025-07-31 15:37:34.272 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: UPDATE `equipment`  SET
            `name` = @Const0 , `icon` = @Const1 , `equip_type_id` = @Const2 , `element` = @Const3 , `slot` = @Const4 , `strengthen_level` = @Const5 , `suit_id` = @Const6   WHERE ( `equip_id` = @equip_id7 ) | 参数: [@Const0=创世铠甲1, @Const1=210, @Const2=4, @Const3=火, @Const4=0, @Const5=0, @Const6=, @equip_id7=000101, @id=0]
[2025-07-31 15:37:34.291 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: UPDATE `equipment`  SET
            `name` = @Const0 , `icon` = @Const1 , `equip_type_id` = @Const2 , `element` = @Const3 , `slot` = @Const4 , `strengthen_level` = @Const5 , `suit_id` = @Const6   WHERE ( `equip_id` = @equip_id7 )
[2025-07-31 15:37:34.301 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: UPDATE `equipment_detail`  SET
            `atk` = @Const0 , `hit` = @Const1 , `def` = @Const2 , `spd` = @Const3 , `dodge` = @Const4 , `hp` = @Const5 , `mp` = @Const6 , `deepen` = @Const7 , `offset` = @Const8 , `vamp` = @Const9 , `vamp_mp` = @Const10 , `equip_type_id` = @Const11 , `description` = @Const12 , `main_attr` = @Const13 , `element_limit` = @Const17 , `equip_name` = @Const18 , `main_attr_value` = @Const14 , `sub_attr` = @Const15 , `sub_attr_value` = @Const16   WHERE ( `equip_id` = @equip_id19 ) | 参数: [@Const0=100, @Const1=0, @Const2=0, @Const3=0, @Const4=0, @Const5=150, @Const6=0, @Const7=0, @Const8=0, @Const9=0, @Const10=0, @Const11=4, @Const12=创世神兵,据传是由自然女神创造口袋大陆时遗留下来的补天石打造而成的!, @Const13=攻击, @Const14=, @Const15=, @Const16=, @Const17=, @Const18=, @equip_id19=000101, @id=0]
[2025-07-31 15:37:34.321 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: UPDATE `equipment_detail`  SET
            `atk` = @Const0 , `hit` = @Const1 , `def` = @Const2 , `spd` = @Const3 , `dodge` = @Const4 , `hp` = @Const5 , `mp` = @Const6 , `deepen` = @Const7 , `offset` = @Const8 , `vamp` = @Const9 , `vamp_mp` = @Const10 , `equip_type_id` = @Const11 , `description` = @Const12 , `main_attr` = @Const13 , `element_limit` = @Const17 , `equip_name` = @Const18 , `main_attr_value` = @Const14 , `sub_attr` = @Const15 , `sub_attr_value` = @Const16   WHERE ( `equip_id` = @equip_id19 )
[2025-07-31 15:37:35.493 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 15:37:35.495 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 15:37:35.498 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:37:35.543 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:37:35.548 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 15:37:35.551 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  
[2025-07-31 15:37:35.592 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  
[2025-07-31 15:37:35.597 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment_detail`  
[2025-07-31 15:37:35.633 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment_detail`  
[2025-07-31 15:37:35.676 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment_type`  
[2025-07-31 15:37:35.713 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment_type`  
[2025-07-31 15:37:35.724 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )   
[2025-07-31 15:37:35.785 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )   
[2025-07-31 15:37:35.818 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `e`.`equip_id` AS `EquipId` , @constant14 AS `ClassId` , `e`.`name` AS `Name` , `e`.`icon` AS `Icon` , `e`.`equip_type_id` AS `EquipTypeId` , `et`.`type_name` AS `EquipTypeName` , `e`.`element` AS `Element` , IFNULL(`e`.`slot`,@MethodConst15) AS `Slot` , IFNULL(`e`.`strengthen_level`,@MethodConst16) AS `StrengthenLevel` , `e`.`suit_id` AS `SuitId` , IFNULL(`ed`.`atk`,@MethodConst17) AS `Atk` , IFNULL(`ed`.`hit`,@MethodConst18) AS `Hit` , IFNULL(`ed`.`def`,@MethodConst19) AS `Def` , IFNULL(`ed`.`spd`,@MethodConst20) AS `Spd` , IFNULL(`ed`.`dodge`,@MethodConst21) AS `Dodge` , IFNULL(`ed`.`hp`,@MethodConst22) AS `Hp` , IFNULL(`ed`.`mp`,@MethodConst23) AS `Mp` , IFNULL(`ed`.`deepen`,@MethodConst24) AS `Deepen` , IFNULL(`ed`.`offset`,@MethodConst25) AS `Offset` , IFNULL(`ed`.`vamp`,@MethodConst26) AS `Vamp` , IFNULL(`ed`.`vamp_mp`,@MethodConst27) AS `VampMp` , `ed`.`description` AS `Description` , `ed`.`main_attr` AS `MainAttr` , `ed`.`main_attr_value` AS `MainAttrValue` , `ed`.`sub_attr` AS `SubAttr` , `ed`.`sub_attr_value` AS `SubAttrValue` , `ed`.`element_limit` AS `ElementLimit` , `ed`.`equip_name` AS `EquipName`  FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )     ORDER BY `e`.`equip_id` ASC LIMIT 0,10 | 参数: [@constant0=, @MethodConst1=0, @MethodConst2=0, @MethodConst3=0, @MethodConst4=0, @MethodConst5=0, @MethodConst6=0, @MethodConst7=0, @MethodConst8=0, @MethodConst9=0, @MethodConst10=0, @MethodConst11=0, @MethodConst12=0, @MethodConst13=0, @constant14=, @MethodConst15=0, @MethodConst16=0, @MethodConst17=0, @MethodConst18=0, @MethodConst19=0, @MethodConst20=0, @MethodConst21=0, @MethodConst22=0, @MethodConst23=0, @MethodConst24=0, @MethodConst25=0, @MethodConst26=0, @MethodConst27=0]
[2025-07-31 15:37:35.992 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `e`.`equip_id` AS `EquipId` , @constant14 AS `ClassId` , `e`.`name` AS `Name` , `e`.`icon` AS `Icon` , `e`.`equip_type_id` AS `EquipTypeId` , `et`.`type_name` AS `EquipTypeName` , `e`.`element` AS `Element` , IFNULL(`e`.`slot`,@MethodConst15) AS `Slot` , IFNULL(`e`.`strengthen_level`,@MethodConst16) AS `StrengthenLevel` , `e`.`suit_id` AS `SuitId` , IFNULL(`ed`.`atk`,@MethodConst17) AS `Atk` , IFNULL(`ed`.`hit`,@MethodConst18) AS `Hit` , IFNULL(`ed`.`def`,@MethodConst19) AS `Def` , IFNULL(`ed`.`spd`,@MethodConst20) AS `Spd` , IFNULL(`ed`.`dodge`,@MethodConst21) AS `Dodge` , IFNULL(`ed`.`hp`,@MethodConst22) AS `Hp` , IFNULL(`ed`.`mp`,@MethodConst23) AS `Mp` , IFNULL(`ed`.`deepen`,@MethodConst24) AS `Deepen` , IFNULL(`ed`.`offset`,@MethodConst25) AS `Offset` , IFNULL(`ed`.`vamp`,@MethodConst26) AS `Vamp` , IFNULL(`ed`.`vamp_mp`,@MethodConst27) AS `VampMp` , `ed`.`description` AS `Description` , `ed`.`main_attr` AS `MainAttr` , `ed`.`main_attr_value` AS `MainAttrValue` , `ed`.`sub_attr` AS `SubAttr` , `ed`.`sub_attr_value` AS `SubAttrValue` , `ed`.`element_limit` AS `ElementLimit` , `ed`.`equip_name` AS `EquipName`  FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )     ORDER BY `e`.`equip_id` ASC LIMIT 0,10
[2025-07-31 15:37:39.392 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 15:37:39.394 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 15:37:39.395 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:37:39.435 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:37:39.436 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 15:37:39.438 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `e`.`equip_id` AS `EquipId` , @constant15 AS `ClassId` , `e`.`name` AS `Name` , `e`.`icon` AS `Icon` , `e`.`equip_type_id` AS `EquipTypeId` , `et`.`type_name` AS `EquipTypeName` , `e`.`element` AS `Element` , IFNULL(`e`.`slot`,@MethodConst16) AS `Slot` , IFNULL(`e`.`strengthen_level`,@MethodConst17) AS `StrengthenLevel` , `e`.`suit_id` AS `SuitId` , IFNULL(`ed`.`atk`,@MethodConst18) AS `Atk` , IFNULL(`ed`.`hit`,@MethodConst19) AS `Hit` , IFNULL(`ed`.`def`,@MethodConst20) AS `Def` , IFNULL(`ed`.`spd`,@MethodConst21) AS `Spd` , IFNULL(`ed`.`dodge`,@MethodConst22) AS `Dodge` , IFNULL(`ed`.`hp`,@MethodConst23) AS `Hp` , IFNULL(`ed`.`mp`,@MethodConst24) AS `Mp` , IFNULL(`ed`.`deepen`,@MethodConst25) AS `Deepen` , IFNULL(`ed`.`offset`,@MethodConst26) AS `Offset` , IFNULL(`ed`.`vamp`,@MethodConst27) AS `Vamp` , IFNULL(`ed`.`vamp_mp`,@MethodConst28) AS `VampMp` , `ed`.`description` AS `Description` , `ed`.`main_attr` AS `MainAttr` , `ed`.`main_attr_value` AS `MainAttrValue` , `ed`.`sub_attr` AS `SubAttr` , `ed`.`sub_attr_value` AS `SubAttrValue` , `ed`.`element_limit` AS `ElementLimit` , `ed`.`equip_name` AS `EquipName`  FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )    WHERE ( `e`.`equip_id` = @equip_id0 )   LIMIT 0,1 | 参数: [@equip_id0=000101, @constant1=, @MethodConst2=0, @MethodConst3=0, @MethodConst4=0, @MethodConst5=0, @MethodConst6=0, @MethodConst7=0, @MethodConst8=0, @MethodConst9=0, @MethodConst10=0, @MethodConst11=0, @MethodConst12=0, @MethodConst13=0, @MethodConst14=0, @constant15=, @MethodConst16=0, @MethodConst17=0, @MethodConst18=0, @MethodConst19=0, @MethodConst20=0, @MethodConst21=0, @MethodConst22=0, @MethodConst23=0, @MethodConst24=0, @MethodConst25=0, @MethodConst26=0, @MethodConst27=0, @MethodConst28=0]
[2025-07-31 15:37:39.476 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `e`.`equip_id` AS `EquipId` , @constant15 AS `ClassId` , `e`.`name` AS `Name` , `e`.`icon` AS `Icon` , `e`.`equip_type_id` AS `EquipTypeId` , `et`.`type_name` AS `EquipTypeName` , `e`.`element` AS `Element` , IFNULL(`e`.`slot`,@MethodConst16) AS `Slot` , IFNULL(`e`.`strengthen_level`,@MethodConst17) AS `StrengthenLevel` , `e`.`suit_id` AS `SuitId` , IFNULL(`ed`.`atk`,@MethodConst18) AS `Atk` , IFNULL(`ed`.`hit`,@MethodConst19) AS `Hit` , IFNULL(`ed`.`def`,@MethodConst20) AS `Def` , IFNULL(`ed`.`spd`,@MethodConst21) AS `Spd` , IFNULL(`ed`.`dodge`,@MethodConst22) AS `Dodge` , IFNULL(`ed`.`hp`,@MethodConst23) AS `Hp` , IFNULL(`ed`.`mp`,@MethodConst24) AS `Mp` , IFNULL(`ed`.`deepen`,@MethodConst25) AS `Deepen` , IFNULL(`ed`.`offset`,@MethodConst26) AS `Offset` , IFNULL(`ed`.`vamp`,@MethodConst27) AS `Vamp` , IFNULL(`ed`.`vamp_mp`,@MethodConst28) AS `VampMp` , `ed`.`description` AS `Description` , `ed`.`main_attr` AS `MainAttr` , `ed`.`main_attr_value` AS `MainAttrValue` , `ed`.`sub_attr` AS `SubAttr` , `ed`.`sub_attr_value` AS `SubAttrValue` , `ed`.`element_limit` AS `ElementLimit` , `ed`.`equip_name` AS `EquipName`  FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )    WHERE ( `e`.`equip_id` = @equip_id0 )   LIMIT 0,1
[2025-07-31 15:37:42.726 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 15:37:42.732 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 15:37:42.739 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:37:42.780 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:37:42.785 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 15:37:42.787 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_id` = @equip_id0 )  | 参数: [@equip_id0=000101]
[2025-07-31 15:37:42.825 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  WHERE ( `equip_id` = @equip_id0 ) 
[2025-07-31 15:37:42.881 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: UPDATE `equipment`  SET
            `name` = @Const0 , `icon` = @Const1 , `equip_type_id` = @Const2 , `element` = @Const3 , `slot` = @Const4 , `strengthen_level` = @Const5 , `suit_id` = @Const6   WHERE ( `equip_id` = @equip_id7 ) | 参数: [@Const0=创世铠甲, @Const1=210, @Const2=4, @Const3=火, @Const4=0, @Const5=0, @Const6=, @equip_id7=000101, @id=0]
[2025-07-31 15:37:42.918 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: UPDATE `equipment`  SET
            `name` = @Const0 , `icon` = @Const1 , `equip_type_id` = @Const2 , `element` = @Const3 , `slot` = @Const4 , `strengthen_level` = @Const5 , `suit_id` = @Const6   WHERE ( `equip_id` = @equip_id7 )
[2025-07-31 15:37:42.928 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: UPDATE `equipment_detail`  SET
            `atk` = @Const0 , `hit` = @Const1 , `def` = @Const2 , `spd` = @Const3 , `dodge` = @Const4 , `hp` = @Const5 , `mp` = @Const6 , `deepen` = @Const7 , `offset` = @Const8 , `vamp` = @Const9 , `vamp_mp` = @Const10 , `equip_type_id` = @Const11 , `description` = @Const12 , `main_attr` = @Const13 , `element_limit` = @Const17 , `equip_name` = @Const18 , `main_attr_value` = @Const14 , `sub_attr` = @Const15 , `sub_attr_value` = @Const16   WHERE ( `equip_id` = @equip_id19 ) | 参数: [@Const0=100, @Const1=0, @Const2=0, @Const3=0, @Const4=0, @Const5=150, @Const6=0, @Const7=0, @Const8=0, @Const9=0, @Const10=0, @Const11=4, @Const12=创世神兵,据传是由自然女神创造口袋大陆时遗留下来的补天石打造而成的!, @Const13=攻击, @Const14=, @Const15=, @Const16=, @Const17=, @Const18=, @equip_id19=000101, @id=0]
[2025-07-31 15:37:42.943 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: UPDATE `equipment_detail`  SET
            `atk` = @Const0 , `hit` = @Const1 , `def` = @Const2 , `spd` = @Const3 , `dodge` = @Const4 , `hp` = @Const5 , `mp` = @Const6 , `deepen` = @Const7 , `offset` = @Const8 , `vamp` = @Const9 , `vamp_mp` = @Const10 , `equip_type_id` = @Const11 , `description` = @Const12 , `main_attr` = @Const13 , `element_limit` = @Const17 , `equip_name` = @Const18 , `main_attr_value` = @Const14 , `sub_attr` = @Const15 , `sub_attr_value` = @Const16   WHERE ( `equip_id` = @equip_id19 )
[2025-07-31 15:37:44.514 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 15:37:44.516 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 15:37:44.519 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:37:44.565 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:37:44.567 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 15:37:44.570 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  
[2025-07-31 15:37:44.610 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  
[2025-07-31 15:37:44.615 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment_detail`  
[2025-07-31 15:37:44.670 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment_detail`  
[2025-07-31 15:37:44.673 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment_type`  
[2025-07-31 15:37:44.715 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment_type`  
[2025-07-31 15:37:44.719 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )   
[2025-07-31 15:37:44.779 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )   
[2025-07-31 15:37:44.783 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `e`.`equip_id` AS `EquipId` , @constant14 AS `ClassId` , `e`.`name` AS `Name` , `e`.`icon` AS `Icon` , `e`.`equip_type_id` AS `EquipTypeId` , `et`.`type_name` AS `EquipTypeName` , `e`.`element` AS `Element` , IFNULL(`e`.`slot`,@MethodConst15) AS `Slot` , IFNULL(`e`.`strengthen_level`,@MethodConst16) AS `StrengthenLevel` , `e`.`suit_id` AS `SuitId` , IFNULL(`ed`.`atk`,@MethodConst17) AS `Atk` , IFNULL(`ed`.`hit`,@MethodConst18) AS `Hit` , IFNULL(`ed`.`def`,@MethodConst19) AS `Def` , IFNULL(`ed`.`spd`,@MethodConst20) AS `Spd` , IFNULL(`ed`.`dodge`,@MethodConst21) AS `Dodge` , IFNULL(`ed`.`hp`,@MethodConst22) AS `Hp` , IFNULL(`ed`.`mp`,@MethodConst23) AS `Mp` , IFNULL(`ed`.`deepen`,@MethodConst24) AS `Deepen` , IFNULL(`ed`.`offset`,@MethodConst25) AS `Offset` , IFNULL(`ed`.`vamp`,@MethodConst26) AS `Vamp` , IFNULL(`ed`.`vamp_mp`,@MethodConst27) AS `VampMp` , `ed`.`description` AS `Description` , `ed`.`main_attr` AS `MainAttr` , `ed`.`main_attr_value` AS `MainAttrValue` , `ed`.`sub_attr` AS `SubAttr` , `ed`.`sub_attr_value` AS `SubAttrValue` , `ed`.`element_limit` AS `ElementLimit` , `ed`.`equip_name` AS `EquipName`  FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )     ORDER BY `e`.`equip_id` ASC LIMIT 0,10 | 参数: [@constant0=, @MethodConst1=0, @MethodConst2=0, @MethodConst3=0, @MethodConst4=0, @MethodConst5=0, @MethodConst6=0, @MethodConst7=0, @MethodConst8=0, @MethodConst9=0, @MethodConst10=0, @MethodConst11=0, @MethodConst12=0, @MethodConst13=0, @constant14=, @MethodConst15=0, @MethodConst16=0, @MethodConst17=0, @MethodConst18=0, @MethodConst19=0, @MethodConst20=0, @MethodConst21=0, @MethodConst22=0, @MethodConst23=0, @MethodConst24=0, @MethodConst25=0, @MethodConst26=0, @MethodConst27=0]
[2025-07-31 15:37:44.843 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `e`.`equip_id` AS `EquipId` , @constant14 AS `ClassId` , `e`.`name` AS `Name` , `e`.`icon` AS `Icon` , `e`.`equip_type_id` AS `EquipTypeId` , `et`.`type_name` AS `EquipTypeName` , `e`.`element` AS `Element` , IFNULL(`e`.`slot`,@MethodConst15) AS `Slot` , IFNULL(`e`.`strengthen_level`,@MethodConst16) AS `StrengthenLevel` , `e`.`suit_id` AS `SuitId` , IFNULL(`ed`.`atk`,@MethodConst17) AS `Atk` , IFNULL(`ed`.`hit`,@MethodConst18) AS `Hit` , IFNULL(`ed`.`def`,@MethodConst19) AS `Def` , IFNULL(`ed`.`spd`,@MethodConst20) AS `Spd` , IFNULL(`ed`.`dodge`,@MethodConst21) AS `Dodge` , IFNULL(`ed`.`hp`,@MethodConst22) AS `Hp` , IFNULL(`ed`.`mp`,@MethodConst23) AS `Mp` , IFNULL(`ed`.`deepen`,@MethodConst24) AS `Deepen` , IFNULL(`ed`.`offset`,@MethodConst25) AS `Offset` , IFNULL(`ed`.`vamp`,@MethodConst26) AS `Vamp` , IFNULL(`ed`.`vamp_mp`,@MethodConst27) AS `VampMp` , `ed`.`description` AS `Description` , `ed`.`main_attr` AS `MainAttr` , `ed`.`main_attr_value` AS `MainAttrValue` , `ed`.`sub_attr` AS `SubAttr` , `ed`.`sub_attr_value` AS `SubAttrValue` , `ed`.`element_limit` AS `ElementLimit` , `ed`.`equip_name` AS `EquipName`  FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )     ORDER BY `e`.`equip_id` ASC LIMIT 0,10
[2025-07-31 15:37:46.904 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 15:37:46.907 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 15:37:46.911 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:37:46.953 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:37:46.955 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 15:37:46.958 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  
[2025-07-31 15:37:46.999 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  
[2025-07-31 15:37:47.003 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment_detail`  
[2025-07-31 15:37:47.043 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment_detail`  
[2025-07-31 15:37:47.051 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment_type`  
[2025-07-31 15:37:47.095 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment_type`  
[2025-07-31 15:37:47.101 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )   
[2025-07-31 15:37:47.148 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )   
[2025-07-31 15:37:47.153 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `e`.`equip_id` AS `EquipId` , @constant14 AS `ClassId` , `e`.`name` AS `Name` , `e`.`icon` AS `Icon` , `e`.`equip_type_id` AS `EquipTypeId` , `et`.`type_name` AS `EquipTypeName` , `e`.`element` AS `Element` , IFNULL(`e`.`slot`,@MethodConst15) AS `Slot` , IFNULL(`e`.`strengthen_level`,@MethodConst16) AS `StrengthenLevel` , `e`.`suit_id` AS `SuitId` , IFNULL(`ed`.`atk`,@MethodConst17) AS `Atk` , IFNULL(`ed`.`hit`,@MethodConst18) AS `Hit` , IFNULL(`ed`.`def`,@MethodConst19) AS `Def` , IFNULL(`ed`.`spd`,@MethodConst20) AS `Spd` , IFNULL(`ed`.`dodge`,@MethodConst21) AS `Dodge` , IFNULL(`ed`.`hp`,@MethodConst22) AS `Hp` , IFNULL(`ed`.`mp`,@MethodConst23) AS `Mp` , IFNULL(`ed`.`deepen`,@MethodConst24) AS `Deepen` , IFNULL(`ed`.`offset`,@MethodConst25) AS `Offset` , IFNULL(`ed`.`vamp`,@MethodConst26) AS `Vamp` , IFNULL(`ed`.`vamp_mp`,@MethodConst27) AS `VampMp` , `ed`.`description` AS `Description` , `ed`.`main_attr` AS `MainAttr` , `ed`.`main_attr_value` AS `MainAttrValue` , `ed`.`sub_attr` AS `SubAttr` , `ed`.`sub_attr_value` AS `SubAttrValue` , `ed`.`element_limit` AS `ElementLimit` , `ed`.`equip_name` AS `EquipName`  FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )     ORDER BY `e`.`equip_id` ASC LIMIT 10,10 | 参数: [@constant0=, @MethodConst1=0, @MethodConst2=0, @MethodConst3=0, @MethodConst4=0, @MethodConst5=0, @MethodConst6=0, @MethodConst7=0, @MethodConst8=0, @MethodConst9=0, @MethodConst10=0, @MethodConst11=0, @MethodConst12=0, @MethodConst13=0, @constant14=, @MethodConst15=0, @MethodConst16=0, @MethodConst17=0, @MethodConst18=0, @MethodConst19=0, @MethodConst20=0, @MethodConst21=0, @MethodConst22=0, @MethodConst23=0, @MethodConst24=0, @MethodConst25=0, @MethodConst26=0, @MethodConst27=0]
[2025-07-31 15:37:47.211 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `e`.`equip_id` AS `EquipId` , @constant14 AS `ClassId` , `e`.`name` AS `Name` , `e`.`icon` AS `Icon` , `e`.`equip_type_id` AS `EquipTypeId` , `et`.`type_name` AS `EquipTypeName` , `e`.`element` AS `Element` , IFNULL(`e`.`slot`,@MethodConst15) AS `Slot` , IFNULL(`e`.`strengthen_level`,@MethodConst16) AS `StrengthenLevel` , `e`.`suit_id` AS `SuitId` , IFNULL(`ed`.`atk`,@MethodConst17) AS `Atk` , IFNULL(`ed`.`hit`,@MethodConst18) AS `Hit` , IFNULL(`ed`.`def`,@MethodConst19) AS `Def` , IFNULL(`ed`.`spd`,@MethodConst20) AS `Spd` , IFNULL(`ed`.`dodge`,@MethodConst21) AS `Dodge` , IFNULL(`ed`.`hp`,@MethodConst22) AS `Hp` , IFNULL(`ed`.`mp`,@MethodConst23) AS `Mp` , IFNULL(`ed`.`deepen`,@MethodConst24) AS `Deepen` , IFNULL(`ed`.`offset`,@MethodConst25) AS `Offset` , IFNULL(`ed`.`vamp`,@MethodConst26) AS `Vamp` , IFNULL(`ed`.`vamp_mp`,@MethodConst27) AS `VampMp` , `ed`.`description` AS `Description` , `ed`.`main_attr` AS `MainAttr` , `ed`.`main_attr_value` AS `MainAttrValue` , `ed`.`sub_attr` AS `SubAttr` , `ed`.`sub_attr_value` AS `SubAttrValue` , `ed`.`element_limit` AS `ElementLimit` , `ed`.`equip_name` AS `EquipName`  FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )     ORDER BY `e`.`equip_id` ASC LIMIT 10,10
[2025-07-31 15:37:48.455 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 15:37:48.456 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 15:37:48.458 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:37:48.495 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:37:48.498 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 15:37:48.501 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  
[2025-07-31 15:37:48.539 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  
[2025-07-31 15:37:48.544 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment_detail`  
[2025-07-31 15:37:48.580 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment_detail`  
[2025-07-31 15:37:48.584 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment_type`  
[2025-07-31 15:37:48.620 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment_type`  
[2025-07-31 15:37:48.624 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )   
[2025-07-31 15:37:48.666 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )   
[2025-07-31 15:37:48.670 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `e`.`equip_id` AS `EquipId` , @constant14 AS `ClassId` , `e`.`name` AS `Name` , `e`.`icon` AS `Icon` , `e`.`equip_type_id` AS `EquipTypeId` , `et`.`type_name` AS `EquipTypeName` , `e`.`element` AS `Element` , IFNULL(`e`.`slot`,@MethodConst15) AS `Slot` , IFNULL(`e`.`strengthen_level`,@MethodConst16) AS `StrengthenLevel` , `e`.`suit_id` AS `SuitId` , IFNULL(`ed`.`atk`,@MethodConst17) AS `Atk` , IFNULL(`ed`.`hit`,@MethodConst18) AS `Hit` , IFNULL(`ed`.`def`,@MethodConst19) AS `Def` , IFNULL(`ed`.`spd`,@MethodConst20) AS `Spd` , IFNULL(`ed`.`dodge`,@MethodConst21) AS `Dodge` , IFNULL(`ed`.`hp`,@MethodConst22) AS `Hp` , IFNULL(`ed`.`mp`,@MethodConst23) AS `Mp` , IFNULL(`ed`.`deepen`,@MethodConst24) AS `Deepen` , IFNULL(`ed`.`offset`,@MethodConst25) AS `Offset` , IFNULL(`ed`.`vamp`,@MethodConst26) AS `Vamp` , IFNULL(`ed`.`vamp_mp`,@MethodConst27) AS `VampMp` , `ed`.`description` AS `Description` , `ed`.`main_attr` AS `MainAttr` , `ed`.`main_attr_value` AS `MainAttrValue` , `ed`.`sub_attr` AS `SubAttr` , `ed`.`sub_attr_value` AS `SubAttrValue` , `ed`.`element_limit` AS `ElementLimit` , `ed`.`equip_name` AS `EquipName`  FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )     ORDER BY `e`.`equip_id` ASC LIMIT 0,10 | 参数: [@constant0=, @MethodConst1=0, @MethodConst2=0, @MethodConst3=0, @MethodConst4=0, @MethodConst5=0, @MethodConst6=0, @MethodConst7=0, @MethodConst8=0, @MethodConst9=0, @MethodConst10=0, @MethodConst11=0, @MethodConst12=0, @MethodConst13=0, @constant14=, @MethodConst15=0, @MethodConst16=0, @MethodConst17=0, @MethodConst18=0, @MethodConst19=0, @MethodConst20=0, @MethodConst21=0, @MethodConst22=0, @MethodConst23=0, @MethodConst24=0, @MethodConst25=0, @MethodConst26=0, @MethodConst27=0]
[2025-07-31 15:37:48.722 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `e`.`equip_id` AS `EquipId` , @constant14 AS `ClassId` , `e`.`name` AS `Name` , `e`.`icon` AS `Icon` , `e`.`equip_type_id` AS `EquipTypeId` , `et`.`type_name` AS `EquipTypeName` , `e`.`element` AS `Element` , IFNULL(`e`.`slot`,@MethodConst15) AS `Slot` , IFNULL(`e`.`strengthen_level`,@MethodConst16) AS `StrengthenLevel` , `e`.`suit_id` AS `SuitId` , IFNULL(`ed`.`atk`,@MethodConst17) AS `Atk` , IFNULL(`ed`.`hit`,@MethodConst18) AS `Hit` , IFNULL(`ed`.`def`,@MethodConst19) AS `Def` , IFNULL(`ed`.`spd`,@MethodConst20) AS `Spd` , IFNULL(`ed`.`dodge`,@MethodConst21) AS `Dodge` , IFNULL(`ed`.`hp`,@MethodConst22) AS `Hp` , IFNULL(`ed`.`mp`,@MethodConst23) AS `Mp` , IFNULL(`ed`.`deepen`,@MethodConst24) AS `Deepen` , IFNULL(`ed`.`offset`,@MethodConst25) AS `Offset` , IFNULL(`ed`.`vamp`,@MethodConst26) AS `Vamp` , IFNULL(`ed`.`vamp_mp`,@MethodConst27) AS `VampMp` , `ed`.`description` AS `Description` , `ed`.`main_attr` AS `MainAttr` , `ed`.`main_attr_value` AS `MainAttrValue` , `ed`.`sub_attr` AS `SubAttr` , `ed`.`sub_attr_value` AS `SubAttrValue` , `ed`.`element_limit` AS `ElementLimit` , `ed`.`equip_name` AS `EquipName`  FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )     ORDER BY `e`.`equip_id` ASC LIMIT 0,10
[2025-07-31 15:38:01.615 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 15:38:01.622 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 15:38:01.625 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:38:01.665 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:38:01.666 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 15:38:01.668 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  
[2025-07-31 15:38:01.709 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  
[2025-07-31 15:38:01.710 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment_detail`  
[2025-07-31 15:38:01.751 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment_detail`  
[2025-07-31 15:38:01.753 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment_type`  
[2025-07-31 15:38:01.793 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment_type`  
[2025-07-31 15:38:01.810 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )   
[2025-07-31 15:38:01.867 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )   
[2025-07-31 15:38:01.877 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `e`.`equip_id` AS `EquipId` , @constant14 AS `ClassId` , `e`.`name` AS `Name` , `e`.`icon` AS `Icon` , `e`.`equip_type_id` AS `EquipTypeId` , `et`.`type_name` AS `EquipTypeName` , `e`.`element` AS `Element` , IFNULL(`e`.`slot`,@MethodConst15) AS `Slot` , IFNULL(`e`.`strengthen_level`,@MethodConst16) AS `StrengthenLevel` , `e`.`suit_id` AS `SuitId` , IFNULL(`ed`.`atk`,@MethodConst17) AS `Atk` , IFNULL(`ed`.`hit`,@MethodConst18) AS `Hit` , IFNULL(`ed`.`def`,@MethodConst19) AS `Def` , IFNULL(`ed`.`spd`,@MethodConst20) AS `Spd` , IFNULL(`ed`.`dodge`,@MethodConst21) AS `Dodge` , IFNULL(`ed`.`hp`,@MethodConst22) AS `Hp` , IFNULL(`ed`.`mp`,@MethodConst23) AS `Mp` , IFNULL(`ed`.`deepen`,@MethodConst24) AS `Deepen` , IFNULL(`ed`.`offset`,@MethodConst25) AS `Offset` , IFNULL(`ed`.`vamp`,@MethodConst26) AS `Vamp` , IFNULL(`ed`.`vamp_mp`,@MethodConst27) AS `VampMp` , `ed`.`description` AS `Description` , `ed`.`main_attr` AS `MainAttr` , `ed`.`main_attr_value` AS `MainAttrValue` , `ed`.`sub_attr` AS `SubAttr` , `ed`.`sub_attr_value` AS `SubAttrValue` , `ed`.`element_limit` AS `ElementLimit` , `ed`.`equip_name` AS `EquipName`  FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )     ORDER BY `e`.`equip_id` ASC LIMIT 10,10 | 参数: [@constant0=, @MethodConst1=0, @MethodConst2=0, @MethodConst3=0, @MethodConst4=0, @MethodConst5=0, @MethodConst6=0, @MethodConst7=0, @MethodConst8=0, @MethodConst9=0, @MethodConst10=0, @MethodConst11=0, @MethodConst12=0, @MethodConst13=0, @constant14=, @MethodConst15=0, @MethodConst16=0, @MethodConst17=0, @MethodConst18=0, @MethodConst19=0, @MethodConst20=0, @MethodConst21=0, @MethodConst22=0, @MethodConst23=0, @MethodConst24=0, @MethodConst25=0, @MethodConst26=0, @MethodConst27=0]
[2025-07-31 15:38:01.945 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `e`.`equip_id` AS `EquipId` , @constant14 AS `ClassId` , `e`.`name` AS `Name` , `e`.`icon` AS `Icon` , `e`.`equip_type_id` AS `EquipTypeId` , `et`.`type_name` AS `EquipTypeName` , `e`.`element` AS `Element` , IFNULL(`e`.`slot`,@MethodConst15) AS `Slot` , IFNULL(`e`.`strengthen_level`,@MethodConst16) AS `StrengthenLevel` , `e`.`suit_id` AS `SuitId` , IFNULL(`ed`.`atk`,@MethodConst17) AS `Atk` , IFNULL(`ed`.`hit`,@MethodConst18) AS `Hit` , IFNULL(`ed`.`def`,@MethodConst19) AS `Def` , IFNULL(`ed`.`spd`,@MethodConst20) AS `Spd` , IFNULL(`ed`.`dodge`,@MethodConst21) AS `Dodge` , IFNULL(`ed`.`hp`,@MethodConst22) AS `Hp` , IFNULL(`ed`.`mp`,@MethodConst23) AS `Mp` , IFNULL(`ed`.`deepen`,@MethodConst24) AS `Deepen` , IFNULL(`ed`.`offset`,@MethodConst25) AS `Offset` , IFNULL(`ed`.`vamp`,@MethodConst26) AS `Vamp` , IFNULL(`ed`.`vamp_mp`,@MethodConst27) AS `VampMp` , `ed`.`description` AS `Description` , `ed`.`main_attr` AS `MainAttr` , `ed`.`main_attr_value` AS `MainAttrValue` , `ed`.`sub_attr` AS `SubAttr` , `ed`.`sub_attr_value` AS `SubAttrValue` , `ed`.`element_limit` AS `ElementLimit` , `ed`.`equip_name` AS `EquipName`  FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )     ORDER BY `e`.`equip_id` ASC LIMIT 10,10
[2025-07-31 15:38:02.918 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 15:38:02.922 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 15:38:02.925 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:38:02.966 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:38:02.970 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 15:38:02.973 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment`  
[2025-07-31 15:38:03.015 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment`  
[2025-07-31 15:38:03.019 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment_detail`  
[2025-07-31 15:38:03.059 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment_detail`  
[2025-07-31 15:38:03.065 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment_type`  
[2025-07-31 15:38:03.107 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment_type`  
[2025-07-31 15:38:03.114 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )   
[2025-07-31 15:38:03.168 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )   
[2025-07-31 15:38:03.172 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `e`.`equip_id` AS `EquipId` , @constant14 AS `ClassId` , `e`.`name` AS `Name` , `e`.`icon` AS `Icon` , `e`.`equip_type_id` AS `EquipTypeId` , `et`.`type_name` AS `EquipTypeName` , `e`.`element` AS `Element` , IFNULL(`e`.`slot`,@MethodConst15) AS `Slot` , IFNULL(`e`.`strengthen_level`,@MethodConst16) AS `StrengthenLevel` , `e`.`suit_id` AS `SuitId` , IFNULL(`ed`.`atk`,@MethodConst17) AS `Atk` , IFNULL(`ed`.`hit`,@MethodConst18) AS `Hit` , IFNULL(`ed`.`def`,@MethodConst19) AS `Def` , IFNULL(`ed`.`spd`,@MethodConst20) AS `Spd` , IFNULL(`ed`.`dodge`,@MethodConst21) AS `Dodge` , IFNULL(`ed`.`hp`,@MethodConst22) AS `Hp` , IFNULL(`ed`.`mp`,@MethodConst23) AS `Mp` , IFNULL(`ed`.`deepen`,@MethodConst24) AS `Deepen` , IFNULL(`ed`.`offset`,@MethodConst25) AS `Offset` , IFNULL(`ed`.`vamp`,@MethodConst26) AS `Vamp` , IFNULL(`ed`.`vamp_mp`,@MethodConst27) AS `VampMp` , `ed`.`description` AS `Description` , `ed`.`main_attr` AS `MainAttr` , `ed`.`main_attr_value` AS `MainAttrValue` , `ed`.`sub_attr` AS `SubAttr` , `ed`.`sub_attr_value` AS `SubAttrValue` , `ed`.`element_limit` AS `ElementLimit` , `ed`.`equip_name` AS `EquipName`  FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )     ORDER BY `e`.`equip_id` ASC LIMIT 0,10 | 参数: [@constant0=, @MethodConst1=0, @MethodConst2=0, @MethodConst3=0, @MethodConst4=0, @MethodConst5=0, @MethodConst6=0, @MethodConst7=0, @MethodConst8=0, @MethodConst9=0, @MethodConst10=0, @MethodConst11=0, @MethodConst12=0, @MethodConst13=0, @constant14=, @MethodConst15=0, @MethodConst16=0, @MethodConst17=0, @MethodConst18=0, @MethodConst19=0, @MethodConst20=0, @MethodConst21=0, @MethodConst22=0, @MethodConst23=0, @MethodConst24=0, @MethodConst25=0, @MethodConst26=0, @MethodConst27=0]
[2025-07-31 15:38:03.226 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `e`.`equip_id` AS `EquipId` , @constant14 AS `ClassId` , `e`.`name` AS `Name` , `e`.`icon` AS `Icon` , `e`.`equip_type_id` AS `EquipTypeId` , `et`.`type_name` AS `EquipTypeName` , `e`.`element` AS `Element` , IFNULL(`e`.`slot`,@MethodConst15) AS `Slot` , IFNULL(`e`.`strengthen_level`,@MethodConst16) AS `StrengthenLevel` , `e`.`suit_id` AS `SuitId` , IFNULL(`ed`.`atk`,@MethodConst17) AS `Atk` , IFNULL(`ed`.`hit`,@MethodConst18) AS `Hit` , IFNULL(`ed`.`def`,@MethodConst19) AS `Def` , IFNULL(`ed`.`spd`,@MethodConst20) AS `Spd` , IFNULL(`ed`.`dodge`,@MethodConst21) AS `Dodge` , IFNULL(`ed`.`hp`,@MethodConst22) AS `Hp` , IFNULL(`ed`.`mp`,@MethodConst23) AS `Mp` , IFNULL(`ed`.`deepen`,@MethodConst24) AS `Deepen` , IFNULL(`ed`.`offset`,@MethodConst25) AS `Offset` , IFNULL(`ed`.`vamp`,@MethodConst26) AS `Vamp` , IFNULL(`ed`.`vamp_mp`,@MethodConst27) AS `VampMp` , `ed`.`description` AS `Description` , `ed`.`main_attr` AS `MainAttr` , `ed`.`main_attr_value` AS `MainAttrValue` , `ed`.`sub_attr` AS `SubAttr` , `ed`.`sub_attr_value` AS `SubAttrValue` , `ed`.`element_limit` AS `ElementLimit` , `ed`.`equip_name` AS `EquipName`  FROM `equipment` `e` Left JOIN `equipment_detail` `ed` ON ( `e`.`equip_id` = `ed`.`equip_id` )  Left JOIN `equipment_type` `et` ON ( `e`.`equip_type_id` = `et`.`equip_type_id` )     ORDER BY `e`.`equip_id` ASC LIMIT 0,10
[2025-07-31 15:38:10.253 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 15:38:10.324 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 15:38:10.494 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:38:10.543 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:38:10.545 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 15:38:10.548 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  DISTINCT `type` FROM `item_config`  WHERE NOT( `type` IS NULL   OR `type`='') 
[2025-07-31 15:38:10.590 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  DISTINCT `type` FROM `item_config`  WHERE NOT( `type` IS NULL   OR `type`='') 
[2025-07-31 15:38:10.593 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  DISTINCT `quality` FROM `item_config`  WHERE NOT( `quality` IS NULL   OR `quality`='') 
[2025-07-31 15:38:10.635 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  DISTINCT `quality` FROM `item_config`  WHERE NOT( `quality` IS NULL   OR `quality`='') 
[2025-07-31 15:38:10.706 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 15:38:10.708 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 15:38:10.713 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:38:10.752 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:38:10.753 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 15:38:10.756 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-07-31 15:38:10.968 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-07-31 15:38:10.974 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 0,10
[2025-07-31 15:38:11.746 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 0,10
[2025-07-31 15:38:14.307 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 15:38:14.310 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 15:38:14.312 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:38:14.353 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:38:14.354 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 15:38:14.356 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=884]
[2025-07-31 15:38:14.436 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-07-31 15:38:14.441 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1 | 参数: [@item_no0=2]
[2025-07-31 15:38:14.483 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1
[2025-07-31 15:38:18.040 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 15:38:18.045 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 15:38:18.048 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:38:18.088 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:38:18.092 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 15:38:18.095 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=884]
[2025-07-31 15:38:18.136 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-07-31 15:38:18.138 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT 1 FROM `item_config`   WHERE ( `item_no` = @item_no0 )  AND ( `id` <> @id1 )   LIMIT 0,1 | 参数: [@item_no0=2, @id1=884]
[2025-07-31 15:38:18.178 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT 1 FROM `item_config`   WHERE ( `item_no` = @item_no0 )  AND ( `id` <> @id1 )   LIMIT 0,1
[2025-07-31 15:38:18.301 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 15:38:18.303 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 15:38:18.305 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:38:18.345 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 15:38:18.347 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 15:38:18.349 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-07-31 15:38:18.552 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-07-31 15:38:18.554 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 0,10
[2025-07-31 15:38:19.257 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 0,10
[2025-07-31 16:01:55.030 +08:00 INF] BMS: 🚀 应用程序启动，日志系统已配置
[2025-07-31 16:01:55.090 +08:00 DBG] BMS: 🔧 调试级别日志测试
[2025-07-31 16:01:55.109 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 16:01:55.112 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 16:01:55.192 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 16:01:55.661 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 16:01:55.675 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 16:01:55.677 +08:00 INF] BMS: ✅ DbContext 初始化成功，日志文件位置: logs/
[2025-07-31 16:01:57.963 +08:00 WRN] Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware: Failed to determine the https port for redirect.
[2025-07-31 16:02:03.679 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 16:02:03.738 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 16:02:03.756 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 16:02:03.806 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 16:02:03.834 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 16:02:04.018 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  DISTINCT `type` FROM `item_config`  WHERE NOT( `type` IS NULL   OR `type`='') 
[2025-07-31 16:02:04.116 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  DISTINCT `type` FROM `item_config`  WHERE NOT( `type` IS NULL   OR `type`='') 
[2025-07-31 16:02:04.159 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  DISTINCT `quality` FROM `item_config`  WHERE NOT( `quality` IS NULL   OR `quality`='') 
[2025-07-31 16:02:04.198 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  DISTINCT `quality` FROM `item_config`  WHERE NOT( `quality` IS NULL   OR `quality`='') 
[2025-07-31 16:02:04.372 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 16:02:04.374 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 16:02:04.379 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 16:02:04.412 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 16:02:04.414 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 16:02:04.614 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-07-31 16:02:04.812 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-07-31 16:02:04.815 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 0,10
[2025-07-31 16:02:05.516 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 0,10
[2025-07-31 16:02:07.591 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 16:02:07.596 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 16:02:07.600 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 16:02:07.642 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 16:02:07.644 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 16:02:07.665 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=884]
[2025-07-31 16:02:07.723 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-07-31 16:02:07.760 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1 | 参数: [@item_no0=2]
[2025-07-31 16:02:07.802 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1
[2025-07-31 16:02:12.862 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 16:02:12.901 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 16:02:12.907 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 16:02:12.954 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 16:02:12.957 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 16:02:12.981 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=884]
[2025-07-31 16:02:13.026 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-07-31 16:02:13.052 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT 1 FROM `item_config`   WHERE ( `item_no` = @item_no0 )  AND ( `id` <> @id1 )   LIMIT 0,1 | 参数: [@item_no0=2, @id1=884]
[2025-07-31 16:02:13.108 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT 1 FROM `item_config`   WHERE ( `item_no` = @item_no0 )  AND ( `id` <> @id1 )   LIMIT 0,1
[2025-07-31 16:02:13.308 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=884]
[2025-07-31 16:02:13.355 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-07-31 16:02:13.358 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1 | 参数: [@item_no0=2]
[2025-07-31 16:02:13.400 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1
[2025-07-31 16:02:13.408 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 16:02:13.409 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 16:02:13.411 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 16:02:13.458 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 16:02:13.459 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 16:02:13.462 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-07-31 16:02:13.696 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-07-31 16:02:13.698 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 0,10
[2025-07-31 16:02:14.404 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 0,10
[2025-07-31 16:02:24.248 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 16:02:24.254 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 16:02:24.257 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 16:02:24.295 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 16:02:24.301 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 16:02:24.303 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=884]
[2025-07-31 16:02:24.340 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-07-31 16:02:24.346 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1 | 参数: [@item_no0=2]
[2025-07-31 16:02:24.386 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1
[2025-07-31 16:02:30.249 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 16:02:30.260 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 16:02:30.267 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 16:02:30.371 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 16:02:30.373 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 16:02:30.376 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=884]
[2025-07-31 16:02:30.415 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-07-31 16:02:30.421 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT 1 FROM `item_config`   WHERE ( `item_no` = @item_no0 )  AND ( `id` <> @id1 )   LIMIT 0,1 | 参数: [@item_no0=2, @id1=884]
[2025-07-31 16:02:30.461 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT 1 FROM `item_config`   WHERE ( `item_no` = @item_no0 )  AND ( `id` <> @id1 )   LIMIT 0,1
[2025-07-31 16:02:30.571 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=884]
[2025-07-31 16:02:30.605 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-07-31 16:02:30.608 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1 | 参数: [@item_no0=2]
[2025-07-31 16:02:30.649 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1
[2025-07-31 16:02:30.658 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 16:02:30.659 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 16:02:30.664 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 16:02:30.700 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 16:02:30.701 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 16:02:30.704 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-07-31 16:02:30.927 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-07-31 16:02:30.931 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 0,10
[2025-07-31 16:02:31.641 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 0,10
[2025-07-31 16:07:27.251 +08:00 INF] BMS: 🚀 应用程序启动，日志系统已配置
[2025-07-31 16:07:27.282 +08:00 DBG] BMS: 🔧 调试级别日志测试
[2025-07-31 16:07:27.307 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 16:07:27.310 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 16:07:27.391 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 16:07:27.873 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 16:07:27.887 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 16:07:27.890 +08:00 INF] BMS: ✅ DbContext 初始化成功，日志文件位置: logs/
[2025-07-31 16:07:29.796 +08:00 WRN] Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware: Failed to determine the https port for redirect.
[2025-07-31 16:07:37.571 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 16:07:37.576 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 16:07:37.582 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 16:07:37.628 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 16:07:37.634 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 16:07:37.692 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  DISTINCT `type` FROM `item_config`  WHERE NOT( `type` IS NULL   OR `type`='') 
[2025-07-31 16:07:37.735 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  DISTINCT `type` FROM `item_config`  WHERE NOT( `type` IS NULL   OR `type`='') 
[2025-07-31 16:07:37.758 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  DISTINCT `quality` FROM `item_config`  WHERE NOT( `quality` IS NULL   OR `quality`='') 
[2025-07-31 16:07:37.799 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  DISTINCT `quality` FROM `item_config`  WHERE NOT( `quality` IS NULL   OR `quality`='') 
[2025-07-31 16:07:37.955 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 16:07:37.981 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 16:07:37.994 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 16:07:38.039 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 16:07:38.153 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 16:07:38.286 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-07-31 16:07:38.505 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-07-31 16:07:38.512 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 0,10
[2025-07-31 16:07:39.231 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 0,10
[2025-07-31 16:07:41.029 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 16:07:41.152 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 16:07:41.154 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 16:07:41.190 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 16:07:41.193 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 16:07:41.204 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`update_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=884]
[2025-07-31 16:07:41.273 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`update_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-07-31 16:07:41.295 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1 | 参数: [@item_no0=2]
[2025-07-31 16:07:41.336 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1
[2025-07-31 16:07:46.874 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 16:07:46.878 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 16:07:46.880 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 16:07:46.935 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 16:07:47.039 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 16:07:47.064 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`update_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=884]
[2025-07-31 16:07:47.107 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`update_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-07-31 16:07:47.119 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT 1 FROM `item_config`   WHERE ( `item_no` = @item_no0 )  AND ( `id` <> @id1 )   LIMIT 0,1 | 参数: [@item_no0=2, @id1=884]
[2025-07-31 16:07:47.157 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT 1 FROM `item_config`   WHERE ( `item_no` = @item_no0 )  AND ( `id` <> @id1 )   LIMIT 0,1
[2025-07-31 16:07:47.317 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`update_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=884]
[2025-07-31 16:07:47.355 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`update_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-07-31 16:07:47.358 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1 | 参数: [@item_no0=2]
[2025-07-31 16:07:47.397 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1
[2025-07-31 16:07:47.403 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 16:07:47.404 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 16:07:47.406 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 16:07:47.447 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 16:07:47.448 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 16:07:47.451 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-07-31 16:07:47.662 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-07-31 16:07:47.664 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 0,10
[2025-07-31 16:07:48.391 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 0,10
[2025-07-31 16:09:42.509 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 16:09:42.513 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 16:09:42.514 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 16:09:42.550 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 16:09:42.551 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 16:09:42.554 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`update_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=884]
[2025-07-31 16:09:42.590 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`update_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-07-31 16:09:42.592 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1 | 参数: [@item_no0=2]
[2025-07-31 16:09:42.632 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1
[2025-07-31 16:09:47.034 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 16:09:47.036 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 16:09:47.037 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 16:09:47.082 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 16:09:47.083 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 16:09:47.085 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`update_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=884]
[2025-07-31 16:09:47.120 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`update_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-07-31 16:09:47.125 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT 1 FROM `item_config`   WHERE ( `item_no` = @item_no0 )  AND ( `id` <> @id1 )   LIMIT 0,1 | 参数: [@item_no0=2, @id1=884]
[2025-07-31 16:09:47.173 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT 1 FROM `item_config`   WHERE ( `item_no` = @item_no0 )  AND ( `id` <> @id1 )   LIMIT 0,1
[2025-07-31 16:09:47.294 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`update_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=884]
[2025-07-31 16:09:47.333 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`update_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-07-31 16:09:47.336 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1 | 参数: [@item_no0=2]
[2025-07-31 16:09:47.395 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1
[2025-07-31 16:09:47.401 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 16:09:47.402 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 16:09:47.409 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 16:09:47.488 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 16:09:47.555 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 16:09:47.559 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-07-31 16:09:47.767 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-07-31 16:09:47.772 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 0,10
[2025-07-31 16:09:48.478 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 0,10
[2025-07-31 16:10:29.160 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 16:10:29.161 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 16:10:29.163 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 16:10:29.201 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 16:10:29.202 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 16:10:29.205 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`update_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=884]
[2025-07-31 16:10:29.268 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`update_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-07-31 16:10:29.273 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1 | 参数: [@item_no0=2]
[2025-07-31 16:10:29.309 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1
[2025-07-31 16:10:34.170 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 16:10:34.339 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 16:10:34.511 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 16:10:34.565 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 16:10:34.569 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 16:10:34.585 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`update_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=884]
[2025-07-31 16:10:34.631 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`update_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-07-31 16:10:34.638 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT 1 FROM `item_config`   WHERE ( `item_no` = @item_no0 )  AND ( `id` <> @id1 )   LIMIT 0,1 | 参数: [@item_no0=2, @id1=884]
[2025-07-31 16:10:34.678 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT 1 FROM `item_config`   WHERE ( `item_no` = @item_no0 )  AND ( `id` <> @id1 )   LIMIT 0,1
[2025-07-31 16:11:02.836 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`update_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=884]
[2025-07-31 16:11:02.899 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`update_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-07-31 16:11:02.905 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1 | 参数: [@item_no0=2]
[2025-07-31 16:11:02.949 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1
[2025-07-31 16:11:02.972 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 16:11:02.975 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 16:11:02.976 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 16:11:03.016 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 16:11:03.018 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 16:11:03.020 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-07-31 16:11:03.239 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-07-31 16:11:03.241 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 0,10
[2025-07-31 16:11:03.945 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 0,10
[2025-07-31 16:11:05.655 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 16:11:05.660 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 16:11:05.665 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 16:11:05.707 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 16:11:05.770 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 16:11:05.777 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`update_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=884]
[2025-07-31 16:11:05.817 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`update_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-07-31 16:11:05.819 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1 | 参数: [@item_no0=2]
[2025-07-31 16:11:05.864 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1
[2025-07-31 16:11:13.513 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 16:11:13.516 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 16:11:13.519 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 16:11:13.571 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 16:11:13.574 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 16:11:13.577 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`update_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=884]
[2025-07-31 16:11:13.631 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`update_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-07-31 16:11:13.633 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT 1 FROM `item_config`   WHERE ( `item_no` = @item_no0 )  AND ( `id` <> @id1 )   LIMIT 0,1 | 参数: [@item_no0=2, @id1=884]
[2025-07-31 16:11:13.677 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT 1 FROM `item_config`   WHERE ( `item_no` = @item_no0 )  AND ( `id` <> @id1 )   LIMIT 0,1
[2025-07-31 16:11:32.104 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`update_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=884]
[2025-07-31 16:11:32.141 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`update_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-07-31 16:11:32.143 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1 | 参数: [@item_no0=2]
[2025-07-31 16:11:32.181 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1
[2025-07-31 16:11:32.187 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 16:11:32.188 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 16:11:32.189 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 16:11:32.232 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 16:11:32.234 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 16:11:32.236 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-07-31 16:11:32.447 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-07-31 16:11:32.486 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 0,10
[2025-07-31 16:11:33.206 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 0,10
[2025-07-31 16:11:34.773 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 16:11:34.775 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 16:11:34.776 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 16:11:34.813 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 16:11:34.814 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 16:11:34.818 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`update_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=884]
[2025-07-31 16:11:34.857 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`update_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-07-31 16:11:34.870 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1 | 参数: [@item_no0=2]
[2025-07-31 16:11:34.910 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1
[2025-07-31 16:11:40.928 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 16:11:40.930 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 16:11:40.933 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 16:11:40.973 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 16:11:40.975 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 16:11:40.978 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`update_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=884]
[2025-07-31 16:11:41.023 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`update_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-07-31 16:11:41.033 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT 1 FROM `item_config`   WHERE ( `item_no` = @item_no0 )  AND ( `id` <> @id1 )   LIMIT 0,1 | 参数: [@item_no0=2, @id1=884]
[2025-07-31 16:11:41.079 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT 1 FROM `item_config`   WHERE ( `item_no` = @item_no0 )  AND ( `id` <> @id1 )   LIMIT 0,1
[2025-07-31 17:17:17.662 +08:00 INF] BMS: 🚀 应用程序启动，日志系统已配置
[2025-07-31 17:17:17.698 +08:00 DBG] BMS: 🔧 调试级别日志测试
[2025-07-31 17:17:17.717 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 17:17:17.739 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 17:17:17.841 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 17:17:18.364 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 17:17:18.454 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 17:17:18.501 +08:00 INF] BMS: ✅ DbContext 初始化成功，日志文件位置: logs/
[2025-07-31 17:17:21.340 +08:00 WRN] Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware: Failed to determine the https port for redirect.
[2025-07-31 17:18:52.754 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 17:18:52.759 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 17:18:52.775 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 17:18:52.822 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 17:18:52.827 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 17:18:52.938 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  DISTINCT `type` FROM `item_config`  WHERE NOT( `type` IS NULL   OR `type`='') 
[2025-07-31 17:18:53.010 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  DISTINCT `type` FROM `item_config`  WHERE NOT( `type` IS NULL   OR `type`='') 
[2025-07-31 17:18:53.089 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  DISTINCT `quality` FROM `item_config`  WHERE NOT( `quality` IS NULL   OR `quality`='') 
[2025-07-31 17:18:53.130 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  DISTINCT `quality` FROM `item_config`  WHERE NOT( `quality` IS NULL   OR `quality`='') 
[2025-07-31 17:18:53.815 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 17:18:53.828 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 17:18:53.840 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 17:18:53.879 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 17:18:53.882 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 17:18:53.887 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  DISTINCT `type` FROM `item_config`  WHERE NOT( `type` IS NULL   OR `type`='') 
[2025-07-31 17:18:53.926 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  DISTINCT `type` FROM `item_config`  WHERE NOT( `type` IS NULL   OR `type`='') 
[2025-07-31 17:18:53.930 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  DISTINCT `quality` FROM `item_config`  WHERE NOT( `quality` IS NULL   OR `quality`='') 
[2025-07-31 17:18:53.972 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  DISTINCT `quality` FROM `item_config`  WHERE NOT( `quality` IS NULL   OR `quality`='') 
[2025-07-31 17:18:54.068 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 17:18:54.070 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 17:18:54.073 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 17:18:54.111 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 17:18:54.114 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 17:18:54.359 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-07-31 17:18:54.594 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-07-31 17:18:54.598 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 0,10
[2025-07-31 17:18:55.320 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 0,10
[2025-07-31 17:18:58.398 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 17:18:58.400 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 17:18:58.403 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 17:18:58.444 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 17:18:58.447 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 17:18:58.466 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`update_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=884]
[2025-07-31 17:18:58.523 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`update_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-07-31 17:18:58.562 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1 | 参数: [@item_no0=2]
[2025-07-31 17:18:58.603 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1
[2025-07-31 17:19:10.472 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 17:19:10.475 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 17:19:10.477 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 17:19:10.526 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 17:19:10.527 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 17:19:10.552 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`update_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=884]
[2025-07-31 17:19:10.589 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`update_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-07-31 17:19:10.599 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT 1 FROM `item_config`   WHERE ( `item_no` = @item_no0 )  AND ( `id` <> @id1 )   LIMIT 0,1 | 参数: [@item_no0=2, @id1=884]
[2025-07-31 17:19:10.656 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT 1 FROM `item_config`   WHERE ( `item_no` = @item_no0 )  AND ( `id` <> @id1 )   LIMIT 0,1
[2025-07-31 17:19:28.307 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`update_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=884]
[2025-07-31 17:19:28.343 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`update_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-07-31 17:19:28.345 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1 | 参数: [@item_no0=2]
[2025-07-31 17:19:28.399 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1
[2025-07-31 17:19:28.426 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 17:19:28.433 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 17:19:28.435 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 17:19:28.472 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 17:19:28.474 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 17:19:28.477 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-07-31 17:19:28.680 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-07-31 17:19:28.691 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 0,10
[2025-07-31 17:19:29.429 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 0,10
[2025-07-31 17:19:32.762 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 17:19:32.764 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 17:19:32.765 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 17:19:32.801 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 17:19:32.805 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 17:19:32.807 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`update_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=884]
[2025-07-31 17:19:32.844 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`update_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-07-31 17:19:32.846 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1 | 参数: [@item_no0=2]
[2025-07-31 17:19:32.887 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1
[2025-07-31 17:19:55.081 +08:00 INF] BMS: 🚀 应用程序启动，日志系统已配置
[2025-07-31 17:19:55.281 +08:00 DBG] BMS: 🔧 调试级别日志测试
[2025-07-31 17:19:55.297 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 17:19:55.300 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 17:19:55.396 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 17:19:55.961 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 17:19:56.021 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 17:19:56.049 +08:00 INF] BMS: ✅ DbContext 初始化成功，日志文件位置: logs/
[2025-07-31 17:19:58.280 +08:00 WRN] Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware: Failed to determine the https port for redirect.
[2025-07-31 17:20:04.294 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 17:20:04.310 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 17:20:04.328 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 17:20:04.386 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 17:20:04.389 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 17:20:04.491 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  DISTINCT `type` FROM `item_config`  WHERE NOT( `type` IS NULL   OR `type`='') 
[2025-07-31 17:20:04.542 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  DISTINCT `type` FROM `item_config`  WHERE NOT( `type` IS NULL   OR `type`='') 
[2025-07-31 17:20:04.584 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  DISTINCT `quality` FROM `item_config`  WHERE NOT( `quality` IS NULL   OR `quality`='') 
[2025-07-31 17:20:04.630 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  DISTINCT `quality` FROM `item_config`  WHERE NOT( `quality` IS NULL   OR `quality`='') 
[2025-07-31 17:20:04.850 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 17:20:04.859 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 17:20:04.925 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 17:20:04.975 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 17:20:04.980 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 17:20:05.229 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-07-31 17:20:05.462 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-07-31 17:20:05.478 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 0,10
[2025-07-31 17:20:06.267 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 0,10
[2025-07-31 17:20:08.425 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 17:20:08.427 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 17:20:08.431 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 17:20:08.478 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 17:20:08.485 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 17:20:08.508 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`update_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=884]
[2025-07-31 17:20:08.569 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`update_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-07-31 17:20:08.602 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1 | 参数: [@item_no0=2]
[2025-07-31 17:20:08.651 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1
[2025-07-31 17:20:12.975 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 17:20:12.976 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 17:20:12.978 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 17:20:13.036 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 17:20:13.037 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 17:20:13.058 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`update_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=884]
[2025-07-31 17:20:13.107 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`update_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-07-31 17:20:13.115 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT 1 FROM `item_config`   WHERE ( `item_no` = @item_no0 )  AND ( `id` <> @id1 )   LIMIT 0,1 | 参数: [@item_no0=2, @id1=884]
[2025-07-31 17:20:13.160 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT 1 FROM `item_config`   WHERE ( `item_no` = @item_no0 )  AND ( `id` <> @id1 )   LIMIT 0,1
[2025-07-31 17:20:55.149 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`update_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=884]
[2025-07-31 17:20:55.188 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`update_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-07-31 17:20:55.193 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1 | 参数: [@item_no0=2]
[2025-07-31 17:20:55.232 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1
[2025-07-31 17:20:55.243 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 17:20:55.244 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 17:20:55.246 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 17:20:55.284 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 17:20:55.287 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 17:20:55.289 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-07-31 17:20:55.494 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-07-31 17:20:55.496 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 0,10
[2025-07-31 17:20:56.242 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 0,10
[2025-07-31 17:21:22.377 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 17:21:22.382 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 17:21:22.388 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 17:21:22.430 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 17:21:22.433 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 17:21:22.436 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`update_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=884]
[2025-07-31 17:21:22.488 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`update_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-07-31 17:21:22.493 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1 | 参数: [@item_no0=2]
[2025-07-31 17:21:22.540 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1
[2025-07-31 17:21:26.650 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 17:21:26.653 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 17:21:26.657 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 17:21:26.701 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 17:21:26.702 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 17:21:26.706 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`update_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=884]
[2025-07-31 17:21:26.748 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`update_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-07-31 17:21:26.761 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT 1 FROM `item_config`   WHERE ( `item_no` = @item_no0 )  AND ( `id` <> @id1 )   LIMIT 0,1 | 参数: [@item_no0=2, @id1=884]
[2025-07-31 17:21:26.809 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT 1 FROM `item_config`   WHERE ( `item_no` = @item_no0 )  AND ( `id` <> @id1 )   LIMIT 0,1
[2025-07-31 17:22:57.808 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`update_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=884]
[2025-07-31 17:22:57.869 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`update_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-07-31 17:22:57.877 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1 | 参数: [@item_no0=2]
[2025-07-31 17:22:57.919 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1
[2025-07-31 17:22:57.949 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 17:22:57.954 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 17:22:57.956 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 17:22:58.000 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 17:22:58.041 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 17:22:58.047 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-07-31 17:22:58.271 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-07-31 17:22:58.276 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 0,10
[2025-07-31 17:22:59.008 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 0,10
[2025-07-31 17:57:30.807 +08:00 INF] BMS: 🚀 应用程序启动，日志系统已配置
[2025-07-31 17:57:30.880 +08:00 DBG] BMS: 🔧 调试级别日志测试
[2025-07-31 17:57:30.914 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 17:57:30.930 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 17:57:31.056 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 17:57:31.588 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 17:57:31.598 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 17:57:31.611 +08:00 INF] BMS: ✅ DbContext 初始化成功，日志文件位置: logs/
[2025-07-31 17:57:33.883 +08:00 WRN] Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware: Failed to determine the https port for redirect.
[2025-07-31 17:57:39.683 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 17:57:39.704 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 17:57:39.746 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 17:57:39.791 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 17:57:39.795 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 17:57:39.916 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  DISTINCT `type` FROM `item_config`  WHERE NOT( `type` IS NULL   OR `type`='') 
[2025-07-31 17:57:39.961 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  DISTINCT `type` FROM `item_config`  WHERE NOT( `type` IS NULL   OR `type`='') 
[2025-07-31 17:57:40.006 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  DISTINCT `quality` FROM `item_config`  WHERE NOT( `quality` IS NULL   OR `quality`='') 
[2025-07-31 17:57:40.044 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  DISTINCT `quality` FROM `item_config`  WHERE NOT( `quality` IS NULL   OR `quality`='') 
[2025-07-31 17:57:40.227 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 17:57:40.230 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 17:57:40.233 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 17:57:40.278 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 17:57:40.282 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 17:57:40.485 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-07-31 17:57:40.693 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-07-31 17:57:40.699 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 0,10
[2025-07-31 17:57:41.420 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 0,10
[2025-07-31 17:57:42.723 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 17:57:42.725 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 17:57:42.727 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 17:57:42.768 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 17:57:42.772 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 17:57:42.791 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`update_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=884]
[2025-07-31 17:57:42.838 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`update_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-07-31 17:57:42.878 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1 | 参数: [@item_no0=2]
[2025-07-31 17:57:42.917 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1
[2025-07-31 17:57:48.331 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 17:57:48.528 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 17:57:48.535 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 17:57:48.575 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 17:57:48.578 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 17:57:48.609 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`update_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=884]
[2025-07-31 17:57:48.654 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`update_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-07-31 17:57:48.677 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT 1 FROM `item_config`   WHERE ( `item_no` = @item_no0 )  AND ( `id` <> @id1 )   LIMIT 0,1 | 参数: [@item_no0=2, @id1=884]
[2025-07-31 17:57:48.715 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT 1 FROM `item_config`   WHERE ( `item_no` = @item_no0 )  AND ( `id` <> @id1 )   LIMIT 0,1
[2025-07-31 17:57:53.503 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: UPDATE `item_config`  SET
           `id`=@id,`item_no`=@item_no,`name`=@name,`type`=@type,`description`=@description,`quality`=@quality,`icon`=@icon,`price`=@price,`use_limit`=@use_limit,`extra`=@extra,`create_time`=@create_time,`update_time`=@update_time,`script`=@script,`is_active`=@is_active  WHERE ( `id` = @id0 ) | 参数: [@id0=884, @id=884, @item_no=2, @name=测试道具1123, @type=未分类, @description=, @quality=普通, @icon=, @price=1, @use_limit=, @extra=, @create_time=2025/7/21 0:09:48, @update_time=2025/7/31 17:57:48, @script=, @is_active=True]
[2025-07-31 17:57:53.526 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: UPDATE `item_config`  SET
           `id`=@id,`item_no`=@item_no,`name`=@name,`type`=@type,`description`=@description,`quality`=@quality,`icon`=@icon,`price`=@price,`use_limit`=@use_limit,`extra`=@extra,`create_time`=@create_time,`update_time`=@update_time,`script`=@script,`is_active`=@is_active  WHERE ( `id` = @id0 )
[2025-07-31 17:58:05.145 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`update_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=884]
[2025-07-31 17:58:05.161 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`update_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-07-31 17:58:45.699 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1 | 参数: [@item_no0=2]
[2025-07-31 17:58:45.726 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1
[2025-07-31 17:58:45.731 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: UPDATE `item_script`  SET
           `item_no`=@item_no,`script`=@script,`description`=@description,`create_time`=@create_time  WHERE `id`=@id | 参数: [@id=882, @item_no=2, @script=, @description=, @create_time=2025/7/21 0:09:48]
[2025-07-31 17:58:45.745 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: UPDATE `item_script`  SET
           `item_no`=@item_no,`script`=@script,`description`=@description,`create_time`=@create_time  WHERE `id`=@id
[2025-07-31 17:58:45.859 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`update_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=884]
[2025-07-31 17:58:45.934 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`update_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-07-31 17:58:45.946 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1 | 参数: [@item_no0=2]
[2025-07-31 17:58:45.987 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1
[2025-07-31 17:58:46.002 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 17:58:46.003 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 17:58:46.005 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 17:58:46.041 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 17:58:46.042 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 17:58:46.044 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-07-31 17:58:46.261 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-07-31 17:58:46.263 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 0,10
[2025-07-31 17:58:47.186 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 0,10
[2025-07-31 17:58:50.193 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 17:58:50.195 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 17:58:50.196 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 17:58:50.236 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 17:58:50.237 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 17:58:50.240 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`update_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=884]
[2025-07-31 17:58:50.276 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`update_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-07-31 17:58:50.281 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1 | 参数: [@item_no0=2]
[2025-07-31 17:58:50.332 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1
[2025-07-31 17:58:55.290 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 17:58:55.306 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 17:58:55.315 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 17:58:55.364 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 17:58:55.368 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 17:58:55.374 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`update_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=884]
[2025-07-31 17:58:55.492 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`update_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-07-31 17:58:55.530 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT 1 FROM `item_config`   WHERE ( `item_no` = @item_no0 )  AND ( `id` <> @id1 )   LIMIT 0,1 | 参数: [@item_no0=2, @id1=884]
[2025-07-31 17:58:55.572 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT 1 FROM `item_config`   WHERE ( `item_no` = @item_no0 )  AND ( `id` <> @id1 )   LIMIT 0,1
[2025-07-31 17:58:59.113 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: UPDATE `item_config`  SET
           `id`=@id,`item_no`=@item_no,`name`=@name,`type`=@type,`description`=@description,`quality`=@quality,`icon`=@icon,`price`=@price,`use_limit`=@use_limit,`extra`=@extra,`create_time`=@create_time,`update_time`=@update_time,`script`=@script,`is_active`=@is_active  WHERE ( `id` = @id0 ) | 参数: [@id0=884, @id=884, @item_no=2, @name=测试道具, @type=未分类, @description=, @quality=普通, @icon=, @price=1, @use_limit=, @extra=, @create_time=2025/7/21 0:09:48, @update_time=2025/7/31 17:58:55, @script=, @is_active=True]
[2025-07-31 17:58:59.130 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: UPDATE `item_config`  SET
           `id`=@id,`item_no`=@item_no,`name`=@name,`type`=@type,`description`=@description,`quality`=@quality,`icon`=@icon,`price`=@price,`use_limit`=@use_limit,`extra`=@extra,`create_time`=@create_time,`update_time`=@update_time,`script`=@script,`is_active`=@is_active  WHERE ( `id` = @id0 )
[2025-07-31 17:58:59.823 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`update_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=884]
[2025-07-31 17:58:59.838 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`update_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-07-31 17:59:00.510 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1 | 参数: [@item_no0=2]
[2025-07-31 17:59:00.541 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1
[2025-07-31 17:59:00.554 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: UPDATE `item_script`  SET
           `item_no`=@item_no,`script`=@script,`description`=@description,`create_time`=@create_time  WHERE `id`=@id | 参数: [@id=882, @item_no=2, @script=, @description=, @create_time=2025/7/21 0:09:48]
[2025-07-31 17:59:00.571 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: UPDATE `item_script`  SET
           `item_no`=@item_no,`script`=@script,`description`=@description,`create_time`=@create_time  WHERE `id`=@id
[2025-07-31 17:59:00.612 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`update_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=884]
[2025-07-31 17:59:00.657 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`name`,`type`,`description`,`quality`,`icon`,`price`,`use_limit`,`extra`,`create_time`,`update_time`,`script`,`is_active` FROM `item_config`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-07-31 17:59:00.668 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1 | 参数: [@item_no0=2]
[2025-07-31 17:59:00.716 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`item_no`,`script`,`description`,`create_time` FROM `item_script`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1
[2025-07-31 17:59:00.750 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 17:59:00.752 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 17:59:00.759 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 17:59:00.795 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 17:59:00.819 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 17:59:00.823 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-07-31 17:59:01.035 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable   
[2025-07-31 17:59:01.039 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 0,10
[2025-07-31 17:59:01.784 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM  (SELECT  `config`.`id` AS `id` , `config`.`item_no` AS `item_no` , `config`.`name` AS `name` , `config`.`type` AS `type` , `config`.`description` AS `description` , `config`.`quality` AS `quality` , `config`.`icon` AS `icon` , `config`.`price` AS `price` , `config`.`use_limit` AS `use_limit` , `config`.`extra` AS `extra` , `config`.`create_time` AS `create_time` , `config`.`is_active` AS `is_active` , `script`.`id` AS `script_id` , `script`.`item_no` AS `script_item_no` , `script`.`script` AS `script_script` , `script`.`description` AS `script_description` , `script`.`create_time` AS `script_create_time`  FROM `item_config` `config` Left JOIN `item_script` `script` ON ( `config`.`item_no` = `script`.`item_no` )   ) MergeTable     ORDER BY `item_no` ASC LIMIT 0,10
[2025-07-31 18:00:01.288 +08:00 INF] BMS: 🚀 应用程序启动，日志系统已配置
[2025-07-31 18:00:01.313 +08:00 DBG] BMS: 🔧 调试级别日志测试
[2025-07-31 18:00:01.328 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-07-31 18:00:01.330 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-07-31 18:00:01.413 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 18:00:01.894 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-07-31 18:00:01.902 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-07-31 18:00:01.904 +08:00 INF] BMS: ✅ DbContext 初始化成功，日志文件位置: logs/
[2025-07-31 18:00:03.142 +08:00 WRN] Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware: Failed to determine the https port for redirect.
