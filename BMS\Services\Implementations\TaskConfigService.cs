using BMS.Models.DTOs;
using BMS.Models.Entities;
using BMS.Models.Common;
using BMS.Services.Interfaces;
using SqlSugar;

namespace BMS.Services.Implementations
{
    /// <summary>
    /// 任务配置服务实现
    /// </summary>
    public class TaskConfigService : ITaskConfigService
    {
        private readonly IDbService _dbService;

        public TaskConfigService(IDbService dbService)
        {
            _dbService = dbService;
        }

        /// <summary>
        /// 获取任务配置列表
        /// </summary>
        public async Task<PagedResult<TaskConfigDto>> GetTaskConfigsAsync(TaskConfigQueryDto queryDto)
        {
            try
            {
                var query = _dbService.Queryable<task_config>()
                    .WhereIF(!string.IsNullOrEmpty(queryDto.TaskName), 
                        t => t.task_name.Contains(queryDto.TaskName!))
                    .WhereIF(queryDto.TaskType.HasValue, 
                        t => t.task_type == queryDto.TaskType)
                    .WhereIF(queryDto.IsActive.HasValue, 
                        t => (t.is_active == 1) == queryDto.IsActive)
                    .WhereIF(queryDto.IsRepeatable.HasValue, 
                        t => (t.is_repeatable == 1) == queryDto.IsRepeatable)
                    .OrderBy(t => t.sort_order)
                    .OrderBy(t => t.created_at, OrderByType.Desc);

                var totalCount = await query.CountAsync();
                var tasks = await query
                    .Skip((queryDto.Page - 1) * queryDto.PageSize)
                    .Take(queryDto.PageSize)
                    .ToListAsync();

                var taskDtos = tasks.Select(MapToDto).ToList();

                // 加载任务目标
                foreach (var taskDto in taskDtos)
                {
                    taskDto.Objectives = await GetTaskObjectivesAsync(taskDto.TaskId);
                }

                return new PagedResult<TaskConfigDto>(taskDtos, queryDto.Page, queryDto.PageSize, totalCount);
            }
            catch (Exception ex)
            {
                throw new Exception($"获取任务配置列表失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 根据ID获取任务配置
        /// </summary>
        public async Task<TaskConfigDto?> GetTaskConfigByIdAsync(string taskId)
        {
            try
            {
                var task = await _dbService.Queryable<task_config>()
                    .FirstAsync(t => t.task_id == taskId);

                if (task == null) return null;

                var taskDto = MapToDto(task);
                taskDto.Objectives = await GetTaskObjectivesAsync(taskId);

                return taskDto;
            }
            catch (Exception ex)
            {
                throw new Exception($"获取任务配置失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 创建任务配置
        /// </summary>
        public async Task<ApiResult<TaskConfigDto>> CreateTaskConfigAsync(TaskConfigCreateDto createDto)
        {
            try
            {
                // 自动生成任务ID
                string taskId = await GenerateUniqueTaskIdAsync();

                // 验证前置任务
                if (!string.IsNullOrEmpty(createDto.PrerequisiteTask))
                {
                    var prerequisiteExists = await _dbService.Queryable<task_config>()
                        .AnyAsync(t => t.task_id == createDto.PrerequisiteTask);

                    if (!prerequisiteExists)
                    {
                        return ApiResult<TaskConfigDto>.Fail("前置任务不存在");
                    }
                }

                var task = new task_config
                {
                    task_id = taskId,
                    task_name = createDto.TaskName,
                    task_description = createDto.TaskDescription,
                    task_type = (byte?)createDto.TaskType,
                    is_repeatable = (byte?)(createDto.IsRepeatable ? 1 : 0),
                    prerequisite_task = createDto.PrerequisiteTask,
                    required_pet = createDto.RequiredPet,
                    reward_config = createDto.RewardConfig,
                    is_network_task = (byte?)(createDto.IsNetworkTask ? 1 : 0),
                    is_active = (byte?)(createDto.IsActive ? 1 : 0),
                    sort_order = createDto.SortOrder,
                    created_at = DateTime.Now,
                    updated_at = DateTime.Now
                };

                await _dbService.Insertable(task).ExecuteCommandAsync();

                // 创建任务目标
                if (createDto.Objectives?.Any() == true)
                {
                    await CreateTaskObjectivesAsync(taskId, createDto.Objectives);
                }

                var result = await GetTaskConfigByIdAsync(taskId);
                return ApiResult<TaskConfigDto>.Ok(result!, $"任务配置创建成功，任务ID: {taskId}");
            }
            catch (Exception ex)
            {
                return ApiResult<TaskConfigDto>.Fail($"创建任务配置失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 更新任务配置
        /// </summary>
        public async Task<ApiResult<TaskConfigDto>> UpdateTaskConfigAsync(string taskId, TaskConfigCreateDto updateDto)
        {
            try
            {
                var existingTask = await _dbService.Queryable<task_config>()
                    .FirstAsync(t => t.task_id == taskId);

                if (existingTask == null)
                {
                    return ApiResult<TaskConfigDto>.Fail("任务配置不存在");
                }

                // 验证前置任务
                if (!string.IsNullOrEmpty(updateDto.PrerequisiteTask) && updateDto.PrerequisiteTask != taskId)
                {
                    var prerequisiteExists = await _dbService.Queryable<task_config>()
                        .AnyAsync(t => t.task_id == updateDto.PrerequisiteTask);

                    if (!prerequisiteExists)
                    {
                        return ApiResult<TaskConfigDto>.Fail("前置任务不存在");
                    }
                }

                // 更新任务配置
                existingTask.task_name = updateDto.TaskName;
                existingTask.task_description = updateDto.TaskDescription;
                existingTask.task_type = (byte?)updateDto.TaskType;
                existingTask.is_repeatable = (byte?)(updateDto.IsRepeatable ? 1 : 0);
                existingTask.prerequisite_task = updateDto.PrerequisiteTask;
                existingTask.required_pet = updateDto.RequiredPet;
                existingTask.reward_config = updateDto.RewardConfig;
                existingTask.is_network_task = (byte?)(updateDto.IsNetworkTask ? 1 : 0);
                existingTask.is_active = (byte?)(updateDto.IsActive ? 1 : 0);
                existingTask.sort_order = updateDto.SortOrder;
                existingTask.updated_at = DateTime.Now;

                await _dbService.Updateable(existingTask).ExecuteCommandAsync();

                // 删除旧的任务目标
                await _dbService.GetClient().Ado.ExecuteCommandAsync(
                    "DELETE FROM task_objective WHERE task_id = @taskId",
                    new { taskId = taskId });

                // 创建新的任务目标
                if (updateDto.Objectives?.Any() == true)
                {
                    await CreateTaskObjectivesAsync(taskId, updateDto.Objectives);
                }

                var result = await GetTaskConfigByIdAsync(taskId);
                return ApiResult<TaskConfigDto>.Ok(result!, "任务配置更新成功");
            }
            catch (Exception ex)
            {
                return ApiResult<TaskConfigDto>.Fail($"更新任务配置失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 删除任务配置
        /// </summary>
        public async Task<ApiResult<bool>> DeleteTaskConfigAsync(string taskId)
        {
            try
            {
                var existingTask = await _dbService.Queryable<task_config>()
                    .FirstAsync(t => t.task_id == taskId);

                if (existingTask == null)
                {
                    return ApiResult<bool>.Fail("任务配置不存在");
                }

                // 检查是否有用户正在进行此任务
                var hasUserTasks = await _dbService.Queryable<user_task>()
                    .AnyAsync(ut => ut.task_id == taskId && ut.task_status == 1);

                if (hasUserTasks)
                {
                    return ApiResult<bool>.Fail("有用户正在进行此任务，无法删除");
                }

                // 删除任务目标
                await _dbService.GetClient().Ado.ExecuteCommandAsync(
                    "DELETE FROM task_objective WHERE task_id = @taskId",
                    new { taskId = taskId });

                // 删除任务配置
                await _dbService.GetClient().Ado.ExecuteCommandAsync(
                    "DELETE FROM task_config WHERE task_id = @taskId",
                    new { taskId = taskId });

                return ApiResult<bool>.Ok(true, "任务配置删除成功");
            }
            catch (Exception ex)
            {
                return ApiResult<bool>.Fail($"删除任务配置失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 启用/禁用任务
        /// </summary>
        public async Task<ApiResult<bool>> ToggleTaskActiveAsync(string taskId, bool isActive)
        {
            try
            {
                var existingTask = await _dbService.Queryable<task_config>()
                    .FirstAsync(t => t.task_id == taskId);

                if (existingTask == null)
                {
                    return ApiResult<bool>.Fail("任务配置不存在");
                }

                existingTask.is_active = (byte?)(isActive ? 1 : 0);
                existingTask.updated_at = DateTime.Now;

                await _dbService.Updateable(existingTask).ExecuteCommandAsync();

                return ApiResult<bool>.Ok(true, $"任务已{(isActive ? "启用" : "禁用")}");
            }
            catch (Exception ex)
            {
                return ApiResult<bool>.Fail($"切换任务状态失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取任务类型选项
        /// </summary>
        public async Task<List<OptionDto>> GetTaskTypeOptionsAsync()
        {
            return await Task.FromResult(new List<OptionDto>
            {
                new OptionDto { Value = "0", Label = "普通任务" },
                new OptionDto { Value = "1", Label = "循环任务" },
                new OptionDto { Value = "2", Label = "活动任务" }
            });
        }

        /// <summary>
        /// 获取任务选项
        /// </summary>
        public async Task<List<OptionDto>> GetTaskOptionsAsync()
        {
            try
            {
                var tasks = await _dbService.Queryable<task_config>()
                    .Where(t => t.is_active == 1)
                    .OrderBy(t => t.sort_order)
                    .Select(t => new OptionDto
                    {
                        Value = t.task_id,
                        Label = t.task_name
                    })
                    .ToListAsync();

                return tasks;
            }
            catch (Exception ex)
            {
                throw new Exception($"获取任务选项失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 获取目标类型选项
        /// </summary>
        public async Task<List<OptionDto>> GetObjectiveTypeOptionsAsync()
        {
            return await Task.FromResult(new List<OptionDto>
            {
                new OptionDto { Value = "KILL_MONSTER", Label = "击杀怪物" },
                new OptionDto { Value = "COLLECT_ITEM", Label = "收集道具" },
                new OptionDto { Value = "REACH_LEVEL", Label = "达到等级" },
                new OptionDto { Value = "VISIT_MAP", Label = "访问地图" },
                new OptionDto { Value = "USE_SKILL", Label = "使用技能" },
                new OptionDto { Value = "EQUIP_ITEM", Label = "装备道具" },
                new OptionDto { Value = "COMPLETE_DUNGEON", Label = "完成副本" },
                new OptionDto { Value = "PET_EVOLUTION", Label = "宠物进化" },
                new OptionDto { Value = "ENHANCE_EQUIPMENT", Label = "强化装备" },
                new OptionDto { Value = "TALK_TO_NPC", Label = "与NPC对话" }
            });
        }

        #region 私有方法

        /// <summary>
        /// 实体转DTO
        /// </summary>
        private TaskConfigDto MapToDto(task_config task)
        {
            return new TaskConfigDto
            {
                TaskId = task.task_id,
                TaskName = task.task_name,
                TaskDescription = task.task_description,
                TaskType = task.task_type ?? 0,
                TaskTypeName = GetTaskTypeName(task.task_type ?? 0),
                IsRepeatable = (task.is_repeatable ?? 0) == 1,
                PrerequisiteTask = task.prerequisite_task,
                RequiredPet = task.required_pet,
                RewardConfig = task.reward_config,
                IsNetworkTask = (task.is_network_task ?? 0) == 1,
                IsActive = (task.is_active ?? 1) == 1,
                SortOrder = task.sort_order ?? 0,
                CreatedAt = task.created_at,
                UpdatedAt = task.updated_at
            };
        }

        /// <summary>
        /// 获取任务类型名称
        /// </summary>
        private string GetTaskTypeName(int taskType)
        {
            return taskType switch
            {
                0 => "普通任务",
                1 => "循环任务",
                2 => "活动任务",
                _ => "未知类型"
            };
        }

        /// <summary>
        /// 获取任务目标列表
        /// </summary>
        private async Task<List<TaskObjectiveDto>> GetTaskObjectivesAsync(string taskId)
        {
            try
            {
                var objectives = await _dbService.Queryable<task_objective>()
                    .Where(o => o.task_id == taskId)
                    .OrderBy(o => o.objective_order)
                    .ToListAsync();

                return objectives.Select(o => new TaskObjectiveDto
                {
                    ObjectiveId = o.objective_id,
                    TaskId = o.task_id,
                    ObjectiveType = o.objective_type,
                    ObjectiveTypeName = GetObjectiveTypeName(o.objective_type),
                    TargetId = o.target_id,
                    TargetName = o.target_name,
                    TargetAmount = o.target_amount,
                    ObjectiveOrder = o.objective_order ?? 0,
                    ObjectiveDescription = o.objective_description,
                    CompleteTemplate = o.complete_template,
                    CreatedAt = o.created_at
                }).ToList();
            }
            catch (Exception ex)
            {
                throw new Exception($"获取任务目标失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 创建任务目标
        /// </summary>
        private async Task CreateTaskObjectivesAsync(string taskId, List<TaskObjectiveCreateDto> objectives)
        {
            try
            {
                if (objectives?.Any() != true)
                    return;

                // 使用原生SQL批量插入避免SqlSugar泛型约束问题
                var sql = @"INSERT INTO task_objective
                           (task_id, objective_type, target_id, target_name, target_amount, objective_order, objective_description, complete_template, created_at)
                           VALUES ";

                var values = new List<string>();
                var parameters = new List<object>();

                for (int i = 0; i < objectives.Count; i++)
                {
                    var obj = objectives[i];
                    values.Add($"(@taskId{i}, @objectiveType{i}, @targetId{i}, @targetName{i}, @targetAmount{i}, @objectiveOrder{i}, @objectiveDescription{i}, @completeTemplate{i}, @createdAt{i})");

                    parameters.Add(new
                    {
                        name = $"taskId{i}",
                        value = taskId
                    });
                    parameters.Add(new
                    {
                        name = $"objectiveType{i}",
                        value = obj.ObjectiveType
                    });
                    parameters.Add(new
                    {
                        name = $"targetId{i}",
                        value = obj.TargetId ?? (object)DBNull.Value
                    });
                    parameters.Add(new
                    {
                        name = $"targetName{i}",
                        value = obj.TargetName ?? (object)DBNull.Value
                    });
                    parameters.Add(new
                    {
                        name = $"targetAmount{i}",
                        value = obj.TargetAmount
                    });
                    parameters.Add(new
                    {
                        name = $"objectiveOrder{i}",
                        value = obj.ObjectiveOrder
                    });
                    parameters.Add(new
                    {
                        name = $"objectiveDescription{i}",
                        value = obj.ObjectiveDescription ?? (object)DBNull.Value
                    });
                    parameters.Add(new
                    {
                        name = $"completeTemplate{i}",
                        value = obj.CompleteTemplate ?? (object)DBNull.Value
                    });
                    parameters.Add(new
                    {
                        name = $"createdAt{i}",
                        value = DateTime.Now
                    });
                }

                sql += string.Join(", ", values);

                // 构建参数字典
                var paramDict = new Dictionary<string, object>();
                foreach (var param in parameters)
                {
                    var paramObj = param as dynamic;
                    paramDict[paramObj.name] = paramObj.value;
                }

                await _dbService.GetClient().Ado.ExecuteCommandAsync(sql, paramDict);
            }
            catch (Exception ex)
            {
                throw new Exception($"创建任务目标失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 获取目标类型名称
        /// </summary>
        private string GetObjectiveTypeName(string objectiveType)
        {
            return objectiveType switch
            {
                "KILL_MONSTER" => "击杀怪物",
                "COLLECT_ITEM" => "收集道具",
                "REACH_LEVEL" => "达到等级",
                "VISIT_MAP" => "访问地图",
                "USE_SKILL" => "使用技能",
                "EQUIP_ITEM" => "装备道具",
                _ => objectiveType
            };
        }

        /// <summary>
        /// 生成唯一的任务ID
        /// </summary>
        private async Task<string> GenerateUniqueTaskIdAsync()
        {
            string taskId;
            bool isUnique = false;
            int attempts = 0;
            const int maxAttempts = 10;

            do
            {
                // 生成格式：TASK_YYYYMMDD_HHMMSS_XXX
                var now = DateTime.Now;
                var dateStr = now.ToString("yyyyMMdd");
                var timeStr = now.ToString("HHmmss");
                var random = new Random().Next(100, 999);

                taskId = $"TASK_{dateStr}_{timeStr}_{random}";

                // 检查是否已存在
                var existingTask = await _dbService.Queryable<task_config>()
                    .FirstAsync(t => t.task_id == taskId);

                isUnique = existingTask == null;
                attempts++;

                if (!isUnique && attempts < maxAttempts)
                {
                    // 如果ID已存在，等待1毫秒后重试
                    await Task.Delay(1);
                }

            } while (!isUnique && attempts < maxAttempts);

            if (!isUnique)
            {
                // 如果多次尝试仍然冲突，使用GUID作为后备方案
                taskId = $"TASK_{Guid.NewGuid().ToString("N")[..8].ToUpper()}";
            }

            return taskId;
        }

        /// <summary>
        /// 迁移target_name字段
        /// </summary>
        public async Task<ApiResult<bool>> MigrateTargetNameFieldAsync()
        {
            try
            {
                var client = _dbService.GetClient();

                // 检查target_name字段是否存在
                var targetNameExists = await CheckColumnExistsAsync("task_objective", "target_name");
                if (!targetNameExists)
                {
                    await client.Ado.ExecuteCommandAsync(@"
                        ALTER TABLE task_objective
                        ADD COLUMN target_name VARCHAR(200) DEFAULT NULL COMMENT '目标名称'
                        AFTER target_id");
                }

                // 检查complete_template字段是否存在
                var completeTemplateExists = await CheckColumnExistsAsync("task_objective", "complete_template");
                if (!completeTemplateExists)
                {
                    await client.Ado.ExecuteCommandAsync(@"
                        ALTER TABLE task_objective
                        ADD COLUMN complete_template VARCHAR(100) DEFAULT NULL COMMENT '进度模版'
                        AFTER objective_description");
                }

                var message = $"迁移完成 - target_name: {(targetNameExists ? "已存在" : "已添加")}, complete_template: {(completeTemplateExists ? "已存在" : "已添加")}";
                return ApiResult<bool>.Ok(true, message);
            }
            catch (Exception ex)
            {
                return ApiResult<bool>.Fail($"迁移失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 检查字段是否存在
        /// </summary>
        private async Task<bool> CheckColumnExistsAsync(string tableName, string columnName)
        {
            var sql = @"
                SELECT COUNT(*)
                FROM information_schema.columns
                WHERE table_schema = DATABASE()
                AND table_name = @tableName
                AND column_name = @columnName";

            var client = _dbService.GetClient();
            var count = await client.Ado.GetIntAsync(sql, new { tableName, columnName });
            return count > 0;
        }

        #endregion
    }
}
