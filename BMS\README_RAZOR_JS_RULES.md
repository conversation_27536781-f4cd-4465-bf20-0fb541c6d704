# Razor + JavaScript 混合开发规范

## 🚫 严禁事项

### 1. JavaScript中直接使用@符号
```javascript
// ❌ 禁止 - 会被Razor解析
const email = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
if (email.includes('@')) { ... }

// ✅ 推荐 - 使用字符编码
const atChar = String.fromCharCode(64); // ASCII码64是@
if (email.includes(atChar)) { ... }
```

### 2. JavaScript注释中使用@符号
```javascript
// ❌ 禁止 - 注释中的@也会被解析
// 检查邮箱中的@符号

// ✅ 推荐 - 避免在注释中出现@
// 检查邮箱中的at符号
```

### 3. 正则表达式中直接使用方括号
```javascript
// ❌ 禁止 - 方括号会被Razor误解析
const phoneRegex = /^1[3-9]\d{9}$/;

// ✅ 推荐 - 使用字符编码构建正则
const leftBracket = String.fromCharCode(91);   // [
const rightBracket = String.fromCharCode(93);  // ]
const phonePattern = '^1' + leftBracket + '3-9' + rightBracket + '\\d{9}$';
const phoneRegex = new RegExp(phonePattern);
```

## ✅ 推荐做法

### 1. 特殊字符编码表
```javascript
// 常用特殊字符的ASCII编码
const SPECIAL_CHARS = {
    AT: 64,           // @
    LEFT_BRACKET: 91, // [
    RIGHT_BRACKET: 93,// ]
    BACKSLASH: 92,    // \
    DOLLAR: 36        // $
};

// 使用示例
const atChar = String.fromCharCode(SPECIAL_CHARS.AT);
```

### 2. 验证函数封装
```javascript
// 推荐：将验证逻辑封装成函数
function validateEmail(email) {
    if (!email) return false;
    const atChar = String.fromCharCode(64);
    return email.includes(atChar) && email.includes('.');
}

function validatePhone(phone) {
    if (!phone) return false;
    const pattern = '^1' + String.fromCharCode(91) + '3-9' + String.fromCharCode(93) + '\\d{9}$';
    return new RegExp(pattern).test(phone);
}
```

### 3. Vue.js中的最佳实践
```javascript
// ✅ 推荐的Vue组件结构
createApp({
    data() {
        return {
            // 数据定义
        };
    },
    methods: {
        // 验证方法独立封装
        validateForm() {
            this.errors = {};
            this.validateEmail();
            this.validatePhone();
            return Object.keys(this.errors).length === 0;
        },
        
        validateEmail() {
            if (!this.formData.email) return;
            const atChar = String.fromCharCode(64);
            if (!this.formData.email.includes(atChar)) {
                this.errors.email = '请输入有效的邮箱地址';
            }
        },
        
        validatePhone() {
            if (!this.formData.phone) return;
            // 简化验证逻辑，避免复杂正则
            if (this.formData.phone.length !== 11 || !this.formData.phone.startsWith('1')) {
                this.errors.phone = '请输入有效的手机号码';
            }
        }
    }
}).mount('#app');
```

## 🔧 调试技巧

### 1. 编译错误定位
当出现以下错误时，立即检查JavaScript中的特殊字符：
- `CS1501: "Write"方法没有采用 0 个参数的重载`
- `RZ1005: "xxx" is not valid at the start of a code block`
- `RZ1003: A space or line break was encountered after the "@" character`

### 2. 常见错误模式
```
错误信息中包含以下关键词时，通常是Razor解析问题：
- "Write"方法
- "code block"
- "after the @ character"
- 表达式项";"无效
```

## 📚 扩展资源

### ASCII码参考表
```javascript
// 常用ASCII码
const ASCII = {
    SPACE: 32,        //   
    EXCLAMATION: 33,  // !
    DOUBLE_QUOTE: 34, // "
    HASH: 35,         // #
    DOLLAR: 36,       // $
    PERCENT: 37,      // %
    AMPERSAND: 38,    // &
    SINGLE_QUOTE: 39, // '
    LEFT_PAREN: 40,   // (
    RIGHT_PAREN: 41,  // )
    ASTERISK: 42,     // *
    PLUS: 43,         // +
    COMMA: 44,        // ,
    MINUS: 45,        // -
    DOT: 46,          // .
    SLASH: 47,        // /
    AT: 64,           // @
    LEFT_BRACKET: 91, // [
    BACKSLASH: 92,    // \
    RIGHT_BRACKET: 93,// ]
    CARET: 94,        // ^
    UNDERSCORE: 95,   // _
    BACKTICK: 96,     // `
    LEFT_BRACE: 123,  // {
    PIPE: 124,        // |
    RIGHT_BRACE: 125, // }
    TILDE: 126        // ~
};
```

## 🎯 快速检查清单

开发前检查：
- [ ] JavaScript中是否有@符号？
- [ ] 正则表达式是否包含[]字符？
- [ ] 注释中是否提到了@符号？
- [ ] 是否使用了其他可能被Razor解析的特殊字符？

发布前检查：
- [ ] `dotnet clean` 清理缓存
- [ ] `dotnet build` 编译检查
- [ ] 确认没有RZ开头的编译错误
- [ ] 确认没有CS1501等相关错误

---

**记住：在Razor视图中，@符号始终具有特殊含义！** 