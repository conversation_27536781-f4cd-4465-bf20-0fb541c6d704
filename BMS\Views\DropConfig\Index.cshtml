@{
    ViewData["Title"] = "掉落配置管理";
}

<!-- 科幻背景 -->
<div class="cyber-bg"></div>

<!-- 科幻掉落配置管理应用容器 -->
<div id="dropConfigApp">
    <!-- 科幻页面标题 -->
    <div class="container-fluid py-4">
        <div class="row mb-4">
            <div class="col-12">
                <div class="cyber-card">
                    <div class="cyber-card-header">
                        <div class="cyber-icon">💎</div>
                        <h1 class="cyber-card-title">掉落配置管理</h1>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 科幻主要内容 -->
    <div class="container-fluid">
        <!-- 科幻搜索和操作区域 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="cyber-card">
                    <div class="cyber-card-header">
                        <div class="cyber-icon">🔍</div>
                        <h3 class="cyber-card-title">搜索条件</h3>
                        <div class="cyber-card-tools">
                            <button type="button" class="cyber-btn cyber-btn-primary" v-on:click="openCreateModal">
                                <i class="fas fa-plus me-1"></i>新增掉落配置
                            </button>
                        </div>
                    </div>
                    <div class="cyber-card-body">
                        <div class="row mb-3">
                            <div class="col-md-2">
                                <label class="cyber-label">地图选择</label>
                                <select class="cyber-select" v-model="searchForm.mapId" v-on:change="onMapChange">
                                    <option value="">请选择地图</option>
                                    <option v-for="option in mapOptions" v-bind:key="option.value" v-bind:value="option.value">
                                        {{ option.label }}
                                    </option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="cyber-label">地图名称</label>
                                <input type="text" class="cyber-input" placeholder="地图名称" v-model="searchForm.mapName">
                            </div>
                            <div class="col-md-2">
                                <label class="cyber-label">掉落类型</label>
                                <select class="cyber-select" v-model="searchForm.dropType">
                                    <option value="">请选择掉落类型</option>
                                    <option v-for="option in dropTypeOptions" v-bind:key="option.value" v-bind:value="option.value">
                                        {{ option.label }}
                                    </option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="cyber-label">怪物选择</label>
                                <select class="cyber-select" v-model="searchForm.monsterId" v-bind:disabled="!searchForm.mapId">
                                    <option value="">请选择怪物</option>
                                    <option v-for="option in monsterOptions" v-bind:key="option.value" v-bind:value="option.value">
                                        {{ option.label }}
                                    </option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="cyber-label">道具ID</label>
                                <input type="text" class="cyber-input" placeholder="道具ID" v-model="searchForm.itemId">
                            </div>
                            <div class="col-md-2">
                                <label class="cyber-label">道具名称</label>
                                <input type="text" class="cyber-input" placeholder="道具名称" v-model="searchForm.itemName">
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-12">
                                <button type="button" class="cyber-btn cyber-btn-info me-2" v-on:click="search">
                                    <i class="fas fa-search me-1"></i>搜索
                                </button>
                                <button type="button" class="cyber-btn cyber-btn-outline me-2" v-on:click="reset">
                                    <i class="fas fa-sync me-1"></i>重置
                                </button>
                                <button type="button" class="cyber-btn cyber-btn-danger" v-on:click="batchDelete" v-bind:disabled="selectedIds.length === 0">
                                    <i class="fas fa-trash me-1"></i>批量删除
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 科幻数据表格 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="cyber-card">
                    <div class="cyber-card-header">
                        <div class="cyber-icon">📊</div>
                        <h3 class="cyber-card-title">掉落配置列表</h3>
                        <div class="cyber-card-tools">
                            <span class="cyber-badge cyber-badge-info">
                                共 {{ totalCount }} 条记录
                            </span>
                        </div>
                    </div>
                    <div class="cyber-card-body">
                        <div class="cyber-table-container">
                            <table class="cyber-table">
                                <thead class="cyber-thead">
                                    <tr class="cyber-tr">
                                        <th class="cyber-th" style="width: 40px;">
                                            <input type="checkbox" class="cyber-checkbox" v-model="selectAll" v-on:change="toggleSelectAll">
                                        </th>
                                        <th class="cyber-th">ID</th>
                                        <th class="cyber-th">地图名称</th>
                                        <th class="cyber-th">掉落类型</th>
                                        <th class="cyber-th">怪物名称</th>
                                        <th class="cyber-th">道具数量</th>
                                        <th class="cyber-th">备注</th>
                                        <th class="cyber-th">创建时间</th>
                                        <th class="cyber-th" style="width: 150px;">操作</th>
                                    </tr>
                                </thead>
                                <tbody class="cyber-tbody" v-if="loading">
                                    <tr class="cyber-tr">
                                        <td colspan="9" class="cyber-td text-center py-4">
                                            <i class="fas fa-spinner fa-spin me-2"></i>加载中...
                                        </td>
                                    </tr>
                                </tbody>
                                <tbody class="cyber-tbody" v-else-if="dropConfigs.length === 0">
                                    <tr class="cyber-tr">
                                        <td colspan="9" class="cyber-td text-center py-4 text-muted">
                                            <i class="fas fa-inbox me-2"></i>暂无数据
                                        </td>
                                    </tr>
                                </tbody>
                                <tbody class="cyber-tbody" v-else>
                                    <tr class="cyber-tr" v-for="item in dropConfigs" v-bind:key="item.id">
                                        <td class="cyber-td">
                                            <input type="checkbox" class="cyber-checkbox" v-model="selectedIds" v-bind:value="item.id">
                                        </td>
                                        <td class="cyber-td">{{ item.id }}</td>
                                        <td class="cyber-td">
                                            <span class="cyber-text-primary">{{ item.mapName }}</span>
                                        </td>
                                        <td class="cyber-td">
                                            <span class="cyber-badge" v-bind:class="getDropTypeClass(item.dropType)">
                                                {{ item.dropType }}
                                            </span>
                                        </td>
                                        <td class="cyber-td">{{ item.monsterName || '-' }}</td>
                                        <td class="cyber-td">
                                            <span v-if="item.dropItemsJson" class="cyber-badge cyber-badge-info">
                                                <i class="fas fa-gem me-1"></i>{{ parseDropItems(item.dropItemsJson).length }}种道具
                                            </span>
                                            <span v-else-if="item.itemId" class="cyber-badge cyber-badge-secondary">
                                                <i class="fas fa-gem me-1"></i>1种道具
                                            </span>
                                            <span v-else>-</span>
                                        </td>
                                        <td class="cyber-td">{{ item.remark || '-' }}</td>
                                        <td class="cyber-td">{{ formatDateTime(item.createTime) }}</td>
                                        <td class="cyber-td">
                                            <button type="button" class="cyber-btn cyber-btn-sm cyber-btn-warning me-1" v-on:click="openUpdateModal(item)">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button type="button" class="cyber-btn cyber-btn-sm cyber-btn-danger" v-on:click="deleteItem(item.id)">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 科幻分页 -->
        <div class="row" v-if="totalCount > 0">
            <div class="col-12">
                <div class="cyber-card">
                    <div class="cyber-card-body">
                        <div class="cyber-pagination-container">
                            <div class="cyber-pagination-info">
                                <span class="cyber-text-muted">
                                    显示第 {{ (currentPage - 1) * pageSize + 1 }} 到 {{ Math.min(currentPage * pageSize, totalCount) }} 条记录，共 {{ totalCount }} 条
                                </span>
                            </div>
                            <div class="cyber-pagination">
                                <button class="cyber-page-btn" v-bind:disabled="currentPage === 1" v-on:click="changePage(currentPage - 1)">
                                    <i class="fas fa-chevron-left"></i>
                                </button>
                                <button class="cyber-page-btn"
                                        v-for="page in visiblePages"
                                        v-bind:key="page"
                                        v-bind:class="{ active: page === currentPage }"
                                        v-on:click="changePage(page)">
                                    {{ page }}
                                </button>
                                <button class="cyber-page-btn" v-bind:disabled="currentPage === totalPages" v-on:click="changePage(currentPage + 1)">
                                    <i class="fas fa-chevron-right"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 科幻新增/编辑模态框 -->
    <div class="modal fade" id="dropConfigModal" tabindex="-1" role="dialog" style="z-index: 9999;">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content cyber-modal">
                <div class="modal-header cyber-modal-header">
                    <h4 class="modal-title cyber-modal-title">
                        <i class="fas fa-cog me-2"></i>{{ isEdit ? '编辑掉落配置' : '新增掉落配置' }}
                    </h4>
                    <button type="button" class="btn-close cyber-btn-close" v-on:click="closeModal">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body cyber-modal-body">
                    <form class="cyber-form">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="cyber-form-group">
                                    <label class="cyber-label">地图 <span class="text-danger">*</span></label>
                                    <select class="cyber-select" v-model="formData.mapId" v-on:change="onFormMapChange">
                                        <option value="">请选择地图</option>
                                        <option v-for="option in mapOptions" v-bind:key="option.value" v-bind:value="option.value">
                                            {{ option.label }}
                                        </option>
                                    </select>
                                    <div class="cyber-error" v-if="formErrors.mapId">{{ formErrors.mapId }}</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="cyber-form-group">
                                    <label class="cyber-label">掉落类型 <span class="text-danger">*</span></label>
                                    <select class="cyber-select" v-model="formData.dropType" v-on:change="onDropTypeChange">
                                        <option value="">请选择掉落类型</option>
                                        <option v-for="option in dropTypeOptions" v-bind:key="option.value" v-bind:value="option.value">
                                            {{ option.label }}
                                        </option>
                                    </select>
                                    <div class="cyber-error" v-if="formErrors.dropType">{{ formErrors.dropType }}</div>
                                </div>
                            </div>
                        </div>
                        <div class="row" v-if="formData.dropType === '怪物掉落'">
                            <div class="col-md-12">
                                <div class="cyber-form-group">
                                    <label class="cyber-label">怪物 <span class="text-danger">*</span></label>
                                    <select class="cyber-select" v-model="formData.monsterId" v-bind:disabled="!formData.mapId">
                                        <option value="">请选择怪物</option>
                                        <option v-for="option in formMonsterOptions" v-bind:key="option.value" v-bind:value="option.value">
                                            {{ option.label }}
                                        </option>
                                    </select>
                                    <div class="cyber-error" v-if="formErrors.monsterId">{{ formErrors.monsterId }}</div>
                                </div>
                            </div>
                        </div>
                        <!-- 道具配置区域 -->
                        <div class="cyber-form-group">
                            <label class="cyber-label">掉落道具配置 <span class="text-danger">*</span></label>

                            <!-- 添加道具区域 -->
                            <div class="cyber-card mb-3">
                                <div class="cyber-card-header">
                                    <div class="cyber-icon">💎</div>
                                    <h3 class="cyber-card-title">添加掉落道具</h3>
                                </div>
                                <div class="cyber-card-body">
                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <label class="cyber-label">选择道具</label>
                                            <input type="text" class="cyber-input mb-2" placeholder="搜索道具..." v-model="itemSearchText" v-on:input="filterItems">
                                            <select class="cyber-select" v-model="currentItem.itemId" size="3" style="height: auto; min-height: 80px; max-height: 120px;">
                                                <option value="">请选择道具</option>
                                                <option v-for="option in filteredItemOptions" v-bind:key="option.value" v-bind:value="option.value">
                                                    [{{ option.value }}] {{ option.label }}
                                                </option>
                                            </select>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="row">
                                                <div class="col-md-12 mb-2">
                                                    <label class="cyber-label">掉落概率</label>
                                                    <input type="number" class="cyber-input" v-model.number="currentItem.dropRate" step="0.01" min="0" max="1" placeholder="0.00-1.00">
                                                </div>
                                                <div class="col-md-6">
                                                    <label class="cyber-label">最小数量</label>
                                                    <input type="number" class="cyber-input" v-model.number="currentItem.minCount" min="1" placeholder="最小数量">
                                                </div>
                                                <div class="col-md-6">
                                                    <label class="cyber-label">最大数量</label>
                                                    <input type="number" class="cyber-input" v-model.number="currentItem.maxCount" min="1" placeholder="最大数量">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-12">
                                            <button type="button" class="cyber-btn cyber-btn-success" v-on:click="addDropItem" v-bind:disabled="!currentItem.itemId">
                                                <i class="fas fa-plus me-1"></i>添加道具
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 已配置的道具列表 -->
                            <div class="cyber-card" v-if="formData.dropItems.length > 0">
                                <div class="cyber-card-header">
                                    <div class="cyber-icon">📦</div>
                                    <h3 class="cyber-card-title">已配置的掉落道具</h3>
                                    <div class="cyber-card-tools">
                                        <span class="cyber-badge cyber-badge-info">{{ formData.dropItems.length }}</span>
                                    </div>
                                </div>
                                <div class="cyber-card-body" style="max-height: 200px; overflow-y: auto;">
                                    <div class="cyber-table-container">
                                        <table class="cyber-table cyber-table-sm">
                                            <thead class="cyber-thead">
                                                <tr class="cyber-tr">
                                                    <th class="cyber-th">道具</th>
                                                    <th class="cyber-th">概率</th>
                                                    <th class="cyber-th">数量</th>
                                                    <th class="cyber-th" style="width: 60px;">操作</th>
                                                </tr>
                                            </thead>
                                            <tbody class="cyber-tbody">
                                                <tr class="cyber-tr" v-for="(item, index) in formData.dropItems" v-bind:key="index">
                                                    <td class="cyber-td">
                                                        <span class="cyber-text-primary">[{{ item.itemId }}]</span> {{ getItemName(item.itemId) }}
                                                    </td>
                                                    <td class="cyber-td">
                                                        <span class="cyber-badge cyber-badge-success">{{ (item.dropRate * 100).toFixed(2) }}%</span>
                                                    </td>
                                                    <td class="cyber-td">
                                                        <span class="cyber-badge cyber-badge-info">
                                                            {{ item.minCount === item.maxCount ? item.minCount : (item.minCount + '-' + item.maxCount) }}
                                                        </span>
                                                    </td>
                                                    <td class="cyber-td">
                                                        <button type="button" class="cyber-btn cyber-btn-sm cyber-btn-danger" v-on:click="removeDropItem(index)">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>

                            <div class="cyber-error" v-if="formErrors.dropItems">{{ formErrors.dropItems }}</div>
                            <small class="cyber-text-muted">
                                显示 {{ filteredItemOptions.length }} / {{ itemOptions.length }} 个道具可选
                            </small>
                        </div>
                        <div class="cyber-form-group">
                            <label class="cyber-label">备注</label>
                            <textarea class="cyber-textarea" v-model="formData.remark" rows="3" placeholder="请输入备注信息"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer cyber-modal-footer">
                    <button type="button" class="cyber-btn cyber-btn-outline me-2" v-on:click="closeModal">
                        <i class="fas fa-times me-1"></i>取消
                    </button>
                    <button type="button" class="cyber-btn cyber-btn-primary" v-on:click="saveDropConfig" v-bind:disabled="loading">
                        <i class="fas fa-save me-1"></i>{{ loading ? '保存中...' : '保存' }}
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
/* 科幻背景 */
.cyber-bg {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
    z-index: -1;
}

.cyber-bg::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%);
    animation: pulse 4s ease-in-out infinite alternate;
}

@@keyframes pulse {
    0% { opacity: 0.5; }
    100% { opacity: 1; }
}

/* 科幻卡片样式 */
.cyber-card {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(102, 126, 234, 0.3);
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
    margin-bottom: 1rem;
}

.cyber-card:hover {
    border-color: rgba(102, 126, 234, 0.6);
    box-shadow: 0 12px 40px rgba(102, 126, 234, 0.2);
    transform: translateY(-2px);
}

.cyber-card-header {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.2) 0%, rgba(118, 75, 162, 0.2) 100%);
    border-bottom: 1px solid rgba(102, 126, 234, 0.3);
    padding: 1rem 1.5rem;
    border-radius: 12px 12px 0 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.cyber-card-title {
    color: #ffffff;
    font-size: 1.1rem;
    font-weight: 600;
    margin: 0;
    display: flex;
    align-items: center;
}

.cyber-icon {
    font-size: 1.2rem;
    margin-right: 0.5rem;
}

.cyber-card-tools {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.cyber-card-body {
    padding: 1.5rem;
    color: #ffffff;
}

/* 科幻按钮样式 */
.cyber-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: 1px solid rgba(102, 126, 234, 0.5);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    font-weight: 500;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    transition: all 0.3s ease;
    cursor: pointer;
}

.cyber-btn:hover {
    background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
    border-color: rgba(102, 126, 234, 0.8);
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.cyber-btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.cyber-btn-info {
    background: linear-gradient(135deg, #11cdef 0%, #1171ef 100%);
    border-color: rgba(17, 205, 239, 0.5);
}

.cyber-btn-info:hover {
    background: linear-gradient(135deg, #1171ef 0%, #11cdef 100%);
    border-color: rgba(17, 205, 239, 0.8);
}

.cyber-btn-warning {
    background: linear-gradient(135deg, #fb6340 0%, #fbb140 100%);
    border-color: rgba(251, 99, 64, 0.5);
}

.cyber-btn-warning:hover {
    background: linear-gradient(135deg, #fbb140 0%, #fb6340 100%);
    border-color: rgba(251, 99, 64, 0.8);
}

.cyber-btn-danger {
    background: linear-gradient(135deg, #f5365c 0%, #f56036 100%);
    border-color: rgba(245, 54, 92, 0.5);
}

.cyber-btn-danger:hover {
    background: linear-gradient(135deg, #f56036 0%, #f5365c 100%);
    border-color: rgba(245, 54, 92, 0.8);
}

.cyber-btn-success {
    background: linear-gradient(135deg, #2dce89 0%, #2dcecc 100%);
    border-color: rgba(45, 206, 137, 0.5);
}

.cyber-btn-success:hover {
    background: linear-gradient(135deg, #2dcecc 0%, #2dce89 100%);
    border-color: rgba(45, 206, 137, 0.8);
}

.cyber-btn-outline {
    background: transparent;
    border: 1px solid rgba(102, 126, 234, 0.5);
    color: #667eea;
}

.cyber-btn-outline:hover {
    background: rgba(102, 126, 234, 0.1);
    border-color: rgba(102, 126, 234, 0.8);
    color: #667eea;
}

.cyber-btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

.cyber-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

/* 科幻表单样式 */
.cyber-label {
    color: #ffffff;
    font-weight: 500;
    margin-bottom: 0.5rem;
    display: block;
}

.cyber-input, .cyber-select, .cyber-textarea {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(102, 126, 234, 0.3);
    border-radius: 8px;
    color: #ffffff;
    padding: 0.5rem 0.75rem;
    width: 100%;
    transition: all 0.3s ease;
}

.cyber-input:focus, .cyber-select:focus, .cyber-textarea:focus {
    outline: none;
    border-color: rgba(102, 126, 234, 0.8);
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    background: rgba(255, 255, 255, 0.15);
}

.cyber-input::placeholder, .cyber-textarea::placeholder {
    color: rgba(255, 255, 255, 0.6);
}

.cyber-select option {
    background: #1a1a2e;
    color: #ffffff;
}

.cyber-form-group {
    margin-bottom: 1rem;
}

.cyber-error {
    color: #f5365c;
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

/* 科幻表格样式 */
.cyber-table-container {
    overflow-x: auto;
    border-radius: 8px;
    border: 1px solid rgba(102, 126, 234, 0.3);
}

.cyber-table {
    width: 100%;
    border-collapse: collapse;
    background: rgba(255, 255, 255, 0.05);
}

.cyber-thead {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.3) 0%, rgba(118, 75, 162, 0.3) 100%);
}

.cyber-th {
    padding: 0.75rem;
    text-align: left;
    font-weight: 600;
    color: #ffffff;
    border-bottom: 1px solid rgba(102, 126, 234, 0.3);
}

.cyber-td {
    padding: 0.75rem;
    border-bottom: 1px solid rgba(102, 126, 234, 0.1);
    color: #ffffff;
}

.cyber-tr:hover .cyber-td {
    background: rgba(102, 126, 234, 0.1);
}

.cyber-table-sm .cyber-th,
.cyber-table-sm .cyber-td {
    padding: 0.5rem;
}

/* 科幻徽章样式 */
.cyber-badge {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    font-weight: 500;
    border-radius: 4px;
    text-align: center;
    white-space: nowrap;
}

.cyber-badge-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.cyber-badge-info {
    background: linear-gradient(135deg, #11cdef 0%, #1171ef 100%);
    color: white;
}

.cyber-badge-success {
    background: linear-gradient(135deg, #2dce89 0%, #2dcecc 100%);
    color: white;
}

.cyber-badge-warning {
    background: linear-gradient(135deg, #fb6340 0%, #fbb140 100%);
    color: white;
}

.cyber-badge-danger {
    background: linear-gradient(135deg, #f5365c 0%, #f56036 100%);
    color: white;
}

.cyber-badge-secondary {
    background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
    color: white;
}

/* 科幻复选框样式 */
.cyber-checkbox {
    appearance: none;
    width: 18px;
    height: 18px;
    border: 2px solid rgba(102, 126, 234, 0.5);
    border-radius: 4px;
    background: transparent;
    cursor: pointer;
    position: relative;
    transition: all 0.3s ease;
}

.cyber-checkbox:checked {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-color: #667eea;
}

.cyber-checkbox:checked::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 12px;
    font-weight: bold;
}

/* 科幻分页样式 */
.cyber-pagination-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.cyber-pagination-info {
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.875rem;
}

.cyber-pagination {
    display: flex;
    gap: 0.25rem;
}

.cyber-page-btn {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(102, 126, 234, 0.3);
    color: #ffffff;
    padding: 0.5rem 0.75rem;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 40px;
    text-align: center;
}

.cyber-page-btn:hover:not(:disabled) {
    background: rgba(102, 126, 234, 0.2);
    border-color: rgba(102, 126, 234, 0.6);
}

.cyber-page-btn.active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-color: #667eea;
}

.cyber-page-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* 科幻模态框样式 */
.cyber-modal {
    background: rgba(26, 26, 46, 0.95);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(102, 126, 234, 0.3);
    border-radius: 12px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
}

.cyber-modal-header {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.3) 0%, rgba(118, 75, 162, 0.3) 100%);
    border-bottom: 1px solid rgba(102, 126, 234, 0.3);
    border-radius: 12px 12px 0 0;
    padding: 1rem 1.5rem;
}

.cyber-modal-title {
    color: #ffffff;
    font-weight: 600;
    margin: 0;
    display: flex;
    align-items: center;
}

.cyber-btn-close {
    background: transparent;
    border: none;
    color: rgba(255, 255, 255, 0.8);
    font-size: 1.2rem;
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.cyber-btn-close:hover {
    color: #ffffff;
    background: rgba(255, 255, 255, 0.1);
}

.cyber-modal-body {
    padding: 1.5rem;
    color: #ffffff;
}

.cyber-modal-footer {
    background: rgba(255, 255, 255, 0.05);
    border-top: 1px solid rgba(102, 126, 234, 0.3);
    border-radius: 0 0 12px 12px;
    padding: 1rem 1.5rem;
    display: flex;
    justify-content: flex-end;
    gap: 0.5rem;
}

/* 科幻文本样式 */
.cyber-text-primary {
    color: #667eea;
}

.cyber-text-muted {
    color: rgba(255, 255, 255, 0.6);
}

/* 确保模态框不被遮挡 */
.modal {
    z-index: 9999 !important;
}

.modal-backdrop {
    display: none !important;
}

.modal-dialog {
    z-index: 10000 !important;
    margin: 30px auto;
}

/* 确保Vue应用不影响页面导航 */
#dropConfigApp {
    position: relative;
    min-height: 100vh;
}

#dropConfigApp * {
    pointer-events: auto;
}

/* 响应式设计 */
@@media (max-width: 768px) {
    .cyber-pagination-container {
        flex-direction: column;
        text-align: center;
    }

    .cyber-card-header {
        flex-direction: column;
        gap: 0.5rem;
        text-align: center;
    }

    .cyber-card-tools {
        justify-content: center;
    }
}
</style>

<script src="~/lib/vue/vue.global.js"></script>
<script src="~/lib/axios.min.js"></script>
<script src="~/lib/jquery/dist/jquery.min.js"></script>
<script src="~/lib/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
<script src="~/plugins/toastr/toastr.min.js"></script>
<script>
try {
    console.log('Vue version:', Vue.version);
    console.log('开始初始化Vue应用...');

    if (typeof Vue === 'undefined') {
        throw new Error('Vue is not loaded');
    }

    const { createApp } = Vue;

    const app = createApp({
        data() {
            return {
                // 数据列表
                dropConfigs: [],
                totalCount: 0,
                currentPage: 1,
                pageSize: 10,
                
                // 搜索表单
                searchForm: {
                    mapId: '',
                    mapName: '',
                    dropType: '',
                    monsterId: '',
                    itemId: '',
                    itemName: ''
                },
                
                // 表单数据
                formData: {
                    id: 0,
                    mapId: '',
                    dropType: '',
                    monsterId: '',
                    remark: '',
                    dropItems: [] // 掉落道具列表
                },
                
                // 表单验证错误
                formErrors: {},
                
                // 选项数据
                mapOptions: [],
                dropTypeOptions: [],
                monsterOptions: [],
                formMonsterOptions: [],
                itemOptions: [],
                
                // 状态
                loading: false,
                isEdit: false,
                selectAll: false,
                selectedIds: [],
                
                // 道具搜索
                itemSearchText: '',
                filteredItemOptions: [],
                
                // 当前选择的道具
                currentItem: {
                    itemId: '',
                    dropRate: 0.1,
                    minCount: 1,
                    maxCount: 1
                }
            }
        },
        
        computed: {
        totalPages() {
            return Math.ceil(this.totalCount / this.pageSize);
        },
        
        visiblePages() {
            const pages = [];
            const start = Math.max(1, this.currentPage - 2);
            const end = Math.min(this.totalPages, this.currentPage + 2);
            
            for (let i = start; i <= end; i++) {
                pages.push(i);
            }
            
            return pages;
                }
        },
        
        mounted() {
            console.log('Vue应用已挂载');
            this.loadOptions();
            this.loadData();
            
            // 检查依赖库是否正确加载
            console.log('jQuery版本:', typeof $ !== 'undefined' ? $.fn.jquery : '未加载');
            console.log('Bootstrap版本:', typeof bootstrap !== 'undefined' ? 'Bootstrap 5' : '未加载或Bootstrap 4');
            
            // 添加ESC键关闭模态框
            document.addEventListener('keydown', (e) => {
                if (e.key === 'Escape' && $('#dropConfigModal').hasClass('show')) {
                    this.closeModal();
                }
            });
        },
        
        methods: {
        // 加载选项数据
        async loadOptions() {
            try {
                const [mapRes, dropTypeRes, itemRes] = await Promise.all([
                    axios.get('/DropConfig/GetMapOptions'),
                    axios.get('/DropConfig/GetDropTypeOptions'),
                    axios.get('/DropConfig/GetItemOptions')
                ]);
                
                this.mapOptions = mapRes.data.data || [];
                this.dropTypeOptions = dropTypeRes.data.data || [];
                this.itemOptions = itemRes.data.data || [];
                this.filteredItemOptions = [...this.itemOptions]; // 初始化过滤后的道具选项
                
                console.log('地图选项:', this.mapOptions);
                console.log('掉落类型选项:', this.dropTypeOptions);
                console.log('道具选项:', this.itemOptions);
            } catch (error) {
                console.error('加载选项失败:', error);
                this.showError('加载选项失败');
            }
        },
        
        // 加载数据
        async loadData() {
            try {
                this.loading = true;
                
                // 构建查询参数
                const queryData = {
                    mapId: this.searchForm.mapId ? parseInt(this.searchForm.mapId) : null,
                    mapName: this.searchForm.mapName || '',
                    dropType: this.searchForm.dropType || '',
                    monsterId: this.searchForm.monsterId ? parseInt(this.searchForm.monsterId) : null,
                    itemId: this.searchForm.itemId || '',
                    itemName: this.searchForm.itemName || '',
                    page: this.currentPage,
                    pageSize: this.pageSize
                };
                
                console.log('发送查询数据:', queryData);
                const response = await axios.post('/DropConfig/GetList', queryData);
                console.log('获取响应数据:', response.data);
                
                if (response.data.code === 200) {
                    this.dropConfigs = response.data.data || [];
                    this.totalCount = response.data.total || 0;
                } else {
                    this.showError(response.data.message);
                }
            } catch (error) {
                console.error('加载数据失败:', error);
                this.showError('加载数据失败');
            } finally {
                this.loading = false;
            }
        },
        
        // 搜索
        search() {
            this.currentPage = 1;
            this.loadData();
        },
        
        // 重置搜索
        reset() {
            this.searchForm = {
                mapId: '',
                mapName: '',
                dropType: '',
                monsterId: '',
                itemId: '',
                itemName: ''
            };
            this.monsterOptions = [];
            this.search();
        },
        
        // 地图变化事件
        async onMapChange() {
            this.searchForm.monsterId = '';
            this.monsterOptions = [];
            
            if (this.searchForm.mapId) {
                await this.loadMonsterOptions(this.searchForm.mapId);
            }
        },
        
        // 表单地图变化事件
        async onFormMapChange() {
            this.formData.monsterId = '';
            this.formMonsterOptions = [];
            
            if (this.formData.mapId) {
                await this.loadFormMonsterOptions(this.formData.mapId);
            }
        },
        
        // 掉落类型变化事件
        onDropTypeChange() {
            if (this.formData.dropType !== '怪物掉落') {
                this.formData.monsterId = '';
            }
        },
        
        // 加载怪物选项
        async loadMonsterOptions(mapId) {
            try {
                const response = await axios.get(`/DropConfig/GetMonsterOptions?mapId=${mapId}`);
                if (response.data.code === 200) {
                    this.monsterOptions = response.data.data || [];
                }
            } catch (error) {
                console.error('加载怪物选项失败:', error);
            }
        },
        
        // 加载表单怪物选项
        async loadFormMonsterOptions(mapId) {
            try {
                const response = await axios.get(`/DropConfig/GetMonsterOptions?mapId=${mapId}`);
                if (response.data.code === 200) {
                    this.formMonsterOptions = response.data.data || [];
                }
            } catch (error) {
                console.error('加载怪物选项失败:', error);
            }
        },
        
        // 翻页
        changePage(page) {
            if (page < 1 || page > this.totalPages || page === this.currentPage) {
                return;
            }
            this.currentPage = page;
            this.loadData();
        },
        
        // 全选/取消全选
        toggleSelectAll() {
            if (this.selectAll) {
                this.selectedIds = this.dropConfigs.map(item => item.id);
            } else {
                this.selectedIds = [];
            }
        },
        
        // 打开新增模态框
        openCreateModal() {
            console.log('打开新增模态框');
            this.isEdit = false;
            this.formData = {
                id: 0,
                mapId: '',
                dropType: '',
                monsterId: '',
                remark: '',
                dropItems: []
            };
            this.currentItem = {
                itemId: '',
                dropRate: 0.1,
                minCount: 1,
                maxCount: 1
            };
            this.formErrors = {};
            this.formMonsterOptions = [];
            this.itemSearchText = '';
            this.filteredItemOptions = [...this.itemOptions];
            
            // 使用原生Bootstrap方式打开模态框
            const modalElement = document.getElementById('dropConfigModal');
            if (modalElement) {
                // 确保Bootstrap已加载
                if (typeof bootstrap !== 'undefined') {
                    const modal = new bootstrap.Modal(modalElement, {
                        backdrop: false,
                        keyboard: true
                    });
                    modal.show();
                } else {
                    // 回退到jQuery方式
            $('#dropConfigModal').modal({
                show: true,
                backdrop: false,
                keyboard: true
            });
                }
            } else {
                console.error('找不到模态框元素');
            }
        },
        
        // 打开编辑模态框
        async openUpdateModal(item) {
            console.log('打开编辑模态框, 原始数据:', item);
            
            // 确保道具选项已加载
            if (!this.itemOptions || this.itemOptions.length === 0) {
                console.log('重新加载道具选项...');
                await this.loadOptions();
            }
            
            this.isEdit = true;
            
            // 根据数据类型决定如何加载掉落道具
            let dropItems = [];
            if (item.dropItemsJson) {
                // 新格式：多道具JSON
                try {
                    dropItems = this.parseDropItems(item.dropItemsJson);
                    console.log('解析多道具配置成功:', dropItems);
                } catch (error) {
                    console.error('解析多道具配置失败:', error);
                    dropItems = [];
                }
            } else if (item.itemId) {
                // 旧格式：单道具
                dropItems = [{
                    itemId: item.itemId.toString(), // 确保为字符串
                    dropRate: parseFloat(item.dropRate) || 0.1,
                    minCount: parseInt(item.minCount) || 1,
                    maxCount: parseInt(item.maxCount) || 1
                }];
                console.log('转换单道具配置:', dropItems);
            }
            
            // 确保mapId和monsterId为字符串（用于select绑定）
            this.formData = {
                id: item.id,
                mapId: item.mapId ? item.mapId.toString() : '',
                dropType: item.dropType || '',
                monsterId: item.monsterId ? item.monsterId.toString() : '',
                remark: item.remark || '',
                dropItems: dropItems
            };
            
            console.log('设置表单数据:', this.formData);
            console.log('当前地图选项:', this.mapOptions);
            console.log('当前道具选项数量:', this.itemOptions.length);
            
            this.currentItem = {
                itemId: '',
                dropRate: 0.1,
                minCount: 1,
                maxCount: 1
            };
            this.formErrors = {};
            
            // 加载对应地图的怪物选项
            if (this.formData.mapId) {
                await this.loadFormMonsterOptions(this.formData.mapId);
            }
            
            this.itemSearchText = '';
            this.filteredItemOptions = [...this.itemOptions];
            
            // 等待DOM更新
            await new Promise(resolve => setTimeout(resolve, 100));
            
            // 使用原生Bootstrap方式打开模态框
            const modalElement = document.getElementById('dropConfigModal');
            if (modalElement) {
                // 确保Bootstrap已加载
                if (typeof bootstrap !== 'undefined') {
                    const modal = new bootstrap.Modal(modalElement, {
                        backdrop: false,
                        keyboard: true
                    });
                    modal.show();
                } else {
                    // 回退到jQuery方式
            $('#dropConfigModal').modal({
                show: true,
                backdrop: false,
                keyboard: true
            });
                }
            } else {
                console.error('找不到模态框元素');
            }
        },
        
        // 关闭模态框
        closeModal() {
            const modalElement = document.getElementById('dropConfigModal');
            if (modalElement) {
                // 使用原生Bootstrap方式关闭模态框
                if (typeof bootstrap !== 'undefined') {
                    const modal = bootstrap.Modal.getInstance(modalElement);
                    if (modal) {
                        modal.hide();
                    }
                } else {
                    // 回退到jQuery方式
            $('#dropConfigModal').modal('hide');
                }
            }
            
            // 清除可能残留的backdrop
            $('.modal-backdrop').remove();
            $('body').removeClass('modal-open');
        },
        
        // 保存掉落配置
        async saveDropConfig() {
            if (!this.validateForm()) {
                return;
            }
            
            try {
                this.loading = true;
                const url = this.isEdit ? '/DropConfig/UpdateMulti' : '/DropConfig/CreateMulti';
                
                // 构建保存数据，确保数据类型正确
                const saveData = {
                    id: this.formData.id || 0,
                    mapId: this.formData.mapId ? parseInt(this.formData.mapId) : 0,
                    dropType: this.formData.dropType,
                    monsterId: this.formData.monsterId && this.formData.monsterId !== '' ? parseInt(this.formData.monsterId) : null,
                    dropItems: this.formData.dropItems.map(item => ({
                        itemId: item.itemId,
                        dropRate: parseFloat(item.dropRate),
                        minCount: parseInt(item.minCount),
                        maxCount: parseInt(item.maxCount)
                    })),
                    remark: this.formData.remark || ''
                };
                
                console.log('发送保存数据:', saveData);
                const response = await axios.post(url, saveData);
                console.log('保存响应:', response.data);
                
                if (response.data.code === 200) {
                    this.showSuccess(response.data.message);
                    this.closeModal();
                    this.loadData();
                } else {
                    this.showError(response.data.message);
                }
            } catch (error) {
                console.error('保存失败:', error);
                this.showError('保存失败');
            } finally {
                this.loading = false;
            }
        },
        
        // 表单验证
        validateForm() {
            this.formErrors = {};
            
            if (!this.formData.mapId) {
                this.formErrors.mapId = '请选择地图';
            }
            
            if (!this.formData.dropType) {
                this.formErrors.dropType = '请选择掉落类型';
            }
            
            if (this.formData.dropType === '怪物掉落' && !this.formData.monsterId) {
                this.formErrors.monsterId = '请选择怪物';
            }
            
            if (!this.formData.dropItems || this.formData.dropItems.length === 0) {
                this.formErrors.dropItems = '请至少添加一个掉落道具';
            }
            
            return Object.keys(this.formErrors).length === 0;
        },
        
        // 删除单个项目
        async deleteItem(id) {
            if (!confirm('确定要删除这个掉落配置吗？')) {
                return;
            }
            
            try {
                const response = await axios.post('/DropConfig/Delete', { id: id });
                if (response.data.code === 200) {
                    this.showSuccess(response.data.message);
                    this.loadData();
                } else {
                    this.showError(response.data.message);
                }
            } catch (error) {
                console.error('删除失败:', error);
                this.showError('删除失败');
            }
        },
        
        // 批量删除
        async batchDelete() {
            if (this.selectedIds.length === 0) {
                this.showWarning('请选择要删除的项目');
                return;
            }
            
            if (!confirm(`确定要删除选中的 ${this.selectedIds.length} 个掉落配置吗？`)) {
                return;
            }
            
            try {
                const response = await axios.post('/DropConfig/BatchDelete', this.selectedIds);
                if (response.data.code === 200) {
                    this.showSuccess(response.data.message);
                    this.selectedIds = [];
                    this.selectAll = false;
                    this.loadData();
                } else {
                    this.showError(response.data.message);
                }
            } catch (error) {
                console.error('批量删除失败:', error);
                this.showError('批量删除失败');
            }
        },
        
        // 格式化日期时间
        formatDateTime(dateTime) {
            if (!dateTime) return '-';
            const date = new Date(dateTime);
            return date.toLocaleString('zh-CN');
        },

        // 获取掉落类型的样式类
        getDropTypeClass(dropType) {
            switch(dropType) {
                case '怪物掉落':
                    return 'cyber-badge-danger';
                case '地图掉落':
                    return 'cyber-badge-info';
                case '宝箱掉落':
                    return 'cyber-badge-warning';
                default:
                    return 'cyber-badge-secondary';
            }
        },
        
        // 显示成功消息
        showSuccess(message) {
            toastr.success(message);
        },
        
        // 显示错误消息
        showError(message) {
            toastr.error(message);
        },
        
        // 显示警告消息
        showWarning(message) {
            toastr.warning(message);
        },
        
        // 过滤道具选项
        filterItems() {
            if (!this.itemSearchText.trim()) {
                this.filteredItemOptions = [...this.itemOptions];
            } else {
                const searchText = this.itemSearchText.toLowerCase().trim();
                this.filteredItemOptions = this.itemOptions.filter(item => 
                    item.label.toLowerCase().includes(searchText) || 
                    item.value.toString().includes(searchText)
                );
            }
        },
        
        // 添加掉落道具
        addDropItem() {
            if (!this.currentItem.itemId) {
                this.showWarning('请选择道具');
                return;
            }
            
            // 检查道具是否已经添加
            if (this.formData.dropItems.some(item => item.itemId === this.currentItem.itemId)) {
                this.showWarning('该道具已经添加，请选择其他道具');
                return;
            }
            
            // 验证当前道具的数据
            if (!this.currentItem.dropRate || this.currentItem.dropRate <= 0 || this.currentItem.dropRate > 1) {
                this.showWarning('掉落概率必须在0-1之间');
                return;
            }
            
            if (!this.currentItem.minCount || this.currentItem.minCount < 1) {
                this.showWarning('最小掉落数量必须大于0');
                return;
            }
            
            if (!this.currentItem.maxCount || this.currentItem.maxCount < 1) {
                this.showWarning('最大掉落数量必须大于0');
                return;
            }
            
            if (this.currentItem.minCount > this.currentItem.maxCount) {
                this.showWarning('最大数量不能小于最小数量');
                return;
            }
            
            // 添加到掉落道具列表
            this.formData.dropItems.push({
                itemId: this.currentItem.itemId,
                dropRate: this.currentItem.dropRate,
                minCount: this.currentItem.minCount,
                maxCount: this.currentItem.maxCount
            });
            
            // 重置当前道具选择
            this.currentItem = {
                itemId: '',
                dropRate: 0.1,
                minCount: 1,
                maxCount: 1
            };
            
            this.showSuccess('道具添加成功');
        },
        
        // 移除掉落道具
        removeDropItem(index) {
            if (index >= 0 && index < this.formData.dropItems.length) {
                this.formData.dropItems.splice(index, 1);
                this.showSuccess('道具移除成功');
            }
        },
        
        // 获取道具名称
        getItemName(itemId) {
            if (!itemId) {
                console.warn('getItemName: itemId为空');
                return '未知道具';
            }
            
            if (!this.itemOptions || this.itemOptions.length === 0) {
                console.warn('getItemName: itemOptions未加载', itemId);
                return `道具${itemId}`;
            }
            
            const item = this.itemOptions.find(option => option.value === itemId || option.value === itemId.toString());
            if (!item) {
                console.warn('getItemName: 找不到道具', itemId, this.itemOptions.length);
                return `道具${itemId}`;
            }
            
            return item.label || `道具${itemId}`;
        },
        
        // 解析掉落道具JSON
        parseDropItems(dropItemsJson) {
            if (!dropItemsJson) return [];
            try {
                const items = JSON.parse(dropItemsJson);
                // 确保数据格式正确
                return items.map(item => ({
                    itemId: item.ItemId || item.itemId || '',
                    dropRate: parseFloat(item.DropRate || item.dropRate || 0.1),
                    minCount: parseInt(item.MinCount || item.minCount || 1),
                    maxCount: parseInt(item.MaxCount || item.maxCount || 1)
                }));
            } catch (error) {
                console.error('解析掉落道具失败:', error);
                return [];
            }
        }
    },
    
        watch: {
            selectedIds() {
                this.selectAll = this.selectedIds.length === this.dropConfigs.length && this.dropConfigs.length > 0;
            }
        }
});

    console.log('Vue应用创建成功，准备挂载...');
    app.mount('#dropConfigApp');
    console.log('Vue应用挂载完成');
    
    // 确保页面导航正常工作
    document.addEventListener('click', function(e) {
        // 允许面包屑导航和其他普通链接正常工作
        if (e.target.tagName === 'A' && e.target.getAttribute('href') && 
            !e.target.getAttribute('href').startsWith('javascript:') && 
            e.target.getAttribute('href') !== '#') {
            // 对于有实际href的链接，允许正常跳转
            return true;
        }
    }, true);

} catch (error) {
    console.error('Vue应用初始化失败:', error);
    document.getElementById('dropConfigApp').innerHTML = '<div class="alert alert-danger">Vue应用加载失败: ' + error.message + '</div>';
}
</script>