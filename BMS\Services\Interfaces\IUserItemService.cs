using BMS.Models.Common;
using BMS.Models.DTOs;

namespace BMS.Services.Interfaces
{
    /// <summary>
    /// 用户道具服务接口
    /// </summary>
    public interface IUserItemService
    {
        /// <summary>
        /// 分页查询用户道具列表
        /// </summary>
        /// <param name="queryDto">查询条件</param>
        /// <returns>分页结果</returns>
        Task<PagedResult<UserItemDto>> GetUserItemListAsync(UserItemQueryDto queryDto);

        /// <summary>
        /// 根据ID获取用户道具
        /// </summary>
        /// <param name="id">记录ID</param>
        /// <returns>用户道具信息</returns>
        Task<UserItemDto?> GetUserItemByIdAsync(int id);

        /// <summary>
        /// 添加用户道具
        /// </summary>
        /// <param name="addDto">添加信息</param>
        /// <returns>是否成功</returns>
        Task<bool> AddUserItemAsync(UserItemAddDto addDto);

        /// <summary>
        /// 更新用户道具
        /// </summary>
        /// <param name="updateDto">更新信息</param>
        /// <returns>是否成功</returns>
        Task<bool> UpdateUserItemAsync(UserItemUpdateDto updateDto);

        /// <summary>
        /// 删除用户道具
        /// </summary>
        /// <param name="deleteDto">删除信息</param>
        /// <returns>是否成功</returns>
        Task<bool> DeleteUserItemAsync(UserItemDeleteDto deleteDto);

        /// <summary>
        /// 获取用户道具统计信息
        /// </summary>
        /// <returns>统计信息</returns>
        Task<UserItemStatisticsDto> GetUserItemStatisticsAsync();

        /// <summary>
        /// 获取所有道具配置
        /// </summary>
        /// <returns>道具配置列表</returns>
        Task<List<ItemConfigDto>> GetAllItemConfigsAsync();

        /// <summary>
        /// 验证道具是否存在
        /// </summary>
        /// <param name="itemNo">道具编号</param>
        /// <returns>是否存在</returns>
        Task<bool> IsItemExistAsync(int itemNo);

        /// <summary>
        /// 批量删除用户道具
        /// </summary>
        /// <param name="ids">记录ID列表</param>
        /// <returns>是否成功</returns>
        Task<bool> BatchDeleteUserItemsAsync(List<int> ids);

        /// <summary>
        /// 根据用户ID获取道具列表
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>道具列表</returns>
        Task<List<UserItemDto>> GetUserItemsByUserIdAsync(int userId);
    }
} 