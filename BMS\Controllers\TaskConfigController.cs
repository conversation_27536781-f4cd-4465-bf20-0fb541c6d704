using Microsoft.AspNetCore.Mvc;
using BMS.Services.Interfaces;
using BMS.Models.DTOs;
using BMS.Models.Common;

namespace BMS.Controllers
{
    /// <summary>
    /// 任务配置管理控制器
    /// </summary>
    public class TaskConfigController : Controller
    {
        private readonly ITaskConfigService _taskConfigService;
        private readonly IMonsterConfigService _monsterConfigService;

        public TaskConfigController(ITaskConfigService taskConfigService, IMonsterConfigService monsterConfigService)
        {
            _taskConfigService = taskConfigService;
            _monsterConfigService = monsterConfigService;
        }

        /// <summary>
        /// 任务配置管理页面
        /// </summary>
        public IActionResult Index()
        {
            return View();
        }

        /// <summary>
        /// 获取任务配置列表
        /// </summary>
        [HttpPost("TaskConfig/GetList")]
        public async Task<IActionResult> GetList([FromBody] TaskConfigQueryDto? queryDto)
        {
            try
            {
                Console.WriteLine($"TaskConfig.GetList 开始，参数: {System.Text.Json.JsonSerializer.Serialize(queryDto)}");

                if (queryDto == null)
                {
                    queryDto = new TaskConfigQueryDto { Page = 1, PageSize = 10 };
                }

                // 详细输出各个参数
                Console.WriteLine($"参数详情 - TaskName: '{queryDto.TaskName}', TaskType: {queryDto.TaskType}");
                Console.WriteLine($"参数详情 - IsActive: {queryDto.IsActive}, IsRepeatable: {queryDto.IsRepeatable}");
                Console.WriteLine($"参数详情 - Page: {queryDto.Page}, PageSize: {queryDto.PageSize}");

                var result = await _taskConfigService.GetTaskConfigsAsync(queryDto);

                Console.WriteLine($"查询成功，返回 {result.Data?.Count ?? 0} 条记录");
                return Json(new { code = 200, data = result.Data, total = result.TotalCount });
            }
            catch (Exception ex)
            {
                Console.WriteLine($"TaskConfig.GetList 异常: {ex.Message}");
                return Json(new { code = 500, message = $"获取任务配置列表失败：{ex.Message}" });
            }
        }

        /// <summary>
        /// 根据ID获取任务配置
        /// </summary>
        [HttpGet("TaskConfig/GetById/{taskId}")]
        public async Task<IActionResult> GetById(string taskId)
        {
            try
            {
                Console.WriteLine($"TaskConfig.GetById 开始，taskId: {taskId}");

                var result = await _taskConfigService.GetTaskConfigByIdAsync(taskId);
                if (result == null)
                {
                    return Json(new { code = 404, message = "任务配置不存在" });
                }

                return Json(new { code = 200, data = result });
            }
            catch (Exception ex)
            {
                Console.WriteLine($"TaskConfig.GetById 异常: {ex.Message}");
                return Json(new { code = 500, message = $"获取任务配置失败：{ex.Message}" });
            }
        }

        /// <summary>
        /// 创建任务配置
        /// </summary>
        [HttpPost("TaskConfig/Create")]
        public async Task<IActionResult> Create([FromBody] TaskConfigCreateDto? createDto)
        {
            try
            {
                Console.WriteLine($"TaskConfig.Create 开始，参数: {System.Text.Json.JsonSerializer.Serialize(createDto)}");

                if (createDto == null)
                {
                    return Json(new { code = 400, message = "请求参数不能为空" });
                }

                if (!ModelState.IsValid)
                {
                    var errors = ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage);
                    return Json(new { code = 400, message = string.Join(", ", errors) });
                }

                var result = await _taskConfigService.CreateTaskConfigAsync(createDto);
                return Json(result.Success ? 
                    new { code = 200, data = result.Data, message = result.Message } : 
                    new { code = 400, message = result.Message });
            }
            catch (Exception ex)
            {
                Console.WriteLine($"TaskConfig.Create 异常: {ex.Message}");
                return Json(new { code = 500, message = $"创建任务配置失败：{ex.Message}" });
            }
        }

        /// <summary>
        /// 更新任务配置
        /// </summary>
        [HttpPost("TaskConfig/Update")]
        public async Task<IActionResult> Update([FromBody] TaskConfigCreateDto? updateDto)
        {
            try
            {
                Console.WriteLine($"TaskConfig.Update 开始，参数: {System.Text.Json.JsonSerializer.Serialize(updateDto)}");

                if (updateDto == null)
                {
                    return Json(new { code = 400, message = "请求参数不能为空" });
                }

                if (!ModelState.IsValid)
                {
                    var errors = ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage);
                    return Json(new { code = 400, message = string.Join(", ", errors) });
                }

                if (string.IsNullOrEmpty(updateDto.TaskId))
                {
                    return Json(new { code = 400, message = "任务ID不能为空" });
                }

                var result = await _taskConfigService.UpdateTaskConfigAsync(updateDto.TaskId, updateDto);
                return Json(result.Success ? 
                    new { code = 200, data = result.Data, message = result.Message } : 
                    new { code = 400, message = result.Message });
            }
            catch (Exception ex)
            {
                Console.WriteLine($"TaskConfig.Update 异常: {ex.Message}");
                return Json(new { code = 500, message = $"更新任务配置失败：{ex.Message}" });
            }
        }

        /// <summary>
        /// 删除任务配置
        /// </summary>
        [HttpPost("TaskConfig/Delete")]
        public async Task<IActionResult> Delete([FromBody] TaskConfigDeleteDto? deleteDto)
        {
            try
            {
                Console.WriteLine($"TaskConfig.Delete 开始，参数: {System.Text.Json.JsonSerializer.Serialize(deleteDto)}");

                if (deleteDto == null)
                {
                    return Json(new { code = 400, message = "请求参数不能为空" });
                }

                if (!ModelState.IsValid)
                {
                    var errors = ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage);
                    return Json(new { code = 400, message = string.Join(", ", errors) });
                }

                if (string.IsNullOrEmpty(deleteDto.TaskId))
                {
                    return Json(new { code = 400, message = "任务ID不能为空" });
                }

                var result = await _taskConfigService.DeleteTaskConfigAsync(deleteDto.TaskId);
                return Json(result.Success ?
                    new { code = 200, message = result.Message } :
                    new { code = 400, message = result.Message });
            }
            catch (Exception ex)
            {
                Console.WriteLine($"TaskConfig.Delete 异常: {ex.Message}");
                return Json(new { code = 500, message = $"删除任务配置失败：{ex.Message}" });
            }
        }

        /// <summary>
        /// 启用/禁用任务
        /// </summary>
        [HttpPost("TaskConfig/ToggleActive")]
        public async Task<IActionResult> ToggleActive([FromBody] TaskConfigToggleActiveDto? toggleDto)
        {
            try
            {
                Console.WriteLine($"TaskConfig.ToggleActive 开始，参数: {System.Text.Json.JsonSerializer.Serialize(toggleDto)}");

                if (toggleDto == null)
                {
                    return Json(new { code = 400, message = "请求参数不能为空" });
                }

                if (!ModelState.IsValid)
                {
                    var errors = ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage);
                    return Json(new { code = 400, message = string.Join(", ", errors) });
                }

                if (string.IsNullOrEmpty(toggleDto.TaskId))
                {
                    return Json(new { code = 400, message = "任务ID不能为空" });
                }

                var result = await _taskConfigService.ToggleTaskActiveAsync(toggleDto.TaskId, toggleDto.IsActive);
                return Json(result.Success ?
                    new { code = 200, message = result.Message } :
                    new { code = 400, message = result.Message });
            }
            catch (Exception ex)
            {
                Console.WriteLine($"TaskConfig.ToggleActive 异常: {ex.Message}");
                return Json(new { code = 500, message = $"切换任务状态失败：{ex.Message}" });
            }
        }

        /// <summary>
        /// 获取任务类型选项
        /// </summary>
        [HttpGet("TaskConfig/GetTaskTypeOptions")]
        public async Task<IActionResult> GetTaskTypeOptions()
        {
            try
            {
                var options = await _taskConfigService.GetTaskTypeOptionsAsync();
                return Json(new { code = 200, data = options });
            }
            catch (Exception ex)
            {
                Console.WriteLine($"TaskConfig.GetTaskTypeOptions 异常: {ex.Message}");
                return Json(new { code = 500, message = $"获取任务类型选项失败：{ex.Message}" });
            }
        }

        /// <summary>
        /// 获取任务选项
        /// </summary>
        [HttpGet("TaskConfig/GetTaskOptions")]
        public async Task<IActionResult> GetTaskOptions()
        {
            try
            {
                var options = await _taskConfigService.GetTaskOptionsAsync();
                return Json(new { code = 200, data = options });
            }
            catch (Exception ex)
            {
                Console.WriteLine($"TaskConfig.GetTaskOptions 异常: {ex.Message}");
                return Json(new { code = 500, message = $"获取任务选项失败：{ex.Message}" });
            }
        }

        /// <summary>
        /// 获取目标类型选项
        /// </summary>
        [HttpGet("TaskConfig/GetObjectiveTypeOptions")]
        public async Task<IActionResult> GetObjectiveTypeOptions()
        {
            try
            {
                var options = await _taskConfigService.GetObjectiveTypeOptionsAsync();
                return Json(new { code = 200, data = options });
            }
            catch (Exception ex)
            {
                Console.WriteLine($"TaskConfig.GetObjectiveTypeOptions 异常: {ex.Message}");
                return Json(new { code = 500, message = $"获取目标类型选项失败：{ex.Message}" });
            }
        }

        /// <summary>
        /// 执行数据库迁移 - 添加target_name字段
        /// </summary>
        [HttpPost("TaskConfig/MigrateTargetName")]
        public async Task<IActionResult> MigrateTargetName()
        {
            try
            {
                var result = await _taskConfigService.MigrateTargetNameFieldAsync();
                return Json(result.Success ?
                    new { code = 200, message = result.Message } :
                    new { code = 400, message = result.Message });
            }
            catch (Exception ex)
            {
                Console.WriteLine($"TaskConfig.MigrateTargetName 异常: {ex.Message}");
                return Json(new { code = 500, message = $"迁移失败：{ex.Message}" });
            }
        }

        /// <summary>
        /// 获取怪物选项列表
        /// </summary>
        [HttpGet("TaskConfig/GetMonsterOptions")]
        public async Task<IActionResult> GetMonsterOptions()
        {
            try
            {
                var options = await _monsterConfigService.GetMonsterOptionsAsync();
                return Json(new { code = 200, data = options });
            }
            catch (Exception ex)
            {
                Console.WriteLine($"TaskConfig.GetMonsterOptions 异常: {ex.Message}");
                return Json(new { code = 500, message = $"获取怪物选项失败：{ex.Message}" });
            }
        }
    }
}
