@{
    ViewData["Title"] = "宠物合成公式管理";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<style>
    /* 强制应用科幻主题 */
    #petSynthesisFormulaApp .cyber-card {
        background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%) !important;
        border: 1px solid rgba(255, 255, 255, 0.1) !important;
        color: #e2e8f0 !important;
        border-radius: 12px !important;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3) !important;
        margin-bottom: 1.5rem !important;
    }

    #petSynthesisFormulaApp .cyber-card-header {
        background: linear-gradient(135deg, rgba(0, 212, 255, 0.1), rgba(139, 92, 246, 0.1)) !important;
        border-bottom: 1px solid rgba(0, 212, 255, 0.3) !important;
        padding: 1rem 1.5rem !important;
        border-radius: 12px 12px 0 0 !important;
        display: flex !important;
        align-items: center !important;
        gap: 0.75rem !important;
    }

    #petSynthesisFormulaApp .cyber-card-body {
        padding: 1.5rem !important;
    }

    #petSynthesisFormulaApp .cyber-form-control {
        background: rgba(26, 26, 46, 0.8) !important;
        border: 1px solid rgba(255, 255, 255, 0.2) !important;
        color: #e2e8f0 !important;
        border-radius: 8px !important;
        padding: 0.75rem 1rem !important;
        transition: all 0.3s ease !important;
    }

    #petSynthesisFormulaApp .cyber-form-control:focus {
        border-color: #00d4ff !important;
        box-shadow: 0 0 0 0.2rem rgba(0, 212, 255, 0.25) !important;
        background: rgba(26, 26, 46, 0.9) !important;
    }

    #petSynthesisFormulaApp .cyber-btn {
        background: linear-gradient(135deg, #00d4ff, #8b5cf6) !important;
        border: none !important;
        color: white !important;
        padding: 0.75rem 1.5rem !important;
        border-radius: 8px !important;
        font-weight: 500 !important;
        transition: all 0.3s ease !important;
    }

    #petSynthesisFormulaApp .cyber-btn:hover {
        transform: translateY(-2px) !important;
        box-shadow: 0 8px 25px rgba(0, 212, 255, 0.3) !important;
    }

    #petSynthesisFormulaApp .cyber-table {
        background: transparent !important;
        color: #e2e8f0 !important;
        border-radius: 8px !important;
        overflow: hidden !important;
    }

    #petSynthesisFormulaApp .cyber-table th {
        background: linear-gradient(135deg, #1a1a2e, #16213e) !important;
        border-bottom: 1px solid rgba(0, 212, 255, 0.3) !important;
        color: #e2e8f0 !important;
        padding: 1rem !important;
        font-weight: 600 !important;
    }

    #petSynthesisFormulaApp .cyber-table td {
        border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
        color: #e2e8f0 !important;
        padding: 0.75rem 1rem !important;
    }

    #petSynthesisFormulaApp .cyber-table tbody tr:hover {
        background: rgba(0, 212, 255, 0.1) !important;
    }

    /* 科幻模态框样式 */
    .cyber-modal .modal-content {
        background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%) !important;
        border: 1px solid rgba(255, 255, 255, 0.1) !important;
        color: #e2e8f0 !important;
        border-radius: 12px !important;
    }

    .cyber-modal .modal-header {
        background: linear-gradient(135deg, rgba(0, 212, 255, 0.1), rgba(139, 92, 246, 0.1)) !important;
        border-bottom: 1px solid rgba(0, 212, 255, 0.3) !important;
        color: #e2e8f0 !important;
    }

    .cyber-modal .modal-body {
        background: transparent !important;
        color: #e2e8f0 !important;
    }

    .cyber-modal .modal-footer {
        background: transparent !important;
        border-top: 1px solid rgba(255, 255, 255, 0.1) !important;
    }

    /* 科幻分页样式 */
    #petSynthesisFormulaApp .cyber-pagination .page-link {
        background: rgba(26, 26, 46, 0.8) !important;
        border: 1px solid rgba(255, 255, 255, 0.2) !important;
        color: #e2e8f0 !important;
        margin: 0 2px !important;
        border-radius: 6px !important;
    }

    #petSynthesisFormulaApp .cyber-pagination .page-link:hover {
        background: rgba(0, 212, 255, 0.2) !important;
        border-color: #00d4ff !important;
        color: #00d4ff !important;
    }

    #petSynthesisFormulaApp .cyber-pagination .page-item.active .page-link {
        background: linear-gradient(135deg, #00d4ff, #8b5cf6) !important;
        border-color: #00d4ff !important;
        color: white !important;
    }

    /* 科幻徽章样式 */
    #petSynthesisFormulaApp .cyber-badge {
        background: linear-gradient(135deg, #00d4ff, #8b5cf6) !important;
        color: white !important;
        padding: 0.25rem 0.75rem !important;
        border-radius: 12px !important;
        font-size: 0.75rem !important;
        font-weight: 500 !important;
    }

    #petSynthesisFormulaApp .cyber-badge-success {
        background: linear-gradient(135deg, #10b981, #059669) !important;
    }

    #petSynthesisFormulaApp .cyber-badge-warning {
        background: linear-gradient(135deg, #f59e0b, #d97706) !important;
    }

    #petSynthesisFormulaApp .cyber-badge-danger {
        background: linear-gradient(135deg, #ef4444, #dc2626) !important;
    }

    #petSynthesisFormulaApp .cyber-badge-info {
        background: linear-gradient(135deg, #3b82f6, #2563eb) !important;
    }

    /* 科幻表单标签样式 */
    #petSynthesisFormulaApp .cyber-form-label {
        color: #e2e8f0 !important;
        font-weight: 500 !important;
        margin-bottom: 0.5rem !important;
    }

    #petSynthesisFormulaApp .cyber-form-group {
        margin-bottom: 1rem !important;
    }

    /* 科幻图标样式 */
    #petSynthesisFormulaApp .cyber-icon {
        color: #00d4ff !important;
        font-size: 1.25rem !important;
    }

    #petSynthesisFormulaApp .cyber-card-title {
        color: #e2e8f0 !important;
        margin: 0 !important;
        font-weight: 600 !important;
    }

    /* 科幻按钮变体 */
    #petSynthesisFormulaApp .cyber-btn-outline {
        background: transparent !important;
        border: 1px solid rgba(0, 212, 255, 0.5) !important;
        color: #00d4ff !important;
    }

    #petSynthesisFormulaApp .cyber-btn-outline:hover {
        background: rgba(0, 212, 255, 0.1) !important;
        border-color: #00d4ff !important;
    }

    #petSynthesisFormulaApp .cyber-btn-success {
        background: linear-gradient(135deg, #10b981, #059669) !important;
    }

    #petSynthesisFormulaApp .cyber-btn-warning {
        background: linear-gradient(135deg, #f59e0b, #d97706) !important;
    }

    #petSynthesisFormulaApp .cyber-btn-danger {
        background: linear-gradient(135deg, #ef4444, #dc2626) !important;
    }

    #petSynthesisFormulaApp .cyber-btn-info {
        background: linear-gradient(135deg, #3b82f6, #2563eb) !important;
    }

    #petSynthesisFormulaApp .cyber-btn-sm {
        padding: 0.5rem 1rem !important;
        font-size: 0.875rem !important;
    }
</style>

<!-- 科幻背景 -->
<div class="cyber-bg"></div>


</style>

<div class="container-fluid p-4">
    <div id="petSynthesisFormulaApp">
        <!-- 科幻页面标题 -->
        <div class="cyber-card mb-4">
            <div class="cyber-card-header">
                <div class="cyber-icon">
                    <i class="fas fa-flask"></i>
                </div>
                <h1 class="cyber-card-title">宠物合成公式管理</h1>
            </div>
        </div>

        <!-- 科幻搜索条件 -->
        <div class="cyber-card">
            <div class="cyber-card-header">
                <div class="cyber-icon">
                    <i class="fas fa-search"></i>
                </div>
                <h5 class="cyber-card-title">搜索筛选</h5>
            </div>
            <div class="cyber-card-body">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <div class="cyber-form-group">
                            <label class="cyber-form-label">主宠物</label>
                            <select v-model="queryForm.mainPetNo" class="cyber-form-control" id="mainPetSelect">
                                <option value="">请选择主宠物</option>
                                <option v-for="pet in petOptions" v-bind:key="pet.value" v-bind:value="pet.value">
                                    {{ pet.label }}
                                </option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="cyber-form-group">
                            <label class="cyber-form-label">副宠物</label>
                            <select v-model="queryForm.subPetNo" class="cyber-form-control" id="subPetSelect">
                                <option value="">请选择副宠物</option>
                                <option v-for="pet in petOptions" v-bind:key="pet.value" v-bind:value="pet.value">
                                    {{ pet.label }}
                                </option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="cyber-form-group">
                            <label class="cyber-form-label">结果宠物</label>
                            <select v-model="queryForm.resultPetNo" class="cyber-form-control" id="resultPetSelect">
                                <option value="">请选择结果宠物</option>
                                <option v-for="pet in petOptions" v-bind:key="pet.value" v-bind:value="pet.value">
                                    {{ pet.label }}
                                </option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="cyber-form-group">
                            <label class="cyber-form-label">公式类型</label>
                            <select v-model="queryForm.formulaType" class="cyber-form-control">
                                <option value="">全部类型</option>
                                <option value="FIXED">固定公式</option>
                                <option value="RANDOM_GOD">随机神宠</option>
                                <option value="RANDOM_HOLY">随机神圣宠物</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <div class="cyber-form-group">
                            <label class="cyber-form-label">激活状态</label>
                            <select v-model="queryForm.isActive" class="cyber-form-control">
                                <option value="">全部状态</option>
                                <option v-bind:value="true">已激活</option>
                                <option v-bind:value="false">已禁用</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="cyber-form-group">
                            <label class="cyber-form-label">最小等级</label>
                            <input type="number" v-model="queryForm.minRequiredLevel" class="cyber-form-control" placeholder="最小等级要求">
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="cyber-form-group">
                            <label class="cyber-form-label">最大等级</label>
                            <input type="number" v-model="queryForm.maxRequiredLevel" class="cyber-form-control" placeholder="最大等级要求">
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="cyber-form-group">
                            <label class="cyber-form-label">&nbsp;</label>
                            <div class="d-flex gap-2">
                                <button v-on:click="searchFormulas" class="cyber-btn">
                                    <i class="fas fa-search"></i> 搜索
                                </button>
                                <button v-on:click="resetSearch" class="cyber-btn cyber-btn-outline">
                                    <i class="fas fa-undo"></i> 重置
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 科幻数据列表 -->
        <div class="cyber-card">
            <div class="cyber-card-header">
                <div class="cyber-icon">
                    <i class="fas fa-list"></i>
                </div>
                <h5 class="cyber-card-title">合成公式列表</h5>
                <div class="ms-auto d-flex gap-2">
                    <span class="cyber-badge cyber-badge-info">共 {{ totalCount }} 条</span>
                    <button v-on:click="showCreateModal" class="cyber-btn cyber-btn-success cyber-btn-sm">
                        <i class="fas fa-plus"></i> 新增公式
                    </button>
                    <button v-on:click="batchDelete" v-bind:disabled="selectedIds.length === 0" class="cyber-btn cyber-btn-danger cyber-btn-sm">
                        <i class="fas fa-trash"></i> 批量删除 ({{ selectedIds.length }})
                    </button>
                </div>
            </div>
            <div class="cyber-card-body">

                <!-- 加载状态 -->
                <div v-if="loading" class="text-center py-4">
                    <i class="fas fa-spinner fa-spin"></i>
                    <span class="ms-2">加载中...</span>
                </div>

                <!-- 数据表格 -->
                <div v-else class="cyber-table-container">
                    <table class="cyber-table">
                        <thead>
                            <tr>
                                <th width="50">
                                    <input type="checkbox" v-model="selectAll" v-on:change="toggleSelectAll" class="form-check-input">
                                </th>
                                <th>主宠物</th>
                                <th>副宠物</th>
                                <th>结果宠物</th>
                                <th>成长要求</th>
                                <th>等级要求</th>
                                <th>消耗金币</th>
                                <th>成功率</th>
                                <th>公式类型</th>
                                <th>状态</th>
                                <th>创建时间</th>
                                <th width="200">操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr v-for="formula in formulaList" v-bind:key="formula.id">
                                <td>
                                    <input type="checkbox" v-bind:value="formula.id" v-model="selectedIds" class="form-check-input">
                                </td>
                                <td>
                                    <div class="d-flex align-items-center gap-2">
                                        <strong>{{ formula.mainPetName }}</strong>
                                        <span class="cyber-badge cyber-badge-info">{{ formula.mainPetAttribute }}</span>
                                        <small class="text-muted">({{ formula.mainPetNo }})</small>
                                    </div>
                                </td>
                                <td>
                                    <div class="d-flex align-items-center gap-2">
                                        <strong>{{ formula.subPetName }}</strong>
                                        <span class="cyber-badge cyber-badge-info">{{ formula.subPetAttribute }}</span>
                                        <small class="text-muted">({{ formula.subPetNo }})</small>
                                    </div>
                                </td>
                                <td>
                                    <div class="d-flex align-items-center gap-2">
                                        <strong>{{ formula.resultPetName }}</strong>
                                        <span class="cyber-badge cyber-badge-success">{{ formula.resultPetAttribute }}</span>
                                        <small class="text-muted">({{ formula.resultPetNo }})</small>
                                    </div>
                                </td>
                                <td>
                                    <div>
                                        <div>主: <span class="text-success fw-bold">{{ formula.mainGrowthMin }}</span></div>
                                        <div>副: <span class="text-success fw-bold">{{ formula.subGrowthMin }}</span></div>
                                    </div>
                                </td>
                                <td>
                                    <span class="cyber-badge cyber-badge-warning">{{ formula.requiredLevel }}级</span>
                                </td>
                                <td>
                                    <span class="text-warning fw-bold">{{ formatNumber(formula.costGold) }}</span>
                                </td>
                                <td>
                                    <span class="text-info fw-bold">{{ formula.baseSuccessRate }}%</span>
                                </td>
                                <td>
                                    <span class="cyber-badge" v-bind:class="getFormulaTypeClass(formula.formulaType)">
                                        {{ formula.formulaTypeText }}
                                    </span>
                                </td>
                                <td>
                                    <button v-on:click="toggleActive(formula)"
                                            class="cyber-btn cyber-btn-sm"
                                            v-bind:class="formula.isActive ? 'cyber-btn-success' : 'cyber-btn-secondary'">
                                        <i v-bind:class="formula.isActive ? 'fas fa-check' : 'fas fa-times'"></i>
                                        {{ formula.isActive ? '已激活' : '已禁用' }}
                                    </button>
                                </td>
                                <td>
                                    <small>{{ formatDateTime(formula.createTime) }}</small>
                                </td>
                                <td>
                                    <div class="d-flex gap-1">
                                        <button v-on:click="showEditModal(formula)" class="cyber-btn cyber-btn-warning cyber-btn-sm">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button v-on:click="showDetailModal(formula)" class="cyber-btn cyber-btn-info cyber-btn-sm">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button v-on:click="deleteFormula(formula)" class="cyber-btn cyber-btn-danger cyber-btn-sm">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- 科幻分页 -->
                <div v-if="totalPages > 1" class="d-flex justify-content-between align-items-center mt-3">
                    <div class="text-muted">
                        显示第 {{ (currentPage - 1) * queryForm.pageSize + 1 }} 到 {{ Math.min(currentPage * queryForm.pageSize, totalCount) }} 条，共 {{ totalCount }} 条记录
                    </div>
                    <nav>
                        <ul class="cyber-pagination pagination">
                            <li class="page-item" v-bind:class="{ disabled: currentPage === 1 }">
                                <a href="#" class="page-link" v-on:click.prevent="changePage(currentPage - 1)">上一页</a>
                            </li>
                            <li class="page-item"
                                v-for="page in visiblePages"
                                v-bind:key="page"
                                v-bind:class="{ active: page === currentPage }">
                                <a href="#" class="page-link" v-on:click.prevent="changePage(page)">{{ page }}</a>
                            </li>
                            <li class="page-item" v-bind:class="{ disabled: currentPage === totalPages }">
                                <a href="#" class="page-link" v-on:click.prevent="changePage(currentPage + 1)">下一页</a>
                            </li>
                        </ul>
                    </nav>
                </div>
            </div>
        </div>

        <!-- 科幻新增/编辑模态框 -->
        <div class="modal fade cyber-modal" id="formulaModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="fas fa-flask me-2"></i>{{ modalTitle }}
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <form>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <div class="cyber-form-group">
                                        <label class="cyber-form-label">主宠物 <span class="text-danger">*</span></label>
                                        <select v-model="formulaForm.mainPetNo" class="cyber-form-control" id="modalMainPetSelect">
                                            <option value="">请选择主宠物</option>
                                            <option v-for="pet in petOptions" v-bind:key="pet.value" v-bind:value="pet.value">
                                                {{ pet.label }}
                                            </option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <div class="cyber-form-group">
                                        <label class="cyber-form-label">副宠物 <span class="text-danger">*</span></label>
                                        <select v-model="formulaForm.subPetNo" class="cyber-form-control" id="modalSubPetSelect">
                                            <option value="">请选择副宠物</option>
                                            <option v-for="pet in petOptions" v-bind:key="pet.value" v-bind:value="pet.value">
                                                {{ pet.label }}
                                            </option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <div class="cyber-form-group">
                                        <label class="cyber-form-label">主宠最小成长要求</label>
                                        <input type="number" v-model="formulaForm.mainGrowthMin" class="cyber-form-control"
                                               step="0.000001" min="0" max="999.999999" placeholder="0.000000">
                                    </div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <div class="cyber-form-group">
                                        <label class="cyber-form-label">副宠最小成长要求</label>
                                        <input type="number" v-model="formulaForm.subGrowthMin" class="cyber-form-control"
                                               step="0.000001" min="0" max="999.999999" placeholder="0.000000">
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <div class="cyber-form-group">
                                        <label class="cyber-form-label">合成结果宠物 <span class="text-danger">*</span></label>
                                        <select v-model="formulaForm.resultPetNo" class="cyber-form-control" id="modalResultPetSelect">
                                            <option value="">请选择结果宠物</option>
                                            <option v-for="pet in petOptions" v-bind:key="pet.value" v-bind:value="pet.value">
                                                {{ pet.label }}
                                            </option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <div class="cyber-form-group">
                                        <label class="cyber-form-label">公式类型 <span class="text-danger">*</span></label>
                                        <select v-model="formulaForm.formulaType" class="cyber-form-control">
                                            <option value="FIXED">固定公式</option>
                                            <option value="RANDOM_GOD">随机神宠</option>
                                            <option value="RANDOM_HOLY">随机神圣宠物</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <div class="cyber-form-group">
                                        <label class="cyber-form-label">基础成功率(%)</label>
                                        <input type="number" v-model="formulaForm.baseSuccessRate" class="cyber-form-control"
                                               min="0" max="100" step="0.01" placeholder="50.00">
                                    </div>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <div class="cyber-form-group">
                                        <label class="cyber-form-label">所需等级</label>
                                        <input type="number" v-model="formulaForm.requiredLevel" class="cyber-form-control"
                                               min="1" max="999" placeholder="40">
                                    </div>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <div class="cyber-form-group">
                                        <label class="cyber-form-label">消耗金币</label>
                                        <input type="number" v-model="formulaForm.costGold" class="cyber-form-control"
                                               min="0" placeholder="50000">
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-12 mb-3">
                                    <div class="form-check">
                                        <input type="checkbox" v-model="formulaForm.isActive" class="form-check-input" id="isActiveCheck">
                                        <label class="form-check-label text-light" for="isActiveCheck">
                                            启用此合成公式
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="cyber-btn cyber-btn-outline" data-bs-dismiss="modal">
                            <i class="fas fa-times"></i> 取消
                        </button>
                        <button type="button" v-on:click="saveFormula" class="cyber-btn">
                            <i class="fas fa-save"></i> 保存
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 科幻详情模态框 -->
        <div class="modal fade cyber-modal" id="detailModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="fas fa-info-circle me-2"></i>合成公式详情
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div v-if="currentFormula" class="row">
                            <div class="col-md-6 mb-3">
                                <label class="cyber-form-label">主宠物信息</label>
                                <div class="p-3 rounded" style="background: rgba(0, 212, 255, 0.1); border: 1px solid rgba(0, 212, 255, 0.3);">
                                    <div><strong>名称:</strong> {{ currentFormula.mainPetName }}</div>
                                    <div><strong>编号:</strong> {{ currentFormula.mainPetNo }}</div>
                                    <div><strong>属性:</strong> {{ currentFormula.mainPetAttribute }}</div>
                                    <div><strong>成长要求:</strong> {{ currentFormula.mainGrowthMin }}</div>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="cyber-form-label">副宠物信息</label>
                                <div class="p-3 rounded" style="background: rgba(0, 212, 255, 0.1); border: 1px solid rgba(0, 212, 255, 0.3);">
                                    <div><strong>名称:</strong> {{ currentFormula.subPetName }}</div>
                                    <div><strong>编号:</strong> {{ currentFormula.subPetNo }}</div>
                                    <div><strong>属性:</strong> {{ currentFormula.subPetAttribute }}</div>
                                    <div><strong>成长要求:</strong> {{ currentFormula.subGrowthMin }}</div>
                                </div>
                            </div>
                            <div class="col-md-12 mb-3">
                                <label class="cyber-form-label">合成结果</label>
                                <div class="p-3 rounded" style="background: rgba(16, 185, 129, 0.1); border: 1px solid rgba(16, 185, 129, 0.3);">
                                    <div><strong>宠物名称:</strong> {{ currentFormula.resultPetName }}</div>
                                    <div><strong>宠物编号:</strong> {{ currentFormula.resultPetNo }}</div>
                                    <div><strong>宠物属性:</strong> {{ currentFormula.resultPetAttribute }}</div>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="cyber-form-label">合成条件</label>
                                <div class="p-3 rounded" style="background: rgba(245, 158, 11, 0.1); border: 1px solid rgba(245, 158, 11, 0.3);">
                                    <div><strong>所需等级:</strong> {{ currentFormula.requiredLevel }}级</div>
                                    <div><strong>消耗金币:</strong> {{ formatNumber(currentFormula.costGold) }}</div>
                                    <div><strong>基础成功率:</strong> {{ currentFormula.baseSuccessRate }}%</div>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="cyber-form-label">公式信息</label>
                                <div class="p-3 rounded" style="background: rgba(59, 130, 246, 0.1); border: 1px solid rgba(59, 130, 246, 0.3);">
                                    <div><strong>公式类型:</strong> {{ currentFormula.formulaTypeText }}</div>
                                    <div><strong>激活状态:</strong>
                                        <span v-bind:class="currentFormula.isActive ? 'text-success' : 'text-danger'">
                                            {{ currentFormula.isActive ? '已激活' : '已禁用' }}
                                        </span>
                                    </div>
                                    <div><strong>创建时间:</strong> {{ formatDateTime(currentFormula.createTime) }}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="cyber-btn cyber-btn-outline" data-bs-dismiss="modal">
                            <i class="fas fa-times"></i> 关闭
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
<script src="https://unpkg.com/axios/dist/axios.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

<script>
    const { createApp, ref, computed, onMounted } = Vue;

    createApp({
        setup() {
            // 响应式数据
            const loading = ref(false);
            const isEdit = ref(false);
            const modalTitle = ref('新增合成公式');
            const currentFormula = ref(null);

            // 查询表单
            const queryForm = ref({
                mainPetNo: '',
                subPetNo: '',
                resultPetNo: '',
                formulaType: '',
                isActive: '',
                minRequiredLevel: '',
                maxRequiredLevel: '',
                page: 1,
                pageSize: 10
            });

            // 公式表单
            const formulaForm = ref({
                id: 0,
                mainPetNo: '',
                subPetNo: '',
                mainGrowthMin: 0.000000,
                subGrowthMin: 0.000000,
                resultPetNo: '',
                baseSuccessRate: 50.00,
                requiredLevel: 40,
                costGold: 50000,
                formulaType: 'FIXED',
                isActive: true
            });

            // 数据列表
            const formulaList = ref([]);
            const petOptions = ref([]);

            // 分页信息
            const currentPage = ref(1);
            const totalCount = ref(0);
            const totalPages = ref(0);

            // 选择状态
            const selectedIds = ref([]);
            const selectAll = ref(false);
            // 计算属性
            const visiblePages = computed(() => {
                const pages = [];
                const start = Math.max(1, currentPage.value - 2);
                const end = Math.min(totalPages.value, currentPage.value + 2);

                for (let i = start; i <= end; i++) {
                    pages.push(i);
                }
                return pages;
            });
            // 方法
            const initSelect2 = () => {
                // 移除Select2依赖，使用原生select
            };
            const loadPetOptions = async () => {
                try {
                    const response = await axios.get('/PetSynthesisFormula/GetPetConfigOptions');
                    if (response.data.code === 200) {
                        petOptions.value = response.data.data.map(pet => ({
                            value: pet.petNo,
                            label: `${pet.name} (${pet.petNo}) [${pet.attribute}]`
                        }));
                    }
                } catch (error) {
                    console.error('加载宠物选项失败:', error);
                }
            };

            const loadFormulas = async () => {
                loading.value = true;
                try {
                    const response = await axios.post('/PetSynthesisFormula/GetList', queryForm.value);
                    if (response.data.code === 200) {
                        formulaList.value = response.data.data;
                        totalCount.value = response.data.total;
                        totalPages.value = Math.ceil(totalCount.value / queryForm.value.pageSize);
                        currentPage.value = queryForm.value.page;
                    } else {
                        console.error('加载失败:', response.data.message);
                    }
                } catch (error) {
                    console.error('加载合成公式列表失败:', error);
                } finally {
                    loading.value = false;
                }
            };
            const searchFormulas = () => {
                queryForm.value.page = 1;
                loadFormulas();
            };

            const resetSearch = () => {
                queryForm.value = {
                    mainPetNo: '',
                    subPetNo: '',
                    resultPetNo: '',
                    formulaType: '',
                    isActive: '',
                    minRequiredLevel: '',
                    maxRequiredLevel: '',
                    page: 1,
                    pageSize: 10
                };
                loadFormulas();
            };

            const changePage = (page) => {
                if (page >= 1 && page <= totalPages.value && page !== currentPage.value) {
                    queryForm.value.page = page;
                    loadFormulas();
                }
            };

            const toggleSelectAll = () => {
                if (selectAll.value) {
                    selectedIds.value = formulaList.value.map(formula => formula.id);
                } else {
                    selectedIds.value = [];
                }
            };
            const showCreateModal = () => {
                isEdit.value = false;
                modalTitle.value = '新增合成公式';
                formulaForm.value = {
                    id: 0,
                    mainPetNo: '',
                    subPetNo: '',
                    mainGrowthMin: 0.000000,
                    subGrowthMin: 0.000000,
                    resultPetNo: '',
                    baseSuccessRate: 50.00,
                    requiredLevel: 40,
                    costGold: 50000,
                    formulaType: 'FIXED',
                    isActive: true
                };
                new bootstrap.Modal(document.getElementById('formulaModal')).show();
            };

            const showEditModal = (formula) => {
                isEdit.value = true;
                modalTitle.value = '编辑合成公式';
                formulaForm.value = {
                    id: formula.id,
                    mainPetNo: formula.mainPetNo,
                    subPetNo: formula.subPetNo,
                    mainGrowthMin: formula.mainGrowthMin,
                    subGrowthMin: formula.subGrowthMin,
                    resultPetNo: formula.resultPetNo,
                    baseSuccessRate: formula.baseSuccessRate,
                    requiredLevel: formula.requiredLevel,
                    costGold: formula.costGold,
                    formulaType: formula.formulaType,
                    isActive: formula.isActive
                };
                new bootstrap.Modal(document.getElementById('formulaModal')).show();
            };

            const showDetailModal = (formula) => {
                currentFormula.value = formula;
                new bootstrap.Modal(document.getElementById('detailModal')).show();
            };
            const saveFormula = async () => {
                try {
                    if (!formulaForm.value.mainPetNo || !formulaForm.value.subPetNo || !formulaForm.value.resultPetNo) {
                        alert('请填写必填字段');
                        return;
                    }

                    const url = isEdit.value ? '/PetSynthesisFormula/Update' : '/PetSynthesisFormula/Create';
                    const response = await axios.post(url, formulaForm.value);

                    if (response.data.success) {
                        alert(response.data.message || '操作成功');
                        bootstrap.Modal.getInstance(document.getElementById('formulaModal')).hide();
                        loadFormulas();
                    } else {
                        alert(response.data.message || '操作失败');
                    }
                } catch (error) {
                    console.error('保存失败:', error);
                    alert('保存失败');
                }
            };

            const deleteFormula = async (formula) => {
                if (!confirm(`确定要删除合成公式"${formula.mainPetName} + ${formula.subPetName} = ${formula.resultPetName}"吗？`)) {
                    return;
                }

                try {
                    const response = await axios.post('/PetSynthesisFormula/Delete', { id: formula.id });
                    if (response.data.success) {
                        alert(response.data.message || '删除成功');
                        loadFormulas();
                    } else {
                        alert(response.data.message || '删除失败');
                    }
                } catch (error) {
                    console.error('删除失败:', error);
                    alert('删除失败');
                }
            };
            const batchDelete = async () => {
                if (selectedIds.value.length === 0) {
                    alert('请选择要删除的合成公式');
                    return;
                }

                if (!confirm(`确定要删除选中的 ${selectedIds.value.length} 个合成公式吗？`)) {
                    return;
                }

                try {
                    const response = await axios.post('/PetSynthesisFormula/BatchDelete', selectedIds.value);
                    if (response.data.success) {
                        alert(response.data.message || '批量删除成功');
                        selectedIds.value = [];
                        selectAll.value = false;
                        loadFormulas();
                    } else {
                        alert(response.data.message || '批量删除失败');
                    }
                } catch (error) {
                    console.error('批量删除失败:', error);
                    alert('批量删除失败');
                }
            };

            const toggleActive = async (formula) => {
                try {
                    const response = await axios.post('/PetSynthesisFormula/ToggleActive', {
                        id: formula.id,
                        isActive: !formula.isActive
                    });

                    if (response.data.success) {
                        formula.isActive = !formula.isActive;
                        alert(response.data.message || '状态切换成功');
                    } else {
                        alert(response.data.message || '状态切换失败');
                    }
                } catch (error) {
                    console.error('状态切换失败:', error);
                    alert('状态切换失败');
                }
            };
            const getFormulaTypeClass = (type) => {
                switch (type) {
                    case 'FIXED': return 'cyber-badge-info';
                    case 'RANDOM_GOD': return 'cyber-badge-warning';
                    case 'RANDOM_HOLY': return 'cyber-badge-success';
                    default: return 'cyber-badge';
                }
            };

            const formatNumber = (num) => {
                return new Intl.NumberFormat('zh-CN').format(num);
            };

            const formatDateTime = (dateTime) => {
                return new Date(dateTime).toLocaleString('zh-CN');
            };

            // 生命周期
            onMounted(async () => {
                await loadPetOptions();
                await loadFormulas();
            });

            return {
                // 数据
                loading,
                isEdit,
                modalTitle,
                currentFormula,
                queryForm,
                formulaForm,
                formulaList,
                petOptions,
                currentPage,
                totalCount,
                totalPages,
                selectedIds,
                selectAll,

                // 计算属性
                visiblePages,

                // 方法
                loadFormulas,
                searchFormulas,
                resetSearch,
                changePage,
                toggleSelectAll,
                showCreateModal,
                showEditModal,
                showDetailModal,
                saveFormula,
                deleteFormula,
                batchDelete,
                toggleActive,
                getFormulaTypeClass,
                formatNumber,
                formatDateTime
            };
        }
    }).mount('#petSynthesisFormulaApp');
</script>
