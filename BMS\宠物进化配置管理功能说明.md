# 宠物进化配置管理功能说明

## 📋 功能概述

宠物进化配置管理是游戏后台管理系统的核心功能之一，用于配置和管理游戏中宠物的进化规则。管理员可以通过此功能设置宠物的进化条件、消耗资源、成功率等参数。

## 🎯 主要功能

### 1. 进化配置管理
- **新增配置**: 创建新的宠物进化规则
- **编辑配置**: 修改现有的进化配置
- **删除配置**: 删除不需要的进化配置
- **批量删除**: 一次性删除多个配置
- **状态切换**: 激活/禁用进化配置

### 2. 智能搜索
- **多条件筛选**: 支持宠物编号、名称、进化类型等多种筛选条件
- **可搜索下拉框**: 宠物选择、目标宠物选择、道具选择都支持实时搜索
- **实时过滤**: 输入时即时显示匹配结果

### 3. 数据验证
- **前端验证**: 表单字段格式验证
- **后端验证**: 业务逻辑验证
- **重复检查**: 防止创建重复的进化配置
- **关联验证**: 确保宠物和道具的存在性

## 🚀 使用指南

### 访问路径
1. 登录后台管理系统
2. 点击导航菜单 "游戏管理"
3. 选择 "宠物进化配置"

### 新增进化配置
1. 点击 "新增配置" 按钮
2. 填写必要信息：
   - **宠物**: 选择要设置进化的宠物
   - **进化类型**: 选择A路线或B路线
   - **目标宠物**: 选择进化后的宠物
   - **所需等级**: 设置进化所需的最低等级
   - **所需道具**: 可选，设置进化需要的道具
   - **道具数量**: 如果需要道具，设置数量
   - **消耗金币**: 设置进化消耗的金币数量
   - **成长加成**: 设置进化后的成长值加成范围
   - **成功率**: 设置进化的成功概率
   - **说明**: 可选，添加配置说明
3. 点击 "保存" 完成创建

### 搜索和筛选
- **宠物编号**: 输入具体的宠物编号
- **宠物名称**: 输入宠物名称进行模糊搜索
- **进化类型**: 选择A路线或B路线
- **目标宠物**: 筛选特定的目标宠物
- **激活状态**: 筛选已激活或已禁用的配置
- **等级要求**: 设置等级范围进行筛选

### 批量操作
1. 勾选需要操作的配置项
2. 点击 "批量删除" 按钮
3. 确认操作

## 📊 配置参数说明

### 基础参数
- **宠物编号**: 关联pet_config表的pet_no字段
- **进化类型**: A或B，表示不同的进化路线
- **目标宠物编号**: 进化后的宠物编号
- **所需等级**: 宠物达到此等级才能进化

### 资源消耗
- **所需道具ID**: 进化需要的道具编号（可选）
- **道具数量**: 需要消耗的道具数量
- **消耗金币**: 进化需要的金币数量

### 进化效果
- **最小成长加成**: 进化后成长值的最小加成
- **最大成长加成**: 进化后成长值的最大加成
- **成功率**: 进化成功的概率（0-100%）

### 状态控制
- **激活状态**: 控制此配置是否生效
- **说明**: 配置的备注信息

## 🔧 技术说明

### 数据库表结构
基于 `pet_evolution_config` 表：
```sql
CREATE TABLE pet_evolution_config (
    id INT AUTO_INCREMENT PRIMARY KEY,
    pet_no INT NOT NULL,
    evolution_type VARCHAR(1) NOT NULL,
    target_pet_no INT NOT NULL,
    required_level INT DEFAULT 40,
    required_item_id VARCHAR(50),
    required_item_count INT DEFAULT 1,
    cost_gold BIGINT DEFAULT 1000,
    growth_min DECIMAL(5,3) DEFAULT 0.100,
    growth_max DECIMAL(5,3) DEFAULT 0.500,
    success_rate DECIMAL(5,2) DEFAULT 100.00,
    is_active TINYINT DEFAULT 1,
    description VARCHAR(50),
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

### API接口
- `POST /PetEvolutionConfig/GetList` - 获取配置列表
- `POST /PetEvolutionConfig/Create` - 创建配置
- `POST /PetEvolutionConfig/Update` - 更新配置
- `POST /PetEvolutionConfig/Delete` - 删除配置
- `POST /PetEvolutionConfig/BatchDelete` - 批量删除
- `POST /PetEvolutionConfig/ToggleActive` - 切换状态
- `GET /PetEvolutionConfig/GetPetConfigOptions` - 获取宠物选项
- `GET /PetEvolutionConfig/GetItemConfigOptions` - 获取道具选项

## ⚠️ 注意事项

1. **唯一性约束**: 同一宠物的同一进化类型只能有一个配置
2. **关联性检查**: 宠物和道具必须在对应的配置表中存在
3. **数值范围**: 成长加成范围需要合理设置，最小值不能大于最大值
4. **成功率**: 建议设置在70%-100%之间，过低会影响游戏体验
5. **等级要求**: 建议根据游戏平衡性设置合理的等级要求

## 🐛 常见问题

### Q: 为什么无法创建进化配置？
A: 请检查：
- 宠物编号是否存在
- 目标宠物编号是否存在
- 是否已存在相同宠物的相同进化类型配置
- 所需道具是否存在（如果设置了道具）

### Q: 如何设置合理的成长加成？
A: 建议：
- 普通进化：0.100-0.300
- 高级进化：0.200-0.500
- 终极进化：0.300-0.800

### Q: 进化配置不生效怎么办？
A: 请确认：
- 配置状态是否为"已激活"
- 游戏客户端是否已更新配置
- 宠物等级是否满足要求

## 📞 技术支持

如有问题，请联系开发团队或查看系统日志获取详细错误信息。
