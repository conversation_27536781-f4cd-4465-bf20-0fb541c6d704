using BMS.Models.DTOs;
using BMS.Models.Common;

namespace BMS.Services.Interfaces
{
    /// <summary>
    /// 技能服务接口
    /// </summary>
    public interface ISkillService
    {
        /// <summary>
        /// 分页查询技能列表
        /// </summary>
        /// <param name="queryDto">查询条件</param>
        /// <returns>分页结果</returns>
        Task<PagedResult<SkillDto>> GetPagedListAsync(SkillQueryDto queryDto);

        /// <summary>
        /// 根据技能ID获取技能详情
        /// </summary>
        /// <param name="skillId">技能ID</param>
        /// <returns>技能详情</returns>
        Task<SkillDto?> GetByIdAsync(string skillId);

        /// <summary>
        /// 创建技能
        /// </summary>
        /// <param name="createDto">创建DTO</param>
        /// <returns>操作结果</returns>
        Task<ApiResult<bool>> CreateAsync(SkillCreateDto createDto);

        /// <summary>
        /// 更新技能
        /// </summary>
        /// <param name="updateDto">更新DTO</param>
        /// <returns>操作结果</returns>
        Task<ApiResult<bool>> UpdateAsync(SkillUpdateDto updateDto);

        /// <summary>
        /// 删除技能
        /// </summary>
        /// <param name="deleteDto">删除DTO</param>
        /// <returns>操作结果</returns>
        Task<ApiResult<bool>> DeleteAsync(SkillDeleteDto deleteDto);

        /// <summary>
        /// 检查技能ID是否存在
        /// </summary>
        /// <param name="skillId">技能ID</param>
        /// <returns>是否存在</returns>
        Task<bool> CheckSkillIdExistsAsync(string skillId);

        /// <summary>
        /// 获取所有效果类型
        /// </summary>
        /// <returns>效果类型列表</returns>
        Task<List<string>> GetEffectTypesAsync();

        /// <summary>
        /// 获取所有五行限制
        /// </summary>
        /// <returns>五行限制列表</returns>
        Task<List<string>> GetElementLimitsAsync();
    }
} 