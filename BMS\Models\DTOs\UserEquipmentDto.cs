using System.ComponentModel.DataAnnotations;

namespace BMS.Models.DTOs;

/// <summary>
/// 用户装备显示DTO
/// </summary>
public class UserEquipmentDto
{
    /// <summary>
    /// 装备ID
    /// </summary>
    public int Id { get; set; }

    /// <summary>
    /// 用户ID
    /// </summary>
    public int UserId { get; set; }

    /// <summary>
    /// 用户名
    /// </summary>
    public string UserName { get; set; }

    /// <summary>
    /// 装备唯一ID
    /// </summary>
    public string EquipId { get; set; }



    /// <summary>
    /// 装备名称
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    /// 装备图标
    /// </summary>
    public string Icon { get; set; }

    /// <summary>
    /// 装备类型ID
    /// </summary>
    public string EquipTypeId { get; set; } = string.Empty;

    /// <summary>
    /// 装备类型名称
    /// </summary>
    public string EquipTypeName { get; set; }

    /// <summary>
    /// 强化等级
    /// </summary>
    public int StrengthenLevel { get; set; }

    /// <summary>
    /// 扩展槽位
    /// </summary>
    public int Slot { get; set; }

    /// <summary>
    /// 装备位置
    /// </summary>
    public int? Position { get; set; }

    /// <summary>
    /// 是否已装备
    /// </summary>
    public bool IsEquipped { get; set; }

    /// <summary>
    /// 获得时间
    /// </summary>
    public DateTime CreateTime { get; set; }

    /// <summary>
    /// 装备属性详情
    /// </summary>
    public UserEquipmentDetailDto? Detail { get; set; }
}

/// <summary>
/// 用户装备详情DTO
/// </summary>
public class UserEquipmentDetailDto
{
    /// <summary>
    /// 攻击加成
    /// </summary>
    public decimal Atk { get; set; }

    /// <summary>
    /// 命中加成
    /// </summary>
    public decimal Hit { get; set; }

    /// <summary>
    /// 防御加成
    /// </summary>
    public decimal Def { get; set; }

    /// <summary>
    /// 速度加成
    /// </summary>
    public decimal Spd { get; set; }

    /// <summary>
    /// 闪避加成
    /// </summary>
    public decimal Dodge { get; set; }

    /// <summary>
    /// 生命加成
    /// </summary>
    public decimal Hp { get; set; }

    /// <summary>
    /// 魔法加成
    /// </summary>
    public decimal Mp { get; set; }

    /// <summary>
    /// 加深加成
    /// </summary>
    public decimal Deepen { get; set; }

    /// <summary>
    /// 抵消加成
    /// </summary>
    public decimal Offset { get; set; }

    /// <summary>
    /// 吸血加成
    /// </summary>
    public decimal Vamp { get; set; }

    /// <summary>
    /// 吸魔加成
    /// </summary>
    public decimal VampMp { get; set; }

    /// <summary>
    /// 装备说明
    /// </summary>
    public string Description { get; set; }

    /// <summary>
    /// 主属性
    /// </summary>
    public string MainAttr { get; set; }

    /// <summary>
    /// 五行限制
    /// </summary>
    public string ElementLimit { get; set; }
}

/// <summary>
/// 用户装备查询DTO
/// </summary>
public class UserEquipmentQueryDto : PagedQueryDto
{
    /// <summary>
    /// 用户ID
    /// </summary>
    public int? UserId { get; set; }

    /// <summary>
    /// 用户名
    /// </summary>
    public string? UserName { get; set; }

    /// <summary>
    /// 装备名称
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// 装备类型ID
    /// </summary>
    public string? EquipTypeId { get; set; }

    /// <summary>
    /// 是否已装备
    /// </summary>
    public bool? IsEquipped { get; set; }

    /// <summary>
    /// 强化等级范围（最小）
    /// </summary>
    public int? MinStrengthenLevel { get; set; }

    /// <summary>
    /// 强化等级范围（最大）
    /// </summary>
    public int? MaxStrengthenLevel { get; set; }
}

/// <summary>
/// 创建用户装备DTO
/// </summary>
public class UserEquipmentCreateDto
{
    /// <summary>
    /// 用户ID
    /// </summary>
    [Required(ErrorMessage = "用户ID不能为空")]
    public int UserId { get; set; }

    /// <summary>
    /// 装备唯一ID
    /// </summary>
    [Required(ErrorMessage = "装备ID不能为空")]
    [StringLength(20, ErrorMessage = "装备ID长度不能超过20个字符")]
    public string EquipId { get; set; }



    /// <summary>
    /// 装备名称
    /// </summary>
    [Required(ErrorMessage = "装备名称不能为空")]
    [StringLength(50, ErrorMessage = "装备名称长度不能超过50个字符")]
    public string Name { get; set; }

    /// <summary>
    /// 装备图标
    /// </summary>
    [StringLength(100, ErrorMessage = "装备图标长度不能超过100个字符")]
    public string? Icon { get; set; }

    /// <summary>
    /// 装备类型ID
    /// </summary>
    [Required(ErrorMessage = "装备类型ID不能为空")]
    public string EquipTypeId { get; set; } = string.Empty;

    /// <summary>
    /// 强化等级
    /// </summary>
    [Range(0, 20, ErrorMessage = "强化等级必须在0-20之间")]
    public int StrengthenLevel { get; set; } = 0;

    /// <summary>
    /// 扩展槽位
    /// </summary>
    [Range(0, 10, ErrorMessage = "扩展槽位必须在0-10之间")]
    public int Slot { get; set; } = 0;

    /// <summary>
    /// 装备位置
    /// </summary>
    public int? Position { get; set; }

    /// <summary>
    /// 是否已装备
    /// </summary>
    public bool IsEquipped { get; set; } = false;
}

/// <summary>
/// 更新用户装备DTO
/// </summary>
public class UserEquipmentUpdateDto
{
    /// <summary>
    /// 装备ID
    /// </summary>
    [Required(ErrorMessage = "装备ID不能为空")]
    public int Id { get; set; }

    /// <summary>
    /// 装备名称
    /// </summary>
    [StringLength(50, ErrorMessage = "装备名称长度不能超过50个字符")]
    public string? Name { get; set; }

    /// <summary>
    /// 装备图标
    /// </summary>
    [StringLength(100, ErrorMessage = "装备图标长度不能超过100个字符")]
    public string? Icon { get; set; }

    /// <summary>
    /// 强化等级
    /// </summary>
    [Range(0, 20, ErrorMessage = "强化等级必须在0-20之间")]
    public int? StrengthenLevel { get; set; }

    /// <summary>
    /// 扩展槽位
    /// </summary>
    [Range(0, 10, ErrorMessage = "扩展槽位必须在0-10之间")]
    public int? Slot { get; set; }

    /// <summary>
    /// 装备位置
    /// </summary>
    public int? Position { get; set; }

    /// <summary>
    /// 是否已装备
    /// </summary>
    public bool? IsEquipped { get; set; }
}

/// <summary>
/// 删除用户装备DTO
/// </summary>
public class UserEquipmentDeleteDto
{
    /// <summary>
    /// 装备ID
    /// </summary>
    [Required(ErrorMessage = "装备ID不能为空")]
    public int Id { get; set; }
}

/// <summary>
/// 装备强化DTO
/// </summary>
public class UserEquipmentStrengthenDto
{
    /// <summary>
    /// 装备ID
    /// </summary>
    [Required(ErrorMessage = "装备ID不能为空")]
    public int Id { get; set; }

    /// <summary>
    /// 目标强化等级
    /// </summary>
    [Required(ErrorMessage = "强化等级不能为空")]
    [Range(1, 20, ErrorMessage = "强化等级必须在1-20之间")]
    public int TargetLevel { get; set; }
}

/// <summary>
/// 装备穿戴DTO
/// </summary>
public class UserEquipmentWearDto
{
    /// <summary>
    /// 装备ID
    /// </summary>
    [Required(ErrorMessage = "装备ID不能为空")]
    [Range(1, int.MaxValue, ErrorMessage = "装备ID必须大于0")]
    public int Id { get; set; }

    /// <summary>
    /// 用户ID（可选，用于验证装备归属）
    /// </summary>
    public int? UserId { get; set; }

    /// <summary>
    /// 装备位置（1-10：头盔、胸甲、腿甲、靴子、武器、副手、戒指1、戒指2、项链、腰带）
    /// 默认为1（头盔位置）
    /// </summary>
    public int? Position { get; set; }

    /// <summary>
    /// 获取有效的装备位置（如果为空则返回默认值1）
    /// </summary>
    public int GetValidPosition() => Position ?? 1;

    /// <summary>
    /// 验证方法
    /// </summary>
    public bool IsValid(out string errorMessage)
    {
        errorMessage = string.Empty;

        if (Id <= 0)
        {
            errorMessage = "装备ID必须大于0";
            return false;
        }

        // 使用默认值进行验证
        int validPosition = GetValidPosition();
        if (validPosition < 1 || validPosition > 10)
        {
            errorMessage = "装备位置必须在1-10之间";
            return false;
        }

        return true;
    }
}