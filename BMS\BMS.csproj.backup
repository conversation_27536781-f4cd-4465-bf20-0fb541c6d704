<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>



  <ItemGroup>
    <None Include="bin\Debug\net8.0\BMS.dll" />
    <None Include="bin\Debug\net8.0\BMS.exe" />
    <None Include="bin\Debug\net8.0\BMS.pdb" />
    <None Include="bin\Debug\net8.0\BouncyCastle.Cryptography.dll" />
    <None Include="bin\Debug\net8.0\Google.Protobuf.dll" />
    <None Include="bin\Debug\net8.0\K4os.Compression.LZ4.dll" />
    <None Include="bin\Debug\net8.0\K4os.Compression.LZ4.Streams.dll" />
    <None Include="bin\Debug\net8.0\K4os.Hash.xxHash.dll" />
    <None Include="bin\Debug\net8.0\Microsoft.Extensions.DependencyModel.dll" />
    <None Include="bin\Debug\net8.0\MySql.Data.dll" />
    <None Include="bin\Debug\net8.0\Newtonsoft.Json.dll" />
    <None Include="bin\Debug\net8.0\runtimes\win-x64\native\comerr64.dll" />
    <None Include="bin\Debug\net8.0\runtimes\win-x64\native\gssapi64.dll" />
    <None Include="bin\Debug\net8.0\runtimes\win-x64\native\k5sprt64.dll" />
    <None Include="bin\Debug\net8.0\runtimes\win-x64\native\krb5_64.dll" />
    <None Include="bin\Debug\net8.0\runtimes\win-x64\native\krbcc64.dll" />
    <None Include="bin\Debug\net8.0\runtimes\win\lib\net8.0\System.Windows.Extensions.dll" />
    <None Include="bin\Debug\net8.0\Serilog.AspNetCore.dll" />
    <None Include="bin\Debug\net8.0\Serilog.dll" />
    <None Include="bin\Debug\net8.0\Serilog.Enrichers.Environment.dll" />
    <None Include="bin\Debug\net8.0\Serilog.Enrichers.Thread.dll" />
    <None Include="bin\Debug\net8.0\Serilog.Extensions.Hosting.dll" />
    <None Include="bin\Debug\net8.0\Serilog.Extensions.Logging.dll" />
    <None Include="bin\Debug\net8.0\Serilog.Formatting.Compact.dll" />
    <None Include="bin\Debug\net8.0\Serilog.Settings.Configuration.dll" />
    <None Include="bin\Debug\net8.0\Serilog.Sinks.Console.dll" />
    <None Include="bin\Debug\net8.0\Serilog.Sinks.Debug.dll" />
    <None Include="bin\Debug\net8.0\Serilog.Sinks.File.dll" />
    <None Include="bin\Debug\net8.0\SqlSugar.dll" />
    <None Include="bin\Debug\net8.0\System.Configuration.ConfigurationManager.dll" />
    <None Include="bin\Debug\net8.0\System.Security.Cryptography.ProtectedData.dll" />
    <None Include="bin\Debug\net8.0\System.Security.Permissions.dll" />
    <None Include="bin\Debug\net8.0\System.Windows.Extensions.dll" />
    <None Include="bin\Debug\net8.0\ZstdSharp.dll" />
    <None Include="bin\Release\net8.0\BMS.dll" />
    <None Include="bin\Release\net8.0\BMS.exe" />
    <None Include="bin\Release\net8.0\BMS.pdb" />
    <None Include="bin\Release\net8.0\BouncyCastle.Cryptography.dll" />
    <None Include="bin\Release\net8.0\Google.Protobuf.dll" />
    <None Include="bin\Release\net8.0\K4os.Compression.LZ4.dll" />
    <None Include="bin\Release\net8.0\K4os.Compression.LZ4.Streams.dll" />
    <None Include="bin\Release\net8.0\K4os.Hash.xxHash.dll" />
    <None Include="bin\Release\net8.0\Microsoft.Extensions.DependencyModel.dll" />
    <None Include="bin\Release\net8.0\MySql.Data.dll" />
    <None Include="bin\Release\net8.0\Newtonsoft.Json.dll" />
    <None Include="bin\Release\net8.0\publish\BMS.dll" />
    <None Include="bin\Release\net8.0\publish\BMS.exe" />
    <None Include="bin\Release\net8.0\publish\BMS.pdb" />
    <None Include="bin\Release\net8.0\publish\BouncyCastle.Cryptography.dll" />
    <None Include="bin\Release\net8.0\publish\Google.Protobuf.dll" />
    <None Include="bin\Release\net8.0\publish\K4os.Compression.LZ4.dll" />
    <None Include="bin\Release\net8.0\publish\K4os.Compression.LZ4.Streams.dll" />
    <None Include="bin\Release\net8.0\publish\K4os.Hash.xxHash.dll" />
    <None Include="bin\Release\net8.0\publish\Microsoft.Extensions.DependencyModel.dll" />
    <None Include="bin\Release\net8.0\publish\MySql.Data.dll" />
    <None Include="bin\Release\net8.0\publish\Newtonsoft.Json.dll" />
    <None Include="bin\Release\net8.0\publish\runtimes\win-x64\native\comerr64.dll" />
    <None Include="bin\Release\net8.0\publish\runtimes\win-x64\native\gssapi64.dll" />
    <None Include="bin\Release\net8.0\publish\runtimes\win-x64\native\k5sprt64.dll" />
    <None Include="bin\Release\net8.0\publish\runtimes\win-x64\native\krb5_64.dll" />
    <None Include="bin\Release\net8.0\publish\runtimes\win-x64\native\krbcc64.dll" />
    <None Include="bin\Release\net8.0\publish\runtimes\win\lib\net8.0\System.Windows.Extensions.dll" />
    <None Include="bin\Release\net8.0\publish\Serilog.AspNetCore.dll" />
    <None Include="bin\Release\net8.0\publish\Serilog.dll" />
    <None Include="bin\Release\net8.0\publish\Serilog.Enrichers.Environment.dll" />
    <None Include="bin\Release\net8.0\publish\Serilog.Enrichers.Thread.dll" />
    <None Include="bin\Release\net8.0\publish\Serilog.Extensions.Hosting.dll" />
    <None Include="bin\Release\net8.0\publish\Serilog.Extensions.Logging.dll" />
    <None Include="bin\Release\net8.0\publish\Serilog.Formatting.Compact.dll" />
    <None Include="bin\Release\net8.0\publish\Serilog.Settings.Configuration.dll" />
    <None Include="bin\Release\net8.0\publish\Serilog.Sinks.Console.dll" />
    <None Include="bin\Release\net8.0\publish\Serilog.Sinks.Debug.dll" />
    <None Include="bin\Release\net8.0\publish\Serilog.Sinks.File.dll" />
    <None Include="bin\Release\net8.0\publish\SqlSugar.dll" />
    <None Include="bin\Release\net8.0\publish\System.Configuration.ConfigurationManager.dll" />
    <None Include="bin\Release\net8.0\publish\System.Security.Cryptography.ProtectedData.dll" />
    <None Include="bin\Release\net8.0\publish\System.Security.Permissions.dll" />
    <None Include="bin\Release\net8.0\publish\System.Windows.Extensions.dll" />
    <None Include="bin\Release\net8.0\publish\web.config" />
    <None Include="bin\Release\net8.0\publish\wwwroot\js\razor-safe.js" />
    <None Include="bin\Release\net8.0\publish\wwwroot\js\site.js" />
    <None Include="bin\Release\net8.0\publish\wwwroot\lib\axios.min.js" />
    <None Include="bin\Release\net8.0\publish\wwwroot\lib\bootstrap\dist\css\bootstrap-grid.css.map" />
    <None Include="bin\Release\net8.0\publish\wwwroot\lib\bootstrap\dist\css\bootstrap-grid.min.css.map" />
    <None Include="bin\Release\net8.0\publish\wwwroot\lib\bootstrap\dist\css\bootstrap-grid.rtl.css.map" />
    <None Include="bin\Release\net8.0\publish\wwwroot\lib\bootstrap\dist\css\bootstrap-grid.rtl.min.css.map" />
    <None Include="bin\Release\net8.0\publish\wwwroot\lib\bootstrap\dist\css\bootstrap-reboot.css.map" />
    <None Include="bin\Release\net8.0\publish\wwwroot\lib\bootstrap\dist\css\bootstrap-reboot.min.css.map" />
    <None Include="bin\Release\net8.0\publish\wwwroot\lib\bootstrap\dist\css\bootstrap-reboot.rtl.css.map" />
    <None Include="bin\Release\net8.0\publish\wwwroot\lib\bootstrap\dist\css\bootstrap-reboot.rtl.min.css.map" />
    <None Include="bin\Release\net8.0\publish\wwwroot\lib\bootstrap\dist\css\bootstrap-utilities.css.map" />
    <None Include="bin\Release\net8.0\publish\wwwroot\lib\bootstrap\dist\css\bootstrap-utilities.min.css.map" />
    <None Include="bin\Release\net8.0\publish\wwwroot\lib\bootstrap\dist\css\bootstrap-utilities.rtl.css.map" />
    <None Include="bin\Release\net8.0\publish\wwwroot\lib\bootstrap\dist\css\bootstrap-utilities.rtl.min.css.map" />
    <None Include="bin\Release\net8.0\publish\wwwroot\lib\bootstrap\dist\css\bootstrap.css.map" />
    <None Include="bin\Release\net8.0\publish\wwwroot\lib\bootstrap\dist\css\bootstrap.min.css.map" />
    <None Include="bin\Release\net8.0\publish\wwwroot\lib\bootstrap\dist\css\bootstrap.rtl.css.map" />
    <None Include="bin\Release\net8.0\publish\wwwroot\lib\bootstrap\dist\css\bootstrap.rtl.min.css.map" />
    <None Include="bin\Release\net8.0\publish\wwwroot\lib\bootstrap\dist\js\bootstrap.bundle.js" />
    <None Include="bin\Release\net8.0\publish\wwwroot\lib\bootstrap\dist\js\bootstrap.bundle.js.map" />
    <None Include="bin\Release\net8.0\publish\wwwroot\lib\bootstrap\dist\js\bootstrap.bundle.min.js" />
    <None Include="bin\Release\net8.0\publish\wwwroot\lib\bootstrap\dist\js\bootstrap.bundle.min.js.map" />
    <None Include="bin\Release\net8.0\publish\wwwroot\lib\bootstrap\dist\js\bootstrap.esm.js" />
    <None Include="bin\Release\net8.0\publish\wwwroot\lib\bootstrap\dist\js\bootstrap.esm.js.map" />
    <None Include="bin\Release\net8.0\publish\wwwroot\lib\bootstrap\dist\js\bootstrap.esm.min.js" />
    <None Include="bin\Release\net8.0\publish\wwwroot\lib\bootstrap\dist\js\bootstrap.esm.min.js.map" />
    <None Include="bin\Release\net8.0\publish\wwwroot\lib\bootstrap\dist\js\bootstrap.js" />
    <None Include="bin\Release\net8.0\publish\wwwroot\lib\bootstrap\dist\js\bootstrap.js.map" />
    <None Include="bin\Release\net8.0\publish\wwwroot\lib\bootstrap\dist\js\bootstrap.min.js" />
    <None Include="bin\Release\net8.0\publish\wwwroot\lib\bootstrap\dist\js\bootstrap.min.js.map" />
    <None Include="bin\Release\net8.0\publish\wwwroot\lib\bootstrap\LICENSE" />
    <None Include="bin\Release\net8.0\publish\wwwroot\lib\jquery-validation-unobtrusive\jquery.validate.unobtrusive.js" />
    <None Include="bin\Release\net8.0\publish\wwwroot\lib\jquery-validation-unobtrusive\jquery.validate.unobtrusive.min.js" />
    <None Include="bin\Release\net8.0\publish\wwwroot\lib\jquery-validation\dist\additional-methods.js" />
    <None Include="bin\Release\net8.0\publish\wwwroot\lib\jquery-validation\dist\additional-methods.min.js" />
    <None Include="bin\Release\net8.0\publish\wwwroot\lib\jquery-validation\dist\jquery.validate.js" />
    <None Include="bin\Release\net8.0\publish\wwwroot\lib\jquery-validation\dist\jquery.validate.min.js" />
    <None Include="bin\Release\net8.0\publish\wwwroot\lib\jquery-validation\LICENSE.md" />
    <None Include="bin\Release\net8.0\publish\wwwroot\lib\jquery\dist\jquery.js" />
    <None Include="bin\Release\net8.0\publish\wwwroot\lib\jquery\dist\jquery.min.js" />
    <None Include="bin\Release\net8.0\publish\wwwroot\lib\jquery\dist\jquery.min.map" />
    <None Include="bin\Release\net8.0\publish\wwwroot\lib\vue\vue.global.js" />
    <None Include="bin\Release\net8.0\publish\wwwroot\lib\vue\vue.js" />
    <None Include="bin\Release\net8.0\publish\wwwroot\lib\vue\vue.min.js" />
    <None Include="bin\Release\net8.0\publish\ZstdSharp.dll" />
    <None Include="bin\Release\net8.0\runtimes\win-x64\native\comerr64.dll" />
    <None Include="bin\Release\net8.0\runtimes\win-x64\native\gssapi64.dll" />
    <None Include="bin\Release\net8.0\runtimes\win-x64\native\k5sprt64.dll" />
    <None Include="bin\Release\net8.0\runtimes\win-x64\native\krb5_64.dll" />
    <None Include="bin\Release\net8.0\runtimes\win-x64\native\krbcc64.dll" />
    <None Include="bin\Release\net8.0\runtimes\win\lib\net8.0\System.Windows.Extensions.dll" />
    <None Include="bin\Release\net8.0\Serilog.AspNetCore.dll" />
    <None Include="bin\Release\net8.0\Serilog.dll" />
    <None Include="bin\Release\net8.0\Serilog.Enrichers.Environment.dll" />
    <None Include="bin\Release\net8.0\Serilog.Enrichers.Thread.dll" />
    <None Include="bin\Release\net8.0\Serilog.Extensions.Hosting.dll" />
    <None Include="bin\Release\net8.0\Serilog.Extensions.Logging.dll" />
    <None Include="bin\Release\net8.0\Serilog.Formatting.Compact.dll" />
    <None Include="bin\Release\net8.0\Serilog.Settings.Configuration.dll" />
    <None Include="bin\Release\net8.0\Serilog.Sinks.Console.dll" />
    <None Include="bin\Release\net8.0\Serilog.Sinks.Debug.dll" />
    <None Include="bin\Release\net8.0\Serilog.Sinks.File.dll" />
    <None Include="bin\Release\net8.0\SqlSugar.dll" />
    <None Include="bin\Release\net8.0\System.Configuration.ConfigurationManager.dll" />
    <None Include="bin\Release\net8.0\System.Security.Cryptography.ProtectedData.dll" />
    <None Include="bin\Release\net8.0\System.Security.Permissions.dll" />
    <None Include="bin\Release\net8.0\System.Windows.Extensions.dll" />
    <None Include="bin\Release\net8.0\ZstdSharp.dll" />
    <None Include="BMS.sln" />
    <None Include="obj\BMS.csproj.EntityFrameworkCore.targets" />
    <None Include="obj\BMS.csproj.nuget.g.props" />
    <None Include="obj\BMS.csproj.nuget.g.targets" />
    <None Include="obj\Debug\net8.0\apphost.exe" />
    <None Include="obj\Debug\net8.0\BMS.AssemblyInfoInputs.cache" />
    <None Include="obj\Debug\net8.0\BMS.assets.cache" />
    <None Include="obj\Debug\net8.0\BMS.csproj.AssemblyReference.cache" />
    <None Include="obj\Debug\net8.0\BMS.csproj.BuildWithSkipAnalyzers" />
    <None Include="obj\Debug\net8.0\BMS.csproj.CoreCompileInputs.cache" />
    <None Include="obj\Debug\net8.0\BMS.csproj.Up2Date" />
    <None Include="obj\Debug\net8.0\BMS.dll" />
    <None Include="obj\Debug\net8.0\BMS.GeneratedMSBuildEditorConfig.editorconfig" />
    <None Include="obj\Debug\net8.0\BMS.genruntimeconfig.cache" />
    <None Include="obj\Debug\net8.0\BMS.MvcApplicationPartsAssemblyInfo.cache" />
    <None Include="obj\Debug\net8.0\BMS.pdb" />
    <None Include="obj\Debug\net8.0\BMS.RazorAssemblyInfo.cache" />
    <None Include="obj\Debug\net8.0\refint\BMS.dll" />
    <None Include="obj\Debug\net8.0\ref\BMS.dll" />
    <None Include="obj\Debug\net8.0\staticwebassets\msbuild.BMS.Microsoft.AspNetCore.StaticWebAssets.props" />
    <None Include="obj\Debug\net8.0\staticwebassets\msbuild.build.BMS.props" />
    <None Include="obj\Debug\net8.0\staticwebassets\msbuild.buildMultiTargeting.BMS.props" />
    <None Include="obj\Debug\net8.0\staticwebassets\msbuild.buildTransitive.BMS.props" />
    <None Include="obj\project.nuget.cache" />
    <None Include="obj\Release\net8.0\apphost.exe" />
    <None Include="obj\Release\net8.0\BMS.AssemblyInfoInputs.cache" />
    <None Include="obj\Release\net8.0\BMS.assets.cache" />
    <None Include="obj\Release\net8.0\BMS.csproj.AssemblyReference.cache" />
    <None Include="obj\Release\net8.0\BMS.csproj.CoreCompileInputs.cache" />
    <None Include="obj\Release\net8.0\BMS.csproj.Up2Date" />
    <None Include="obj\Release\net8.0\BMS.dll" />
    <None Include="obj\Release\net8.0\BMS.GeneratedMSBuildEditorConfig.editorconfig" />
    <None Include="obj\Release\net8.0\BMS.genruntimeconfig.cache" />
    <None Include="obj\Release\net8.0\BMS.MvcApplicationPartsAssemblyInfo.cache" />
    <None Include="obj\Release\net8.0\BMS.pdb" />
    <None Include="obj\Release\net8.0\BMS.RazorAssemblyInfo.cache" />
    <None Include="obj\Release\net8.0\PubTmp\Out\BMS.dll" />
    <None Include="obj\Release\net8.0\PubTmp\Out\BMS.exe" />
    <None Include="obj\Release\net8.0\PubTmp\Out\BMS.pdb" />
    <None Include="obj\Release\net8.0\PubTmp\Out\BouncyCastle.Cryptography.dll" />
    <None Include="obj\Release\net8.0\PubTmp\Out\Google.Protobuf.dll" />
    <None Include="obj\Release\net8.0\PubTmp\Out\K4os.Compression.LZ4.dll" />
    <None Include="obj\Release\net8.0\PubTmp\Out\K4os.Compression.LZ4.Streams.dll" />
    <None Include="obj\Release\net8.0\PubTmp\Out\K4os.Hash.xxHash.dll" />
    <None Include="obj\Release\net8.0\PubTmp\Out\Microsoft.Extensions.DependencyModel.dll" />
    <None Include="obj\Release\net8.0\PubTmp\Out\MySql.Data.dll" />
    <None Include="obj\Release\net8.0\PubTmp\Out\Newtonsoft.Json.dll" />
    <None Include="obj\Release\net8.0\PubTmp\Out\runtimes\win-x64\native\comerr64.dll" />
    <None Include="obj\Release\net8.0\PubTmp\Out\runtimes\win-x64\native\gssapi64.dll" />
    <None Include="obj\Release\net8.0\PubTmp\Out\runtimes\win-x64\native\k5sprt64.dll" />
    <None Include="obj\Release\net8.0\PubTmp\Out\runtimes\win-x64\native\krb5_64.dll" />
    <None Include="obj\Release\net8.0\PubTmp\Out\runtimes\win-x64\native\krbcc64.dll" />
    <None Include="obj\Release\net8.0\PubTmp\Out\runtimes\win\lib\net8.0\System.Windows.Extensions.dll" />
    <None Include="obj\Release\net8.0\PubTmp\Out\Serilog.AspNetCore.dll" />
    <None Include="obj\Release\net8.0\PubTmp\Out\Serilog.dll" />
    <None Include="obj\Release\net8.0\PubTmp\Out\Serilog.Enrichers.Environment.dll" />
    <None Include="obj\Release\net8.0\PubTmp\Out\Serilog.Enrichers.Thread.dll" />
    <None Include="obj\Release\net8.0\PubTmp\Out\Serilog.Extensions.Hosting.dll" />
    <None Include="obj\Release\net8.0\PubTmp\Out\Serilog.Extensions.Logging.dll" />
    <None Include="obj\Release\net8.0\PubTmp\Out\Serilog.Formatting.Compact.dll" />
    <None Include="obj\Release\net8.0\PubTmp\Out\Serilog.Settings.Configuration.dll" />
    <None Include="obj\Release\net8.0\PubTmp\Out\Serilog.Sinks.Console.dll" />
    <None Include="obj\Release\net8.0\PubTmp\Out\Serilog.Sinks.Debug.dll" />
    <None Include="obj\Release\net8.0\PubTmp\Out\Serilog.Sinks.File.dll" />
    <None Include="obj\Release\net8.0\PubTmp\Out\SqlSugar.dll" />
    <None Include="obj\Release\net8.0\PubTmp\Out\System.Configuration.ConfigurationManager.dll" />
    <None Include="obj\Release\net8.0\PubTmp\Out\System.Security.Cryptography.ProtectedData.dll" />
    <None Include="obj\Release\net8.0\PubTmp\Out\System.Security.Permissions.dll" />
    <None Include="obj\Release\net8.0\PubTmp\Out\System.Windows.Extensions.dll" />
    <None Include="obj\Release\net8.0\PubTmp\Out\web.config" />
    <None Include="obj\Release\net8.0\PubTmp\Out\wwwroot\js\razor-safe.js" />
    <None Include="obj\Release\net8.0\PubTmp\Out\wwwroot\js\site.js" />
    <None Include="obj\Release\net8.0\PubTmp\Out\wwwroot\lib\axios.min.js" />
    <None Include="obj\Release\net8.0\PubTmp\Out\wwwroot\lib\bootstrap\dist\css\bootstrap-grid.css.map" />
    <None Include="obj\Release\net8.0\PubTmp\Out\wwwroot\lib\bootstrap\dist\css\bootstrap-grid.min.css.map" />
    <None Include="obj\Release\net8.0\PubTmp\Out\wwwroot\lib\bootstrap\dist\css\bootstrap-grid.rtl.css.map" />
    <None Include="obj\Release\net8.0\PubTmp\Out\wwwroot\lib\bootstrap\dist\css\bootstrap-grid.rtl.min.css.map" />
    <None Include="obj\Release\net8.0\PubTmp\Out\wwwroot\lib\bootstrap\dist\css\bootstrap-reboot.css.map" />
    <None Include="obj\Release\net8.0\PubTmp\Out\wwwroot\lib\bootstrap\dist\css\bootstrap-reboot.min.css.map" />
    <None Include="obj\Release\net8.0\PubTmp\Out\wwwroot\lib\bootstrap\dist\css\bootstrap-reboot.rtl.css.map" />
    <None Include="obj\Release\net8.0\PubTmp\Out\wwwroot\lib\bootstrap\dist\css\bootstrap-reboot.rtl.min.css.map" />
    <None Include="obj\Release\net8.0\PubTmp\Out\wwwroot\lib\bootstrap\dist\css\bootstrap-utilities.css.map" />
    <None Include="obj\Release\net8.0\PubTmp\Out\wwwroot\lib\bootstrap\dist\css\bootstrap-utilities.min.css.map" />
    <None Include="obj\Release\net8.0\PubTmp\Out\wwwroot\lib\bootstrap\dist\css\bootstrap-utilities.rtl.css.map" />
    <None Include="obj\Release\net8.0\PubTmp\Out\wwwroot\lib\bootstrap\dist\css\bootstrap-utilities.rtl.min.css.map" />
    <None Include="obj\Release\net8.0\PubTmp\Out\wwwroot\lib\bootstrap\dist\css\bootstrap.css.map" />
    <None Include="obj\Release\net8.0\PubTmp\Out\wwwroot\lib\bootstrap\dist\css\bootstrap.min.css.map" />
    <None Include="obj\Release\net8.0\PubTmp\Out\wwwroot\lib\bootstrap\dist\css\bootstrap.rtl.css.map" />
    <None Include="obj\Release\net8.0\PubTmp\Out\wwwroot\lib\bootstrap\dist\css\bootstrap.rtl.min.css.map" />
    <None Include="obj\Release\net8.0\PubTmp\Out\wwwroot\lib\bootstrap\dist\js\bootstrap.bundle.js" />
    <None Include="obj\Release\net8.0\PubTmp\Out\wwwroot\lib\bootstrap\dist\js\bootstrap.bundle.js.map" />
    <None Include="obj\Release\net8.0\PubTmp\Out\wwwroot\lib\bootstrap\dist\js\bootstrap.bundle.min.js" />
    <None Include="obj\Release\net8.0\PubTmp\Out\wwwroot\lib\bootstrap\dist\js\bootstrap.bundle.min.js.map" />
    <None Include="obj\Release\net8.0\PubTmp\Out\wwwroot\lib\bootstrap\dist\js\bootstrap.esm.js" />
    <None Include="obj\Release\net8.0\PubTmp\Out\wwwroot\lib\bootstrap\dist\js\bootstrap.esm.js.map" />
    <None Include="obj\Release\net8.0\PubTmp\Out\wwwroot\lib\bootstrap\dist\js\bootstrap.esm.min.js" />
    <None Include="obj\Release\net8.0\PubTmp\Out\wwwroot\lib\bootstrap\dist\js\bootstrap.esm.min.js.map" />
    <None Include="obj\Release\net8.0\PubTmp\Out\wwwroot\lib\bootstrap\dist\js\bootstrap.js" />
    <None Include="obj\Release\net8.0\PubTmp\Out\wwwroot\lib\bootstrap\dist\js\bootstrap.js.map" />
    <None Include="obj\Release\net8.0\PubTmp\Out\wwwroot\lib\bootstrap\dist\js\bootstrap.min.js" />
    <None Include="obj\Release\net8.0\PubTmp\Out\wwwroot\lib\bootstrap\dist\js\bootstrap.min.js.map" />
    <None Include="obj\Release\net8.0\PubTmp\Out\wwwroot\lib\bootstrap\LICENSE" />
    <None Include="obj\Release\net8.0\PubTmp\Out\wwwroot\lib\jquery-validation-unobtrusive\jquery.validate.unobtrusive.js" />
    <None Include="obj\Release\net8.0\PubTmp\Out\wwwroot\lib\jquery-validation-unobtrusive\jquery.validate.unobtrusive.min.js" />
    <None Include="obj\Release\net8.0\PubTmp\Out\wwwroot\lib\jquery-validation\dist\additional-methods.js" />
    <None Include="obj\Release\net8.0\PubTmp\Out\wwwroot\lib\jquery-validation\dist\additional-methods.min.js" />
    <None Include="obj\Release\net8.0\PubTmp\Out\wwwroot\lib\jquery-validation\dist\jquery.validate.js" />
    <None Include="obj\Release\net8.0\PubTmp\Out\wwwroot\lib\jquery-validation\dist\jquery.validate.min.js" />
    <None Include="obj\Release\net8.0\PubTmp\Out\wwwroot\lib\jquery-validation\LICENSE.md" />
    <None Include="obj\Release\net8.0\PubTmp\Out\wwwroot\lib\jquery\dist\jquery.js" />
    <None Include="obj\Release\net8.0\PubTmp\Out\wwwroot\lib\jquery\dist\jquery.min.js" />
    <None Include="obj\Release\net8.0\PubTmp\Out\wwwroot\lib\jquery\dist\jquery.min.map" />
    <None Include="obj\Release\net8.0\PubTmp\Out\wwwroot\lib\vue\vue.global.js" />
    <None Include="obj\Release\net8.0\PubTmp\Out\wwwroot\lib\vue\vue.js" />
    <None Include="obj\Release\net8.0\PubTmp\Out\wwwroot\lib\vue\vue.min.js" />
    <None Include="obj\Release\net8.0\PubTmp\Out\ZstdSharp.dll" />
    <None Include="obj\Release\net8.0\refint\BMS.dll" />
    <None Include="obj\Release\net8.0\ref\BMS.dll" />
    <None Include="obj\Release\net8.0\staticwebassets\msbuild.BMS.Microsoft.AspNetCore.StaticWebAssets.props" />
    <None Include="obj\Release\net8.0\staticwebassets\msbuild.build.BMS.props" />
    <None Include="obj\Release\net8.0\staticwebassets\msbuild.buildMultiTargeting.BMS.props" />
    <None Include="obj\Release\net8.0\staticwebassets\msbuild.buildTransitive.BMS.props" />
  </ItemGroup>

  <ItemGroup>
    <!-- SqlSugar ORM -->
    <PackageReference Include="SqlSugar" Version="5.1.4.195" />
    <!-- MySQL数据库驱动 -->
    <PackageReference Include="MySql.Data" Version="9.0.0" />
    <!-- 身份验证相关 -->
    <PackageReference Include="Microsoft.AspNetCore.Authentication.Cookies" Version="2.2.0" />
    <!-- JSON处理 -->
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <!-- Serilog 日志框架 -->
    <PackageReference Include="Serilog.AspNetCore" Version="8.0.0" />
    <PackageReference Include="Serilog.Sinks.Console" Version="5.0.1" />
    <PackageReference Include="Serilog.Sinks.File" Version="5.0.0" />
    <PackageReference Include="Serilog.Enrichers.Environment" Version="2.3.0" />
    <PackageReference Include="Serilog.Enrichers.Thread" Version="3.1.0" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="logs\" />
    <Folder Include="Models\ViewModels\" />
  </ItemGroup>

</Project>
