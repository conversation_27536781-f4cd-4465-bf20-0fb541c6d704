﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace BMS.Models.Entities
{
    ///<summary>
    ///套装属性表
    ///</summary>
    [SugarTable("suit_attribute")]
    public partial class suit_attribute
    {
           public suit_attribute(){


           }
           /// <summary>
           /// Desc:自增主键
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true,IsIdentity=true)]
           public int id {get;set;}

           /// <summary>
           /// Desc:套装ID（关联suit_config表）
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string suit_id {get;set;}

           /// <summary>
           /// Desc:激活所需件数（2件/3件/4件/5件）
           /// Default:
           /// Nullable:False
           /// </summary>           
           public int piece_count {get;set;}

           /// <summary>
           /// Desc:属性类型（攻击/命中/防御/速度/闪避/生命/魔法/加深/抵消/吸血/吸魔）
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string attribute_type {get;set;}

           /// <summary>
           /// Desc:属性值（数值或百分比字符串）
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string attribute_value {get;set;}

           /// <summary>
           /// Desc:是否百分比（0-数值型，1-百分比型）
           /// Default:true
           /// Nullable:True
           /// </summary>           
           public bool? is_percentage {get;set;}

           /// <summary>
           /// Desc:创建时间
           /// Default:CURRENT_TIMESTAMP
           /// Nullable:True
           /// </summary>           
           public DateTime? create_time {get;set;}

    }
}
