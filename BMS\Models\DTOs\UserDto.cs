using System.ComponentModel.DataAnnotations;

namespace BMS.Models.DTOs
{
    /// <summary>
    /// 用户信息DTO
    /// </summary>
    public class UserDto
    {
        /// <summary>
        /// 用户ID
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 用户名/账号
        /// </summary>
        public string Username { get; set; } = string.Empty;

        /// <summary>
        /// 角色昵称
        /// </summary>
        public string? Nickname { get; set; }

        /// <summary>
        /// 性别
        /// </summary>
        public string? Sex { get; set; }

        /// <summary>
        /// 性别文本
        /// </summary>
        public string SexText => Sex ?? "未设置";

        /// <summary>
        /// VIP等级
        /// </summary>
        public int? VipLevel { get; set; }

        /// <summary>
        /// VIP等级文本
        /// </summary>
        public string VipLevelText => VipLevel.HasValue && VipLevel > 0 ? $"VIP{VipLevel}" : "普通用户";

        /// <summary>
        /// VIP积分
        /// </summary>
        public int? VipScore { get; set; }

        /// <summary>
        /// 金币
        /// </summary>
        public long? Gold { get; set; }

        /// <summary>
        /// 元宝
        /// </summary>
        public int? Yuanbao { get; set; }

        /// <summary>
        /// 水晶
        /// </summary>
        public int? Crystal { get; set; }

        /// <summary>
        /// 称号
        /// </summary>
        public string? Title { get; set; }

        /// <summary>
        /// 主宠物名称
        /// </summary>
        public string? MainPetName { get; set; }

        /// <summary>
        /// 注册时间
        /// </summary>
        public DateTime? RegTime { get; set; }

        /// <summary>
        /// 最后登录时间
        /// </summary>
        public DateTime? LastLoginTime { get; set; }
    }
    
    /// <summary>
    /// 用户查询DTO
    /// </summary>
    public class UserQueryDto : PagedQueryDto
    {
        /// <summary>
        /// 用户ID
        /// </summary>
        public int? UserId { get; set; }

        /// <summary>
        /// 用户名
        /// </summary>
        public string? Username { get; set; }

        /// <summary>
        /// 昵称
        /// </summary>
        public string? Nickname { get; set; }

        /// <summary>
        /// 性别
        /// </summary>
        public string? Sex { get; set; }

        /// <summary>
        /// VIP等级
        /// </summary>
        public int? VipLevel { get; set; }
    }

    /// <summary>
    /// 用户创建DTO
    /// </summary>
    public class UserCreateDto
    {
        /// <summary>
        /// 用户名
        /// </summary>
        [Required(ErrorMessage = "用户名不能为空")]
        [StringLength(50, ErrorMessage = "用户名长度不能超过50个字符")]
        public string Username { get; set; } = string.Empty;

        /// <summary>
        /// 密码
        /// </summary>
        [Required(ErrorMessage = "密码不能为空")]
        [StringLength(20, MinimumLength = 6, ErrorMessage = "密码长度必须在6-20个字符之间")]
        public string Password { get; set; } = string.Empty;

        /// <summary>
        /// 昵称
        /// </summary>
        [StringLength(50, ErrorMessage = "昵称长度不能超过50个字符")]
        public string? Nickname { get; set; }

        /// <summary>
        /// 性别
        /// </summary>
        public string? Sex { get; set; }

        /// <summary>
        /// VIP等级
        /// </summary>
        public int? VipLevel { get; set; }

        /// <summary>
        /// VIP积分
        /// </summary>
        public int? VipScore { get; set; }

        /// <summary>
        /// 金币
        /// </summary>
        public long? Gold { get; set; }

        /// <summary>
        /// 元宝
        /// </summary>
        public int? Yuanbao { get; set; }

        /// <summary>
        /// 水晶
        /// </summary>
        public int? Crystal { get; set; }

        /// <summary>
        /// 称号
        /// </summary>
        public string? Title { get; set; }
    }
    
    /// <summary>
    /// 用户更新DTO
    /// </summary>
    public class UserUpdateDto
    {
        /// <summary>
        /// 用户ID
        /// </summary>
        [Required]
        public int Id { get; set; }

        /// <summary>
        /// 用户名
        /// </summary>
        [Required(ErrorMessage = "用户名不能为空")]
        [StringLength(50, ErrorMessage = "用户名长度不能超过50个字符")]
        public string Username { get; set; } = string.Empty;

        /// <summary>
        /// 昵称
        /// </summary>
        public string? Nickname { get; set; }

        /// <summary>
        /// 性别
        /// </summary>
        public string? Sex { get; set; }

        /// <summary>
        /// VIP等级
        /// </summary>
        public int? VipLevel { get; set; }

        /// <summary>
        /// VIP积分
        /// </summary>
        public int? VipScore { get; set; }

        /// <summary>
        /// 金币
        /// </summary>
        public long? Gold { get; set; }

        /// <summary>
        /// 元宝
        /// </summary>
        public int? Yuanbao { get; set; }

        /// <summary>
        /// 水晶
        /// </summary>
        public int? Crystal { get; set; }

        /// <summary>
        /// 称号
        /// </summary>
        public string? Title { get; set; }

        /// <summary>
        /// 主宠物名称
        /// </summary>
        public string? MainPetName { get; set; }
    }

    /// <summary>
    /// 用户资产更新DTO
    /// </summary>
    public class UserAssetUpdateDto
    {
        /// <summary>
        /// 用户ID
        /// </summary>
        [Required]
        public int Id { get; set; }

        /// <summary>
        /// 金币
        /// </summary>
        public long? Gold { get; set; }

        /// <summary>
        /// 元宝
        /// </summary>
        public int? Yuanbao { get; set; }

        /// <summary>
        /// 水晶
        /// </summary>
        public int? Crystal { get; set; }

        /// <summary>
        /// VIP等级
        /// </summary>
        public int? VipLevel { get; set; }

        /// <summary>
        /// VIP积分
        /// </summary>
        public int? VipScore { get; set; }
    }

    /// <summary>
    /// 用户删除DTO
    /// </summary>
    public class UserDeleteDto
    {
        /// <summary>
        /// 用户ID
        /// </summary>
        [Required]
        public int Id { get; set; }
    }

    /// <summary>
    /// 用户重置密码DTO
    /// </summary>
    public class UserResetPasswordDto
    {
        /// <summary>
        /// 用户ID
        /// </summary>
        [Required]
        public int Id { get; set; }

        /// <summary>
        /// 新密码
        /// </summary>
        [Required(ErrorMessage = "新密码不能为空")]
        [StringLength(20, MinimumLength = 6, ErrorMessage = "密码长度必须在6-20个字符之间")]
        public string NewPassword { get; set; } = string.Empty;
    }

    /// <summary>
    /// 用户统计信息DTO
    /// </summary>
    public class UserStatisticsDto
    {
        /// <summary>
        /// 用户总数
        /// </summary>
        public int TotalUsers { get; set; }

        /// <summary>
        /// 今日新增用户
        /// </summary>
        public int TodayNewUsers { get; set; }

        /// <summary>
        /// VIP用户数量
        /// </summary>
        public int VipUsers { get; set; }

        /// <summary>
        /// 金币总量
        /// </summary>
        public long TotalGold { get; set; }
    }
}