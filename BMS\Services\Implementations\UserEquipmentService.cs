using BMS.Models.DTOs;
using BMS.Models.Entities;
using BMS.Models.Common;
using BMS.Services.Interfaces;
using SqlSugar;

namespace BMS.Services.Implementations;

/// <summary>
/// 用户装备服务实现
/// </summary>
public class UserEquipmentService : IUserEquipmentService
{
    private readonly IDbService _dbService;

    public UserEquipmentService(IDbService dbService)
    {
        _dbService = dbService;
    }

    /// <summary>
    /// 获取用户装备列表（分页）
    /// </summary>
    /// <param name="queryDto">查询条件</param>
    /// <returns>分页结果</returns>
    public async Task<PagedResult<UserEquipmentDto>> GetUserEquipmentsAsync(UserEquipmentQueryDto queryDto)
    {
        try
        {
            Console.WriteLine($"开始查询用户装备列表，参数: {System.Text.Json.JsonSerializer.Serialize(queryDto)}");

            // 检查基础数据
            var userCount = await _dbService.Queryable<user>().CountAsync();
            var equipmentCount = await _dbService.Queryable<equipment>().CountAsync();
            var equipmentTypeCount = await _dbService.Queryable<equipment_type>().CountAsync();
            var userEquipmentCount = await _dbService.Queryable<user_equipment>().CountAsync();

            Console.WriteLine($"基础数据统计 - 用户: {userCount}, 装备配置: {equipmentCount}, 装备类型: {equipmentTypeCount}, 用户装备: {userEquipmentCount}");

            // 构建查询条件 - 使用分步联表方式（更稳定）
            var query = _dbService.GetClient().Queryable<user_equipment>()
                .LeftJoin<user>((ue, u) => ue.user_id == u.id)
                .LeftJoin<equipment>((ue, u, e) => ue.equip_id == e.equip_id)
                .LeftJoin<equipment_type>((ue, u, e, et) => e.equip_type_id == et.equip_type_id)
                .Where((ue, u, e, et) => true);

            Console.WriteLine("基础查询构建完成");

            // 应用筛选条件 - 修正参数顺序
            if (queryDto.UserId.HasValue)
                query = query.Where((ue, u, e, et) => ue.user_id == queryDto.UserId.Value);

            if (!string.IsNullOrWhiteSpace(queryDto.UserName))
                query = query.Where((ue, u, e, et) => u.username.Contains(queryDto.UserName));

            if (!string.IsNullOrWhiteSpace(queryDto.Name))
                query = query.Where((ue, u, e, et) => ue.name.Contains(queryDto.Name));

            if (!string.IsNullOrEmpty(queryDto.EquipTypeId))
                query = query.Where((ue, u, e, et) => et.equip_type_id == queryDto.EquipTypeId);

            if (queryDto.IsEquipped.HasValue)
                query = query.Where((ue, u, e, et) => ue.is_equipped == queryDto.IsEquipped.Value);

            if (queryDto.MinStrengthenLevel.HasValue)
                query = query.Where((ue, u, e, et) => ue.strengthen_level >= queryDto.MinStrengthenLevel.Value);

            if (queryDto.MaxStrengthenLevel.HasValue)
                query = query.Where((ue, u, e, et) => ue.strengthen_level <= queryDto.MaxStrengthenLevel.Value);

            Console.WriteLine("开始执行分页查询");

            // 使用SqlSugar的ToPageListAsync进行分页查询 - 修正参数顺序
            RefAsync<int> totalCount = 0;
            var equipments = await query
                .OrderBy((ue, u, e, et) => ue.create_time, OrderByType.Desc)
                .Select((ue, u, e, et) => new UserEquipmentDto
                {
                    Id = ue.id,
                    UserId = ue.user_id,
                    UserName = u.username ?? "",
                    EquipId = ue.equip_id,
                    Name = ue.name,
                    Icon = ue.icon ?? "",
                    EquipTypeId = et.equip_type_id ?? "",
                    EquipTypeName = et.type_name ?? "",
                    StrengthenLevel = ue.strengthen_level ?? 0,
                    Slot = ue.slot ?? 0,
                    Position = ue.position,
                    IsEquipped = ue.is_equipped ?? false,
                    CreateTime = ue.create_time ?? DateTime.Now
                })
                .ToPageListAsync(queryDto.Page, queryDto.PageSize, totalCount);

            Console.WriteLine($"查询完成，返回 {equipments?.Count ?? 0} 条记录，总数: {totalCount.Value}");

            // 获取装备详情
            Console.WriteLine("开始获取装备详情");
            foreach (var equipment in equipments)
            {
                try
                {
                    equipment.Detail = await GetEquipmentDetailAsync(equipment.EquipId);
                }
                catch (Exception detailEx)
                {
                    Console.WriteLine($"获取装备详情失败 EquipId: {equipment.EquipId}, 错误: {detailEx.Message}");
                    // 继续处理其他装备，不因为单个装备详情失败而中断整个查询
                }
            }

            Console.WriteLine("用户装备列表查询成功完成");
            return new PagedResult<UserEquipmentDto>(equipments, queryDto.Page, queryDto.PageSize, totalCount.Value);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"获取用户装备列表失败: {ex.Message}");
            Console.WriteLine($"堆栈跟踪: {ex.StackTrace}");

            // 如果联表查询失败，尝试简化查询
            if (ex.Message.Contains("Unknown column") || ex.Message.Contains("on clause"))
            {
                Console.WriteLine("尝试使用简化查询...");
                try
                {
                    return await GetUserEquipmentsSimpleAsync(queryDto);
                }
                catch (Exception simpleEx)
                {
                    Console.WriteLine($"简化查询也失败: {simpleEx.Message}");
                }
            }

            throw new ApplicationException($"获取用户装备列表失败: {ex.Message}", ex);
        }
    }

    /// <summary>
    /// 简化版本的用户装备查询（备用方案）
    /// </summary>
    /// <param name="queryDto">查询条件</param>
    /// <returns>分页结果</returns>
    private async Task<PagedResult<UserEquipmentDto>> GetUserEquipmentsSimpleAsync(UserEquipmentQueryDto queryDto)
    {
        Console.WriteLine("使用简化查询方案");

        // 先查询用户装备基础信息
        var userEquipmentQuery = _dbService.Queryable<user_equipment>();

        if (queryDto.UserId.HasValue)
            userEquipmentQuery = userEquipmentQuery.Where(ue => ue.user_id == queryDto.UserId.Value);

        if (!string.IsNullOrWhiteSpace(queryDto.Name))
            userEquipmentQuery = userEquipmentQuery.Where(ue => ue.name.Contains(queryDto.Name));

        if (queryDto.IsEquipped.HasValue)
            userEquipmentQuery = userEquipmentQuery.Where(ue => ue.is_equipped == queryDto.IsEquipped.Value);

        if (queryDto.MinStrengthenLevel.HasValue)
            userEquipmentQuery = userEquipmentQuery.Where(ue => ue.strengthen_level >= queryDto.MinStrengthenLevel.Value);

        if (queryDto.MaxStrengthenLevel.HasValue)
            userEquipmentQuery = userEquipmentQuery.Where(ue => ue.strengthen_level <= queryDto.MaxStrengthenLevel.Value);

        RefAsync<int> totalCount = 0;
        var userEquipments = await userEquipmentQuery
            .OrderBy(ue => ue.create_time, OrderByType.Desc)
            .ToPageListAsync(queryDto.Page, queryDto.PageSize, totalCount);

        // 手动填充关联数据
        var result = new List<UserEquipmentDto>();
        foreach (var ue in userEquipments)
        {
            var dto = new UserEquipmentDto
            {
                Id = ue.id,
                UserId = ue.user_id,
                EquipId = ue.equip_id,
                Name = ue.name,
                Icon = ue.icon ?? "",
                EquipTypeId = "", // 暂时为空
                EquipTypeName = "", // 暂时为空
                StrengthenLevel = ue.strengthen_level ?? 0,
                Slot = ue.slot ?? 0,
                Position = ue.position,
                IsEquipped = ue.is_equipped ?? false,
                CreateTime = ue.create_time ?? DateTime.Now,
                UserName = "" // 暂时为空
            };

            // 尝试获取用户名
            try
            {
                var user = await _dbService.Queryable<user>().Where(u => u.id == ue.user_id).FirstAsync();
                dto.UserName = user?.username ?? "";
            }
            catch { }

            // 尝试获取装备类型信息
            try
            {
                var equipment = await _dbService.Queryable<equipment>().Where(e => e.equip_id == ue.equip_id).FirstAsync();
                if (equipment != null)
                {
                    var equipType = await _dbService.Queryable<equipment_type>().Where(et => et.equip_type_id == equipment.equip_type_id).FirstAsync();
                    dto.EquipTypeId = equipType?.equip_type_id ?? "";
                    dto.EquipTypeName = equipType?.type_name ?? "";
                }
            }
            catch { }

            result.Add(dto);
        }

        return new PagedResult<UserEquipmentDto>(result, queryDto.Page, queryDto.PageSize, totalCount.Value);
    }

    /// <summary>
    /// 简化版本的根据ID获取用户装备详情（备用方案）
    /// </summary>
    /// <param name="id">装备ID</param>
    /// <returns>装备详情</returns>
    private async Task<UserEquipmentDto?> GetUserEquipmentByIdSimpleAsync(int id)
    {
        Console.WriteLine($"使用简化查询获取用户装备详情，ID: {id}");

        // 先查询用户装备基础信息
        var userEquipment = await _dbService.Queryable<user_equipment>()
            .Where(ue => ue.id == id)
            .FirstAsync();

        if (userEquipment == null)
        {
            Console.WriteLine($"未找到ID为 {id} 的用户装备");
            return null;
        }

        var dto = new UserEquipmentDto
        {
            Id = userEquipment.id,
            UserId = userEquipment.user_id,
            EquipId = userEquipment.equip_id,
            Name = userEquipment.name,
            Icon = userEquipment.icon ?? "",
            EquipTypeId = "", // 暂时为空
            EquipTypeName = "", // 暂时为空
            StrengthenLevel = userEquipment.strengthen_level ?? 0,
            Slot = userEquipment.slot ?? 0,
            Position = userEquipment.position,
            IsEquipped = userEquipment.is_equipped ?? false,
            CreateTime = userEquipment.create_time ?? DateTime.Now,
            UserName = "" // 暂时为空
        };

        // 尝试获取用户名
        try
        {
            var user = await _dbService.Queryable<user>().Where(u => u.id == userEquipment.user_id).FirstAsync();
            dto.UserName = user?.username ?? "";
        }
        catch (Exception ex)
        {
            Console.WriteLine($"获取用户名失败: {ex.Message}");
        }

        // 尝试获取装备类型信息
        try
        {
            var equipment = await _dbService.Queryable<equipment>().Where(e => e.equip_id == userEquipment.equip_id).FirstAsync();
            if (equipment != null)
            {
                var equipType = await _dbService.Queryable<equipment_type>().Where(et => et.equip_type_id == equipment.equip_type_id).FirstAsync();
                dto.EquipTypeId = equipType?.equip_type_id ?? "";
                dto.EquipTypeName = equipType?.type_name ?? "";
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"获取装备类型信息失败: {ex.Message}");
        }

        // 获取装备详情
        try
        {
            dto.Detail = await GetEquipmentDetailAsync(dto.EquipId);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"获取装备详情失败: {ex.Message}");
        }

        return dto;
    }

    /// <summary>
    /// 根据ID获取用户装备详情
    /// </summary>
    /// <param name="id">装备ID</param>
    /// <returns>装备详情</returns>
    public async Task<UserEquipmentDto?> GetUserEquipmentByIdAsync(int id)
    {
        try
        {
            Console.WriteLine($"开始查询用户装备详情，ID: {id}");

            var equipment = await _dbService.GetClient().Queryable<user_equipment>()
                .LeftJoin<user>((ue, u) => ue.user_id == u.id)
                .LeftJoin<equipment>((ue, u, e) => ue.equip_id == e.equip_id)
                .LeftJoin<equipment_type>((ue, u, e, et) => e.equip_type_id == et.equip_type_id)
                .Where((ue, u, e, et) => ue.id == id)
                .Select((ue, u, e, et) => new UserEquipmentDto
                {
                    Id = ue.id,
                    UserId = ue.user_id,
                    UserName = u.username ?? "",
                    EquipId = ue.equip_id,
                    Name = ue.name,
                    Icon = ue.icon ?? "",
                    EquipTypeId = et.equip_type_id ?? "",
                    EquipTypeName = et.type_name ?? "",
                    StrengthenLevel = ue.strengthen_level ?? 0,
                    Slot = ue.slot ?? 0,
                    Position = ue.position,
                    IsEquipped = ue.is_equipped ?? false,
                    CreateTime = ue.create_time ?? DateTime.Now
                })
            .FirstAsync();

            if (equipment != null)
            {
                Console.WriteLine($"查询到装备信息: {equipment.Name}");
                equipment.Detail = await GetEquipmentDetailAsync(equipment.EquipId);
            }
            else
            {
                Console.WriteLine($"未找到ID为 {id} 的用户装备");
            }

            return equipment;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"获取用户装备详情失败: {ex.Message}");

            // 如果联表查询失败，尝试简化查询
            if (ex.Message.Contains("Unknown column") || ex.Message.Contains("on clause"))
            {
                Console.WriteLine("尝试使用简化查询...");
                try
                {
                    return await GetUserEquipmentByIdSimpleAsync(id);
                }
                catch (Exception simpleEx)
                {
                    Console.WriteLine($"简化查询也失败: {simpleEx.Message}");
                }
            }

            throw new ApplicationException($"获取用户装备详情失败: {ex.Message}", ex);
        }
    }

    /// <summary>
    /// 创建用户装备
    /// </summary>
    /// <param name="createDto">创建DTO</param>
    /// <returns>操作结果</returns>
    public async Task<ApiResult<UserEquipmentDto>> CreateUserEquipmentAsync(UserEquipmentCreateDto createDto)
    {
        try
        {
            // 验证用户是否存在
            var userExists = await  _dbService.Queryable<user>().AnyAsync(u => u.id == createDto.UserId);
            if (!userExists)
            {
                return ApiResult<UserEquipmentDto>.Fail("用户不存在");
            }

            // 验证装备是否已存在
            var exists = await ExistsAsync(createDto.EquipId, createDto.UserId);
            if (exists)
            {
                return ApiResult<UserEquipmentDto>.Fail("该装备已存在");
            }

            // 验证装备类型是否存在
            var equipTypeExists = await  _dbService.Queryable<equipment_type>().AnyAsync(et => et.equip_type_id == createDto.EquipTypeId);
            if (!equipTypeExists)
            {
                return ApiResult<UserEquipmentDto>.Fail("装备类型不存在");
            }

            // 解析装备类型ID为整数
            if (!int.TryParse(createDto.EquipTypeId, out int equipTypeIdInt))
            {
                return ApiResult<UserEquipmentDto>.Fail("装备类型ID格式不正确");
            }

            // 创建装备实体
            var userEquipment = new user_equipment
            {
                user_id = createDto.UserId,
                equip_id = createDto.EquipId,
                name = createDto.Name,
                icon = createDto.Icon,
                equip_type_id = equipTypeIdInt,
                strengthen_level = createDto.StrengthenLevel,
                slot = createDto.Slot,
                position = createDto.Position,
                is_equipped = createDto.IsEquipped,
                create_time = DateTime.Now,
                update_time = DateTime.Now
            };

            // 插入数据库
            var insertedId = await  _dbService.Insertable(userEquipment).ExecuteReturnIdentityAsync();
            userEquipment.id = insertedId;

            // 返回创建的装备详情
            var result = await GetUserEquipmentByIdAsync(insertedId);
            return ApiResult<UserEquipmentDto>.Ok(result, "创建用户装备成功");
        }
        catch (Exception ex)
        {
            return ApiResult<UserEquipmentDto>.Fail($"创建用户装备失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 更新用户装备
    /// </summary>
    /// <param name="updateDto">更新DTO</param>
    /// <returns>操作结果</returns>
    public async Task<ApiResult<UserEquipmentDto>> UpdateUserEquipmentAsync(UserEquipmentUpdateDto updateDto)
    {
        try
        {
            // 检查装备是否存在
            var existingEquipment = await  _dbService.Queryable<user_equipment>().FirstAsync(ue => ue.id == updateDto.Id);
            if (existingEquipment == null)
            {
                return ApiResult<UserEquipmentDto>.Fail("装备不存在");
            }

            // 更新字段
            if (!string.IsNullOrWhiteSpace(updateDto.Name))
                existingEquipment.name = updateDto.Name;

            if (!string.IsNullOrWhiteSpace(updateDto.Icon))
                existingEquipment.icon = updateDto.Icon;

            if (updateDto.StrengthenLevel.HasValue)
                existingEquipment.strengthen_level = updateDto.StrengthenLevel.Value;

            if (updateDto.Slot.HasValue)
                existingEquipment.slot = updateDto.Slot.Value;

            if (updateDto.Position.HasValue)
                existingEquipment.position = updateDto.Position;

            if (updateDto.IsEquipped.HasValue)
                existingEquipment.is_equipped = updateDto.IsEquipped.Value;

            existingEquipment.update_time = DateTime.Now;

            // 更新数据库
            await  _dbService.Updateable(existingEquipment).ExecuteCommandAsync();

            // 返回更新后的装备详情
            var result = await GetUserEquipmentByIdAsync(updateDto.Id);
            return ApiResult<UserEquipmentDto>.Ok(result, "更新用户装备成功");
        }
        catch (Exception ex)
        {
            return ApiResult<UserEquipmentDto>.Fail($"更新用户装备失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 删除用户装备
    /// </summary>
    /// <param name="deleteDto">删除DTO</param>
    /// <returns>操作结果</returns>
    public async Task<ApiResult<bool>> DeleteUserEquipmentAsync(UserEquipmentDeleteDto deleteDto)
    {
        try
        {
            // 检查装备是否存在
            var equipment = await  _dbService.Queryable<user_equipment>().FirstAsync(ue => ue.id == deleteDto.Id);
            if (equipment == null)
            {
                return ApiResult<bool>.Fail("装备不存在");
            }

            // 检查装备是否已装备
            if (equipment.is_equipped == true)
            {
                return ApiResult<bool>.Fail("已装备的装备不能删除，请先卸下装备");
            }

            // 删除装备
            await  _dbService.Deleteable<user_equipment>().Where(ue => ue.id == deleteDto.Id).ExecuteCommandAsync();

            return ApiResult<bool>.Ok(true, "删除用户装备成功");
        }
        catch (Exception ex)
        {
            return ApiResult<bool>.Fail($"删除用户装备失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 强化装备
    /// </summary>
    /// <param name="strengthenDto">强化DTO</param>
    /// <returns>操作结果</returns>
    public async Task<ApiResult<UserEquipmentDto>> StrengthenEquipmentAsync(UserEquipmentStrengthenDto strengthenDto)
    {
        try
        {
            // 检查装备是否存在
            var equipment = await  _dbService.Queryable<user_equipment>().FirstAsync(ue => ue.id == strengthenDto.Id);
            if (equipment == null)
            {
                return ApiResult<UserEquipmentDto>.Fail("装备不存在");
            }

            // 检查强化等级
            if (equipment.strengthen_level >= strengthenDto.TargetLevel)
            {
                return ApiResult<UserEquipmentDto>.Fail("目标强化等级必须高于当前等级");
            }

            if (strengthenDto.TargetLevel > 20)
            {
                return ApiResult<UserEquipmentDto>.Fail("强化等级不能超过20级");
            }

            // 更新强化等级
            equipment.strengthen_level = strengthenDto.TargetLevel;
            equipment.update_time = DateTime.Now;

            await  _dbService.Updateable(equipment).ExecuteCommandAsync();

            // 返回更新后的装备详情
            var result = await GetUserEquipmentByIdAsync(strengthenDto.Id);
            return ApiResult<UserEquipmentDto>.Ok(result, $"装备强化成功，当前等级：{strengthenDto.TargetLevel}");
        }
        catch (Exception ex)
        {
            return ApiResult<UserEquipmentDto>.Fail($"装备强化失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 穿戴装备
    /// </summary>
    /// <param name="wearDto">穿戴DTO</param>
    /// <returns>操作结果</returns>
    public async Task<ApiResult<UserEquipmentDto>> WearEquipmentAsync(UserEquipmentWearDto wearDto)
    {
        try
        {
            // 获取有效的装备位置（如果前端传null，则使用默认值1）
            int targetPosition = wearDto.GetValidPosition();
            Console.WriteLine($"开始穿戴装备，参数: ID={wearDto.Id}, UserId={wearDto.UserId}, Position={wearDto.Position} -> 实际位置={targetPosition}");

            // 参数验证
            if (!wearDto.IsValid(out string validationError))
            {
                Console.WriteLine($"参数验证失败: {validationError}");
                return ApiResult<UserEquipmentDto>.Fail(validationError);
            }

            // 检查装备是否存在
            var equipment = await  _dbService.Queryable<user_equipment>().FirstAsync(ue => ue.id == wearDto.Id);
            if (equipment == null)
            {
                Console.WriteLine($"装备不存在，ID: {wearDto.Id}");
                return ApiResult<UserEquipmentDto>.Fail("装备不存在");
            }

            Console.WriteLine($"找到装备: {equipment.name}, 用户ID: {equipment.user_id}, 当前状态: {(equipment.is_equipped == true ? "已装备" : "未装备")}");

            // 检查装备是否已装备
            if (equipment.is_equipped == true)
            {
                Console.WriteLine($"装备已穿戴，当前位置: {equipment.position}");
                return ApiResult<UserEquipmentDto>.Fail("装备已穿戴");
            }

            // 检查该位置是否已有装备
            Console.WriteLine($"检查位置 {targetPosition} 是否已有装备");
            var existingEquipment = await  _dbService.Queryable<user_equipment>()
                .FirstAsync(ue => ue.user_id == equipment.user_id &&
                                 ue.is_equipped == true);

            if (existingEquipment != null)
            {
                Console.WriteLine($"位置 {targetPosition} 已有装备: {existingEquipment.name}，将自动卸下");
                // 卸下原有装备
                existingEquipment.is_equipped = false;
                existingEquipment.position = null;
                existingEquipment.update_time = DateTime.Now;
                var unequipResult = await  _dbService.Updateable(existingEquipment).ExecuteCommandAsync();
                Console.WriteLine($"卸下原装备结果: {unequipResult} 行受影响");
            }
            else
            {
                Console.WriteLine($"位置 {targetPosition} 当前无装备");
            }

            // 穿戴新装备
            Console.WriteLine($"开始穿戴装备: {equipment.name} 到位置 {targetPosition}");
            equipment.is_equipped = true;
            equipment.position = targetPosition;
            equipment.update_time = DateTime.Now;

            var equipResult = await  _dbService.Updateable(equipment).ExecuteCommandAsync();
            Console.WriteLine($"穿戴装备结果: {equipResult} 行受影响");

            if (equipResult <= 0)
            {
                Console.WriteLine("穿戴装备失败，数据库更新无效果");
                return ApiResult<UserEquipmentDto>.Fail("装备穿戴失败，数据库更新失败");
            }

            // 返回更新后的装备详情
            Console.WriteLine("获取更新后的装备详情");
            var result = await GetUserEquipmentByIdAsync(wearDto.Id);

            Console.WriteLine("装备穿戴成功完成");
            return ApiResult<UserEquipmentDto>.Ok(result, "装备穿戴成功");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"装备穿戴异常: {ex.Message}");
            Console.WriteLine($"异常堆栈: {ex.StackTrace}");
            return ApiResult<UserEquipmentDto>.Fail($"装备穿戴失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 脱下装备
    /// </summary>
    /// <param name="equipmentId">装备ID</param>
    /// <returns>操作结果</returns>
    public async Task<ApiResult<UserEquipmentDto>> UnwearEquipmentAsync(int equipmentId)
    {
        try
        {
            // 检查装备是否存在
            var equipment = await  _dbService.Queryable<user_equipment>().FirstAsync(ue => ue.id == equipmentId);
            if (equipment == null)
            {
                return ApiResult<UserEquipmentDto>.Fail("装备不存在");
            }

            // 检查装备是否已装备
            if (equipment.is_equipped != true)
            {
                return ApiResult<UserEquipmentDto>.Fail("装备未穿戴");
            }

            // 脱下装备
            equipment.is_equipped = false;
            equipment.position = null;
            equipment.update_time = DateTime.Now;

            await  _dbService.Updateable(equipment).ExecuteCommandAsync();

            // 返回更新后的装备详情
            var result = await GetUserEquipmentByIdAsync(equipmentId);
            return ApiResult<UserEquipmentDto>.Ok(result, "装备卸下成功");
        }
        catch (Exception ex)
        {
            return ApiResult<UserEquipmentDto>.Fail($"装备卸下失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 获取装备类型选项
    /// </summary>
    /// <returns>装备类型列表</returns>
    public async Task<List<OptionDto>> GetEquipmentTypeOptionsAsync()
    {
        try
        {
            return await  _dbService.Queryable<equipment_type>()
                .Select(et => new OptionDto
                {
                    Value = et.equip_type_id,
                    Label = et.type_name
                })
                .ToListAsync();
        }
        catch (Exception ex)
        {
            throw new ApplicationException($"获取装备类型选项失败: {ex.Message}", ex);
        }
    }

    /// <summary>
    /// 获取装备配置选项
    /// </summary>
    /// <returns>装备配置列表</returns>
    public async Task<List<EquipmentConfigOptionDto>> GetEquipmentConfigOptionsAsync()
    {
        try
        {
            return await  _dbService.Queryable<equipment>()
                .Select(e => new EquipmentConfigOptionDto
                {
                    Value = e.equip_id,
                    Label = e.name,
                    Name = e.name,
                    Icon = e.icon,
                    EquipTypeId = e.equip_type_id
                })
                .ToListAsync();
        }
        catch (Exception ex)
        {
            throw new ApplicationException($"获取装备配置选项失败: {ex.Message}", ex);
        }
    }

    /// <summary>
    /// 检查装备是否存在
    /// </summary>
    /// <param name="equipId">装备唯一ID</param>
    /// <param name="userId">用户ID</param>
    /// <returns>是否存在</returns>
    public async Task<bool> ExistsAsync(string equipId, int userId)
    {
        try
        {
            return await  _dbService.Queryable<user_equipment>()
                .AnyAsync(ue => ue.equip_id == equipId && ue.user_id == userId);
        }
        catch (Exception ex)
        {
            throw new ApplicationException($"检查装备是否存在失败: {ex.Message}", ex);
        }
    }

    /// <summary>
    /// 获取用户已装备的装备列表
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <returns>已装备的装备列表</returns>
    public async Task<List<UserEquipmentDto>> GetUserEquippedItemsAsync(int userId)
    {
        try
        {
            return await _dbService.GetClient().Queryable<user_equipment>()
                .LeftJoin<equipment>((ue, e) => ue.equip_id == e.equip_id)
                .LeftJoin<equipment_type>((ue, e, et) => e.equip_type_id == et.equip_type_id)
                .Where((ue, e, et) => ue.user_id == userId && ue.is_equipped == true)
                .OrderBy((ue, e, et) => ue.position)
                .Select((ue, e, et) => new UserEquipmentDto
                {
                    Id = ue.id,
                    UserId = ue.user_id,
                    EquipId = ue.equip_id,
                    Name = ue.name,
                    Icon = ue.icon ?? "",
                    EquipTypeId = et.equip_type_id ?? "",
                    EquipTypeName = et.type_name ?? "",
                    StrengthenLevel = ue.strengthen_level ?? 0,
                    Slot = ue.slot ?? 0,
                    Position = ue.position,
                    IsEquipped = ue.is_equipped ?? false,
                    CreateTime = ue.create_time ?? DateTime.Now,
                    UserName = "" // 这个方法不需要用户名
                })
                .ToListAsync();
        }
        catch (Exception ex)
        {
            throw new ApplicationException($"获取用户已装备列表失败: {ex.Message}", ex);
        }
    }

    /// <summary>
    /// 获取装备详情
    /// </summary>
    /// <param name="equipId">装备ID</param>
    /// <returns>装备详情</returns>
    private async Task<UserEquipmentDetailDto?> GetEquipmentDetailAsync(string equipId)
    {
        try
        {
            return await  _dbService.Queryable<equipment_detail>()
                .Where(ed => ed.equip_id == equipId)
                .Select(ed => new UserEquipmentDetailDto
                {
                    Atk = ed.atk ?? 0,
                    Hit = ed.hit ?? 0,
                    Def = ed.def ?? 0,
                    Spd = ed.spd ?? 0,
                    Dodge = ed.dodge ?? 0,
                    Hp = ed.hp ?? 0,
                    Mp = ed.mp ?? 0,
                    Deepen = ed.deepen ?? 0,
                    Offset = ed.offset ?? 0,
                    Vamp = ed.vamp ?? 0,
                    VampMp = ed.vamp_mp ?? 0,
                    Description = ed.description ?? string.Empty,
                    MainAttr = ed.main_attr ?? string.Empty,
                    ElementLimit = ed.element_limit ?? string.Empty
                })
                .FirstAsync();
        }
        catch
        {
            return null;
        }
    }
} 