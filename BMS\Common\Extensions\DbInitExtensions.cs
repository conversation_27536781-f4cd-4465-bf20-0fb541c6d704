
using BMS.Common.Helpers;
using BMS.Models.Entities;
using SqlSugar;

namespace BMS.Common.Extensions
{
    /// <summary>
    /// 数据库初始化扩展
    /// </summary>
    public static class DbInitExtensions
    {
        /// <summary>
        /// 初始化数据库
        /// </summary>
        /// <param name="client">SqlSugar客户端</param>
        public static void InitializeDatabase(this ISqlSugarClient client)
        {
            try
            {
                // 检查admin_bm表是否存在
                var adminTableExists = client.DbMaintenance.IsAnyTable("admin_bm", false);
                
                //if (!adminTableExists)
                //{
                //    // 创建表
                //    client.CodeFirst.InitTables(typeof(admin_bm));
                    
                //    // 插入默认管理员账号
                //    var defaultAdmin = new admin_bm
                //    {
                //        Username = "admin",
                //        Password = PasswordHelper.Md5Hash("123456"), // 默认密码：123456
                //        RealName = "系统管理员",
                //        Status = 1,
                //        CreateTime = DateTime.Now,
                //        UpdateTime = DateTime.Now
                //    };
                    
                //    client.Insertable(defaultAdmin).ExecuteCommand();
                    
                //    Console.WriteLine("数据库初始化完成！");
                //    Console.WriteLine("默认管理员账号：admin，密码：123456");
                //}
                //else
                //{
                //    // 检查是否有管理员账号
                //    var adminCount = client.Queryable<AdminBm>().Count();
                //    if (adminCount == 0)
                //    {
                //        // 插入默认管理员账号
                //        var defaultAdmin = new AdminBm
                //        {
                //            Username = "admin",
                //            Password = PasswordHelper.Md5Hash("123456"), // 默认密码：123456
                //            RealName = "系统管理员",
                //            Status = 1,
                //            CreateTime = DateTime.Now,
                //            UpdateTime = DateTime.Now
                //        };
                        
                //        client.Insertable(defaultAdmin).ExecuteCommand();
                        
                //        Console.WriteLine("已创建默认管理员账号：admin，密码：123456");
                //    }
                //}
            }
            catch (Exception ex)
            {
                Console.WriteLine($"数据库初始化失败：{ex.Message}");
            }
        }
    }
} 