using BMS.Models.DTOs;
using BMS.Models.Common;

namespace BMS.Services.Interfaces
{
    /// <summary>
    /// 掉落配置服务接口
    /// </summary>
    public interface IDropConfigService
    {
        /// <summary>
        /// 分页查询掉落配置列表
        /// </summary>
        /// <param name="queryDto">查询条件</param>
        /// <returns>分页结果</returns>
        Task<PagedResult<DropConfigDto>> GetPagedListAsync(DropConfigQueryDto queryDto);

        /// <summary>
        /// 根据ID获取掉落配置详情
        /// </summary>
        /// <param name="id">掉落配置ID</param>
        /// <returns>掉落配置详情</returns>
        Task<DropConfigDto?> GetByIdAsync(int id);

        /// <summary>
        /// 创建掉落配置
        /// </summary>
        /// <param name="createDto">创建DTO</param>
        /// <returns>操作结果</returns>
        Task<ApiResult<DropConfigDto>> CreateAsync(DropConfigCreateDto createDto);

        /// <summary>
        /// 更新掉落配置
        /// </summary>
        /// <param name="updateDto">更新DTO</param>
        /// <returns>操作结果</returns>
        Task<ApiResult<DropConfigDto>> UpdateAsync(DropConfigUpdateDto updateDto);

        /// <summary>
        /// 删除掉落配置
        /// </summary>
        /// <param name="deleteDto">删除DTO</param>
        /// <returns>操作结果</returns>
        Task<ApiResult<bool>> DeleteAsync(DropConfigDeleteDto deleteDto);

        /// <summary>
        /// 批量删除掉落配置
        /// </summary>
        /// <param name="ids">掉落配置ID数组</param>
        /// <returns>操作结果</returns>
        Task<ApiResult<bool>> BatchDeleteAsync(int[] ids);

        /// <summary>
        /// 获取掉落类型选项
        /// </summary>
        /// <returns>掉落类型选项列表</returns>
        Task<List<DropTypeOptionDto>> GetDropTypeOptionsAsync();

        /// <summary>
        /// 获取地图选项
        /// </summary>
        /// <returns>地图选项列表</returns>
        Task<List<OptionDto>> GetMapOptionsAsync();

        /// <summary>
        /// 根据地图ID获取怪物选项
        /// </summary>
        /// <param name="mapId">地图ID</param>
        /// <returns>怪物选项列表</returns>
        Task<List<OptionDto>> GetMonsterOptionsByMapIdAsync(int mapId);

        /// <summary>
        /// 获取道具选项
        /// </summary>
        /// <returns>道具选项列表</returns>
        Task<List<OptionDto>> GetItemOptionsAsync();

        /// <summary>
        /// 检查掉落配置是否存在
        /// </summary>
        /// <param name="mapId">地图ID</param>
        /// <param name="dropType">掉落类型</param>
        /// <param name="monsterId">怪物ID</param>
        /// <param name="itemId">道具ID</param>
        /// <param name="excludeId">排除的ID（用于更新时检查）</param>
        /// <returns>是否存在</returns>
        Task<bool> ExistsAsync(int mapId, string dropType, int? monsterId, string itemId, int? excludeId = null);

        /// <summary>
        /// 根据地图ID获取掉落配置列表
        /// </summary>
        /// <param name="mapId">地图ID</param>
        /// <returns>掉落配置列表</returns>
        Task<List<DropConfigDto>> GetByMapIdAsync(int mapId);

        /// <summary>
        /// 根据怪物ID获取掉落配置列表
        /// </summary>
        /// <param name="monsterId">怪物ID</param>
        /// <returns>掉落配置列表</returns>
        Task<List<DropConfigDto>> GetByMonsterIdAsync(int monsterId);

        /// <summary>
        /// 创建多道具掉落配置
        /// </summary>
        /// <param name="createDto">创建DTO</param>
        /// <returns>操作结果</returns>
        Task<ApiResult<bool>> CreateMultiAsync(MultiDropConfigCreateDto createDto);

        /// <summary>
        /// 更新多道具掉落配置
        /// </summary>
        /// <param name="updateDto">更新DTO</param>
        /// <returns>操作结果</returns>
        Task<ApiResult<bool>> UpdateMultiAsync(MultiDropConfigUpdateDto updateDto);
    }
} 