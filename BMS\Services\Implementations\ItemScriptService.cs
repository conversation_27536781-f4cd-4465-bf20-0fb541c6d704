using BMS.Models.DTOs;
using BMS.Models.Common;
using BMS.Models.Entities;
using BMS.Services.Interfaces;
using SqlSugar;

namespace BMS.Services.Implementations
{
    /// <summary>
    /// 道具脚本服务实现
    /// </summary>
    public class ItemScriptService : IItemScriptService
    {
        private readonly IDbService _dbService;

        public ItemScriptService(IDbService dbService)
        {
            _dbService = dbService;
        }

        /// <summary>
        /// 根据道具编号获取脚本
        /// </summary>
        /// <param name="itemNo">道具编号</param>
        /// <returns></returns>
        public async Task<ItemScriptDto?> GetByItemNoAsync(int itemNo)
        {
            var entity = await _dbService.Queryable<item_script>()
                .Where(x => x.item_no == itemNo)
                .FirstAsync();

            return entity == null ? null : MapToDto(entity);
        }

        /// <summary>
        /// 创建或更新道具脚本
        /// </summary>
        /// <param name="upsertDto">创建/更新DTO</param>
        /// <returns></returns>
        public async Task<ApiResult<int>> UpsertAsync(ItemScriptUpsertDto upsertDto)
        {
            try
            {
                // 检查道具是否存在
                var itemExists = await _dbService.Queryable<item_config>()
                    .Where(x => x.item_no == upsertDto.ItemNo)
                    .AnyAsync();

                if (!itemExists)
                {
                    return ApiResult<int>.Fail($"道具编号 {upsertDto.ItemNo} 不存在");
                }

                // 检查是否已存在脚本
                var existingScript = await _dbService.Queryable<item_script>()
                    .Where(x => x.item_no == upsertDto.ItemNo)
                    .FirstAsync();

                if (existingScript != null)
                {
                    // 更新现有脚本
                    existingScript.script = upsertDto.Script;
                    existingScript.description = upsertDto.Description;

                    var updateResult = await _dbService.Updateable(existingScript).ExecuteCommandAsync();
                    return updateResult > 0 ? 
                        ApiResult<int>.Ok(existingScript.id, "更新成功") : 
                        ApiResult<int>.Fail("更新失败");
                }
                else
                {
                    // 创建新脚本
                    var entity = new item_script
                    {
                        item_no = upsertDto.ItemNo,
                        script = upsertDto.Script,
                        description = upsertDto.Description,
                        create_time = DateTime.Now
                    };

                    var result = await _dbService.Insertable(entity).ExecuteReturnIdentityAsync();
                    return ApiResult<int>.Ok(result, "创建成功");
                }
            }
            catch (Exception ex)
            {
                return ApiResult<int>.Fail($"操作失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 删除道具脚本
        /// </summary>
        /// <param name="id">脚本ID</param>
        /// <returns></returns>
        public async Task<ApiResult<bool>> DeleteAsync(int id)
        {
            try
            {
                var result = await _dbService.Deleteable<item_script>()
                    .Where(x => x.id == id)
                    .ExecuteCommandAsync();

                return result > 0 ? 
                    ApiResult<bool>.Ok(true, "删除成功") : 
                    ApiResult<bool>.Fail("删除失败，脚本不存在");
            }
            catch (Exception ex)
            {
                return ApiResult<bool>.Fail($"删除失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 根据道具编号删除脚本
        /// </summary>
        /// <param name="itemNo">道具编号</param>
        /// <returns></returns>
        public async Task<ApiResult<bool>> DeleteByItemNoAsync(int itemNo)
        {
            try
            {
                var result = await _dbService.Deleteable<item_script>()
                    .Where(x => x.item_no == itemNo)
                    .ExecuteCommandAsync();

                return result > 0 ? 
                    ApiResult<bool>.Ok(true, "删除成功") : 
                    ApiResult<bool>.Ok(true, "未找到相关脚本");
            }
            catch (Exception ex)
            {
                return ApiResult<bool>.Fail($"删除失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 实体转DTO
        /// </summary>
        /// <param name="entity">实体</param>
        /// <returns></returns>
        private static ItemScriptDto MapToDto(item_script entity)
        {
            return new ItemScriptDto
            {
                Id = entity.id,
                ItemNo = entity.item_no,
                Script = entity.script,
                Description = entity.description,
                CreateTime = entity.create_time
            };
        }
    }
} 