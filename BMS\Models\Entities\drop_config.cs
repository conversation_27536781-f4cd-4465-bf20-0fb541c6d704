﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace BMS.Models.Entities
{
    ///<summary>
    ///地图/怪物掉落配置表
    ///</summary>
    [SugarTable("drop_config")]
    public partial class drop_config
    {
           public drop_config(){


           }
           /// <summary>
           /// Desc:自增主键
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true,IsIdentity=true)]
           public int id {get;set;}

           /// <summary>
           /// Desc:地图ID
           /// Default:
           /// Nullable:False
           /// </summary>           
           public int map_id {get;set;}

           /// <summary>
           /// Desc:掉落类型
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string drop_type {get;set;}

           /// <summary>
           /// Desc:怪物ID（怪物掉落时填写，地图掉落时为空）
           /// Default:
           /// Nullable:True
           /// </summary>           
           public int? monster_id {get;set;}

           /// <summary>
           /// Desc:道具ID
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? item_id {get;set;}

           /// <summary>
           /// Desc:掉落概率（0~1之间，1为必掉）
           /// Default:1.0000
           /// Nullable:True
           /// </summary>           
           public decimal? drop_rate {get;set;}

           /// <summary>
           /// Desc:最小掉落数量
           /// Default:1
           /// Nullable:True
           /// </summary>           
           public int? min_count {get;set;}

           /// <summary>
           /// Desc:最大掉落数量
           /// Default:1
           /// Nullable:True
           /// </summary>           
           public int? max_count {get;set;}

           /// <summary>
           /// Desc:多道具掉落配置JSON数据
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? drop_items_json {get;set;}

           /// <summary>
           /// Desc:备注
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? remark {get;set;}

           /// <summary>
           /// Desc:创建时间
           /// Default:CURRENT_TIMESTAMP
           /// Nullable:True
           /// </summary>           
           public DateTime? create_time {get;set;}

           /// <summary>
           /// Desc:修改时间
           /// Default:CURRENT_TIMESTAMP
           /// Nullable:True
           /// </summary>           
           public DateTime? update_time {get;set;}

    }
}
