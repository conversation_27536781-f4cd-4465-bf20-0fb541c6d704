@{
    ViewData["Title"] = "怪物配置管理";
}

<!-- 科幻背景 -->
<div class="cyber-bg"></div>

<!-- 科幻怪物配置管理应用容器 -->
<div id="monsterConfigApp">
    <!-- 科幻页面标题 -->
    <div class="container-fluid py-4">
        <div class="row mb-4">
            <div class="col-12">
                <div class="cyber-card">
                    <div class="cyber-card-header">
                        <div class="cyber-icon">👹</div>
                        <h1 class="cyber-card-title">怪物配置管理</h1>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 科幻主要内容 -->
    <div class="container-fluid">

        <!-- 科幻搜索条件 -->
        <div class="cyber-card">
            <div class="cyber-card-header">
                <div class="cyber-icon">🔍</div>
                <h3 class="cyber-card-title">搜索条件</h3>
            </div>
            <div class="cyber-card-body">
                <div class="row">
                    <div class="col-md-3">
                        <div class="cyber-form-group">
                            <label class="cyber-form-label" for="search-monster-no">怪物编号</label>
                            <input type="number" class="cyber-form-control" id="search-monster-no" v-model="queryForm.monsterNo" placeholder="请输入怪物编号">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="cyber-form-group">
                            <label class="cyber-form-label" for="search-monster-name">怪物名称</label>
                            <input type="text" class="cyber-form-control" id="search-monster-name" v-model="queryForm.name" placeholder="请输入怪物名称">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="cyber-form-group">
                            <label class="cyber-form-label" for="search-monster-attribute">怪物属性</label>
                            <div class="cyber-select-wrapper">
                                <select class="cyber-form-control" id="search-monster-attribute" v-model="queryForm.attribute">
                                    <option value="">请选择属性</option>
                                    <option value="金">金</option>
                                    <option value="木">木</option>
                                    <option value="水">水</option>
                                    <option value="火">火</option>
                                    <option value="土">土</option>
                                    <option value="神">神</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="cyber-form-group">
                            <label class="cyber-form-label">&nbsp;</label>
                            <div class="d-flex gap-2 flex-wrap">
                                <button type="button" class="cyber-btn cyber-btn-primary" v-on:click="loadData">
                                    <i class="fas fa-search"></i> 搜索
                                </button>
                                <button type="button" class="cyber-btn cyber-btn-outline" v-on:click="resetQuery">
                                    <i class="fas fa-redo"></i> 重置
                                </button>
                                <button type="button" class="cyber-btn cyber-btn-success" v-on:click="showCreateModal">
                                    <i class="fas fa-plus"></i> 新增怪物
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 科幻怪物配置列表 -->
        <div class="cyber-card">
            <div class="cyber-card-header">
                <div class="cyber-icon">📋</div>
                <h3 class="cyber-card-title">怪物配置列表</h3>
            </div>
            <div class="cyber-card-body">
                <div class="cyber-table-container">
                    <table class="cyber-table">
                        <thead class="cyber-thead">
                            <tr>
                                <th class="cyber-th">ID</th>
                                <th class="cyber-th">怪物编号</th>
                                <th class="cyber-th">怪物名称</th>
                                <th class="cyber-th">怪物属性</th>
                                <th class="cyber-th">技能编号</th>
                                <th class="cyber-th">操作</th>
                            </tr>
                        </thead>
                        <tbody class="cyber-tbody" v-if="loading">
                            <tr class="cyber-tr">
                                <td colspan="6" class="cyber-td text-center py-4">
                                    <i class="fas fa-spinner fa-spin me-2"></i>加载中...
                                </td>
                            </tr>
                        </tbody>
                        <tbody class="cyber-tbody" v-else-if="!monsterConfigs || monsterConfigs.length === 0">
                            <tr class="cyber-tr">
                                <td colspan="6" class="cyber-td text-center py-4 text-muted">
                                    <i class="fas fa-inbox me-2"></i>暂无数据
                                </td>
                            </tr>
                        </tbody>
                        <tbody class="cyber-tbody" v-else>
                            <tr class="cyber-tr" v-for="monsterConfig in monsterConfigs" v-bind:key="monsterConfig.id">
                                <td class="cyber-td">{{ monsterConfig.id }}</td>
                                <td class="cyber-td">{{ monsterConfig.monsterNo }}</td>
                                <td class="cyber-td">
                                    <i class="fas fa-dragon me-1" v-bind:style="{ color: getAttributeColor(monsterConfig.attribute) }"></i>
                                    {{ monsterConfig.name }}
                                </td>
                                <td class="cyber-td">
                                    <span class="badge" v-bind:style="{ background: getAttributeColor(monsterConfig.attribute) }">
                                        {{ monsterConfig.attribute }}
                                    </span>
                                </td>
                                <td class="cyber-td">{{ monsterConfig.skill || '无' }}</td>
                                <td class="cyber-td">
                                    <div class="d-flex gap-1 flex-wrap">
                                        <button type="button" class="cyber-btn cyber-btn-sm cyber-btn-warning" v-on:click="showEditModal(monsterConfig)" title="编辑">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button type="button" class="cyber-btn cyber-btn-sm cyber-btn-danger" v-on:click="deleteMonsterConfig(monsterConfig.id)" title="删除">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- 科幻分页 -->
            <div class="mt-3" v-if="totalCount > 0">
                <div class="row align-items-center">
                    <div class="col-sm-6">
                        <div class="text-muted">
                            显示第 {{ (currentPage - 1) * pageSize + 1 }} 到 {{ Math.min(currentPage * pageSize, totalCount) }} 条记录，共 {{ totalCount }} 条记录
                        </div>
                    </div>
                    <div class="col-sm-6">
                        <ul class="cyber-pagination justify-content-end">
                            <li class="page-item" v-bind:class="{ disabled: currentPage <= 1 }">
                                <a href="#" class="page-link" v-on:click.prevent="changePage(currentPage - 1)">上一页</a>
                            </li>
                            <li v-for="page in visiblePages" v-bind:key="page" class="page-item" v-bind:class="{ active: page === currentPage }">
                                <a href="#" class="page-link" v-on:click.prevent="changePage(page)">{{ page }}</a>
                            </li>
                            <li class="page-item" v-bind:class="{ disabled: currentPage >= totalPages }">
                                <a href="#" class="page-link" v-on:click.prevent="changePage(currentPage + 1)">下一页</a>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

    </div>

    <!-- 科幻新增怪物配置模态框 -->
    <div class="modal fade cyber-modal" id="createModal" tabindex="-1" role="dialog" aria-labelledby="createModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="createModalLabel">
                        <i class="fas fa-plus me-2"></i>新增怪物配置
                    </h5>
                    <button type="button" class="btn-close" v-on:click="closeCreateModal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form>
                        <div class="cyber-form-group">
                            <label class="cyber-form-label" for="createMonsterNo">怪物编号 <span class="text-danger">*</span></label>
                            <input type="number" class="cyber-form-control" id="createMonsterNo" v-model="createForm.monsterNo" placeholder="请输入怪物编号" required>
                        </div>
                        <div class="cyber-form-group">
                            <label class="cyber-form-label" for="createMonsterName">怪物名称 <span class="text-danger">*</span></label>
                            <input type="text" class="cyber-form-control" id="createMonsterName" v-model="createForm.name" placeholder="请输入怪物名称" required>
                        </div>
                        <div class="cyber-form-group">
                            <label class="cyber-form-label" for="createMonsterAttribute">怪物属性 <span class="text-danger">*</span></label>
                            <div class="cyber-select-wrapper">
                                <select class="cyber-form-control" id="createMonsterAttribute" v-model="createForm.attribute" required>
                                    <option value="">请选择属性</option>
                                    <option value="金">金</option>
                                    <option value="木">木</option>
                                    <option value="水">水</option>
                                    <option value="火">火</option>
                                    <option value="土">土</option>
                                    <option value="神">神</option>
                                </select>
                            </div>
                        </div>
                        <div class="cyber-form-group">
                            <label class="cyber-form-label" for="createMonsterSkill">技能编号</label>
                            <input type="text" class="cyber-form-control" id="createMonsterSkill" v-model="createForm.skill" placeholder="请输入技能编号">
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="cyber-btn cyber-btn-outline" v-on:click="closeCreateModal">
                        <i class="fas fa-times me-1"></i>取消
                    </button>
                    <button type="button" class="cyber-btn cyber-btn-primary" v-on:click="createMonsterConfig" v-bind:disabled="submitting">
                        <i v-if="submitting" class="fas fa-spinner fa-spin me-1"></i>
                        <i v-else class="fas fa-save me-1"></i>
                        {{ submitting ? '提交中...' : '确定' }}
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 科幻编辑怪物配置模态框 -->
    <div class="modal fade cyber-modal" id="editModal" tabindex="-1" role="dialog" aria-labelledby="editModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="editModalLabel">
                        <i class="fas fa-edit me-2"></i>编辑怪物配置
                    </h5>
                    <button type="button" class="btn-close" v-on:click="closeEditModal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form>
                        <div class="cyber-form-group">
                            <label class="cyber-form-label" for="editMonsterNo">怪物编号 <span class="text-danger">*</span></label>
                            <input type="number" class="cyber-form-control" id="editMonsterNo" v-model="editForm.monsterNo" placeholder="请输入怪物编号" required>
                        </div>
                        <div class="cyber-form-group">
                            <label class="cyber-form-label" for="editMonsterName">怪物名称 <span class="text-danger">*</span></label>
                            <input type="text" class="cyber-form-control" id="editMonsterName" v-model="editForm.name" placeholder="请输入怪物名称" required>
                        </div>
                        <div class="cyber-form-group">
                            <label class="cyber-form-label" for="editMonsterAttribute">怪物属性 <span class="text-danger">*</span></label>
                            <div class="cyber-select-wrapper">
                                <select class="cyber-form-control" id="editMonsterAttribute" v-model="editForm.attribute" required>
                                    <option value="">请选择属性</option>
                                    <option value="金">金</option>
                                    <option value="木">木</option>
                                    <option value="水">水</option>
                                    <option value="火">火</option>
                                    <option value="土">土</option>
                                    <option value="神">神</option>
                                </select>
                            </div>
                        </div>
                        <div class="cyber-form-group">
                            <label class="cyber-form-label" for="editMonsterSkill">技能编号</label>
                            <input type="text" class="cyber-form-control" id="editMonsterSkill" v-model="editForm.skill" placeholder="请输入技能编号">
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="cyber-btn cyber-btn-outline" v-on:click="closeEditModal">
                        <i class="fas fa-times me-1"></i>取消
                    </button>
                    <button type="button" class="cyber-btn cyber-btn-primary" v-on:click="updateMonsterConfig" v-bind:disabled="submitting">
                        <i v-if="submitting" class="fas fa-spinner fa-spin me-1"></i>
                        <i v-else class="fas fa-save me-1"></i>
                        {{ submitting ? '提交中...' : '确定' }}
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        // 初始化Vue应用
        Vue.createApp({
        data() {
            return {
                loading: false,
                submitting: false,
                monsterConfigs: [],
                currentPage: 1,
                pageSize: 10,
                totalCount: 0,
                totalPages: 0,
                queryForm: {
                    monsterNo: null,
                    name: '',
                    attribute: ''
                },
                createForm: {
                    monsterNo: null,
                    name: '',
                    attribute: '',
                    skill: ''
                },
                editForm: {
                    id: 0,
                    monsterNo: null,
                    name: '',
                    attribute: '',
                    skill: ''
                }
            };
        },
        computed: {
            visiblePages() {
                if (!this.totalPages || this.totalPages <= 0) {
                    return [];
                }
                const start = Math.max(1, this.currentPage - 2);
                const end = Math.min(this.totalPages, this.currentPage + 2);
                const pages = [];
                for (let i = start; i <= end; i++) {
                    pages.push(i);
                }
                return pages;
            }
        },
        mounted() {
            this.loadData();
        },
        methods: {
            // 加载数据
            async loadData() {
                this.loading = true;
                try {
                    const response = await axios.post('/MonsterConfig/GetList', {
                        monsterNo: this.queryForm.monsterNo,
                        name: this.queryForm.name,
                        attribute: this.queryForm.attribute,
                        page: this.currentPage,
                        pageSize: this.pageSize
                    });
                    
                    if (response.data.success) {
                        this.monsterConfigs = response.data.data.data || [];
                        this.totalCount = response.data.data.totalCount || 0;
                        this.totalPages = response.data.data.totalPages || 0;
                    } else {
                        alert(response.data.message);
                        this.monsterConfigs = [];
                        this.totalCount = 0;
                        this.totalPages = 0;
                    }
                } catch (error) {
                    console.error('加载数据失败：', error);
                    alert('加载数据失败，请重试');
                    // 确保在错误情况下重置数据
                    this.monsterConfigs = [];
                    this.totalCount = 0;
                    this.totalPages = 0;
                } finally {
                    this.loading = false;
                }
            },
            
            // 重置查询条件
            resetQuery() {
                this.queryForm = {
                    monsterNo: null,
                    name: '',
                    attribute: ''
                };
                this.currentPage = 1;
                this.loadData();
            },
            
            // 获取属性颜色
            getAttributeColor(attribute) {
                const colors = {
                    '金': '#FFD700',
                    '木': '#32CD32',
                    '水': '#1E90FF',
                    '火': '#FF4500',
                    '土': '#8B4513',
                    '神': '#9370DB'
                };
                return colors[attribute] || 'var(--cyber-blue)';
            },

            // 显示新增模态框
            showCreateModal() {
                this.createForm = {
                    monsterNo: null,
                    name: '',
                    attribute: '',
                    skill: ''
                };
                $('#createModal').modal('show');
            },

            // 关闭新增模态框
            closeCreateModal() {
                $('#createModal').modal('hide');
            },

            // 显示编辑模态框
            showEditModal(item) {
                this.editForm = {
                    id: item.id,
                    monsterNo: item.monsterNo,
                    name: item.name,
                    attribute: item.attribute,
                    skill: item.skill || ''
                };
                $('#editModal').modal('show');
            },

            // 关闭编辑模态框
            closeEditModal() {
                $('#editModal').modal('hide');
            },
            
            // 创建怪物配置
            async createMonsterConfig() {
                if (!this.validateCreateForm()) {
                    return;
                }
                
                this.submitting = true;
                try {
                    const response = await axios.post('/MonsterConfig/Create', this.createForm);
                    
                    if (response.data.success) {
                        alert(response.data.message || '怪物配置创建成功');
                        $('#createModal').modal('hide');
                        this.loadData();
                    } else {
                        alert(response.data.message || '创建失败');
                    }
                } catch (error) {
                    console.error('创建怪物配置失败：', error);
                    alert('创建怪物配置失败，请重试');
                } finally {
                    this.submitting = false;
                }
            },
            
            // 更新怪物配置
            async updateMonsterConfig() {
                if (!this.validateEditForm()) {
                    return;
                }
                
                this.submitting = true;
                try {
                    const response = await axios.post('/MonsterConfig/Update', this.editForm);
                    
                    if (response.data.success) {
                        alert(response.data.message || '怪物配置更新成功');
                        $('#editModal').modal('hide');
                        this.loadData();
                    } else {
                        alert(response.data.message || '更新失败');
                    }
                } catch (error) {
                    console.error('更新怪物配置失败：', error);
                    alert('更新怪物配置失败，请重试');
                } finally {
                    this.submitting = false;
                }
            },
            
            // 删除怪物配置
            async deleteMonsterConfig(id) {
                if (!confirm('确定要删除这个怪物配置吗？此操作无法撤销！')) {
                    return;
                }

                try {
                    const response = await axios.post('/MonsterConfig/Delete', { id: id });

                    if (response.data.success) {
                        alert(response.data.message || '怪物配置删除成功');
                        this.loadData();
                    } else {
                        alert(response.data.message || '删除失败');
                    }
                } catch (error) {
                    console.error('删除怪物配置失败：', error);
                    alert('删除失败，请重试');
                }
            },
            
            // 验证创建表单
            validateCreateForm() {
                if (!this.createForm.monsterNo) {
                    alert('请输入怪物编号');
                    return false;
                }
                if (!this.createForm.name) {
                    alert('请输入怪物名称');
                    return false;
                }
                if (!this.createForm.attribute) {
                    alert('请选择怪物属性');
                    return false;
                }
                return true;
            },
            
            // 验证编辑表单
            validateEditForm() {
                if (!this.editForm.monsterNo) {
                    alert('请输入怪物编号');
                    return false;
                }
                if (!this.editForm.name) {
                    alert('请输入怪物名称');
                    return false;
                }
                if (!this.editForm.attribute) {
                    alert('请选择怪物属性');
                    return false;
                }
                return true;
            },
            
            // 切换页码
            changePage(page) {
                if (!this.totalPages || page < 1 || page > this.totalPages || page === this.currentPage) {
                    return;
                }
                this.currentPage = page;
                this.loadData();
            }
        }
        }).mount('#monsterConfigApp');
    </script>
}