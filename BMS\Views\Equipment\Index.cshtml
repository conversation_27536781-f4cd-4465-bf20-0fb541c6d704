@{
    ViewData["Title"] = "装备管理";
    Layout = "_Layout";
}

<!-- 科幻背景 -->
<div class="cyber-bg"></div>

<!-- 科幻装备管理应用容器 -->
<div id="equipmentApp">
    <!-- 科幻页面标题 -->
    <div class="container-fluid py-4">
        <div class="row mb-4">
            <div class="col-12">
                <div class="cyber-card">
                    <div class="cyber-card-header">
                        <div class="cyber-icon">⚔️</div>
                        <h1 class="cyber-card-title">装备管理系统</h1>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 科幻主要内容 -->
    <div class="container-fluid">
        <!-- 科幻选项卡导航 -->
        <div class="cyber-tabs">
            <button class="cyber-tab-btn active" id="equipment-tab" data-target="equipment-content" type="button">
                <i class="fas fa-shield-alt me-2"></i>装备管理
            </button>
            <button class="cyber-tab-btn" id="equipment-type-tab" data-target="equipment-type-content" type="button">
                <i class="fas fa-tags me-2"></i>装备类型管理
            </button>
        </div>

        <!-- 科幻选项卡内容 -->
        <div class="cyber-tab-content">
            <!-- 装备管理选项卡 -->
            <div class="cyber-tab-pane active" id="equipment-content">
                        <!-- 科幻搜索条件 -->
                        <div class="cyber-card">
                            <div class="cyber-card-header">
                                <div class="cyber-icon">🔍</div>
                                <h3 class="cyber-card-title">搜索条件</h3>
                            </div>
                            <div class="cyber-card-body">
                                <div class="row">
                                    <div class="col-lg-2 col-md-3 col-sm-6">
                                        <div class="cyber-form-group">
                                            <label class="cyber-form-label">装备ID</label>
                                            <input type="text" class="cyber-form-control" placeholder="输入装备ID" v-model="queryForm.EquipId">
                                        </div>
                                    </div>
                                    <div class="col-lg-3 col-md-3 col-sm-6">
                                        <div class="cyber-form-group">
                                            <label class="cyber-form-label">装备名称</label>
                                            <input type="text" class="cyber-form-control" placeholder="输入装备名称" v-model="queryForm.Name">
                                        </div>
                                    </div>
                                    <div class="col-lg-3 col-md-3 col-sm-6">
                                        <div class="cyber-form-group">
                                            <label class="cyber-form-label">装备类型</label>
                                            <div class="cyber-select-wrapper">
                                                <select class="cyber-form-control" v-model="queryForm.EquipTypeId">
                                                    <option value="">全部类型</option>
                                                    <option v-for="type in equipmentTypes" v-bind:key="type.equipTypeId" v-bind:value="type.equipTypeId">
                                                        {{ type.typeName }}
                                                    </option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-lg-2 col-md-3 col-sm-6">
                                        <div class="cyber-form-group">
                                            <label class="cyber-form-label">五行属性</label>
                                            <div class="cyber-select-wrapper">
                                                <select class="cyber-form-control" v-model="queryForm.Element">
                                                    <option value="">全部五行</option>
                                                    <option v-for="element in elements" v-bind:key="element" v-bind:value="element">
                                                        {{ element }}
                                                    </option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-lg-2 col-md-3 col-sm-6">
                                        <div class="cyber-form-group">
                                            <label class="cyber-form-label">套装ID</label>
                                            <input type="text" class="cyber-form-control" placeholder="输入套装ID" v-model="queryForm.SuitId">
                                        </div>
                                    </div>
                                </div>

                                <!-- 操作按钮区域 -->
                                <div class="row mt-3">
                                    <div class="col-12">
                                        <div class="d-flex justify-content-between align-items-center flex-wrap">
                                            <div class="d-flex gap-2 mb-2 mb-md-0">
                                                <button type="button" class="cyber-btn cyber-btn-primary" v-on:click="searchEquipments">
                                                    <i class="fas fa-search"></i> 搜索
                                                </button>
                                                <button type="button" class="cyber-btn cyber-btn-outline" v-on:click="resetSearch">
                                                    <i class="fas fa-redo"></i> 重置
                                                </button>
                                            </div>
                                            <div class="d-flex align-items-center gap-3">
                                                <div class="text-muted">
                                                    <span v-if="totalCount > 0">找到 {{ totalCount }} 条装备记录</span>
                                                    <span v-else>暂无装备数据</span>
                                                </div>
                                                <button type="button" class="cyber-btn cyber-btn-success" v-on:click="showAddModal">
                                                    <i class="fas fa-plus"></i> 新增装备
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 科幻装备列表 -->
                        <div class="cyber-card">
                            <div class="cyber-card-header">
                                <div class="cyber-icon">⚔️</div>
                                <h3 class="cyber-card-title">装备列表</h3>
                            </div>
                            <div class="cyber-card-body">
                                <div v-if="loading" class="text-center py-4">
                                    <i class="fas fa-spinner fa-spin me-2" style="font-size: 1.5rem; color: var(--cyber-blue);"></i>
                                    <span>数据加载中...</span>
                                </div>
                                <div v-else>
                                    <div class="cyber-table-container">
                                        <table class="cyber-table">
                                            <thead class="cyber-thead">
                                                <tr>
                                                    <th class="cyber-th">装备ID</th>
                                                    <th class="cyber-th">装备名称</th>
                                                    <th class="cyber-th">装备类型</th>
                                                    <th class="cyber-th">五行属性</th>
                                                    <th class="cyber-th">攻击</th>
                                                    <th class="cyber-th">防御</th>
                                                    <th class="cyber-th">生命</th>
                                                    <th class="cyber-th">强化等级</th>
                                                    <th class="cyber-th">套装ID</th>
                                                    <th class="cyber-th">操作</th>
                                                </tr>
                                            </thead>
                                            <tbody class="cyber-tbody" v-if="equipments.length === 0">
                                                <tr class="cyber-tr">
                                                    <td colspan="10" class="cyber-td text-center py-4 text-muted">
                                                        <i class="fas fa-inbox me-2"></i>暂无装备数据
                                                    </td>
                                                </tr>
                                            </tbody>
                                            <tbody class="cyber-tbody" v-else>
                                                <tr class="cyber-tr" v-for="equipment in equipments" v-bind:key="equipment.equipId">
                                                    <td class="cyber-td">{{ equipment.equipId }}</td>
                                                    <td class="cyber-td">
                                                        <i class="fas fa-shield-alt me-1" style="color: var(--cyber-blue);"></i>
                                                        {{ equipment.name }}
                                                    </td>
                                                    <td class="cyber-td">{{ equipment.equipTypeName }}</td>
                                                    <td class="cyber-td">
                                                        <span v-if="equipment.element" class="badge badge-info">{{ equipment.element }}</span>
                                                        <span v-else class="text-muted">无</span>
                                                    </td>
                                                    <td class="cyber-td">{{ equipment.atk }}</td>
                                                    <td class="cyber-td">{{ equipment.def }}</td>
                                                    <td class="cyber-td">{{ equipment.hp }}</td>
                                                    <td class="cyber-td">{{ equipment.strengthenLevel }}</td>
                                                    <td class="cyber-td">{{ equipment.suitId || '无' }}</td>
                                                    <td class="cyber-td">
                                                        <div class="d-flex gap-1 flex-wrap">
                                                            <button type="button" class="cyber-btn cyber-btn-sm"
                                                                    v-on:click="showDetailModal(equipment)" title="查看详情"
                                                                    style="background: linear-gradient(135deg, var(--cyber-blue), var(--cyber-purple));">
                                                                <i class="fas fa-eye"></i>
                                                            </button>
                                                            <button type="button" class="cyber-btn cyber-btn-sm cyber-btn-warning"
                                                                    v-on:click="showEditModal(equipment)" title="编辑装备">
                                                                <i class="fas fa-edit"></i>
                                                            </button>
                                                            <button type="button" class="cyber-btn cyber-btn-sm cyber-btn-danger"
                                                                    v-on:click="deleteEquipment(equipment)" title="删除装备">
                                                                <i class="fas fa-trash-alt"></i>
                                                            </button>
                                                        </div>
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>

                                    <!-- 科幻分页 -->
                                    <div class="mt-3" v-if="totalCount > 0">
                                        <div class="row align-items-center">
                                            <div class="col-sm-6">
                                                <div class="text-muted">
                                                    显示第 {{ (currentPage - 1) * pageSize + 1 }} 到 {{ Math.min(currentPage * pageSize, totalCount) }} 条记录，共 {{ totalCount }} 条记录
                                                </div>
                                            </div>
                                            <div class="col-sm-6">
                                                <ul class="cyber-pagination justify-content-end">
                                                    <li class="page-item" :class="{ disabled: currentPage <= 1 }">
                                                        <a class="page-link" href="#" v-on:click.prevent="changePage(currentPage - 1)">上一页</a>
                                                    </li>
                                                    <li class="page-item" v-for="page in visiblePages" :key="page" :class="{ active: page === currentPage }">
                                                        <a class="page-link" href="#" v-on:click.prevent="changePage(page)" v-text="page"></a>
                                                    </li>
                                                    <li class="page-item" :class="{ disabled: currentPage >= totalPages }">
                                                        <a class="page-link" href="#" v-on:click.prevent="changePage(currentPage + 1)">下一页</a>
                                                    </li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                </div>

            <!-- 装备类型管理选项卡 -->
            <div class="cyber-tab-pane" id="equipment-type-content">
                        <!-- 科幻装备类型搜索 -->
                        <div class="cyber-card">
                            <div class="cyber-card-header">
                                <div class="cyber-icon">🏷️</div>
                                <h3 class="cyber-card-title">装备类型搜索</h3>
                            </div>
                            <div class="cyber-card-body">
                                <div class="row">
                                    <div class="col-lg-3 col-md-4 col-sm-6">
                                        <div class="cyber-form-group">
                                            <label class="cyber-form-label">类型ID</label>
                                            <input type="text" class="cyber-form-control" placeholder="输入装备类型ID" v-model="typeQueryForm.EquipTypeId">
                                        </div>
                                    </div>
                                    <div class="col-lg-3 col-md-4 col-sm-6">
                                        <div class="cyber-form-group">
                                            <label class="cyber-form-label">类型名称</label>
                                            <input type="text" class="cyber-form-control" placeholder="输入装备类型名称" v-model="typeQueryForm.TypeName">
                                        </div>
                                    </div>
                                    <div class="col-lg-6 col-md-4 col-sm-12">
                                        <div class="cyber-form-group">
                                            <label class="cyber-form-label">&nbsp;</label>
                                            <div class="d-flex gap-2 flex-wrap">
                                                <button type="button" class="cyber-btn cyber-btn-primary" v-on:click="searchEquipmentTypes">
                                                    <i class="fas fa-search"></i> 搜索
                                                </button>
                                                <button type="button" class="cyber-btn cyber-btn-outline" v-on:click="resetTypeSearch">
                                                    <i class="fas fa-redo"></i> 重置
                                                </button>
                                                <button type="button" class="cyber-btn cyber-btn-success" v-on:click="showAddTypeModal">
                                                    <i class="fas fa-plus"></i> 新增类型
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 统计信息 -->
                                <div class="row mt-3">
                                    <div class="col-12">
                                        <div class="text-muted">
                                            <span v-if="typesTotalCount > 0">找到 {{ typesTotalCount }} 条装备类型记录</span>
                                            <span v-else>暂无装备类型数据</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 科幻装备类型列表 -->
                        <div class="cyber-card">
                            <div class="cyber-card-header">
                                <div class="cyber-icon">📋</div>
                                <h3 class="cyber-card-title">装备类型列表</h3>
                            </div>
                            <div class="cyber-card-body">
                                <div v-if="typeLoading" class="text-center py-4">
                                    <i class="fas fa-spinner fa-spin me-2" style="font-size: 1.5rem; color: var(--cyber-blue);"></i>
                                    <span>数据加载中...</span>
                                </div>
                                <div v-else>
                                    <div class="cyber-table-container">
                                        <table class="cyber-table">
                                            <thead class="cyber-thead">
                                                <tr>
                                                    <th class="cyber-th">装备类型ID</th>
                                                    <th class="cyber-th">装备类型名称</th>
                                                    <th class="cyber-th">关联装备数</th>
                                                    <th class="cyber-th">操作</th>
                                                </tr>
                                            </thead>
                                            <tbody class="cyber-tbody" v-if="equipmentTypesList.length === 0">
                                                <tr class="cyber-tr">
                                                    <td colspan="4" class="cyber-td text-center py-4 text-muted">
                                                        <i class="fas fa-inbox me-2"></i>暂无装备类型数据
                                                    </td>
                                                </tr>
                                            </tbody>
                                            <tbody class="cyber-tbody" v-else>
                                                <tr class="cyber-tr" v-for="equipType in equipmentTypesList" v-bind:key="equipType.equipTypeId">
                                                    <td class="cyber-td">{{ equipType.equipTypeId }}</td>
                                                    <td class="cyber-td">
                                                        <i class="fas fa-tags me-1" style="color: var(--cyber-blue);"></i>
                                                        {{ equipType.typeName }}
                                                    </td>
                                                    <td class="cyber-td text-center">
                                                        <span class="badge badge-info">{{ equipType.equipmentCount || 0 }}</span>
                                                    </td>
                                                    <td class="cyber-td">
                                                        <div class="d-flex gap-1 flex-wrap">
                                                            <button type="button" class="cyber-btn cyber-btn-sm cyber-btn-warning"
                                                                    v-on:click="showEditTypeModal(equipType)" title="编辑装备类型">
                                                                <i class="fas fa-edit"></i>
                                                            </button>
                                                            <button type="button" class="cyber-btn cyber-btn-sm cyber-btn-danger"
                                                                    v-on:click="deleteEquipmentType(equipType)" title="删除装备类型"
                                                                    v-bind:disabled="equipType.equipmentCount > 0">
                                                                <i class="fas fa-trash-alt"></i>
                                                            </button>
                                                        </div>
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>

                                    <!-- 科幻分页 -->
                                    <div class="mt-3" v-if="typesTotalCount > 0">
                                        <div class="row align-items-center">
                                            <div class="col-sm-6">
                                                <div class="text-muted">
                                                    显示第 {{ (typeCurrentPage - 1) * typePageSize + 1 }} 到 {{ Math.min(typeCurrentPage * typePageSize, typesTotalCount) }} 条记录，共 {{ typesTotalCount }} 条记录
                                                </div>
                                            </div>
                                            <div class="col-sm-6">
                                                <ul class="cyber-pagination justify-content-end">
                                                    <li class="page-item" :class="{ disabled: typeCurrentPage <= 1 }">
                                                        <a class="page-link" href="#" v-on:click.prevent="changeTypePage(typeCurrentPage - 1)">上一页</a>
                                                    </li>
                                                    <li class="page-item" v-for="page in typeVisiblePages" :key="page" :class="{ active: page === typeCurrentPage }">
                                                        <a class="page-link" href="#" v-on:click.prevent="changeTypePage(page)" v-text="page"></a>
                                                    </li>
                                                    <li class="page-item" :class="{ disabled: typeCurrentPage >= typeTotalPages }">
                                                        <a class="page-link" href="#" v-on:click.prevent="changeTypePage(typeCurrentPage + 1)">下一页</a>
                                                    </li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
            </div>
        </div>
    </div>

    <!-- 科幻新增/编辑装备模态框 -->
    <div class="modal fade cyber-modal" id="equipmentModal" tabindex="-1" role="dialog">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title">
                        <i class="fas fa-shield-alt me-2"></i>{{ isEdit ? '编辑装备' : '新增装备' }}
                    </h4>
                    <button type="button" class="btn-close" v-on:click="closeModal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form>
                        <div class="row">
                            <div class="col-md-12">
                                <div class="cyber-form-group">
                                    <label class="cyber-form-label">装备ID <span class="text-danger">*</span></label>
                                    <input type="text" class="cyber-form-control" v-model="equipmentForm.equipId" v-bind:disabled="isEdit" placeholder="请输入装备ID">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="cyber-form-group">
                                    <label class="cyber-form-label">装备名称 <span class="text-danger">*</span></label>
                                    <input type="text" class="cyber-form-control" v-model="equipmentForm.name" placeholder="请输入装备名称">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="cyber-form-group">
                                    <label class="cyber-form-label">装备类型 <span class="text-danger">*</span></label>
                                    <div class="cyber-select-wrapper">
                                        <select class="cyber-form-control" v-model="equipmentForm.equipTypeId">
                                            <option value="">请选择装备类型</option>
                                            <option v-for="type in equipmentTypes" v-bind:key="type.equipTypeId" v-bind:value="type.equipTypeId">
                                                {{ type.typeName }}
                                            </option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="cyber-form-group">
                                    <label class="cyber-form-label">五行属性</label>
                                    <div class="cyber-select-wrapper">
                                        <select class="cyber-form-control" v-model="equipmentForm.element">
                                            <option value="">请选择五行属性</option>
                                            <option v-for="element in elements" v-bind:key="element" v-bind:value="element">
                                                {{ element }}
                                            </option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="cyber-form-group">
                                    <label class="cyber-form-label">装备图标</label>
                                    <input type="text" class="cyber-form-control" v-model="equipmentForm.icon" placeholder="请输入装备图标路径">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="cyber-form-group">
                                    <label class="cyber-form-label">扩展槽位</label>
                                    <input type="number" class="cyber-form-control" v-model="equipmentForm.slot" min="0" max="99">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="cyber-form-group">
                                    <label class="cyber-form-label">强化等级</label>
                                    <input type="number" class="cyber-form-control" v-model="equipmentForm.strengthenLevel" min="0" max="99">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="cyber-form-group">
                                    <label class="cyber-form-label">套装ID</label>
                                    <input type="text" class="cyber-form-control" v-model="equipmentForm.suitId" placeholder="请输入套装ID">
                                </div>
                            </div>
                        </div>

                        <!-- 科幻属性设置 -->
                        <div class="mt-4">
                            <h5 class="cyber-section-title">
                                <i class="fas fa-chart-bar me-2"></i>基础属性
                            </h5>
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="cyber-form-group">
                                        <label class="cyber-form-label">攻击加成</label>
                                        <input type="number" class="cyber-form-control" v-model="equipmentForm.atk" step="0.01" min="0">
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="cyber-form-group">
                                        <label class="cyber-form-label">命中加成</label>
                                        <input type="number" class="cyber-form-control" v-model="equipmentForm.hit" step="0.01" min="0">
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="cyber-form-group">
                                        <label class="cyber-form-label">防御加成</label>
                                        <input type="number" class="cyber-form-control" v-model="equipmentForm.def" step="0.01" min="0">
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="cyber-form-group">
                                        <label class="cyber-form-label">速度加成</label>
                                        <input type="number" class="cyber-form-control" v-model="equipmentForm.spd" step="0.01" min="0">
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="cyber-form-group">
                                        <label class="cyber-form-label">闪避加成</label>
                                        <input type="number" class="cyber-form-control" v-model="equipmentForm.dodge" step="0.01" min="0">
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="cyber-form-group">
                                        <label class="cyber-form-label">生命加成</label>
                                        <input type="number" class="cyber-form-control" v-model="equipmentForm.hp" step="0.01" min="0">
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="cyber-form-group">
                                        <label class="cyber-form-label">魔法加成</label>
                                        <input type="number" class="cyber-form-control" v-model="equipmentForm.mp" step="0.01" min="0">
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="cyber-form-group">
                                        <label class="cyber-form-label">加深加成</label>
                                        <input type="number" class="cyber-form-control" v-model="equipmentForm.deepen" step="0.01" min="0">
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="cyber-form-group">
                                        <label class="cyber-form-label">抵消加成</label>
                                        <input type="number" class="cyber-form-control" v-model="equipmentForm.offset" step="0.01" min="0">
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="cyber-form-group">
                                        <label class="cyber-form-label">吸血加成</label>
                                        <input type="number" class="cyber-form-control" v-model="equipmentForm.vamp" step="0.01" min="0">
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="cyber-form-group">
                                        <label class="cyber-form-label">吸魔加成</label>
                                        <input type="number" class="cyber-form-control" v-model="equipmentForm.vampMp" step="0.01" min="0">
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="cyber-form-group">
                                        <label class="cyber-form-label">主属性</label>
                                        <input type="text" class="cyber-form-control" v-model="equipmentForm.mainAttr" placeholder="主属性">
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="cyber-form-group">
                                        <label class="cyber-form-label">主属性值</label>
                                        <input type="text" class="cyber-form-control" v-model="equipmentForm.mainAttrValue" placeholder="主属性值">
                                    </div>
                                </div>
                            </div>
                        </div>
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="cyber-form-group">
                                        <label class="cyber-form-label">五行限制</label>
                                        <div class="cyber-select-wrapper">
                                            <select class="cyber-form-control" v-model="equipmentForm.elementLimit">
                                                <option value="">无限制</option>
                                                <option v-for="limit in elementLimits" v-bind:key="limit" v-bind:value="limit">
                                                    {{ limit }}
                                                </option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="cyber-form-group">
                                        <label class="cyber-form-label">副属性</label>
                                        <input type="text" class="cyber-form-control" v-model="equipmentForm.subAttr" placeholder="副属性">
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="cyber-form-group">
                                        <label class="cyber-form-label">副属性值</label>
                                        <input type="text" class="cyber-form-control" v-model="equipmentForm.subAttrValue" placeholder="副属性值">
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="cyber-form-group">
                                        <label class="cyber-form-label">装备说明</label>
                                        <textarea class="cyber-form-control" v-model="equipmentForm.description" rows="3" placeholder="装备说明"></textarea>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="cyber-form-group">
                                        <label class="cyber-form-label">装备名称（详情表）</label>
                                        <input type="text" class="cyber-form-control" v-model="equipmentForm.equipName" placeholder="装备名称（详情表）">
                                    </div>
                                </div>
                            </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="cyber-btn cyber-btn-outline" v-on:click="closeModal">
                        <i class="fas fa-times me-1"></i>取消
                    </button>
                    <button type="button" class="cyber-btn cyber-btn-success" v-on:click="saveEquipment" v-bind:disabled="saving">
                        <span v-if="saving" class="spinner-border spinner-border-sm me-1"></span>
                        <i v-if="!saving" class="fas fa-save me-1"></i>
                        {{ isEdit ? '更新装备' : '创建装备' }}
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 科幻新增/编辑装备类型模态框 -->
    <div class="modal fade cyber-modal" id="equipmentTypeModal" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title">
                        <i class="fas fa-tags me-2"></i>{{ isTypeEdit ? '编辑装备类型' : '新增装备类型' }}
                    </h4>
                    <button type="button" class="btn-close" v-on:click="closeTypeModal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form>
                        <div class="cyber-form-group">
                            <label class="cyber-form-label">装备类型ID <span class="text-danger">*</span></label>
                            <input type="text" class="cyber-form-control" v-model="equipmentTypeForm.equipTypeId" v-bind:disabled="isTypeEdit" placeholder="请输入装备类型ID（如：weapon, armor等）">
                            <small class="form-text text-muted">装备类型ID用于程序识别，创建后不可修改</small>
                        </div>
                        <div class="cyber-form-group">
                            <label class="cyber-form-label">装备类型名称 <span class="text-danger">*</span></label>
                            <input type="text" class="cyber-form-control" v-model="equipmentTypeForm.typeName" placeholder="请输入装备类型名称（如：武器、护甲等）">
                            <small class="form-text text-muted">装备类型名称用于前端显示</small>
                        </div>
                        <div class="cyber-form-group">
                            <label class="cyber-form-label">类型描述</label>
                            <textarea class="cyber-form-control" v-model="equipmentTypeForm.description" rows="3" placeholder="请输入类型描述"></textarea>
                            <small class="form-text text-muted">装备类型的详细描述</small>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="cyber-form-group">
                                    <label class="cyber-form-label">排序号</label>
                                    <input type="number" class="cyber-form-control" v-model="equipmentTypeForm.sortOrder" min="0" max="9999" placeholder="排序号">
                                    <small class="form-text text-muted">数字越小排序越靠前</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="cyber-form-group">
                                    <label class="cyber-form-label">是否启用</label>
                                    <div class="cyber-select-wrapper">
                                        <select class="cyber-form-control" v-model="equipmentTypeForm.isActive">
                                            <option value="true">启用</option>
                                            <option value="false">禁用</option>
                                        </select>
                                    </div>
                                    <small class="form-text text-muted">禁用后该类型将不可用</small>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="cyber-btn cyber-btn-outline" v-on:click="closeTypeModal">
                        <i class="fas fa-times me-1"></i>取消
                    </button>
                    <button type="button" class="cyber-btn cyber-btn-success" v-on:click="saveEquipmentType" v-bind:disabled="typeSaving">
                        <span v-if="typeSaving" class="spinner-border spinner-border-sm me-1"></span>
                        <i v-if="!typeSaving" class="fas fa-save me-1"></i>
                        {{ isTypeEdit ? '更新类型' : '创建类型' }}
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 科幻装备详情模态框 -->
    <div class="modal fade cyber-modal" id="detailModal" tabindex="-1" role="dialog">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title">
                        <i class="fas fa-info-circle me-2"></i>装备详情
                    </h4>
                    <button type="button" class="btn-close" v-on:click="closeDetailModal" aria-label="Close"></button>
                </div>
                <div class="modal-body" v-if="currentEquipment">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <h6 class="cyber-section-title">
                                <i class="fas fa-cog me-2"></i>基本信息
                            </h6>
                            <div class="cyber-detail-item">
                                <strong>装备ID：</strong>
                                <span>{{ currentEquipment.equipId }}</span>
                            </div>
                            <div class="cyber-detail-item">
                                <strong>装备类ID：</strong>
                                <span>{{ currentEquipment.classId }}</span>
                            </div>
                            <div class="cyber-detail-item">
                                <strong>装备名称：</strong>
                                <span>{{ currentEquipment.name }}</span>
                            </div>
                            <div class="cyber-detail-item">
                                <strong>装备类型：</strong>
                                <span>{{ currentEquipment.equipTypeName }}</span>
                            </div>
                            <div class="cyber-detail-item">
                                <strong>五行属性：</strong>
                                <span v-if="currentEquipment.element" class="badge badge-info">{{ currentEquipment.element }}</span>
                                <span v-else class="text-muted">无</span>
                            </div>
                            <div class="cyber-detail-item">
                                <strong>扩展槽位：</strong>
                                <span>{{ currentEquipment.slot }}</span>
                            </div>
                            <div class="cyber-detail-item">
                                <strong>强化等级：</strong>
                                <span>{{ currentEquipment.strengthenLevel }}</span>
                            </div>
                            <div class="cyber-detail-item">
                                <strong>套装ID：</strong>
                                <span>{{ currentEquipment.suitId || '无' }}</span>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <h6 class="cyber-section-title">
                                <i class="fas fa-chart-bar me-2"></i>属性加成
                            </h6>
                            <div class="row">
                                <div class="col-6">
                                    <div class="cyber-detail-item">
                                        <strong>攻击：</strong>
                                        <span>{{ currentEquipment.atk }}</span>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="cyber-detail-item">
                                        <strong>命中：</strong>
                                        <span>{{ currentEquipment.hit }}</span>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-6">
                                    <div class="cyber-detail-item">
                                        <strong>防御：</strong>
                                        <span>{{ currentEquipment.def }}</span>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="cyber-detail-item">
                                        <strong>速度：</strong>
                                        <span>{{ currentEquipment.spd }}</span>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-6">
                                    <div class="cyber-detail-item">
                                        <strong>闪避：</strong>
                                        <span>{{ currentEquipment.dodge }}</span>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="cyber-detail-item">
                                        <strong>生命：</strong>
                                        <span>{{ currentEquipment.hp }}</span>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-6">
                                    <div class="cyber-detail-item">
                                        <strong>魔法：</strong>
                                        <span>{{ currentEquipment.mp }}</span>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="cyber-detail-item">
                                        <strong>加深：</strong>
                                        <span>{{ currentEquipment.deepen }}</span>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-6">
                                    <div class="cyber-detail-item">
                                        <strong>抵消：</strong>
                                        <span>{{ currentEquipment.offset }}</span>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="cyber-detail-item">
                                        <strong>吸血：</strong>
                                        <span>{{ currentEquipment.vamp }}</span>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-6">
                                    <div class="cyber-detail-item">
                                        <strong>吸魔：</strong>
                                        <span>{{ currentEquipment.vampMp }}</span>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="cyber-detail-item">
                                        <strong>主属性：</strong>
                                        <span>{{ currentEquipment.mainAttr || '无' }}</span>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-6">
                                    <div class="cyber-detail-item">
                                        <strong>主属性值：</strong>
                                        <span>{{ currentEquipment.mainAttrValue || '无' }}</span>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="cyber-detail-item">
                                        <strong>副属性：</strong>
                                        <span>{{ currentEquipment.subAttr || '无' }}</span>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-6">
                                    <div class="cyber-detail-item">
                                        <strong>副属性值：</strong>
                                        <span>{{ currentEquipment.subAttrValue || '无' }}</span>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="cyber-detail-item">
                                        <strong>五行限制：</strong>
                                        <span>{{ currentEquipment.elementLimit || '无' }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row" v-if="currentEquipment.description">
                        <div class="col-md-12">
                            <h6 class="cyber-section-title">
                                <i class="fas fa-file-text me-2"></i>装备说明
                            </h6>
                            <div class="cyber-detail-description">
                                {{ currentEquipment.description }}
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="cyber-btn cyber-btn-outline" v-on:click="closeDetailModal">
                        <i class="fas fa-times me-1"></i>关闭
                    </button>
                </div>
            </div>
     </div>
    
</div>

</div>


<script src="~/lib/vue/vue.global.js"></script>
<script src="~/lib/axios.min.js"></script>
<script>
    const { createApp, ref, computed, onMounted } = Vue;

    const equipmentApp = createApp({
        setup() {
            // 响应式数据
            const loading = ref(false);
            const saving = ref(false);
            const equipments = ref([]);
            const equipmentTypes = ref(@Html.Raw(Json.Serialize(ViewBag.EquipmentTypes ?? new List<object>())));
            const elements = ref(@Html.Raw(Json.Serialize(ViewBag.Elements ?? new List<string>())));
            const elementLimits = ref(@Html.Raw(Json.Serialize(ViewBag.ElementLimits ?? new List<string>())));
            const totalCount = ref(0);
            const currentPage = ref(1);
            const pageSize = ref(10);
            
            const queryForm = ref({
                EquipId: '',
                Name: '',
                EquipTypeId: '',
                Element: '',
                SuitId: '',
                MainAttr: '',
                SubAttr: '',
                Page: 1,
                PageSize: 10
            });
            
            const equipmentForm = ref({
                equipId: '',
                classId: '',
                name: '',
                icon: '',
                equipTypeId: '',
                element: '',
                slot: 0,
                strengthenLevel: 0,
                suitId: '',
                atk: 0,
                hit: 0,
                def: 0,
                spd: 0,
                dodge: 0,
                hp: 0,
                mp: 0,
                deepen: 0,
                offset: 0,
                vamp: 0,
                vampMp: 0,
                description: '',
                mainAttr: '',
                mainAttrValue: '',
                subAttr: '',
                subAttrValue: '',
                elementLimit: '',
                equipName: ''
            });
            
            const currentEquipment = ref(null);
            const isEdit = ref(false);

            // ==================== 装备类型管理数据 ====================
            const typeLoading = ref(false);
            const typeSaving = ref(false);
            const equipmentTypesList = ref([]);
            const typesTotalCount = ref(0);
            const typeCurrentPage = ref(1);
            const typePageSize = ref(10);
            
            const typeQueryForm = ref({
                EquipTypeId: '',
                TypeName: '',
                Page: 1,
                PageSize: 10
            });
            
            const equipmentTypeForm = ref({
                equipTypeId: '',
                typeName: '',
                description: '',
                sortOrder: 0,
                isActive: true
            });
            
            const isTypeEdit = ref(false);

            // 计算属性
            const totalPages = computed(() => {
                return Math.ceil(totalCount.value / pageSize.value);
            });

            const visiblePages = computed(() => {
                const start = Math.max(1, currentPage.value - 2);
                const end = Math.min(totalPages.value, currentPage.value + 2);
                const pages = [];
                for (let i = start; i <= end; i++) {
                    pages.push(i);
                }
                return pages;
            });

            // 装备类型计算属性
            const typeTotalPages = computed(() => {
                return Math.ceil(typesTotalCount.value / typePageSize.value);
            });

            const typeVisiblePages = computed(() => {
                const start = Math.max(1, typeCurrentPage.value - 2);
                const end = Math.min(typeTotalPages.value, typeCurrentPage.value + 2);
                const pages = [];
                for (let i = start; i <= end; i++) {
                    pages.push(i);
                }
                return pages;
            });

            // 方法
            const loadEquipments = async () => {
                loading.value = true;
                try {
                    const requestData = {
                        ...queryForm.value,
                        Page: currentPage.value,
                        PageSize: pageSize.value
                    };
                    console.log('发送的请求参数：', requestData);
                    
                    const response = await axios.post('/Equipment/GetList', requestData);

                    if (response.data.code === 200) {
                        equipments.value = response.data.data || [];
                        totalCount.value = response.data.total || 0;
                    } else {
                        alert('加载装备列表失败');
                    }
                } catch (error) {
                    console.error('加载装备列表出错：', error);
                    alert('加载装备列表失败');
                } finally {
                    loading.value = false;
                }
            };

            const searchEquipments = () => {
                currentPage.value = 1;
                loadEquipments();
            };

            const resetSearch = () => {
                queryForm.value = {
                    EquipId: '',
                    Name: '',
                    EquipTypeId: '',
                    Element: '',
                    SuitId: '',
                    MainAttr: '',
                    SubAttr: '',
                    Page: 1,
                    PageSize: 10
                };
                currentPage.value = 1;
                loadEquipments();
            };

            const resetForm = () => {
                equipmentForm.value = {
                    equipId: '',
                    classId: '',
                    name: '',
                    icon: '',
                    equipTypeId: '',
                    element: '',
                    slot: 0,
                    strengthenLevel: 0,
                    suitId: '',
                    atk: 0,
                    hit: 0,
                    def: 0,
                    spd: 0,
                    dodge: 0,
                    hp: 0,
                    mp: 0,
                    deepen: 0,
                    offset: 0,
                    vamp: 0,
                    vampMp: 0,
                    description: '',
                    mainAttr: '',
                    mainAttrValue: '',
                    subAttr: '',
                    subAttrValue: '',
                    elementLimit: '',
                    equipName: ''
                };
            };

            const showAddModal = () => {
                isEdit.value = false;
                resetForm();
                $('#equipmentModal').modal('show');
            };

            const showEditModal = async (equipment) => {
                isEdit.value = true;
                
                // 获取完整的装备信息
                try {
                    const response = await axios.get(`/Equipment/GetById?equipId=${equipment.equipId}`);
                    if (response.data.code === 200) {
                        const data = response.data.data;
                        equipmentForm.value = {
                            equipId: data.equipId,
                            classId: data.classId,
                            name: data.name,
                            icon: data.icon || '',
                            equipTypeId: data.equipTypeId,
                            element: data.element || '',
                            slot: data.slot,
                            strengthenLevel: data.strengthenLevel,
                            suitId: data.suitId || '',
                            atk: data.atk,
                            hit: data.hit,
                            def: data.def,
                            spd: data.spd,
                            dodge: data.dodge,
                            hp: data.hp,
                            mp: data.mp,
                            deepen: data.deepen,
                            offset: data.offset,
                            vamp: data.vamp,
                            vampMp: data.vampMp,
                            description: data.description || '',
                            mainAttr: data.mainAttr || '',
                            mainAttrValue: data.mainAttrValue || '',
                            subAttr: data.subAttr || '',
                            subAttrValue: data.subAttrValue || '',
                            elementLimit: data.elementLimit || '',
                            equipName: data.equipName || ''
                        };
                        $('#equipmentModal').modal('show');
                    } else {
                        alert('获取装备信息失败');
                    }
                } catch (error) {
                    console.error('获取装备信息出错：', error);
                    alert('获取装备信息失败');
                }
            };

            const showDetailModal = (equipment) => {
                currentEquipment.value = equipment;
                $('#detailModal').modal('show');
            };

            const saveEquipment = async () => {
                // 验证表单
                if (!equipmentForm.value.equipId || !equipmentForm.value.name || !equipmentForm.value.equipTypeId) {
                    alert('请填写必填字段');
                    return;
                }

                // 自动设置classId为equipTypeId（后端需要但前端不显示）
                if (!equipmentForm.value.classId) {
                    equipmentForm.value.classId = equipmentForm.value.equipTypeId;
                }

                saving.value = true;
                try {
                    const url = isEdit.value ? '/Equipment/Update' : '/Equipment/Create';
                    const response = await axios.post(url, equipmentForm.value);

                    if (response.data.code === 200) {
                        alert(isEdit.value ? '装备更新成功' : '装备创建成功');
                        $('#equipmentModal').modal('hide');
                        loadEquipments();
                    } else {
                        alert(response.data.message || '操作失败');
                    }
                } catch (error) {
                    console.error('保存装备出错：', error);
                    alert('保存装备失败');
                } finally {
                    saving.value = false;
                }
            };

            const deleteEquipment = async (equipment) => {
                if (!confirm(`确定要删除装备"${equipment.name}"吗？`)) {
                    return;
                }

                try {
                    const response = await axios.post('/Equipment/Delete', {
                        equipId: equipment.equipId
                    });

                    if (response.data.code === 200) {
                        alert('删除成功');
                        loadEquipments();
                    } else {
                        alert(response.data.message || '删除失败');
                    }
                } catch (error) {
                    console.error('删除装备出错：', error);
                    alert('删除失败');
                }
            };

            const changePage = (page) => {
                if (page < 1 || page > totalPages.value || page === currentPage.value) {
                    return;
                }
                currentPage.value = page;
                loadEquipments();
            };

            // 关闭模态框
            const closeModal = () => {
                $('#equipmentModal').modal('hide');
            };

            // 关闭详情模态框
            const closeDetailModal = () => {
                $('#detailModal').modal('hide');
            };

            // ==================== 装备类型管理方法 ====================

            // 加载装备类型列表
            const loadEquipmentTypes = async () => {
                typeLoading.value = true;
                try {
                    const requestData = {
                        ...typeQueryForm.value,
                        Page: typeCurrentPage.value,
                        PageSize: typePageSize.value
                    };
                    console.log('发送的装备类型查询参数：', requestData);
                    
                    const response = await axios.post('/Equipment/GetEquipmentTypeList', requestData);

                    if (response.data.code === 200) {
                        equipmentTypesList.value = response.data.data || [];
                        typesTotalCount.value = response.data.total || 0;
                    } else {
                        alert('加载装备类型列表失败');
                    }
                } catch (error) {
                    console.error('加载装备类型列表出错：', error);
                    alert('加载装备类型列表失败');
                } finally {
                    typeLoading.value = false;
                }
            };

            // 搜索装备类型
            const searchEquipmentTypes = () => {
                typeCurrentPage.value = 1;
                loadEquipmentTypes();
            };

            // 重置装备类型搜索
            const resetTypeSearch = () => {
                typeQueryForm.value = {
                    EquipTypeId: '',
                    TypeName: '',
                    Page: 1,
                    PageSize: 10
                };
                typeCurrentPage.value = 1;
                loadEquipmentTypes();
            };

            // 重置装备类型表单
            const resetTypeForm = () => {
                equipmentTypeForm.value = {
                    equipTypeId: '',
                    typeName: '',
                    description: '',
                    sortOrder: 0,
                    isActive: true
                };
            };

            // 显示新增装备类型模态框
            const showAddTypeModal = () => {
                isTypeEdit.value = false;
                resetTypeForm();
                $('#equipmentTypeModal').modal('show');
            };

            // 显示编辑装备类型模态框
            const showEditTypeModal = async (equipmentType) => {
                isTypeEdit.value = true;
                
                try {
                    const response = await axios.get(`/Equipment/GetEquipmentTypeById?equipTypeId=${equipmentType.equipTypeId}`);
                    if (response.data.code === 200) {
                        const data = response.data.data;
                        equipmentTypeForm.value = {
                            equipTypeId: data.equipTypeId,
                            typeName: data.typeName,
                            description: data.description || '',
                            sortOrder: data.sortOrder || 0,
                            isActive: data.isActive !== undefined ? data.isActive : true
                        };
                        $('#equipmentTypeModal').modal('show');
                    } else {
                        alert('获取装备类型信息失败');
                    }
                } catch (error) {
                    console.error('获取装备类型信息出错：', error);
                    alert('获取装备类型信息失败');
                }
            };

            // 保存装备类型
            const saveEquipmentType = async () => {
                // 验证表单
                if (!equipmentTypeForm.value.equipTypeId || !equipmentTypeForm.value.typeName) {
                    alert('请填写必填字段');
                    return;
                }

                typeSaving.value = true;
                try {
                    const url = isTypeEdit.value ? '/Equipment/UpdateEquipmentType' : '/Equipment/CreateEquipmentType';
                    const response = await axios.post(url, equipmentTypeForm.value);

                    if (response.data.code === 200) {
                        alert(isTypeEdit.value ? '装备类型更新成功' : '装备类型创建成功');
                        $('#equipmentTypeModal').modal('hide');
                        loadEquipmentTypes();
                        
                        // 刷新装备管理页面的装备类型下拉选项
                        try {
                            const typesResponse = await axios.get('/Equipment/GetEquipmentTypes');
                            if (typesResponse.data && Array.isArray(typesResponse.data)) {
                                equipmentTypes.value = typesResponse.data;
                            }
                        } catch (error) {
                            console.log('刷新装备类型下拉选项失败：', error);
                        }
                    } else {
                        alert(response.data.message || '操作失败');
                    }
                } catch (error) {
                    console.error('保存装备类型出错：', error);
                    alert('保存装备类型失败');
                } finally {
                    typeSaving.value = false;
                }
            };

            // 删除装备类型
            const deleteEquipmentType = async (equipmentType) => {
                if (equipmentType.equipmentCount > 0) {
                    alert(`该装备类型下还有 ${equipmentType.equipmentCount} 个装备，无法删除！`);
                    return;
                }

                if (!confirm(`确定要删除装备类型"${equipmentType.typeName}"吗？`)) {
                    return;
                }

                try {
                    const response = await axios.post('/Equipment/DeleteEquipmentType', {
                        equipTypeId: equipmentType.equipTypeId
                    });

                    if (response.data.code === 200) {
                        alert('删除成功');
                        loadEquipmentTypes();
                        
                        // 刷新装备管理页面的装备类型下拉选项
                        try {
                            const typesResponse = await axios.get('/Equipment/GetEquipmentTypes');
                            if (typesResponse.data && Array.isArray(typesResponse.data)) {
                                equipmentTypes.value = typesResponse.data;
                            }
                        } catch (error) {
                            console.log('刷新装备类型下拉选项失败：', error);
                        }
                    } else {
                        alert(response.data.message || '删除失败');
                    }
                } catch (error) {
                    console.error('删除装备类型出错：', error);
                    alert('删除失败');
                }
            };

            // 装备类型分页
            const changeTypePage = (page) => {
                if (page < 1 || page > typeTotalPages.value || page === typeCurrentPage.value) {
                    return;
                }
                typeCurrentPage.value = page;
                loadEquipmentTypes();
            };

            // 关闭装备类型模态框
            const closeTypeModal = () => {
                $('#equipmentTypeModal').modal('hide');
            };

            // 生命周期
            onMounted(async () => {
                // 顺序执行，避免DataReader冲突
                await loadEquipments();
                await loadEquipmentTypes();
            });

            return {
                // 装备管理相关
                loading,
                saving,
                equipments,
                equipmentTypes,
                elements,
                elementLimits,
                totalCount,
                currentPage,
                pageSize,
                queryForm,
                equipmentForm,
                currentEquipment,
                isEdit,
                totalPages,
                visiblePages,
                loadEquipments,
                searchEquipments,
                resetSearch,
                showAddModal,
                showEditModal,
                showDetailModal,
                saveEquipment,
                deleteEquipment,
                changePage,
                closeModal,
                closeDetailModal,
                
                // 装备类型管理相关
                typeLoading,
                typeSaving,
                equipmentTypesList,
                typesTotalCount,
                typeCurrentPage,
                typePageSize,
                typeQueryForm,
                equipmentTypeForm,
                isTypeEdit,
                typeTotalPages,
                typeVisiblePages,
                loadEquipmentTypes,
                searchEquipmentTypes,
                resetTypeSearch,
                showAddTypeModal,
                showEditTypeModal,
                saveEquipmentType,
                deleteEquipmentType,
                changeTypePage,
                closeTypeModal
            };
        }
    });

    equipmentApp.mount('#equipmentApp');

    // 科幻选项卡切换处理
    document.addEventListener('DOMContentLoaded', function() {
        const tabButtons = document.querySelectorAll('.cyber-tab-btn');
        const tabPanes = document.querySelectorAll('.cyber-tab-pane');

        tabButtons.forEach(button => {
            button.addEventListener('click', function() {
                const targetId = this.getAttribute('data-target');

                // 移除所有按钮的active类
                tabButtons.forEach(btn => btn.classList.remove('active'));
                // 给当前按钮添加active类
                this.classList.add('active');

                // 隐藏所有选项卡内容
                tabPanes.forEach(pane => pane.classList.remove('active'));
                // 显示目标选项卡内容
                const targetPane = document.getElementById(targetId);
                if (targetPane) {
                    targetPane.classList.add('active');
                }
            });
        });
    });
</script>