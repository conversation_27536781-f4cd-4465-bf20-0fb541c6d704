using BMS.Models.DTOs;
using BMS.Models.Common;
using BMS.Models.Entities;
using BMS.Services.Interfaces;
using SqlSugar;

namespace BMS.Services.Implementations
{
    /// <summary>
    /// 地图怪物服务实现
    /// </summary>
    public class MapMonsterService : IMapMonsterService
    {
        private readonly IDbService _dbService;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="dbService">数据库服务</param>
        public MapMonsterService(IDbService dbService)
        {
            _dbService = dbService;
        }

        /// <summary>
        /// 分页获取地图怪物列表
        /// </summary>
        /// <param name="queryDto">查询条件</param>
        /// <returns>分页结果</returns>
        public async Task<PagedResult<MapMonsterDto>> GetPagedListAsync(MapMonsterQueryDto queryDto)
        {
            var query = _dbService.Queryable<map_monster>();

            // 添加查询条件
            if (queryDto.MapId.HasValue)
            {
                query = query.Where(x => x.map_id == queryDto.MapId.Value);
            }

            if (!string.IsNullOrEmpty(queryDto.MonsterName))
            {
                query = query.Where(x => x.monster_name.Contains(queryDto.MonsterName));
            }

            if (!string.IsNullOrEmpty(queryDto.Element))
            {
                query = query.Where(x => x.element == queryDto.Element);
            }

            // 获取总数
            var totalCount = await query.CountAsync();

            // 分页查询
            var monsters = await query
                .OrderBy(x => x.map_id)
                .OrderBy(x => x.monster_id)
                .Skip((queryDto.Page - 1) * queryDto.PageSize)
                .Take(queryDto.PageSize)
                .ToListAsync();

            // 获取所有相关的地图信息
            var mapIds = monsters.Select(m => m.map_id).Distinct().ToList();
            var maps = await _dbService.Queryable<map_config>()
                .Where(m => mapIds.Contains(m.map_id))
                .ToListAsync();

            // 创建地图ID到地图名称的映射
            var mapNameDict = maps.ToDictionary(m => m.map_id, m => m.map_name ?? string.Empty);

            // 转换为DTO并关联地图名称
            var dtoList = monsters.Select(monster => ConvertToDto(monster, 
                mapNameDict.TryGetValue(monster.map_id, out var mapName) ? mapName : "未知地图")).ToList();

            return new PagedResult<MapMonsterDto>(dtoList, queryDto.Page, queryDto.PageSize, totalCount);
        }

        /// <summary>
        /// 根据ID获取地图怪物
        /// </summary>
        /// <param name="id">地图怪物ID</param>
        /// <returns>地图怪物信息</returns>
        public async Task<MapMonsterDto?> GetByIdAsync(int id)
        {
            var entity = await _dbService.Queryable<map_monster>()
                .FirstAsync(x => x.id == id);

            return entity != null ? ConvertToDto(entity) : null;
        }

        /// <summary>
        /// 根据地图ID获取怪物列表
        /// </summary>
        /// <param name="mapId">地图ID</param>
        /// <returns>怪物列表</returns>
        public async Task<List<MapMonsterDto>> GetByMapIdAsync(int mapId)
        {
            var entities = await _dbService.Queryable<map_monster>()
                .Where(x => x.map_id == mapId)
                .OrderBy(x => x.monster_id)
                .ToListAsync();

            return entities.Select(ConvertToDto).ToList();
        }

        /// <summary>
        /// 创建地图怪物
        /// </summary>
        /// <param name="createDto">创建DTO</param>
        /// <returns>操作结果</returns>
        public async Task<ApiResult<bool>> CreateAsync(MapMonsterCreateDto createDto)
        {
            try
            {
                // 检查地图怪物是否已存在
                var exists = await CheckMapMonsterExistsAsync(createDto.MapId, createDto.MonsterId);
                if (exists)
                {
                    return ApiResult<bool>.Fail("该地图中的怪物序号已存在");
                }

                // 转换为实体
                var entity = ConvertToEntity(createDto);

                // 插入数据
                var result = await _dbService.Insertable(entity)
                    .ExecuteReturnBigIdentityAsync();

                if (result > 0)
                {
                    return ApiResult<bool>.Ok(true, "地图怪物创建成功");
                }
                else
                {
                    return ApiResult<bool>.Fail("地图怪物创建失败");
                }
            }
            catch (Exception ex)
            {
                return ApiResult<bool>.Fail($"创建地图怪物时发生错误：{ex.Message}");
            }
        }

        /// <summary>
        /// 更新地图怪物
        /// </summary>
        /// <param name="updateDto">更新DTO</param>
        /// <returns>操作结果</returns>
        public async Task<ApiResult<bool>> UpdateAsync(MapMonsterUpdateDto updateDto)
        {
            try
            {
                // 检查记录是否存在
                var exists = await _dbService.Queryable<map_monster>()
                    .AnyAsync(x => x.id == updateDto.Id);
                if (!exists)
                {
                    return ApiResult<bool>.Fail("地图怪物不存在");
                }

                // 检查地图怪物序号是否被其他记录使用
                var monsterExists = await CheckMapMonsterExistsAsync(updateDto.MapId, updateDto.MonsterId, updateDto.Id);
                if (monsterExists)
                {
                    return ApiResult<bool>.Fail("该地图中的怪物序号已被其他记录使用");
                }

                // 转换为实体
                var entity = ConvertToEntity(updateDto);

                // 更新数据
                var result = await _dbService.Updateable(entity)
                    .ExecuteCommandAsync();

                if (result > 0)
                {
                    return ApiResult<bool>.Ok(true, "地图怪物更新成功");
                }
                else
                {
                    return ApiResult<bool>.Fail("地图怪物更新失败");
                }
            }
            catch (Exception ex)
            {
                return ApiResult<bool>.Fail($"更新地图怪物时发生错误：{ex.Message}");
            }
        }

        /// <summary>
        /// 删除地图怪物
        /// </summary>
        /// <param name="id">地图怪物ID</param>
        /// <returns>操作结果</returns>
        public async Task<ApiResult<bool>> DeleteAsync(int id)
        {
            try
            {
                // 检查记录是否存在
                var exists = await _dbService.Queryable<map_monster>()
                    .AnyAsync(x => x.id == id);
                if (!exists)
                {
                    return ApiResult<bool>.Fail("地图怪物不存在");
                }

                // 删除数据
                var result = await _dbService.Deleteable<map_monster>()
                    .Where(x => x.id == id)
                    .ExecuteCommandAsync();

                if (result > 0)
                {
                    return ApiResult<bool>.Ok(true, "地图怪物删除成功");
                }
                else
                {
                    return ApiResult<bool>.Fail("地图怪物删除失败");
                }
            }
            catch (Exception ex)
            {
                return ApiResult<bool>.Fail($"删除地图怪物时发生错误：{ex.Message}");
            }
        }

        /// <summary>
        /// 根据地图ID删除所有怪物
        /// </summary>
        /// <param name="mapId">地图ID</param>
        /// <returns>操作结果</returns>
        public async Task<ApiResult<bool>> DeleteByMapIdAsync(int mapId)
        {
            try
            {
                // 删除数据
                var result = await _dbService.Deleteable<map_monster>()
                    .Where(x => x.map_id == mapId)
                    .ExecuteCommandAsync();

                return ApiResult<bool>.Ok(true, $"删除了{result}条地图怪物记录");
            }
            catch (Exception ex)
            {
                return ApiResult<bool>.Fail($"删除地图怪物时发生错误：{ex.Message}");
            }
        }

        /// <summary>
        /// 检查地图怪物是否存在
        /// </summary>
        /// <param name="mapId">地图ID</param>
        /// <param name="monsterId">怪物序号</param>
        /// <param name="id">排除的ID（编辑时使用）</param>
        /// <returns>是否存在</returns>
        public async Task<bool> CheckMapMonsterExistsAsync(int mapId, int monsterId, int? id = null)
        {
            var query = _dbService.Queryable<map_monster>()
                .Where(x => x.map_id == mapId && x.monster_id == monsterId);

            if (id.HasValue)
            {
                query = query.Where(x => x.id != id.Value);
            }

            return await query.AnyAsync();
        }

        /// <summary>
        /// 实体转换为DTO
        /// </summary>
        /// <param name="entity">实体</param>
        /// <returns>DTO</returns>
        private static MapMonsterDto ConvertToDto(map_monster entity)
        {
            return new MapMonsterDto
            {
                Id = entity.id,
                MapId = entity.map_id,
                MapName = string.Empty,
                MonsterId = entity.monster_id ?? 0,
                MonsterName = entity.monster_name ?? string.Empty,
                Growth = entity.growth ?? 0,
                Element = entity.element,
                MaxLevel = entity.max_level ?? 0,
                MinLevel = entity.min_level ?? 0,
                MaxDrop = entity.max_drop ?? 0,
                Exp = entity.exp ?? 0,
                Hp = entity.hp ?? 0,
                Mp = entity.mp ?? 0,
                MaxHp = entity.max_hp ?? 0,
                MaxMp = entity.max_mp ?? 0,
                Atk = entity.atk ?? 0,
                Def = entity.def ?? 0,
                Dodge = entity.dodge ?? 0,
                Spd = entity.spd ?? 0
            };
        }

        /// <summary>
        /// 实体转换为DTO（包含地图名称）
        /// </summary>
        /// <param name="entity">实体</param>
        /// <param name="mapName">地图名称</param>
        /// <returns>DTO</returns>
        private static MapMonsterDto ConvertToDto(map_monster entity, string mapName)
        {
            return new MapMonsterDto
            {
                Id = entity.id,
                MapId = entity.map_id,
                MapName = mapName,
                MonsterId = entity.monster_id ?? 0,
                MonsterName = entity.monster_name ?? string.Empty,
                Growth = entity.growth ?? 0,
                Element = entity.element,
                MaxLevel = entity.max_level ?? 0,
                MinLevel = entity.min_level ?? 0,
                MaxDrop = entity.max_drop ?? 0,
                Exp = entity.exp ?? 0,
                Hp = entity.hp ?? 0,
                Mp = entity.mp ?? 0,
                MaxHp = entity.max_hp ?? 0,
                MaxMp = entity.max_mp ?? 0,
                Atk = entity.atk ?? 0,
                Def = entity.def ?? 0,
                Dodge = entity.dodge ?? 0,
                Spd = entity.spd ?? 0
            };
        }

        /// <summary>
        /// 创建DTO转换为实体
        /// </summary>
        /// <param name="createDto">创建DTO</param>
        /// <returns>实体</returns>
        private static map_monster ConvertToEntity(MapMonsterCreateDto createDto)
        {
            return new map_monster
            {
                map_id = createDto.MapId,
                monster_id = createDto.MonsterId,
                monster_name = createDto.MonsterName,
                growth = createDto.Growth,
                element = createDto.Element,
                max_level = createDto.MaxLevel,
                min_level = createDto.MinLevel,
                max_drop = createDto.MaxDrop,
                exp = createDto.Exp,
                hp = createDto.Hp,
                mp = createDto.Mp,
                max_hp = createDto.MaxHp,
                max_mp = createDto.MaxMp,
                atk = createDto.Atk,
                def = createDto.Def,
                dodge = createDto.Dodge,
                spd = createDto.Spd
            };
        }

        /// <summary>
        /// 更新DTO转换为实体
        /// </summary>
        /// <param name="updateDto">更新DTO</param>
        /// <returns>实体</returns>
        private static map_monster ConvertToEntity(MapMonsterUpdateDto updateDto)
        {
            return new map_monster
            {
                id = updateDto.Id,
                map_id = updateDto.MapId,
                monster_id = updateDto.MonsterId,
                monster_name = updateDto.MonsterName,
                growth = updateDto.Growth,
                element = updateDto.Element,
                max_level = updateDto.MaxLevel,
                min_level = updateDto.MinLevel,
                max_drop = updateDto.MaxDrop,
                exp = updateDto.Exp,
                hp = updateDto.Hp,
                mp = updateDto.Mp,
                max_hp = updateDto.MaxHp,
                max_mp = updateDto.MaxMp,
                atk = updateDto.Atk,
                def = updateDto.Def,
                dodge = updateDto.Dodge,
                spd = updateDto.Spd
            };
        }
    }
} 