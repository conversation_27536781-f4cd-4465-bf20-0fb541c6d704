using BMS.Models.DTOs;
using BMS.Models.Common;

namespace BMS.Services.Interfaces;

/// <summary>
/// 用户装备服务接口
/// </summary>
public interface IUserEquipmentService
{
    /// <summary>
    /// 获取用户装备列表（分页）
    /// </summary>
    /// <param name="queryDto">查询条件</param>
    /// <returns>分页结果</returns>
    Task<PagedResult<UserEquipmentDto>> GetUserEquipmentsAsync(UserEquipmentQueryDto queryDto);

    /// <summary>
    /// 根据ID获取用户装备详情
    /// </summary>
    /// <param name="id">装备ID</param>
    /// <returns>装备详情</returns>
    Task<UserEquipmentDto?> GetUserEquipmentByIdAsync(int id);

    /// <summary>
    /// 创建用户装备
    /// </summary>
    /// <param name="createDto">创建DTO</param>
    /// <returns>操作结果</returns>
    Task<ApiResult<UserEquipmentDto>> CreateUserEquipmentAsync(UserEquipmentCreateDto createDto);

    /// <summary>
    /// 更新用户装备
    /// </summary>
    /// <param name="updateDto">更新DTO</param>
    /// <returns>操作结果</returns>
    Task<ApiResult<UserEquipmentDto>> UpdateUserEquipmentAsync(UserEquipmentUpdateDto updateDto);

    /// <summary>
    /// 删除用户装备
    /// </summary>
    /// <param name="deleteDto">删除DTO</param>
    /// <returns>操作结果</returns>
    Task<ApiResult<bool>> DeleteUserEquipmentAsync(UserEquipmentDeleteDto deleteDto);

    /// <summary>
    /// 强化装备
    /// </summary>
    /// <param name="strengthenDto">强化DTO</param>
    /// <returns>操作结果</returns>
    Task<ApiResult<UserEquipmentDto>> StrengthenEquipmentAsync(UserEquipmentStrengthenDto strengthenDto);

    /// <summary>
    /// 穿戴装备
    /// </summary>
    /// <param name="wearDto">穿戴DTO</param>
    /// <returns>操作结果</returns>
    Task<ApiResult<UserEquipmentDto>> WearEquipmentAsync(UserEquipmentWearDto wearDto);

    /// <summary>
    /// 脱下装备
    /// </summary>
    /// <param name="equipmentId">装备ID</param>
    /// <returns>操作结果</returns>
    Task<ApiResult<UserEquipmentDto>> UnwearEquipmentAsync(int equipmentId);

    /// <summary>
    /// 获取装备类型选项
    /// </summary>
    /// <returns>装备类型列表</returns>
    Task<List<OptionDto>> GetEquipmentTypeOptionsAsync();

    /// <summary>
    /// 获取装备配置选项
    /// </summary>
    /// <returns>装备配置列表</returns>
    Task<List<EquipmentConfigOptionDto>> GetEquipmentConfigOptionsAsync();

    /// <summary>
    /// 检查装备是否存在
    /// </summary>
    /// <param name="equipId">装备唯一ID</param>
    /// <param name="userId">用户ID</param>
    /// <returns>是否存在</returns>
    Task<bool> ExistsAsync(string equipId, int userId);

    /// <summary>
    /// 获取用户已装备的装备列表
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <returns>已装备的装备列表</returns>
    Task<List<UserEquipmentDto>> GetUserEquippedItemsAsync(int userId);
} 