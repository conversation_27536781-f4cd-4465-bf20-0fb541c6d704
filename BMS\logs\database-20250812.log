[2025-08-12 22:39:52.849 +08:00 INF] 🚀 应用程序启动，日志系统已配置
[2025-08-12 22:39:52.887 +08:00 DBG] 🔧 调试级别日志测试
[2025-08-12 22:39:52.907 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-08-12 22:39:52.910 +08:00 INF] 开始测试数据库连接...
[2025-08-12 22:39:53.005 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-12 22:39:53.628 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-12 22:39:53.643 +08:00 INF] ✅ 数据库连接测试成功
[2025-08-12 22:39:53.646 +08:00 INF] ✅ DbContext 初始化成功，日志文件位置: logs/
[2025-08-12 22:39:55.174 +08:00 WRN] Failed to determine the https port for redirect.
[2025-08-12 22:40:07.068 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-08-12 22:40:07.107 +08:00 INF] 开始测试数据库连接...
[2025-08-12 22:40:07.237 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-12 22:40:07.300 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-12 22:40:07.302 +08:00 INF] ✅ 数据库连接测试成功
[2025-08-12 22:40:07.451 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-08-12 22:40:07.453 +08:00 INF] 开始测试数据库连接...
[2025-08-12 22:40:07.456 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-12 22:40:07.516 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-12 22:40:07.518 +08:00 INF] ✅ 数据库连接测试成功
[2025-08-12 22:40:07.681 +08:00 DBG] 执行SQL: SELECT COUNT(1) FROM `user`  
[2025-08-12 22:40:07.743 +08:00 DBG] SQL执行完成: SELECT COUNT(1) FROM `user`  
[2025-08-12 22:40:07.801 +08:00 DBG] 执行SQL: SELECT `id`,`username`,`password`,`nickname`,`sex`,`vip_level`,`vip_score`,`gold`,`yuanbao`,`crystal`,`reg_time`,`title`,`main_pet_id`,`main_pet_name`,`create_time`,`evolution_cd`,`synthesis_cd`,`nirvana_cd`,`money`,`supreme_vip`,`prop_capacity`,`pasture_capacity`,`dragon_ball_exp`,`dragon_ball_level`,`status`,`prestige`,`auto_battle_count`,`auto_nirvana_count`,`monster_count`,`next_battle`,`open_maps`,`star_vip`,`star_vip_expire`,`task_helper_enabled`,`title_attributes`,`soul_pet`,`hell_floor`,`tower_floor` FROM `user`    ORDER BY `create_time` DESC LIMIT 0,1000
[2025-08-12 22:40:07.882 +08:00 DBG] SQL执行完成: SELECT `id`,`username`,`password`,`nickname`,`sex`,`vip_level`,`vip_score`,`gold`,`yuanbao`,`crystal`,`reg_time`,`title`,`main_pet_id`,`main_pet_name`,`create_time`,`evolution_cd`,`synthesis_cd`,`nirvana_cd`,`money`,`supreme_vip`,`prop_capacity`,`pasture_capacity`,`dragon_ball_exp`,`dragon_ball_level`,`status`,`prestige`,`auto_battle_count`,`auto_nirvana_count`,`monster_count`,`next_battle`,`open_maps`,`star_vip`,`star_vip_expire`,`task_helper_enabled`,`title_attributes`,`soul_pet`,`hell_floor`,`tower_floor` FROM `user`    ORDER BY `create_time` DESC LIMIT 0,1000
[2025-08-12 22:40:07.944 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-08-12 22:40:07.945 +08:00 INF] 开始测试数据库连接...
[2025-08-12 22:40:07.946 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-12 22:40:08.018 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-12 22:40:08.023 +08:00 INF] ✅ 数据库连接测试成功
[2025-08-12 22:40:08.036 +08:00 DBG] 执行SQL: SELECT  `id` AS `Id` , `item_no` AS `ItemNo` , `name` AS `Name` , `type` AS `Type` , `description` AS `Description` , `quality` AS `Quality` , `icon` AS `Icon` , `price` AS `Price` , `use_limit` AS `UseLimit` , `create_time` AS `CreateTime`  FROM `item_config` ORDER BY `item_no` ASC 
[2025-08-12 22:40:08.111 +08:00 DBG] SQL执行完成: SELECT  `id` AS `Id` , `item_no` AS `ItemNo` , `name` AS `Name` , `type` AS `Type` , `description` AS `Description` , `quality` AS `Quality` , `icon` AS `Icon` , `price` AS `Price` , `use_limit` AS `UseLimit` , `create_time` AS `CreateTime`  FROM `item_config` ORDER BY `item_no` ASC 
[2025-08-12 22:40:08.313 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-08-12 22:40:08.320 +08:00 INF] 开始测试数据库连接...
[2025-08-12 22:40:08.323 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-12 22:40:08.373 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-12 22:40:08.376 +08:00 INF] ✅ 数据库连接测试成功
[2025-08-12 22:40:08.395 +08:00 DBG] 执行SQL: SELECT SUM(`item_count`) FROM `user_item`  
[2025-08-12 22:40:08.451 +08:00 DBG] SQL执行完成: SELECT SUM(`item_count`) FROM `user_item`  
[2025-08-12 22:40:08.487 +08:00 DBG] 执行SQL: SELECT COUNT(1) FROM `user_item` GROUP BY `item_id`  
[2025-08-12 22:40:08.678 +08:00 DBG] SQL执行完成: SELECT COUNT(1) FROM `user_item` GROUP BY `item_id`  
[2025-08-12 22:40:09.438 +08:00 DBG] 执行SQL: SELECT  `ic`.`quality` AS `Quality` , SUM(`ui`.`item_count`) AS `Count`  FROM `user_item` `ui` Left JOIN `item_config` `ic` ON ( `ui`.`item_id` = CAST(`ic`.`item_no` AS CHAR))  GROUP BY `ic`.`quality`  
[2025-08-12 22:40:09.496 +08:00 DBG] SQL执行完成: SELECT  `ic`.`quality` AS `Quality` , SUM(`ui`.`item_count`) AS `Count`  FROM `user_item` `ui` Left JOIN `item_config` `ic` ON ( `ui`.`item_id` = CAST(`ic`.`item_no` AS CHAR))  GROUP BY `ic`.`quality`  
[2025-08-12 22:40:09.565 +08:00 DBG] 执行SQL: SELECT SUM(( `ui`.`item_count` *IFNULL(`ic`.`price`,@MethodConst0))) FROM `user_item` `ui` Left JOIN `item_config` `ic` ON ( `ui`.`item_id` = CAST(`ic`.`item_no` AS CHAR))    | 参数: [@MethodConst0=0]
[2025-08-12 22:40:09.658 +08:00 DBG] SQL执行完成: SELECT SUM(( `ui`.`item_count` *IFNULL(`ic`.`price`,@MethodConst0))) FROM `user_item` `ui` Left JOIN `item_config` `ic` ON ( `ui`.`item_id` = CAST(`ic`.`item_no` AS CHAR))   
[2025-08-12 22:40:09.705 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-08-12 22:40:09.713 +08:00 INF] 开始测试数据库连接...
[2025-08-12 22:40:09.718 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-12 22:40:09.787 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-12 22:40:09.791 +08:00 INF] ✅ 数据库连接测试成功
[2025-08-12 22:40:09.845 +08:00 DBG] 执行SQL: SELECT COUNT(1) FROM `user_item` `ui` Left JOIN `user` `u` ON ( `ui`.`user_id` = `u`.`id` )  Left JOIN `item_config` `ic` ON ( `ui`.`item_id` = CAST(`ic`.`item_no` AS CHAR))   
[2025-08-12 22:40:09.898 +08:00 DBG] SQL执行完成: SELECT COUNT(1) FROM `user_item` `ui` Left JOIN `user` `u` ON ( `ui`.`user_id` = `u`.`id` )  Left JOIN `item_config` `ic` ON ( `ui`.`item_id` = CAST(`ic`.`item_no` AS CHAR))   
[2025-08-12 22:40:09.911 +08:00 DBG] 执行SQL: SELECT  `ui`.`id` AS `Id` , `ui`.`user_id` AS `UserId` , IFNULL(`u`.`username`,@MethodConst2) AS `Username` , `ui`.`item_id` AS `ItemId` , CAST(`ui`.`item_id` AS SIGNED) AS `ItemNo` , IFNULL(`ic`.`name`,@MethodConst3) AS `ItemName` , `ic`.`type` AS `ItemType` , `ic`.`quality` AS `ItemQuality` , `ui`.`item_count` AS `ItemCount` , `ui`.`item_pos` AS `ItemPos` , `ui`.`item_seq` AS `ItemSeq` , `ic`.`description` AS `ItemDescription` , `ic`.`price` AS `ItemPrice` , `ic`.`icon` AS `ItemIcon`  FROM `user_item` `ui` Left JOIN `user` `u` ON ( `ui`.`user_id` = `u`.`id` )  Left JOIN `item_config` `ic` ON ( `ui`.`item_id` = CAST(`ic`.`item_no` AS CHAR))     ORDER BY `ui`.`id` DESC LIMIT 0,15 | 参数: [@MethodConst0=, @MethodConst1=, @MethodConst2=, @MethodConst3=]
[2025-08-12 22:40:10.002 +08:00 DBG] SQL执行完成: SELECT  `ui`.`id` AS `Id` , `ui`.`user_id` AS `UserId` , IFNULL(`u`.`username`,@MethodConst2) AS `Username` , `ui`.`item_id` AS `ItemId` , CAST(`ui`.`item_id` AS SIGNED) AS `ItemNo` , IFNULL(`ic`.`name`,@MethodConst3) AS `ItemName` , `ic`.`type` AS `ItemType` , `ic`.`quality` AS `ItemQuality` , `ui`.`item_count` AS `ItemCount` , `ui`.`item_pos` AS `ItemPos` , `ui`.`item_seq` AS `ItemSeq` , `ic`.`description` AS `ItemDescription` , `ic`.`price` AS `ItemPrice` , `ic`.`icon` AS `ItemIcon`  FROM `user_item` `ui` Left JOIN `user` `u` ON ( `ui`.`user_id` = `u`.`id` )  Left JOIN `item_config` `ic` ON ( `ui`.`item_id` = CAST(`ic`.`item_no` AS CHAR))     ORDER BY `ui`.`id` DESC LIMIT 0,15
[2025-08-12 22:40:22.089 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-08-12 22:40:22.091 +08:00 INF] 开始测试数据库连接...
[2025-08-12 22:40:22.093 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-12 22:40:22.147 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-12 22:40:22.150 +08:00 INF] ✅ 数据库连接测试成功
[2025-08-12 22:40:22.175 +08:00 DBG] 执行SQL: DELETE FROM `user_item` WHERE ( `id` = @id0 )  | 参数: [@id0=5]
[2025-08-12 22:40:22.327 +08:00 DBG] SQL执行完成: DELETE FROM `user_item` WHERE ( `id` = @id0 ) 
[2025-08-12 22:40:23.440 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-08-12 22:40:23.440 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-08-12 22:40:23.442 +08:00 INF] 开始测试数据库连接...
[2025-08-12 22:40:23.445 +08:00 INF] 开始测试数据库连接...
[2025-08-12 22:40:23.448 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-12 22:40:23.452 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-12 22:40:23.512 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-12 22:40:23.514 +08:00 INF] ✅ 数据库连接测试成功
[2025-08-12 22:40:23.518 +08:00 DBG] 执行SQL: SELECT SUM(`item_count`) FROM `user_item`  
[2025-08-12 22:40:23.579 +08:00 DBG] SQL执行完成: SELECT SUM(`item_count`) FROM `user_item`  
[2025-08-12 22:40:23.583 +08:00 DBG] 执行SQL: SELECT COUNT(1) FROM `user_item` GROUP BY `item_id`  
[2025-08-12 22:40:23.633 +08:00 DBG] SQL执行完成: SELECT COUNT(1) FROM `user_item` GROUP BY `item_id`  
[2025-08-12 22:40:23.637 +08:00 DBG] 执行SQL: SELECT  `ic`.`quality` AS `Quality` , SUM(`ui`.`item_count`) AS `Count`  FROM `user_item` `ui` Left JOIN `item_config` `ic` ON ( `ui`.`item_id` = CAST(`ic`.`item_no` AS CHAR))  GROUP BY `ic`.`quality`  
[2025-08-12 22:40:23.688 +08:00 DBG] SQL执行完成: SELECT  `ic`.`quality` AS `Quality` , SUM(`ui`.`item_count`) AS `Count`  FROM `user_item` `ui` Left JOIN `item_config` `ic` ON ( `ui`.`item_id` = CAST(`ic`.`item_no` AS CHAR))  GROUP BY `ic`.`quality`  
[2025-08-12 22:40:23.692 +08:00 DBG] 执行SQL: SELECT SUM(( `ui`.`item_count` *IFNULL(`ic`.`price`,@MethodConst0))) FROM `user_item` `ui` Left JOIN `item_config` `ic` ON ( `ui`.`item_id` = CAST(`ic`.`item_no` AS CHAR))    | 参数: [@MethodConst0=0]
[2025-08-12 22:40:23.743 +08:00 DBG] SQL执行完成: SELECT SUM(( `ui`.`item_count` *IFNULL(`ic`.`price`,@MethodConst0))) FROM `user_item` `ui` Left JOIN `item_config` `ic` ON ( `ui`.`item_id` = CAST(`ic`.`item_no` AS CHAR))   
[2025-08-12 22:40:23.912 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-12 22:40:23.913 +08:00 INF] ✅ 数据库连接测试成功
[2025-08-12 22:40:23.914 +08:00 DBG] 执行SQL: SELECT COUNT(1) FROM `user_item` `ui` Left JOIN `user` `u` ON ( `ui`.`user_id` = `u`.`id` )  Left JOIN `item_config` `ic` ON ( `ui`.`item_id` = CAST(`ic`.`item_no` AS CHAR))   
[2025-08-12 22:40:23.950 +08:00 DBG] SQL执行完成: SELECT COUNT(1) FROM `user_item` `ui` Left JOIN `user` `u` ON ( `ui`.`user_id` = `u`.`id` )  Left JOIN `item_config` `ic` ON ( `ui`.`item_id` = CAST(`ic`.`item_no` AS CHAR))   
[2025-08-12 22:40:23.953 +08:00 DBG] 执行SQL: SELECT  `ui`.`id` AS `Id` , `ui`.`user_id` AS `UserId` , IFNULL(`u`.`username`,@MethodConst2) AS `Username` , `ui`.`item_id` AS `ItemId` , CAST(`ui`.`item_id` AS SIGNED) AS `ItemNo` , IFNULL(`ic`.`name`,@MethodConst3) AS `ItemName` , `ic`.`type` AS `ItemType` , `ic`.`quality` AS `ItemQuality` , `ui`.`item_count` AS `ItemCount` , `ui`.`item_pos` AS `ItemPos` , `ui`.`item_seq` AS `ItemSeq` , `ic`.`description` AS `ItemDescription` , `ic`.`price` AS `ItemPrice` , `ic`.`icon` AS `ItemIcon`  FROM `user_item` `ui` Left JOIN `user` `u` ON ( `ui`.`user_id` = `u`.`id` )  Left JOIN `item_config` `ic` ON ( `ui`.`item_id` = CAST(`ic`.`item_no` AS CHAR))     ORDER BY `ui`.`id` DESC LIMIT 0,15 | 参数: [@MethodConst0=, @MethodConst1=, @MethodConst2=, @MethodConst3=]
[2025-08-12 22:40:24.001 +08:00 DBG] SQL执行完成: SELECT  `ui`.`id` AS `Id` , `ui`.`user_id` AS `UserId` , IFNULL(`u`.`username`,@MethodConst2) AS `Username` , `ui`.`item_id` AS `ItemId` , CAST(`ui`.`item_id` AS SIGNED) AS `ItemNo` , IFNULL(`ic`.`name`,@MethodConst3) AS `ItemName` , `ic`.`type` AS `ItemType` , `ic`.`quality` AS `ItemQuality` , `ui`.`item_count` AS `ItemCount` , `ui`.`item_pos` AS `ItemPos` , `ui`.`item_seq` AS `ItemSeq` , `ic`.`description` AS `ItemDescription` , `ic`.`price` AS `ItemPrice` , `ic`.`icon` AS `ItemIcon`  FROM `user_item` `ui` Left JOIN `user` `u` ON ( `ui`.`user_id` = `u`.`id` )  Left JOIN `item_config` `ic` ON ( `ui`.`item_id` = CAST(`ic`.`item_no` AS CHAR))     ORDER BY `ui`.`id` DESC LIMIT 0,15
[2025-08-12 22:40:35.621 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-08-12 22:40:35.625 +08:00 INF] 开始测试数据库连接...
[2025-08-12 22:40:35.626 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-12 22:40:35.669 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-12 22:40:35.670 +08:00 INF] ✅ 数据库连接测试成功
[2025-08-12 22:40:56.055 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-08-12 22:40:56.059 +08:00 INF] 开始测试数据库连接...
[2025-08-12 22:40:56.062 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-12 22:40:56.098 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-12 22:40:56.100 +08:00 INF] ✅ 数据库连接测试成功
[2025-08-12 22:40:56.113 +08:00 DBG] 执行SQL: SELECT 1 FROM `user`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=1]
[2025-08-12 22:40:56.147 +08:00 DBG] SQL执行完成: SELECT 1 FROM `user`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-08-12 22:40:56.151 +08:00 DBG] 执行SQL: SELECT 1 FROM `item_config`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1 | 参数: [@item_no0=20]
[2025-08-12 22:40:56.185 +08:00 DBG] SQL执行完成: SELECT 1 FROM `item_config`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1
[2025-08-12 22:40:56.197 +08:00 DBG] 执行SQL: SELECT `id`,`user_id`,`item_id`,`item_count`,`item_pos`,`item_seq`,`create_time` FROM `user_item`   WHERE (( `user_id` = @user_id0 ) AND ( `item_id` = CAST(@MethodConst1 AS CHAR)))   LIMIT 0,1 | 参数: [@user_id0=1, @MethodConst1=20]
[2025-08-12 22:40:56.246 +08:00 DBG] SQL执行完成: SELECT `id`,`user_id`,`item_id`,`item_count`,`item_pos`,`item_seq`,`create_time` FROM `user_item`   WHERE (( `user_id` = @user_id0 ) AND ( `item_id` = CAST(@MethodConst1 AS CHAR)))   LIMIT 0,1
[2025-08-12 22:40:56.306 +08:00 DBG] 执行SQL: UPDATE `user_item`  SET
           `user_id`=@user_id,`item_id`=@item_id,`item_count`=@item_count,`item_pos`=@item_pos,`item_seq`=@item_seq,`create_time`=@create_time  WHERE `id`=@id | 参数: [@id=7, @user_id=1, @item_id=20, @item_count=20, @item_pos=12, @item_seq=6, @create_time=]
[2025-08-12 22:40:56.466 +08:00 DBG] SQL执行完成: UPDATE `user_item`  SET
           `user_id`=@user_id,`item_id`=@item_id,`item_count`=@item_count,`item_pos`=@item_pos,`item_seq`=@item_seq,`create_time`=@create_time  WHERE `id`=@id
[2025-08-12 22:40:58.861 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-08-12 22:40:58.869 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-08-12 22:40:58.888 +08:00 INF] 开始测试数据库连接...
[2025-08-12 22:40:58.889 +08:00 INF] 开始测试数据库连接...
[2025-08-12 22:40:58.893 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-12 22:40:58.893 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-12 22:40:58.932 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-12 22:40:58.933 +08:00 INF] ✅ 数据库连接测试成功
[2025-08-12 22:40:58.935 +08:00 DBG] 执行SQL: SELECT SUM(`item_count`) FROM `user_item`  
[2025-08-12 22:40:58.951 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-12 22:40:58.952 +08:00 INF] ✅ 数据库连接测试成功
[2025-08-12 22:40:58.957 +08:00 DBG] 执行SQL: SELECT COUNT(1) FROM `user_item` `ui` Left JOIN `user` `u` ON ( `ui`.`user_id` = `u`.`id` )  Left JOIN `item_config` `ic` ON ( `ui`.`item_id` = CAST(`ic`.`item_no` AS CHAR))   
[2025-08-12 22:40:58.969 +08:00 DBG] SQL执行完成: SELECT SUM(`item_count`) FROM `user_item`  
[2025-08-12 22:40:58.979 +08:00 DBG] 执行SQL: SELECT COUNT(1) FROM `user_item` GROUP BY `item_id`  
[2025-08-12 22:40:59.024 +08:00 DBG] SQL执行完成: SELECT COUNT(1) FROM `user_item` `ui` Left JOIN `user` `u` ON ( `ui`.`user_id` = `u`.`id` )  Left JOIN `item_config` `ic` ON ( `ui`.`item_id` = CAST(`ic`.`item_no` AS CHAR))   
[2025-08-12 22:40:59.028 +08:00 DBG] SQL执行完成: SELECT COUNT(1) FROM `user_item` GROUP BY `item_id`  
[2025-08-12 22:40:59.036 +08:00 DBG] 执行SQL: SELECT  `ui`.`id` AS `Id` , `ui`.`user_id` AS `UserId` , IFNULL(`u`.`username`,@MethodConst2) AS `Username` , `ui`.`item_id` AS `ItemId` , CAST(`ui`.`item_id` AS SIGNED) AS `ItemNo` , IFNULL(`ic`.`name`,@MethodConst3) AS `ItemName` , `ic`.`type` AS `ItemType` , `ic`.`quality` AS `ItemQuality` , `ui`.`item_count` AS `ItemCount` , `ui`.`item_pos` AS `ItemPos` , `ui`.`item_seq` AS `ItemSeq` , `ic`.`description` AS `ItemDescription` , `ic`.`price` AS `ItemPrice` , `ic`.`icon` AS `ItemIcon`  FROM `user_item` `ui` Left JOIN `user` `u` ON ( `ui`.`user_id` = `u`.`id` )  Left JOIN `item_config` `ic` ON ( `ui`.`item_id` = CAST(`ic`.`item_no` AS CHAR))     ORDER BY `ui`.`id` DESC LIMIT 0,15 | 参数: [@MethodConst0=, @MethodConst1=, @MethodConst2=, @MethodConst3=]
[2025-08-12 22:40:59.068 +08:00 DBG] 执行SQL: SELECT  `ic`.`quality` AS `Quality` , SUM(`ui`.`item_count`) AS `Count`  FROM `user_item` `ui` Left JOIN `item_config` `ic` ON ( `ui`.`item_id` = CAST(`ic`.`item_no` AS CHAR))  GROUP BY `ic`.`quality`  
[2025-08-12 22:40:59.208 +08:00 DBG] SQL执行完成: SELECT  `ui`.`id` AS `Id` , `ui`.`user_id` AS `UserId` , IFNULL(`u`.`username`,@MethodConst2) AS `Username` , `ui`.`item_id` AS `ItemId` , CAST(`ui`.`item_id` AS SIGNED) AS `ItemNo` , IFNULL(`ic`.`name`,@MethodConst3) AS `ItemName` , `ic`.`type` AS `ItemType` , `ic`.`quality` AS `ItemQuality` , `ui`.`item_count` AS `ItemCount` , `ui`.`item_pos` AS `ItemPos` , `ui`.`item_seq` AS `ItemSeq` , `ic`.`description` AS `ItemDescription` , `ic`.`price` AS `ItemPrice` , `ic`.`icon` AS `ItemIcon`  FROM `user_item` `ui` Left JOIN `user` `u` ON ( `ui`.`user_id` = `u`.`id` )  Left JOIN `item_config` `ic` ON ( `ui`.`item_id` = CAST(`ic`.`item_no` AS CHAR))     ORDER BY `ui`.`id` DESC LIMIT 0,15
[2025-08-12 22:40:59.301 +08:00 DBG] SQL执行完成: SELECT  `ic`.`quality` AS `Quality` , SUM(`ui`.`item_count`) AS `Count`  FROM `user_item` `ui` Left JOIN `item_config` `ic` ON ( `ui`.`item_id` = CAST(`ic`.`item_no` AS CHAR))  GROUP BY `ic`.`quality`  
[2025-08-12 22:40:59.304 +08:00 DBG] 执行SQL: SELECT SUM(( `ui`.`item_count` *IFNULL(`ic`.`price`,@MethodConst0))) FROM `user_item` `ui` Left JOIN `item_config` `ic` ON ( `ui`.`item_id` = CAST(`ic`.`item_no` AS CHAR))    | 参数: [@MethodConst0=0]
[2025-08-12 22:40:59.374 +08:00 DBG] SQL执行完成: SELECT SUM(( `ui`.`item_count` *IFNULL(`ic`.`price`,@MethodConst0))) FROM `user_item` `ui` Left JOIN `item_config` `ic` ON ( `ui`.`item_id` = CAST(`ic`.`item_no` AS CHAR))   
[2025-08-12 22:41:10.701 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-08-12 22:41:10.704 +08:00 INF] 开始测试数据库连接...
[2025-08-12 22:41:10.706 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-12 22:41:10.774 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-12 22:41:10.776 +08:00 INF] ✅ 数据库连接测试成功
[2025-08-12 22:41:10.787 +08:00 DBG] 执行SQL: SELECT `id`,`user_id`,`item_id`,`item_count`,`item_pos`,`item_seq`,`create_time` FROM `user_item`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=7]
[2025-08-12 22:41:10.853 +08:00 DBG] SQL执行完成: SELECT `id`,`user_id`,`item_id`,`item_count`,`item_pos`,`item_seq`,`create_time` FROM `user_item`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-08-12 22:41:10.860 +08:00 DBG] 执行SQL: UPDATE `user_item`  SET
           `user_id`=@user_id,`item_id`=@item_id,`item_count`=@item_count,`item_pos`=@item_pos,`item_seq`=@item_seq,`create_time`=@create_time  WHERE `id`=@id | 参数: [@id=7, @user_id=1, @item_id=20, @item_count=20, @item_pos=1, @item_seq=6, @create_time=]
[2025-08-12 22:41:11.012 +08:00 DBG] SQL执行完成: UPDATE `user_item`  SET
           `user_id`=@user_id,`item_id`=@item_id,`item_count`=@item_count,`item_pos`=@item_pos,`item_seq`=@item_seq,`create_time`=@create_time  WHERE `id`=@id
[2025-08-12 22:41:12.310 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-08-12 22:41:12.322 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-08-12 22:41:12.424 +08:00 INF] 开始测试数据库连接...
[2025-08-12 22:41:12.470 +08:00 INF] 开始测试数据库连接...
[2025-08-12 22:41:12.473 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-12 22:41:12.483 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-12 22:41:12.538 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-12 22:41:12.544 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-12 22:41:12.745 +08:00 INF] ✅ 数据库连接测试成功
[2025-08-12 22:41:12.746 +08:00 INF] ✅ 数据库连接测试成功
[2025-08-12 22:41:12.768 +08:00 DBG] 执行SQL: SELECT SUM(`item_count`) FROM `user_item`  
[2025-08-12 22:41:12.769 +08:00 DBG] 执行SQL: SELECT COUNT(1) FROM `user_item` `ui` Left JOIN `user` `u` ON ( `ui`.`user_id` = `u`.`id` )  Left JOIN `item_config` `ic` ON ( `ui`.`item_id` = CAST(`ic`.`item_no` AS CHAR))   
[2025-08-12 22:41:12.860 +08:00 DBG] SQL执行完成: SELECT COUNT(1) FROM `user_item` `ui` Left JOIN `user` `u` ON ( `ui`.`user_id` = `u`.`id` )  Left JOIN `item_config` `ic` ON ( `ui`.`item_id` = CAST(`ic`.`item_no` AS CHAR))   
[2025-08-12 22:41:12.862 +08:00 DBG] SQL执行完成: SELECT SUM(`item_count`) FROM `user_item`  
[2025-08-12 22:41:12.867 +08:00 DBG] 执行SQL: SELECT  `ui`.`id` AS `Id` , `ui`.`user_id` AS `UserId` , IFNULL(`u`.`username`,@MethodConst2) AS `Username` , `ui`.`item_id` AS `ItemId` , CAST(`ui`.`item_id` AS SIGNED) AS `ItemNo` , IFNULL(`ic`.`name`,@MethodConst3) AS `ItemName` , `ic`.`type` AS `ItemType` , `ic`.`quality` AS `ItemQuality` , `ui`.`item_count` AS `ItemCount` , `ui`.`item_pos` AS `ItemPos` , `ui`.`item_seq` AS `ItemSeq` , `ic`.`description` AS `ItemDescription` , `ic`.`price` AS `ItemPrice` , `ic`.`icon` AS `ItemIcon`  FROM `user_item` `ui` Left JOIN `user` `u` ON ( `ui`.`user_id` = `u`.`id` )  Left JOIN `item_config` `ic` ON ( `ui`.`item_id` = CAST(`ic`.`item_no` AS CHAR))     ORDER BY `ui`.`id` DESC LIMIT 0,15 | 参数: [@MethodConst0=, @MethodConst1=, @MethodConst2=, @MethodConst3=]
[2025-08-12 22:41:12.913 +08:00 DBG] 执行SQL: SELECT COUNT(1) FROM `user_item` GROUP BY `item_id`  
[2025-08-12 22:41:12.987 +08:00 DBG] SQL执行完成: SELECT COUNT(1) FROM `user_item` GROUP BY `item_id`  
[2025-08-12 22:41:12.994 +08:00 DBG] 执行SQL: SELECT  `ic`.`quality` AS `Quality` , SUM(`ui`.`item_count`) AS `Count`  FROM `user_item` `ui` Left JOIN `item_config` `ic` ON ( `ui`.`item_id` = CAST(`ic`.`item_no` AS CHAR))  GROUP BY `ic`.`quality`  
[2025-08-12 22:41:13.020 +08:00 DBG] SQL执行完成: SELECT  `ui`.`id` AS `Id` , `ui`.`user_id` AS `UserId` , IFNULL(`u`.`username`,@MethodConst2) AS `Username` , `ui`.`item_id` AS `ItemId` , CAST(`ui`.`item_id` AS SIGNED) AS `ItemNo` , IFNULL(`ic`.`name`,@MethodConst3) AS `ItemName` , `ic`.`type` AS `ItemType` , `ic`.`quality` AS `ItemQuality` , `ui`.`item_count` AS `ItemCount` , `ui`.`item_pos` AS `ItemPos` , `ui`.`item_seq` AS `ItemSeq` , `ic`.`description` AS `ItemDescription` , `ic`.`price` AS `ItemPrice` , `ic`.`icon` AS `ItemIcon`  FROM `user_item` `ui` Left JOIN `user` `u` ON ( `ui`.`user_id` = `u`.`id` )  Left JOIN `item_config` `ic` ON ( `ui`.`item_id` = CAST(`ic`.`item_no` AS CHAR))     ORDER BY `ui`.`id` DESC LIMIT 0,15
[2025-08-12 22:41:13.082 +08:00 DBG] SQL执行完成: SELECT  `ic`.`quality` AS `Quality` , SUM(`ui`.`item_count`) AS `Count`  FROM `user_item` `ui` Left JOIN `item_config` `ic` ON ( `ui`.`item_id` = CAST(`ic`.`item_no` AS CHAR))  GROUP BY `ic`.`quality`  
[2025-08-12 22:41:13.092 +08:00 DBG] 执行SQL: SELECT SUM(( `ui`.`item_count` *IFNULL(`ic`.`price`,@MethodConst0))) FROM `user_item` `ui` Left JOIN `item_config` `ic` ON ( `ui`.`item_id` = CAST(`ic`.`item_no` AS CHAR))    | 参数: [@MethodConst0=0]
[2025-08-12 22:41:13.156 +08:00 DBG] SQL执行完成: SELECT SUM(( `ui`.`item_count` *IFNULL(`ic`.`price`,@MethodConst0))) FROM `user_item` `ui` Left JOIN `item_config` `ic` ON ( `ui`.`item_id` = CAST(`ic`.`item_no` AS CHAR))   
[2025-08-12 22:42:18.112 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-08-12 22:42:18.117 +08:00 INF] 开始测试数据库连接...
[2025-08-12 22:42:18.123 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-12 22:42:18.165 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-12 22:42:18.168 +08:00 INF] ✅ 数据库连接测试成功
[2025-08-12 22:42:18.171 +08:00 DBG] 执行SQL: SELECT 1 FROM `user`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=1]
[2025-08-12 22:42:18.214 +08:00 DBG] SQL执行完成: SELECT 1 FROM `user`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-08-12 22:42:18.219 +08:00 DBG] 执行SQL: SELECT 1 FROM `item_config`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1 | 参数: [@item_no0=2018031001]
[2025-08-12 22:42:18.272 +08:00 DBG] SQL执行完成: SELECT 1 FROM `item_config`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1
[2025-08-12 22:42:18.313 +08:00 DBG] 执行SQL: SELECT `id`,`user_id`,`item_id`,`item_count`,`item_pos`,`item_seq`,`create_time` FROM `user_item`   WHERE (( `user_id` = @user_id0 ) AND ( `item_id` = CAST(@MethodConst1 AS CHAR)))   LIMIT 0,1 | 参数: [@user_id0=1, @MethodConst1=2018031001]
[2025-08-12 22:42:18.354 +08:00 DBG] SQL执行完成: SELECT `id`,`user_id`,`item_id`,`item_count`,`item_pos`,`item_seq`,`create_time` FROM `user_item`   WHERE (( `user_id` = @user_id0 ) AND ( `item_id` = CAST(@MethodConst1 AS CHAR)))   LIMIT 0,1
[2025-08-12 22:42:18.367 +08:00 DBG] 执行SQL: SELECT MAX(`item_seq`) FROM `user_item`  WHERE ( `user_id` = @user_id0 )  | 参数: [@user_id0=1]
[2025-08-12 22:42:18.401 +08:00 DBG] SQL执行完成: SELECT MAX(`item_seq`) FROM `user_item`  WHERE ( `user_id` = @user_id0 ) 
[2025-08-12 22:42:18.435 +08:00 DBG] 执行SQL: INSERT INTO `user_item`  
           (`id`,`user_id`,`item_id`,`item_count`,`item_pos`,`item_seq`,`create_time`)
     VALUES
           (@id,@user_id,@item_id,@item_count,@item_pos,@item_seq,@create_time) ; | 参数: [@id=0, @user_id=1, @item_id=2018031001, @item_count=1000, @item_pos=1, @item_seq=15, @create_time=]
[2025-08-12 22:42:18.492 +08:00 ERR] 数据库操作发生错误: Duplicate entry '0' for key 'PRIMARY'
SqlSugar.SqlSugarException: Duplicate entry '0' for key 'PRIMARY'
[2025-08-12 22:43:21.309 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-08-12 22:43:21.310 +08:00 INF] 开始测试数据库连接...
[2025-08-12 22:43:21.312 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-12 22:43:21.348 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-12 22:43:21.349 +08:00 INF] ✅ 数据库连接测试成功
[2025-08-12 22:43:21.352 +08:00 DBG] 执行SQL: SELECT 1 FROM `user`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=1]
[2025-08-12 22:43:21.387 +08:00 DBG] SQL执行完成: SELECT 1 FROM `user`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-08-12 22:43:21.389 +08:00 DBG] 执行SQL: SELECT 1 FROM `item_config`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1 | 参数: [@item_no0=2018031001]
[2025-08-12 22:43:21.415 +08:00 DBG] SQL执行完成: SELECT 1 FROM `item_config`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1
[2025-08-12 22:43:21.418 +08:00 DBG] 执行SQL: SELECT `id`,`user_id`,`item_id`,`item_count`,`item_pos`,`item_seq`,`create_time` FROM `user_item`   WHERE (( `user_id` = @user_id0 ) AND ( `item_id` = CAST(@MethodConst1 AS CHAR)))   LIMIT 0,1 | 参数: [@user_id0=1, @MethodConst1=2018031001]
[2025-08-12 22:43:21.449 +08:00 DBG] SQL执行完成: SELECT `id`,`user_id`,`item_id`,`item_count`,`item_pos`,`item_seq`,`create_time` FROM `user_item`   WHERE (( `user_id` = @user_id0 ) AND ( `item_id` = CAST(@MethodConst1 AS CHAR)))   LIMIT 0,1
[2025-08-12 22:43:21.453 +08:00 DBG] 执行SQL: SELECT MAX(`item_seq`) FROM `user_item`  WHERE ( `user_id` = @user_id0 )  | 参数: [@user_id0=1]
[2025-08-12 22:43:21.489 +08:00 DBG] SQL执行完成: SELECT MAX(`item_seq`) FROM `user_item`  WHERE ( `user_id` = @user_id0 ) 
[2025-08-12 22:43:21.492 +08:00 DBG] 执行SQL: INSERT INTO `user_item`  
           (`id`,`user_id`,`item_id`,`item_count`,`item_pos`,`item_seq`,`create_time`)
     VALUES
           (@id,@user_id,@item_id,@item_count,@item_pos,@item_seq,@create_time) ; | 参数: [@id=0, @user_id=1, @item_id=2018031001, @item_count=1000, @item_pos=1, @item_seq=15, @create_time=]
[2025-08-12 22:43:21.534 +08:00 ERR] 数据库操作发生错误: Duplicate entry '0' for key 'PRIMARY'
SqlSugar.SqlSugarException: Duplicate entry '0' for key 'PRIMARY'
[2025-08-12 22:43:39.821 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-08-12 22:43:39.823 +08:00 INF] 开始测试数据库连接...
[2025-08-12 22:43:39.825 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-12 22:43:39.880 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-12 22:43:39.882 +08:00 INF] ✅ 数据库连接测试成功
[2025-08-12 22:43:39.885 +08:00 DBG] 执行SQL: SELECT 1 FROM `user`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=1]
[2025-08-12 22:43:39.919 +08:00 DBG] SQL执行完成: SELECT 1 FROM `user`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-08-12 22:43:39.922 +08:00 DBG] 执行SQL: SELECT 1 FROM `item_config`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1 | 参数: [@item_no0=2018031001]
[2025-08-12 22:43:39.952 +08:00 DBG] SQL执行完成: SELECT 1 FROM `item_config`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1
[2025-08-12 22:43:39.959 +08:00 DBG] 执行SQL: SELECT `id`,`user_id`,`item_id`,`item_count`,`item_pos`,`item_seq`,`create_time` FROM `user_item`   WHERE (( `user_id` = @user_id0 ) AND ( `item_id` = CAST(@MethodConst1 AS CHAR)))   LIMIT 0,1 | 参数: [@user_id0=1, @MethodConst1=2018031001]
[2025-08-12 22:43:39.994 +08:00 DBG] SQL执行完成: SELECT `id`,`user_id`,`item_id`,`item_count`,`item_pos`,`item_seq`,`create_time` FROM `user_item`   WHERE (( `user_id` = @user_id0 ) AND ( `item_id` = CAST(@MethodConst1 AS CHAR)))   LIMIT 0,1
[2025-08-12 22:43:39.997 +08:00 DBG] 执行SQL: SELECT MAX(`item_seq`) FROM `user_item`  WHERE ( `user_id` = @user_id0 )  | 参数: [@user_id0=1]
[2025-08-12 22:43:40.032 +08:00 DBG] SQL执行完成: SELECT MAX(`item_seq`) FROM `user_item`  WHERE ( `user_id` = @user_id0 ) 
[2025-08-12 22:43:40.035 +08:00 DBG] 执行SQL: INSERT INTO `user_item`  
           (`id`,`user_id`,`item_id`,`item_count`,`item_pos`,`item_seq`,`create_time`)
     VALUES
           (@id,@user_id,@item_id,@item_count,@item_pos,@item_seq,@create_time) ; | 参数: [@id=0, @user_id=1, @item_id=2018031001, @item_count=1000, @item_pos=1, @item_seq=15, @create_time=]
[2025-08-12 22:43:40.069 +08:00 ERR] 数据库操作发生错误: Duplicate entry '0' for key 'PRIMARY'
SqlSugar.SqlSugarException: Duplicate entry '0' for key 'PRIMARY'
[2025-08-12 22:45:22.963 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-08-12 22:45:22.966 +08:00 INF] 开始测试数据库连接...
[2025-08-12 22:45:22.972 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-12 22:45:23.026 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-12 22:45:23.028 +08:00 INF] ✅ 数据库连接测试成功
[2025-08-12 22:45:28.089 +08:00 DBG] 执行SQL: SELECT 1 FROM `user`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=1]
[2025-08-12 22:45:28.132 +08:00 DBG] SQL执行完成: SELECT 1 FROM `user`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-08-12 22:45:29.312 +08:00 DBG] 执行SQL: SELECT 1 FROM `item_config`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1 | 参数: [@item_no0=2018031001]
[2025-08-12 22:45:29.360 +08:00 DBG] SQL执行完成: SELECT 1 FROM `item_config`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1
[2025-08-12 22:45:34.369 +08:00 DBG] 执行SQL: SELECT `id`,`user_id`,`item_id`,`item_count`,`item_pos`,`item_seq`,`create_time` FROM `user_item`   WHERE (( `user_id` = @user_id0 ) AND ( `item_id` = CAST(@MethodConst1 AS CHAR)))   LIMIT 0,1 | 参数: [@user_id0=1, @MethodConst1=2018031001]
[2025-08-12 22:45:34.398 +08:00 DBG] SQL执行完成: SELECT `id`,`user_id`,`item_id`,`item_count`,`item_pos`,`item_seq`,`create_time` FROM `user_item`   WHERE (( `user_id` = @user_id0 ) AND ( `item_id` = CAST(@MethodConst1 AS CHAR)))   LIMIT 0,1
[2025-08-12 22:45:43.533 +08:00 DBG] 执行SQL: SELECT MAX(`item_seq`) FROM `user_item`  WHERE ( `user_id` = @user_id0 )  | 参数: [@user_id0=1]
[2025-08-12 22:45:43.566 +08:00 DBG] SQL执行完成: SELECT MAX(`item_seq`) FROM `user_item`  WHERE ( `user_id` = @user_id0 ) 
[2025-08-12 22:46:42.131 +08:00 DBG] 执行SQL: INSERT INTO `user_item`  
           (`id`,`user_id`,`item_id`,`item_count`,`item_pos`,`item_seq`,`create_time`)
     VALUES
           (@id,@user_id,@item_id,@item_count,@item_pos,@item_seq,@create_time) ; | 参数: [@id=0, @user_id=1, @item_id=2018031001, @item_count=1000, @item_pos=1, @item_seq=15, @create_time=]
[2025-08-12 22:46:42.178 +08:00 ERR] 数据库操作发生错误: Duplicate entry '0' for key 'PRIMARY'
SqlSugar.SqlSugarException: Duplicate entry '0' for key 'PRIMARY'
[2025-08-12 22:47:39.551 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-08-12 22:47:39.703 +08:00 INF] 开始测试数据库连接...
[2025-08-12 22:47:39.771 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-12 22:47:39.831 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-12 22:47:39.900 +08:00 INF] ✅ 数据库连接测试成功
[2025-08-12 22:47:41.763 +08:00 DBG] 执行SQL: SELECT 1 FROM `user`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=1]
[2025-08-12 22:47:41.789 +08:00 DBG] SQL执行完成: SELECT 1 FROM `user`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-08-12 22:47:41.791 +08:00 DBG] 执行SQL: SELECT 1 FROM `item_config`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1 | 参数: [@item_no0=231601]
[2025-08-12 22:47:41.836 +08:00 DBG] SQL执行完成: SELECT 1 FROM `item_config`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1
[2025-08-12 22:47:41.840 +08:00 DBG] 执行SQL: SELECT `id`,`user_id`,`item_id`,`item_count`,`item_pos`,`item_seq`,`create_time` FROM `user_item`   WHERE (( `user_id` = @user_id0 ) AND ( `item_id` = CAST(@MethodConst1 AS CHAR)))   LIMIT 0,1 | 参数: [@user_id0=1, @MethodConst1=231601]
[2025-08-12 22:47:41.874 +08:00 DBG] SQL执行完成: SELECT `id`,`user_id`,`item_id`,`item_count`,`item_pos`,`item_seq`,`create_time` FROM `user_item`   WHERE (( `user_id` = @user_id0 ) AND ( `item_id` = CAST(@MethodConst1 AS CHAR)))   LIMIT 0,1
[2025-08-12 22:47:44.600 +08:00 DBG] 执行SQL: SELECT MAX(`item_seq`) FROM `user_item`  WHERE ( `user_id` = @user_id0 )  | 参数: [@user_id0=1]
[2025-08-12 22:47:44.640 +08:00 DBG] SQL执行完成: SELECT MAX(`item_seq`) FROM `user_item`  WHERE ( `user_id` = @user_id0 ) 
[2025-08-12 22:47:49.375 +08:00 DBG] 执行SQL: INSERT INTO `user_item`  
           (`id`,`user_id`,`item_id`,`item_count`,`item_pos`,`item_seq`,`create_time`)
     VALUES
           (@id,@user_id,@item_id,@item_count,@item_pos,@item_seq,@create_time) ; | 参数: [@id=0, @user_id=1, @item_id=231601, @item_count=100, @item_pos=1, @item_seq=15, @create_time=]
[2025-08-12 22:47:49.445 +08:00 ERR] 数据库操作发生错误: Duplicate entry '0' for key 'PRIMARY'
SqlSugar.SqlSugarException: Duplicate entry '0' for key 'PRIMARY'
[2025-08-12 22:50:43.919 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-08-12 22:50:43.921 +08:00 INF] 开始测试数据库连接...
[2025-08-12 22:50:43.923 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-12 22:50:44.177 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-12 22:50:44.199 +08:00 INF] ✅ 数据库连接测试成功
[2025-08-12 22:50:45.761 +08:00 DBG] 执行SQL: SELECT 1 FROM `user`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=1]
[2025-08-12 22:50:45.801 +08:00 DBG] SQL执行完成: SELECT 1 FROM `user`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-08-12 22:50:45.803 +08:00 DBG] 执行SQL: SELECT 1 FROM `item_config`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1 | 参数: [@item_no0=231601]
[2025-08-12 22:50:45.836 +08:00 DBG] SQL执行完成: SELECT 1 FROM `item_config`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1
[2025-08-12 22:50:45.838 +08:00 DBG] 执行SQL: SELECT `id`,`user_id`,`item_id`,`item_count`,`item_pos`,`item_seq`,`create_time` FROM `user_item`   WHERE (( `user_id` = @user_id0 ) AND ( `item_id` = CAST(@MethodConst1 AS CHAR)))   LIMIT 0,1 | 参数: [@user_id0=1, @MethodConst1=231601]
[2025-08-12 22:50:45.897 +08:00 DBG] SQL执行完成: SELECT `id`,`user_id`,`item_id`,`item_count`,`item_pos`,`item_seq`,`create_time` FROM `user_item`   WHERE (( `user_id` = @user_id0 ) AND ( `item_id` = CAST(@MethodConst1 AS CHAR)))   LIMIT 0,1
[2025-08-12 22:50:47.903 +08:00 DBG] 执行SQL: SELECT MAX(`item_seq`) FROM `user_item`  WHERE ( `user_id` = @user_id0 )  | 参数: [@user_id0=1]
[2025-08-12 22:50:47.956 +08:00 DBG] SQL执行完成: SELECT MAX(`item_seq`) FROM `user_item`  WHERE ( `user_id` = @user_id0 ) 
[2025-08-12 22:50:49.207 +08:00 DBG] 执行SQL: INSERT INTO `user_item`  
           (`id`,`user_id`,`item_id`,`item_count`,`item_pos`,`item_seq`,`create_time`)
     VALUES
           (@id,@user_id,@item_id,@item_count,@item_pos,@item_seq,@create_time) ; | 参数: [@id=0, @user_id=1, @item_id=231601, @item_count=100, @item_pos=1, @item_seq=1, @create_time=]
[2025-08-12 22:50:49.302 +08:00 DBG] SQL执行完成: INSERT INTO `user_item`  
           (`id`,`user_id`,`item_id`,`item_count`,`item_pos`,`item_seq`,`create_time`)
     VALUES
           (@id,@user_id,@item_id,@item_count,@item_pos,@item_seq,@create_time) ;
[2025-08-12 22:50:52.211 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-08-12 22:50:52.211 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-08-12 22:50:52.212 +08:00 INF] 开始测试数据库连接...
[2025-08-12 22:50:52.213 +08:00 INF] 开始测试数据库连接...
[2025-08-12 22:50:52.215 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-12 22:50:52.215 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-12 22:50:52.276 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-12 22:50:52.280 +08:00 INF] ✅ 数据库连接测试成功
[2025-08-12 22:50:52.283 +08:00 DBG] 执行SQL: SELECT COUNT(1) FROM `user_item` `ui` Left JOIN `user` `u` ON ( `ui`.`user_id` = `u`.`id` )  Left JOIN `item_config` `ic` ON ( `ui`.`item_id` = CAST(`ic`.`item_no` AS CHAR))   
[2025-08-12 22:50:52.561 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-12 22:50:52.567 +08:00 INF] ✅ 数据库连接测试成功
[2025-08-12 22:50:52.570 +08:00 DBG] 执行SQL: SELECT SUM(`item_count`) FROM `user_item`  
[2025-08-12 22:50:52.613 +08:00 DBG] SQL执行完成: SELECT COUNT(1) FROM `user_item` `ui` Left JOIN `user` `u` ON ( `ui`.`user_id` = `u`.`id` )  Left JOIN `item_config` `ic` ON ( `ui`.`item_id` = CAST(`ic`.`item_no` AS CHAR))   
[2025-08-12 22:50:52.618 +08:00 DBG] 执行SQL: SELECT  `ui`.`id` AS `Id` , `ui`.`user_id` AS `UserId` , IFNULL(`u`.`username`,@MethodConst2) AS `Username` , `ui`.`item_id` AS `ItemId` , CAST(`ui`.`item_id` AS SIGNED) AS `ItemNo` , IFNULL(`ic`.`name`,@MethodConst3) AS `ItemName` , `ic`.`type` AS `ItemType` , `ic`.`quality` AS `ItemQuality` , `ui`.`item_count` AS `ItemCount` , `ui`.`item_pos` AS `ItemPos` , `ui`.`item_seq` AS `ItemSeq` , `ic`.`description` AS `ItemDescription` , `ic`.`price` AS `ItemPrice` , `ic`.`icon` AS `ItemIcon`  FROM `user_item` `ui` Left JOIN `user` `u` ON ( `ui`.`user_id` = `u`.`id` )  Left JOIN `item_config` `ic` ON ( `ui`.`item_id` = CAST(`ic`.`item_no` AS CHAR))     ORDER BY `ui`.`id` DESC LIMIT 0,15 | 参数: [@MethodConst0=, @MethodConst1=, @MethodConst2=, @MethodConst3=]
[2025-08-12 22:50:52.665 +08:00 DBG] SQL执行完成: SELECT SUM(`item_count`) FROM `user_item`  
[2025-08-12 22:50:52.665 +08:00 DBG] SQL执行完成: SELECT  `ui`.`id` AS `Id` , `ui`.`user_id` AS `UserId` , IFNULL(`u`.`username`,@MethodConst2) AS `Username` , `ui`.`item_id` AS `ItemId` , CAST(`ui`.`item_id` AS SIGNED) AS `ItemNo` , IFNULL(`ic`.`name`,@MethodConst3) AS `ItemName` , `ic`.`type` AS `ItemType` , `ic`.`quality` AS `ItemQuality` , `ui`.`item_count` AS `ItemCount` , `ui`.`item_pos` AS `ItemPos` , `ui`.`item_seq` AS `ItemSeq` , `ic`.`description` AS `ItemDescription` , `ic`.`price` AS `ItemPrice` , `ic`.`icon` AS `ItemIcon`  FROM `user_item` `ui` Left JOIN `user` `u` ON ( `ui`.`user_id` = `u`.`id` )  Left JOIN `item_config` `ic` ON ( `ui`.`item_id` = CAST(`ic`.`item_no` AS CHAR))     ORDER BY `ui`.`id` DESC LIMIT 0,15
[2025-08-12 22:50:52.667 +08:00 DBG] 执行SQL: SELECT COUNT(1) FROM `user_item` GROUP BY `item_id`  
[2025-08-12 22:50:52.702 +08:00 DBG] SQL执行完成: SELECT COUNT(1) FROM `user_item` GROUP BY `item_id`  
[2025-08-12 22:50:52.706 +08:00 DBG] 执行SQL: SELECT  `ic`.`quality` AS `Quality` , SUM(`ui`.`item_count`) AS `Count`  FROM `user_item` `ui` Left JOIN `item_config` `ic` ON ( `ui`.`item_id` = CAST(`ic`.`item_no` AS CHAR))  GROUP BY `ic`.`quality`  
[2025-08-12 22:50:52.795 +08:00 DBG] SQL执行完成: SELECT  `ic`.`quality` AS `Quality` , SUM(`ui`.`item_count`) AS `Count`  FROM `user_item` `ui` Left JOIN `item_config` `ic` ON ( `ui`.`item_id` = CAST(`ic`.`item_no` AS CHAR))  GROUP BY `ic`.`quality`  
[2025-08-12 22:50:52.798 +08:00 DBG] 执行SQL: SELECT SUM(( `ui`.`item_count` *IFNULL(`ic`.`price`,@MethodConst0))) FROM `user_item` `ui` Left JOIN `item_config` `ic` ON ( `ui`.`item_id` = CAST(`ic`.`item_no` AS CHAR))    | 参数: [@MethodConst0=0]
[2025-08-12 22:50:52.878 +08:00 DBG] SQL执行完成: SELECT SUM(( `ui`.`item_count` *IFNULL(`ic`.`price`,@MethodConst0))) FROM `user_item` `ui` Left JOIN `item_config` `ic` ON ( `ui`.`item_id` = CAST(`ic`.`item_no` AS CHAR))   
[2025-08-12 22:51:34.544 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-08-12 22:51:34.562 +08:00 INF] 开始测试数据库连接...
[2025-08-12 22:51:34.603 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-12 22:51:34.687 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-12 22:51:34.689 +08:00 INF] ✅ 数据库连接测试成功
[2025-08-12 22:51:38.032 +08:00 DBG] 执行SQL: SELECT 1 FROM `user`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=1]
[2025-08-12 22:51:38.069 +08:00 DBG] SQL执行完成: SELECT 1 FROM `user`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-08-12 22:51:38.072 +08:00 DBG] 执行SQL: SELECT 1 FROM `item_config`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1 | 参数: [@item_no0=2018031001]
[2025-08-12 22:51:38.112 +08:00 DBG] SQL执行完成: SELECT 1 FROM `item_config`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1
[2025-08-12 22:51:38.117 +08:00 DBG] 执行SQL: SELECT `id`,`user_id`,`item_id`,`item_count`,`item_pos`,`item_seq`,`create_time` FROM `user_item`   WHERE (( `user_id` = @user_id0 ) AND ( `item_id` = CAST(@MethodConst1 AS CHAR)))   LIMIT 0,1 | 参数: [@user_id0=1, @MethodConst1=2018031001]
[2025-08-12 22:51:38.152 +08:00 DBG] SQL执行完成: SELECT `id`,`user_id`,`item_id`,`item_count`,`item_pos`,`item_seq`,`create_time` FROM `user_item`   WHERE (( `user_id` = @user_id0 ) AND ( `item_id` = CAST(@MethodConst1 AS CHAR)))   LIMIT 0,1
[2025-08-12 22:51:40.303 +08:00 DBG] 执行SQL: SELECT MAX(`item_seq`) FROM `user_item`  WHERE ( `user_id` = @user_id0 )  | 参数: [@user_id0=1]
[2025-08-12 22:51:40.355 +08:00 DBG] SQL执行完成: SELECT MAX(`item_seq`) FROM `user_item`  WHERE ( `user_id` = @user_id0 ) 
[2025-08-12 22:51:40.358 +08:00 DBG] 执行SQL: INSERT INTO `user_item`  
           (`id`,`user_id`,`item_id`,`item_count`,`item_pos`,`item_seq`,`create_time`)
     VALUES
           (@id,@user_id,@item_id,@item_count,@item_pos,@item_seq,@create_time) ; | 参数: [@id=0, @user_id=1, @item_id=2018031001, @item_count=1000, @item_pos=1, @item_seq=2, @create_time=]
[2025-08-12 22:51:40.471 +08:00 DBG] SQL执行完成: INSERT INTO `user_item`  
           (`id`,`user_id`,`item_id`,`item_count`,`item_pos`,`item_seq`,`create_time`)
     VALUES
           (@id,@user_id,@item_id,@item_count,@item_pos,@item_seq,@create_time) ;
[2025-08-12 22:51:42.319 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-08-12 22:51:42.321 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-08-12 22:51:42.329 +08:00 INF] 开始测试数据库连接...
[2025-08-12 22:51:42.330 +08:00 INF] 开始测试数据库连接...
[2025-08-12 22:51:42.332 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-12 22:51:42.334 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-12 22:51:42.370 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-12 22:51:42.371 +08:00 INF] ✅ 数据库连接测试成功
[2025-08-12 22:51:42.373 +08:00 DBG] 执行SQL: SELECT COUNT(1) FROM `user_item` `ui` Left JOIN `user` `u` ON ( `ui`.`user_id` = `u`.`id` )  Left JOIN `item_config` `ic` ON ( `ui`.`item_id` = CAST(`ic`.`item_no` AS CHAR))   
[2025-08-12 22:51:42.384 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-12 22:51:42.389 +08:00 INF] ✅ 数据库连接测试成功
[2025-08-12 22:51:42.391 +08:00 DBG] 执行SQL: SELECT SUM(`item_count`) FROM `user_item`  
[2025-08-12 22:51:42.419 +08:00 DBG] SQL执行完成: SELECT COUNT(1) FROM `user_item` `ui` Left JOIN `user` `u` ON ( `ui`.`user_id` = `u`.`id` )  Left JOIN `item_config` `ic` ON ( `ui`.`item_id` = CAST(`ic`.`item_no` AS CHAR))   
[2025-08-12 22:51:42.425 +08:00 DBG] 执行SQL: SELECT  `ui`.`id` AS `Id` , `ui`.`user_id` AS `UserId` , IFNULL(`u`.`username`,@MethodConst2) AS `Username` , `ui`.`item_id` AS `ItemId` , CAST(`ui`.`item_id` AS SIGNED) AS `ItemNo` , IFNULL(`ic`.`name`,@MethodConst3) AS `ItemName` , `ic`.`type` AS `ItemType` , `ic`.`quality` AS `ItemQuality` , `ui`.`item_count` AS `ItemCount` , `ui`.`item_pos` AS `ItemPos` , `ui`.`item_seq` AS `ItemSeq` , `ic`.`description` AS `ItemDescription` , `ic`.`price` AS `ItemPrice` , `ic`.`icon` AS `ItemIcon`  FROM `user_item` `ui` Left JOIN `user` `u` ON ( `ui`.`user_id` = `u`.`id` )  Left JOIN `item_config` `ic` ON ( `ui`.`item_id` = CAST(`ic`.`item_no` AS CHAR))     ORDER BY `ui`.`id` DESC LIMIT 0,15 | 参数: [@MethodConst0=, @MethodConst1=, @MethodConst2=, @MethodConst3=]
[2025-08-12 22:51:42.446 +08:00 DBG] SQL执行完成: SELECT SUM(`item_count`) FROM `user_item`  
[2025-08-12 22:51:42.453 +08:00 DBG] 执行SQL: SELECT COUNT(1) FROM `user_item` GROUP BY `item_id`  
[2025-08-12 22:51:42.472 +08:00 DBG] SQL执行完成: SELECT  `ui`.`id` AS `Id` , `ui`.`user_id` AS `UserId` , IFNULL(`u`.`username`,@MethodConst2) AS `Username` , `ui`.`item_id` AS `ItemId` , CAST(`ui`.`item_id` AS SIGNED) AS `ItemNo` , IFNULL(`ic`.`name`,@MethodConst3) AS `ItemName` , `ic`.`type` AS `ItemType` , `ic`.`quality` AS `ItemQuality` , `ui`.`item_count` AS `ItemCount` , `ui`.`item_pos` AS `ItemPos` , `ui`.`item_seq` AS `ItemSeq` , `ic`.`description` AS `ItemDescription` , `ic`.`price` AS `ItemPrice` , `ic`.`icon` AS `ItemIcon`  FROM `user_item` `ui` Left JOIN `user` `u` ON ( `ui`.`user_id` = `u`.`id` )  Left JOIN `item_config` `ic` ON ( `ui`.`item_id` = CAST(`ic`.`item_no` AS CHAR))     ORDER BY `ui`.`id` DESC LIMIT 0,15
[2025-08-12 22:51:42.502 +08:00 DBG] SQL执行完成: SELECT COUNT(1) FROM `user_item` GROUP BY `item_id`  
[2025-08-12 22:51:42.505 +08:00 DBG] 执行SQL: SELECT  `ic`.`quality` AS `Quality` , SUM(`ui`.`item_count`) AS `Count`  FROM `user_item` `ui` Left JOIN `item_config` `ic` ON ( `ui`.`item_id` = CAST(`ic`.`item_no` AS CHAR))  GROUP BY `ic`.`quality`  
[2025-08-12 22:51:42.565 +08:00 DBG] SQL执行完成: SELECT  `ic`.`quality` AS `Quality` , SUM(`ui`.`item_count`) AS `Count`  FROM `user_item` `ui` Left JOIN `item_config` `ic` ON ( `ui`.`item_id` = CAST(`ic`.`item_no` AS CHAR))  GROUP BY `ic`.`quality`  
[2025-08-12 22:51:42.574 +08:00 DBG] 执行SQL: SELECT SUM(( `ui`.`item_count` *IFNULL(`ic`.`price`,@MethodConst0))) FROM `user_item` `ui` Left JOIN `item_config` `ic` ON ( `ui`.`item_id` = CAST(`ic`.`item_no` AS CHAR))    | 参数: [@MethodConst0=0]
[2025-08-12 22:51:42.642 +08:00 DBG] SQL执行完成: SELECT SUM(( `ui`.`item_count` *IFNULL(`ic`.`price`,@MethodConst0))) FROM `user_item` `ui` Left JOIN `item_config` `ic` ON ( `ui`.`item_id` = CAST(`ic`.`item_no` AS CHAR))   
[2025-08-12 22:52:55.328 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-08-12 22:52:55.346 +08:00 INF] 开始测试数据库连接...
[2025-08-12 22:52:55.349 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-12 22:52:55.412 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-12 22:52:55.415 +08:00 INF] ✅ 数据库连接测试成功
[2025-08-12 22:52:55.417 +08:00 DBG] 执行SQL: SELECT 1 FROM `user`   WHERE ( `id` = @id0 )   LIMIT 0,1 | 参数: [@id0=1]
[2025-08-12 22:52:55.503 +08:00 DBG] SQL执行完成: SELECT 1 FROM `user`   WHERE ( `id` = @id0 )   LIMIT 0,1
[2025-08-12 22:52:55.509 +08:00 DBG] 执行SQL: SELECT 1 FROM `item_config`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1 | 参数: [@item_no0=2019020101]
[2025-08-12 22:52:55.575 +08:00 DBG] SQL执行完成: SELECT 1 FROM `item_config`   WHERE ( `item_no` = @item_no0 )   LIMIT 0,1
[2025-08-12 22:52:55.576 +08:00 DBG] 执行SQL: SELECT `id`,`user_id`,`item_id`,`item_count`,`item_pos`,`item_seq`,`create_time` FROM `user_item`   WHERE (( `user_id` = @user_id0 ) AND ( `item_id` = CAST(@MethodConst1 AS CHAR)))   LIMIT 0,1 | 参数: [@user_id0=1, @MethodConst1=2019020101]
[2025-08-12 22:52:55.640 +08:00 DBG] SQL执行完成: SELECT `id`,`user_id`,`item_id`,`item_count`,`item_pos`,`item_seq`,`create_time` FROM `user_item`   WHERE (( `user_id` = @user_id0 ) AND ( `item_id` = CAST(@MethodConst1 AS CHAR)))   LIMIT 0,1
[2025-08-12 22:52:55.642 +08:00 DBG] 执行SQL: SELECT MAX(`item_seq`) FROM `user_item`  WHERE ( `user_id` = @user_id0 )  | 参数: [@user_id0=1]
[2025-08-12 22:52:55.691 +08:00 DBG] SQL执行完成: SELECT MAX(`item_seq`) FROM `user_item`  WHERE ( `user_id` = @user_id0 ) 
[2025-08-12 22:52:55.695 +08:00 DBG] 执行SQL: INSERT INTO `user_item`  
           (`id`,`user_id`,`item_id`,`item_count`,`item_pos`,`item_seq`,`create_time`)
     VALUES
           (@id,@user_id,@item_id,@item_count,@item_pos,@item_seq,@create_time) ; | 参数: [@id=0, @user_id=1, @item_id=2019020101, @item_count=1000, @item_pos=1, @item_seq=3, @create_time=]
[2025-08-12 22:52:55.851 +08:00 DBG] SQL执行完成: INSERT INTO `user_item`  
           (`id`,`user_id`,`item_id`,`item_count`,`item_pos`,`item_seq`,`create_time`)
     VALUES
           (@id,@user_id,@item_id,@item_count,@item_pos,@item_seq,@create_time) ;
[2025-08-12 22:52:57.349 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-08-12 22:52:57.351 +08:00 INF] 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-08-12 22:52:57.352 +08:00 INF] 开始测试数据库连接...
[2025-08-12 22:52:57.353 +08:00 INF] 开始测试数据库连接...
[2025-08-12 22:52:57.355 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-12 22:52:57.355 +08:00 DBG] 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-12 22:52:57.390 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-12 22:52:57.391 +08:00 INF] ✅ 数据库连接测试成功
[2025-08-12 22:52:57.392 +08:00 DBG] 执行SQL: SELECT SUM(`item_count`) FROM `user_item`  
[2025-08-12 22:52:57.400 +08:00 DBG] SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-12 22:52:57.401 +08:00 INF] ✅ 数据库连接测试成功
[2025-08-12 22:52:57.405 +08:00 DBG] 执行SQL: SELECT COUNT(1) FROM `user_item` `ui` Left JOIN `user` `u` ON ( `ui`.`user_id` = `u`.`id` )  Left JOIN `item_config` `ic` ON ( `ui`.`item_id` = CAST(`ic`.`item_no` AS CHAR))   
[2025-08-12 22:52:57.440 +08:00 DBG] SQL执行完成: SELECT SUM(`item_count`) FROM `user_item`  
[2025-08-12 22:52:57.443 +08:00 DBG] 执行SQL: SELECT COUNT(1) FROM `user_item` GROUP BY `item_id`  
[2025-08-12 22:52:57.466 +08:00 DBG] SQL执行完成: SELECT COUNT(1) FROM `user_item` `ui` Left JOIN `user` `u` ON ( `ui`.`user_id` = `u`.`id` )  Left JOIN `item_config` `ic` ON ( `ui`.`item_id` = CAST(`ic`.`item_no` AS CHAR))   
[2025-08-12 22:52:57.468 +08:00 DBG] 执行SQL: SELECT  `ui`.`id` AS `Id` , `ui`.`user_id` AS `UserId` , IFNULL(`u`.`username`,@MethodConst2) AS `Username` , `ui`.`item_id` AS `ItemId` , CAST(`ui`.`item_id` AS SIGNED) AS `ItemNo` , IFNULL(`ic`.`name`,@MethodConst3) AS `ItemName` , `ic`.`type` AS `ItemType` , `ic`.`quality` AS `ItemQuality` , `ui`.`item_count` AS `ItemCount` , `ui`.`item_pos` AS `ItemPos` , `ui`.`item_seq` AS `ItemSeq` , `ic`.`description` AS `ItemDescription` , `ic`.`price` AS `ItemPrice` , `ic`.`icon` AS `ItemIcon`  FROM `user_item` `ui` Left JOIN `user` `u` ON ( `ui`.`user_id` = `u`.`id` )  Left JOIN `item_config` `ic` ON ( `ui`.`item_id` = CAST(`ic`.`item_no` AS CHAR))     ORDER BY `ui`.`id` DESC LIMIT 0,15 | 参数: [@MethodConst0=, @MethodConst1=, @MethodConst2=, @MethodConst3=]
[2025-08-12 22:52:57.484 +08:00 DBG] SQL执行完成: SELECT COUNT(1) FROM `user_item` GROUP BY `item_id`  
[2025-08-12 22:52:57.501 +08:00 DBG] 执行SQL: SELECT  `ic`.`quality` AS `Quality` , SUM(`ui`.`item_count`) AS `Count`  FROM `user_item` `ui` Left JOIN `item_config` `ic` ON ( `ui`.`item_id` = CAST(`ic`.`item_no` AS CHAR))  GROUP BY `ic`.`quality`  
[2025-08-12 22:52:57.523 +08:00 DBG] SQL执行完成: SELECT  `ui`.`id` AS `Id` , `ui`.`user_id` AS `UserId` , IFNULL(`u`.`username`,@MethodConst2) AS `Username` , `ui`.`item_id` AS `ItemId` , CAST(`ui`.`item_id` AS SIGNED) AS `ItemNo` , IFNULL(`ic`.`name`,@MethodConst3) AS `ItemName` , `ic`.`type` AS `ItemType` , `ic`.`quality` AS `ItemQuality` , `ui`.`item_count` AS `ItemCount` , `ui`.`item_pos` AS `ItemPos` , `ui`.`item_seq` AS `ItemSeq` , `ic`.`description` AS `ItemDescription` , `ic`.`price` AS `ItemPrice` , `ic`.`icon` AS `ItemIcon`  FROM `user_item` `ui` Left JOIN `user` `u` ON ( `ui`.`user_id` = `u`.`id` )  Left JOIN `item_config` `ic` ON ( `ui`.`item_id` = CAST(`ic`.`item_no` AS CHAR))     ORDER BY `ui`.`id` DESC LIMIT 0,15
[2025-08-12 22:52:57.544 +08:00 DBG] SQL执行完成: SELECT  `ic`.`quality` AS `Quality` , SUM(`ui`.`item_count`) AS `Count`  FROM `user_item` `ui` Left JOIN `item_config` `ic` ON ( `ui`.`item_id` = CAST(`ic`.`item_no` AS CHAR))  GROUP BY `ic`.`quality`  
[2025-08-12 22:52:57.547 +08:00 DBG] 执行SQL: SELECT SUM(( `ui`.`item_count` *IFNULL(`ic`.`price`,@MethodConst0))) FROM `user_item` `ui` Left JOIN `item_config` `ic` ON ( `ui`.`item_id` = CAST(`ic`.`item_no` AS CHAR))    | 参数: [@MethodConst0=0]
[2025-08-12 22:52:57.583 +08:00 DBG] SQL执行完成: SELECT SUM(( `ui`.`item_count` *IFNULL(`ic`.`price`,@MethodConst0))) FROM `user_item` `ui` Left JOIN `item_config` `ic` ON ( `ui`.`item_id` = CAST(`ic`.`item_no` AS CHAR))   
