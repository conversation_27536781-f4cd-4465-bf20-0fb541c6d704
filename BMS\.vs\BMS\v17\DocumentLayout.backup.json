{"Version": 1, "WorkspaceRootPath": "D:\\AI OB\\HM_one_bg\\BMS\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{75205788-6466-48DB-AC86-28EB0CF49CFC}|BMS.csproj|d:\\ai ob\\hm_one_bg\\bms\\services\\implementations\\taskconfigservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{75205788-6466-48DB-AC86-28EB0CF49CFC}|BMS.csproj|solutionrelative:services\\implementations\\taskconfigservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{75205788-6466-48DB-AC86-28EB0CF49CFC}|BMS.csproj|d:\\ai ob\\hm_one_bg\\bms\\services\\interfaces\\itaskconfigservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{75205788-6466-48DB-AC86-28EB0CF49CFC}|BMS.csproj|solutionrelative:services\\interfaces\\itaskconfigservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{75205788-6466-48DB-AC86-28EB0CF49CFC}|BMS.csproj|d:\\ai ob\\hm_one_bg\\bms\\models\\entities\\task_objective.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{75205788-6466-48DB-AC86-28EB0CF49CFC}|BMS.csproj|solutionrelative:models\\entities\\task_objective.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{75205788-6466-48DB-AC86-28EB0CF49CFC}|BMS.csproj|d:\\ai ob\\hm_one_bg\\bms\\views\\taskconfig\\index.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{75205788-6466-48DB-AC86-28EB0CF49CFC}|BMS.csproj|solutionrelative:views\\taskconfig\\index.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}"}, {"AbsoluteMoniker": "D:0:0:{75205788-6466-48DB-AC86-28EB0CF49CFC}|BMS.csproj|d:\\ai ob\\hm_one_bg\\bms\\views\\equipment\\index.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{75205788-6466-48DB-AC86-28EB0CF49CFC}|BMS.csproj|solutionrelative:views\\equipment\\index.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 2, "Children": [{"$type": "Bookmark", "Name": "ST:128:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Document", "DocumentIndex": 1, "Title": "ITaskConfigService.cs", "DocumentMoniker": "D:\\AI OB\\HM_one_bg\\BMS\\Services\\Interfaces\\ITaskConfigService.cs", "RelativeDocumentMoniker": "Services\\Interfaces\\ITaskConfigService.cs", "ToolTip": "D:\\AI OB\\HM_one_bg\\BMS\\Services\\Interfaces\\ITaskConfigService.cs", "RelativeToolTip": "Services\\Interfaces\\ITaskConfigService.cs", "ViewState": "AgIAADoAAAAAAAAAAAAAADoAAAA5AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-09-08T14:49:30.057Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 0, "Title": "TaskConfigService.cs", "DocumentMoniker": "D:\\AI OB\\HM_one_bg\\BMS\\Services\\Implementations\\TaskConfigService.cs", "RelativeDocumentMoniker": "Services\\Implementations\\TaskConfigService.cs", "ToolTip": "D:\\AI OB\\HM_one_bg\\BMS\\Services\\Implementations\\TaskConfigService.cs", "RelativeToolTip": "Services\\Implementations\\TaskConfigService.cs", "ViewState": "AgIAACABAAAAAAAAAAAAADUCAAA7AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-09-08T13:07:36.875Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "Index.cshtml", "DocumentMoniker": "D:\\AI OB\\HM_one_bg\\BMS\\Views\\TaskConfig\\Index.cshtml", "RelativeDocumentMoniker": "Views\\TaskConfig\\Index.cshtml", "ToolTip": "D:\\AI OB\\HM_one_bg\\BMS\\Views\\TaskConfig\\Index.cshtml", "RelativeToolTip": "Views\\TaskConfig\\Index.cshtml", "ViewState": "AgIAAHECAAAAAAAAAADgv4ACAAAfAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000759|", "WhenOpened": "2025-09-08T03:09:37.812Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "task_objective.cs", "DocumentMoniker": "D:\\AI OB\\HM_one_bg\\BMS\\Models\\Entities\\task_objective.cs", "RelativeDocumentMoniker": "Models\\Entities\\task_objective.cs", "ToolTip": "D:\\AI OB\\HM_one_bg\\BMS\\Models\\Entities\\task_objective.cs", "RelativeToolTip": "Models\\Entities\\task_objective.cs", "ViewState": "AgIAACIAAAAAAAAAAADwvzQAAAAgAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-09-07T16:24:19.729Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "Index.cshtml", "DocumentMoniker": "D:\\AI OB\\HM_one_bg\\BMS\\Views\\Equipment\\Index.cshtml", "RelativeDocumentMoniker": "Views\\Equipment\\Index.cshtml", "ToolTip": "D:\\AI OB\\HM_one_bg\\BMS\\Views\\Equipment\\Index.cshtml", "RelativeToolTip": "Views\\Equipment\\Index.cshtml", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAgAAAATAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000759|", "WhenOpened": "2025-09-07T14:09:29.854Z", "EditorCaption": ""}]}]}]}