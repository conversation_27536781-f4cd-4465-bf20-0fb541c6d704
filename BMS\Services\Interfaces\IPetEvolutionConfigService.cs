using BMS.Models.DTOs;
using BMS.Models.Common;

namespace BMS.Services.Interfaces
{
    /// <summary>
    /// 宠物进化配置服务接口
    /// </summary>
    public interface IPetEvolutionConfigService
    {
        /// <summary>
        /// 获取宠物进化配置列表（分页）
        /// </summary>
        /// <param name="queryDto">查询条件</param>
        /// <returns>分页结果</returns>
        Task<PagedResult<PetEvolutionConfigDetailDto>> GetPetEvolutionConfigsAsync(PetEvolutionConfigQueryDto queryDto);

        /// <summary>
        /// 根据ID获取宠物进化配置
        /// </summary>
        /// <param name="id">配置ID</param>
        /// <returns>配置详情</returns>
        Task<ApiResult<PetEvolutionConfigDetailDto>> GetByIdAsync(int id);

        /// <summary>
        /// 创建宠物进化配置
        /// </summary>
        /// <param name="createDto">创建DTO</param>
        /// <returns>操作结果</returns>
        Task<ApiResult<int>> CreateAsync(PetEvolutionConfigCreateDto createDto);

        /// <summary>
        /// 更新宠物进化配置
        /// </summary>
        /// <param name="updateDto">更新DTO</param>
        /// <returns>操作结果</returns>
        Task<ApiResult<bool>> UpdateAsync(PetEvolutionConfigUpdateDto updateDto);

        /// <summary>
        /// 删除宠物进化配置
        /// </summary>
        /// <param name="id">配置ID</param>
        /// <returns>操作结果</returns>
        Task<ApiResult<bool>> DeleteAsync(int id);

        /// <summary>
        /// 批量删除宠物进化配置
        /// </summary>
        /// <param name="ids">配置ID列表</param>
        /// <returns>操作结果</returns>
        Task<ApiResult<bool>> BatchDeleteAsync(List<int> ids);

        /// <summary>
        /// 切换激活状态
        /// </summary>
        /// <param name="id">配置ID</param>
        /// <param name="isActive">是否激活</param>
        /// <returns>操作结果</returns>
        Task<ApiResult<bool>> ToggleActiveAsync(int id, bool isActive);

        /// <summary>
        /// 检查进化配置是否存在
        /// </summary>
        /// <param name="petNo">宠物编号</param>
        /// <param name="evolutionType">进化类型</param>
        /// <param name="excludeId">排除的ID</param>
        /// <returns>是否存在</returns>
        Task<bool> CheckEvolutionConfigExistsAsync(int petNo, string evolutionType, int? excludeId = null);

        /// <summary>
        /// 获取宠物配置选项
        /// </summary>
        /// <returns>宠物配置选项列表</returns>
        Task<List<PetConfigOptionDto>> GetPetConfigOptionsAsync();

        /// <summary>
        /// 获取道具配置选项
        /// </summary>
        /// <returns>道具配置选项列表</returns>
        Task<List<ItemConfigOptionDto>> GetItemConfigOptionsAsync();

        /// <summary>
        /// 根据宠物编号获取进化配置
        /// </summary>
        /// <param name="petNo">宠物编号</param>
        /// <returns>进化配置列表</returns>
        Task<List<PetEvolutionConfigDetailDto>> GetEvolutionConfigsByPetNoAsync(int petNo);

        /// <summary>
        /// 验证进化条件
        /// </summary>
        /// <param name="userPetId">用户宠物ID</param>
        /// <param name="evolutionType">进化类型</param>
        /// <returns>验证结果</returns>
        Task<ApiResult<bool>> ValidateEvolutionConditionsAsync(int userPetId, string evolutionType);
    }
}
