using BMS.Models.DTOs;
using BMS.Models.Common;
using BMS.Models.Entities;
using BMS.Services.Interfaces;
using SqlSugar;

namespace BMS.Services.Implementations
{
    /// <summary>
    /// 境界服务实现
    /// </summary>
    public class RealmService : IRealmService
    {
        private readonly IDbService _dbService;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="dbService">数据库服务</param>
        public RealmService(IDbService dbService)
        {
            _dbService = dbService;
        }

        /// <summary>
        /// 分页查询境界列表
        /// </summary>
        /// <param name="queryDto">查询条件</param>
        /// <returns>分页结果</returns>
        public async Task<PagedResult<RealmDto>> GetRealmsAsync(RealmQueryDto queryDto)
        {
            try
            {
                // 构建查询条件
                var query =  _dbService.Queryable<realm_config>();

                // 根据境界ID筛选
                if (queryDto.RealmId.HasValue)
                {
                    query = query.Where(r => r.realm_id == queryDto.RealmId.Value);
                }

                // 根据境界名称筛选
                if (!string.IsNullOrWhiteSpace(queryDto.RealmName))
                {
                    query = query.Where(r => r.realm_name.Contains(queryDto.RealmName));
                }

                // 获取总数
                var totalCount = await query.CountAsync();

                // 分页查询
                var realms = await query
                    .OrderBy(r => r.realm_id)
                    .ToPageListAsync(queryDto.Page, queryDto.PageSize);

                // 转换为DTO并获取关联宠物数量
                var realmDtos = new List<RealmDto>();
                foreach (var realm in realms)
                {
                    var petCount = await GetPetCountByRealmAsync(realm.realm_name);
                    realmDtos.Add(new RealmDto
                    {
                        Id = realm.id,
                        RealmId = realm.realm_id,
                        RealmName = realm.realm_name,
                        PetCount = petCount
                    });
                }

                return new PagedResult<RealmDto>(realmDtos, queryDto.Page, queryDto.PageSize, totalCount);
            }
            catch (Exception ex)
            {
                throw new Exception($"查询境界列表失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 根据境界ID获取境界信息
        /// </summary>
        /// <param name="realmId">境界ID</param>
        /// <returns>境界信息</returns>
        public async Task<RealmDto?> GetRealmByIdAsync(int realmId)
        {
            try
            {
                var realm = await  _dbService.Queryable<realm_config>()
                    .Where(r => r.realm_id == realmId)
                    .FirstAsync();

                if (realm == null)
                {
                    return null;
                }

                var petCount = await GetPetCountByRealmAsync(realm.realm_name);

                return new RealmDto
                {
                    Id = realm.id,
                    RealmId = realm.realm_id,
                    RealmName = realm.realm_name,
                    PetCount = petCount
                };
            }
            catch (Exception ex)
            {
                throw new Exception($"获取境界信息失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 创建境界
        /// </summary>
        /// <param name="createDto">创建DTO</param>
        /// <returns>操作结果</returns>
        public async Task<ApiResult<RealmDto>> CreateRealmAsync(RealmCreateDto createDto)
        {
            try
            {
                // 检查境界ID是否已存在
                var exists = await ExistsAsync(createDto.RealmId);
                if (exists)
                {
                    return ApiResult<RealmDto>.Fail($"境界ID {createDto.RealmId} 已存在");
                }

                // 创建境界实体
                var realm = new realm_config
                {
                    realm_id = createDto.RealmId,
                    realm_name = createDto.RealmName
                };

                // 插入数据库
                var result = await  _dbService.Insertable(realm).ExecuteReturnEntityAsync();

                // 返回结果
                var realmDto = new RealmDto
                {
                    Id = result.id,
                    RealmId = result.realm_id,
                    RealmName = result.realm_name,
                    PetCount = 0
                };

                return ApiResult<RealmDto>.Ok(realmDto, "境界创建成功");
            }
            catch (Exception ex)
            {
                return ApiResult<RealmDto>.Fail($"创建境界失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 更新境界
        /// </summary>
        /// <param name="updateDto">更新DTO</param>
        /// <returns>操作结果</returns>
        public async Task<ApiResult<RealmDto>> UpdateRealmAsync(RealmUpdateDto updateDto)
        {
            try
            {
                // 检查境界是否存在
                var realm = await  _dbService.Queryable<realm_config>()
                    .Where(r => r.realm_id == updateDto.RealmId)
                    .FirstAsync();

                if (realm == null)
                {
                    return ApiResult<RealmDto>.Fail($"境界ID {updateDto.RealmId} 不存在");
                }

                // 更新境界信息
                realm.realm_name = updateDto.RealmName;

                var affectedRows = await  _dbService.Updateable(realm).ExecuteCommandAsync();
                if (affectedRows <= 0)
                {
                    return ApiResult<RealmDto>.Fail("更新境界失败");
                }

                // 获取宠物数量
                var petCount = await GetPetCountByRealmAsync(realm.realm_name);

                // 返回结果
                var realmDto = new RealmDto
                {
                    Id = realm.id,
                    RealmId = realm.realm_id,
                    RealmName = realm.realm_name,
                    PetCount = petCount
                };

                return ApiResult<RealmDto>.Ok(realmDto, "境界更新成功");
            }
            catch (Exception ex)
            {
                return ApiResult<RealmDto>.Fail($"更新境界失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 删除境界
        /// </summary>
        /// <param name="deleteDto">删除DTO</param>
        /// <returns>操作结果</returns>
        public async Task<ApiResult<bool>> DeleteRealmAsync(RealmDeleteDto deleteDto)
        {
            try
            {
                // 检查境界是否存在
                var realm = await  _dbService.Queryable<realm_config>()
                    .Where(r => r.realm_id == deleteDto.RealmId)
                    .FirstAsync();

                if (realm == null)
                {
                    return ApiResult<bool>.Fail($"境界ID {deleteDto.RealmId} 不存在");
                }

                // 检查是否有宠物使用该境界
                var petCount = await GetPetCountByRealmAsync(realm.realm_name);
                if (petCount > 0)
                {
                    return ApiResult<bool>.Fail($"该境界下还有 {petCount} 个宠物，无法删除");
                }

                // 删除境界
                var affectedRows = await  _dbService.Deleteable<realm_config>()
                    .Where(r => r.realm_id == deleteDto.RealmId)
                    .ExecuteCommandAsync();

                if (affectedRows <= 0)
                {
                    return ApiResult<bool>.Fail("删除境界失败");
                }

                return ApiResult<bool>.Ok(true, "境界删除成功");
            }
            catch (Exception ex)
            {
                return ApiResult<bool>.Fail($"删除境界失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 检查境界ID是否存在
        /// </summary>
        /// <param name="realmId">境界ID</param>
        /// <returns>是否存在</returns>
        public async Task<bool> ExistsAsync(int realmId)
        {
            try
            {
                return await  _dbService.Queryable<realm_config>()
                    .Where(r => r.realm_id == realmId)
                    .AnyAsync();
            }
            catch (Exception ex)
            {
                throw new Exception($"检查境界是否存在失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 获取境界下的宠物数量
        /// </summary>
        /// <param name="realmName">境界名称</param>
        /// <returns>宠物数量</returns>
        public async Task<int> GetPetCountByRealmAsync(string realmName)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(realmName))
                {
                    return 0;
                }

                return await  _dbService.Queryable<user_pet>()
                    .Where(p => p.realm == realmName)
                    .CountAsync();
            }
            catch (Exception ex)
            {
                throw new Exception($"获取境界宠物数量失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 获取所有境界的下拉选项
        /// </summary>
        /// <returns>境界选项列表</returns>
        public async Task<List<RealmDto>> GetRealmOptionsAsync()
        {
            try
            {
                var realms = await  _dbService.Queryable<realm_config>()
                    .OrderBy(r => r.realm_id)
                    .ToListAsync();

                return realms.Select(r => new RealmDto
                {
                    Id = r.id,
                    RealmId = r.realm_id,
                    RealmName = r.realm_name,
                    PetCount = 0 // 下拉选项不需要宠物数量
                }).ToList();
            }
            catch (Exception ex)
            {
                throw new Exception($"获取境界选项失败: {ex.Message}", ex);
            }
        }
    }
}