-- 为task_objective表添加target_name字段
-- 执行前请备份数据库

-- 检查表是否存在
SELECT COUNT(*) as table_exists FROM information_schema.tables 
WHERE table_schema = DATABASE() AND table_name = 'task_objective';

-- 添加target_name字段（如果不存在）
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM information_schema.columns 
     WHERE table_schema = DATABASE() 
     AND table_name = 'task_objective' 
     AND column_name = 'target_name') = 0,
    'ALTER TABLE task_objective ADD COLUMN target_name VARCHAR(200) DEFAULT NULL COMMENT ''目标名称'' AFTER target_id',
    'SELECT ''target_name字段已存在'' as message'
));

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加complete_template字段（如果不存在）
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM information_schema.columns 
     WHERE table_schema = DATABASE() 
     AND table_name = 'task_objective' 
     AND column_name = 'complete_template') = 0,
    'ALTER TABLE task_objective ADD COLUMN complete_template VARCHAR(100) DEFAULT NULL COMMENT ''进度模版'' AFTER objective_description',
    'SELECT ''complete_template字段已存在'' as message'
));

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 查看表结构
DESCRIBE task_objective;

-- 验证数据
SELECT 
    objective_id,
    task_id,
    objective_type,
    target_id,
    target_name,
    target_amount,
    objective_order,
    objective_description,
    complete_template,
    created_at
FROM task_objective
ORDER BY objective_id;
