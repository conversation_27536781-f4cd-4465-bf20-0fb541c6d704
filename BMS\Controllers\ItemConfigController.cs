using Microsoft.AspNetCore.Mvc;
using BMS.Models.DTOs;
using BMS.Services.Interfaces;

namespace BMS.Controllers
{
    /// <summary>
    /// 道具配置控制器
    /// </summary>
    public class ItemConfigController : Controller
    {
        private readonly IItemConfigService _itemConfigService;
        private readonly IItemScriptService _itemScriptService;

        public ItemConfigController(IItemConfigService itemConfigService, IItemScriptService itemScriptService)
        {
            _itemConfigService = itemConfigService;
            _itemScriptService = itemScriptService;
        }

        /// <summary>
        /// 道具配置列表页面
        /// </summary>
        /// <returns></returns>
        public async Task<IActionResult> Index()
        {
            ViewBag.Types = await _itemConfigService.GetAllTypesAsync();
            ViewBag.Qualities = await _itemConfigService.GetAllQualitiesAsync();
            return View();
        }

        /// <summary>
        /// 获取道具配置列表（API）
        /// </summary>
        /// <param name="queryDto">查询条件</param>
        /// <returns></returns>
        [HttpPost]
        public async Task<IActionResult> GetList([FromBody] ItemConfigQueryDto queryDto)
        {
            if (!ModelState.IsValid)
            {
                return Json(new { success = false, message = "参数验证失败" });
            }

            var result = await _itemConfigService.GetPagedListAsync(queryDto);
            return Json(new
            {
                success = true,
                data = result.Data,
                total = result.TotalCount,
                pageIndex = result.PageIndex,
                pageSize = result.PageSize
            });
        }

        /// <summary>
        /// 获取道具配置详情（API）
        /// </summary>
        /// <param name="id">道具配置ID</param>
        /// <returns></returns>
        [HttpGet]
        public async Task<IActionResult> GetById(int id)
        {
            var item = await _itemConfigService.GetByIdAsync(id);
            if (item == null)
            {
                return Json(new { success = false, message = "道具配置不存在" });
            }

            // 获取脚本信息
            var script = await _itemScriptService.GetByItemNoAsync(item.ItemNo);

            return Json(new
            {
                success = true,
                data = new
                {
                    item,
                    script
                }
            });
        }

        /// <summary>
        /// 创建道具配置（API）
        /// </summary>
        /// <param name="createDto">创建DTO</param>
        /// <returns></returns>
        [HttpPost]
        public async Task<IActionResult> Create([FromBody] ItemConfigCreateDto createDto)
        {
            if (!ModelState.IsValid)
            {
                var errors = ModelState.Values.SelectMany(v => v.Errors)
                    .Select(e => e.ErrorMessage)
                    .ToList();
                return Json(new { success = false, message = string.Join(", ", errors) });
            }

            var result = await _itemConfigService.CreateAsync(createDto);
            return Json(new
            {
                success = result.Success,
                message = result.Message,
                data = result.Data
            });
        }

        /// <summary>
        /// 更新道具配置（API）
        /// </summary>
        /// <param name="updateDto">更新DTO</param>
        /// <returns></returns>
        [HttpPost]
        public async Task<IActionResult> Update([FromBody] ItemConfigUpdateDto updateDto)
        {
            if (!ModelState.IsValid)
            {
                var errors = ModelState.Values.SelectMany(v => v.Errors)
                    .Select(e => e.ErrorMessage)
                    .ToList();
                return Json(new { success = false, message = string.Join(", ", errors) });
            }

            var result = await _itemConfigService.UpdateAsync(updateDto);
            return Json(new
            {
                success = result.Success,
                message = result.Message
            });
        }

        /// <summary>
        /// 删除道具配置（API）
        /// </summary>
        /// <param name="deleteDto">删除DTO</param>
        /// <returns></returns>
        [HttpPost]
        public async Task<IActionResult> Delete([FromBody] ItemConfigDeleteDto deleteDto)
        {
            if (!ModelState.IsValid)
            {
                var errors = ModelState.Values.SelectMany(v => v.Errors)
                    .Select(e => e.ErrorMessage)
                    .ToList();
                return Json(new { success = false, message = string.Join(", ", errors) });
            }

            var result = await _itemConfigService.DeleteAsync(deleteDto.Id);
            return Json(new
            {
                success = result.Success,
                message = result.Message
            });
        }

        /// <summary>
        /// 获取道具配置和脚本关联列表（API）
        /// </summary>
        /// <param name="queryDto">查询条件</param>
        /// <returns></returns>
        [HttpPost]
        public async Task<IActionResult> GetListWithScript([FromBody] ItemConfigWithScriptQueryDto queryDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    var errors = ModelState.Values.SelectMany(v => v.Errors)
                        .Select(e => e.ErrorMessage)
                        .ToList();
                    return Json(new { success = false, message = "参数验证失败：" + string.Join(", ", errors) });
                }

                var result = await _itemConfigService.GetPagedListWithScriptAsync(queryDto);
                return Json(new
                {
                    success = true,
                    data = result.Data,
                    total = result.TotalCount,
                    pageIndex = result.PageIndex,
                    pageSize = result.PageSize
                });
            }
            catch (Exception ex)
            {
                // 记录异常信息
                Console.WriteLine($"GetListWithScript Error: {ex.Message}");
                Console.WriteLine($"StackTrace: {ex.StackTrace}");
                return Json(new { success = false, message = $"服务器错误：{ex.Message}" });
            }
        }

        /// <summary>
        /// 获取道具配置和脚本关联详情（API）
        /// </summary>
        /// <param name="id">道具配置ID</param>
        /// <returns></returns>
        [HttpGet]
        public async Task<IActionResult> GetByIdWithScript(int id)
        {
            var item = await _itemConfigService.GetByIdWithScriptAsync(id);
            if (item == null)
            {
                return Json(new { success = false, message = "道具配置不存在" });
            }

            return Json(new
            {
                success = true,
                data = item
            });
        }

        /// <summary>
        /// 创建道具配置和脚本关联（API）
        /// </summary>
        /// <param name="createDto">创建DTO</param>
        /// <returns></returns>
        [HttpPost]
        public async Task<IActionResult> CreateWithScript([FromBody] ItemConfigWithScriptCreateDto createDto)
        {
            if (!ModelState.IsValid)
            {
                var errors = ModelState.Values.SelectMany(v => v.Errors)
                    .Select(e => e.ErrorMessage)
                    .ToList();
                return Json(new { success = false, message = string.Join(", ", errors) });
            }

            var result = await _itemConfigService.CreateWithScriptAsync(createDto);
            return Json(new
            {
                success = result.Success,
                message = result.Message,
                data = result.Data
            });
        }

        /// <summary>
        /// 更新道具配置和脚本关联（API）
        /// </summary>
        /// <param name="updateDto">更新DTO</param>
        /// <returns></returns>
        [HttpPost]
        public async Task<IActionResult> UpdateWithScript([FromBody] ItemConfigWithScriptUpdateDto updateDto)
        {
            try
            {
                Console.WriteLine($"Controller收到更新请求: ID={updateDto.Config.Id}, Name={updateDto.Config.Name}");

                if (!ModelState.IsValid)
                {
                    var errors = ModelState.Values.SelectMany(v => v.Errors)
                        .Select(e => e.ErrorMessage)
                        .ToList();
                    Console.WriteLine($"模型验证失败: {string.Join(", ", errors)}");
                    return Json(new { success = false, message = string.Join(", ", errors) });
                }

                var result = await _itemConfigService.UpdateWithScriptAsync(updateDto);

                Console.WriteLine($"Service返回结果: Success={result.Success}, Message={result.Message}");

                // 如果更新成功，验证数据是否真的被更新
                if (result.Success)
                {
                    var verifyData = await _itemConfigService.GetByIdWithScriptAsync(updateDto.Config.Id);
                    if (verifyData != null)
                    {
                        Console.WriteLine($"验证更新后的数据: Name={verifyData.Name}, IsActive={verifyData.IsActive}");
                    }
                }

                return Json(new
                {
                    success = result.Success,
                    message = result.Message
                });
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Controller异常: {ex.Message}");
                return Json(new { success = false, message = $"服务器错误：{ex.Message}" });
            }
        }

        /// <summary>
        /// 测试数据库连接和数据（API）
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public async Task<IActionResult> TestDatabase()
        {
            try
            {
                // 测试基本连接
                var connectionTest = await _itemConfigService.GetAllTypesAsync();

                // 获取道具配置数量
                var itemCount = await _itemConfigService.GetPagedListAsync(new ItemConfigQueryDto { Page = 1, PageSize = 1 });

                // 获取脚本数量
                var scriptCount = await _itemScriptService.GetByItemNoAsync(1); // 测试脚本服务

                return Json(new
                {
                    success = true,
                    message = "数据库连接正常",
                    data = new
                    {
                        itemCount = itemCount.TotalCount,
                        typesCount = connectionTest.Count,
                        hasScript = scriptCount != null
                    }
                });
            }
            catch (Exception ex)
            {
                return Json(new
                {
                    success = false,
                    message = $"数据库连接测试失败：{ex.Message}",
                    error = ex.ToString()
                });
            }
        }

        /// <summary>
        /// 保存道具脚本（API）
        /// </summary>
        /// <param name="upsertDto">脚本创建/更新DTO</param>
        /// <returns></returns>
        [HttpPost]
        public async Task<IActionResult> SaveScript([FromBody] ItemScriptUpsertDto upsertDto)
        {
            if (!ModelState.IsValid)
            {
                var errors = ModelState.Values.SelectMany(v => v.Errors)
                    .Select(e => e.ErrorMessage)
                    .ToList();
                return Json(new { success = false, message = string.Join(", ", errors) });
            }

            var result = await _itemScriptService.UpsertAsync(upsertDto);
            return Json(new
            {
                success = result.Success,
                message = result.Message,
                data = result.Data
            });
        }

        /// <summary>
        /// 删除道具脚本（API）
        /// </summary>
        /// <param name="deleteDto">删除DTO</param>
        /// <returns></returns>
        [HttpPost]
        public async Task<IActionResult> DeleteScript([FromBody] ItemScriptDeleteDto deleteDto)
        {
            if (!ModelState.IsValid)
            {
                return Json(new { success = false, message = "参数验证失败" });
            }

            var result = await _itemScriptService.DeleteAsync(deleteDto.Id);
            return Json(new
            {
                success = result.Success,
                message = result.Message
            });
        }

        /// <summary>
        /// 检查道具编号是否存在（API）
        /// </summary>
        /// <param name="itemNo">道具编号</param>
        /// <param name="excludeId">排除的ID</param>
        /// <returns></returns>
        [HttpGet]
        public async Task<IActionResult> CheckItemNo(int itemNo, int? excludeId = null)
        {
            var exists = await _itemConfigService.IsItemNoExistsAsync(itemNo, excludeId);
            return Json(new { exists });
        }

        /// <summary>
        /// 批量更新道具状态（API）
        /// </summary>
        /// <param name="batchDto">批量更新DTO</param>
        /// <returns></returns>
        [HttpPost]
        public async Task<IActionResult> BatchUpdateStatus([FromBody] ItemConfigBatchUpdateStatusDto batchDto)
        {
            if (!ModelState.IsValid)
            {
                var errors = ModelState.Values.SelectMany(v => v.Errors)
                    .Select(e => e.ErrorMessage)
                    .ToList();
                return Json(new { success = false, message = string.Join(", ", errors) });
            }

            var result = await _itemConfigService.BatchUpdateStatusAsync(batchDto);
            return Json(new { success = result.Success, message = result.Message });
        }
    }
} 