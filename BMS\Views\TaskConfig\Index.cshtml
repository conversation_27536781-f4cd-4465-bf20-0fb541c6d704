@{
    ViewData["Title"] = "任务配置管理";
}

<!-- 科幻背景 -->
<div class="cyber-bg"></div>

<style>
    /* 科幻背景 */
    .cyber-bg {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
        z-index: -1;
    }

    .cyber-bg::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background:
            radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
            radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
            radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%);
        animation: pulse 4s ease-in-out infinite alternate;
    }

    @@keyframes pulse {
        0% { opacity: 0.5; }
        100% { opacity: 1; }
    }

    /* 科幻变量 */
    :root {
        --cyber-blue: #667eea;
        --cyber-purple: #764ba2;
        --cyber-green: #2dce89;
        --cyber-gold: #ffc107;
        --cyber-red: #f5365c;
        --cyber-text: #ffffff;
        --cyber-text-muted: rgba(255, 255, 255, 0.7);
        --cyber-bg: rgba(26, 26, 46, 0.95);
        --cyber-card-bg: rgba(255, 255, 255, 0.05);
        --cyber-border: rgba(102, 126, 234, 0.3);
    }

    /* 科幻页面容器 */
    .cyber-page {
        min-height: 100vh;
        color: var(--cyber-text);
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    }

    /* 科幻卡片样式 */
    .cyber-card {
        background: var(--cyber-card-bg);
        backdrop-filter: blur(10px);
        border: 1px solid var(--cyber-border);
        border-radius: 12px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        transition: all 0.3s ease;
        margin-bottom: 1rem;
    }

    .cyber-card:hover {
        border-color: rgba(102, 126, 234, 0.6);
        box-shadow: 0 12px 40px rgba(102, 126, 234, 0.2);
        transform: translateY(-2px);
    }

    .cyber-card-header {
        background: linear-gradient(135deg, rgba(102, 126, 234, 0.2) 0%, rgba(118, 75, 162, 0.2) 100%);
        border-bottom: 1px solid var(--cyber-border);
        padding: 1rem 1.5rem;
        border-radius: 12px 12px 0 0;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .cyber-card-title {
        color: var(--cyber-text);
        font-size: 1.1rem;
        font-weight: 600;
        margin: 0;
        display: flex;
        align-items: center;
    }

    .cyber-icon {
        font-size: 1.2rem;
        margin-right: 0.5rem;
        color: var(--cyber-blue);
    }

    .cyber-card-body {
        padding: 1.5rem;
        color: var(--cyber-text);
    }

    /* 科幻按钮样式 */
    .cyber-btn {
        background: linear-gradient(135deg, var(--cyber-blue) 0%, var(--cyber-purple) 100%);
        border: 1px solid rgba(102, 126, 234, 0.5);
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 8px;
        font-weight: 500;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        transition: all 0.3s ease;
        cursor: pointer;
    }

    .cyber-btn:hover {
        background: linear-gradient(135deg, var(--cyber-purple) 0%, var(--cyber-blue) 100%);
        border-color: rgba(102, 126, 234, 0.8);
        color: white;
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
    }

    .cyber-btn-primary {
        background: linear-gradient(135deg, var(--cyber-blue) 0%, var(--cyber-purple) 100%);
    }

    .cyber-btn-success {
        background: linear-gradient(135deg, #2dce89 0%, #2dcecc 100%);
        border-color: rgba(45, 206, 137, 0.5);
    }

    .cyber-btn-success:hover {
        background: linear-gradient(135deg, #2dcecc 0%, #2dce89 100%);
        border-color: rgba(45, 206, 137, 0.8);
        box-shadow: 0 4px 12px rgba(45, 206, 137, 0.3);
    }

    .cyber-btn-warning {
        background: linear-gradient(135deg, #fb6340 0%, #fbb140 100%);
        border-color: rgba(251, 99, 64, 0.5);
    }

    .cyber-btn-warning:hover {
        background: linear-gradient(135deg, #fbb140 0%, #fb6340 100%);
        border-color: rgba(251, 99, 64, 0.8);
        box-shadow: 0 4px 12px rgba(251, 99, 64, 0.3);
    }

    .cyber-btn-info {
        background: linear-gradient(135deg, #11cdef 0%, #1171ef 100%);
        border-color: rgba(17, 205, 239, 0.5);
    }

    .cyber-btn-info:hover {
        background: linear-gradient(135deg, #1171ef 0%, #11cdef 100%);
        border-color: rgba(17, 205, 239, 0.8);
        box-shadow: 0 4px 12px rgba(17, 205, 239, 0.3);
    }

    .cyber-btn-danger {
        background: linear-gradient(135deg, var(--cyber-red) 0%, #f56036 100%);
        border-color: rgba(245, 54, 92, 0.5);
    }

    .cyber-btn-danger:hover {
        background: linear-gradient(135deg, #f56036 0%, var(--cyber-red) 100%);
        border-color: rgba(245, 54, 92, 0.8);
        box-shadow: 0 4px 12px rgba(245, 54, 92, 0.3);
    }

    .cyber-btn-sm {
        padding: 0.25rem 0.5rem;
        font-size: 0.875rem;
    }

    .cyber-btn:disabled {
        opacity: 0.6;
        cursor: not-allowed;
        transform: none;
    }

    /* 科幻表单样式 */
    .cyber-form-control {
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid var(--cyber-border);
        border-radius: 8px;
        color: var(--cyber-text);
        padding: 0.5rem 0.75rem;
        transition: all 0.3s ease;
    }

    .cyber-form-control:focus {
        outline: none;
        border-color: rgba(102, 126, 234, 0.8);
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        background: rgba(255, 255, 255, 0.15);
    }

    .cyber-form-control::placeholder {
        color: rgba(255, 255, 255, 0.6);
    }

    .cyber-form-control option {
        background: #1a1a2e;
        color: var(--cyber-text);
    }

    .cyber-label {
        color: var(--cyber-text);
        font-weight: 500;
        margin-bottom: 0.5rem;
        display: block;
    }

    /* 科幻表格样式 */
    .cyber-table-container {
        overflow-x: auto;
        border-radius: 8px;
        border: 1px solid var(--cyber-border);
    }

    .cyber-table {
        width: 100%;
        border-collapse: collapse;
        background: var(--cyber-card-bg);
    }

    .cyber-thead {
        background: linear-gradient(135deg, rgba(102, 126, 234, 0.3) 0%, rgba(118, 75, 162, 0.3) 100%);
    }

    .cyber-th {
        padding: 0.75rem;
        text-align: left;
        font-weight: 600;
        color: var(--cyber-text);
        border-bottom: 1px solid var(--cyber-border);
    }

    .cyber-td {
        padding: 0.75rem;
        border-bottom: 1px solid rgba(102, 126, 234, 0.1);
        color: var(--cyber-text);
    }

    .cyber-tr:hover .cyber-td {
        background: rgba(102, 126, 234, 0.1);
    }

    /* 科幻页面标题 */
    .cyber-page-title {
        color: var(--cyber-text);
        font-weight: 700;
        background: linear-gradient(135deg, var(--cyber-blue) 0%, var(--cyber-purple) 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    /* 科幻模态框样式 */
    .cyber-modal {
        background: var(--cyber-bg);
        backdrop-filter: blur(10px);
        border: 1px solid var(--cyber-border);
        border-radius: 12px;
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
    }

    .cyber-modal-header {
        background: linear-gradient(135deg, rgba(102, 126, 234, 0.3) 0%, rgba(118, 75, 162, 0.3) 100%);
        border-bottom: 1px solid var(--cyber-border);
        border-radius: 12px 12px 0 0;
        padding: 1rem 1.5rem;
    }

    .cyber-modal-title {
        color: var(--cyber-text);
        font-weight: 600;
        margin: 0;
        display: flex;
        align-items: center;
    }

    .cyber-modal-body {
        padding: 1.5rem;
        color: var(--cyber-text);
    }

    .cyber-modal-footer {
        background: rgba(255, 255, 255, 0.05);
        border-top: 1px solid var(--cyber-border);
        border-radius: 0 0 12px 12px;
        padding: 1rem 1.5rem;
        display: flex;
        justify-content: flex-end;
        gap: 0.5rem;
    }

    /* 科幻复选框 */
    .cyber-checkbox {
        appearance: none;
        width: 18px;
        height: 18px;
        border: 2px solid var(--cyber-border);
        border-radius: 4px;
        background: transparent;
        cursor: pointer;
        position: relative;
        transition: all 0.3s ease;
    }

    .cyber-checkbox:checked {
        background: linear-gradient(135deg, var(--cyber-blue) 0%, var(--cyber-purple) 100%);
        border-color: var(--cyber-blue);
    }

    .cyber-checkbox:checked::after {
        content: '✓';
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        color: white;
        font-size: 12px;
        font-weight: bold;
    }



    /* 科幻徽章样式 */
    .cyber-badge {
        display: inline-block;
        padding: 0.25rem 0.5rem;
        font-size: 0.75rem;
        font-weight: 500;
        border-radius: 4px;
        text-align: center;
        white-space: nowrap;
    }

    .cyber-badge-primary {
        background: linear-gradient(135deg, var(--cyber-blue) 0%, var(--cyber-purple) 100%);
        color: white;
    }

    .cyber-badge-success {
        background: linear-gradient(135deg, var(--cyber-green) 0%, #2dcecc 100%);
        color: white;
    }

    .cyber-badge-warning {
        background: linear-gradient(135deg, var(--cyber-gold) 0%, #fbb140 100%);
        color: white;
    }

    .cyber-badge-danger {
        background: linear-gradient(135deg, var(--cyber-red) 0%, #f56036 100%);
        color: white;
    }

    .cyber-badge-info {
        background: linear-gradient(135deg, #11cdef 0%, #1171ef 100%);
        color: white;
    }

    .cyber-badge-secondary {
        background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
        color: white;
    }

    /* 科幻下拉框样式 */
    .cyber-dropdown {
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: var(--cyber-card-bg);
        backdrop-filter: blur(10px);
        border: 1px solid var(--cyber-border);
        border-radius: 8px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        z-index: 1000;
        max-height: 400px; /* 增加最大高度以显示更多道具 */
        overflow-y: auto;
        margin-top: 2px;
        /* 优化滚动条样式 */
        scrollbar-width: thin;
        scrollbar-color: rgba(102, 126, 234, 0.5) transparent;
    }

    /* Webkit浏览器滚动条样式 */
    .cyber-dropdown::-webkit-scrollbar {
        width: 6px;
    }

    .cyber-dropdown::-webkit-scrollbar-track {
        background: transparent;
    }

    .cyber-dropdown::-webkit-scrollbar-thumb {
        background: rgba(102, 126, 234, 0.5);
        border-radius: 3px;
    }

    .cyber-dropdown::-webkit-scrollbar-thumb:hover {
        background: rgba(102, 126, 234, 0.7);
    }

    /* 下拉框头部样式 */
    .cyber-dropdown-header {
        padding: 0.5rem 0.75rem;
        background: rgba(102, 126, 234, 0.1);
        border-bottom: 1px solid rgba(102, 126, 234, 0.2);
        position: sticky;
        top: 0;
        z-index: 1;
    }

    /* 自定义选择器样式（参考UserItem实现） */
    .custom-select-container {
        position: relative;
    }

    .custom-select-arrow {
        position: absolute;
        right: 12px;
        top: 50%;
        transform: translateY(-50%);
        color: var(--cyber-primary);
        pointer-events: none;
        transition: transform 0.3s ease;
    }

    .custom-select-container.open .custom-select-arrow {
        transform: translateY(-50%) rotate(180deg);
    }

    .custom-select-dropdown {
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: var(--cyber-card-bg);
        backdrop-filter: blur(10px);
        border: 1px solid var(--cyber-border);
        border-radius: 8px;
        max-height: 400px;
        overflow-y: auto;
        z-index: 1000;
        display: none;
        margin-top: 2px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    }

    .custom-select-dropdown.show {
        display: block;
    }

    .custom-select-option {
        padding: 0.75rem;
        cursor: pointer;
        border-bottom: 1px solid rgba(102, 126, 234, 0.1);
        transition: all 0.3s ease;
        color: var(--cyber-text);
    }

    .custom-select-option:hover {
        background: rgba(102, 126, 234, 0.1);
    }

    .custom-select-option.selected {
        background: rgba(102, 126, 234, 0.2);
        border-left: 3px solid var(--cyber-primary);
    }

    .custom-select-option:last-child {
        border-bottom: none;
    }

    .item-name {
        color: var(--cyber-text);
        font-weight: 500;
    }

    .item-details {
        color: var(--cyber-text-muted);
        font-size: 0.8rem;
        margin-top: 0.25rem;
    }

    /* 搜索框样式 */
    .custom-select-dropdown .cyber-form-control {
        margin: 0.5rem;
        width: calc(100% - 1rem);
    }

    .cyber-dropdown-item {
        padding: 0.75rem;
        cursor: pointer;
        border-bottom: 1px solid rgba(102, 126, 234, 0.1);
        transition: all 0.3s ease;
        color: var(--cyber-text);
    }

    .cyber-dropdown-item:hover {
        background: rgba(102, 126, 234, 0.2);
        border-color: rgba(102, 126, 234, 0.3);
    }

    .cyber-dropdown-item:last-child {
        border-bottom: none;
    }

    /* 道具品质徽章样式 */
    .cyber-badge-quality-1 {
        background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
        color: white;
    }

    .cyber-badge-quality-2 {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        color: white;
    }

    .cyber-badge-quality-3 {
        background: linear-gradient(135deg, #007bff 0%, #6610f2 100%);
        color: white;
    }

    .cyber-badge-quality-4 {
        background: linear-gradient(135deg, #fd7e14 0%, #e83e8c 100%);
        color: white;
    }

    .cyber-badge-quality-5 {
        background: linear-gradient(135deg, #dc3545 0%, #6f42c1 100%);
        color: white;
    }

    /* 科幻文本样式 */
    .cyber-text-primary {
        color: var(--cyber-blue);
    }

    .cyber-text-muted {
        color: var(--cyber-text-muted);
    }

    /* 任务目标配置样式 */
    .cyber-objective-item {
        background: rgba(102, 126, 234, 0.1);
        border: 1px solid var(--cyber-border) !important;
        border-radius: 8px;
        padding: 1rem;
        margin-bottom: 1rem;
        transition: all 0.3s ease;
    }

    .cyber-objective-item:hover {
        background: rgba(102, 126, 234, 0.15);
        border-color: rgba(102, 126, 234, 0.5) !important;
    }

    .cyber-objective-header {
        background: linear-gradient(135deg, var(--cyber-blue) 0%, var(--cyber-purple) 100%);
        color: white;
        border-radius: 6px;
        padding: 0.5rem 0.75rem;
        margin: -1rem -1rem 1rem -1rem;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .cyber-objective-header h6 {
        color: white !important;
        margin: 0;
        font-size: 0.9rem;
    }

    /* 科幻分页样式 */
    .cyber-pagination {
        display: flex;
        gap: 0.25rem;
        justify-content: center;
    }

    .cyber-page-btn {
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid var(--cyber-border);
        color: var(--cyber-text);
        padding: 0.5rem 0.75rem;
        border-radius: 6px;
        cursor: pointer;
        transition: all 0.3s ease;
        min-width: 40px;
        text-align: center;
        text-decoration: none;
    }

    .cyber-page-btn:hover:not(.disabled) {
        background: rgba(102, 126, 234, 0.2);
        border-color: rgba(102, 126, 234, 0.6);
        color: var(--cyber-text);
    }

    .cyber-page-btn.active {
        background: linear-gradient(135deg, var(--cyber-blue) 0%, var(--cyber-purple) 100%);
        border-color: var(--cyber-blue);
        color: white;
    }

    .cyber-page-btn.disabled {
        opacity: 0.5;
        cursor: not-allowed;
    }

    /* 响应式设计 */
    @@media (max-width: 768px) {
        .cyber-card-header {
            flex-direction: column;
            gap: 0.5rem;
            text-align: center;
        }
    }
</style>

<!-- 科幻任务配置管理应用容器 -->
<div id="taskConfigApp">
    <!-- 科幻页面标题 -->
    <div class="container-fluid py-4">
        <div class="row mb-4">
            <div class="col-12">
                <div class="cyber-card">
                    <div class="cyber-card-header">
                        <div class="cyber-icon">🎯</div>
                        <h1 class="cyber-card-title">任务配置管理</h1>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 科幻主要内容 -->
    <div class="container-fluid">

        <!-- 科幻搜索条件 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="cyber-card">
                    <div class="cyber-card-header">
                        <div class="cyber-icon">🔍</div>
                        <h3 class="cyber-card-title">搜索条件</h3>
                    </div>
                    <div class="cyber-card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label class="cyber-label" for="searchTaskName">任务名称</label>
                                    <input type="text" class="form-control cyber-form-control" id="searchTaskName"
                                           v-model="queryForm.taskName" placeholder="请输入任务名称">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label class="cyber-label" for="searchTaskType">任务类型</label>
                                    <select class="form-control cyber-form-control" id="searchTaskType" v-model="queryForm.taskType">
                                        <option value="">全部类型</option>
                                        <option v-for="option in taskTypeOptions"
                                                :key="option.value" :value="option.value">
                                            {{ option.label }}
                                        </option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label class="cyber-label" for="searchIsActive">激活状态</label>
                                    <select class="form-control cyber-form-control" id="searchIsActive" v-model="queryForm.isActive">
                                        <option value="">全部状态</option>
                                        <option value="true">已激活</option>
                                        <option value="false">已禁用</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label class="cyber-label">&nbsp;</label>
                                    <div>
                                        <button type="button" class="cyber-btn cyber-btn-primary me-2" v-on:click="searchTasks">
                                            <i class="fas fa-search me-1"></i>搜索
                                        </button>
                                        <button type="button" class="cyber-btn cyber-btn-warning me-2" v-on:click="resetSearch">
                                            <i class="fas fa-redo me-1"></i>重置
                                        </button>
                                        <button type="button" class="cyber-btn cyber-btn-success" v-on:click="showAddModal">
                                            <i class="fas fa-plus me-1"></i>新增任务
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 科幻任务列表 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="cyber-card">
                    <div class="cyber-card-header">
                        <div class="cyber-icon">📋</div>
                        <h3 class="cyber-card-title">任务配置列表</h3>
                        <div class="cyber-card-tools">
                            <span class="cyber-badge cyber-badge-info">
                                共 {{ totalCount }} 条记录
                            </span>
                        </div>
                    </div>
                    <div class="cyber-card-body">
                        <div class="cyber-table-container">
                            <table class="cyber-table">
                                <thead class="cyber-thead">
                                    <tr class="cyber-tr">
                                        <th class="cyber-th">任务ID</th>
                                        <th class="cyber-th">任务名称</th>
                                        <th class="cyber-th">任务类型</th>
                                        <th class="cyber-th">是否可重复</th>
                                        <th class="cyber-th">前置任务</th>
                                        <th class="cyber-th">状态</th>
                                        <th class="cyber-th">排序</th>
                                        <th class="cyber-th">创建时间</th>
                                        <th class="cyber-th">操作</th>
                                    </tr>
                                </thead>
                                <tbody class="cyber-tbody" v-if="loading">
                                    <tr class="cyber-tr">
                                        <td colspan="9" class="cyber-td text-center py-4">
                                            <i class="fas fa-spinner fa-spin me-2"></i>加载中...
                                        </td>
                                    </tr>
                                </tbody>
                                <tbody class="cyber-tbody" v-else-if="tasksList.length === 0">
                                    <tr class="cyber-tr">
                                        <td colspan="9" class="cyber-td text-center py-4 cyber-text-muted">
                                            <i class="fas fa-inbox me-2"></i>暂无数据
                                        </td>
                                    </tr>
                                </tbody>
                                <tbody class="cyber-tbody" v-else>
                                    <tr class="cyber-tr" v-for="task in tasksList" :key="task.taskId">
                                        <td class="cyber-td">
                                            <span class="cyber-text-primary">{{ task.taskId }}</span>
                                        </td>
                                        <td class="cyber-td">
                                            <strong>{{ task.taskName }}</strong>
                                            <br>
                                            <small class="cyber-text-muted">{{ task.taskDescription }}</small>
                                        </td>
                                        <td class="cyber-td">
                                            <span class="cyber-badge cyber-badge-info">{{ task.taskTypeName }}</span>
                                        </td>
                                        <td class="cyber-td">
                                            <span v-if="task.isRepeatable" class="cyber-badge cyber-badge-success">
                                                <i class="fas fa-check me-1"></i>可重复
                                            </span>
                                            <span v-else class="cyber-badge cyber-badge-warning">
                                                <i class="fas fa-times me-1"></i>不可重复
                                            </span>
                                        </td>
                                        <td class="cyber-td">{{ task.prerequisiteTaskName || '-' }}</td>
                                        <td class="cyber-td">
                                            <span v-if="task.isActive" class="cyber-badge cyber-badge-success">
                                                <i class="fas fa-check me-1"></i>已激活
                                            </span>
                                            <span v-else class="cyber-badge cyber-badge-danger">
                                                <i class="fas fa-times me-1"></i>已禁用
                                            </span>
                                        </td>
                                        <td class="cyber-td">{{ task.sortOrder }}</td>
                                        <td class="cyber-td">{{ formatDateTime(task.createdAt) }}</td>
                                        <td class="cyber-td">
                                            <div class="btn-group btn-group-sm">
                                                <button type="button" class="cyber-btn cyber-btn-sm cyber-btn-info me-1"
                                                        v-on:click="viewDetail(task)" title="查看详情">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                <button type="button" class="cyber-btn cyber-btn-sm cyber-btn-warning me-1"
                                                        v-on:click="showEditModal(task)" title="编辑基本信息">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button type="button" class="cyber-btn cyber-btn-sm cyber-btn-primary me-1"
                                                        v-on:click="showRewardModalForTask(task)" title="配置奖励">
                                                    <i class="fas fa-gift"></i>
                                                </button>
                                                <button type="button" class="cyber-btn cyber-btn-sm cyber-btn-secondary me-1"
                                                        v-on:click="showObjectiveModalForTask(task)" title="配置目标">
                                                    <i class="fas fa-target"></i>
                                                </button>
                                                <button v-if="task.isActive" type="button" class="cyber-btn cyber-btn-sm cyber-btn-warning me-1"
                                                        v-on:click="toggleActive(task, false)" title="禁用">
                                                    <i class="fas fa-pause"></i>
                                                </button>
                                                <button v-else type="button" class="cyber-btn cyber-btn-sm cyber-btn-success me-1"
                                                        v-on:click="toggleActive(task, true)" title="启用">
                                                    <i class="fas fa-play"></i>
                                                </button>
                                                <button type="button" class="cyber-btn cyber-btn-sm cyber-btn-danger"
                                                        v-on:click="deleteTask(task)" title="删除">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 科幻分页 -->
        <div class="row" v-if="tasksList.length > 0">
            <div class="col-12">
                <div class="cyber-card">
                    <div class="cyber-card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div class="cyber-text-muted">
                                显示第 {{ (currentPage - 1) * pageSize + 1 }} 到 {{ Math.min(currentPage * pageSize, totalCount) }} 条记录，共 {{ totalCount }} 条记录
                            </div>
                            <div class="cyber-pagination">
                                <a href="#" class="cyber-page-btn" :class="{ disabled: currentPage === 1 }" v-on:click.prevent="changePage(currentPage - 1)">
                                    <i class="fas fa-chevron-left"></i>
                                </a>
                                <a v-for="page in visiblePages" :key="page" href="#" class="cyber-page-btn"
                                   :class="{ active: page === currentPage }" v-on:click.prevent="changePage(page)">
                                    {{ page }}
                                </a>
                                <a href="#" class="cyber-page-btn" :class="{ disabled: currentPage === totalPages }" v-on:click.prevent="changePage(currentPage + 1)">
                                    <i class="fas fa-chevron-right"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 科幻任务创建/编辑模态框 -->
    <div class="modal fade" id="taskModal" tabindex="-1" aria-labelledby="taskModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content cyber-modal">
                <div class="modal-header cyber-modal-header">
                    <h5 class="modal-title cyber-modal-title" id="taskModalLabel">
                        <i class="fas fa-tasks me-2"></i>{{ isEdit ? '编辑任务' : '新增任务' }}
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body cyber-modal-body">
                    <form v-on:submit.prevent="saveTask">
                        <!-- 任务ID自动生成提示 -->
                        <div class="row" v-if="isEdit">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="cyber-label">任务ID</label>
                                    <input type="text" class="cyber-form-control" :value="taskForm.taskId" readonly>
                                    <div class="form-text cyber-text-muted">
                                        <i class="fas fa-info-circle me-1"></i>任务ID由系统自动生成，不可修改
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="taskName" class="cyber-label">任务名称 <span class="text-danger">*</span></label>
                                    <input type="text" class="cyber-form-control" id="taskName" v-model="taskForm.taskName"
                                           required maxlength="200">
                                </div>
                            </div>
                        </div>

                        <!-- 创建任务时只显示任务名称 -->
                        <div class="row" v-if="!isEdit">
                            <div class="col-md-12">
                                <div class="mb-3">
                                    <label for="taskName" class="cyber-label">任务名称 <span class="text-danger">*</span></label>
                                    <input type="text" class="cyber-form-control" id="taskName" v-model="taskForm.taskName"
                                           required maxlength="200">
                                    <div class="form-text cyber-text-muted">
                                        <i class="fas fa-magic me-1"></i>任务ID将在创建时自动生成
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="taskDescription" class="cyber-label">任务描述</label>
                            <textarea class="cyber-form-control" id="taskDescription" v-model="taskForm.taskDescription"
                                      rows="3" maxlength="500"></textarea>
                        </div>

                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="taskType" class="cyber-label">任务类型</label>
                                    <select class="cyber-form-control" id="taskType" v-model="taskForm.taskType">
                                        <option v-for="option in taskTypeOptions"
                                                :key="option.value" :value="parseInt(option.value)">
                                            {{ option.label }}
                                        </option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="prerequisiteTask" class="cyber-label">前置任务</label>
                                    <select class="cyber-form-control" id="prerequisiteTask" v-model="taskForm.prerequisiteTask">
                                        <option value="">无前置任务</option>
                                        <option v-for="option in taskOptions"
                                                :key="option.value" :value="option.value">
                                            {{ option.label }}
                                        </option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="sortOrder" class="cyber-label">排序顺序</label>
                                    <input type="number" class="cyber-form-control" id="sortOrder" v-model="taskForm.sortOrder"
                                           min="0" max="9999">
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="cyber-checkbox" type="checkbox" id="isRepeatable"
                                               v-model="taskForm.isRepeatable">
                                        <label class="cyber-label" for="isRepeatable" style="margin-left: 0.5rem;">
                                            可重复任务
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="cyber-checkbox" type="checkbox" id="isActive"
                                               v-model="taskForm.isActive">
                                        <label class="cyber-label" for="isActive" style="margin-left: 0.5rem;">
                                            启用任务
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>


                    </form>
                </div>
                <div class="modal-footer cyber-modal-footer">
                    <button type="button" class="cyber-btn cyber-btn-warning me-2" data-bs-dismiss="modal">
                        <i class="fas fa-times me-1"></i>取消
                    </button>
                    <button type="button" class="cyber-btn cyber-btn-primary" v-on:click="saveTask" :disabled="saving">
                        <i class="fas fa-spinner fa-spin me-1" v-if="saving"></i>
                        <i class="fas fa-save me-1" v-else></i>
                        {{ saving ? '保存中...' : (isEdit ? '更新' : '创建') }}
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 奖励配置模态框 -->
    <div class="modal fade" id="rewardModal" tabindex="-1" aria-labelledby="rewardModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl">
            <div class="modal-content cyber-modal">
                <div class="modal-header cyber-modal-header">
                    <h5 class="modal-title cyber-modal-title" id="rewardModalLabel">
                        <i class="fas fa-gift me-2"></i>奖励配置
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body cyber-modal-body">
                    <div class="cyber-card">
                        <div class="cyber-card-header">
                            <div class="cyber-icon">🎁</div>
                            <h6 class="cyber-card-title">
                                奖励列表
                                <span class="cyber-badge cyber-badge-primary ms-2" v-if="taskForm.rewards.length > 0">
                                    {{ taskForm.rewards.length }} 个奖励
                                </span>
                            </h6>
                            <button type="button" class="cyber-btn cyber-btn-sm cyber-btn-success" v-on:click="addReward">
                                <i class="fas fa-plus me-1"></i>添加奖励
                            </button>
                        </div>
                        <div class="cyber-card-body">
                            <div v-if="taskForm.rewards.length === 0" class="text-center cyber-text-muted py-3">
                                <i class="fas fa-gift me-2"></i>暂无奖励，点击"添加奖励"按钮添加
                            </div>

                            <div v-for="(reward, index) in taskForm.rewards" :key="index"
                                 class="cyber-objective-item">
                                <div class="cyber-objective-header">
                                    <h6 class="mb-0">
                                        <i class="fas fa-gift me-1"></i>奖励 {{ index + 1 }}
                                    </h6>
                                    <button type="button" class="cyber-btn cyber-btn-sm cyber-btn-danger"
                                            v-on:click="removeReward(index)" title="删除此奖励">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                                <div class="row">
                                    <div class="col-md-4">
                                        <label class="cyber-label">奖励类型 <span class="text-danger">*</span></label>
                                        <select class="cyber-form-control" v-model="reward.type" required>
                                            <option value="">请选择奖励类型</option>
                                            <option value="exp">经验值</option>
                                            <option value="gold">金币</option>
                                            <option value="item">道具</option>
                                            <option value="pet">宠物</option>
                                            <option value="skill">技能</option>
                                        </select>
                                    </div>
                                    <div class="col-md-4" v-if="reward.type === 'item'">
                                        <label class="cyber-label">道具 <span class="text-danger">*</span></label>
                                        <div class="custom-select-container" :class="{ open: reward.showDropdown }">
                                            <input
                                                type="text"
                                                class="cyber-form-control"
                                                :value="getSelectedItemDisplay(reward)"
                                                @@click="toggleItemDropdown(reward)"
                                                placeholder="请选择道具"
                                                readonly
                                                required>
                                            <i class="fas fa-chevron-down custom-select-arrow"></i>
                                            <div class="custom-select-dropdown" :class="{ show: reward.showDropdown }">
                                                <input
                                                    type="text"
                                                    class="cyber-form-control"
                                                    v-model="reward.searchText"
                                                    placeholder="搜索道具名称或编号..."
                                                    @@input="filterItems(reward)">
                                                <div
                                                    v-for="item in reward.filteredItems"
                                                    :key="item.id"
                                                    class="custom-select-option"
                                                    :class="{ selected: reward.id == item.id }"
                                                    @@click="selectItem(reward, item)">
                                                    <div class="item-name">{{ item.name }} ({{ item.id }})</div>
                                                    <div class="item-details">
                                                        {{ getItemTypeText(item.type) }} | {{ getItemQualityName(item.quality) }} | 价格: {{ item.price || 0 }}
                                                    </div>
                                                </div>
                                                <div v-if="reward.filteredItems.length === 0" class="custom-select-option" style="color: #64748b; text-align: center;">
                                                    未找到匹配的道具
                                                </div>
                                                <!-- 道具计数显示 -->
                                                <div v-if="reward.filteredItems.length > 0" class="cyber-dropdown-header" style="position: static; margin-top: 0.5rem;">
                                                    <small class="text-muted">
                                                        <i class="fas fa-list me-1"></i>
                                                        <span v-if="!reward.searchText">
                                                            显示前 {{ reward.filteredItems.length }} 个道具（共 {{ this.itemOptions.length }} 个）
                                                        </span>
                                                        <span v-else>
                                                            找到 {{ reward.filteredItems.length }} 个匹配道具
                                                        </span>
                                                    </small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4" v-else-if="reward.type === 'pet' || reward.type === 'skill'">
                                        <label class="cyber-label">{{ reward.type === 'pet' ? '宠物ID' : '技能ID' }} <span class="text-danger">*</span></label>
                                        <input type="text" class="cyber-form-control"
                                               v-model="reward.id" :placeholder="reward.type === 'pet' ? '宠物ID' : '技能ID'" required>
                                    </div>
                                    <div class="col-md-4">
                                        <label class="cyber-label">数量/值 <span class="text-danger">*</span></label>
                                        <input type="number" class="cyber-form-control"
                                               v-model="reward.amount" min="1" required>
                                    </div>
                                </div>
                                <div class="row mt-2" v-if="reward.type === 'item'">
                                    <div class="col-md-6">
                                        <label class="cyber-label">道具名称</label>
                                        <input type="text" class="cyber-form-control"
                                               v-model="reward.name"
                                               placeholder="道具名称（可选）">
                                    </div>
                                    <div class="col-md-6">
                                        <label class="cyber-label">品质</label>
                                        <select class="cyber-form-control" v-model="reward.quality">
                                            <option value="">默认品质</option>
                                            <option value="1">普通</option>
                                            <option value="2">优秀</option>
                                            <option value="3">稀有</option>
                                            <option value="4">史诗</option>
                                            <option value="5">传说</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer cyber-modal-footer">
                    <button type="button" class="cyber-btn cyber-btn-warning me-2" data-bs-dismiss="modal">
                        <i class="fas fa-times me-1"></i>关闭
                    </button>
                    <button type="button" class="cyber-btn cyber-btn-primary" v-on:click="saveRewardConfig">
                        <i class="fas fa-save me-1"></i>保存配置
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 任务目标配置模态框 -->
    <div class="modal fade" id="objectiveModal" tabindex="-1" aria-labelledby="objectiveModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl">
            <div class="modal-content cyber-modal">
                <div class="modal-header cyber-modal-header">
                    <h5 class="modal-title cyber-modal-title" id="objectiveModalLabel">
                        <i class="fas fa-target me-2"></i>任务目标配置
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body cyber-modal-body">
                    <div class="cyber-card">
                        <div class="cyber-card-header">
                            <div class="cyber-icon">🎯</div>
                            <h6 class="cyber-card-title">
                                目标列表
                                <span class="cyber-badge cyber-badge-primary ms-2" v-if="taskForm.objectives.length > 0">
                                    {{ taskForm.objectives.length }} 个目标
                                </span>
                            </h6>
                            <button type="button" class="cyber-btn cyber-btn-sm cyber-btn-success" v-on:click="addObjective">
                                <i class="fas fa-plus me-1"></i>添加目标
                            </button>
                        </div>
                        <div class="cyber-card-body">
                            <div v-if="taskForm.objectives.length === 0" class="text-center cyber-text-muted py-3">
                                <i class="fas fa-inbox me-2"></i>暂无目标，点击"添加目标"按钮添加
                            </div>

                            <div v-for="(objective, index) in taskForm.objectives" :key="index"
                                 class="cyber-objective-item">
                                <div class="cyber-objective-header">
                                    <h6 class="mb-0">
                                        <i class="fas fa-target me-1"></i>目标 {{ index + 1 }}
                                    </h6>
                                    <button type="button" class="cyber-btn cyber-btn-sm cyber-btn-danger"
                                            v-on:click="removeObjective(index)" title="删除此目标">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                                <div class="row">
                                    <div class="col-md-4">
                                        <label class="cyber-label">目标类型 <span class="text-danger">*</span></label>
                                        <select class="cyber-form-control" v-model="objective.objectiveType" required>
                                            <option value="">请选择目标类型</option>
                                            <option v-for="option in objectiveTypeOptions"
                                                    :key="option.value" :value="option.value">
                                                {{ option.label }}
                                            </option>
                                        </select>
                                    </div>
                                    <div class="col-md-4">
                                        <!-- 收集道具时显示下拉框 -->
                                        <div v-if="objective.objectiveType === 'COLLECT_ITEM'">
                                            <label class="cyber-label">目标道具 <span class="text-danger">*</span></label>
                                            <div class="custom-select-container" :class="{ open: objective.showDropdown }">
                                                <input
                                                    type="text"
                                                    class="cyber-form-control"
                                                    :value="getSelectedObjectiveItemDisplay(objective)"
                                                    @@click="toggleObjectiveItemDropdown(objective)"
                                                    placeholder="请选择道具"
                                                    readonly
                                                    required>
                                                <i class="fas fa-chevron-down custom-select-arrow"></i>
                                                <div class="custom-select-dropdown" :class="{ show: objective.showDropdown }">
                                                    <input
                                                        type="text"
                                                        class="cyber-form-control"
                                                        v-model="objective.searchText"
                                                        placeholder="搜索道具名称或编号..."
                                                        @@input="filterObjectiveItems(objective)">
                                                    <div
                                                        v-for="item in objective.filteredItems"
                                                        :key="item.id"
                                                        class="custom-select-option"
                                                        :class="{ selected: objective.targetId == item.id }"
                                                        @@click="selectObjectiveItem(objective, item)">
                                                        <div class="item-name">{{ item.name }} ({{ item.id }})</div>
                                                        <div class="item-details">
                                                            {{ getItemTypeText(item.type) }} | {{ getItemQualityName(item.quality) }} | 价格: {{ item.price || 0 }}
                                                        </div>
                                                    </div>
                                                    <div v-if="objective.filteredItems.length === 0" class="custom-select-option" style="color: #64748b; text-align: center;">
                                                        未找到匹配的道具
                                                    </div>
                                                    <!-- 道具计数显示 -->
                                                    <div v-if="objective.filteredItems.length > 0" class="cyber-dropdown-header" style="position: static; margin-top: 0.5rem;">
                                                        <small class="text-muted">
                                                            <i class="fas fa-list me-1"></i>
                                                            <span v-if="!objective.searchText">
                                                                显示前 {{ objective.filteredItems.length }} 个道具（共 {{ this.itemOptions.length }} 个）
                                                            </span>
                                                            <span v-else>
                                                                找到 {{ objective.filteredItems.length }} 个匹配道具
                                                            </span>
                                                        </small>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <!-- 其他目标类型显示文本输入框 -->
                                        <div v-else>
                                            <label class="cyber-label">目标ID</label>
                                            <input type="text" class="cyber-form-control"
                                                   v-model="objective.targetId" placeholder="怪物ID/技能ID等">
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <label class="cyber-label">目标数量 <span class="text-danger">*</span></label>
                                        <input type="number" class="cyber-form-control"
                                               v-model="objective.targetAmount" min="1" required>
                                    </div>
                                    <div class="col-md-2">
                                        <label class="cyber-label">顺序</label>
                                        <input type="number" class="cyber-form-control"
                                               v-model="objective.objectiveOrder" min="0">
                                    </div>
                                </div>
                                <div class="row mt-2">
                                    <div class="col-md-6">
                                        <label class="cyber-label">目标描述</label>
                                        <input type="text" class="cyber-form-control"
                                               v-model="objective.objectiveDescription"
                                               placeholder="目标的详细描述">
                                    </div>
                                    <div class="col-md-6">
                                        <label class="cyber-label">进度模版</label>
                                        <input type="text" class="cyber-form-control"
                                               v-model="objective.completeTemplate"
                                               placeholder="进度显示模版，如：{current}/{total}">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer cyber-modal-footer">
                    <button type="button" class="cyber-btn cyber-btn-warning me-2" data-bs-dismiss="modal">
                        <i class="fas fa-times me-1"></i>关闭
                    </button>
                    <button type="button" class="cyber-btn cyber-btn-primary" v-on:click="saveObjectiveConfig">
                        <i class="fas fa-save me-1"></i>保存配置
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 科幻任务详情模态框 -->
    <div class="modal fade" id="taskDetailModal" tabindex="-1" aria-labelledby="taskDetailModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content cyber-modal">
                <div class="modal-header cyber-modal-header">
                    <h5 class="modal-title cyber-modal-title" id="taskDetailModalLabel">
                        <i class="fas fa-info-circle me-2"></i>任务详情
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body cyber-modal-body" v-if="selectedTask">
                        <div class="row">
                            <div class="col-md-6">
                                <h6><i class="fas fa-id-card mr-1"></i>基本信息</h6>
                                <table class="table table-sm">
                                    <tr>
                                        <td><strong>任务ID:</strong></td>
                                        <td>{{ selectedTask.taskId }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>任务名称:</strong></td>
                                        <td>{{ selectedTask.taskName }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>任务类型:</strong></td>
                                        <td><span class="text-muted">{{ selectedTask.taskTypeName }}</span></td>
                                    </tr>
                                    <tr>
                                        <td><strong>状态:</strong></td>
                                        <td>
                                            <span v-if="selectedTask.isActive" class="text-muted">已启用</span>
                                            <span v-else class="text-muted">已禁用</span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong>可重复:</strong></td>
                                        <td>
                                            <span v-if="selectedTask.isRepeatable" class="text-muted">是</span>
                                            <span v-else class="text-muted">否</span>
                                        </td>
                                    </tr>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <h6><i class="fas fa-cog mr-1"></i>配置信息</h6>
                                <table class="table table-sm">
                                    <tr>
                                        <td><strong>前置任务:</strong></td>
                                        <td>{{ selectedTask.prerequisiteTaskName || '无' }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>排序顺序:</strong></td>
                                        <td>{{ selectedTask.sortOrder }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>创建时间:</strong></td>
                                        <td>{{ formatDateTime(selectedTask.createdAt) }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>更新时间:</strong></td>
                                        <td>{{ formatDateTime(selectedTask.updatedAt) }}</td>
                                    </tr>
                                </table>
                            </div>
                        </div>

                        <div class="mt-3" v-if="selectedTask.taskDescription">
                            <h6><i class="fas fa-align-left mr-1"></i>任务描述</h6>
                            <p class="text-muted">{{ selectedTask.taskDescription }}</p>
                        </div>

                        <div class="mt-3" v-if="selectedTask.rewardConfig">
                            <h6><i class="fas fa-gift mr-1"></i>奖励配置</h6>
                            <div class="table-responsive">
                                <table class="table table-sm table-striped">
                                    <thead>
                                        <tr>
                                            <th>奖励类型</th>
                                            <th>奖励ID</th>
                                            <th>数量/值</th>
                                            <th>名称</th>
                                            <th>品质</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr v-for="reward in parseRewardConfig(selectedTask.rewardConfig)" :key="reward.type + (reward.id || '')">
                                            <td>
                                                <span class="cyber-badge cyber-badge-info">
                                                    {{ getRewardTypeName(reward.type) }}
                                                </span>
                                            </td>
                                            <td>{{ reward.id || '-' }}</td>
                                            <td>{{ reward.amount }}</td>
                                            <td>{{ reward.name || '-' }}</td>
                                            <td>
                                                <span v-if="reward.quality" class="cyber-badge" :class="getQualityBadgeClass(reward.quality)">
                                                    {{ getQualityName(reward.quality) }}
                                                </span>
                                                <span v-else>-</span>
                                            </td>
                                        </tr>
                                        <tr v-if="parseRewardConfig(selectedTask.rewardConfig).length === 0">
                                            <td colspan="5" class="text-center cyber-text-muted">暂无奖励配置</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            <details class="mt-2">
                                <summary class="cyber-text-muted" style="cursor: pointer;">查看原始JSON配置</summary>
                                <pre class="bg-light p-2 rounded mt-2"><code>{{ selectedTask.rewardConfig }}</code></pre>
                            </details>
                        </div>

                        <div class="mt-3" v-if="selectedTask.objectives && selectedTask.objectives.length > 0">
                            <h6><i class="fas fa-bullseye mr-1"></i>任务目标</h6>
                            <div class="table-responsive">
                                <table class="table table-sm table-striped">
                                    <thead>
                                        <tr>
                                            <th>顺序</th>
                                            <th>目标类型</th>
                                            <th>目标ID</th>
                                            <th>目标名称</th>
                                            <th>目标数量</th>
                                            <th>描述</th>
                                            <th>进度模版</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr v-for="objective in selectedTask.objectives" :key="objective.objectiveId">
                                            <td>{{ objective.objectiveOrder }}</td>
                                            <td><span class="text-muted">{{ objective.objectiveTypeName }}</span></td>
                                            <td>{{ objective.targetId || '-' }}</td>
                                            <td>{{ objective.targetName || '-' }}</td>
                                            <td>{{ objective.targetAmount }}</td>
                                            <td>{{ objective.objectiveDescription || '-' }}</td>
                                            <td>{{ objective.completeTemplate || '-' }}</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                </div>
                <div class="modal-footer cyber-modal-footer">
                    <button type="button" class="cyber-btn cyber-btn-warning me-2" data-bs-dismiss="modal">
                        <i class="fas fa-times me-1"></i>关闭
                    </button>
                    <button type="button" class="cyber-btn cyber-btn-primary" v-on:click="editTaskFromDetail" v-if="selectedTask">
                        <i class="fas fa-edit me-1"></i>编辑任务
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        // 任务配置管理Vue应用
        Vue.createApp({
            data() {
                return {
                    // 加载状态
                    loading: false,
                    saving: false,

                    // 任务列表数据
                    tasksList: [],
                    totalCount: 0,
                    currentPage: 1,
                    pageSize: 10,

                    // 表单状态
                    isEdit: false,
                    selectedTask: null,

                    // 下拉选项
                    taskTypeOptions: [],
                    taskOptions: [],
                    objectiveTypeOptions: [],
                    itemOptions: [], // 道具选项列表

                    // 查询表单
                    queryForm: {
                        taskName: '',
                        taskType: '',
                        isActive: ''
                    },

                    // 任务表单
                    taskForm: {
                        taskId: '',
                        taskName: '',
                        taskDescription: '',
                        taskType: 0,
                        isRepeatable: false,
                        prerequisiteTask: '',
                        requiredPet: '',
                        rewardConfig: '',
                        isNetworkTask: false,
                        isActive: true,
                        sortOrder: 0,
                        objectives: [],
                        rewards: []
                    }
                };
            },

            computed: {
                // 总页数
                totalPages() {
                    return Math.ceil(this.totalCount / this.pageSize);
                },

                // 可见页码
                visiblePages() {
                    const total = this.totalPages;
                    const current = this.currentPage;
                    const delta = 2;
                    const pages = [];

                    for (let i = Math.max(2, current - delta); i <= Math.min(total - 1, current + delta); i++) {
                        pages.push(i);
                    }

                    if (current - delta > 2) {
                        pages.unshift("...");
                    }
                    if (current + delta < total - 1) {
                        pages.push("...");
                    }

                    pages.unshift(1);
                    if (total > 1) {
                        pages.push(total);
                    }

                    return pages.filter((page, index, arr) => arr.indexOf(page) === index);
                }
            },

            methods: {
                // 加载任务列表
                async loadTasks() {
                    try {
                        this.loading = true;

                        const requestData = {
                            TaskName: this.queryForm.taskName || '',
                            TaskType: this.queryForm.taskType ? parseInt(this.queryForm.taskType) : null,
                            IsActive: this.queryForm.isActive === '' ? null : (this.queryForm.isActive === 'true'),
                            Page: this.currentPage,
                            PageSize: this.pageSize
                        };

                        console.log('发送请求数据:', requestData);

                        const response = await axios.post('/TaskConfig/GetList', requestData);
                        if (response.data.code === 200) {
                            this.tasksList = response.data.data || [];
                            this.totalCount = response.data.total || 0;
                        } else {
                            console.error('获取任务列表失败：', response.data.message);
                            alert('获取任务列表失败：' + response.data.message);
                        }
                    } catch (error) {
                        console.error('获取任务列表失败：', error);
                        alert('获取任务列表失败，请重试');
                    } finally {
                        this.loading = false;
                    }
                },

                // 搜索任务
                searchTasks() {
                    console.log('搜索参数:', this.queryForm);
                    this.currentPage = 1;
                    this.loadTasks();
                },

                // 重置搜索
                resetSearch() {
                    Object.assign(this.queryForm, {
                        taskName: '',
                        taskType: '',
                        isActive: ''
                    });
                    this.searchTasks();
                },

                // 分页
                changePage(page) {
                    if (page >= 1 && page <= this.totalPages) {
                        this.currentPage = page;
                        this.loadTasks();
                    }
                },

                // 显示新增模态框
                showAddModal() {
                    this.isEdit = false;
                    this.taskForm = {
                        taskName: '',
                        taskDescription: '',
                        taskType: 0,
                        isRepeatable: false,
                        prerequisiteTask: '',
                        requiredPet: '',
                        rewardConfig: '',
                        isNetworkTask: false,
                        isActive: true,
                        sortOrder: 0,
                        objectives: [],
                        rewards: []
                    };

                    // 使用Bootstrap 5的方式显示模态框
                    const modal = new bootstrap.Modal(document.getElementById('taskModal'));
                    modal.show();
                },

                // 显示编辑模态框
                showEditModal(task) {
                    this.isEdit = true;
                    this.taskForm = {
                        taskId: task.taskId,
                        taskName: task.taskName,
                        taskDescription: task.taskDescription,
                        taskType: task.taskType,
                        isRepeatable: task.isRepeatable,
                        prerequisiteTask: task.prerequisiteTask,
                        requiredPet: task.requiredPet,
                        rewardConfig: task.rewardConfig,
                        isNetworkTask: task.isNetworkTask,
                        isActive: task.isActive,
                        sortOrder: task.sortOrder,
                        objectives: this.processObjectives(task.objectives || []),
                        rewards: this.parseRewardConfig(task.rewardConfig)
                    };

                    // 使用Bootstrap 5的方式显示模态框
                    const modal = new bootstrap.Modal(document.getElementById('taskModal'));
                    modal.show();
                },

                // 保存任务
                async saveTask() {
                    try {
                        this.saving = true;

                        // 验证必填字段
                        if (!this.taskForm.taskName.trim()) {
                            alert('请输入任务名称');
                            return;
                        }

                        // 验证奖励配置
                        for (let i = 0; i < this.taskForm.rewards.length; i++) {
                            const reward = this.taskForm.rewards[i];
                            if (!reward.type) {
                                alert(`第${i + 1}个奖励的类型不能为空`);
                                return;
                            }
                            if (!reward.amount || reward.amount <= 0) {
                                alert(`第${i + 1}个奖励的数量必须大于0`);
                                return;
                            }
                            if ((reward.type === 'item' || reward.type === 'pet' || reward.type === 'skill') && !reward.id) {
                                alert(`第${i + 1}个奖励的ID不能为空`);
                                return;
                            }
                        }

                        // 准备提交数据
                        const submitData = { ...this.taskForm };
                        // 将奖励数组转换为JSON字符串
                        submitData.rewardConfig = this.generateRewardConfig(this.taskForm.rewards);

                        const url = this.isEdit ? '/TaskConfig/Update' : '/TaskConfig/Create';
                        const response = await axios.post(url, submitData);

                        if (response.data.code === 200) {
                            // 关闭模态框
                            const modal = bootstrap.Modal.getInstance(document.getElementById('taskModal'));
                            modal.hide();

                            // 刷新列表
                            await this.loadTasks();

                            alert(response.data.message || (this.isEdit ? '更新成功' : '创建成功'));
                        } else {
                            alert(response.data.message || '操作失败');
                        }
                    } catch (error) {
                        console.error('保存任务失败：', error);
                        alert('保存失败，请重试');
                    } finally {
                        this.saving = false;
                    }
                },

                // 删除任务
                async deleteTask(task) {
                    if (!confirm(`确定要删除任务"${task.taskName}"吗？此操作无法撤销！`)) {
                        return;
                    }

                    try {
                        const response = await axios.post('/TaskConfig/Delete', { taskId: task.taskId });
                        if (response.data.code === 200) {
                            this.loadTasks();
                            alert('删除成功');
                        } else {
                            alert(response.data.message || '删除失败');
                        }
                    } catch (error) {
                        console.error('删除任务失败：', error);
                        alert('删除失败，请重试');
                    }
                },

                // 切换任务状态
                async toggleActive(task, isActive) {
                    try {
                        const response = await axios.post('/TaskConfig/ToggleActive', {
                            taskId: task.taskId,
                            isActive: isActive
                        });

                        if (response.data.code === 200) {
                            this.loadTasks();
                            alert(response.data.message || '状态切换成功');
                        } else {
                            alert(response.data.message || '状态切换失败');
                        }
                    } catch (error) {
                        console.error('切换任务状态失败：', error);
                        alert('状态切换失败，请重试');
                    }
                },

                // 查看详情
                async viewDetail(task) {
                    try {
                        const response = await axios.get(`/TaskConfig/GetById/${task.taskId}`);
                        if (response.data.code === 200) {
                            this.selectedTask = response.data.data;

                            // 使用Bootstrap 5的方式显示模态框
                            const modal = new bootstrap.Modal(document.getElementById('taskDetailModal'));
                            modal.show();
                        } else {
                            alert(response.data.message || '获取任务详情失败');
                        }
                    } catch (error) {
                        console.error('获取任务详情失败：', error);
                        alert('获取任务详情失败，请重试');
                    }
                },

                // 从详情页面编辑任务
                editTaskFromDetail() {
                    // 关闭详情模态框
                    const detailModal = bootstrap.Modal.getInstance(document.getElementById('taskDetailModal'));
                    detailModal.hide();

                    // 显示编辑模态框
                    setTimeout(() => {
                        this.showEditModal(this.selectedTask);
                    }, 300);
                },

                // 显示奖励配置模态框
                showRewardModal() {
                    const modal = new bootstrap.Modal(document.getElementById('rewardModal'));
                    modal.show();
                },

                // 保存奖励配置
                saveRewardConfig() {
                    // 验证奖励配置
                    for (let i = 0; i < this.taskForm.rewards.length; i++) {
                        const reward = this.taskForm.rewards[i];
                        if (!reward.type) {
                            alert(`第${i + 1}个奖励的类型不能为空`);
                            return;
                        }
                        if (!reward.amount || reward.amount <= 0) {
                            alert(`第${i + 1}个奖励的数量必须大于0`);
                            return;
                        }
                        if ((reward.type === 'item' || reward.type === 'pet' || reward.type === 'skill') && !reward.id) {
                            alert(`第${i + 1}个奖励的ID不能为空`);
                            return;
                        }
                    }

                    // 准备提交数据
                    const submitData = { ...this.taskForm };
                    // 将奖励数组转换为JSON字符串
                    submitData.rewardConfig = this.generateRewardConfig(this.taskForm.rewards);

                    // 提交更新
                    this.updateTaskConfig(submitData);

                    // 关闭模态框
                    const modal = bootstrap.Modal.getInstance(document.getElementById('rewardModal'));
                    modal.hide();
                },

                // 显示任务目标配置模态框
                showObjectiveModal() {
                    const modal = new bootstrap.Modal(document.getElementById('objectiveModal'));
                    modal.show();
                },

                // 保存任务目标配置
                saveObjectiveConfig() {
                    // 验证任务目标配置
                    for (let i = 0; i < this.taskForm.objectives.length; i++) {
                        const objective = this.taskForm.objectives[i];
                        if (!objective.objectiveType) {
                            alert(`第${i + 1}个目标的类型不能为空`);
                            return;
                        }
                        if (!objective.targetAmount || objective.targetAmount <= 0) {
                            alert(`第${i + 1}个目标的数量必须大于0`);
                            return;
                        }
                    }

                    // 准备提交数据
                    const submitData = { ...this.taskForm };
                    // 将奖励数组转换为JSON字符串
                    submitData.rewardConfig = this.generateRewardConfig(this.taskForm.rewards);

                    // 提交更新
                    this.updateTaskConfig(submitData);

                    // 关闭模态框
                    const modal = bootstrap.Modal.getInstance(document.getElementById('objectiveModal'));
                    modal.hide();
                },

                // 从任务列表直接打开奖励配置弹框
                async showRewardModalForTask(task) {
                    try {
                        // 获取任务详细信息
                        const response = await axios.get(`/TaskConfig/GetById/${task.taskId}`);
                        if (response.data.code === 200) {
                            const taskDetail = response.data.data;

                            // 设置当前编辑的任务表单
                            this.taskForm = {
                                taskId: taskDetail.taskId,
                                taskName: taskDetail.taskName,
                                taskDescription: taskDetail.taskDescription,
                                taskType: taskDetail.taskType,
                                isRepeatable: taskDetail.isRepeatable,
                                prerequisiteTask: taskDetail.prerequisiteTask,
                                requiredPet: taskDetail.requiredPet,
                                rewardConfig: taskDetail.rewardConfig,
                                isNetworkTask: taskDetail.isNetworkTask,
                                isActive: taskDetail.isActive,
                                sortOrder: taskDetail.sortOrder,
                                objectives: this.processObjectives(taskDetail.objectives || []),
                                rewards: this.parseRewardConfig(taskDetail.rewardConfig)
                            };

                            // 显示奖励配置弹框
                            const modal = new bootstrap.Modal(document.getElementById('rewardModal'));
                            modal.show();
                        } else {
                            alert('获取任务信息失败：' + response.data.message);
                        }
                    } catch (error) {
                        console.error('获取任务信息失败：', error);
                        alert('获取任务信息失败，请重试');
                    }
                },

                // 从任务列表直接打开任务目标配置弹框
                async showObjectiveModalForTask(task) {
                    try {
                        // 获取任务详细信息
                        const response = await axios.get(`/TaskConfig/GetById/${task.taskId}`);
                        if (response.data.code === 200) {
                            const taskDetail = response.data.data;

                            // 设置当前编辑的任务表单
                            this.taskForm = {
                                taskId: taskDetail.taskId,
                                taskName: taskDetail.taskName,
                                taskDescription: taskDetail.taskDescription,
                                taskType: taskDetail.taskType,
                                isRepeatable: taskDetail.isRepeatable,
                                prerequisiteTask: taskDetail.prerequisiteTask,
                                requiredPet: taskDetail.requiredPet,
                                rewardConfig: taskDetail.rewardConfig,
                                isNetworkTask: taskDetail.isNetworkTask,
                                isActive: taskDetail.isActive,
                                sortOrder: taskDetail.sortOrder,
                                objectives: this.processObjectives(taskDetail.objectives || []),
                                rewards: this.parseRewardConfig(taskDetail.rewardConfig)
                            };

                            // 显示任务目标配置弹框
                            const modal = new bootstrap.Modal(document.getElementById('objectiveModal'));
                            modal.show();
                        } else {
                            alert('获取任务信息失败：' + response.data.message);
                        }
                    } catch (error) {
                        console.error('获取任务信息失败：', error);
                        alert('获取任务信息失败，请重试');
                    }
                },

                // 通用的任务配置更新方法
                async updateTaskConfig(submitData) {
                    try {
                        const response = await axios.post('/TaskConfig/Update', submitData);
                        if (response.data.code === 200) {
                            // 刷新列表
                            await this.loadTasks();
                            alert('配置更新成功');
                        } else {
                            alert('更新失败：' + response.data.message);
                        }
                    } catch (error) {
                        console.error('更新任务配置失败：', error);
                        alert('更新失败，请重试');
                    }
                },

                // 添加任务目标
                addObjective() {
                    this.taskForm.objectives.push({
                        objectiveType: '',
                        targetId: '',
                        targetName: '', // 新增目标名称字段
                        targetAmount: 1,
                        objectiveOrder: this.taskForm.objectives.length,
                        objectiveDescription: '',
                        completeTemplate: '',
                        // 道具选择相关属性
                        searchText: '',
                        showDropdown: false,
                        filteredItems: []
                    });
                },

                // 删除任务目标
                removeObjective(index) {
                    if (confirm('确定要删除这个目标吗？')) {
                        this.taskForm.objectives.splice(index, 1);
                        // 重新排序
                        this.taskForm.objectives.forEach((obj, idx) => {
                            obj.objectiveOrder = idx;
                        });
                    }
                },

                // 添加奖励
                addReward() {
                    this.taskForm.rewards.push({
                        type: '',
                        id: '',
                        amount: 1,
                        name: '',
                        quality: '',
                        // 搜索相关属性
                        searchText: '',
                        showDropdown: false,
                        filteredItems: [],
                        hasMoreResults: false,
                        totalSearchResults: 0,
                        isDefaultView: false
                    });
                },

                // 删除奖励
                removeReward(index) {
                    if (confirm('确定要删除这个奖励吗？')) {
                        this.taskForm.rewards.splice(index, 1);
                    }
                },

                // 切换道具下拉框的显示状态（参考UserItem实现）
                toggleItemDropdown(reward) {
                    reward.showDropdown = !reward.showDropdown;
                    if (reward.showDropdown) {
                        // 显示所有道具，但限制数量避免性能问题
                        const maxDisplayCount = 50;
                        reward.filteredItems = this.itemOptions.slice(0, maxDisplayCount);
                        reward.searchText = ''; // 清空搜索文本
                        console.log('显示道具下拉框，当前道具总数:', this.itemOptions.length, '显示数量:', reward.filteredItems.length);
                    }
                },

                // 过滤道具列表（参考UserItem实现）
                filterItems(reward) {
                    const searchText = reward.searchText.toLowerCase();
                    if (!searchText.trim()) {
                        // 没有搜索文本时，显示前50个道具
                        const maxDisplayCount = 50;
                        reward.filteredItems = this.itemOptions.slice(0, maxDisplayCount);
                        return;
                    }

                    // 搜索匹配的道具，添加类型安全检查
                    const searchResults = this.itemOptions.filter(item => {
                        try {
                            const itemName = (item.name || '').toLowerCase();
                            const itemId = String(item.id || '').toLowerCase();
                            return itemName.includes(searchText) || itemId.includes(searchText);
                        } catch (error) {
                            console.error('过滤道具时出错:', error, item);
                            return false;
                        }
                    });

                    console.log('搜索关键字:', searchText, '找到结果:', searchResults.length, '个');

                    // 限制搜索结果数量
                    const maxSearchResults = 50;
                    reward.filteredItems = searchResults.slice(0, maxSearchResults);
                },

                // 切换目标道具下拉框的显示状态
                toggleObjectiveItemDropdown(objective) {
                    objective.showDropdown = !objective.showDropdown;
                    if (objective.showDropdown) {
                        // 显示所有道具，但限制数量避免性能问题
                        const maxDisplayCount = 50;
                        objective.filteredItems = this.itemOptions.slice(0, maxDisplayCount);
                        objective.searchText = '';
                        console.log('显示目标道具下拉框，当前道具总数:', this.itemOptions.length, '显示数量:', objective.filteredItems.length);
                    }
                },

                // 过滤目标道具列表
                filterObjectiveItems(objective) {
                    const searchText = objective.searchText.toLowerCase();
                    if (!searchText.trim()) {
                        // 没有搜索文本时，显示前50个道具
                        const maxDisplayCount = 50;
                        objective.filteredItems = this.itemOptions.slice(0, maxDisplayCount);
                        return;
                    }

                    // 搜索匹配的道具，添加类型安全检查
                    const searchResults = this.itemOptions.filter(item => {
                        try {
                            const itemName = (item.name || '').toLowerCase();
                            const itemId = String(item.id || '').toLowerCase();
                            return itemName.includes(searchText) || itemId.includes(searchText);
                        } catch (error) {
                            console.error('过滤目标道具时出错:', error, item);
                            return false;
                        }
                    });

                    console.log('搜索目标道具关键字:', searchText, '找到结果:', searchResults.length, '个');

                    // 限制搜索结果数量
                    const maxSearchResults = 50;
                    objective.filteredItems = searchResults.slice(0, maxSearchResults);
                },

                // 获取选中目标道具的显示文本
                getSelectedObjectiveItemDisplay(objective) {
                    if (!objective.targetId) {
                        return '请选择道具';
                    }
                    const item = this.itemOptions.find(item => item.id == objective.targetId);
                    if (!item) {
                        return objective.targetId; // 如果没有找到，显示ID
                    }
                    return `${item.name} (${item.id})`;
                },

                // 选择目标道具
                selectObjectiveItem(objective, item) {
                    objective.targetId = item.id;
                    objective.targetName = item.name; // 设置目标名称
                    objective.showDropdown = false;
                    objective.searchText = ''; // 清空搜索文本
                    objective.filteredItems = [];
                    console.log('选择目标道具:', item.name, '(', item.id, ')');
                },

                // 处理目标对象，确保有必要的属性
                processObjectives(objectives) {
                    return objectives.map(objective => ({
                        ...objective,
                        // 确保有必要的属性
                        targetName: objective.targetName || '',
                        searchText: '',
                        showDropdown: false,
                        filteredItems: []
                    }));
                },

                // 获取选中道具的显示文本（参考UserItem实现）
                getSelectedItemDisplay(reward) {
                    if (!reward.id) {
                        return '请选择道具';
                    }
                    const item = this.itemOptions.find(item => item.id == reward.id);
                    if (!item) {
                        return reward.id; // 如果没有找到，显示ID
                    }
                    return `${item.name} (${item.id})`;
                },

                // 选择道具（参考UserItem实现）
                selectItem(reward, item) {
                    reward.id = item.id;
                    reward.name = item.name;
                    reward.quality = item.quality;
                    reward.showDropdown = false;
                    reward.searchText = ''; // 清空搜索文本
                    reward.filteredItems = [];
                    console.log('选择道具:', item.name, '(', item.id, ')');
                },

                // 处理点击外部关闭下拉框
                handleOutsideClick(event) {
                    const container = event.target.closest('.custom-select-container');
                    if (!container) {
                        // 关闭所有奖励的下拉框
                        this.taskForm.rewards.forEach(reward => {
                            if (reward.showDropdown) {
                                reward.showDropdown = false;
                            }
                        });
                        // 关闭所有目标的下拉框
                        this.taskForm.objectives.forEach(objective => {
                            if (objective.showDropdown) {
                                objective.showDropdown = false;
                            }
                        });
                    }
                },

                // 获取道具品质样式类
                getItemQualityClass(quality) {
                    return `cyber-badge-quality-${quality || 1}`;
                },

                // 获取道具品质名称
                getItemQualityName(quality) {
                    const qualityNames = {
                        1: '普通',
                        2: '优秀',
                        3: '稀有',
                        4: '史诗',
                        5: '传说'
                    };
                    return qualityNames[quality] || '普通';
                },

                // 解析奖励配置JSON为奖励数组
                parseRewardConfig(rewardConfigJson) {
                    if (!rewardConfigJson) return [];

                    try {
                        const config = JSON.parse(rewardConfigJson);
                        const rewards = [];

                        // 处理各种奖励类型
                        if (config.exp) {
                            rewards.push({
                                type: 'exp',
                                id: '',
                                amount: config.exp,
                                name: '',
                                quality: '',
                                searchText: '',
                                showDropdown: false,
                                filteredItems: [],
                                hasMoreResults: false,
                                totalSearchResults: 0,
                                isDefaultView: false
                            });
                        }
                        if (config.gold) {
                            rewards.push({
                                type: 'gold',
                                id: '',
                                amount: config.gold,
                                name: '',
                                quality: '',
                                searchText: '',
                                showDropdown: false,
                                filteredItems: [],
                                hasMoreResults: false,
                                totalSearchResults: 0,
                                isDefaultView: false
                            });
                        }
                        if (config.items && Array.isArray(config.items)) {
                            config.items.forEach(item => {
                                const itemName = item.name || '';
                                const itemId = item.id || '';
                                rewards.push({
                                    type: 'item',
                                    id: itemId,
                                    amount: item.amount || 1,
                                    name: itemName,
                                    quality: item.quality || '',
                                    searchText: itemName ? `${itemName} (${itemId})` : itemId,
                                    showDropdown: false,
                                    filteredItems: [],
                                    hasMoreResults: false,
                                    totalSearchResults: 0,
                                    isDefaultView: false
                                });
                            });
                        }
                        if (config.pets && Array.isArray(config.pets)) {
                            config.pets.forEach(pet => {
                                rewards.push({
                                    type: 'pet',
                                    id: pet.id || '',
                                    amount: pet.amount || 1,
                                    name: pet.name || '',
                                    quality: '',
                                    searchText: '',
                                    showDropdown: false,
                                    filteredItems: [],
                                    hasMoreResults: false,
                                    totalSearchResults: 0,
                                    isDefaultView: false
                                });
                            });
                        }
                        if (config.skills && Array.isArray(config.skills)) {
                            config.skills.forEach(skill => {
                                rewards.push({
                                    type: 'skill',
                                    id: skill.id || '',
                                    amount: skill.level || 1,
                                    name: skill.name || '',
                                    quality: '',
                                    searchText: '',
                                    showDropdown: false,
                                    filteredItems: [],
                                    hasMoreResults: false,
                                    totalSearchResults: 0,
                                    isDefaultView: false
                                });
                            });
                        }

                        return rewards;
                    } catch (error) {
                        console.error('解析奖励配置失败：', error);
                        return [];
                    }
                },

                // 将奖励数组转换为JSON配置
                generateRewardConfig(rewards) {
                    if (!rewards || rewards.length === 0) return '';

                    const config = {};
                    const items = [];
                    const pets = [];
                    const skills = [];

                    rewards.forEach(reward => {
                        switch (reward.type) {
                            case 'exp':
                                config.exp = parseInt(reward.amount);
                                break;
                            case 'gold':
                                config.gold = parseInt(reward.amount);
                                break;
                            case 'item':
                                const item = {
                                    id: reward.id,
                                    amount: parseInt(reward.amount)
                                };
                                if (reward.name) item.name = reward.name;
                                if (reward.quality) item.quality = parseInt(reward.quality);
                                items.push(item);
                                break;
                            case 'pet':
                                const pet = {
                                    id: reward.id,
                                    amount: parseInt(reward.amount)
                                };
                                if (reward.name) pet.name = reward.name;
                                pets.push(pet);
                                break;
                            case 'skill':
                                const skill = {
                                    id: reward.id,
                                    level: parseInt(reward.amount)
                                };
                                if (reward.name) skill.name = reward.name;
                                skills.push(skill);
                                break;
                        }
                    });

                    if (items.length > 0) config.items = items;
                    if (pets.length > 0) config.pets = pets;
                    if (skills.length > 0) config.skills = skills;

                    return JSON.stringify(config);
                },

                // 获取奖励类型名称
                getRewardTypeName(type) {
                    const typeNames = {
                        'exp': '经验值',
                        'gold': '金币',
                        'item': '道具',
                        'pet': '宠物',
                        'skill': '技能'
                    };
                    return typeNames[type] || type;
                },

                // 获取品质名称
                getQualityName(quality) {
                    const qualityNames = {
                        '1': '普通',
                        '2': '优秀',
                        '3': '稀有',
                        '4': '史诗',
                        '5': '传说'
                    };
                    return qualityNames[quality] || quality;
                },

                // 获取品质徽章样式
                getQualityBadgeClass(quality) {
                    const qualityClasses = {
                        '1': 'cyber-badge-secondary',
                        '2': 'cyber-badge-success',
                        '3': 'cyber-badge-info',
                        '4': 'cyber-badge-warning',
                        '5': 'cyber-badge-danger'
                    };
                    return qualityClasses[quality] || 'cyber-badge-secondary';
                },

                // 获取道具类型文本（用于前端显示）
                getItemTypeText(type) {
                    const typeMap = {
                        'weapon': '武器',
                        'armor': '装备',
                        'consumable': '消耗品',
                        'material': '材料',
                        'quest': '任务物品',
                        'other': '其它'
                    };
                    return typeMap[type] || '其它';
                },

                // 格式化日期时间
                formatDateTime(dateStr) {
                    if (!dateStr) return '';
                    const date = new Date(dateStr);
                    return date.toLocaleString('zh-CN');
                },

                // 加载下拉选项
                async loadOptions() {
                    try {
                        // 加载任务类型选项
                        const typeResponse = await axios.get('/TaskConfig/GetTaskTypeOptions');
                        if (typeResponse.data.code === 200) {
                            this.taskTypeOptions = typeResponse.data.data;
                        }

                        // 加载任务选项
                        const taskResponse = await axios.get('/TaskConfig/GetTaskOptions');
                        if (taskResponse.data.code === 200) {
                            this.taskOptions = taskResponse.data.data;
                        }

                        // 加载目标类型选项
                        this.objectiveTypeOptions = [
                            { value: 'KILL_MONSTER', label: '击杀怪物' },
                            { value: 'COLLECT_ITEM', label: '收集道具' },
                            { value: 'REACH_LEVEL', label: '达到等级' },
                            { value: 'VISIT_MAP', label: '访问地图' },
                            { value: 'USE_SKILL', label: '使用技能' },
                            { value: 'EQUIP_ITEM', label: '装备道具' },
                            { value: 'COMPLETE_DUNGEON', label: '完成副本' },
                            { value: 'PET_EVOLUTION', label: '宠物进化' },
                            { value: 'ENHANCE_EQUIPMENT', label: '强化装备' },
                            { value: 'TALK_TO_NPC', label: '与NPC对话' }
                        ];

                        // 加载道具选项
                        await this.loadItemOptions();
                    } catch (error) {
                        console.error('加载选项失败：', error);
                    }
                },

                // 获取道具类型文本（需要在loadItemOptions之前定义）
                getItemTypeText(type) {
                    const typeMap = {
                        'weapon': '武器',
                        'armor': '装备',
                        'consumable': '消耗品',
                        'material': '材料',
                        'quest': '任务物品',
                        'other': '其它'
                    };
                    return typeMap[type] || '其它';
                },

                // 转换品质格式（从UserItem的格式转换为数字格式）
                convertQuality(quality) {
                    const qualityMap = {
                        'white': 1,    // 普通
                        'green': 2,    // 精良
                        'blue': 3,     // 稀有
                        'purple': 4,   // 史诗
                        'orange': 5    // 传说
                    };
                    return qualityMap[quality] || 1;
                },

                // 加载道具选项
                async loadItemOptions() {
                    try {
                        // 使用与UserItem页面相同的接口获取道具配置
                        const response = await axios.get('/UserItem/GetItemConfigs');
                        if (response.data.success) {
                            const itemConfigs = response.data.data || [];
                            // 转换为奖励配置所需的格式
                            this.itemOptions = itemConfigs.map(item => ({
                                id: String(item.itemNo),   // 确保ID是字符串类型
                                name: item.name,           // 道具名称
                                quality: this.convertQuality(item.quality), // 转换品质格式
                                description: item.description || `${this.getItemTypeText(item.type)} | 价格: ${item.price || 0}`,
                                type: item.type,           // 道具类型
                                price: item.price || 0     // 道具价格
                            }));
                            console.log('道具配置加载成功，共', this.itemOptions.length, '个道具');
                            console.log('道具列表详情：', this.itemOptions);
                        } else {
                            console.error('加载道具配置失败：', response.data.message);
                            this.loadFallbackItems();
                        }
                    } catch (error) {
                        console.error('加载道具选项失败：', error);
                        this.loadFallbackItems();
                    }
                },



                // 加载备用道具数据（当接口失败时使用）
                loadFallbackItems() {
                    console.log('使用备用道具数据');
                    this.itemOptions = [
                        { id: 'ITEM_001', name: '生命药水', quality: 1, description: '恢复100点生命值', type: 'consumable', price: 10 },
                        { id: 'ITEM_002', name: '魔法药水', quality: 2, description: '恢复100点魔法值', type: 'consumable', price: 15 },
                        { id: 'ITEM_003', name: '力量药水', quality: 2, description: '临时增加力量属性', type: 'consumable', price: 20 },
                        { id: 'ITEM_004', name: '钢铁剑', quality: 3, description: '攻击力+50的武器', type: 'weapon', price: 100 },
                        { id: 'ITEM_005', name: '魔法法杖', quality: 3, description: '魔法攻击力+80的法杖', type: 'weapon', price: 120 },
                        { id: 'ITEM_006', name: '龙鳞甲', quality: 4, description: '防御力+120的护甲', type: 'armor', price: 200 },
                        { id: 'ITEM_007', name: '凤凰羽毛', quality: 4, description: '复活道具，满血复活', type: 'consumable', price: 500 },
                        { id: 'ITEM_008', name: '神圣之剑', quality: 5, description: '传说级武器，攻击力+200', type: 'weapon', price: 1000 },
                        { id: 'ITEM_009', name: '时空宝石', quality: 5, description: '传说级饰品，全属性+50', type: 'other', price: 2000 },
                        { id: 'ITEM_010', name: '经验卷轴', quality: 1, description: '使用后获得1000经验值', type: 'consumable', price: 5 }
                    ];
                }
            },



            async mounted() {
                await this.loadOptions();
                await this.loadTasks();

                // 添加点击外部关闭下拉框的事件监听
                document.addEventListener('click', this.handleOutsideClick);
            },

            beforeUnmount() {
                // 清理事件监听
                document.removeEventListener('click', this.handleOutsideClick);
            }
        }).mount('#taskConfigApp');
    </script>
}
