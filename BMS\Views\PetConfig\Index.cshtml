@{
    ViewData["Title"] = "宠物配置管理";
}

<!-- 科幻背景 -->
<div class="cyber-bg"></div>

<div id="petConfigApp">
    <!-- 主控制台 -->
    <div class="cyber-card">
        <div class="cyber-card-header">
            <div class="cyber-icon">
                <i class="fas fa-dog"></i>
            </div>
            <h5 class="cyber-card-title">宠物配置控制台</h5>
            <div class="ms-auto">
                <button type="button" class="cyber-btn cyber-btn-success" v-on:click="showCreateModal">
                    <i class="fas fa-plus"></i> 新增宠物配置
                </button>
            </div>
        </div>

        <div class="container-fluid">
            <!-- 查询控制面板 -->
            <div class="cyber-card">
                <div class="cyber-card-header">
                    <div class="cyber-icon">
                        <i class="fas fa-search"></i>
                    </div>
                    <h5 class="cyber-card-title">查询控制面板</h5>
                </div>
                <div>
                    <div class="row">
                        <div class="col-md-3">
                            <div class="cyber-form-group">
                                <label class="cyber-form-label">宠物编号</label>
                                <input type="number" class="cyber-form-control" v-model="queryForm.petNo" placeholder="请输入宠物编号">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="cyber-form-group">
                                <label class="cyber-form-label">宠物名称</label>
                                <input type="text" class="cyber-form-control" v-model="queryForm.name" placeholder="请输入宠物名称">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="cyber-form-group">
                                <label class="cyber-form-label">宠物属性</label>
                                <div class="cyber-select-wrapper">
                                    <select class="cyber-form-control" v-model="queryForm.attribute">
                                        <option value="">请选择属性</option>
                                        <option value="金">金</option>
                                        <option value="木">木</option>
                                        <option value="水">水</option>
                                        <option value="火">火</option>
                                        <option value="土">土</option>
                                        <option value="神">神</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 d-flex align-items-end">
                            <div class="cyber-form-group">
                                <button type="button" class="cyber-btn me-2" v-on:click="loadData">
                                    <i class="fas fa-search"></i> 查询
                                </button>
                                <button type="button" class="cyber-btn cyber-btn-outline" v-on:click="resetQuery">
                                    <i class="fas fa-redo"></i> 重置
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 宠物数据表 -->
            <div class="cyber-card">
                <div class="cyber-card-header">
                    <div class="cyber-icon">
                        <i class="fas fa-database"></i>
                    </div>
                    <h5 class="cyber-card-title">宠物数据表</h5>
                    <div class="ms-auto">
                        <span class="badge" style="background: linear-gradient(135deg, var(--cyber-blue), var(--cyber-purple)); color: white; padding: 0.5rem 1rem; border-radius: 12px;">
                            共 {{ totalCount || 0 }} 条记录
                        </span>
                    </div>
                </div>
                <div class="cyber-table-container">
                    <table class="cyber-table">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>宠物编号</th>
                                <th>宠物名称</th>
                                <th>宠物属性</th>
                                <th>技能编号</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr v-if="loading">
                                <td colspan="6" class="text-center" style="color: var(--text-secondary);">数据加载中...</td>
                            </tr>
                            <tr v-else-if="!petConfigs || petConfigs.length === 0">
                                <td colspan="6" class="text-center" style="color: var(--text-secondary);">暂无数据</td>
                            </tr>
                            <tr v-else v-for="petConfig in petConfigs" v-bind:key="petConfig.id">
                                <td><span style="font-weight: 600; color: var(--cyber-blue);">{{ petConfig.id }}</span></td>
                                <td><strong style="color: var(--text-primary);">{{ petConfig.petNo }}</strong></td>
                                <td style="color: var(--text-primary);">{{ petConfig.name }}</td>
                                <td>
                                    <span class="badge" style="background: linear-gradient(135deg, var(--cyber-orange), var(--cyber-pink)); color: white; padding: 0.25rem 0.75rem; border-radius: 8px; font-size: 0.75rem;">
                                        {{ petConfig.attribute }}
                                    </span>
                                </td>
                                <td style="color: var(--text-secondary);">{{ petConfig.skill || '无' }}</td>
                                <td>
                                    <div style="display: flex; gap: 0.25rem;">
                                        <button type="button" class="cyber-btn cyber-btn-warning cyber-btn-sm" v-on:click="showEditModal(petConfig)" title="编辑">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button type="button" class="cyber-btn cyber-btn-danger cyber-btn-sm" v-on:click="deletePetConfig(petConfig.id)" title="删除">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- 分页控制 -->
                <div v-if="totalCount > 0" style="padding: 1.5rem; border-top: 1px solid rgba(255, 255, 255, 0.1);">
                    <div class="row align-items-center">
                        <div class="col-sm-12 col-md-5">
                            <div style="color: var(--text-secondary); font-weight: 500;">
                                显示第 {{ (currentPage - 1) * pageSize + 1 }} 到 {{ Math.min(currentPage * pageSize, totalCount) }} 条，共 {{ totalCount }} 条记录
                            </div>
                        </div>
                        <div class="col-sm-12 col-md-7">
                            <div class="float-end">
                                <ul class="cyber-pagination">
                                    <li class="page-item" v-bind:class="{ disabled: currentPage <= 1 }">
                                        <a href="#" class="page-link" v-on:click.prevent="changePage(currentPage - 1)">上一页</a>
                                    </li>
                                    <li class="page-item" v-for="page in visiblePages" v-bind:key="page" v-bind:class="{ active: page === currentPage }">
                                        <a href="#" class="page-link" v-on:click.prevent="changePage(page)">{{ page }}</a>
                                    </li>
                                    <li class="page-item" v-bind:class="{ disabled: currentPage >= totalPages }">
                                        <a href="#" class="page-link" v-on:click.prevent="changePage(currentPage + 1)">下一页</a>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 新增宠物配置弹框 -->
    <div class="modal fade cyber-modal" id="createModal" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-plus me-2"></i>新增宠物配置
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="cyber-form-group">
                        <label class="cyber-form-label">宠物编号 <span style="color: var(--cyber-red);">*</span></label>
                        <input type="number" class="cyber-form-control" v-model="createForm.petNo" placeholder="请输入宠物编号">
                    </div>
                    <div class="cyber-form-group">
                        <label class="cyber-form-label">宠物名称 <span style="color: var(--cyber-red);">*</span></label>
                        <input type="text" class="cyber-form-control" v-model="createForm.name" placeholder="请输入宠物名称">
                    </div>
                    <div class="cyber-form-group">
                        <label class="cyber-form-label">宠物属性 <span style="color: var(--cyber-red);">*</span></label>
                        <div class="cyber-select-wrapper">
                            <select class="cyber-form-control" v-model="createForm.attribute">
                                <option value="">请选择属性</option>
                                <option value="金">金</option>
                                <option value="木">木</option>
                                <option value="水">水</option>
                                <option value="火">火</option>
                                <option value="土">土</option>
                                <option value="神">神</option>
                            </select>
                        </div>
                    </div>
                    <div class="cyber-form-group">
                        <label class="cyber-form-label">技能编号</label>
                        <input type="text" class="cyber-form-control" v-model="createForm.skill" placeholder="请输入技能编号（多个用逗号分隔）">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="cyber-btn cyber-btn-outline" data-bs-dismiss="modal">
                        <i class="fas fa-times"></i> 取消
                    </button>
                    <button type="button" class="cyber-btn cyber-btn-success" v-on:click="createPetConfig" v-bind:disabled="submitting">
                        <i class="fas fa-check"></i> {{ submitting ? '提交中...' : '确定' }}
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 编辑宠物配置弹框 -->
    <div class="modal fade cyber-modal" id="editModal" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-edit me-2"></i>编辑宠物配置
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="cyber-form-group">
                        <label class="cyber-form-label">宠物编号 <span style="color: var(--cyber-red);">*</span></label>
                        <input type="number" class="cyber-form-control" v-model="editForm.petNo" placeholder="请输入宠物编号">
                    </div>
                    <div class="cyber-form-group">
                        <label class="cyber-form-label">宠物名称 <span style="color: var(--cyber-red);">*</span></label>
                        <input type="text" class="cyber-form-control" v-model="editForm.name" placeholder="请输入宠物名称">
                    </div>
                    <div class="cyber-form-group">
                        <label class="cyber-form-label">宠物属性 <span style="color: var(--cyber-red);">*</span></label>
                        <div class="cyber-select-wrapper">
                            <select class="cyber-form-control" v-model="editForm.attribute">
                                <option value="">请选择属性</option>
                                <option value="金">金</option>
                                <option value="木">木</option>
                                <option value="水">水</option>
                                <option value="火">火</option>
                                <option value="土">土</option>
                                <option value="神">神</option>
                            </select>
                        </div>
                    </div>
                    <div class="cyber-form-group">
                        <label class="cyber-form-label">技能编号</label>
                        <input type="text" class="cyber-form-control" v-model="editForm.skill" placeholder="请输入技能编号（多个用逗号分隔）">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="cyber-btn cyber-btn-outline" data-bs-dismiss="modal">
                        <i class="fas fa-times"></i> 取消
                    </button>
                    <button type="button" class="cyber-btn cyber-btn-success" v-on:click="updatePetConfig" v-bind:disabled="submitting">
                        <i class="fas fa-check"></i> {{ submitting ? '提交中...' : '确定' }}
                    </button>
                </div>
            </div>
        </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const { createApp } = Vue;
    
    createApp({
        data() {
            return {
                loading: false,
                submitting: false,
                petConfigs: [],
                currentPage: 1,
                pageSize: 10,
                totalCount: 0,
                totalPages: 0,
                queryForm: {
                    petNo: null,
                    name: '',
                    attribute: ''
                },
                createForm: {
                    petNo: null,
                    name: '',
                    attribute: '',
                    skill: ''
                },
                editForm: {
                    id: 0,
                    petNo: null,
                    name: '',
                    attribute: '',
                    skill: ''
                }
            };
        },
        computed: {
            visiblePages() {
                if (!this.totalPages || this.totalPages <= 0) {
                    return [];
                }
                const start = Math.max(1, this.currentPage - 2);
                const end = Math.min(this.totalPages, this.currentPage + 2);
                const pages = [];
                for (let i = start; i <= end; i++) {
                    pages.push(i);
                }
                return pages;
            }
        },
        mounted() {
            this.loadData();
        },
        methods: {
            // 加载数据
            async loadData() {
                this.loading = true;
                try {
                    const response = await axios.post('/PetConfig/GetList', {
                        petNo: this.queryForm.petNo,
                        name: this.queryForm.name,
                        attribute: this.queryForm.attribute,
                        page: this.currentPage,
                        pageSize: this.pageSize
                    });
                    
                    if (response.data.success) {
                        this.petConfigs = response.data.data.data || [];
                        this.totalCount = response.data.data.totalCount || 0;
                        this.totalPages = response.data.data.totalPages || 0;
                    } else {
                        alert(response.data.message);
                        this.petConfigs = [];
                        this.totalCount = 0;
                        this.totalPages = 0;
                    }
                } catch (error) {
                    console.error('加载数据失败：', error);
                    alert('加载数据失败，请重试');
                    // 确保在错误情况下重置数据
                    this.petConfigs = [];
                    this.totalCount = 0;
                    this.totalPages = 0;
                } finally {
                    this.loading = false;
                }
            },
            
            // 重置查询条件
            resetQuery() {
                this.queryForm = {
                    petNo: null,
                    name: '',
                    attribute: ''
                };
                this.currentPage = 1;
                this.loadData();
            },
            
            // 显示新增模态框
            showCreateModal() {
                this.createForm = {
                    petNo: null,
                    name: '',
                    attribute: '',
                    skill: ''
                };
                $('#createModal').modal('show');
            },
            
            // 显示编辑模态框
            showEditModal(item) {
                this.editForm = {
                    id: item.id,
                    petNo: item.petNo,
                    name: item.name,
                    attribute: item.attribute,
                    skill: item.skill || ''
                };
                $('#editModal').modal('show');
            },
            
            // 创建宠物配置
            async createPetConfig() {
                if (!this.validateCreateForm()) {
                    return;
                }
                
                this.submitting = true;
                try {
                    const response = await axios.post('/PetConfig/Create', this.createForm);
                    
                    if (response.data.success) {
                        alert('宠物配置创建成功');
                        $('#createModal').modal('hide');
                        this.loadData();
                    } else {
                        alert(response.data.message);
                    }
                } catch (error) {
                    console.error('创建宠物配置失败：', error);
                    alert('创建宠物配置失败，请重试');
                } finally {
                    this.submitting = false;
                }
            },
            
            // 更新宠物配置
            async updatePetConfig() {
                if (!this.validateEditForm()) {
                    return;
                }
                
                this.submitting = true;
                try {
                    const response = await axios.post('/PetConfig/Update', this.editForm);
                    
                    if (response.data.success) {
                        alert('宠物配置更新成功');
                        $('#editModal').modal('hide');
                        this.loadData();
                    } else {
                        alert(response.data.message);
                    }
                } catch (error) {
                    console.error('更新宠物配置失败：', error);
                    alert('更新宠物配置失败，请重试');
                } finally {
                    this.submitting = false;
                }
            },
            
            // 删除宠物配置
            async deletePetConfig(id) {
                if (!confirm('确定要删除这个宠物配置吗？此操作不可撤销。')) {
                    return;
                }
                
                try {
                    const response = await axios.post('/PetConfig/Delete', { id: id });
                    
                    if (response.data.success) {
                        alert('宠物配置删除成功');
                        this.loadData();
                    } else {
                        alert(response.data.message);
                    }
                } catch (error) {
                    console.error('删除宠物配置失败：', error);
                    alert('删除宠物配置失败，请重试');
                }
            },
            
            // 验证创建表单
            validateCreateForm() {
                if (!this.createForm.petNo) {
                    alert('请输入宠物编号');
                    return false;
                }
                if (!this.createForm.name) {
                    alert('请输入宠物名称');
                    return false;
                }
                if (!this.createForm.attribute) {
                    alert('请选择宠物属性');
                    return false;
                }
                return true;
            },
            
            // 验证编辑表单
            validateEditForm() {
                if (!this.editForm.petNo) {
                    alert('请输入宠物编号');
                    return false;
                }
                if (!this.editForm.name) {
                    alert('请输入宠物名称');
                    return false;
                }
                if (!this.editForm.attribute) {
                    alert('请选择宠物属性');
                    return false;
                }
                return true;
            },
            
            // 切换页码
            changePage(page) {
                if (!this.totalPages || page < 1 || page > this.totalPages || page === this.currentPage) {
                    return;
                }
                this.currentPage = page;
                this.loadData();
            }
        }
    }).mount('#petConfigApp');
});
</script> 