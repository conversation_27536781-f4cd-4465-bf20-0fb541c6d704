using BMS.Models.DTOs;
using BMS.Models.Common;
using BMS.Models.Entities;
using BMS.Services.Interfaces;

namespace BMS.Services.Implementations
{
    /// <summary>
    /// 怪物配置服务实现
    /// </summary>
    public class MonsterConfigService : IMonsterConfigService
    {
        private readonly IDbService _dbService;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="dbService">数据库服务</param>
        public MonsterConfigService(IDbService dbService)
        {
            _dbService = dbService;
        }

        /// <summary>
        /// 分页获取怪物配置列表
        /// </summary>
        /// <param name="queryDto">查询条件</param>
        /// <returns>分页结果</returns>
        public async Task<PagedResult<MonsterConfigDto>> GetPagedListAsync(MonsterConfigQueryDto queryDto)
        {
            var query = _dbService.Queryable<monster_config>();

            // 添加查询条件
            if (queryDto.MonsterNo.HasValue)
            {
                query = query.Where(x => x.monster_no == queryDto.MonsterNo.Value);
            }

            if (!string.IsNullOrEmpty(queryDto.Name))
            {
                query = query.Where(x => x.name.Contains(queryDto.Name));
            }

            if (!string.IsNullOrEmpty(queryDto.Attribute))
            {
                query = query.Where(x => x.attribute == queryDto.Attribute);
            }

            // 获取总数
            var totalCount = await query.CountAsync();

            // 分页查询
            var items = await query
                .OrderBy(x => x.monster_no)
                .Skip((queryDto.Page - 1) * queryDto.PageSize)
                .Take(queryDto.PageSize)
                .ToListAsync();

            // 转换为DTO
            var dtoList = items.Select(ConvertToDto).ToList();

            return new PagedResult<MonsterConfigDto>(dtoList, queryDto.Page, queryDto.PageSize, totalCount);
        }

        /// <summary>
        /// 根据ID获取怪物配置
        /// </summary>
        /// <param name="id">怪物配置ID</param>
        /// <returns>怪物配置信息</returns>
        public async Task<MonsterConfigDto?> GetByIdAsync(int id)
        {
            var entity = await _dbService.Queryable<monster_config>()
                .FirstAsync(x => x.id == id);

            return entity != null ? ConvertToDto(entity) : null;
        }

        /// <summary>
        /// 根据怪物编号获取怪物配置
        /// </summary>
        /// <param name="monsterNo">怪物编号</param>
        /// <returns>怪物配置信息</returns>
        public async Task<MonsterConfigDto?> GetByMonsterNoAsync(int monsterNo)
        {
            var entity = await _dbService.Queryable<monster_config>()
                .FirstAsync(x => x.monster_no == monsterNo);

            return entity != null ? ConvertToDto(entity) : null;
        }

        /// <summary>
        /// 获取所有怪物配置列表（不分页）
        /// </summary>
        /// <returns>怪物配置列表</returns>
        public async Task<List<MonsterConfigDto>> GetAllAsync()
        {
            var entities = await _dbService.Queryable<monster_config>()
                .OrderBy(x => x.monster_no)
                .ToListAsync();

            return entities.Select(ConvertToDto).ToList();
        }

        /// <summary>
        /// 创建怪物配置
        /// </summary>
        /// <param name="createDto">创建DTO</param>
        /// <returns>操作结果</returns>
        public async Task<ApiResult<bool>> CreateAsync(MonsterConfigCreateDto createDto)
        {
            try
            {
                // 检查怪物编号是否已存在
                var exists = await CheckMonsterNoExistsAsync(createDto.MonsterNo);
                if (exists)
                {
                    return ApiResult<bool>.Fail("该怪物编号已存在");
                }

                // 转换为实体
                var entity = ConvertToEntity(createDto);

                // 插入数据
                var result = await _dbService.Insertable(entity)
                    .ExecuteReturnBigIdentityAsync();

                if (result > 0)
                {
                    return ApiResult<bool>.Ok(true, "怪物配置创建成功");
                }
                else
                {
                    return ApiResult<bool>.Fail("怪物配置创建失败");
                }
            }
            catch (Exception ex)
            {
                return ApiResult<bool>.Fail($"创建怪物配置时发生错误：{ex.Message}");
            }
        }

        /// <summary>
        /// 更新怪物配置
        /// </summary>
        /// <param name="updateDto">更新DTO</param>
        /// <returns>操作结果</returns>
        public async Task<ApiResult<bool>> UpdateAsync(MonsterConfigUpdateDto updateDto)
        {
            try
            {
                // 检查记录是否存在
                var exists = await _dbService.Queryable<monster_config>()
                    .AnyAsync(x => x.id == updateDto.Id);
                if (!exists)
                {
                    return ApiResult<bool>.Fail("怪物配置不存在");
                }

                // 检查怪物编号是否被其他记录使用
                var monsterNoExists = await CheckMonsterNoExistsAsync(updateDto.MonsterNo, updateDto.Id);
                if (monsterNoExists)
                {
                    return ApiResult<bool>.Fail("该怪物编号已被其他记录使用");
                }

                // 转换为实体
                var entity = ConvertToEntity(updateDto);

                // 更新数据
                var result = await _dbService.Updateable(entity)
                    .ExecuteCommandAsync();

                if (result > 0)
                {
                    return ApiResult<bool>.Ok(true, "怪物配置更新成功");
                }
                else
                {
                    return ApiResult<bool>.Fail("怪物配置更新失败");
                }
            }
            catch (Exception ex)
            {
                return ApiResult<bool>.Fail($"更新怪物配置时发生错误：{ex.Message}");
            }
        }

        /// <summary>
        /// 删除怪物配置
        /// </summary>
        /// <param name="id">怪物配置ID</param>
        /// <returns>操作结果</returns>
        public async Task<ApiResult<bool>> DeleteAsync(int id)
        {
            try
            {
                // 检查记录是否存在
                var exists = await _dbService.Queryable<monster_config>()
                    .AnyAsync(x => x.id == id);
                if (!exists)
                {
                    return ApiResult<bool>.Fail("怪物配置不存在");
                }

                // 删除数据
                var result = await _dbService.Deleteable<monster_config>()
                    .Where(x => x.id == id)
                    .ExecuteCommandAsync();

                if (result > 0)
                {
                    return ApiResult<bool>.Ok(true, "怪物配置删除成功");
                }
                else
                {
                    return ApiResult<bool>.Fail("怪物配置删除失败");
                }
            }
            catch (Exception ex)
            {
                return ApiResult<bool>.Fail($"删除怪物配置时发生错误：{ex.Message}");
            }
        }

        /// <summary>
        /// 检查怪物编号是否存在
        /// </summary>
        /// <param name="monsterNo">怪物编号</param>
        /// <param name="id">排除的ID（编辑时使用）</param>
        /// <returns>是否存在</returns>
        public async Task<bool> CheckMonsterNoExistsAsync(int monsterNo, int? id = null)
        {
            var query = _dbService.Queryable<monster_config>()
                .Where(x => x.monster_no == monsterNo);

            if (id.HasValue)
            {
                query = query.Where(x => x.id != id.Value);
            }

            return await query.AnyAsync();
        }

        /// <summary>
        /// 实体转换为DTO
        /// </summary>
        /// <param name="entity">实体</param>
        /// <returns>DTO</returns>
        private static MonsterConfigDto ConvertToDto(monster_config entity)
        {
            return new MonsterConfigDto
            {
                Id = entity.id,
                MonsterNo = entity.monster_no,
                Name = entity.name ?? string.Empty,
                Attribute = entity.attribute ?? string.Empty,
                Skill = entity.skill
            };
        }

        /// <summary>
        /// 创建DTO转换为实体
        /// </summary>
        /// <param name="createDto">创建DTO</param>
        /// <returns>实体</returns>
        private static monster_config ConvertToEntity(MonsterConfigCreateDto createDto)
        {
            return new monster_config
            {
                monster_no = createDto.MonsterNo,
                name = createDto.Name,
                attribute = createDto.Attribute,
                skill = createDto.Skill
            };
        }

        /// <summary>
        /// 更新DTO转换为实体
        /// </summary>
        /// <param name="updateDto">更新DTO</param>
        /// <returns>实体</returns>
        private static monster_config ConvertToEntity(MonsterConfigUpdateDto updateDto)
        {
            return new monster_config
            {
                id = updateDto.Id,
                monster_no = updateDto.MonsterNo,
                name = updateDto.Name,
                attribute = updateDto.Attribute,
                skill = updateDto.Skill
            };
        }
    }
} 