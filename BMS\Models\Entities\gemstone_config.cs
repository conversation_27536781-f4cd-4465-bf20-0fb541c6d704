﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace BMS.Models.Entities
{
    ///<summary>
    ///宝石配置表
    ///</summary>
    [SugarTable("gemstone_config")]
    public partial class gemstone_config
    {
           public gemstone_config(){


           }
           /// <summary>
           /// Desc:自增主键
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true,IsIdentity=true)]
           public int id {get;set;}

           /// <summary>
           /// Desc:宝石类型名称（红宝石/蓝宝石/混元石等）
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string type_name {get;set;}

           /// <summary>
           /// Desc:提升属性类型（攻击/命中/防御/速度/闪避/生命/魔法等）
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string up_type {get;set;}

           /// <summary>
           /// Desc:提升数值（具体的属性加成值）
           /// Default:
           /// Nullable:False
           /// </summary>           
           public decimal up_num {get;set;}

           /// <summary>
           /// Desc:宝石等级（1-3级，对应一孔/二孔/三孔）
           /// Default:
           /// Nullable:False
           /// </summary>           
           public int level {get;set;}

           /// <summary>
           /// Desc:宝石颜色（红色/蓝色/绿色/紫色/金色等）
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? color {get;set;}

           /// <summary>
           /// Desc:对应道具ID（关联item_config表）
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? prop_id {get;set;}

           /// <summary>
           /// Desc:宝石分类（一孔宝石/二孔宝石/三孔宝石）
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string type_class {get;set;}

           /// <summary>
           /// Desc:可镶嵌装备类型（JSON数组格式）
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? equip_types {get;set;}

           /// <summary>
           /// Desc:可镶嵌位置（JSON数组，1/2/3对应槽位）
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? positions {get;set;}

           /// <summary>
           /// Desc:排序号（显示顺序）
           /// Default:0
           /// Nullable:True
           /// </summary>           
           public int? order_num {get;set;}

           /// <summary>
           /// Desc:创建时间
           /// Default:CURRENT_TIMESTAMP
           /// Nullable:True
           /// </summary>           
           public DateTime? create_time {get;set;}

    }
}
