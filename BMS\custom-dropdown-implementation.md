# 自定义道具下拉框实现说明

## 概述
参考UserItem页面中添加用户道具弹框的道具选择下拉实现方式，重新设计了TaskConfig奖励配置中的道具下拉框，解决了原有的搜索功能问题和用户体验问题。

## 实现对比

### 原实现（有问题）
```html
<!-- 原实现：直接输入框 + 悬浮下拉框 -->
<input v-model="reward.searchText" @input="searchItems(reward, $event.target.value)">
<div v-if="reward.showDropdown" class="cyber-dropdown">
    <!-- 搜索结果 -->
</div>
```

**问题：**
- 事件绑定冲突（v-model + @input）
- 搜索功能不稳定
- 用户体验差
- 性能问题

### 新实现（参考UserItem）
```html
<!-- 新实现：自定义选择器 -->
<div class="custom-select-container" :class="{ open: reward.showDropdown }">
    <input :value="getSelectedItemDisplay(reward)" @click="toggleItemDropdown(reward)" readonly>
    <i class="fas fa-chevron-down custom-select-arrow"></i>
    <div class="custom-select-dropdown" :class="{ show: reward.showDropdown }">
        <input v-model="reward.searchText" @input="filterItems(reward)" placeholder="搜索...">
        <!-- 道具选项 -->
    </div>
</div>
```

**优势：**
- 清晰的事件处理
- 稳定的搜索功能
- 优秀的用户体验
- 性能优化

## 核心特性

### 1. 自定义选择器结构
- **显示输入框**：只读，显示选中的道具信息
- **下拉箭头**：视觉指示，支持旋转动画
- **下拉面板**：包含搜索框和道具列表
- **搜索框**：独立的搜索输入框

### 2. 交互方式
- **点击切换**：点击显示输入框切换下拉框显示/隐藏
- **实时搜索**：在下拉框内的搜索框中输入关键字
- **点击选择**：点击道具项进行选择
- **点击外部关闭**：点击下拉框外部自动关闭

### 3. 搜索功能
- **多字段匹配**：支持按道具名称和ID搜索
- **实时过滤**：输入时立即显示匹配结果
- **性能优化**：限制显示数量（最多50个）
- **无结果提示**：搜索无结果时显示友好提示

## 技术实现

### 1. HTML结构
```html
<div class="custom-select-container" :class="{ open: reward.showDropdown }">
    <!-- 显示输入框 -->
    <input type="text" class="cyber-form-control"
           :value="getSelectedItemDisplay(reward)"
           @click="toggleItemDropdown(reward)"
           placeholder="请选择道具"
           readonly required>
    
    <!-- 下拉箭头 -->
    <i class="fas fa-chevron-down custom-select-arrow"></i>
    
    <!-- 下拉面板 -->
    <div class="custom-select-dropdown" :class="{ show: reward.showDropdown }">
        <!-- 搜索框 -->
        <input type="text" class="cyber-form-control"
               v-model="reward.searchText"
               placeholder="搜索道具名称或编号..."
               @input="filterItems(reward)">
        
        <!-- 道具选项 -->
        <div v-for="item in reward.filteredItems" :key="item.id"
             class="custom-select-option"
             :class="{ selected: reward.id == item.id }"
             @click="selectItem(reward, item)">
            <div class="item-name">{{ item.name }} ({{ item.id }})</div>
            <div class="item-details">
                {{ getItemTypeText(item.type) }} | {{ getItemQualityName(item.quality) }} | 价格: {{ item.price || 0 }}
            </div>
        </div>
        
        <!-- 无结果提示 -->
        <div v-if="reward.filteredItems.length === 0" class="custom-select-option">
            未找到匹配的道具
        </div>
    </div>
</div>
```

### 2. CSS样式
```css
/* 容器样式 */
.custom-select-container {
    position: relative;
}

/* 下拉箭头 */
.custom-select-arrow {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    transition: transform 0.3s ease;
}

.custom-select-container.open .custom-select-arrow {
    transform: translateY(-50%) rotate(180deg);
}

/* 下拉面板 */
.custom-select-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: var(--cyber-card-bg);
    border: 1px solid var(--cyber-border);
    border-radius: 8px;
    max-height: 400px;
    overflow-y: auto;
    z-index: 1000;
    display: none;
}

.custom-select-dropdown.show {
    display: block;
}

/* 道具选项 */
.custom-select-option {
    padding: 0.75rem;
    cursor: pointer;
    border-bottom: 1px solid rgba(102, 126, 234, 0.1);
    transition: all 0.3s ease;
}

.custom-select-option:hover {
    background: rgba(102, 126, 234, 0.1);
}

.custom-select-option.selected {
    background: rgba(102, 126, 234, 0.2);
    border-left: 3px solid var(--cyber-primary);
}
```

### 3. JavaScript方法
```javascript
// 切换下拉框显示状态
toggleItemDropdown(reward) {
    reward.showDropdown = !reward.showDropdown;
    if (reward.showDropdown) {
        const maxDisplayCount = 50;
        reward.filteredItems = this.itemOptions.slice(0, maxDisplayCount);
        reward.searchText = '';
    }
}

// 过滤道具列表
filterItems(reward) {
    const searchText = reward.searchText.toLowerCase();
    if (!searchText.trim()) {
        const maxDisplayCount = 50;
        reward.filteredItems = this.itemOptions.slice(0, maxDisplayCount);
        return;
    }
    
    const searchResults = this.itemOptions.filter(item => 
        item.name.toLowerCase().includes(searchText) ||
        item.id.toLowerCase().includes(searchText)
    );
    
    const maxSearchResults = 50;
    reward.filteredItems = searchResults.slice(0, maxSearchResults);
}

// 选择道具
selectItem(reward, item) {
    reward.id = item.id;
    reward.name = item.name;
    reward.quality = item.quality;
    reward.showDropdown = false;
    reward.searchText = '';
    reward.filteredItems = [];
}

// 获取选中道具的显示文本
getSelectedItemDisplay(reward) {
    if (!reward.id) {
        return '请选择道具';
    }
    const item = this.itemOptions.find(item => item.id == reward.id);
    if (!item) {
        return reward.id;
    }
    return `${item.name} (${item.id})`;
}

// 处理点击外部关闭下拉框
handleOutsideClick(event) {
    const container = event.target.closest('.custom-select-container');
    if (!container) {
        this.taskForm.rewards.forEach(reward => {
            if (reward.showDropdown) {
                reward.showDropdown = false;
            }
        });
    }
}
```

## 性能优化

### 1. 限制显示数量
- 默认显示最多50个道具
- 搜索结果最多50个
- 避免大量DOM节点导致的性能问题

### 2. 事件优化
- 使用点击切换而不是focus/blur
- 独立的搜索框避免事件冲突
- 点击外部关闭功能

### 3. 状态管理
- 清晰的状态控制
- 及时清理临时数据
- 避免内存泄漏

## 用户体验提升

### 1. 视觉反馈
- 下拉箭头旋转动画
- 选中状态高亮显示
- 悬停效果

### 2. 交互优化
- 只读输入框避免误操作
- 清晰的搜索提示
- 友好的无结果提示

### 3. 功能完整
- 支持键盘和鼠标操作
- 点击外部自动关闭
- 搜索结果实时更新

## 验证步骤

1. **基础功能**
   - 点击道具输入框，查看下拉框是否正确显示
   - 验证下拉箭头旋转动画
   - 检查道具列表是否正确加载

2. **搜索功能**
   - 在搜索框中输入"小强化剂"
   - 验证搜索结果是否正确
   - 测试按ID搜索功能

3. **选择功能**
   - 点击选择一个道具
   - 验证输入框显示是否正确
   - 检查下拉框是否自动关闭

4. **交互体验**
   - 点击下拉框外部，验证是否自动关闭
   - 测试多个奖励项的下拉框互不干扰
   - 验证性能表现

这个新实现完全解决了原有的搜索功能问题，提供了更好的用户体验和更稳定的功能表现。
