using System.ComponentModel.DataAnnotations;

namespace BMS.Models.DTOs
{
    /// <summary>
    /// 装备类型创建DTO
    /// </summary>
    public class EquipmentTypeCreateDto
    {
        /// <summary>
        /// 装备类型ID
        /// </summary>
        [Required(ErrorMessage = "装备类型ID不能为空")]
        [StringLength(50, ErrorMessage = "装备类型ID长度不能超过50个字符")]
        public string EquipTypeId { get; set; } = string.Empty;

        /// <summary>
        /// 装备类型名称
        /// </summary>
        [Required(ErrorMessage = "装备类型名称不能为空")]
        [StringLength(100, ErrorMessage = "装备类型名称长度不能超过100个字符")]
        public string TypeName { get; set; } = string.Empty;

        /// <summary>
        /// 类型描述
        /// </summary>
        [StringLength(255, ErrorMessage = "类型描述长度不能超过255个字符")]
        public string? Description { get; set; }

        /// <summary>
        /// 排序号
        /// </summary>
        [Range(0, 9999, ErrorMessage = "排序号必须在0-9999之间")]
        public int SortOrder { get; set; } = 0;

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsActive { get; set; } = true;
    }
} 