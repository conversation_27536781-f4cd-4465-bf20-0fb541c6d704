using BMS.Models.Common;
using BMS.Models.DTOs;
using BMS.Models.Entities;
using BMS.Services.Interfaces;
using BMS.Common.Helpers;
using SqlSugar;

namespace BMS.Services.Implementations
{
    /// <summary>
    /// 用户服务实现
    /// </summary>
    public class UserService : IUserService
    {
        private readonly IDbService _dbService;

        public UserService(IDbService dbService)
        {
            _dbService = dbService;
        }

        /// <summary>
        /// 分页查询用户列表
        /// </summary>
        /// <param name="queryDto">查询条件</param>
        /// <returns>分页结果</returns>
        public async Task<PagedResult<UserDto>> GetUsersAsync(UserQueryDto queryDto)
        {
            try
            {
                var query = _dbService.Queryable<user>();

                // 构建查询条件
                if (queryDto.UserId.HasValue)
                {
                    query = query.Where(x => x.id == queryDto.UserId.Value);
                }

                if (!string.IsNullOrWhiteSpace(queryDto.Username))
                {
                    query = query.Where(x => x.username.Contains(queryDto.Username));
                }

                if (!string.IsNullOrWhiteSpace(queryDto.Nickname))
                {
                    query = query.Where(x => x.nickname != null && x.nickname.Contains(queryDto.Nickname));
                }

                if (!string.IsNullOrWhiteSpace(queryDto.Sex))
                {
                    query = query.Where(x => x.sex == queryDto.Sex);
                }

                if (queryDto.VipLevel.HasValue)
                {
                    query = query.Where(x => x.vip_level == queryDto.VipLevel.Value);
                }

                // 获取总数
                var totalCount = await query.CountAsync();

                // 分页查询
                var users = await query
                    .OrderByDescending(x => x.create_time)
                    .Skip((queryDto.Page - 1) * queryDto.PageSize)
                    .Take(queryDto.PageSize)
                    .ToListAsync();

                // 转换为DTO
                var userDtos = users.Select(MapToDto).ToList();

                return new PagedResult<UserDto>(userDtos, queryDto.Page, queryDto.PageSize, totalCount);
            }
            catch (Exception ex)
            {
                throw new Exception($"查询用户列表失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 根据ID获取用户信息
        /// </summary>
        /// <param name="id">用户ID</param>
        /// <returns>用户信息</returns>
        public async Task<UserDto?> GetUserByIdAsync(int id)
        {
            try
            {
                var entity = await _dbService.Queryable<user>()
                    .Where(x => x.id == id)
                    .FirstAsync();

                return entity != null ? MapToDto(entity) : null;
            }
            catch (Exception ex)
            {
                throw new Exception($"获取用户信息失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 根据用户名获取用户信息
        /// </summary>
        /// <param name="username">用户名</param>
        /// <returns>用户信息</returns>
        public async Task<UserDto?> GetUserByUsernameAsync(string username)
        {
            try
            {
                var entity = await _dbService.Queryable<user>()
                    .Where(x => x.username == username)
                    .FirstAsync();

                return entity != null ? MapToDto(entity) : null;
            }
            catch (Exception ex)
            {
                throw new Exception($"获取用户信息失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 创建用户
        /// </summary>
        /// <param name="createDto">创建用户DTO</param>
        /// <returns>创建结果</returns>
        public async Task<ApiResult<int>> CreateUserAsync(UserCreateDto createDto)
        {
            try
            {
                // 检查用户名是否已存在
                bool exists = await CheckUsernameExistsAsync(createDto.Username);
                if (exists)
                {
                    return ApiResult<int>.Fail("用户名已存在");
                }

                // 使用MD5加密密码
                string encryptedPassword = PasswordHelper.Md5Hash(createDto.Password);

                // 创建实体
                var entity = new user
                {
                    username = createDto.Username,
                    password = encryptedPassword,
                    nickname = createDto.Nickname,
                    sex = createDto.Sex,
                    vip_level = createDto.VipLevel,
                    vip_score = createDto.VipScore,
                    gold = createDto.Gold,
                    yuanbao = createDto.Yuanbao,
                    crystal = createDto.Crystal,
                    title = createDto.Title,
                    reg_time = DateTime.Now,
                    create_time = DateTime.Now
                };

                // 插入数据库
                var result = await _dbService.Insertable(entity).ExecuteReturnIdentityAsync();

                return ApiResult<int>.Ok(result, "创建用户成功");
            }
            catch (Exception ex)
            {
                return ApiResult<int>.Fail($"创建用户失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 更新用户信息
        /// </summary>
        /// <param name="updateDto">更新用户DTO</param>
        /// <returns>更新结果</returns>
        public async Task<ApiResult<bool>> UpdateUserAsync(UserUpdateDto updateDto)
        {
            try
            {
                // 检查用户是否存在
                var existing = await _dbService.Queryable<user>()
                    .Where(x => x.id == updateDto.Id)
                    .FirstAsync();

                if (existing == null)
                {
                    return ApiResult<bool>.Fail("用户不存在");
                }

                // 检查用户名是否已被其他用户使用
                bool exists = await CheckUsernameExistsAsync(updateDto.Username, updateDto.Id);
                if (exists)
                {
                    return ApiResult<bool>.Fail("用户名已被其他用户使用");
                }

                // 更新实体
                existing.username = updateDto.Username;
                existing.nickname = updateDto.Nickname;
                existing.sex = updateDto.Sex;
                existing.vip_level = updateDto.VipLevel;
                existing.vip_score = updateDto.VipScore;
                existing.gold = updateDto.Gold;
                existing.yuanbao = updateDto.Yuanbao;
                existing.crystal = updateDto.Crystal;
                existing.title = updateDto.Title;
                existing.main_pet_name = updateDto.MainPetName;

                // 更新数据库
                bool result = await _dbService.Updateable(existing).ExecuteCommandHasChangeAsync();

                return result ? ApiResult<bool>.Ok(true, "更新用户成功") : ApiResult<bool>.Fail("更新用户失败");
            }
            catch (Exception ex)
            {
                return ApiResult<bool>.Fail($"更新用户失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 更新用户资产
        /// </summary>
        /// <param name="assetDto">用户资产更新DTO</param>
        /// <returns>更新结果</returns>
        public async Task<ApiResult<bool>> UpdateUserAssetAsync(UserAssetUpdateDto assetDto)
        {
            try
            {
                // 检查用户是否存在
                var existing = await _dbService.Queryable<user>()
                    .Where(x => x.id == assetDto.Id)
                    .FirstAsync();

                if (existing == null)
                {
                    return ApiResult<bool>.Fail("用户不存在");
                }

                // 更新资产字段
                existing.gold = assetDto.Gold;
                existing.yuanbao = assetDto.Yuanbao;
                existing.crystal = assetDto.Crystal;
                existing.vip_level = assetDto.VipLevel;
                existing.vip_score = assetDto.VipScore;

                // 更新数据库
                bool result = await _dbService.Updateable(existing)
                    .UpdateColumns(x => new { x.gold, x.yuanbao, x.crystal, x.vip_level, x.vip_score })
                    .ExecuteCommandHasChangeAsync();

                return result ? ApiResult<bool>.Ok(true, "更新用户资产成功") : ApiResult<bool>.Fail("更新用户资产失败");
            }
            catch (Exception ex)
            {
                return ApiResult<bool>.Fail($"更新用户资产失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 重置用户密码
        /// </summary>
        /// <param name="resetDto">重置密码DTO</param>
        /// <returns>重置结果</returns>
        public async Task<ApiResult<bool>> ResetPasswordAsync(UserResetPasswordDto resetDto)
        {
            try
            {
                // 检查用户是否存在
                var existing = await _dbService.Queryable<user>()
                    .Where(x => x.id == resetDto.Id)
                    .FirstAsync();

                if (existing == null)
                {
                    return ApiResult<bool>.Fail("用户不存在");
                }

                // 加密新密码
                string encryptedPassword = PasswordHelper.Md5Hash(resetDto.NewPassword);

                // 更新密码
                existing.password = encryptedPassword;

                bool result = await _dbService.Updateable(existing)
                    .UpdateColumns(x => new { x.password })
                    .ExecuteCommandHasChangeAsync();

                return result ? ApiResult<bool>.Ok(true, "重置密码成功") : ApiResult<bool>.Fail("重置密码失败");
            }
            catch (Exception ex)
            {
                return ApiResult<bool>.Fail($"重置密码失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 删除用户
        /// </summary>
        /// <param name="id">用户ID</param>
        /// <returns>删除结果</returns>
        public async Task<ApiResult<bool>> DeleteUserAsync(int id)
        {
            try
            {
                // 检查用户是否存在
                var existing = await _dbService.Queryable<user>()
                    .Where(x => x.id == id)
                    .FirstAsync();

                if (existing == null)
                {
                    return ApiResult<bool>.Fail("用户不存在");
                }

                // 可以在这里添加关联数据检查，比如用户是否有宠物、道具等
                // 为了数据安全，实际项目中可能不删除用户，而是标记为已删除状态

                // 删除用户
                bool result = await _dbService.Deleteable<user>()
                    .Where(x => x.id == id)
                    .ExecuteCommandHasChangeAsync();

                return result ? ApiResult<bool>.Ok(true, "删除用户成功") : ApiResult<bool>.Fail("删除用户失败");
            }
            catch (Exception ex)
            {
                return ApiResult<bool>.Fail($"删除用户失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 检查用户名是否存在
        /// </summary>
        /// <param name="username">用户名</param>
        /// <param name="excludeId">排除的用户ID（用于更新时检查）</param>
        /// <returns>是否存在</returns>
        public async Task<bool> CheckUsernameExistsAsync(string username, int? excludeId = null)
        {
            try
            {
                var query = _dbService.Queryable<user>().Where(x => x.username == username);

                if (excludeId.HasValue)
                {
                    query = query.Where(x => x.id != excludeId.Value);
                }

                return await query.AnyAsync();
            }
            catch (Exception ex)
            {
                throw new Exception($"检查用户名是否存在失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 验证用户密码
        /// </summary>
        /// <param name="username">用户名</param>
        /// <param name="password">明文密码</param>
        /// <returns>验证结果</returns>
        public async Task<ApiResult<UserDto>> ValidateUserAsync(string username, string password)
        {
            try
            {
                var entity = await _dbService.Queryable<user>()
                    .Where(x => x.username == username)
                    .FirstAsync();

                if (entity == null)
                {
                    return ApiResult<UserDto>.Fail("用户不存在");
                }

                string encryptedPassword = PasswordHelper.Md5Hash(password);
                if (entity.password != encryptedPassword)
                {
                    return ApiResult<UserDto>.Fail("密码错误");
                }

                return ApiResult<UserDto>.Ok(MapToDto(entity), "验证成功");
            }
            catch (Exception ex)
            {
                return ApiResult<UserDto>.Fail($"验证失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 获取用户统计信息
        /// </summary>
        /// <returns>用户统计信息</returns>
        public async Task<UserStatisticsDto> GetUserStatisticsAsync()
        {
            try
            {
                var totalUsers = await _dbService.Queryable<user>().CountAsync();
                var todayStart = DateTime.Today;
                var todayNewUsers = await _dbService.Queryable<user>()
                    .Where(x => x.create_time >= todayStart)
                    .CountAsync();

                var vipUsers = await _dbService.Queryable<user>()
                    .Where(x => x.vip_level > 0)
                    .CountAsync();

                var totalGold = await _dbService.Queryable<user>().SumAsync(x => x.gold ?? 0);

                return new UserStatisticsDto
                {
                    TotalUsers = totalUsers,
                    TodayNewUsers = todayNewUsers,
                    VipUsers = vipUsers,
                    TotalGold = totalGold
                };
            }
            catch (Exception ex)
            {
                throw new Exception($"获取用户统计信息失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 实体转DTO映射
        /// </summary>
        /// <param name="entity">用户实体</param>
        /// <returns>用户DTO</returns>
        private static UserDto MapToDto(user entity)
        {
            return new UserDto
            {
                Id = entity.id,
                Username = entity.username,
                Nickname = entity.nickname,
                Sex = entity.sex,
                VipLevel = entity.vip_level,
                VipScore = entity.vip_score,
                Gold = entity.gold,
                Yuanbao = entity.yuanbao,
                Crystal = entity.crystal,
                RegTime = entity.reg_time,
                Title = entity.title,
                MainPetName = entity.main_pet_name,
                LastLoginTime = null // 可以在后续添加最后登录时间字段
            };
        }
    }
} 