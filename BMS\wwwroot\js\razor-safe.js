/**
 * Razor安全的JavaScript工具类
 * 避免在Razor视图中使用会被解析的特殊字符
 */
class RazorSafe {
    
    // ASCII码常量
    static CHARS = {
        AT: 64,           // @
        LEFT_BRACKET: 91, // [
        RIGHT_BRACKET: 93,// ]
        BACKSLASH: 92,    // \
        DOLLAR: 36,       // $
        CARET: 94,        // ^
        PIPE: 124,        // |
        DOT: 46,          // .
        PLUS: 43,         // +
        ASTERISK: 42,     // *
        QUESTION: 63      // ?
    };
    
    /**
     * 获取特殊字符
     * @param {string} charName - 字符名称
     * @returns {string} 字符
     */
    static getChar(charName) {
        const code = this.CHARS[charName.toUpperCase()];
        return code ? String.fromCharCode(code) : '';
    }
    
    /**
     * 邮箱验证（Razor安全）
     * @param {string} email - 邮箱地址
     * @returns {boolean} 是否有效
     */
    static validateEmail(email) {
        if (!email || typeof email !== 'string') {
            return false;
        }
        
        const atChar = this.getChar('AT');
        const dotChar = this.getChar('DOT');
        
        // 基本格式检查
        if (!email.includes(atChar) || !email.includes(dotChar)) {
            return false;
        }
        
        // 检查@符号位置
        const atIndex = email.indexOf(atChar);
        const lastDotIndex = email.lastIndexOf(dotChar);
        
        return atIndex > 0 && 
               atIndex < email.length - 1 && 
               lastDotIndex > atIndex && 
               lastDotIndex < email.length - 1;
    }
    
    /**
     * 手机号验证（Razor安全）
     * @param {string} phone - 手机号
     * @returns {boolean} 是否有效
     */
    static validatePhone(phone) {
        if (!phone || typeof phone !== 'string') {
            return false;
        }
        
        // 简化验证，避免复杂正则表达式
        const phoneStr = phone.toString().trim();
        
        // 检查长度
        if (phoneStr.length !== 11) {
            return false;
        }
        
        // 检查是否以1开头
        if (!phoneStr.startsWith('1')) {
            return false;
        }
        
        // 检查第二位是否为3-9
        const secondDigit = parseInt(phoneStr.charAt(1));
        if (secondDigit < 3 || secondDigit > 9) {
            return false;
        }
        
        // 检查是否全为数字
        return /^\d+$/.test(phoneStr);
    }
    
    /**
     * 构建正则表达式模式（Razor安全）
     * @param {string} pattern - 模式字符串，使用占位符
     * @returns {RegExp} 正则表达式对象
     */
    static buildRegex(pattern) {
        let safePattern = pattern;
        
        // 替换占位符
        safePattern = safePattern.replace(/\{AT\}/g, this.getChar('AT'));
        safePattern = safePattern.replace(/\{LEFT_BRACKET\}/g, this.getChar('LEFT_BRACKET'));
        safePattern = safePattern.replace(/\{RIGHT_BRACKET\}/g, this.getChar('RIGHT_BRACKET'));
        safePattern = safePattern.replace(/\{BACKSLASH\}/g, this.getChar('BACKSLASH'));
        safePattern = safePattern.replace(/\{DOLLAR\}/g, this.getChar('DOLLAR'));
        safePattern = safePattern.replace(/\{CARET\}/g, this.getChar('CARET'));
        safePattern = safePattern.replace(/\{PIPE\}/g, this.getChar('PIPE'));
        safePattern = safePattern.replace(/\{DOT\}/g, this.getChar('DOT'));
        safePattern = safePattern.replace(/\{PLUS\}/g, this.getChar('PLUS'));
        safePattern = safePattern.replace(/\{ASTERISK\}/g, this.getChar('ASTERISK'));
        safePattern = safePattern.replace(/\{QUESTION\}/g, this.getChar('QUESTION'));
        
        return new RegExp(safePattern);
    }
    
    /**
     * 密码强度验证
     * @param {string} password - 密码
     * @returns {object} 验证结果
     */
    static validatePassword(password) {
        const result = {
            isValid: false,
            score: 0,
            errors: []
        };
        
        if (!password) {
            result.errors.push('密码不能为空');
            return result;
        }
        
        // 长度检查
        if (password.length < 6) {
            result.errors.push('密码长度不能少于6位');
        } else {
            result.score += 1;
        }
        
        if (password.length >= 8) {
            result.score += 1;
        }
        
        // 包含数字
        if (/\d/.test(password)) {
            result.score += 1;
        }
        
        // 包含小写字母
        if (/[a-z]/.test(password)) {
            result.score += 1;
        }
        
        // 包含大写字母
        if (/[A-Z]/.test(password)) {
            result.score += 1;
        }
        
        // 包含特殊字符（避免使用会被Razor解析的字符）
        if (/[!#$%&*+\-=?^_`|~]/.test(password)) {
            result.score += 1;
        }
        
        result.isValid = result.errors.length === 0 && result.score >= 2;
        
        return result;
    }
    
    /**
     * 通用表单验证
     * @param {object} formData - 表单数据
     * @param {object} rules - 验证规则
     * @returns {object} 验证结果
     */
    static validateForm(formData, rules) {
        const errors = {};
        
        Object.keys(rules).forEach(field => {
            const rule = rules[field];
            const value = formData[field];
            
            // 必填检查
            if (rule.required && (!value || value.toString().trim() === '')) {
                errors[field] = rule.requiredMessage || `${field}不能为空`;
                return;
            }
            
            // 如果不是必填且值为空，跳过后续验证
            if (!value) return;
            
            // 长度检查
            if (rule.minLength && value.length < rule.minLength) {
                errors[field] = rule.minLengthMessage || `${field}长度不能少于${rule.minLength}位`;
                return;
            }
            
            if (rule.maxLength && value.length > rule.maxLength) {
                errors[field] = rule.maxLengthMessage || `${field}长度不能超过${rule.maxLength}位`;
                return;
            }
            
            // 类型验证
            if (rule.type === 'email' && !this.validateEmail(value)) {
                errors[field] = rule.message || '请输入有效的邮箱地址';
            }
            
            if (rule.type === 'phone' && !this.validatePhone(value)) {
                errors[field] = rule.message || '请输入有效的手机号码';
            }
            
            // 自定义验证函数
            if (rule.validator && typeof rule.validator === 'function') {
                if (!rule.validator(value)) {
                    errors[field] = rule.message || '验证失败';
                }
            }
        });
        
        return {
            isValid: Object.keys(errors).length === 0,
            errors: errors
        };
    }
}

// 使用示例：
// const email = '<EMAIL>';
// const isValidEmail = RazorSafe.validateEmail(email);
// 
// const phone = '13800138000';
// const isValidPhone = RazorSafe.validatePhone(phone);
//
// 表单验证示例：
// const formData = { email: '<EMAIL>', phone: '13800138000' };
// const rules = {
//     email: { required: true, type: 'email', message: '请输入有效邮箱' },
//     phone: { required: true, type: 'phone', message: '请输入有效手机号' }
// };
// const result = RazorSafe.validateForm(formData, rules); 