# 任务配置功能优化说明

## 功能概述

对任务配置管理页面进行了重大优化：
1. 新增了专门的奖励配置编辑器，用于可视化地配置任务奖励
2. 将奖励配置和任务目标配置的入口按钮直接放到任务列表中
3. 简化了主任务配置弹框，专注于基本信息配置
4. 避免了弹框嵌套的不合理设计

## 功能特点

### 1. 操作流程优化
- **列表直接操作**：在任务列表中直接提供奖励配置和目标配置按钮
- **主弹框简化**：只包含基本任务信息配置
- **独立配置弹框**：专门用于配置任务奖励和目标
- **避免弹框嵌套**：不再在弹框中打开新弹框，操作更合理

### 2. 可视化奖励配置
- 提供直观的表单界面，无需手动编写JSON
- 支持多种奖励类型的配置
- 实时验证输入数据的有效性
- **道具选择优化**：道具类型奖励支持搜索下拉框选择

### 3. 支持的奖励类型
- **经验值 (exp)**: 直接配置经验值数量
- **金币 (gold)**: 直接配置金币数量
- **道具 (item)**: 通过搜索下拉框选择道具，支持按名称和ID搜索
- **宠物 (pet)**: 配置宠物ID、数量、名称
- **技能 (skill)**: 配置技能ID、等级、名称

### 4. 道具搜索下拉框功能
- **智能搜索**：支持按道具名称和ID进行模糊搜索
- **实时过滤**：输入时实时显示匹配的道具列表
- **品质显示**：不同品质的道具用不同颜色的徽章标识
- **详细信息**：显示道具ID、名称和描述信息
- **快速选择**：点击道具自动填充相关信息
- **用户友好**：支持键盘和鼠标操作，体验流畅

### 5. 数据转换
- 自动将可视化配置转换为JSON格式存储到rewardConfig字段
- 编辑时自动解析JSON配置为可视化表单
- 保持与现有API接口的完全兼容

## 使用方法

### 主任务配置
1. 在任务列表中点击"新增任务"或"编辑"按钮（铅笔图标）
2. 在主弹框中填写基本任务信息：
   - 任务名称、描述
   - 任务类型、前置任务
   - 是否可重复、是否激活等
3. 点击"保存"完成基本信息配置

### 配置奖励
1. **直接从列表操作**：在任务列表的操作列中点击"奖励配置"按钮（礼物图标）
2. 系统自动加载任务信息并打开奖励配置弹框
3. 在奖励配置弹框中：
   - 点击"添加奖励"按钮添加新奖励
   - 选择奖励类型并填写相应信息
   - 点击"保存配置"直接更新任务配置
4. 根据奖励类型填写相应字段：
   - 经验值/金币：只需填写数量
   - 道具：通过搜索下拉框选择道具，系统自动填充道具信息
   - 宠物：填写宠物ID、数量，可选填名称
   - 技能：填写技能ID、等级，可选填名称

#### 道具选择详细步骤：
1. 选择奖励类型为"道具"
2. 在道具搜索框中输入道具名称或ID的关键字
3. 系统实时显示匹配的道具列表，包含：
   - 道具名称和ID
   - 品质等级（用颜色徽章标识）
   - 道具描述信息
4. 点击选择所需道具，系统自动填充：
   - 道具ID
   - 道具名称
   - 道具品质
5. 手动设置道具数量

### 配置任务目标
1. **直接从列表操作**：在任务列表的操作列中点击"目标配置"按钮（靶心图标）
2. 系统自动加载任务信息并打开任务目标配置弹框
3. 在任务目标配置弹框中：
   - 点击"添加目标"按钮添加新目标
   - 选择目标类型并填写目标信息
   - 点击"保存配置"直接更新任务配置

### 操作按钮说明
任务列表中每个任务的操作按钮：
- 👁️ **查看详情**：查看任务的完整信息
- ✏️ **编辑基本信息**：修改任务的基本信息
- 🎁 **配置奖励**：直接配置任务奖励
- 🎯 **配置目标**：直接配置任务目标
- ⏸️/▶️ **启用/禁用**：切换任务状态
- 🗑️ **删除**：删除任务

### 优势
- **操作直观**：所有配置入口都在列表中，一目了然
- **避免嵌套**：不再有弹框中打开弹框的情况
- **专业化配置**：每个配置弹框专注于特定功能
- **即时保存**：配置完成后直接保存，无需返回主弹框

## JSON格式说明

生成的JSON配置格式如下：

```json
{
  "exp": 100,
  "gold": 50,
  "items": [
    {
      "id": "item001",
      "amount": 2,
      "name": "魔法药水",
      "quality": 3
    }
  ],
  "pets": [
    {
      "id": "pet001",
      "amount": 1,
      "name": "火焰龙"
    }
  ],
  "skills": [
    {
      "id": "skill001",
      "level": 5,
      "name": "火球术"
    }
  ]
}
```

## 验证规则

### 必填字段验证
- 奖励类型必须选择
- 数量/值必须大于0
- 道具、宠物、技能类型的奖励必须填写ID

### 数据类型验证
- 数量字段只接受正整数
- 品质字段只接受1-5的整数值

## 兼容性

- 完全兼容现有的TaskConfig/Update接口
- 不影响现有的任务配置数据
- 支持混合使用（部分任务使用新编辑器，部分保持原JSON格式）

## 技术实现

### 前端实现
- 使用Vue.js实现响应式表单
- 提供parseRewardConfig()方法解析JSON为表单数据
- 提供generateRewardConfig()方法将表单数据转换为JSON
- 在saveTask()方法中自动处理数据转换

#### 道具数据源集成
- **数据来源**：使用`/UserItem/GetItemConfigs`接口获取道具配置
- **数据一致性**：与UserItem页面使用相同的数据源
- **格式转换**：自动转换UserItem的数据格式为奖励配置所需格式
- **品质映射**：white→1, green→2, blue→3, purple→4, orange→5
- **备用数据**：接口失败时使用预设的备用道具数据

### 后端兼容
- 无需修改后端代码
- 继续使用rewardConfig字段存储JSON数据
- 保持与现有API接口的完全兼容

## 使用示例

### 示例1：配置经验和金币奖励
1. 添加奖励 → 选择"经验值" → 输入数量"100"
2. 添加奖励 → 选择"金币" → 输入数量"50"

生成JSON：`{"exp": 100, "gold": 50}`

### 示例2：配置道具奖励
1. 添加奖励 → 选择"道具"
2. 填写道具ID："potion001"
3. 填写数量："3"
4. 填写名称："生命药水"（可选）
5. 选择品质："稀有"（可选）

生成JSON：
```json
{
  "items": [
    {
      "id": "potion001",
      "amount": 3,
      "name": "生命药水",
      "quality": 3
    }
  ]
}
```

## 注意事项

1. 保存前会进行数据验证，确保所有必填字段都已填写
2. 奖励配置为可选项，可以创建没有奖励的任务
3. 编辑现有任务时，如果原JSON格式不标准，可能无法正确解析，建议重新配置
4. 品质字段对应关系：1=普通，2=优秀，3=稀有，4=史诗，5=传说
