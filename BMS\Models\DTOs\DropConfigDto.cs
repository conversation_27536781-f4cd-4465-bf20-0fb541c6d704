using System.ComponentModel.DataAnnotations;
using BMS.Models.Common;

namespace BMS.Models.DTOs
{
    /// <summary>
    /// 掉落配置显示DTO
    /// </summary>
    public class DropConfigDto
    {
        /// <summary>
        /// 自增主键
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 地图ID
        /// </summary>
        public int MapId { get; set; }

        /// <summary>
        /// 地图名称
        /// </summary>
        public string MapName { get; set; } = string.Empty;

        /// <summary>
        /// 掉落类型
        /// </summary>
        public string DropType { get; set; } = string.Empty;

        /// <summary>
        /// 怪物ID（怪物掉落时填写，地图掉落时为空）
        /// </summary>
        public int? MonsterId { get; set; }

        /// <summary>
        /// 怪物名称
        /// </summary>
        public string? MonsterName { get; set; }

        /// <summary>
        /// 道具ID
        /// </summary>
        public string ItemId { get; set; } = string.Empty;

        /// <summary>
        /// 道具名称
        /// </summary>
        public string ItemName { get; set; } = string.Empty;

        /// <summary>
        /// 掉落概率（0~1之间，1为必掉）
        /// </summary>
        public decimal DropRate { get; set; }

        /// <summary>
        /// 掉落概率百分比显示
        /// </summary>
        public string DropRatePercent => $"{DropRate * 100:F2}%";

        /// <summary>
        /// 最小掉落数量
        /// </summary>
        public int MinCount { get; set; }

        /// <summary>
        /// 最大掉落数量
        /// </summary>
        public int MaxCount { get; set; }

        /// <summary>
        /// 掉落数量范围显示
        /// </summary>
        public string CountRange => MinCount == MaxCount ? MinCount.ToString() : $"{MinCount}-{MaxCount}";

        /// <summary>
        /// 备注
        /// </summary>
        public string? Remark { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateTime { get; set; }

        /// <summary>
        /// 修改时间
        /// </summary>
        public DateTime UpdateTime { get; set; }

        /// <summary>
        /// 掉落道具JSON数据（多道具配置）
        /// </summary>
        public string? DropItemsJson { get; set; }
    }

    /// <summary>
    /// 掉落配置查询DTO
    /// </summary>
    public class DropConfigQueryDto : PagedQueryDto
    {
        /// <summary>
        /// 地图ID
        /// </summary>
        public int? MapId { get; set; }

        /// <summary>
        /// 地图名称（模糊搜索）
        /// </summary>
        public string? MapName { get; set; }

        /// <summary>
        /// 掉落类型
        /// </summary>
        public string? DropType { get; set; }

        /// <summary>
        /// 怪物ID
        /// </summary>
        public int? MonsterId { get; set; }

        /// <summary>
        /// 道具ID（模糊搜索）
        /// </summary>
        public string? ItemId { get; set; }

        /// <summary>
        /// 道具名称（模糊搜索）
        /// </summary>
        public string? ItemName { get; set; }
    }

    /// <summary>
    /// 创建掉落配置DTO
    /// </summary>
    public class DropConfigCreateDto
    {
        /// <summary>
        /// 地图ID
        /// </summary>
        [Required(ErrorMessage = "地图ID不能为空")]
        public int MapId { get; set; }

        /// <summary>
        /// 掉落类型
        /// </summary>
        [Required(ErrorMessage = "掉落类型不能为空")]
        public string DropType { get; set; } = string.Empty;

        /// <summary>
        /// 怪物ID（怪物掉落时必填）
        /// </summary>
        public int? MonsterId { get; set; }

        /// <summary>
        /// 道具ID
        /// </summary>
        [Required(ErrorMessage = "道具ID不能为空")]
        [StringLength(20, ErrorMessage = "道具ID长度不能超过20个字符")]
        public string ItemId { get; set; } = string.Empty;

        /// <summary>
        /// 掉落概率（0~1之间，1为必掉）
        /// </summary>
        [Range(0, 1, ErrorMessage = "掉落概率必须在0-1之间")]
        public decimal DropRate { get; set; } = 1.0m;

        /// <summary>
        /// 最小掉落数量
        /// </summary>
        [Range(1, 999, ErrorMessage = "最小掉落数量必须在1-999之间")]
        public int MinCount { get; set; } = 1;

        /// <summary>
        /// 最大掉落数量
        /// </summary>
        [Range(1, 999, ErrorMessage = "最大掉落数量必须在1-999之间")]
        public int MaxCount { get; set; } = 1;

        /// <summary>
        /// 备注
        /// </summary>
        [StringLength(255, ErrorMessage = "备注长度不能超过255个字符")]
        public string? Remark { get; set; }
    }

    /// <summary>
    /// 更新掉落配置DTO
    /// </summary>
    public class DropConfigUpdateDto
    {
        /// <summary>
        /// 自增主键
        /// </summary>
        [Required(ErrorMessage = "ID不能为空")]
        public int Id { get; set; }

        /// <summary>
        /// 地图ID
        /// </summary>
        [Required(ErrorMessage = "地图ID不能为空")]
        public int MapId { get; set; }

        /// <summary>
        /// 掉落类型
        /// </summary>
        [Required(ErrorMessage = "掉落类型不能为空")]
        public string DropType { get; set; } = string.Empty;

        /// <summary>
        /// 怪物ID（怪物掉落时必填）
        /// </summary>
        public int? MonsterId { get; set; }

        /// <summary>
        /// 道具ID
        /// </summary>
        [Required(ErrorMessage = "道具ID不能为空")]
        [StringLength(20, ErrorMessage = "道具ID长度不能超过20个字符")]
        public string ItemId { get; set; } = string.Empty;

        /// <summary>
        /// 掉落概率（0~1之间，1为必掉）
        /// </summary>
        [Range(0, 1, ErrorMessage = "掉落概率必须在0-1之间")]
        public decimal DropRate { get; set; }

        /// <summary>
        /// 最小掉落数量
        /// </summary>
        [Range(1, 999, ErrorMessage = "最小掉落数量必须在1-999之间")]
        public int MinCount { get; set; }

        /// <summary>
        /// 最大掉落数量
        /// </summary>
        [Range(1, 999, ErrorMessage = "最大掉落数量必须在1-999之间")]
        public int MaxCount { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [StringLength(255, ErrorMessage = "备注长度不能超过255个字符")]
        public string? Remark { get; set; }
    }

    /// <summary>
    /// 删除掉落配置DTO
    /// </summary>
    public class DropConfigDeleteDto
    {
        /// <summary>
        /// 自增主键
        /// </summary>
        [Required(ErrorMessage = "ID不能为空")]
        public int Id { get; set; }
    }

    /// <summary>
    /// 掉落类型选项DTO
    /// </summary>
    public class DropTypeOptionDto
    {
        /// <summary>
        /// 选项值
        /// </summary>
        public string Value { get; set; } = string.Empty;

        /// <summary>
        /// 选项文本
        /// </summary>
        public string Label { get; set; } = string.Empty;
    }

    /// <summary>
    /// 掉落道具项DTO
    /// </summary>
    public class DropItemDto
    {
        /// <summary>
        /// 道具ID
        /// </summary>
        public string ItemId { get; set; } = string.Empty;

        /// <summary>
        /// 掉落概率（0~1之间，1为必掉）
        /// </summary>
        public decimal DropRate { get; set; }

        /// <summary>
        /// 最小掉落数量
        /// </summary>
        public int MinCount { get; set; }

        /// <summary>
        /// 最大掉落数量
        /// </summary>
        public int MaxCount { get; set; }
    }

    /// <summary>
    /// 多道具掉落配置创建DTO
    /// </summary>
    public class MultiDropConfigCreateDto
    {
        /// <summary>
        /// 地图ID
        /// </summary>
        [Required(ErrorMessage = "地图ID不能为空")]
        public int MapId { get; set; }

        /// <summary>
        /// 掉落类型
        /// </summary>
        [Required(ErrorMessage = "掉落类型不能为空")]
        public string DropType { get; set; } = string.Empty;

        /// <summary>
        /// 怪物ID（怪物掉落时必填）
        /// </summary>
        public int? MonsterId { get; set; }

        /// <summary>
        /// 掉落道具列表
        /// </summary>
        [Required(ErrorMessage = "掉落道具列表不能为空")]
        public List<DropItemDto> DropItems { get; set; } = new List<DropItemDto>();

        /// <summary>
        /// 备注
        /// </summary>
        [StringLength(255, ErrorMessage = "备注长度不能超过255个字符")]
        public string? Remark { get; set; }
    }

    /// <summary>
    /// 多道具掉落配置更新DTO
    /// </summary>
    public class MultiDropConfigUpdateDto
    {
        /// <summary>
        /// 自增主键
        /// </summary>
        [Required(ErrorMessage = "ID不能为空")]
        public int Id { get; set; }

        /// <summary>
        /// 地图ID
        /// </summary>
        [Required(ErrorMessage = "地图ID不能为空")]
        public int MapId { get; set; }

        /// <summary>
        /// 掉落类型
        /// </summary>
        [Required(ErrorMessage = "掉落类型不能为空")]
        public string DropType { get; set; } = string.Empty;

        /// <summary>
        /// 怪物ID（怪物掉落时必填）
        /// </summary>
        public int? MonsterId { get; set; }

        /// <summary>
        /// 掉落道具列表
        /// </summary>
        [Required(ErrorMessage = "掉落道具列表不能为空")]
        public List<DropItemDto> DropItems { get; set; } = new List<DropItemDto>();

        /// <summary>
        /// 备注
        /// </summary>
        [StringLength(255, ErrorMessage = "备注长度不能超过255个字符")]
        public string? Remark { get; set; }
    }
} 