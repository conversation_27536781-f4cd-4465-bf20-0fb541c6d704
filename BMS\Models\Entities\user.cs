﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace BMS.Models.Entities
{
    ///<summary>
    ///用户信息表
    ///</summary>
    [SugarTable("user")]
    public partial class user
    {
           public user(){


           }
           /// <summary>
           /// Desc:自增主键
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true,IsIdentity=true)]
           public int id {get;set;}

           /// <summary>
           /// Desc:用户名/账号
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string username {get;set;}

           /// <summary>
           /// Desc:密码（加密存储）
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string password {get;set;}

           /// <summary>
           /// Desc:角色昵称
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? nickname {get;set;}

           /// <summary>
           /// Desc:性别
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? sex {get;set;}

           /// <summary>
           /// Desc:VIP等级
           /// Default:0
           /// Nullable:True
           /// </summary>           
           public int? vip_level {get;set;}

           /// <summary>
           /// Desc:VIP积分
           /// Default:0
           /// Nullable:True
           /// </summary>           
           public int? vip_score {get;set;}

           /// <summary>
           /// Desc:金币
           /// Default:0
           /// Nullable:True
           /// </summary>           
           public long? gold {get;set;}

           /// <summary>
           /// Desc:元宝
           /// Default:0
           /// Nullable:True
           /// </summary>           
           public int? yuanbao {get;set;}

           /// <summary>
           /// Desc:水晶
           /// Default:0
           /// Nullable:True
           /// </summary>           
           public int? crystal {get;set;}

           /// <summary>
           /// Desc:注册时间
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? reg_time {get;set;}

           /// <summary>
           /// Desc:称号
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? title {get;set;}

           /// <summary>
           /// Desc:主宠物ID
           /// Default:
           /// Nullable:True
           /// </summary>           
           public int? main_pet_id {get;set;}

           /// <summary>
           /// Desc:主宠物名称
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? main_pet_name {get;set;}

           /// <summary>
           /// Desc:创建时间
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? create_time {get;set;}

           /// <summary>
           /// Desc:进化冷却时间
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? evolution_cd {get;set;}

           /// <summary>
           /// Desc:合成冷却时间
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? synthesis_cd {get;set;}

           /// <summary>
           /// Desc:转生冷却时间
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? nirvana_cd {get;set;}

           /// <summary>
           /// Desc:金币数量
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? money {get;set;}

           /// <summary>
           /// Desc:是否至尊VIP
           /// Default:true
           /// Nullable:True
           /// </summary>           
           public bool? supreme_vip {get;set;}

           /// <summary>
           /// Desc:道具容量
           /// Default:
           /// Nullable:True
           /// </summary>           
           public int? prop_capacity {get;set;}

           /// <summary>
           /// Desc:牧场容量
           /// Default:
           /// Nullable:True
           /// </summary>           
           public int? pasture_capacity {get;set;}

           /// <summary>
           /// Desc:龙珠经验
           /// Default:
           /// Nullable:True
           /// </summary>           
           public long? dragon_ball_exp {get;set;}

           /// <summary>
           /// Desc:龙珠等级
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? dragon_ball_level {get;set;}

           /// <summary>
           /// Desc:用户状态 (0正常 1封禁)
           /// Default:
           /// Nullable:True
           /// </summary>           
           public int? status {get;set;}

           /// <summary>
           /// Desc:威望值
           /// Default:0
           /// Nullable:True
           /// </summary>           
           public int? prestige {get;set;}

           /// <summary>
           /// Desc:自动战斗次数
           /// Default:0
           /// Nullable:True
           /// </summary>           
           public int? auto_battle_count {get;set;}

           /// <summary>
           /// Desc:自动合成涅槃次数
           /// Default:0
           /// Nullable:True
           /// </summary>           
           public int? auto_nirvana_count {get;set;}

           /// <summary>
           /// Desc:刷怪数
           /// Default:0
           /// Nullable:True
           /// </summary>           
           public int? monster_count {get;set;}

           /// <summary>
           /// Desc:下次战斗遭遇怪物ID
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? next_battle {get;set;}

           /// <summary>
           /// Desc:已开启地图列表（JSON格式）
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? open_maps {get;set;}

           /// <summary>
           /// Desc:星辰VIP状态
           /// Default:true
           /// Nullable:True
           /// </summary>           
           public bool? star_vip {get;set;}

           /// <summary>
           /// Desc:星辰VIP过期时间
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? star_vip_expire {get;set;}

           /// <summary>
           /// Desc:任务助手开启状态
           /// Default:true
           /// Nullable:True
           /// </summary>           
           public bool? task_helper_enabled {get;set;}

           /// <summary>
           /// Desc:称号属性加成(JSON格式)
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? title_attributes {get;set;}

           /// <summary>
           /// Desc:当前装备的魂宠ID
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? soul_pet {get;set;}

           /// <summary>
           /// Desc:地狱之门当前层数
           /// Default:0
           /// Nullable:True
           /// </summary>           
           public int? hell_floor {get;set;}

           /// <summary>
           /// Desc:通天塔当前层数
           /// Default:0
           /// Nullable:True
           /// </summary>           
           public int? tower_floor {get;set;}

    }
}
