using BMS.Models.DTOs;
using BMS.Services.Interfaces;
using Microsoft.AspNetCore.Mvc;

namespace BMS.Controllers
{
    /// <summary>
    /// 用户道具管理控制器
    /// </summary>
    public class UserItemController : Controller
    {
        private readonly IUserItemService _userItemService;

        public UserItemController(IUserItemService userItemService)
        {
            _userItemService = userItemService;
        }

        /// <summary>
        /// 用户道具管理主页
        /// </summary>
        /// <returns></returns>
        public IActionResult Index()
        {
            return View();
        }

        /// <summary>
        /// 获取用户道具列表
        /// </summary>
        /// <param name="queryDto">查询条件</param>
        /// <returns></returns>
        [HttpPost]
        public async Task<IActionResult> GetList([FromBody] UserItemQueryDto queryDto)
        {
            try
            {
                var result = await _userItemService.GetUserItemListAsync(queryDto);
                return Json(new { success = true, data = result });
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = ex.Message });
            }
        }

        /// <summary>
        /// 获取道具详情
        /// </summary>
        /// <param name="id">记录ID</param>
        /// <returns></returns>
        [HttpGet]
        public async Task<IActionResult> GetDetail(int id)
        {
            try
            {
                var result = await _userItemService.GetUserItemByIdAsync(id);
                if (result == null)
                {
                    return Json(new { success = false, message = "道具记录不存在" });
                }
                return Json(new { success = true, data = result });
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = ex.Message });
            }
        }

        /// <summary>
        /// 添加用户道具
        /// </summary>
        /// <param name="addDto">添加信息</param>
        /// <returns></returns>
        [HttpPost]
        public async Task<IActionResult> Add([FromBody] UserItemAddDto addDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    var firstError = ModelState.Values
                        .SelectMany(v => v.Errors)
                        .FirstOrDefault()?.ErrorMessage;
                    return Json(new { success = false, message = firstError ?? "数据验证失败" });
                }

                var result = await _userItemService.AddUserItemAsync(addDto);
                if (result)
                {
                    return Json(new { success = true, message = "添加成功" });
                }
                else
                {
                    return Json(new { success = false, message = "添加失败" });
                }
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = ex.Message });
            }
        }

        /// <summary>
        /// 更新用户道具
        /// </summary>
        /// <param name="updateDto">更新信息</param>
        /// <returns></returns>
        [HttpPost]
        public async Task<IActionResult> Update([FromBody] UserItemUpdateDto updateDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    var firstError = ModelState.Values
                        .SelectMany(v => v.Errors)
                        .FirstOrDefault()?.ErrorMessage;
                    return Json(new { success = false, message = firstError ?? "数据验证失败" });
                }

                var result = await _userItemService.UpdateUserItemAsync(updateDto);
                if (result)
                {
                    return Json(new { success = true, message = "更新成功" });
                }
                else
                {
                    return Json(new { success = false, message = "更新失败" });
                }
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = ex.Message });
            }
        }

        /// <summary>
        /// 删除用户道具
        /// </summary>
        /// <param name="deleteDto">删除信息</param>
        /// <returns></returns>
        [HttpPost]
        public async Task<IActionResult> Delete([FromBody] UserItemDeleteDto deleteDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    var firstError = ModelState.Values
                        .SelectMany(v => v.Errors)
                        .FirstOrDefault()?.ErrorMessage;
                    return Json(new { success = false, message = firstError ?? "数据验证失败" });
                }

                var result = await _userItemService.DeleteUserItemAsync(deleteDto);
                if (result)
                {
                    return Json(new { success = true, message = "删除成功" });
                }
                else
                {
                    return Json(new { success = false, message = "删除失败" });
                }
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = ex.Message });
            }
        }

        /// <summary>
        /// 获取统计信息
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public async Task<IActionResult> GetStatistics()
        {
            try
            {
                var result = await _userItemService.GetUserItemStatisticsAsync();
                return Json(new { success = true, data = result });
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = ex.Message });
            }
        }

        /// <summary>
        /// 获取所有道具配置
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public async Task<IActionResult> GetItemConfigs()
        {
            try
            {
                var result = await _userItemService.GetAllItemConfigsAsync();
                return Json(new { success = true, data = result });
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = ex.Message });
            }
        }

        /// <summary>
        /// 根据用户ID获取道具列表
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns></returns>
        [HttpGet]
        public async Task<IActionResult> GetByUserId(int userId)
        {
            try
            {
                var result = await _userItemService.GetUserItemsByUserIdAsync(userId);
                return Json(new { success = true, data = result });
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = ex.Message });
            }
        }

        /// <summary>
        /// 批量删除用户道具
        /// </summary>
        /// <param name="ids">记录ID列表</param>
        /// <returns></returns>
        [HttpPost]
        public async Task<IActionResult> BatchDelete([FromBody] List<int> ids)
        {
            try
            {
                if (ids == null || !ids.Any())
                {
                    return Json(new { success = false, message = "请选择要删除的记录" });
                }

                var result = await _userItemService.BatchDeleteUserItemsAsync(ids);
                if (result)
                {
                    return Json(new { success = true, message = $"成功删除{ids.Count}条记录" });
                }
                else
                {
                    return Json(new { success = false, message = "删除失败" });
                }
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = ex.Message });
            }
        }
    }
} 