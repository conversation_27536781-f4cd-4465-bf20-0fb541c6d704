using SqlSugar;
using BMS.Services.Interfaces;
using BMS.Services.Implementations;
using BMS.Sugar;
using Microsoft.AspNetCore.Authentication.Cookies;
using System.Text.Encodings.Web;
using System.Text.Json;
using Serilog;

var builder = WebApplication.CreateBuilder(args);

// 配置 Serilog
Log.Logger = new LoggerConfiguration()
    .ReadFrom.Configuration(builder.Configuration)
    .CreateLogger();

// 使用 Serilog 作为日志提供程序
builder.Host.UseSerilog();

// Add services to the container.
builder.Services.AddControllersWithViews()
    .AddJsonOptions(options =>
    {
        // 配置JSON序列化选项，支持中文字符正常显示
        options.JsonSerializerOptions.Encoder = JavaScriptEncoder.UnsafeRelaxedJsonEscaping;
        options.JsonSerializerOptions.PropertyNamingPolicy = JsonNamingPolicy.CamelCase; // 使用camelCase命名
        options.JsonSerializerOptions.WriteIndented = true; // 格式化输出
    });

// 注册您的自定义 DbContext
builder.Services.AddScoped<DbContext>();

// 配置 ISqlSugarClient（为了兼容现有服务）
builder.Services.AddScoped<ISqlSugarClient>(provider =>
{
    var dbContext = provider.GetRequiredService<DbContext>();
    return dbContext.Db;
});

// 注册数据库服务
builder.Services.AddScoped<IDbService, DbService>();

// 注册认证服务
builder.Services.AddScoped<IAuthService, AuthService>();

// 注册管理员服务
builder.Services.AddScoped<IAdminBmService, AdminBmService>();

// 注册地图系统服务
builder.Services.AddScoped<IMapConfigService, MapConfigService>();
builder.Services.AddScoped<IMapDetailService, MapDetailService>();
builder.Services.AddScoped<IMapMonsterService, MapMonsterService>();

// 注册宠物配置服务
builder.Services.AddScoped<IPetConfigService, PetConfigService>();

// 注册怪物配置服务
builder.Services.AddScoped<IMonsterConfigService, MonsterConfigService>();

// 注册技能服务
builder.Services.AddScoped<ISkillService, SkillService>();

// 注册装备服务
builder.Services.AddScoped<IEquipmentService, EquipmentService>();

// 注册用户服务
builder.Services.AddScoped<IUserService, UserService>();

// 注册用户宠物服务
builder.Services.AddScoped<IUserPetService, UserPetService>();

// 注册境界服务
builder.Services.AddScoped<IRealmService, RealmService>();
builder.Services.AddScoped<IUserEquipmentService, UserEquipmentService>();

// 注册用户道具服务
builder.Services.AddScoped<IUserItemService, UserItemService>();

// 注册道具配置服务
builder.Services.AddScoped<IItemConfigService, ItemConfigService>();

// 注册道具脚本服务
builder.Services.AddScoped<IItemScriptService, ItemScriptService>();

// 注册掉落配置服务
builder.Services.AddScoped<IDropConfigService, DropConfigService>();

// 注册任务管理服务
builder.Services.AddScoped<ITaskConfigService, TaskConfigService>();

// 注册宠物进化配置服务
builder.Services.AddScoped<IPetEvolutionConfigService, PetEvolutionConfigService>();

// 注册宠物合成公式服务
builder.Services.AddScoped<IPetSynthesisFormulaService, PetSynthesisFormulaService>();

// 配置身份验证
builder.Services.AddAuthentication(CookieAuthenticationDefaults.AuthenticationScheme)
    .AddCookie(options =>
    {
        options.LoginPath = "/Auth/Login";
        options.LogoutPath = "/Auth/Logout";
        options.ExpireTimeSpan = TimeSpan.FromMinutes(30);
        options.SlidingExpiration = true;
        options.Cookie.Name = "BMS.Auth";
        options.Cookie.HttpOnly = true;
        options.Cookie.SecurePolicy = CookieSecurePolicy.SameAsRequest;
    });

// 配置Session
builder.Services.AddSession(options =>
{
    options.IdleTimeout = TimeSpan.FromMinutes(30);
    options.Cookie.HttpOnly = true;
    options.Cookie.IsEssential = true;
    options.Cookie.Name = "BMS.Session";
});

var app = builder.Build();

// 测试日志配置
app.Logger.LogInformation("🚀 应用程序启动，日志系统已配置");
app.Logger.LogDebug("🔧 调试级别日志测试");

try
{
    // 测试 DbContext 日志
    using (var scope = app.Services.CreateScope())
    {
        var dbContext = scope.ServiceProvider.GetRequiredService<DbContext>();
        app.Logger.LogInformation("✅ DbContext 初始化成功，日志文件位置: logs/");
    }
}
catch (Exception ex)
{
    app.Logger.LogError(ex, "❌ DbContext 初始化失败");
}

// Configure the HTTP request pipeline.
if (!app.Environment.IsDevelopment())
{
    app.UseExceptionHandler("/Home/Error");
    // The default HSTS value is 30 days. You may want to change this for production scenarios, see https://aka.ms/aspnetcore-hsts.
    app.UseHsts();
}

app.UseHttpsRedirection();
app.UseStaticFiles();

app.UseRouting();

// 启用Session
app.UseSession();

// 启用身份验证和授权
app.UseAuthentication();
app.UseAuthorization();

app.MapControllerRoute(
    name: "default",
    pattern: "{controller=Home}/{action=Index}/{id?}");

app.Run();
