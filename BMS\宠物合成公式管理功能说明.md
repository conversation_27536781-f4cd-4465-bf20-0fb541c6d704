# 宠物合成公式管理功能说明

## 📋 功能概述

宠物合成公式管理是游戏后台管理系统的核心功能之一，用于配置和管理游戏中宠物的合成规则。管理员可以通过此功能设置宠物合成的条件、消耗资源、成功率等参数。

## 🎯 主要功能

### 1. 合成公式管理
- **新增公式**: 创建新的宠物合成规则
- **编辑公式**: 修改现有的合成公式
- **删除公式**: 删除不需要的合成公式
- **批量删除**: 一次性删除多个公式
- **状态切换**: 激活/禁用合成公式

### 2. 智能搜索
- **多条件筛选**: 支持主宠物、副宠物、结果宠物、公式类型等多种筛选条件
- **可搜索下拉框**: 宠物选择支持实时搜索和过滤
- **等级范围**: 支持按等级要求范围筛选

### 3. 公式类型支持
- **FIXED**: 固定公式 - 指定宠物合成指定结果
- **RANDOM_GOD**: 随机神宠 - 合成随机神级宠物
- **RANDOM_HOLY**: 随机神圣宠物 - 合成随机神圣级宠物

### 4. 数据验证
- **前端验证**: 表单字段格式验证
- **后端验证**: 业务逻辑验证
- **重复检查**: 防止创建重复的合成公式
- **关联验证**: 确保宠物的存在性

## 🚀 使用指南

### 访问路径
1. 登录后台管理系统
2. 点击导航菜单 "游戏管理"
3. 选择 "宠物合成公式"

### 新增合成公式
1. 点击 "新增公式" 按钮
2. 填写必要信息：
   - **主宠物**: 选择作为主材料的宠物
   - **副宠物**: 选择作为副材料的宠物
   - **主宠最小成长要求**: 主宠物的最低成长值要求
   - **副宠最小成长要求**: 副宠物的最低成长值要求
   - **合成结果宠物**: 选择合成后得到的宠物
   - **公式类型**: 选择合成公式的类型
   - **基础成功率**: 设置合成的基础成功概率
   - **所需等级**: 设置合成所需的最低等级
   - **消耗金币**: 设置合成消耗的金币数量
3. 点击 "保存" 完成创建

### 搜索和筛选
- **主宠物**: 筛选指定的主宠物
- **副宠物**: 筛选指定的副宠物
- **结果宠物**: 筛选特定的合成结果
- **公式类型**: 筛选固定公式、随机神宠或随机神圣宠物
- **激活状态**: 筛选已激活或已禁用的公式
- **等级要求**: 设置等级范围进行筛选

### 批量操作
1. 勾选需要操作的公式项
2. 点击 "批量删除" 按钮
3. 确认操作

## 📊 配置参数说明

### 基础参数
- **主宠物编号**: 关联pet_config表的pet_no字段
- **副宠物编号**: 关联pet_config表的pet_no字段
- **合成结果宠物编号**: 合成后得到的宠物编号

### 成长要求
- **主宠最小成长要求**: 主宠物必须达到的最小成长值
- **副宠最小成长要求**: 副宠物必须达到的最小成长值

### 合成条件
- **所需等级**: 宠物达到此等级才能进行合成
- **消耗金币**: 合成需要的金币数量
- **基础成功率**: 合成成功的基础概率（0-100%）

### 公式类型
- **FIXED（固定公式）**: 指定的宠物组合合成指定的结果宠物
- **RANDOM_GOD（随机神宠）**: 合成随机的神级宠物
- **RANDOM_HOLY（随机神圣宠物）**: 合成随机的神圣级宠物

### 状态控制
- **激活状态**: 控制此公式是否生效
- **创建时间**: 公式的创建时间

## 🔧 技术说明

### 数据库表结构
基于 `pet_synthesis_formula` 表：
```sql
CREATE TABLE pet_synthesis_formula (
    id INT AUTO_INCREMENT PRIMARY KEY,
    main_pet_no INT NOT NULL,
    sub_pet_no INT NOT NULL,
    main_growth_min DECIMAL(9,6) DEFAULT 0.000000,
    sub_growth_min DECIMAL(9,6) DEFAULT 0.000000,
    result_pet_no INT NOT NULL,
    base_success_rate DECIMAL(5,2) DEFAULT 50.00,
    required_level INT DEFAULT 40,
    cost_gold BIGINT DEFAULT 50000,
    formula_type VARCHAR(20) DEFAULT 'FIXED',
    is_active TINYINT DEFAULT 1,
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

### API接口
- `POST /PetSynthesisFormula/GetList` - 获取公式列表
- `POST /PetSynthesisFormula/Create` - 创建公式
- `POST /PetSynthesisFormula/Update` - 更新公式
- `POST /PetSynthesisFormula/Delete` - 删除公式
- `POST /PetSynthesisFormula/BatchDelete` - 批量删除
- `POST /PetSynthesisFormula/ToggleActive` - 切换状态
- `GET /PetSynthesisFormula/GetPetConfigOptions` - 获取宠物选项
- `GET /PetSynthesisFormula/CheckSynthesisFormulaExists` - 检查公式是否存在
- `GET /PetSynthesisFormula/GetSynthesisFormulasByPetNo` - 根据宠物获取公式
- `GET /PetSynthesisFormula/ValidateSynthesisConditions` - 验证合成条件

## ⚠️ 注意事项

1. **唯一性约束**: 同一主宠物和副宠物的组合只能有一个公式
2. **关联性检查**: 主宠物、副宠物和结果宠物必须在pet_config表中存在
3. **成长值范围**: 成长要求需要合理设置，建议在0-1之间
4. **成功率**: 建议根据公式类型设置合理的成功率
   - 固定公式：70%-90%
   - 随机神宠：30%-50%
   - 随机神圣宠物：10%-30%
5. **等级要求**: 建议根据游戏平衡性设置合理的等级要求
6. **金币消耗**: 高级合成应设置更高的金币消耗

## 🎮 游戏平衡建议

### 固定公式设计
- 低级宠物 → 中级宠物：成功率80-90%，消耗较少
- 中级宠物 → 高级宠物：成功率70-80%，消耗适中
- 高级宠物 → 神级宠物：成功率60-70%，消耗较高

### 随机公式设计
- 随机神宠：成功率30-50%，消耗很高，结果随机但保证神级
- 随机神圣宠物：成功率10-30%，消耗极高，结果随机但保证神圣级

### 成长要求建议
- 初级合成：0.2-0.4
- 中级合成：0.4-0.6
- 高级合成：0.6-0.8
- 神级合成：0.8-1.0

## 🐛 常见问题

### Q: 为什么无法创建合成公式？
A: 请检查：
- 主宠物、副宠物、结果宠物是否都存在
- 是否已存在相同的宠物组合公式
- 参数值是否在有效范围内

### Q: 如何设置合理的成功率？
A: 建议：
- 考虑游戏平衡性
- 参考宠物稀有度
- 根据消耗成本调整

### Q: 合成公式不生效怎么办？
A: 请确认：
- 公式状态是否为"已激活"
- 游戏客户端是否已更新配置
- 宠物等级和成长是否满足要求

## 📞 技术支持

如有问题，请联系开发团队或查看系统日志获取详细错误信息。
