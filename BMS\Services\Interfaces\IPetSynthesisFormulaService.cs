using BMS.Models.DTOs;
using BMS.Models.Common;

namespace BMS.Services.Interfaces
{
    /// <summary>
    /// 宠物合成公式服务接口
    /// </summary>
    public interface IPetSynthesisFormulaService
    {
        /// <summary>
        /// 获取宠物合成公式列表（分页）
        /// </summary>
        /// <param name="queryDto">查询条件</param>
        /// <returns>分页结果</returns>
        Task<PagedResult<PetSynthesisFormulaDetailDto>> GetPetSynthesisFormulasAsync(PetSynthesisFormulaQueryDto queryDto);

        /// <summary>
        /// 根据ID获取宠物合成公式
        /// </summary>
        /// <param name="id">公式ID</param>
        /// <returns>公式详情</returns>
        Task<ApiResult<PetSynthesisFormulaDetailDto>> GetByIdAsync(int id);

        /// <summary>
        /// 新增宠物合成公式
        /// </summary>
        /// <param name="createDto">新增DTO</param>
        /// <returns>操作结果</returns>
        Task<ApiResult<int>> CreateAsync(PetSynthesisFormulaCreateDto createDto);

        /// <summary>
        /// 更新宠物合成公式
        /// </summary>
        /// <param name="updateDto">更新DTO</param>
        /// <returns>操作结果</returns>
        Task<ApiResult<bool>> UpdateAsync(PetSynthesisFormulaUpdateDto updateDto);

        /// <summary>
        /// 删除宠物合成公式
        /// </summary>
        /// <param name="id">公式ID</param>
        /// <returns>操作结果</returns>
        Task<ApiResult<bool>> DeleteAsync(int id);

        /// <summary>
        /// 批量删除宠物合成公式
        /// </summary>
        /// <param name="ids">公式ID列表</param>
        /// <returns>操作结果</returns>
        Task<ApiResult<bool>> BatchDeleteAsync(List<int> ids);

        /// <summary>
        /// 切换激活状态
        /// </summary>
        /// <param name="id">公式ID</param>
        /// <param name="isActive">是否激活</param>
        /// <returns>操作结果</returns>
        Task<ApiResult<bool>> ToggleActiveAsync(int id, bool isActive);

        /// <summary>
        /// 获取宠物配置选项
        /// </summary>
        /// <returns>宠物配置选项</returns>
        Task<List<PetConfigOptionDto>> GetPetConfigOptionsAsync();

        /// <summary>
        /// 检查合成公式是否存在
        /// </summary>
        /// <param name="mainPetNo">主宠物编号</param>
        /// <param name="subPetNo">副宠物编号</param>
        /// <param name="id">排除的ID</param>
        /// <returns>是否存在</returns>
        Task<bool> CheckSynthesisFormulaExistsAsync(int mainPetNo, int subPetNo, int? id = null);

        /// <summary>
        /// 根据宠物编号获取合成公式
        /// </summary>
        /// <param name="mainPetNo">主宠物编号</param>
        /// <param name="subPetNo">副宠物编号</param>
        /// <returns>合成公式列表</returns>
        Task<List<PetSynthesisFormulaDetailDto>> GetSynthesisFormulasByPetNoAsync(int mainPetNo, int subPetNo);

        /// <summary>
        /// 验证合成条件
        /// </summary>
        /// <param name="mainPetId">主宠物ID</param>
        /// <param name="subPetId">副宠物ID</param>
        /// <returns>验证结果</returns>
        Task<ApiResult<bool>> ValidateSynthesisConditionsAsync(int mainPetId, int subPetId);
    }
}
