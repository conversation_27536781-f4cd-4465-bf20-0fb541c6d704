using BMS.Models.DTOs;
using BMS.Models.Common;
using BMS.Services.Interfaces;
using BMS.Models.Entities;
using BMS.Common.Helpers;
using BMS.Sugar;
using SqlSugar;

namespace BMS.Services.Implementations
{
    /// <summary>
    /// 管理员服务实现
    /// </summary>
    public class AdminBmService : IAdminBmService
    {
        private readonly DbContext _dbContext;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="dbContext">自定义数据库上下文</param>
        public AdminBmService(DbContext dbContext)
        {
            _dbContext = dbContext;
        }

        /// <summary>
        /// 分页获取管理员列表
        /// </summary>
        /// <param name="queryDto">查询条件</param>
        /// <returns>分页结果</returns>
        public async Task<PagedResult<AdminBmDto>> GetPagedListAsync(AdminBmQueryDto queryDto)
        {
            try
            {
                var query = _dbContext.Db.Queryable<admin_bm>();

                // 用户名模糊查询
                if (!string.IsNullOrEmpty(queryDto.Username))
                {
                    query = query.Where(x => x.username.Contains(queryDto.Username));
                }

                // 真实姓名模糊查询
                if (!string.IsNullOrEmpty(queryDto.RealName))
                {
                    query = query.Where(x => x.real_name != null && x.real_name.Contains(queryDto.RealName));
                }

                // 手机号模糊查询
                if (!string.IsNullOrEmpty(queryDto.Phone))
                {
                    query = query.Where(x => x.phone != null && x.phone.Contains(queryDto.Phone));
                }

                // 状态筛选
                if (queryDto.Status.HasValue)
                {
                    bool statusBool = queryDto.Status.Value == 1;
                    query = query.Where(x => x.status == statusBool);
                }

                // 创建时间范围查询
                if (queryDto.CreateTimeStart.HasValue)
                {
                    query = query.Where(x => x.create_time >= queryDto.CreateTimeStart.Value);
                }
                if (queryDto.CreateTimeEnd.HasValue)
                {
                    query = query.Where(x => x.create_time <= queryDto.CreateTimeEnd.Value.AddDays(1));
                }

                // 获取总数
                int totalCount = await query.CountAsync();

                // 分页查询
                var items = await query
                    .OrderByDescending(x => x.create_time)
                    .Skip((queryDto.PageIndex - 1) * queryDto.PageSize)
                    .Take(queryDto.PageSize)
                    .ToListAsync();

                // 转换为DTO
                var dtoList = items.Select(MapToDto).ToList();

                return new PagedResult<AdminBmDto>(dtoList, queryDto.PageIndex, queryDto.PageSize, totalCount);
            }
            catch (Exception ex)
            {
                throw new Exception($"获取管理员列表失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 根据ID获取管理员信息
        /// </summary>
        /// <param name="id">管理员ID</param>
        /// <returns>管理员信息</returns>
        public async Task<AdminBmDto?> GetByIdAsync(int id)
        {
            try
            {
                var entity = await _dbContext.Db.Queryable<admin_bm>()
                    .Where(x => x.id == id)
                    .FirstAsync();

                return entity != null ? MapToDto(entity) : null;
            }
            catch (Exception ex)
            {
                throw new Exception($"获取管理员信息失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 根据用户名获取管理员信息
        /// </summary>
        /// <param name="username">用户名</param>
        /// <returns>管理员信息</returns>
        public async Task<AdminBmDto?> GetByUsernameAsync(string username)
        {
            try
            {
                var entity = await _dbContext.Db.Queryable<admin_bm>()
                    .Where(x => x.username == username)
                    .FirstAsync();

                return entity != null ? MapToDto(entity) : null;
            }
            catch (Exception ex)
            {
                throw new Exception($"获取管理员信息失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 创建管理员
        /// </summary>
        /// <param name="createDto">创建管理员DTO</param>
        /// <returns>创建结果</returns>
        public async Task<ApiResult<int>> CreateAsync(AdminBmCreateDto createDto)
        {
            try
            {
                // 检查用户名是否已存在
                bool exists = await CheckUsernameExistsAsync(createDto.Username);
                if (exists)
                {
                    return ApiResult<int>.Fail("用户名已存在");
                }

                // 使用简单MD5加密密码
                string encryptedPassword = PasswordHelper.Md5Hash(createDto.Password);

                // 创建实体
                var entity = new admin_bm
                {
                    username = createDto.Username,
                    password = encryptedPassword,
                    real_name = createDto.RealName,
                    phone = createDto.Phone,
                    email = createDto.Email,
                    status = createDto.Status == 1,
                    create_time = DateTime.Now,
                    update_time = DateTime.Now
                };

                // 插入数据库
                var result = await _dbContext.Db.Insertable(entity).ExecuteReturnIdentityAsync();
                
                return ApiResult<int>.Ok(result, "创建管理员成功");
            }
            catch (Exception ex)
            {
                return ApiResult<int>.Fail($"创建管理员失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 更新管理员信息
        /// </summary>
        /// <param name="updateDto">更新管理员DTO</param>
        /// <returns>更新结果</returns>
        public async Task<ApiResult<bool>> UpdateAsync(AdminBmUpdateDto updateDto)
        {
            try
            {
                // 检查管理员是否存在
                var existing = await _dbContext.Db.Queryable<admin_bm>()
                    .Where(x => x.id == updateDto.Id)
                    .FirstAsync();

                if (existing == null)
                {
                    return ApiResult<bool>.Fail("管理员不存在");
                }

                // 检查用户名是否已被其他用户使用
                bool exists = await CheckUsernameExistsAsync(updateDto.Username, updateDto.Id);
                if (exists)
                {
                    return ApiResult<bool>.Fail("用户名已被其他用户使用");
                }

                // 更新实体
                existing.username = updateDto.Username;
                existing.real_name = updateDto.RealName;
                existing.phone = updateDto.Phone;
                existing.email = updateDto.Email;
                existing.status = updateDto.Status == 1;
                existing.update_time = DateTime.Now;

                // 更新数据库
                int rows = await _dbContext.Db.Updateable(existing).ExecuteCommandAsync();
                
                return rows > 0 
                    ? ApiResult<bool>.Ok(true, "更新管理员信息成功")
                    : ApiResult<bool>.Fail("更新管理员信息失败");
            }
            catch (Exception ex)
            {
                return ApiResult<bool>.Fail($"更新管理员信息失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 删除管理员
        /// </summary>
        /// <param name="id">管理员ID</param>
        /// <returns>删除结果</returns>
        public async Task<ApiResult<bool>> DeleteAsync(int id)
        {
            try
            {
                // 检查管理员是否存在
                var existing = await _dbContext.Db.Queryable<admin_bm>()
                    .Where(x => x.id == id)
                    .FirstAsync();

                if (existing == null)
                {
                    return ApiResult<bool>.Fail("管理员不存在");
                }

                // 删除管理员
                int rows = await _dbContext.Db.Deleteable<admin_bm>()
                    .Where(x => x.id == id)
                    .ExecuteCommandAsync();

                return rows > 0 
                    ? ApiResult<bool>.Ok(true, "删除管理员成功")
                    : ApiResult<bool>.Fail("删除管理员失败");
            }
            catch (Exception ex)
            {
                return ApiResult<bool>.Fail($"删除管理员失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 更改管理员状态
        /// </summary>
        /// <param name="id">管理员ID</param>
        /// <param name="status">状态：0-禁用，1-启用</param>
        /// <returns>更新结果</returns>
        public async Task<ApiResult<bool>> ChangeStatusAsync(int id, int status)
        {
            try
            {
                bool statusBool = status == 1;
                int rows = await _dbContext.Db.Updateable<admin_bm>()
                    .SetColumns(x => new admin_bm { status = statusBool, update_time = DateTime.Now })
                    .Where(x => x.id == id)
                    .ExecuteCommandAsync();

                return rows > 0 
                    ? ApiResult<bool>.Ok(true, $"管理员状态已{(statusBool ? "启用" : "禁用")}")
                    : ApiResult<bool>.Fail("更改管理员状态失败");
            }
            catch (Exception ex)
            {
                return ApiResult<bool>.Fail($"更改管理员状态失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 重置密码
        /// </summary>
        /// <param name="id">管理员ID</param>
        /// <param name="newPassword">新密码</param>
        /// <returns>重置结果</returns>
        public async Task<ApiResult<bool>> ResetPasswordAsync(int id, string newPassword)
        {
            try
            {
                // 使用简单MD5加密新密码
                string encryptedPassword = PasswordHelper.Md5Hash(newPassword);

                int rows = await _dbContext.Db.Updateable<admin_bm>()
                    .SetColumns(x => new admin_bm { password = encryptedPassword, update_time = DateTime.Now })
                    .Where(x => x.id == id)
                    .ExecuteCommandAsync();

                return rows > 0 
                    ? ApiResult<bool>.Ok(true, "重置密码成功")
                    : ApiResult<bool>.Fail("重置密码失败");
            }
            catch (Exception ex)
            {
                return ApiResult<bool>.Fail($"重置密码失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 检查用户名是否已存在
        /// </summary>
        /// <param name="username">用户名</param>
        /// <param name="excludeId">排除的ID（用于编辑时检查）</param>
        /// <returns>是否存在</returns>
        public async Task<bool> CheckUsernameExistsAsync(string username, int? excludeId = null)
        {
            try
            {
                var query = _dbContext.Db.Queryable<admin_bm>().Where(x => x.username == username);
                
                if (excludeId.HasValue)
                {
                    query = query.Where(x => x.id != excludeId.Value);
                }

                return await query.AnyAsync();
            }
            catch (Exception ex)
            {
                throw new Exception($"检查用户名是否存在失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 实体转DTO
        /// </summary>
        /// <param name="entity">管理员实体</param>
        /// <returns>管理员DTO</returns>
        private static AdminBmDto MapToDto(admin_bm entity)
        {
            return new AdminBmDto
            {
                Id = entity.id,
                Username = entity.username,
                RealName = entity.real_name,
                Phone = entity.phone,
                Email = entity.email,
                Status = entity.status ? 1 : 0,
                CreateTime = entity.create_time,
                UpdateTime = entity.update_time,
                LastLoginTime = entity.last_login_time
            };
        }
    }
} 