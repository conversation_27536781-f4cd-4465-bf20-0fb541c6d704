using SqlSugar;
using BMS.Models.Entities;
using BMS.Models.DTOs;
using BMS.Services.Interfaces;
using BMS.Models.Common;

namespace BMS.Services.Implementations
{
    /// <summary>
    /// 技能服务实现
    /// </summary>
    public class SkillService : ISkillService
    {
        private readonly IDbService _dbService;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="dbService">数据库服务</param>
        public SkillService(IDbService dbService)
        {
            _dbService = dbService;
        }

        /// <summary>
        /// 分页查询技能列表
        /// </summary>
        /// <param name="queryDto">查询条件</param>
        /// <returns>分页结果</returns>
        public async Task<PagedResult<SkillDto>> GetPagedListAsync(SkillQueryDto queryDto)
        {
            try
            {
                var query = _dbService.Queryable<skill>();

                // 技能ID模糊查询
                if (!string.IsNullOrWhiteSpace(queryDto.SkillId))
                {
                    query = query.Where(x => x.skill_id.Contains(queryDto.SkillId));
                }

                // 技能名称模糊查询
                if (!string.IsNullOrWhiteSpace(queryDto.SkillName))
                {
                    query = query.Where(x => x.skill_name.Contains(queryDto.SkillName));
                }

                // 效果类型查询
                if (!string.IsNullOrWhiteSpace(queryDto.EffectType))
                {
                    query = query.Where(x => x.effect_type != null && x.effect_type.ToString().Contains(queryDto.EffectType));
                }

                // 五行限制查询
                if (!string.IsNullOrWhiteSpace(queryDto.ElementLimit))
                {
                    query = query.Where(x => x.element_limit != null && x.element_limit.ToString().Contains(queryDto.ElementLimit));
                }

                // 获取总数
                var totalCount = await query.CountAsync();

                // 分页查询
                var skills = await query
                    .OrderBy(x => x.create_time, OrderByType.Desc)
                    .Skip((queryDto.Page - 1) * queryDto.PageSize)
                    .Take(queryDto.PageSize)
                    .ToListAsync();

                // 转换为DTO
                var skillDtos = skills.Select(x => new SkillDto
                {
                    SkillId = x.skill_id,
                    SkillName = x.skill_name,
                    SkillPercent = x.skill_percent,
                    EffectType = x.effect_type?.ToString(),
                    EffectValue = x.effect_value?.ToString(),
                    ManaCost = x.mana_cost,
                    BuffInfo = x.buff_info?.ToString(),
                    ElementLimit = x.element_limit?.ToString(),
                    CreateTime = x.create_time ?? DateTime.Now
                }).ToList();

                return new PagedResult<SkillDto>(skillDtos, queryDto.Page, queryDto.PageSize, totalCount);
            }
            catch (Exception ex)
            {
                throw new Exception($"分页查询技能列表失败：{ex.Message}", ex);
            }
        }

        /// <summary>
        /// 根据技能ID获取技能详情
        /// </summary>
        /// <param name="skillId">技能ID</param>
        /// <returns>技能详情</returns>
        public async Task<SkillDto?> GetByIdAsync(string skillId)
        {
            try
            {
                var skillEntity = await _dbService.Queryable<skill>()
                    .FirstAsync(x => x.skill_id == skillId);

                if (skillEntity == null)
                    return null;

                return new SkillDto
                {
                    SkillId = skillEntity.skill_id,
                    SkillName = skillEntity.skill_name,
                    SkillPercent = skillEntity.skill_percent,
                    EffectType = skillEntity.effect_type?.ToString(),
                    EffectValue = skillEntity.effect_value?.ToString(),
                    ManaCost = skillEntity.mana_cost,
                    BuffInfo = skillEntity.buff_info?.ToString(),
                    ElementLimit = skillEntity.element_limit?.ToString(),
                    CreateTime = skillEntity.create_time ?? DateTime.Now
                };
            }
            catch (Exception ex)
            {
                throw new Exception($"获取技能详情失败：{ex.Message}", ex);
            }
        }

        /// <summary>
        /// 创建技能
        /// </summary>
        /// <param name="createDto">创建DTO</param>
        /// <returns>操作结果</returns>
        public async Task<ApiResult<bool>> CreateAsync(SkillCreateDto createDto)
        {
            try
            {
                // 检查技能ID是否已存在
                var exists = await CheckSkillIdExistsAsync(createDto.SkillId);
                if (exists)
                {
                    return ApiResult<bool>.Fail("技能ID已存在");
                }

                // 创建技能实体
                var skillEntity = new skill
                {
                    skill_id = createDto.SkillId,
                    skill_name = createDto.SkillName,
                    skill_percent = createDto.SkillPercent,
                    effect_type = !string.IsNullOrWhiteSpace(createDto.EffectType) ? createDto.EffectType : null,
                    effect_value = !string.IsNullOrWhiteSpace(createDto.EffectValue) ? createDto.EffectValue : null,
                    mana_cost = createDto.ManaCost,
                    buff_info = !string.IsNullOrWhiteSpace(createDto.BuffInfo) ? createDto.BuffInfo : null,
                    element_limit = !string.IsNullOrWhiteSpace(createDto.ElementLimit) ? createDto.ElementLimit : null,
                    create_time = DateTime.Now
                };

                // 插入数据库
                var result = await _dbService.Insertable(skillEntity).ExecuteCommandAsync();
                if (result > 0)
                {
                    return ApiResult<bool>.Ok(true, "技能创建成功");
                }
                else
                {
                    return ApiResult<bool>.Fail("技能创建失败");
                }
            }
            catch (Exception ex)
            {
                return ApiResult<bool>.Fail($"创建技能时发生错误：{ex.Message}");
            }
        }

        /// <summary>
        /// 更新技能
        /// </summary>
        /// <param name="updateDto">更新DTO</param>
        /// <returns>操作结果</returns>
        public async Task<ApiResult<bool>> UpdateAsync(SkillUpdateDto updateDto)
        {
            try
            {
                // 检查技能是否存在
                var existingSkill = await _dbService.Queryable<skill>()
                    .FirstAsync(x => x.skill_id == updateDto.SkillId);

                if (existingSkill == null)
                {
                    return ApiResult<bool>.Fail("技能不存在");
                }

                // 更新技能信息
                existingSkill.skill_name = updateDto.SkillName;
                existingSkill.skill_percent = updateDto.SkillPercent;
                existingSkill.effect_type = !string.IsNullOrWhiteSpace(updateDto.EffectType) ? updateDto.EffectType : null;
                existingSkill.effect_value = !string.IsNullOrWhiteSpace(updateDto.EffectValue) ? updateDto.EffectValue : null;
                existingSkill.mana_cost = updateDto.ManaCost;
                existingSkill.buff_info = !string.IsNullOrWhiteSpace(updateDto.BuffInfo) ? updateDto.BuffInfo : null;
                existingSkill.element_limit = !string.IsNullOrWhiteSpace(updateDto.ElementLimit) ? updateDto.ElementLimit : null;

                // 更新数据库
                var result = await _dbService.Updateable(existingSkill).ExecuteCommandAsync();
                if (result > 0)
                {
                    return ApiResult<bool>.Ok(true, "技能更新成功");
                }
                else
                {
                    return ApiResult<bool>.Fail("技能更新失败");
                }
            }
            catch (Exception ex)
            {
                return ApiResult<bool>.Fail($"更新技能时发生错误：{ex.Message}");
            }
        }

        /// <summary>
        /// 删除技能
        /// </summary>
        /// <param name="deleteDto">删除DTO</param>
        /// <returns>操作结果</returns>
        public async Task<ApiResult<bool>> DeleteAsync(SkillDeleteDto deleteDto)
        {
            try
            {
                // 检查技能是否存在
                var exists = await CheckSkillIdExistsAsync(deleteDto.SkillId);
                if (!exists)
                {
                    return ApiResult<bool>.Fail("技能不存在");
                }

                // 删除技能
                var result = await _dbService.Deleteable<skill>()
                    .Where(x => x.skill_id == deleteDto.SkillId)
                    .ExecuteCommandAsync();

                if (result > 0)
                {
                    return ApiResult<bool>.Ok(true, "技能删除成功");
                }
                else
                {
                    return ApiResult<bool>.Fail("技能删除失败");
                }
            }
            catch (Exception ex)
            {
                return ApiResult<bool>.Fail($"删除技能时发生错误：{ex.Message}");
            }
        }

        /// <summary>
        /// 检查技能ID是否存在
        /// </summary>
        /// <param name="skillId">技能ID</param>
        /// <returns>是否存在</returns>
        public async Task<bool> CheckSkillIdExistsAsync(string skillId)
        {
            try
            {
                return await _dbService.Queryable<skill>()
                    .AnyAsync(x => x.skill_id == skillId);
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 获取所有效果类型
        /// </summary>
        /// <returns>效果类型列表</returns>
        public async Task<List<string>> GetEffectTypesAsync()
        {
            await Task.CompletedTask;
            return new List<string>
            {
                "攻击", "命中", "防御", "生命", "法力", "经验", "金钱", "暴击", "闪避", "反击", "回复"
            };
        }

        /// <summary>
        /// 获取所有五行限制
        /// </summary>
        /// <returns>五行限制列表</returns>
        public async Task<List<string>> GetElementLimitsAsync()
        {
            await Task.CompletedTask;
            return new List<string>
            {
                "金", "木", "水", "火", "土"
            };
        }
    }
} 