@{
    ViewData["Title"] = "用户管理";
}

<!-- 科幻背景 -->
<div class="cyber-bg"></div>




<div id="userApp">
    <!-- 主控制台 -->
    <div class="cyber-card">
        <div class="cyber-card-header">
            <div class="cyber-icon">
                <i class="fas fa-users"></i>
            </div>
            <h5 class="cyber-card-title">用户管理控制台</h5>
        </div>
        <div class="container-fluid">
            <!-- 统计面板 -->
            <div class="row mb-4" v-if="showStatistics">
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="cyber-card text-center">
                        <div class="cyber-icon mb-3">
                            <i class="fas fa-users"></i>
                        </div>
                        <h3 class="text-primary mb-2" style="font-size: 2rem; color: var(--cyber-blue) !important;">{{ statistics.totalUsers || 0 }}</h3>
                        <p class="mb-0" style="color: var(--text-secondary);">用户总数</p>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="cyber-card text-center">
                        <div class="cyber-icon mb-3" style="background: linear-gradient(135deg, var(--cyber-green), var(--cyber-blue));">
                            <i class="fas fa-user-plus"></i>
                        </div>
                        <h3 class="text-success mb-2" style="font-size: 2rem; color: var(--cyber-green) !important;">{{ statistics.todayNewUsers || 0 }}</h3>
                        <p class="mb-0" style="color: var(--text-secondary);">今日新增</p>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="cyber-card text-center">
                        <div class="cyber-icon mb-3" style="background: linear-gradient(135deg, var(--cyber-orange), var(--cyber-pink));">
                            <i class="fas fa-crown"></i>
                        </div>
                        <h3 class="text-warning mb-2" style="font-size: 2rem; color: var(--cyber-orange) !important;">{{ statistics.vipUsers || 0 }}</h3>
                        <p class="mb-0" style="color: var(--text-secondary);">VIP用户</p>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="cyber-card text-center">
                        <div class="cyber-icon mb-3" style="background: linear-gradient(135deg, var(--cyber-red), var(--cyber-purple));">
                            <i class="fas fa-coins"></i>
                        </div>
                        <h3 class="text-danger mb-2" style="font-size: 2rem; color: var(--cyber-red) !important;">{{ formatNumber(statistics.totalGold) || 0 }}</h3>
                        <p class="mb-0" style="color: var(--text-secondary);">金币总量</p>
                    </div>
                </div>
            </div>

            <!-- 查询控制面板 -->
            <div class="cyber-card">
                <div class="cyber-card-header">
                    <div class="cyber-icon">
                        <i class="fas fa-search"></i>
                    </div>
                    <h5 class="cyber-card-title">查询控制面板</h5>
                    <div class="ms-auto">
                        <button type="button" class="cyber-btn cyber-btn-sm" v-on:click="toggleStatistics">
                            <i class="fas fa-chart-bar"></i> {{ showStatistics ? '隐藏' : '显示' }}统计
                        </button>
                    </div>
                </div>
                <div>
                    <div class="row">
                        <div class="col-md-2">
                            <div class="cyber-form-group">
                                <label class="cyber-form-label">用户ID</label>
                                <input type="number" class="cyber-form-control" v-model="queryForm.userId" placeholder="请输入用户ID">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="cyber-form-group">
                                <label class="cyber-form-label">用户名</label>
                                <input type="text" class="cyber-form-control" v-model="queryForm.username" placeholder="请输入用户名">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="cyber-form-group">
                                <label class="cyber-form-label">昵称</label>
                                <input type="text" class="cyber-form-control" v-model="queryForm.nickname" placeholder="请输入昵称">
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="cyber-form-group">
                                <label class="cyber-form-label">性别</label>
                                <div class="cyber-select-wrapper">
                                    <select class="cyber-form-control" v-model="queryForm.sex">
                                        <option value="">全部</option>
                                        <option value="男">男</option>
                                        <option value="女">女</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="cyber-form-group">
                                <label class="cyber-form-label">VIP等级</label>
                                <div class="cyber-select-wrapper">
                                    <select class="cyber-form-control" v-model="queryForm.vipLevel">
                                        <option value="">全部</option>
                                        <option value="0">普通用户</option>
                                        <option value="1">VIP1</option>
                                        <option value="2">VIP2</option>
                                        <option value="3">VIP3</option>
                                        <option value="4">VIP4</option>
                                        <option value="5">VIP5</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-md-12 text-end">
                            <button type="button" class="cyber-btn me-2" v-on:click="loadData">
                                <i class="fas fa-search"></i> 查询
                            </button>
                            <button type="button" class="cyber-btn cyber-btn-outline me-2" v-on:click="resetQuery">
                                <i class="fas fa-redo"></i> 重置
                            </button>
                            <button type="button" class="cyber-btn cyber-btn-success" v-on:click="showCreateModal">
                                <i class="fas fa-plus"></i> 新增用户
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 用户数据表 -->
            <div class="cyber-card">
                <div class="cyber-card-header">
                    <div class="cyber-icon">
                        <i class="fas fa-database"></i>
                    </div>
                    <h5 class="cyber-card-title">用户数据表</h5>
                    <div class="ms-auto">
                        <span class="badge" style="background: linear-gradient(135deg, var(--cyber-blue), var(--cyber-purple)); color: white; padding: 0.5rem 1rem; border-radius: 12px;">
                            共 {{ totalCount }} 条记录
                        </span>
                    </div>
                </div>
                <div class="cyber-table-container">
                    <table class="cyber-table">
                        <thead>
                            <tr>
                                <th>用户ID</th>
                                <th>用户名</th>
                                <th>昵称</th>
                                <th>性别</th>
                                <th>VIP等级</th>
                                <th>金币</th>
                                <th>元宝</th>
                                <th>水晶</th>
                                <th>注册时间</th>
                                <th width="200">操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr v-for="item in userList" v-bind:key="item.id">
                                <td><span style="font-weight: 600; color: var(--cyber-blue);">{{ item.id }}</span></td>
                                <td><strong style="color: var(--text-primary);">{{ item.username }}</strong></td>
                                <td style="color: var(--text-secondary);">{{ item.nickname || '-' }}</td>
                                <td style="color: var(--text-secondary);">{{ item.sexText }}</td>
                                <td>
                                    <span class="badge" v-bind:style="item.vipLevel > 0 ? 'background: linear-gradient(135deg, var(--cyber-orange), var(--cyber-pink)); color: white;' : 'background: rgba(255,255,255,0.1); color: var(--text-muted);'" style="padding: 0.25rem 0.75rem; border-radius: 8px; font-size: 0.75rem;">
                                        {{ item.vipLevelText }}
                                    </span>
                                </td>
                                <td><span style="color: var(--cyber-orange); font-weight: 500;">{{ formatNumber(item.gold) }}</span></td>
                                <td><span style="color: var(--cyber-purple); font-weight: 500;">{{ formatNumber(item.yuanbao) }}</span></td>
                                <td><span style="color: var(--cyber-blue); font-weight: 500;">{{ formatNumber(item.crystal) }}</span></td>
                                <td style="color: var(--text-muted);">{{ formatDate(item.regTime) }}</td>
                                <td>
                                    <div style="display: flex; gap: 0.25rem;">
                                        <button type="button" class="cyber-btn cyber-btn-warning cyber-btn-sm" v-on:click="showEditModal(item)" title="编辑">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button type="button" class="cyber-btn cyber-btn-success cyber-btn-sm" v-on:click="showAssetModal(item)" title="资产管理">
                                            <i class="fas fa-coins"></i>
                                        </button>
                                        <button type="button" class="cyber-btn cyber-btn-sm" v-on:click="showResetPasswordModal(item)" title="重置密码">
                                            <i class="fas fa-key"></i>
                                        </button>
                                        <button type="button" class="cyber-btn cyber-btn-danger cyber-btn-sm" v-on:click="deleteUser(item)" title="删除">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- 分页控制 -->
                <div v-if="totalPages > 1" style="padding: 1.5rem; border-top: 1px solid rgba(255, 255, 255, 0.1);">
                    <div class="row align-items-center">
                        <div class="col-sm-12 col-md-5">
                            <div style="color: var(--text-secondary); font-weight: 500;">
                                显示第 {{ (currentPage - 1) * pageSize + 1 }} 到 {{ Math.min(currentPage * pageSize, totalCount) }} 条记录，共 {{ totalCount }} 条
                            </div>
                        </div>
                        <div class="col-sm-12 col-md-7">
                            <div class="float-end">
                                <ul class="cyber-pagination">
                                    <li class="page-item" v-bind:class="{disabled: currentPage === 1}">
                                        <a href="#" class="page-link" v-on:click="changePage(currentPage - 1)">上一页</a>
                                    </li>
                                    <li class="page-item" v-for="page in visiblePages" v-bind:key="page" v-bind:class="{active: page === currentPage}">
                                        <a href="#" class="page-link" v-on:click="changePage(page)">{{ page }}</a>
                                    </li>
                                    <li class="page-item" v-bind:class="{disabled: currentPage === totalPages}">
                                        <a href="#" class="page-link" v-on:click="changePage(currentPage + 1)">下一页</a>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 新增用户模态框 -->
            <div class="modal fade cyber-modal" id="createModal" tabindex="-1" role="dialog">
                <div class="modal-dialog modal-lg" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-user-plus me-2"></i>新增用户
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="cyber-form-group">
                                        <label class="cyber-form-label">用户名</label>
                                        <input type="text" class="cyber-form-control" v-model="createForm.username" placeholder="请输入用户名">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="cyber-form-group">
                                        <label class="cyber-form-label">密码</label>
                                        <input type="password" class="cyber-form-control" v-model="createForm.password" placeholder="请输入密码">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="cyber-form-group">
                                        <label class="cyber-form-label">昵称</label>
                                        <input type="text" class="cyber-form-control" v-model="createForm.nickname" placeholder="请输入昵称">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="cyber-form-group">
                                        <label class="cyber-form-label">性别</label>
                                        <div class="cyber-select-wrapper">
                                            <select class="cyber-form-control" v-model="createForm.sex">
                                                <option value="">请选择性别</option>
                                                <option value="男">男</option>
                                                <option value="女">女</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="cyber-btn cyber-btn-outline" data-bs-dismiss="modal">
                                <i class="fas fa-times"></i> 取消
                            </button>
                            <button type="button" class="cyber-btn cyber-btn-success" v-on:click="createUser" v-bind:disabled="submitting">
                                <i class="fas fa-check"></i> {{ submitting ? '提交中...' : '确定' }}
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 编辑用户模态框 -->
            <div class="modal fade cyber-modal" id="editModal" tabindex="-1" role="dialog">
                <div class="modal-dialog modal-lg" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-user-edit me-2"></i>编辑用户
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="cyber-form-group">
                                        <label class="cyber-form-label">用户ID</label>
                                        <input type="text" class="cyber-form-control" v-model="editForm.id" readonly>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="cyber-form-group">
                                        <label class="cyber-form-label">用户名</label>
                                        <input type="text" class="cyber-form-control" v-model="editForm.username" placeholder="请输入用户名">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="cyber-form-group">
                                        <label class="cyber-form-label">昵称</label>
                                        <input type="text" class="cyber-form-control" v-model="editForm.nickname" placeholder="请输入昵称">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="cyber-form-group">
                                        <label class="cyber-form-label">性别</label>
                                        <div class="cyber-select-wrapper">
                                            <select class="cyber-form-control" v-model="editForm.sex">
                                                <option value="">请选择性别</option>
                                                <option value="男">男</option>
                                                <option value="女">女</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="cyber-btn cyber-btn-outline" data-bs-dismiss="modal">
                                <i class="fas fa-times"></i> 取消
                            </button>
                            <button type="button" class="cyber-btn cyber-btn-success" v-on:click="updateUser" v-bind:disabled="submitting">
                                <i class="fas fa-check"></i> {{ submitting ? '提交中...' : '确定' }}
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 资产管理模态框 -->
            <div class="modal fade cyber-modal" id="assetModal" tabindex="-1" role="dialog">
                <div class="modal-dialog" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-coins me-2"></i>资产管理
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <div class="cyber-form-group">
                                <label class="cyber-form-label">用户信息</label>
                                <input type="text" class="cyber-form-control" v-bind:value="assetForm.username + ' (ID: ' + assetForm.id + ')'" readonly>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="cyber-form-group">
                                        <label class="cyber-form-label">金币</label>
                                        <input type="number" class="cyber-form-control" v-model="assetForm.gold" placeholder="请输入金币" min="0">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="cyber-form-group">
                                        <label class="cyber-form-label">元宝</label>
                                        <input type="number" class="cyber-form-control" v-model="assetForm.yuanbao" placeholder="请输入元宝" min="0">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="cyber-form-group">
                                        <label class="cyber-form-label">水晶</label>
                                        <input type="number" class="cyber-form-control" v-model="assetForm.crystal" placeholder="请输入水晶" min="0">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="cyber-form-group">
                                        <label class="cyber-form-label">VIP等级</label>
                                        <div class="cyber-select-wrapper">
                                            <select class="cyber-form-control" v-model="assetForm.vipLevel">
                                                <option value="0">普通用户</option>
                                                <option value="1">VIP1</option>
                                                <option value="2">VIP2</option>
                                                <option value="3">VIP3</option>
                                                <option value="4">VIP4</option>
                                                <option value="5">VIP5</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-12">
                                    <div class="cyber-form-group">
                                        <label class="cyber-form-label">VIP积分</label>
                                        <input type="number" class="cyber-form-control" v-model="assetForm.vipScore" placeholder="请输入VIP积分" min="0">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="cyber-btn cyber-btn-outline" data-bs-dismiss="modal">
                                <i class="fas fa-times"></i> 取消
                            </button>
                            <button type="button" class="cyber-btn cyber-btn-success" v-on:click="updateAsset" v-bind:disabled="submitting">
                                <i class="fas fa-check"></i> {{ submitting ? '提交中...' : '确定' }}
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 重置密码模态框 -->
            <div class="modal fade cyber-modal" id="resetPasswordModal" tabindex="-1" role="dialog">
                <div class="modal-dialog" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-key me-2"></i>重置密码
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <div class="cyber-form-group">
                                <label class="cyber-form-label">用户信息</label>
                                <input type="text" class="cyber-form-control" v-bind:value="resetPasswordForm.username + ' (ID: ' + resetPasswordForm.id + ')'" readonly>
                            </div>
                            <div class="cyber-form-group">
                                <label class="cyber-form-label">新密码</label>
                                <input type="password" class="cyber-form-control" v-model="resetPasswordForm.newPassword" placeholder="请输入新密码">
                            </div>
                            <div class="cyber-form-group">
                                <label class="cyber-form-label">确认密码</label>
                                <input type="password" class="cyber-form-control" v-model="resetPasswordForm.confirmPassword" placeholder="请确认新密码">
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="cyber-btn cyber-btn-outline" data-bs-dismiss="modal">
                                <i class="fas fa-times"></i> 取消
                            </button>
                            <button type="button" class="cyber-btn cyber-btn-success" v-on:click="resetPassword" v-bind:disabled="submitting">
                                <i class="fas fa-check"></i> {{ submitting ? '提交中...' : '确定' }}
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>
</div>

<!-- 引入Vue.js和axios -->
<script src="~/lib/vue/vue.global.js"></script>
<script src="~/lib/axios.min.js"></script>

<script>
    const { createApp } = Vue;

    createApp({
        data() {
            return {
                // 查询条件
                queryForm: {
                    userId: '',
                    username: '',
                    nickname: '',
                    sex: '',
                    vipLevel: '',
                    page: 1,
                    pageSize: 15
                },
                // 用户列表
                userList: [],
                totalCount: 0,
                totalPages: 0,
                currentPage: 1,
                pageSize: 15,
                submitting: false,
                // 统计信息
                showStatistics: true,
                statistics: {},
                // 新增表单
                createForm: {
                    username: '',
                    password: '',
                    nickname: '',
                    sex: '',
                    vipLevel: 0,
                    vipScore: 0,
                    gold: 0,
                    yuanbao: 0,
                    crystal: 0,
                    title: ''
                },
                // 编辑表单
                editForm: {
                    id: '',
                    username: '',
                    nickname: '',
                    sex: '',
                    vipLevel: 0,
                    vipScore: 0,
                    gold: 0,
                    yuanbao: 0,
                    crystal: 0,
                    title: '',
                    mainPetName: ''
                },
                // 资产管理表单
                assetForm: {
                    id: '',
                    username: '',
                    gold: 0,
                    yuanbao: 0,
                    crystal: 0,
                    vipLevel: 0,
                    vipScore: 0
                },
                // 重置密码表单
                resetPasswordForm: {
                    id: '',
                    username: '',
                    newPassword: '',
                    confirmPassword: ''
                }
            };
        },
        computed: {
            // 计算可见的页码
            visiblePages() {
                const pages = [];
                const total = this.totalPages;
                const current = this.currentPage;
                
                let start = Math.max(1, current - 2);
                let end = Math.min(total, current + 2);
                
                if (end - start < 4) {
                    if (start === 1) {
                        end = Math.min(total, start + 4);
                    } else {
                        start = Math.max(1, end - 4);
                    }
                }
                
                for (let i = start; i <= end; i++) {
                    pages.push(i);
                }
                
                return pages;
            }
        },
        async mounted() {
            await this.loadStatistics();
            await this.loadData();
        },
        methods: {
            // 加载统计信息
            async loadStatistics() {
                try {
                    const response = await axios.get('/User/GetStatistics');
                    if (response.data.success) {
                        this.statistics = response.data.data || {};
                    }
                } catch (error) {
                    console.error('加载统计信息失败：', error);
                }
            },
            
            // 切换统计显示
            toggleStatistics() {
                this.showStatistics = !this.showStatistics;
                if (this.showStatistics) {
                    this.loadStatistics();
                }
            },

            // 加载数据
            async loadData() {
                try {
                    const response = await axios.post('/User/GetList', {
                        userId: this.queryForm.userId || null,
                        username: this.queryForm.username || null,
                        nickname: this.queryForm.nickname || null,
                        sex: this.queryForm.sex || null,
                        vipLevel: this.queryForm.vipLevel ? parseInt(this.queryForm.vipLevel) : null,
                        page: this.currentPage,
                        pageSize: this.pageSize
                    });
                    
                    if (response.data.success) {
                        const result = response.data.data || {};
                        this.userList = result.data || [];
                        this.totalCount = result.totalCount || 0;
                        this.totalPages = result.totalPages || 0;
                        this.currentPage = result.pageIndex || 1;
                    } else {
                        alert(response.data.message || '加载数据失败');
                    }
                } catch (error) {
                    console.error('加载数据失败：', error);
                    alert('加载数据失败，请重试');
                }
            },

            // 重置查询条件
            resetQuery() {
                this.queryForm = {
                    userId: '',
                    username: '',
                    nickname: '',
                    sex: '',
                    vipLevel: '',
                    page: 1,
                    pageSize: 15
                };
                this.currentPage = 1;
                this.loadData();
            },

            // 换页
            changePage(page) {
                if (page < 1 || page > this.totalPages || page === this.currentPage) {
                    return;
                }
                this.currentPage = page;
                this.loadData();
            },

            // 显示新增模态框
            showCreateModal() {
                this.createForm = {
                    username: '',
                    password: '',
                    nickname: '',
                    sex: '',
                    vipLevel: 0,
                    vipScore: 0,
                    gold: 0,
                    yuanbao: 0,
                    crystal: 0,
                    title: ''
                };
                new bootstrap.Modal(document.getElementById('createModal')).show();
            },

            // 显示编辑模态框
            showEditModal(item) {
                this.editForm = {
                    id: item.id,
                    username: item.username,
                    nickname: item.nickname || '',
                    sex: item.sex || '',
                    vipLevel: item.vipLevel || 0,
                    vipScore: item.vipScore || 0,
                    gold: item.gold || 0,
                    yuanbao: item.yuanbao || 0,
                    crystal: item.crystal || 0,
                    title: item.title || '',
                    mainPetName: item.mainPetName || ''
                };
                new bootstrap.Modal(document.getElementById('editModal')).show();
            },

            // 显示资产管理模态框
            showAssetModal(item) {
                this.assetForm = {
                    id: item.id,
                    username: item.username,
                    gold: item.gold || 0,
                    yuanbao: item.yuanbao || 0,
                    crystal: item.crystal || 0,
                    vipLevel: item.vipLevel || 0,
                    vipScore: item.vipScore || 0
                };
                new bootstrap.Modal(document.getElementById('assetModal')).show();
            },

            // 显示重置密码模态框
            showResetPasswordModal(item) {
                this.resetPasswordForm = {
                    id: item.id,
                    username: item.username,
                    newPassword: '',
                    confirmPassword: ''
                };
                new bootstrap.Modal(document.getElementById('resetPasswordModal')).show();
            },

            // 创建用户
            async createUser() {
                if (!this.validateCreateForm()) {
                    return;
                }
                
                this.submitting = true;
                try {
                    const response = await axios.post('/User/Create', this.createForm);
                    
                    if (response.data.success) {
                        alert('用户创建成功');
                        bootstrap.Modal.getInstance(document.getElementById('createModal')).hide();
                        this.loadData();
                        this.loadStatistics();
                    } else {
                        alert(response.data.message || '创建失败');
                    }
                } catch (error) {
                    console.error('创建用户失败：', error);
                    alert('创建用户失败，请重试');
                } finally {
                    this.submitting = false;
                }
            },

            // 更新用户
            async updateUser() {
                if (!this.validateEditForm()) {
                    return;
                }
                
                this.submitting = true;
                try {
                    const response = await axios.post('/User/Update', this.editForm);
                    
                    if (response.data.success) {
                        alert('用户更新成功');
                        bootstrap.Modal.getInstance(document.getElementById('editModal')).hide();
                        this.loadData();
                    } else {
                        alert(response.data.message || '更新失败');
                    }
                } catch (error) {
                    console.error('更新用户失败：', error);
                    alert('更新用户失败，请重试');
                } finally {
                    this.submitting = false;
                }
            },

            // 更新资产
            async updateAsset() {
                if (!this.validateAssetForm()) {
                    return;
                }
                
                this.submitting = true;
                try {
                    const response = await axios.post('/User/UpdateAsset', this.assetForm);
                    
                    if (response.data.success) {
                        alert('资产更新成功');
                        bootstrap.Modal.getInstance(document.getElementById('assetModal')).hide();
                        this.loadData();
                        this.loadStatistics();
                    } else {
                        alert(response.data.message || '更新失败');
                    }
                } catch (error) {
                    console.error('更新资产失败：', error);
                    alert('更新资产失败，请重试');
                } finally {
                    this.submitting = false;
                }
            },

            // 重置密码
            async resetPassword() {
                if (!this.validateResetPasswordForm()) {
                    return;
                }
                
                this.submitting = true;
                try {
                    const response = await axios.post('/User/ResetPassword', {
                        id: this.resetPasswordForm.id,
                        newPassword: this.resetPasswordForm.newPassword
                    });
                    
                    if (response.data.success) {
                        alert('密码重置成功');
                        bootstrap.Modal.getInstance(document.getElementById('resetPasswordModal')).hide();
                    } else {
                        alert(response.data.message || '重置失败');
                    }
                } catch (error) {
                    console.error('重置密码失败：', error);
                    alert('重置密码失败，请重试');
                } finally {
                    this.submitting = false;
                }
            },

            // 删除用户
            async deleteUser(item) {
                if (!confirm(`确定要删除用户 "${item.username}" 吗？\n\n警告：此操作将删除用户的所有相关数据，且无法恢复！`)) {
                    return;
                }

                try {
                    const response = await axios.post('/User/Delete', { id: item.id });
                    
                    if (response.data.success) {
                        alert('用户删除成功');
                        this.loadData();
                        this.loadStatistics();
                    } else {
                        alert(response.data.message || '删除失败');
                    }
                } catch (error) {
                    console.error('删除用户失败：', error);
                    alert('删除用户失败，请重试');
                }
            },

            // 验证新增表单
            validateCreateForm() {
                if (!this.createForm.username?.trim()) {
                    alert('请输入用户名');
                    return false;
                }
                if (!this.createForm.password?.trim()) {
                    alert('请输入密码');
                    return false;
                }
                if (this.createForm.password.length < 6) {
                    alert('密码长度不能少于6位');
                    return false;
                }
                return true;
            },

            // 验证编辑表单
            validateEditForm() {
                if (!this.editForm.username?.trim()) {
                    alert('请输入用户名');
                    return false;
                }
                return true;
            },

            // 验证资产表单
            validateAssetForm() {
                if (this.assetForm.gold < 0) {
                    alert('金币不能为负数');
                    return false;
                }
                if (this.assetForm.yuanbao < 0) {
                    alert('元宝不能为负数');
                    return false;
                }
                if (this.assetForm.crystal < 0) {
                    alert('水晶不能为负数');
                    return false;
                }
                return true;
            },

            // 验证重置密码表单
            validateResetPasswordForm() {
                if (!this.resetPasswordForm.newPassword?.trim()) {
                    alert('请输入新密码');
                    return false;
                }
                if (this.resetPasswordForm.newPassword.length < 6) {
                    alert('密码长度不能少于6位');
                    return false;
                }
                if (this.resetPasswordForm.newPassword !== this.resetPasswordForm.confirmPassword) {
                    alert('两次输入的密码不一致');
                    return false;
                }
                return true;
            },

            // 格式化数字
            formatNumber(num) {
                if (num == null) return '0';
                return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
            },

            // 格式化日期
            formatDate(dateStr) {
                if (!dateStr) return '-';
                try {
                    const date = new Date(dateStr);
                    return date.toLocaleString('zh-CN', {
                        year: 'numeric',
                        month: '2-digit',
                        day: '2-digit'
                    });
                } catch {
                    return dateStr;
                }
            }
        }
    }).mount('#userApp');
</script>
}