using BMS.Models.DTOs;
using BMS.Models.Common;
using BMS.Models.Entities;

namespace BMS.Services.Interfaces
{
    /// <summary>
    /// 认证服务接口
    /// </summary>
    public interface IAuthService
    {
        /// <summary>
        /// 管理员登录
        /// </summary>
        /// <param name="loginDto">登录信息</param>
        /// <returns>登录结果</returns>
        Task<ApiResult<admin_bm>> LoginAsync(LoginDto loginDto);

        /// <summary>
        /// 根据用户名获取管理员信息
        /// </summary>
        /// <param name="username">用户名</param>
        /// <returns>管理员信息</returns>
        Task<admin_bm?> GetAdminByUsernameAsync(string username);

        /// <summary>
        /// 根据ID获取管理员信息
        /// </summary>
        /// <param name="id">管理员ID</param>
        /// <returns>管理员信息</returns>
        Task<admin_bm?> GetAdminByIdAsync(int id);

        /// <summary>
        /// 验证密码
        /// </summary>
        /// <param name="password">原始密码</param>
        /// <param name="hashedPassword">加密后的密码</param>
        /// <returns>是否匹配</returns>
        bool VerifyPassword(string password, string hashedPassword);

        /// <summary>
        /// 更新最后登录时间
        /// </summary>
        /// <param name="adminId">管理员ID</param>
        /// <returns>是否成功</returns>
        Task<bool> UpdateLastLoginTimeAsync(int adminId);

        /// <summary>
        /// 修改密码
        /// </summary>
        /// <param name="adminId">管理员ID</param>
        /// <param name="changePasswordDto">修改密码信息</param>
        /// <returns>修改结果</returns>
        Task<ApiResult> ChangePasswordAsync(int adminId, ChangePasswordDto changePasswordDto);
    }
} 