﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace BMS.Models.Entities
{
    ///<summary>
    ///套装配置表
    ///</summary>
    [SugarTable("suit_config")]
    public partial class suit_config
    {
           public suit_config(){


           }
           /// <summary>
           /// Desc:自增主键
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true,IsIdentity=true)]
           public int id {get;set;}

           /// <summary>
           /// Desc:套装ID（业务唯一标识）
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string suit_id {get;set;}

           /// <summary>
           /// Desc:套装名称（天魔套装/自然套装等）
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string suit_name {get;set;}

           /// <summary>
           /// Desc:套装装备列表（JSON数组格式存储装备ID）
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? equipment_list {get;set;}

           /// <summary>
           /// Desc:套装描述
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? description {get;set;}

           /// <summary>
           /// Desc:套装总件数
           /// Default:0
           /// Nullable:True
           /// </summary>           
           public int? total_pieces {get;set;}

           /// <summary>
           /// Desc:创建时间
           /// Default:CURRENT_TIMESTAMP
           /// Nullable:True
           /// </summary>           
           public DateTime? create_time {get;set;}

    }
}
