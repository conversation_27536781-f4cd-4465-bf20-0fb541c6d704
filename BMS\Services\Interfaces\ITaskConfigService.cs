using BMS.Models.DTOs;
using BMS.Models.Common;

namespace BMS.Services.Interfaces
{
    /// <summary>
    /// 任务配置服务接口
    /// </summary>
    public interface ITaskConfigService
    {
        /// <summary>
        /// 获取任务配置列表
        /// </summary>
        Task<PagedResult<TaskConfigDto>> GetTaskConfigsAsync(TaskConfigQueryDto queryDto);

        /// <summary>
        /// 根据ID获取任务配置
        /// </summary>
        Task<TaskConfigDto?> GetTaskConfigByIdAsync(string taskId);

        /// <summary>
        /// 创建任务配置
        /// </summary>
        Task<ApiResult<TaskConfigDto>> CreateTaskConfigAsync(TaskConfigCreateDto createDto);

        /// <summary>
        /// 更新任务配置
        /// </summary>
        Task<ApiResult<TaskConfigDto>> UpdateTaskConfigAsync(string taskId, TaskConfigCreateDto updateDto);

        /// <summary>
        /// 删除任务配置
        /// </summary>
        Task<ApiResult<bool>> DeleteTaskConfigAsync(string taskId);

        /// <summary>
        /// 启用/禁用任务
        /// </summary>
        Task<ApiResult<bool>> ToggleTaskActiveAsync(string taskId, bool isActive);

        /// <summary>
        /// 获取任务类型选项
        /// </summary>
        Task<List<OptionDto>> GetTaskTypeOptionsAsync();

        /// <summary>
        /// 获取任务选项（用于前置任务选择）
        /// </summary>
        Task<List<OptionDto>> GetTaskOptionsAsync();

        /// <summary>
        /// 获取目标类型选项
        /// </summary>
        Task<List<OptionDto>> GetObjectiveTypeOptionsAsync();
    }
}
