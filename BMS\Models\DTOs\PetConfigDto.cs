using System.ComponentModel.DataAnnotations;

namespace BMS.Models.DTOs
{
    /// <summary>
    /// 宠物配置DTO
    /// </summary>
    public class PetConfigDto
    {
        /// <summary>
        /// 自增主键
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 宠物编号
        /// </summary>
        public int PetNo { get; set; }

        /// <summary>
        /// 宠物名称
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 宠物属性
        /// </summary>
        public string Attribute { get; set; } = string.Empty;

        /// <summary>
        /// 技能编号（多个技能用逗号分隔）
        /// </summary>
        public string? Skill { get; set; }
    }

    /// <summary>
    /// 宠物配置查询DTO
    /// </summary>
    public class PetConfigQueryDto
    {
        /// <summary>
        /// 宠物编号
        /// </summary>
        public int? PetNo { get; set; }

        /// <summary>
        /// 宠物名称（支持模糊查询）
        /// </summary>
        public string? Name { get; set; }

        /// <summary>
        /// 宠物属性
        /// </summary>
        public string? Attribute { get; set; }

        /// <summary>
        /// 页码
        /// </summary>
        public int Page { get; set; } = 1;

        /// <summary>
        /// 每页数量
        /// </summary>
        public int PageSize { get; set; } = 10;
    }

    /// <summary>
    /// 创建宠物配置DTO
    /// </summary>
    public class PetConfigCreateDto
    {
        /// <summary>
        /// 宠物编号
        /// </summary>
        [Required(ErrorMessage = "宠物编号不能为空")]
        [Range(1, int.MaxValue, ErrorMessage = "宠物编号必须大于0")]
        public int PetNo { get; set; }

        /// <summary>
        /// 宠物名称
        /// </summary>
        [Required(ErrorMessage = "宠物名称不能为空")]
        [StringLength(50, ErrorMessage = "宠物名称长度不能超过50个字符")]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 宠物属性
        /// </summary>
        [Required(ErrorMessage = "宠物属性不能为空")]
        [StringLength(10, ErrorMessage = "宠物属性长度不能超过10个字符")]
        public string Attribute { get; set; } = string.Empty;

        /// <summary>
        /// 技能编号（多个技能用逗号分隔）
        /// </summary>
        [StringLength(100, ErrorMessage = "技能编号长度不能超过100个字符")]
        public string? Skill { get; set; }
    }

    /// <summary>
    /// 更新宠物配置DTO
    /// </summary>
    public class PetConfigUpdateDto
    {
        /// <summary>
        /// 自增主键
        /// </summary>
        [Required(ErrorMessage = "ID不能为空")]
        [Range(1, int.MaxValue, ErrorMessage = "ID必须大于0")]
        public int Id { get; set; }

        /// <summary>
        /// 宠物编号
        /// </summary>
        [Required(ErrorMessage = "宠物编号不能为空")]
        [Range(1, int.MaxValue, ErrorMessage = "宠物编号必须大于0")]
        public int PetNo { get; set; }

        /// <summary>
        /// 宠物名称
        /// </summary>
        [Required(ErrorMessage = "宠物名称不能为空")]
        [StringLength(50, ErrorMessage = "宠物名称长度不能超过50个字符")]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 宠物属性
        /// </summary>
        [Required(ErrorMessage = "宠物属性不能为空")]
        [StringLength(10, ErrorMessage = "宠物属性长度不能超过10个字符")]
        public string Attribute { get; set; } = string.Empty;

        /// <summary>
        /// 技能编号（多个技能用逗号分隔）
        /// </summary>
        [StringLength(100, ErrorMessage = "技能编号长度不能超过100个字符")]
        public string? Skill { get; set; }
    }

    /// <summary>
    /// 删除宠物配置DTO
    /// </summary>
    public class PetConfigDeleteDto
    {
        /// <summary>
        /// 自增主键
        /// </summary>
        [Required(ErrorMessage = "ID不能为空")]
        [Range(1, int.MaxValue, ErrorMessage = "ID必须大于0")]
        public int Id { get; set; }
    }
} 