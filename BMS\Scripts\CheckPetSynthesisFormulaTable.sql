-- 检查pet_synthesis_formula表是否存在
SELECT COUNT(*) as table_exists 
FROM information_schema.tables 
WHERE table_schema = DATABASE() 
AND table_name = 'pet_synthesis_formula';

-- 检查表结构
DESCRIBE pet_synthesis_formula;

-- 检查表中是否有数据
SELECT COUNT(*) as record_count FROM pet_synthesis_formula;

-- 查看前几条记录
SELECT * FROM pet_synthesis_formula LIMIT 5;

-- 检查pet_config表是否有数据
SELECT COUNT(*) as pet_config_count FROM pet_config;

-- 查看pet_config前几条记录
SELECT pet_no, name, attribute FROM pet_config LIMIT 10;
