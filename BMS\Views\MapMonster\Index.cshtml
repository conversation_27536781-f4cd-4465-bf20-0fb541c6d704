@{
    ViewData["Title"] = "地图怪物管理";
}

<!-- 科幻背景 -->
<div class="cyber-bg"></div>

<style>
/* 科幻可搜索下拉框样式 */
.cyber-searchable-select {
    position: relative;
    width: 100%;
}

.cyber-searchable-select .cyber-select-input {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    color: var(--text-primary);
    padding: 12px 40px 12px 16px;
    width: 100%;
    transition: var(--transition);
    cursor: pointer;
}

.cyber-searchable-select .cyber-select-input:focus {
    border-color: var(--cyber-blue);
    box-shadow: 0 0 0 2px rgba(0, 212, 255, 0.2);
    outline: none;
    background: rgba(255, 255, 255, 0.08);
}

.cyber-searchable-select .cyber-select-arrow {
    position: absolute;
    top: 50%;
    right: 12px;
    transform: translateY(-50%);
    color: var(--cyber-blue);
    pointer-events: none;
    font-size: 12px;
    transition: var(--transition);
}

.cyber-searchable-select.open .cyber-select-arrow {
    transform: translateY(-50%) rotate(180deg);
}

.cyber-searchable-select .cyber-select-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    z-index: 1000;
    background: linear-gradient(135deg, var(--dark-card), var(--dark-surface));
    border: 1px solid rgba(0, 212, 255, 0.3);
    border-radius: 8px;
    box-shadow: var(--card-glow), var(--neon-glow);
    margin-top: 4px;
    max-height: 200px;
    overflow-y: auto;
    display: none;
}

.cyber-searchable-select.open .cyber-select-dropdown {
    display: block;
}

.cyber-searchable-select .cyber-select-option {
    padding: 12px 16px;
    color: var(--text-primary);
    cursor: pointer;
    transition: var(--transition);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.cyber-searchable-select .cyber-select-option:last-child {
    border-bottom: none;
}

.cyber-searchable-select .cyber-select-option:hover {
    background: rgba(0, 212, 255, 0.2);
    color: var(--cyber-blue);
}

.cyber-searchable-select .cyber-select-option.selected {
    background: var(--cyber-blue);
    color: white;
}

.cyber-searchable-select .cyber-select-option.no-results {
    color: var(--text-muted);
    font-style: italic;
    cursor: default;
}

.cyber-searchable-select .cyber-select-option.no-results:hover {
    background: transparent;
    color: var(--text-muted);
}

/* 滚动条样式 */
.cyber-searchable-select .cyber-select-dropdown::-webkit-scrollbar {
    width: 6px;
}

.cyber-searchable-select .cyber-select-dropdown::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
}

.cyber-searchable-select .cyber-select-dropdown::-webkit-scrollbar-thumb {
    background: var(--cyber-blue);
    border-radius: 3px;
}

.cyber-searchable-select .cyber-select-dropdown::-webkit-scrollbar-thumb:hover {
    background: var(--cyber-purple);
}
</style>

<!-- 科幻地图怪物管理应用容器 -->
<div id="mapMonsterApp">
    <!-- 科幻页面标题 -->
    <div class="container-fluid py-4">
        <div class="row mb-4">
            <div class="col-12">
                <div class="cyber-card">
                    <div class="cyber-card-header">
                        <div class="cyber-icon">🗺️</div>
                        <h1 class="cyber-card-title">地图怪物管理</h1>
                        <div class="ms-auto">
                            <button type="button" class="cyber-btn cyber-btn-success" v-on:click="showCreateModal">
                                <i class="fas fa-plus"></i> 新增地图怪物
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 科幻主要内容 -->
    <div class="container-fluid">
        <!-- 科幻搜索条件 -->
        <div class="cyber-card">
            <div class="cyber-card-header">
                <div class="cyber-icon">🔍</div>
                <h3 class="cyber-card-title">搜索条件</h3>
            </div>
            <div class="cyber-card-body">
                <div class="row">
                    <div class="col-md-3">
                        <div class="cyber-form-group">
                            <label class="cyber-form-label" for="mapSearch">地图选择</label>
                            <div class="cyber-select-wrapper">
                                <select class="cyber-form-control" v-model="searchForm.MapId" id="mapSearch">
                                    <option value="">请选择地图</option>
                                    <option v-for="map in maps" :key="map.mapId" :value="map.mapId">
                                        {{ map.mapName }}
                                    </option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="cyber-form-group">
                            <label class="cyber-form-label" for="monsterNameSearch">怪物名称</label>
                            <input type="text" class="cyber-form-control" v-model="searchForm.MonsterName"
                                   placeholder="请输入怪物名称" id="monsterNameSearch">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="cyber-form-group">
                            <label class="cyber-form-label" for="elementSearch">五行属性</label>
                            <div class="cyber-select-wrapper">
                                <select class="cyber-form-control" v-model="searchForm.Element" id="elementSearch">
                                    <option value="">请选择五行</option>
                                    <option value="金">金</option>
                                    <option value="木">木</option>
                                    <option value="水">水</option>
                                    <option value="火">火</option>
                                    <option value="土">土</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="cyber-form-group">
                            <label class="cyber-form-label">&nbsp;</label>
                            <div class="d-flex gap-2 flex-wrap">
                                <button type="button" class="cyber-btn cyber-btn-primary" v-on:click="searchMapMonsters">
                                    <i class="fas fa-search"></i> 搜索
                                </button>
                                <button type="button" class="cyber-btn cyber-btn-outline" v-on:click="resetSearch">
                                    <i class="fas fa-redo"></i> 重置
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 科幻地图怪物列表 -->
        <div class="cyber-card">
            <div class="cyber-card-header">
                <div class="cyber-icon">👾</div>
                <h3 class="cyber-card-title">地图怪物列表</h3>
            </div>
            <div class="cyber-card-body">
                <div class="cyber-table-container">
                    <table class="cyber-table">
                        <thead class="cyber-thead">
                            <tr>
                                <th class="cyber-th">ID</th>
                                <th class="cyber-th">地图ID</th>
                                <th class="cyber-th">地图名称</th>
                                <th class="cyber-th">怪物序号</th>
                                <th class="cyber-th">怪物名称</th>
                                <th class="cyber-th">五行属性</th>
                                <th class="cyber-th">等级范围</th>
                                <th class="cyber-th">HP</th>
                                <th class="cyber-th">攻击力</th>
                                <th class="cyber-th">防御力</th>
                                <th class="cyber-th">经验值</th>
                                <th class="cyber-th">操作</th>
                            </tr>
                        </thead>
                        <tbody class="cyber-tbody" v-if="loading">
                            <tr class="cyber-tr">
                                <td colspan="12" class="cyber-td text-center py-4">
                                    <i class="fas fa-spinner fa-spin me-2"></i>数据加载中...
                                </td>
                            </tr>
                        </tbody>
                        <tbody class="cyber-tbody" v-else-if="mapMonsters.length === 0">
                            <tr class="cyber-tr">
                                <td colspan="12" class="cyber-td text-center py-4 text-muted">
                                    <i class="fas fa-inbox me-2"></i>暂无数据
                                </td>
                            </tr>
                        </tbody>
                        <tbody class="cyber-tbody" v-else>
                            <tr class="cyber-tr" v-for="monster in mapMonsters" :key="monster.id">
                                <td class="cyber-td">{{ monster.id }}</td>
                                <td class="cyber-td">{{ monster.mapId }}</td>
                                <td class="cyber-td">{{ monster.mapName || '未知地图' }}</td>
                                <td class="cyber-td">{{ monster.monsterId }}</td>
                                <td class="cyber-td">
                                    <i class="fas fa-dragon me-1" style="color: var(--cyber-blue);"></i>
                                    {{ monster.monsterName }}
                                </td>
                                <td class="cyber-td">
                                    <span v-if="monster.element" class="badge badge-info">{{ monster.element }}</span>
                                    <span v-else class="text-muted">-</span>
                                </td>
                                <td class="cyber-td">{{ monster.minLevel }}-{{ monster.maxLevel }}</td>
                                <td class="cyber-td">{{ monster.maxHp || 0 }}</td>
                                <td class="cyber-td">{{ monster.atk || 0 }}</td>
                                <td class="cyber-td">{{ monster.def || 0 }}</td>
                                <td class="cyber-td">{{ monster.exp || 0 }}</td>
                                <td class="cyber-td">
                                    <div class="d-flex gap-1 flex-wrap">
                                        <button type="button" class="cyber-btn cyber-btn-sm"
                                                v-on:click="viewDetails(monster)" title="查看详情"
                                                style="background: linear-gradient(135deg, var(--cyber-blue), var(--cyber-purple));">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button type="button" class="cyber-btn cyber-btn-sm cyber-btn-warning"
                                                v-on:click="showEditModal(monster)" title="编辑">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button type="button" class="cyber-btn cyber-btn-sm cyber-btn-danger"
                                                v-on:click="deleteMapMonster(monster.id)" title="删除">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- 科幻分页 -->
                <div class="mt-3" v-if="pagination.totalPages > 1">
                    <div class="row align-items-center">
                        <div class="col-sm-6">
                            <div class="text-muted">
                                显示第 {{ (pagination.page - 1) * pagination.pageSize + 1 }} 到 {{ Math.min(pagination.page * pagination.pageSize, pagination.totalCount) }} 条记录，共 {{ pagination.totalCount }} 条记录
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <ul class="cyber-pagination justify-content-end">
                                <li class="page-item" :class="{ disabled: pagination.page <= 1 }">
                                    <a class="page-link" href="#" v-on:click.prevent="changePage(pagination.page - 1)">上一页</a>
                                </li>
                                <li class="page-item" v-for="page in getPageNumbers()" :key="page" :class="{ active: page === pagination.page }">
                                    <a class="page-link" href="#" v-on:click.prevent="changePage(page)" v-text="page"></a>
                                </li>
                                <li class="page-item" :class="{ disabled: pagination.page >= pagination.totalPages }">
                                    <a class="page-link" href="#" v-on:click.prevent="changePage(pagination.page + 1)">下一页</a>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </div>

    <!-- 科幻新增地图怪物模态框 -->
    <div class="modal fade cyber-modal" id="createModal" tabindex="-1" aria-labelledby="createModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="createModalLabel">
                        <i class="fas fa-plus me-2"></i>新增地图怪物
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="createForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="cyber-form-group">
                                    <label for="createMapId" class="cyber-form-label">地图 <span class="text-danger">*</span></label>
                                    <div class="cyber-select-wrapper">
                                        <select class="cyber-form-control" v-model="createForm.mapId" id="createMapId" required>
                                            <option value="">请选择地图</option>
                                            <option v-for="map in maps" v-bind:key="map.mapId" v-bind:value="map.mapId">
                                                {{ map.mapName }}
                                            </option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="cyber-form-group">
                                    <label for="createMonsterSelect" class="cyber-form-label">选择怪物 <span class="text-danger">*</span></label>
                                    <div class="cyber-searchable-select" :class="{ open: createMonsterDropdownOpen }" v-click-outside="closeCreateMonsterDropdown">
                                        <input type="text"
                                               class="cyber-select-input"
                                               v-model="createMonsterSearchText"
                                               @@click="toggleCreateMonsterDropdown"
                                               @@input="filterCreateMonsterOptions"
                                               placeholder="请搜索并选择怪物">
                                        <div class="cyber-select-arrow">▼</div>
                                        <div class="cyber-select-dropdown">
                                            <div v-if="filteredCreateMonsterOptions.length === 0" class="cyber-select-option no-results">
                                                {{ createMonsterSearchText ? '未找到匹配的怪物' : '暂无怪物数据' }}
                                            </div>
                                            <div v-for="monster in filteredCreateMonsterOptions"
                                                 :key="monster.id"
                                                 class="cyber-select-option"
                                                 :class="{ selected: createForm.selectedMonsterId === monster.id }"
                                                 @@click="selectCreateMonster(monster)">
                                                <i class="fas fa-dragon me-2" style="color: var(--cyber-blue);"></i>
                                                {{ monster.name }} (ID: {{ monster.id }})
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="cyber-form-group">
                                    <label for="createElement" class="cyber-form-label">五行属性</label>
                                    <div class="cyber-select-wrapper">
                                        <select class="cyber-form-control" v-model="createForm.element" id="createElement">
                                            <option value="">请选择五行</option>
                                            <option value="金">金</option>
                                            <option value="木">木</option>
                                            <option value="水">水</option>
                                            <option value="火">火</option>
                                            <option value="土">土</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="cyber-form-group">
                                    <label for="createMinLevel" class="cyber-form-label">最小等级</label>
                                    <input type="number" class="cyber-form-control" v-model="createForm.minLevel"
                                           id="createMinLevel" placeholder="最小等级" min="1">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="cyber-form-group">
                                    <label for="createMaxLevel" class="cyber-form-label">最大等级</label>
                                    <input type="number" class="cyber-form-control" v-model="createForm.maxLevel"
                                           id="createMaxLevel" placeholder="最大等级" min="1">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="cyber-form-group">
                                    <label for="createGrowth" class="cyber-form-label">怪物成长</label>
                                    <input type="number" class="cyber-form-control" v-model="createForm.growth"
                                           id="createGrowth" placeholder="怪物成长" min="0">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="cyber-form-group">
                                    <label for="createMaxDrop" class="cyber-form-label">最大掉落</label>
                                    <input type="number" class="cyber-form-control" v-model="createForm.maxDrop"
                                           id="createMaxDrop" placeholder="最大掉落" min="0">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="cyber-form-group">
                                    <label for="createExp" class="cyber-form-label">经验值</label>
                                    <input type="number" class="cyber-form-control" v-model="createForm.exp"
                                           id="createExp" placeholder="经验值" min="0">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="cyber-form-group">
                                    <label for="createHp" class="cyber-form-label">生命</label>
                                    <input type="number" class="cyber-form-control" v-model="createForm.hp"
                                           id="createHp" placeholder="生命值" min="0">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="cyber-form-group">
                                    <label for="createMp" class="cyber-form-label">魔法</label>
                                    <input type="number" class="cyber-form-control" v-model="createForm.mp"
                                           id="createMp" placeholder="魔法值" min="0">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="cyber-form-group">
                                    <label for="createMaxHp" class="cyber-form-label">最大生命</label>
                                    <input type="number" class="cyber-form-control" v-model="createForm.maxHp"
                                           id="createMaxHp" placeholder="最大生命值" min="0">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="cyber-form-group">
                                    <label for="createMaxMp" class="cyber-form-label">最大魔法</label>
                                    <input type="number" class="cyber-form-control" v-model="createForm.maxMp"
                                           id="createMaxMp" placeholder="最大魔法值" min="0">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="cyber-form-group">
                                    <label for="createAtk" class="cyber-form-label">攻击</label>
                                    <input type="number" class="cyber-form-control" v-model="createForm.atk"
                                           id="createAtk" placeholder="攻击力" min="0">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="cyber-form-group">
                                    <label for="createDef" class="cyber-form-label">防御</label>
                                    <input type="number" class="cyber-form-control" v-model="createForm.def"
                                           id="createDef" placeholder="防御力" min="0">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="cyber-form-group">
                                    <label for="createDodge" class="cyber-form-label">闪避</label>
                                    <input type="number" class="cyber-form-control" v-model="createForm.dodge"
                                           id="createDodge" placeholder="闪避值" min="0">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="cyber-form-group">
                                    <label for="createSpd" class="cyber-form-label">速度</label>
                                    <input type="number" class="cyber-form-control" v-model="createForm.spd"
                                           id="createSpd" placeholder="速度值" min="0">
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="cyber-btn cyber-btn-outline" data-bs-dismiss="modal">
                        <i class="fas fa-times"></i> 取消
                    </button>
                    <button type="button" class="cyber-btn cyber-btn-success" v-on:click="submitCreate" v-bind:disabled="submitting">
                        <span v-if="submitting" class="spinner-border spinner-border-sm me-2" role="status"></span>
                        <i v-if="!submitting" class="fas fa-check"></i>
                        {{ submitting ? '提交中...' : '确定' }}
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 科幻编辑地图怪物模态框 -->
    <div class="modal fade cyber-modal" id="editModal" tabindex="-1" aria-labelledby="editModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="editModalLabel">
                        <i class="fas fa-edit me-2"></i>编辑地图怪物
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="editForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="cyber-form-group">
                                    <label for="editMapId" class="cyber-form-label">地图 <span class="text-danger">*</span></label>
                                    <div class="cyber-select-wrapper">
                                        <select class="cyber-form-control" v-model="editForm.mapId" id="editMapId" required>
                                            <option value="">请选择地图</option>
                                            <option v-for="map in maps" v-bind:key="map.mapId" v-bind:value="map.mapId">
                                                {{ map.mapName }}
                                            </option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="cyber-form-group">
                                    <label for="editMonsterSelect" class="cyber-form-label">选择怪物 <span class="text-danger">*</span></label>
                                    <div class="cyber-searchable-select" :class="{ open: editMonsterDropdownOpen }" v-click-outside="closeEditMonsterDropdown">
                                        <input type="text"
                                               class="cyber-select-input"
                                               v-model="editMonsterSearchText"
                                               @@click="toggleEditMonsterDropdown"
                                               @@input="filterEditMonsterOptions"
                                               placeholder="请搜索并选择怪物">
                                        <div class="cyber-select-arrow">▼</div>
                                        <div class="cyber-select-dropdown">
                                            <div v-if="filteredEditMonsterOptions.length === 0" class="cyber-select-option no-results">
                                                {{ editMonsterSearchText ? '未找到匹配的怪物' : '暂无怪物数据' }}
                                            </div>
                                            <div v-for="monster in filteredEditMonsterOptions"
                                                 :key="monster.id"
                                                 class="cyber-select-option"
                                                 :class="{ selected: editForm.selectedMonsterId === monster.id }"
                                                 @@click="selectEditMonster(monster)">
                                                <i class="fas fa-dragon me-2" style="color: var(--cyber-blue);"></i>
                                                {{ monster.name }} (ID: {{ monster.id }})
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="cyber-form-group">
                                    <label for="editElement" class="cyber-form-label">五行属性</label>
                                    <div class="cyber-select-wrapper">
                                        <select class="cyber-form-control" v-model="editForm.element" id="editElement">
                                            <option value="">请选择五行</option>
                                            <option value="金">金</option>
                                            <option value="木">木</option>
                                            <option value="水">水</option>
                                            <option value="火">火</option>
                                            <option value="土">土</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="cyber-form-group">
                                    <label for="editMinLevel" class="cyber-form-label">最小等级</label>
                                    <input type="number" class="cyber-form-control" v-model="editForm.minLevel"
                                           id="editMinLevel" placeholder="最小等级" min="1">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="cyber-form-group">
                                    <label for="editMaxLevel" class="cyber-form-label">最大等级</label>
                                    <input type="number" class="cyber-form-control" v-model="editForm.maxLevel"
                                           id="editMaxLevel" placeholder="最大等级" min="1">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="cyber-form-group">
                                    <label for="editGrowth" class="cyber-form-label">怪物成长</label>
                                    <input type="number" class="cyber-form-control" v-model="editForm.growth"
                                           id="editGrowth" placeholder="怪物成长" min="0">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="cyber-form-group">
                                    <label for="editMaxDrop" class="cyber-form-label">最大掉落</label>
                                    <input type="number" class="cyber-form-control" v-model="editForm.maxDrop"
                                           id="editMaxDrop" placeholder="最大掉落" min="0">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="cyber-form-group">
                                    <label for="editExp" class="cyber-form-label">经验值</label>
                                    <input type="number" class="cyber-form-control" v-model="editForm.exp"
                                           id="editExp" placeholder="经验值" min="0">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="cyber-form-group">
                                    <label for="editHp" class="cyber-form-label">生命</label>
                                    <input type="number" class="cyber-form-control" v-model="editForm.hp"
                                           id="editHp" placeholder="生命值" min="0">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="cyber-form-group">
                                    <label for="editMp" class="cyber-form-label">魔法</label>
                                    <input type="number" class="cyber-form-control" v-model="editForm.mp"
                                           id="editMp" placeholder="魔法值" min="0">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="cyber-form-group">
                                    <label for="editMaxHp" class="cyber-form-label">最大生命</label>
                                    <input type="number" class="cyber-form-control" v-model="editForm.maxHp"
                                           id="editMaxHp" placeholder="最大生命值" min="0">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="cyber-form-group">
                                    <label for="editMaxMp" class="cyber-form-label">最大魔法</label>
                                    <input type="number" class="cyber-form-control" v-model="editForm.maxMp"
                                           id="editMaxMp" placeholder="最大魔法值" min="0">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="cyber-form-group">
                                    <label for="editAtk" class="cyber-form-label">攻击</label>
                                    <input type="number" class="cyber-form-control" v-model="editForm.atk"
                                           id="editAtk" placeholder="攻击力" min="0">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="cyber-form-group">
                                    <label for="editDef" class="cyber-form-label">防御</label>
                                    <input type="number" class="cyber-form-control" v-model="editForm.def"
                                           id="editDef" placeholder="防御力" min="0">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="cyber-form-group">
                                    <label for="editDodge" class="cyber-form-label">闪避</label>
                                    <input type="number" class="cyber-form-control" v-model="editForm.dodge"
                                           id="editDodge" placeholder="闪避值" min="0">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="cyber-form-group">
                                    <label for="editSpd" class="cyber-form-label">速度</label>
                                    <input type="number" class="cyber-form-control" v-model="editForm.spd"
                                           id="editSpd" placeholder="速度值" min="0">
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="cyber-btn cyber-btn-outline" data-bs-dismiss="modal">
                        <i class="fas fa-times"></i> 取消
                    </button>
                    <button type="button" class="cyber-btn cyber-btn-success" v-on:click="submitEdit" v-bind:disabled="submitting">
                        <span v-if="submitting" class="spinner-border spinner-border-sm me-2" role="status"></span>
                        <i v-if="!submitting" class="fas fa-check"></i>
                        {{ submitting ? '提交中...' : '确定' }}
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 科幻详情模态框 -->
    <div class="modal fade cyber-modal" id="detailModal" tabindex="-1" aria-labelledby="detailModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="detailModalLabel">
                        <i class="fas fa-info-circle me-2"></i>地图怪物详情
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="row" v-if="selectedItem">
                        <div class="col-md-6 mb-2">
                            <strong>地图ID:</strong> <span v-text="selectedItem.mapId"></span>
                        </div>
                        <div class="col-md-6 mb-2">
                            <strong>怪物序号:</strong> <span v-text="selectedItem.monsterId"></span>
                        </div>
                        <div class="col-md-6 mb-2">
                            <strong>怪物名称:</strong> <span v-text="selectedItem.monsterName"></span>
                        </div>
                        <div class="col-md-6 mb-2">
                            <strong>怪物成长:</strong> <span v-text="selectedItem.growth || 0"></span>
                        </div>
                        <div class="col-md-6 mb-2">
                            <strong>五行属性:</strong> <span v-text="selectedItem.element || '-'"></span>
                        </div>
                        <div class="col-md-6 mb-2">
                            <strong>等级范围:</strong> <span>{{ selectedItem.minLevel }}-{{ selectedItem.maxLevel }}</span>
                        </div>
                        <div class="col-md-6 mb-2">
                            <strong>最大掉落:</strong> <span v-text="selectedItem.maxDrop || 0"></span>
                        </div>
                        <div class="col-md-6 mb-2">
                            <strong>经验值:</strong> <span v-text="selectedItem.exp || 0"></span>
                        </div>
                        <div class="col-md-6 mb-2">
                            <strong>生命:</strong> <span v-text="selectedItem.hp || 0"></span>
                        </div>
                        <div class="col-md-6 mb-2">
                            <strong>魔法:</strong> <span v-text="selectedItem.mp || 0"></span>
                        </div>
                        <div class="col-md-6 mb-2">
                            <strong>最大生命:</strong> <span v-text="selectedItem.maxHp || 0"></span>
                        </div>
                        <div class="col-md-6 mb-2">
                            <strong>最大魔法:</strong> <span v-text="selectedItem.maxMp || 0"></span>
                        </div>
                        <div class="col-md-6 mb-2">
                            <strong>攻击:</strong> <span v-text="selectedItem.atk || 0"></span>
                        </div>
                        <div class="col-md-6 mb-2">
                            <strong>防御:</strong> <span v-text="selectedItem.def || 0"></span>
                        </div>
                        <div class="col-md-6 mb-2">
                            <strong>闪避:</strong> <span v-text="selectedItem.dodge || 0"></span>
                        </div>
                        <div class="col-md-6 mb-2">
                            <strong>速度:</strong> <span v-text="selectedItem.spd || 0"></span>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="cyber-btn cyber-btn-outline" data-bs-dismiss="modal">
                        <i class="fas fa-times me-1"></i>关闭
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        Vue.createApp({
            data() {
                return {
                    mapMonsters: [],
                    maps: [],
                    monsterConfigs: [],
                    searchForm: {
                        MapId: '',
                        MonsterName: '',
                        Element: ''
                    },
                    pagination: {
                        page: 1,
                        pageSize: 10,
                        totalCount: 0,
                        totalPages: 0
                    },
                    loading: false,
                    submitting: false,
                    selectedItem: null,
                    // 新增模态框搜索相关
                    createMonsterDropdownOpen: false,
                    createMonsterSearchText: '',
                    filteredCreateMonsterOptions: [],
                    // 编辑模态框搜索相关
                    editMonsterDropdownOpen: false,
                    editMonsterSearchText: '',
                    filteredEditMonsterOptions: [],
                    createForm: {
                        mapId: '',
                        selectedMonsterId: '',
                        monsterId: '',
                        monsterName: '',
                        growth: 0,
                        element: '',
                        minLevel: 0,
                        maxLevel: 0,
                        maxDrop: 0,
                        exp: 0,
                        hp: 0,
                        mp: 0,
                        maxHp: 0,
                        maxMp: 0,
                        atk: 0,
                        def: 0,
                        dodge: 0,
                        spd: 0
                    },
                    editForm: {
                        id: '',
                        mapId: '',
                        selectedMonsterId: '',
                        monsterId: '',
                        monsterName: '',
                        growth: 0,
                        element: '',
                        minLevel: 0,
                        maxLevel: 0,
                        maxDrop: 0,
                        exp: 0,
                        hp: 0,
                        mp: 0,
                        maxHp: 0,
                        maxMp: 0,
                        atk: 0,
                        def: 0,
                        dodge: 0,
                        spd: 0
                    }
                };
            },
            async mounted() {
                try {
                    // 串行加载以避免并发连接问题
                    await this.loadMaps();
                    await this.loadMonsterConfigs();
                    await this.loadMapMonsters();
                    console.log('✅ 地图怪物管理页面初始化完成');
                } catch (error) {
                    console.error('❌ 页面初始化失败:', error);
                }
            },
            methods: {
                // 加载地图列表
                async loadMaps() {
                    // 防止重复调用
                    if (this._loadingMaps) {
                        console.log('🗺️ loadMaps已在执行中，跳过重复调用');
                        return;
                    }
                    this._loadingMaps = true;
                    
                    try {
                        const response = await fetch('/MapConfig/GetAll');
                        const result = await response.json();
                        if (result.success) {
                            this.maps = result.data || [];
                            console.log('✅ 成功加载地图列表，数量:', this.maps.length);
                        } else {
                            console.error('❌ 加载地图列表失败:', result.message);
                        }
                    } catch (error) {
                        console.error('❌ 加载地图列表异常：', error);
                    } finally {
                        this._loadingMaps = false;
                    }
                },

                // 加载怪物配置列表
                async loadMonsterConfigs() {
                    try {
                        console.log('开始加载怪物配置列表...');
                        const response = await fetch('/MonsterConfig/GetAll');
                        console.log('怪物配置API响应状态:', response.status);
                        const result = await response.json();
                        console.log('怪物配置API响应结果:', result);
                        if (result.success) {
                            this.monsterConfigs = result.data || [];
                            console.log('加载到的怪物配置数据:', this.monsterConfigs);
                        } else {
                            console.error('加载怪物配置失败:', result.message);
                            alert('加载怪物配置失败：' + result.message);
                        }
                    } catch (error) {
                        console.error('加载怪物配置列表失败：', error);
                        alert('加载怪物配置列表失败：' + error.message);
                    }
                },

                // ===== 新增模态框搜索相关方法 =====
                toggleCreateMonsterDropdown() {
                    this.createMonsterDropdownOpen = !this.createMonsterDropdownOpen;
                    if (this.createMonsterDropdownOpen) {
                        this.filteredCreateMonsterOptions = [...this.monsterConfigs];
                    }
                },

                closeCreateMonsterDropdown() {
                    this.createMonsterDropdownOpen = false;
                },

                filterCreateMonsterOptions() {
                    const searchText = this.createMonsterSearchText.toLowerCase();
                    this.filteredCreateMonsterOptions = this.monsterConfigs.filter(monster =>
                        monster.name.toLowerCase().includes(searchText) ||
                        monster.id.toString().includes(searchText)
                    );
                },

                selectCreateMonster(monster) {
                    this.createForm.selectedMonsterId = monster.id;
                    this.createForm.monsterId = monster.id;
                    this.createForm.monsterName = monster.name;
                    this.createForm.element = monster.attribute || '';
                    this.createMonsterSearchText = `${monster.name} (ID: ${monster.id})`;
                    this.createMonsterDropdownOpen = false;
                },

                // ===== 编辑模态框搜索相关方法 =====
                toggleEditMonsterDropdown() {
                    this.editMonsterDropdownOpen = !this.editMonsterDropdownOpen;
                    if (this.editMonsterDropdownOpen) {
                        this.filteredEditMonsterOptions = [...this.monsterConfigs];
                    }
                },

                closeEditMonsterDropdown() {
                    this.editMonsterDropdownOpen = false;
                },

                filterEditMonsterOptions() {
                    const searchText = this.editMonsterSearchText.toLowerCase();
                    this.filteredEditMonsterOptions = this.monsterConfigs.filter(monster =>
                        monster.name.toLowerCase().includes(searchText) ||
                        monster.id.toString().includes(searchText)
                    );
                },

                selectEditMonster(monster) {
                    this.editForm.selectedMonsterId = monster.id;
                    this.editForm.monsterId = monster.id;
                    this.editForm.monsterName = monster.name;
                    this.editForm.element = monster.attribute || '';
                    this.editMonsterSearchText = `${monster.name} (ID: ${monster.id})`;
                    this.editMonsterDropdownOpen = false;
                },

                // 加载地图怪物列表
                async loadMapMonsters() {
                    this.loading = true;
                    try {
                        // 构建查询参数，过滤掉空值
                        const queryParams = {
                            Page: this.pagination.page,
                            PageSize: this.pagination.pageSize
                        };
                        
                        // 只添加非空的搜索条件
                        if (this.searchForm.MapId) {
                            queryParams.MapId = this.searchForm.MapId;
                        }
                        if (this.searchForm.MonsterName) {
                            queryParams.MonsterName = this.searchForm.MonsterName;
                        }
                        if (this.searchForm.Element) {
                            queryParams.Element = this.searchForm.Element;
                        }
                        
                        const params = new URLSearchParams(queryParams);
                        
                        const response = await fetch('/MapMonster/GetPagedList?' + params.toString());
                        const result = await response.json();
                        
                        if (result.success) {
                            this.mapMonsters = result.data || [];
                            this.pagination.totalCount = result.totalCount;
                            this.pagination.totalPages = result.totalPages;
                        } else {
                            alert('加载数据失败：' + result.message);
                        }
                    } catch (error) {
                        console.error('加载地图怪物列表失败：', error);
                        alert('加载数据失败');
                    } finally {
                        this.loading = false;
                    }
                },
                
                // 搜索
                searchMapMonsters() {
                    this.pagination.page = 1;
                    this.loadMapMonsters();
                },
                
                // 重置搜索
                resetSearch() {
                    this.searchForm = {
                        MapId: '',
                        MonsterName: '',
                        Element: ''
                    };
                    this.pagination.page = 1;
                    this.loadMapMonsters();
                },
                
                // 分页
                changePage(page) {
                    if (page >= 1 && page <= this.pagination.totalPages) {
                        this.pagination.page = page;
                        this.loadMapMonsters();
                    }
                },
                
                // 获取页码数组
                getPageNumbers() {
                    const pages = [];
                    const start = Math.max(1, this.pagination.page - 2);
                    const end = Math.min(this.pagination.totalPages, start + 4);
                    
                    for (let i = start; i <= end; i++) {
                        pages.push(i);
                    }
                    return pages;
                },
                


                // 显示新增模态框
                showCreateModal() {
                    // 重置表单
                    this.createForm = {
                        mapId: '',
                        selectedMonsterId: '',
                        monsterId: '',
                        monsterName: '',
                        growth: 0,
                        element: '',
                        minLevel: 0,
                        maxLevel: 0,
                        maxDrop: 0,
                        exp: 0,
                        hp: 0,
                        mp: 0,
                        maxHp: 0,
                        maxMp: 0,
                        atk: 0,
                        def: 0,
                        dodge: 0,
                        spd: 0
                    };

                    // 重置搜索相关状态
                    this.createMonsterDropdownOpen = false;
                    this.createMonsterSearchText = '';
                    this.filteredCreateMonsterOptions = [...this.monsterConfigs];

                    // 显示模态框
                    const modal = new bootstrap.Modal(document.getElementById('createModal'));
                    modal.show();
                },
                
                // 提交新增
                async submitCreate() {
                    if (!this.validateCreateForm()) {
                        return;
                    }
                    
                    this.submitting = true;
                    try {
                        const response = await fetch('/MapMonster/Create', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]')?.value || ''
                            },
                            body: JSON.stringify(this.createForm)
                        });
                        
                        const result = await response.json();
                        
                        if (result.success) {
                            alert(result.message || '创建成功');
                            // 关闭模态框
                            const modal = bootstrap.Modal.getInstance(document.getElementById('createModal'));
                            modal.hide();
                            // 刷新列表
                            this.loadMapMonsters();
                        } else {
                            alert(result.message || '创建失败');
                        }
                    } catch (error) {
                        console.error('创建地图怪物失败：', error);
                        alert('创建失败');
                    } finally {
                        this.submitting = false;
                    }
                },
                
                // 验证新增表单
                validateCreateForm() {
                    if (!this.createForm.mapId) {
                        alert('请选择地图');
                        return false;
                    }
                    if (!this.createForm.selectedMonsterId) {
                        alert('请选择怪物');
                        return false;
                    }
                    return true;
                },
                
                // 显示编辑模态框
                showEditModal(item) {
                    // 复制数据到编辑表单
                    this.editForm = {
                        id: item.id,
                        mapId: item.mapId,
                        selectedMonsterId: item.monsterId, // 设置选中的怪物ID
                        monsterId: item.monsterId,
                        monsterName: item.monsterName,
                        growth: item.growth || 0,
                        element: item.element || '',
                        minLevel: item.minLevel || 0,
                        maxLevel: item.maxLevel || 0,
                        maxDrop: item.maxDrop || 0,
                        exp: item.exp || 0,
                        hp: item.hp || 0,
                        mp: item.mp || 0,
                        maxHp: item.maxHp || 0,
                        maxMp: item.maxMp || 0,
                        atk: item.atk || 0,
                        def: item.def || 0,
                        dodge: item.dodge || 0,
                        spd: item.spd || 0
                    };

                    // 设置搜索框显示文本
                    this.editMonsterDropdownOpen = false;
                    this.editMonsterSearchText = `${item.monsterName} (ID: ${item.monsterId})`;
                    this.filteredEditMonsterOptions = [...this.monsterConfigs];

                    // 显示模态框
                    const modal = new bootstrap.Modal(document.getElementById('editModal'));
                    modal.show();
                },
                
                // 提交编辑
                async submitEdit() {
                    if (!this.validateEditForm()) {
                        return;
                    }
                    
                    this.submitting = true;
                    try {
                        const response = await fetch('/MapMonster/Update', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]')?.value || ''
                            },
                            body: JSON.stringify(this.editForm)
                        });
                        
                        const result = await response.json();
                        
                        if (result.success) {
                            alert(result.message || '更新成功');
                            // 关闭模态框
                            const modal = bootstrap.Modal.getInstance(document.getElementById('editModal'));
                            modal.hide();
                            // 刷新列表
                            this.loadMapMonsters();
                        } else {
                            alert(result.message || '更新失败');
                        }
                    } catch (error) {
                        console.error('更新地图怪物失败：', error);
                        alert('更新失败');
                    } finally {
                        this.submitting = false;
                    }
                },
                
                // 验证编辑表单
                validateEditForm() {
                    if (!this.editForm.mapId) {
                        alert('请选择地图');
                        return false;
                    }
                    if (!this.editForm.selectedMonsterId) {
                        alert('请选择怪物');
                        return false;
                    }
                    return true;
                },
                
                // 查看详情
                viewDetails(item) {
                    this.selectedItem = item;
                    const modal = new bootstrap.Modal(document.getElementById('detailModal'));
                    modal.show();
                },
                
                // 删除地图怪物
                async deleteMapMonster(id) {
                    if (!confirm('确定要删除这个地图怪物吗？')) {
                        return;
                    }
                    
                    try {
                        const response = await fetch('/MapMonster/Delete', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]')?.value || ''
                            },
                            body: JSON.stringify({ id })
                        });
                        
                        const result = await response.json();
                        
                        if (result.success) {
                            alert(result.message || '删除成功');
                            this.loadMapMonsters();
                        } else {
                            alert(result.message || '删除失败');
                        }
                    } catch (error) {
                        console.error('删除地图怪物失败：', error);
                        alert('删除失败');
                    }
                }
            },
            // 添加click-outside指令
            directives: {
                'click-outside': {
                    mounted(el, binding) {
                        el.clickOutsideEvent = function(event) {
                            if (!(el === event.target || el.contains(event.target))) {
                                binding.value();
                            }
                        };
                        document.addEventListener('click', el.clickOutsideEvent);
                    },
                    unmounted(el) {
                        document.removeEventListener('click', el.clickOutsideEvent);
                    }
                }
            }
        }).mount('#mapMonsterApp');
    </script>
}