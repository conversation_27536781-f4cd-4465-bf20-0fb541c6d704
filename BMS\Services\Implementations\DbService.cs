using SqlSugar;
using BMS.Services.Interfaces;
using BMS.Sugar;

namespace BMS.Services.Implementations
{
    /// <summary>
    /// 数据库服务实现
    /// </summary>
    public class DbService : IDbService
    {
        private readonly DbContext _dbContext;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="dbContext">自定义数据库上下文</param>
        public DbService(DbContext dbContext)
        {
            _dbContext = dbContext;
        }

        /// <summary>
        /// 获取数据库客户端
        /// </summary>
        /// <returns>SqlSugar客户端</returns>
        public ISqlSugarClient GetClient()
        {
            return _dbContext.Db;
        }

        /// <summary>
        /// 获取可查询对象
        /// </summary>
        /// <typeparam name="T">实体类型</typeparam>
        /// <returns>可查询对象</returns>
        public ISugarQueryable<T> Queryable<T>() where T : class, new()
        {
            return _dbContext.Db.Queryable<T>();
        }

        /// <summary>
        /// 获取可更新对象
        /// </summary>
        /// <typeparam name="T">实体类型</typeparam>
        /// <returns>可更新对象</returns>
        public IUpdateable<T> Updateable<T>() where T : class, new()
        {
            return _dbContext.Db.Updateable<T>();
        }

        /// <summary>
        /// 获取可更新对象(带实体参数)
        /// </summary>
        /// <typeparam name="T">实体类型</typeparam>
        /// <param name="entity">实体对象</param>
        /// <returns>可更新对象</returns>
        public IUpdateable<T> Updateable<T>(T entity) where T : class, new()
        {
            return _dbContext.Db.Updateable(entity);
        }

        /// <summary>
        /// 获取可插入对象
        /// </summary>
        /// <typeparam name="T">实体类型</typeparam>
        /// <returns>可插入对象</returns>
        public IInsertable<T> Insertable<T>() where T : class, new()
        {
            return _dbContext.Db.Insertable<T>(new T());
        }

        /// <summary>
        /// 获取可插入对象(带实体参数)
        /// </summary>
        /// <typeparam name="T">实体类型</typeparam>
        /// <param name="entity">实体对象</param>
        /// <returns>可插入对象</returns>
        public IInsertable<T> Insertable<T>(T entity) where T : class, new()
        {
            return _dbContext.Db.Insertable(entity);
        }

        /// <summary>
        /// 获取可删除对象
        /// </summary>
        /// <typeparam name="T">实体类型</typeparam>
        /// <returns>可删除对象</returns>
        public IDeleteable<T> Deleteable<T>() where T : class, new()
        {
            return _dbContext.Db.Deleteable<T>();
        }

        /// <summary>
        /// 开始事务
        /// </summary>
        /// <returns>事务对象</returns>
        public ITenant BeginTran()
        {
            var tenant = _dbContext.Db.AsTenant();
            tenant.BeginTran();
            return tenant;
        }
    }
} 