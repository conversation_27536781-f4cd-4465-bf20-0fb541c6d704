-- 宠物进化配置测试数据
-- 注意：执行前请确保pet_config表中已有相应的宠物数据

-- 插入一些测试的宠物进化配置
INSERT INTO pet_evolution_config (
    pet_no, 
    evolution_type, 
    target_pet_no, 
    required_level, 
    required_item_id, 
    required_item_count, 
    cost_gold, 
    growth_min, 
    growth_max, 
    success_rate, 
    is_active, 
    description, 
    create_time
) VALUES 
-- 金波姆 -> 金波姆王 (A路线)
(1, 'A', 3, 40, 'item_001', 1, 5000, 0.100, 0.300, 85.00, 1, '金波姆的A路线进化，需要进化石', NOW()),

-- 金波姆 -> 波光姆 (B路线) 
(1, 'B', 2, 35, 'item_002', 2, 3000, 0.080, 0.250, 90.00, 1, '金波姆的B路线进化，需要光明石', NOW()),

-- 波光姆 -> 黄金鸟 (A路线)
(2, 'A', 4, 50, 'item_003', 1, 8000, 0.150, 0.400, 75.00, 1, '波光姆的A路线进化，需要飞行石', NOW()),

-- 金波姆王 -> 金光鼠 (A路线)
(3, 'A', 5, 60, 'item_004', 3, 12000, 0.200, 0.500, 70.00, 1, '金波姆王的终极进化，需要王者石', NOW()),

-- 黄金鸟 -> 金光鼠 (A路线)
(4, 'A', 5, 55, 'item_005', 2, 10000, 0.180, 0.450, 80.00, 1, '黄金鸟的终极进化，需要神圣石', NOW());

-- 查询插入的数据
SELECT 
    pec.id,
    pec.pet_no,
    pc1.name as pet_name,
    pec.evolution_type,
    pec.target_pet_no,
    pc2.name as target_pet_name,
    pec.required_level,
    pec.required_item_id,
    pec.required_item_count,
    pec.cost_gold,
    pec.growth_min,
    pec.growth_max,
    pec.success_rate,
    pec.is_active,
    pec.description,
    pec.create_time
FROM pet_evolution_config pec
LEFT JOIN pet_config pc1 ON pec.pet_no = pc1.pet_no
LEFT JOIN pet_config pc2 ON pec.target_pet_no = pc2.pet_no
ORDER BY pec.pet_no, pec.evolution_type;
