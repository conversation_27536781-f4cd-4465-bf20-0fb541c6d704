@{
    Layout = null;
    ViewData["Title"] = "登录";
}

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>@ViewData["Title"] - 后台管理系统</title>

    <!-- Google Font: Source Sans Pro -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="~/lib/bootstrap/dist/css/bootstrap.min.css" />
    <!-- Toastr -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.css" />
    
    <style>
        :root {
            --cyber-blue: #00d4ff;
            --cyber-purple: #8b5cf6;
            --cyber-pink: #ec4899;
            --cyber-green: #10b981;
            --cyber-gold: #f59e0b;
            --cyber-red: #ef4444;
            --cyber-dark: #0a0a0a;
            --cyber-darker: #050505;
            --cyber-light: rgba(255, 255, 255, 0.1);
            --cyber-glow: 0 0 20px;
            --cyber-transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 0;
            min-height: 100vh;
            background: radial-gradient(ellipse at center, #1a1a2e 0%, #16213e 50%, #0f0f23 100%);
            position: relative;
            overflow: hidden;
            color: #ffffff;
        }

        /* 科幻背景网格 */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image:
                linear-gradient(rgba(0, 212, 255, 0.1) 1px, transparent 1px),
                linear-gradient(90deg, rgba(0, 212, 255, 0.1) 1px, transparent 1px);
            background-size: 50px 50px;
            animation: gridMove 20s linear infinite;
            z-index: 1;
        }

        @@keyframes gridMove {
            0% { transform: translate(0, 0); }
            100% { transform: translate(50px, 50px); }
        }

        /* 科幻粒子系统 */
        .particles {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
            z-index: 2;
            pointer-events: none;
        }

        .particle {
            position: absolute;
            background: var(--cyber-blue);
            border-radius: 50%;
            box-shadow: var(--cyber-glow) var(--cyber-blue);
            animation: cyberFloat 15s infinite linear;
        }

        .particle.purple {
            background: var(--cyber-purple);
            box-shadow: var(--cyber-glow) var(--cyber-purple);
            animation-duration: 18s;
        }

        .particle.pink {
            background: var(--cyber-pink);
            box-shadow: var(--cyber-glow) var(--cyber-pink);
            animation-duration: 12s;
        }

        @@keyframes cyberFloat {
            0% {
                transform: translateY(100vh) translateX(0) rotate(0deg) scale(0);
                opacity: 0;
            }
            5% {
                opacity: 1;
                transform: translateY(95vh) translateX(0) rotate(18deg) scale(1);
            }
            95% {
                opacity: 1;
                transform: translateY(5vh) translateX(100px) rotate(342deg) scale(1);
            }
            100% {
                transform: translateY(-5vh) translateX(200px) rotate(360deg) scale(0);
                opacity: 0;
            }
        }

        /* 科幻光束效果 */
        .cyber-beams {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1;
            pointer-events: none;
        }

        .beam {
            position: absolute;
            width: 2px;
            height: 100vh;
            background: linear-gradient(to bottom,
                transparent 0%,
                var(--cyber-blue) 50%,
                transparent 100%);
            animation: beamMove 8s infinite linear;
            opacity: 0.3;
        }

        .beam:nth-child(2) {
            background: linear-gradient(to bottom,
                transparent 0%,
                var(--cyber-purple) 50%,
                transparent 100%);
            animation-delay: -2s;
            animation-duration: 10s;
        }

        .beam:nth-child(3) {
            background: linear-gradient(to bottom,
                transparent 0%,
                var(--cyber-pink) 50%,
                transparent 100%);
            animation-delay: -4s;
            animation-duration: 12s;
        }

        @@keyframes beamMove {
            0% {
                left: -2px;
                transform: skewX(-10deg);
            }
            100% {
                left: 100vw;
                transform: skewX(-10deg);
            }
        }

        .login-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            z-index: 10;
            padding: 20px;
        }

        .login-card {
            background: rgba(10, 10, 10, 0.85);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            border: 2px solid transparent;
            background-clip: padding-box;
            box-shadow:
                0 0 50px rgba(0, 212, 255, 0.3),
                inset 0 0 50px rgba(255, 255, 255, 0.05);
            width: 100%;
            max-width: 480px;
            padding: 60px 50px;
            position: relative;
            animation: cyberSlideUp 1s ease-out;
            overflow: hidden;
        }

        /* 科幻边框动画 */
        .login-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            border-radius: 20px;
            padding: 2px;
            background: linear-gradient(45deg,
                var(--cyber-blue),
                var(--cyber-purple),
                var(--cyber-pink),
                var(--cyber-blue));
            background-size: 400% 400%;
            animation: borderGlow 4s ease infinite;
            mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
            mask-composite: exclude;
            z-index: -1;
        }

        @@keyframes borderGlow {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }

        @@keyframes cyberSlideUp {
            0% {
                opacity: 0;
                transform: translateY(60px) scale(0.9);
                filter: blur(10px);
            }
            50% {
                opacity: 0.7;
                transform: translateY(20px) scale(0.95);
                filter: blur(5px);
            }
            100% {
                opacity: 1;
                transform: translateY(0) scale(1);
                filter: blur(0);
            }
        }

        /* 科幻扫描线效果 */
        .login-card::after {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg,
                transparent,
                rgba(0, 212, 255, 0.1),
                transparent);
            animation: scanLine 3s infinite;
            z-index: 1;
        }

        @@keyframes scanLine {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        .login-header {
            text-align: center;
            margin-bottom: 50px;
            position: relative;
            z-index: 2;
        }

        .login-title {
            font-size: 36px;
            font-weight: 700;
            background: linear-gradient(45deg, var(--cyber-blue), var(--cyber-purple));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 12px;
            letter-spacing: 2px;
            text-transform: uppercase;
            animation: titleGlow 2s ease-in-out infinite alternate;
            position: relative;
        }

        .login-title::after {
            content: '';
            position: absolute;
            bottom: -5px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 2px;
            background: linear-gradient(45deg, var(--cyber-blue), var(--cyber-purple));
            animation: lineExpand 1s ease-out 0.5s both;
        }

        @@keyframes titleGlow {
            0% { filter: brightness(1) drop-shadow(0 0 5px var(--cyber-blue)); }
            100% { filter: brightness(1.2) drop-shadow(0 0 15px var(--cyber-blue)); }
        }

        @@keyframes lineExpand {
            0% { width: 0; }
            100% { width: 60px; }
        }

        .login-subtitle {
            color: rgba(255, 255, 255, 0.7);
            font-size: 16px;
            font-weight: 400;
            margin: 0;
            margin-top: 20px;
            animation: fadeInUp 1s ease-out 0.8s both;
        }

        @@keyframes fadeInUp {
            0% {
                opacity: 0;
                transform: translateY(20px);
            }
            100% {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .form-group {
            margin-bottom: 30px;
            position: relative;
            animation: formSlideIn 0.8s ease-out both;
        }

        .form-group:nth-child(2) { animation-delay: 0.2s; }
        .form-group:nth-child(3) { animation-delay: 0.4s; }

        @@keyframes formSlideIn {
            0% {
                opacity: 0;
                transform: translateX(-30px);
            }
            100% {
                opacity: 1;
                transform: translateX(0);
            }
        }

        .form-control {
            width: 100%;
            padding: 18px 60px 18px 24px;
            border: 2px solid rgba(0, 212, 255, 0.3);
            border-radius: 12px;
            font-size: 16px;
            font-weight: 500;
            transition: var(--cyber-transition);
            background: rgba(255, 255, 255, 0.08);
            color: #ffffff !important;
            backdrop-filter: blur(10px);
            position: relative;
            z-index: 2;
            text-shadow: 0 0 2px rgba(255, 255, 255, 0.3);
        }

        .form-control:focus {
            outline: none;
            border-color: var(--cyber-blue);
            box-shadow:
                0 0 20px rgba(0, 212, 255, 0.3),
                inset 0 0 20px rgba(0, 212, 255, 0.1);
            transform: translateY(-2px);
            background: rgba(255, 255, 255, 0.12);
            color: #ffffff !important;
            text-shadow: 0 0 3px rgba(255, 255, 255, 0.5);
        }

        .form-control::placeholder {
            color: rgba(255, 255, 255, 0.7);
            font-weight: 400;
            text-shadow: 0 0 1px rgba(255, 255, 255, 0.2);
        }

        /* 确保输入文字始终为白色 */
        .form-control,
        .form-control:focus,
        .form-control:active,
        .form-control:hover {
            color: #ffffff !important;
        }

        /* 针对不同浏览器的输入框文字颜色 */
        input[type="text"],
        input[type="password"],
        input[type="email"] {
            color: #ffffff !important;
            -webkit-text-fill-color: #ffffff !important;
        }

        /* 自动填充时的样式 */
        .form-control:-webkit-autofill,
        .form-control:-webkit-autofill:hover,
        .form-control:-webkit-autofill:focus {
            -webkit-text-fill-color: #ffffff !important;
            -webkit-box-shadow: 0 0 0px 1000px rgba(255, 255, 255, 0.08) inset !important;
            transition: background-color 5000s ease-in-out 0s;
        }

        .input-icon {
            position: absolute;
            right: 20px;
            top: 50%;
            transform: translateY(-50%);
            color: rgba(0, 212, 255, 0.6);
            font-size: 18px;
            pointer-events: none;
            transition: var(--cyber-transition);
            z-index: 3;
        }

        .form-control:focus + .input-icon {
            color: var(--cyber-blue);
            filter: drop-shadow(0 0 5px var(--cyber-blue));
            animation: iconPulse 1s ease-in-out infinite alternate;
        }

        @@keyframes iconPulse {
            0% { transform: translateY(-50%) scale(1); }
            100% { transform: translateY(-50%) scale(1.1); }
        }

        /* 科幻输入框发光效果 */
        .form-group::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            border-radius: 12px;
            background: linear-gradient(45deg,
                transparent,
                rgba(0, 212, 255, 0.1),
                transparent);
            opacity: 0;
            transition: var(--cyber-transition);
            z-index: 1;
        }

        .form-group:hover::before {
            opacity: 1;
        }

        .remember-row {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 40px;
            animation: formSlideIn 0.8s ease-out 0.6s both;
        }

        .custom-checkbox {
            display: flex;
            align-items: center;
            gap: 12px;
            cursor: pointer;
            user-select: none;
            position: relative;
        }

        .custom-checkbox input[type="checkbox"] {
            width: 20px;
            height: 20px;
            border: 2px solid rgba(0, 212, 255, 0.5);
            border-radius: 6px;
            cursor: pointer;
            transition: var(--cyber-transition);
            background: transparent;
            appearance: none;
            position: relative;
        }

        .custom-checkbox input[type="checkbox"]:checked {
            background: linear-gradient(45deg, var(--cyber-blue), var(--cyber-purple));
            border-color: var(--cyber-blue);
            box-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
        }

        .custom-checkbox input[type="checkbox"]:checked::after {
            content: '✓';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-size: 12px;
            font-weight: bold;
        }

        .custom-checkbox label {
            color: rgba(255, 255, 255, 0.8);
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            margin: 0;
            transition: var(--cyber-transition);
        }

        .custom-checkbox:hover label {
            color: var(--cyber-blue);
        }

        .forgot-link {
            color: var(--cyber-blue);
            text-decoration: none;
            font-size: 14px;
            font-weight: 500;
            transition: var(--cyber-transition);
            position: relative;
        }

        .forgot-link::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            width: 0;
            height: 1px;
            background: var(--cyber-blue);
            transition: var(--cyber-transition);
        }

        .forgot-link:hover {
            color: var(--cyber-purple);
            text-decoration: none;
            filter: drop-shadow(0 0 5px var(--cyber-blue));
        }

        .forgot-link:hover::after {
            width: 100%;
        }

        .login-btn {
            width: 100%;
            padding: 18px;
            background: linear-gradient(45deg, var(--cyber-blue), var(--cyber-purple));
            border: 2px solid transparent;
            border-radius: 12px;
            color: white;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: var(--cyber-transition);
            position: relative;
            overflow: hidden;
            text-transform: uppercase;
            letter-spacing: 1px;
            animation: formSlideIn 0.8s ease-out 0.8s both;
        }

        .login-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg,
                transparent,
                rgba(255, 255, 255, 0.2),
                transparent);
            transition: var(--cyber-transition);
        }

        .login-btn:hover:not(:disabled) {
            transform: translateY(-3px);
            box-shadow:
                0 15px 35px rgba(0, 212, 255, 0.4),
                0 0 30px rgba(139, 92, 246, 0.3);
            border-color: rgba(255, 255, 255, 0.3);
        }

        .login-btn:hover:not(:disabled)::before {
            left: 100%;
        }

        .login-btn:active:not(:disabled) {
            transform: translateY(-1px);
        }

        .login-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
            filter: grayscale(0.5);
        }

        .btn-content {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            position: relative;
            z-index: 1;
        }

        .btn-spinner {
            width: 22px;
            height: 22px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-top: 2px solid white;
            border-radius: 50%;
            animation: cyberSpin 1s linear infinite;
        }

        @@keyframes cyberSpin {
            0% {
                transform: rotate(0deg);
                box-shadow: 0 0 5px var(--cyber-blue);
            }
            50% {
                box-shadow: 0 0 15px var(--cyber-blue);
            }
            100% {
                transform: rotate(360deg);
                box-shadow: 0 0 5px var(--cyber-blue);
            }
        }

        .error-message {
            background: rgba(239, 68, 68, 0.15);
            border: 1px solid var(--cyber-red);
            color: #ff6b6b;
            padding: 16px 20px;
            border-radius: 10px;
            font-size: 14px;
            margin-bottom: 25px;
            display: flex;
            align-items: center;
            gap: 10px;
            backdrop-filter: blur(10px);
            animation: cyberShake 0.6s ease-in-out;
            box-shadow: 0 0 20px rgba(239, 68, 68, 0.2);
        }

        .success-message {
            background: rgba(34, 197, 94, 0.15);
            border: 1px solid var(--cyber-green);
            color: #4ade80;
            padding: 16px 20px;
            border-radius: 10px;
            font-size: 14px;
            margin-bottom: 25px;
            display: flex;
            align-items: center;
            gap: 10px;
            backdrop-filter: blur(10px);
            animation: successSlideIn 0.5s ease-out;
            box-shadow: 0 0 20px rgba(34, 197, 94, 0.2);
        }

        @@keyframes cyberShake {
            0%, 100% {
                transform: translateX(0);
                box-shadow: 0 0 20px rgba(239, 68, 68, 0.2);
            }
            25% {
                transform: translateX(-8px);
                box-shadow: 0 0 25px rgba(239, 68, 68, 0.4);
            }
            75% {
                transform: translateX(8px);
                box-shadow: 0 0 25px rgba(239, 68, 68, 0.4);
            }
        }

        @@keyframes successSlideIn {
            0% {
                opacity: 0;
                transform: translateY(-20px);
                filter: blur(5px);
            }
            100% {
                opacity: 1;
                transform: translateY(0);
                filter: blur(0);
            }
        }

        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(10, 10, 10, 0.9);
            backdrop-filter: blur(10px);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            opacity: 0;
            animation: cyberFadeIn 0.5s ease-out forwards;
        }

        @@keyframes cyberFadeIn {
            0% {
                opacity: 0;
                backdrop-filter: blur(0px);
            }
            100% {
                opacity: 1;
                backdrop-filter: blur(10px);
            }
        }

        .loading-spinner {
            width: 80px;
            height: 80px;
            border: 3px solid transparent;
            border-top: 3px solid var(--cyber-blue);
            border-right: 3px solid var(--cyber-purple);
            border-radius: 50%;
            animation: cyberSpinLoad 1.5s linear infinite;
            box-shadow: 0 0 30px rgba(0, 212, 255, 0.5);
        }

        @@keyframes cyberSpinLoad {
            0% {
                transform: rotate(0deg);
                box-shadow: 0 0 30px rgba(0, 212, 255, 0.5);
            }
            50% {
                box-shadow: 0 0 50px rgba(139, 92, 246, 0.7);
            }
            100% {
                transform: rotate(360deg);
                box-shadow: 0 0 30px rgba(0, 212, 255, 0.5);
            }
        }

        /* 科幻版权信息 */
        .cyber-footer {
            position: fixed;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            color: rgba(255, 255, 255, 0.4);
            font-size: 12px;
            text-align: center;
            z-index: 10;
            animation: fadeInUp 1s ease-out 1.5s both;
        }

        /* 响应式设计 */
        @@media (max-width: 480px) {
            .login-card {
                margin: 15px;
                padding: 50px 35px;
                border-radius: 20px;
                max-width: 95%;
            }

            .login-title {
                font-size: 28px;
                letter-spacing: 1px;
            }

            .form-control {
                padding: 16px 55px 16px 20px;
                font-size: 16px;
            }

            .input-icon {
                right: 18px;
                font-size: 16px;
            }

            .login-btn {
                padding: 16px;
                font-size: 15px;
            }

            .remember-row {
                flex-direction: column;
                gap: 15px;
                align-items: flex-start;
            }

            .particles {
                display: none; /* 在移动设备上隐藏粒子效果以提高性能 */
            }

            .cyber-beams {
                display: none; /* 在移动设备上隐藏光束效果 */
            }
        }

        @@media (max-width: 320px) {
            .login-card {
                padding: 40px 25px;
                margin: 10px;
            }

            .login-title {
                font-size: 24px;
            }

            .form-control {
                padding: 14px 50px 14px 18px;
            }
        }

    </style>
</head>
<body>
    <!-- 科幻背景效果 -->
    <div class="particles" id="particles"></div>
    <div class="cyber-beams">
        <div class="beam"></div>
        <div class="beam"></div>
        <div class="beam"></div>
    </div>

    <div id="loginApp">
        <div class="login-container">
            <div class="login-card">
                <div class="login-header">
                    <h1 class="login-title">口袋世界</h1>
                    <p class="login-subtitle"></p>
                </div>

                    <form v-on:submit.prevent="login">
                        @Html.AntiForgeryToken()
                        
                    <div class="error-message" v-if="errorMessage" v-text="errorMessage"></div>

                    <div class="form-group">
                        <input type="text" 
                               class="form-control" 
                               v-model="formData.username" 
                               placeholder="请输入用户名" 
                               required 
                               autofocus>
                        <i class="fas fa-user input-icon"></i>
                        </div>

                    <div class="form-group">
                        <input type="password" 
                               class="form-control" 
                               v-model="formData.password" 
                               placeholder="请输入密码" 
                               required>
                        <i class="fas fa-lock input-icon"></i>
                        </div>

                    <div class="remember-row">
                        <div class="custom-checkbox">
                                    <input type="checkbox" id="remember" v-model="formData.rememberMe">
                                    <label for="remember">记住我</label>
                                </div>
                        <a href="#" class="forgot-link">忘记密码？</a>
                            </div>

                    <button type="submit" class="login-btn" :disabled="isLoading">
                        <div class="btn-content">
                            <div v-if="isLoading" class="btn-spinner"></div>
                            <i v-else class="fas fa-sign-in-alt"></i>
                            <span>{{ isLoading ? '登录中...' : '登录' }}</span>
                        </div>
                    </button>
                    </form>
            </div>
        </div>

        <!-- Loading Overlay -->
        <div class="loading-overlay" v-if="isLoading">
            <div class="loading-spinner"></div>
        </div>
    </div>

    <!-- jQuery -->
    <script src="~/lib/jquery/dist/jquery.min.js"></script>
    <!-- Bootstrap 4 -->
    <script src="~/lib/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Toastr -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js"></script>
    <!-- Vue.js 3 -->
    <script src="~/lib/vue/vue.global.js"></script>

    <script>
        // 创建科幻粒子效果
        function createParticles() {
            const particlesContainer = document.getElementById('particles');
            const particleCount = 60;
            const particleTypes = ['', 'purple', 'pink'];

            for (let i = 0; i < particleCount; i++) {
                const particle = document.createElement('div');
                const typeClass = particleTypes[Math.floor(Math.random() * particleTypes.length)];
                particle.className = `particle ${typeClass}`;

                // 随机大小和位置
                const size = Math.random() * 6 + 2;
                const startX = Math.random() * window.innerWidth;
                const duration = Math.random() * 8 + 12;
                const delay = Math.random() * 15;

                particle.style.width = size + 'px';
                particle.style.height = size + 'px';
                particle.style.left = startX + 'px';
                particle.style.animationDuration = duration + 's';
                particle.style.animationDelay = delay + 's';

                particlesContainer.appendChild(particle);
            }
        }

        // 页面加载完成后创建粒子效果
        document.addEventListener('DOMContentLoaded', createParticles);

        // 配置toastr
        toastr.options = {
            closeButton: true,
            progressBar: true,
            positionClass: "toast-top-center",
            timeOut: 3000,
            extendedTimeOut: 1000,
            showEasing: "swing",
            hideEasing: "linear",
            showMethod: "fadeIn",
            hideMethod: "fadeOut"
        };

        // 初始化Vue应用
        Vue.createApp({
            data() {
                return {
                    formData: {
                        username: '',
                        password: '',
                        rememberMe: false
                    },
                    isLoading: false,
                    errorMessage: ''
                };
            },
            methods: {
                // 登录方法
                async login() {
                    this.isLoading = true;
                    this.errorMessage = '';

                    try {
                        const response = await fetch('@Url.Action("Login", "Auth")', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]').value
                            },
                            body: JSON.stringify(this.formData)
                        });
                        
                        const result = await response.json();
                        
                        if (result.success) {
                            toastr.success('登录成功，正在跳转...', '欢迎！');
                            setTimeout(() => {
                            window.location.href = '@Url.Action("Index", "Home")';
                            }, 1000);
                        } else {
                            this.errorMessage = result.message || '登录失败，请检查用户名和密码';
                            toastr.error(this.errorMessage, '登录失败');
                        }
                    } catch (error) {
                        console.error('登录失败:', error);
                        this.errorMessage = '登录失败，请稍后重试';
                        toastr.error('网络连接异常，请稍后重试', '连接失败');
                    } finally {
                        this.isLoading = false;
                    }
                }
            },
            mounted() {
                // 添加键盘事件监听
                document.addEventListener('keydown', (e) => {
                    if (e.key === 'Enter' && !this.isLoading) {
                        this.login();
                    }
                });
            }
        }).mount('#loginApp');
    </script>

    <!-- 科幻版权信息 -->
    <div class="cyber-footer">
         <div></div>
         <div style="margin-top: 5px; font-size: 10px; opacity: 0.6;">
            本系统版权由算法奇点工作室所有
         </div> 
    </div>
</body>
</html>