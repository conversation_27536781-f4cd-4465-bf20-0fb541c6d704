@model AdminBmUpdateDto
@{
    ViewData["Title"] = "编辑管理员";
}

<div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1>编辑管理员</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="@Url.Action("Index", "Home")">首页</a></li>
                        <li class="breadcrumb-item"><a href="@Url.Action("Index")">管理员管理</a></li>
                        <li class="breadcrumb-item active">编辑管理员</li>
                    </ol>
                </div>
            </div>
        </div>
    </section>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            <div class="card">
                <div class="card-body" id="editApp">
                    <form v-on:submit.prevent="submitForm">
                        @Html.AntiForgeryToken()
                        <input type="hidden" v-model="formData.id" />
                        <div class="form-group">
                            <label>用户名</label>
                            <input type="text" class="form-control" v-model="formData.username" readonly />
                        </div>
                        <div class="form-group">
                            <label>真实姓名</label>
                            <input type="text" class="form-control" v-model="formData.realName" 
                                   placeholder="请输入真实姓名" />
                        </div>
                        <div class="form-group">
                            <label>手机号码</label>
                            <input type="text" class="form-control" v-model="formData.phone" 
                                   placeholder="请输入手机号码" />
                            <small class="form-text text-danger" v-if="errors.phone">{{ errors.phone }}</small>
                        </div>
                        <div class="form-group">
                            <label>邮箱地址</label>
                            <input type="email" class="form-control" v-model="formData.email" 
                                   placeholder="请输入邮箱地址" />
                            <small class="form-text text-danger" v-if="errors.email">{{ errors.email }}</small>
                        </div>
                        <div class="form-group">
                            <label>状态</label>
                            <select class="form-control" v-model="formData.status">
                                <option value="1">启用</option>
                                <option value="0">禁用</option>
                            </select>
                        </div>
                        <button type="submit" class="btn btn-primary" :disabled="isSubmitting">
                            <i class="fas fa-save mr-1"></i>
                            {{ isSubmitting ? '保存中...' : '保存' }}
                        </button>
                        <a href="@Url.Action("Index")" class="btn btn-default">
                            <i class="fas fa-arrow-left mr-1"></i>
                            返回列表
                        </a>
                    </form>
                </div>
            </div>
        </div>
    </section>
</div>

@section Scripts {
    <script>
        // 初始化Vue应用
        Vue.createApp({
            data() {
                return {
                    formData: {
                        id: '@Model.Id',
                        username: '@Model.Username',
                        realName: '@Html.Raw(Model.RealName ?? "")',
                        phone: '@Html.Raw(Model.Phone ?? "")',
                        email: '@Html.Raw(Model.Email ?? "")',
                        status: '@Model.Status'
                    },
                    errors: {},
                    isSubmitting: false
                };
            },
            methods: {
                // 表单验证
                validate() {
                    this.errors = {};
                    
                    // 手机号验证
                    if (this.formData.phone) {
                        const phonePattern = '^1' + String.fromCharCode(91) + '3-9' + String.fromCharCode(93) + '\\d{9}$';
                        const phoneRegex = new RegExp(phonePattern);
                        if (!phoneRegex.test(this.formData.phone)) {
                            this.errors.phone = '请输入有效的手机号码';
                        }
                    }
                    
                    // 邮箱验证
                    if (this.formData.email) {
                        // 简单的邮箱格式验证，使用字符编码避免Razor解析at符号
                        const atChar = String.fromCharCode(64); // at符号
                        if (!this.formData.email.includes(atChar) || !this.formData.email.includes('.')) {
                            this.errors.email = '请输入有效的邮箱地址';
                        }
                    }
                    
                    return Object.keys(this.errors).length === 0;
                },
                
                // 提交表单
                async submitForm() {
                    if (!this.validate()) return;
                    
                    this.isSubmitting = true;
                    
                    try {
                        const response = await fetch('/AdminBm/EditApi', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]').value
                            },
                            body: JSON.stringify(this.formData)
                        });
                        
                        const result = await response.json();
                        
                        if (result.success) {
                            toastr.success('管理员信息更新成功');
                            setTimeout(() => {
                                window.location.href = '/AdminBm/Index';
                            }, 1500);
                        } else {
                            toastr.error(result.message || '保存失败，请重试');
                        }
                    } catch (error) {
                        console.error('保存出错:', error);
                        toastr.error('系统错误，请重试');
                    } finally {
                        this.isSubmitting = false;
                    }
                }
            }
        }).mount('#editApp');
    </script>
} 