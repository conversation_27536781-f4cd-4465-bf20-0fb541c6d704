using System.ComponentModel.DataAnnotations;

namespace BMS.Models.DTOs
{
    /// <summary>
    /// 管理员信息DTO
    /// </summary>
    public class AdminBmDto
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 用户名
        /// </summary>
        public string Username { get; set; } = string.Empty;

        /// <summary>
        /// 真实姓名
        /// </summary>
        public string? RealName { get; set; }

        /// <summary>
        /// 手机号
        /// </summary>
        public string? Phone { get; set; }

        /// <summary>
        /// 邮箱
        /// </summary>
        public string? Email { get; set; }

        /// <summary>
        /// 状态：0-禁用，1-启用
        /// </summary>
        public int Status { get; set; }

        /// <summary>
        /// 状态显示文本
        /// </summary>
        public string StatusText => Status == 1 ? "启用" : "禁用";

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateTime { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime UpdateTime { get; set; }

        /// <summary>
        /// 最后登录时间
        /// </summary>
        public DateTime? LastLoginTime { get; set; }
    }

    /// <summary>
    /// 删除管理员DTO
    /// </summary>
    public class AdminBmDeleteDto
    {
        /// <summary>
        /// 要删除的管理员ID
        /// </summary>
        [Required(ErrorMessage = "管理员ID不能为空")]
        [Range(1, int.MaxValue, ErrorMessage = "管理员ID必须大于0")]
        public int Id { get; set; }
    }

    /// <summary>
    /// 更改管理员状态DTO
    /// </summary>
    public class AdminBmChangeStatusDto
    {
        /// <summary>
        /// 管理员ID
        /// </summary>
        [Required(ErrorMessage = "管理员ID不能为空")]
        [Range(1, int.MaxValue, ErrorMessage = "管理员ID必须大于0")]
        public int Id { get; set; }

        /// <summary>
        /// 状态：0-禁用，1-启用
        /// </summary>
        [Required(ErrorMessage = "状态不能为空")]
        [Range(0, 1, ErrorMessage = "状态值只能为0或1")]
        public int Status { get; set; }
    }
} 