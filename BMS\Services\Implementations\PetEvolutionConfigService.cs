using BMS.Models.DTOs;
using BMS.Models.Common;
using BMS.Models.Entities;
using BMS.Services.Interfaces;
using SqlSugar;

namespace BMS.Services.Implementations
{
    /// <summary>
    /// 宠物进化配置服务实现
    /// </summary>
    public class PetEvolutionConfigService : IPetEvolutionConfigService
    {
        private readonly IDbService _dbService;

        public PetEvolutionConfigService(IDbService dbService)
        {
            _dbService = dbService;
        }

        /// <summary>
        /// 获取宠物进化配置列表（分页）
        /// </summary>
        public async Task<PagedResult<PetEvolutionConfigDetailDto>> GetPetEvolutionConfigsAsync(PetEvolutionConfigQueryDto queryDto)
        {
            var query = _dbService.Queryable<pet_evolution_config>()
                .LeftJoin<pet_config>((pec, pc) => pec.pet_no == pc.pet_no)
                .LeftJoin<pet_config>((pec, pc, tpc) => pec.target_pet_no == tpc.pet_no)
                .LeftJoin<item_config>((pec, pc, tpc, ic) => pec.required_item_id == ic.item_no.ToString())
                .WhereIF(queryDto.PetNo.HasValue, (pec, pc, tpc, ic) => pec.pet_no == queryDto.PetNo.Value)
                .WhereIF(!string.IsNullOrEmpty(queryDto.PetName), (pec, pc, tpc, ic) => pc.name.Contains(queryDto.PetName))
                .WhereIF(!string.IsNullOrEmpty(queryDto.EvolutionType), (pec, pc, tpc, ic) => pec.evolution_type == queryDto.EvolutionType)
                .WhereIF(queryDto.TargetPetNo.HasValue, (pec, pc, tpc, ic) => pec.target_pet_no == queryDto.TargetPetNo.Value)
                .WhereIF(!string.IsNullOrEmpty(queryDto.TargetPetName), (pec, pc, tpc, ic) => tpc.name.Contains(queryDto.TargetPetName))
                .WhereIF(queryDto.IsActive.HasValue, (pec, pc, tpc, ic) => pec.is_active == (queryDto.IsActive.Value ? 1 : 0))
                .WhereIF(queryDto.MinRequiredLevel.HasValue, (pec, pc, tpc, ic) => pec.required_level >= queryDto.MinRequiredLevel.Value)
                .WhereIF(queryDto.MaxRequiredLevel.HasValue, (pec, pc, tpc, ic) => pec.required_level <= queryDto.MaxRequiredLevel.Value);

            var totalCount = await query.CountAsync();

            var data = await query
                .OrderByDescending((pec, pc, tpc, ic) => pec.id)
                .Select((pec, pc, tpc, ic) => new PetEvolutionConfigDetailDto
                {
                    Id = pec.id,
                    PetNo = pec.pet_no,
                    PetName = pc.name ?? "",
                    PetAttribute = pc.attribute ?? "",
                    EvolutionType = pec.evolution_type,
                    TargetPetNo = pec.target_pet_no,
                    TargetPetName = tpc.name ?? "",
                    TargetPetAttribute = tpc.attribute ?? "",
                    RequiredLevel = pec.required_level,
                    RequiredItemId = pec.required_item_id,
                    RequiredItemName = ic.name ?? "",
                    RequiredItemCount = pec.required_item_count ?? 1,
                    CostGold = pec.cost_gold ?? 1000,
                    GrowthMin = pec.growth_min ?? 0.100m,
                    GrowthMax = pec.growth_max ?? 0.500m,
                    SuccessRate = pec.success_rate ?? 100.00m,
                    IsActive = pec.is_active == 1,
                    Description = pec.description ?? "",
                    CreateTime = pec.create_time ?? DateTime.Now
                })
                .Skip((queryDto.Page - 1) * queryDto.PageSize)
                .Take(queryDto.PageSize)
                .ToListAsync();

            return new PagedResult<PetEvolutionConfigDetailDto>(data, queryDto.Page, queryDto.PageSize, totalCount);
        }

        /// <summary>
        /// 根据ID获取宠物进化配置
        /// </summary>
        public async Task<ApiResult<PetEvolutionConfigDetailDto>> GetByIdAsync(int id)
        {
            var config = await _dbService.Queryable<pet_evolution_config>()
                .LeftJoin<pet_config>((pec, pc) => pec.pet_no == pc.pet_no)
                .LeftJoin<pet_config>((pec, pc, tpc) => pec.target_pet_no == tpc.pet_no)
                .LeftJoin<item_config>((pec, pc, tpc, ic) => pec.required_item_id == ic.item_no.ToString())
                .Where((pec, pc, tpc, ic) => pec.id == id)
                .Select((pec, pc, tpc, ic) => new PetEvolutionConfigDetailDto
                {
                    Id = pec.id,
                    PetNo = pec.pet_no,
                    PetName = pc.name ?? "",
                    PetAttribute = pc.attribute ?? "",
                    EvolutionType = pec.evolution_type,
                    TargetPetNo = pec.target_pet_no,
                    TargetPetName = tpc.name ?? "",
                    TargetPetAttribute = tpc.attribute ?? "",
                    RequiredLevel = pec.required_level,
                    RequiredItemId = pec.required_item_id,
                    RequiredItemName = ic.name ?? "",
                    RequiredItemCount = pec.required_item_count ?? 1,
                    CostGold = pec.cost_gold ?? 1000,
                    GrowthMin = pec.growth_min ?? 0.100m,
                    GrowthMax = pec.growth_max ?? 0.500m,
                    SuccessRate = pec.success_rate ?? 100.00m,
                    IsActive = pec.is_active == 1,
                    Description = pec.description ?? "",
                    CreateTime = pec.create_time ?? DateTime.Now
                })
                .FirstAsync();

            return config != null
                ? ApiResult<PetEvolutionConfigDetailDto>.Ok(config)
                : ApiResult<PetEvolutionConfigDetailDto>.Fail("进化配置不存在");
        }

        /// <summary>
        /// 创建宠物进化配置
        /// </summary>
        public async Task<ApiResult<int>> CreateAsync(PetEvolutionConfigCreateDto createDto)
        {
            // 检查宠物编号是否存在
            var petExists = await _dbService.Queryable<pet_config>()
                .Where(x => x.pet_no == createDto.PetNo)
                .AnyAsync();
            if (!petExists)
            {
                return ApiResult<int>.Fail("宠物编号不存在");
            }

            // 检查目标宠物编号是否存在
            var targetPetExists = await _dbService.Queryable<pet_config>()
                .Where(x => x.pet_no == createDto.TargetPetNo)
                .AnyAsync();
            if (!targetPetExists)
            {
                return ApiResult<int>.Fail("目标宠物编号不存在");
            }

            // 检查进化配置是否已存在
            var exists = await CheckEvolutionConfigExistsAsync(createDto.PetNo, createDto.EvolutionType);
            if (exists)
            {
                return ApiResult<int>.Fail($"宠物{createDto.PetNo}的{createDto.EvolutionType}路线进化配置已存在");
            }

            // 检查道具是否存在（如果指定了道具）
            if (!string.IsNullOrEmpty(createDto.RequiredItemId))
            {
                var itemExists = await _dbService.Queryable<item_config>()
                    .Where(x => x.item_no.ToString() == createDto.RequiredItemId)
                    .AnyAsync();
                if (!itemExists)
                {
                    return ApiResult<int>.Fail("所需道具不存在");
                }
            }

            // 验证成长值范围
            if (createDto.GrowthMin > createDto.GrowthMax)
            {
                return ApiResult<int>.Fail("最小成长加成不能大于最大成长加成");
            }

            var entity = new pet_evolution_config
            {
                pet_no = createDto.PetNo,
                evolution_type = createDto.EvolutionType,
                target_pet_no = createDto.TargetPetNo,
                required_level = createDto.RequiredLevel,
                required_item_id = createDto.RequiredItemId,
                required_item_count = createDto.RequiredItemCount,
                cost_gold = createDto.CostGold,
                growth_min = createDto.GrowthMin,
                growth_max = createDto.GrowthMax,
                success_rate = createDto.SuccessRate,
                is_active = createDto.IsActive ? 1 : 0,
                description = createDto.Description,
                create_time = DateTime.Now
            };

            var result = await _dbService.Insertable(entity).ExecuteReturnIdentityAsync();
            return result > 0 
                ? ApiResult<int>.Ok(result, "创建进化配置成功")
                : ApiResult<int>.Fail("创建进化配置失败");
        }

        /// <summary>
        /// 更新宠物进化配置
        /// </summary>
        public async Task<ApiResult<bool>> UpdateAsync(PetEvolutionConfigUpdateDto updateDto)
        {
            // 检查配置是否存在
            var exists = await _dbService.Queryable<pet_evolution_config>()
                .Where(x => x.id == updateDto.Id)
                .AnyAsync();
            if (!exists)
            {
                return ApiResult<bool>.Fail("进化配置不存在");
            }

            // 检查宠物编号是否存在
            var petExists = await _dbService.Queryable<pet_config>()
                .Where(x => x.pet_no == updateDto.PetNo)
                .AnyAsync();
            if (!petExists)
            {
                return ApiResult<bool>.Fail("宠物编号不存在");
            }

            // 检查目标宠物编号是否存在
            var targetPetExists = await _dbService.Queryable<pet_config>()
                .Where(x => x.pet_no == updateDto.TargetPetNo)
                .AnyAsync();
            if (!targetPetExists)
            {
                return ApiResult<bool>.Fail("目标宠物编号不存在");
            }

            // 检查进化配置是否已存在（排除当前记录）
            var duplicateExists = await CheckEvolutionConfigExistsAsync(updateDto.PetNo, updateDto.EvolutionType, updateDto.Id);
            if (duplicateExists)
            {
                return ApiResult<bool>.Fail($"宠物{updateDto.PetNo}的{updateDto.EvolutionType}路线进化配置已存在");
            }

            // 检查道具是否存在（如果指定了道具）
            if (!string.IsNullOrEmpty(updateDto.RequiredItemId))
            {
                var itemExists = await _dbService.Queryable<item_config>()
                    .Where(x => x.item_no.ToString() == updateDto.RequiredItemId)
                    .AnyAsync();
                if (!itemExists)
                {
                    return ApiResult<bool>.Fail("所需道具不存在");
                }
            }

            // 验证成长值范围
            if (updateDto.GrowthMin > updateDto.GrowthMax)
            {
                return ApiResult<bool>.Fail("最小成长加成不能大于最大成长加成");
            }

            var result = await _dbService.Updateable<pet_evolution_config>()
                .SetColumns(x => new pet_evolution_config
                {
                    pet_no = updateDto.PetNo,
                    evolution_type = updateDto.EvolutionType,
                    target_pet_no = updateDto.TargetPetNo,
                    required_level = updateDto.RequiredLevel,
                    required_item_id = updateDto.RequiredItemId,
                    required_item_count = updateDto.RequiredItemCount,
                    cost_gold = updateDto.CostGold,
                    growth_min = updateDto.GrowthMin,
                    growth_max = updateDto.GrowthMax,
                    success_rate = updateDto.SuccessRate,
                    is_active = updateDto.IsActive ? 1 : 0,
                    description = updateDto.Description
                })
                .Where(x => x.id == updateDto.Id)
                .ExecuteCommandAsync();

            return result > 0 
                ? ApiResult<bool>.Ok(true, "更新进化配置成功")
                : ApiResult<bool>.Fail("更新进化配置失败");
        }

        /// <summary>
        /// 删除宠物进化配置
        /// </summary>
        public async Task<ApiResult<bool>> DeleteAsync(int id)
        {
            var result = await _dbService.Deleteable<pet_evolution_config>()
                .Where(x => x.id == id)
                .ExecuteCommandAsync();

            return result > 0 
                ? ApiResult<bool>.Ok(true, "删除进化配置成功")
                : ApiResult<bool>.Fail("删除进化配置失败");
        }

        /// <summary>
        /// 批量删除宠物进化配置
        /// </summary>
        public async Task<ApiResult<bool>> BatchDeleteAsync(List<int> ids)
        {
            if (ids == null || ids.Count == 0)
            {
                return ApiResult<bool>.Fail("请选择要删除的配置");
            }

            var result = await _dbService.Deleteable<pet_evolution_config>()
                .Where(x => ids.Contains(x.id))
                .ExecuteCommandAsync();

            return result > 0 
                ? ApiResult<bool>.Ok(true, $"成功删除{result}个进化配置")
                : ApiResult<bool>.Fail("删除进化配置失败");
        }

        /// <summary>
        /// 切换激活状态
        /// </summary>
        public async Task<ApiResult<bool>> ToggleActiveAsync(int id, bool isActive)
        {
            var result = await _dbService.Updateable<pet_evolution_config>()
                .SetColumns(x => new pet_evolution_config { is_active = isActive ? 1 : 0 })
                .Where(x => x.id == id)
                .ExecuteCommandAsync();

            return result > 0
                ? ApiResult<bool>.Ok(true, $"已{(isActive ? "激活" : "禁用")}进化配置")
                : ApiResult<bool>.Fail("操作失败");
        }

        /// <summary>
        /// 检查进化配置是否存在
        /// </summary>
        public async Task<bool> CheckEvolutionConfigExistsAsync(int petNo, string evolutionType, int? excludeId = null)
        {
            var query = _dbService.Queryable<pet_evolution_config>()
                .Where(x => x.pet_no == petNo && x.evolution_type == evolutionType);

            if (excludeId.HasValue)
            {
                query = query.Where(x => x.id != excludeId.Value);
            }

            return await query.AnyAsync();
        }

        /// <summary>
        /// 获取宠物配置选项
        /// </summary>
        public async Task<List<PetConfigOptionDto>> GetPetConfigOptionsAsync()
        {
            return await _dbService.Queryable<pet_config>()
                .OrderBy(x => x.pet_no)
                .Select(x => new PetConfigOptionDto
                {
                    PetNo = x.pet_no,
                    Name = x.name ?? "",
                    Attribute = x.attribute ?? ""
                })
                .ToListAsync();
        }

        /// <summary>
        /// 获取道具配置选项
        /// </summary>
        public async Task<List<ItemConfigOptionDto>> GetItemConfigOptionsAsync()
        {
            return await _dbService.Queryable<item_config>()
                .OrderBy(x => x.item_no)
                .Select(x => new ItemConfigOptionDto
                {
                    ItemId = x.item_no.ToString(),
                    Name = x.name ?? "",
                    Type = x.type ?? ""
                })
                .ToListAsync();
        }

        /// <summary>
        /// 根据宠物编号获取进化配置
        /// </summary>
        public async Task<List<PetEvolutionConfigDetailDto>> GetEvolutionConfigsByPetNoAsync(int petNo)
        {
            return await _dbService.Queryable<pet_evolution_config>()
                .LeftJoin<pet_config>((pec, pc) => pec.pet_no == pc.pet_no)
                .LeftJoin<pet_config>((pec, pc, tpc) => pec.target_pet_no == tpc.pet_no)
                .LeftJoin<item_config>((pec, pc, tpc, ic) => pec.required_item_id == ic.item_no.ToString())
                .Where((pec, pc, tpc, ic) => pec.pet_no == petNo && pec.is_active == 1)
                .Select((pec, pc, tpc, ic) => new PetEvolutionConfigDetailDto
                {
                    Id = pec.id,
                    PetNo = pec.pet_no,
                    PetName = pc.name ?? "",
                    PetAttribute = pc.attribute ?? "",
                    EvolutionType = pec.evolution_type,
                    TargetPetNo = pec.target_pet_no,
                    TargetPetName = tpc.name ?? "",
                    TargetPetAttribute = tpc.attribute ?? "",
                    RequiredLevel = pec.required_level,
                    RequiredItemId = pec.required_item_id,
                    RequiredItemName = ic.name ?? "",
                    RequiredItemCount = pec.required_item_count ?? 1,
                    CostGold = pec.cost_gold ?? 1000,
                    GrowthMin = pec.growth_min ?? 0.100m,
                    GrowthMax = pec.growth_max ?? 0.500m,
                    SuccessRate = pec.success_rate ?? 100.00m,
                    IsActive = pec.is_active == 1,
                    Description = pec.description ?? "",
                    CreateTime = pec.create_time ?? DateTime.Now
                })
                .ToListAsync();
        }

        /// <summary>
        /// 验证进化条件
        /// </summary>
        public async Task<ApiResult<bool>> ValidateEvolutionConditionsAsync(int userPetId, string evolutionType)
        {
            // 获取用户宠物信息
            var userPet = await _dbService.Queryable<user_pet>()
                .Where(x => x.id == userPetId)
                .FirstAsync();

            if (userPet == null)
            {
                return ApiResult<bool>.Fail("用户宠物不存在");
            }

            // 获取进化配置
            var evolutionConfig = await _dbService.Queryable<pet_evolution_config>()
                .Where(x => x.pet_no == userPet.pet_no && x.evolution_type == evolutionType && x.is_active == 1)
                .FirstAsync();

            if (evolutionConfig == null)
            {
                return ApiResult<bool>.Fail("进化配置不存在");
            }

            // 检查等级要求
            var currentLevel = CalculateLevel(userPet.exp ?? 0);
            if (currentLevel < evolutionConfig.required_level)
            {
                return ApiResult<bool>.Fail($"宠物等级不足，需要{evolutionConfig.required_level}级，当前{currentLevel}级");
            }

            // 检查道具要求（如果有）
            if (!string.IsNullOrEmpty(evolutionConfig.required_item_id))
            {
                var userItem = await _dbService.Queryable<user_item>()
                    .Where(x => x.user_id == userPet.user_id && x.item_id == evolutionConfig.required_item_id)
                    .FirstAsync();

                if (userItem == null || userItem.item_count < evolutionConfig.required_item_count)
                {
                    return ApiResult<bool>.Fail($"所需道具不足，需要{evolutionConfig.required_item_count}个");
                }
            }

            // 检查金币要求
            var user = await _dbService.Queryable<user>()
                .Where(x => x.id == userPet.user_id)
                .FirstAsync();

            if (user == null || user.gold < evolutionConfig.cost_gold)
            {
                return ApiResult<bool>.Fail($"金币不足，需要{evolutionConfig.cost_gold}金币");
            }

            return ApiResult<bool>.Ok(true, "满足进化条件");
        }

        /// <summary>
        /// 计算等级
        /// </summary>
        private int CalculateLevel(long exp)
        {
            // 简单的等级计算公式，可以根据实际需求调整
            return (int)Math.Floor(Math.Sqrt(exp / 100.0)) + 1;
        }
    }
}
