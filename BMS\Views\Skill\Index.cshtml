@{
    ViewData["Title"] = "技能管理";
    Layout = "_Layout";
}

<!-- 科幻背景 -->
<div class="cyber-bg"></div>

<!-- 科幻技能管理应用容器 -->
<div id="skillApp">
    <!-- 科幻页面标题 -->
    <div class="container-fluid py-4">
        <div class="row mb-4">
            <div class="col-12">
                <div class="cyber-card">
                    <div class="cyber-card-header">
                        <div class="cyber-icon">🔮</div>
                        <h1 class="cyber-card-title">技能管理系统</h1>
                        <div class="ms-auto">
                            <button type="button" class="cyber-btn cyber-btn-success" v-on:click="showCreateModal">
                                <i class="fas fa-plus"></i> 新增技能
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 科幻主要内容 -->
    <div class="container-fluid">
        <!-- 科幻搜索条件 -->
        <div class="cyber-card">
            <div class="cyber-card-header">
                <div class="cyber-icon">🔍</div>
                <h3 class="cyber-card-title">搜索条件</h3>
            </div>
            <div class="cyber-card-body">

                <div class="row">
                    <div class="col-md-3">
                        <div class="cyber-form-group">
                            <label class="cyber-form-label">技能ID</label>
                            <input type="text" class="cyber-form-control" v-model="queryForm.skillId" placeholder="请输入技能ID">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="cyber-form-group">
                            <label class="cyber-form-label">技能名称</label>
                            <input type="text" class="cyber-form-control" v-model="queryForm.skillName" placeholder="请输入技能名称">
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="cyber-form-group">
                            <label class="cyber-form-label">效果类型</label>
                            <div class="cyber-select-wrapper">
                                <select class="cyber-form-control" v-model="queryForm.effectType">
                                    <option value="">全部</option>
                                    <option v-for="type in effectTypes" :key="type" :value="type">{{ type }}</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="cyber-form-group">
                            <label class="cyber-form-label">五行限制</label>
                            <div class="cyber-select-wrapper">
                                <select class="cyber-form-control" v-model="queryForm.elementLimit">
                                    <option value="">全部</option>
                                    <option v-for="element in elementLimits" :key="element" :value="element">{{ element }}</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="cyber-form-group">
                            <label class="cyber-form-label">&nbsp;</label>
                            <div class="d-flex gap-2 flex-wrap">
                                <button type="button" class="cyber-btn cyber-btn-primary" v-on:click="searchSkills">
                                    <i class="fas fa-search"></i> 搜索
                                </button>
                                <button type="button" class="cyber-btn cyber-btn-outline" v-on:click="resetSearch">
                                    <i class="fas fa-redo"></i> 重置
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 科幻技能列表 -->
        <div class="cyber-card">
            <div class="cyber-card-header">
                <div class="cyber-icon">🔮</div>
                <h3 class="cyber-card-title">技能列表</h3>
            </div>
            <div class="cyber-card-body">
                <div v-if="loading" class="text-center py-4">
                    <i class="fas fa-spinner fa-spin me-2" style="font-size: 1.5rem; color: var(--cyber-blue);"></i>
                    <span>数据加载中...</span>
                </div>
                <div v-else>
                    <div class="cyber-table-container">
                        <table class="cyber-table">
                            <thead class="cyber-thead">
                                <tr>
                                    <th class="cyber-th">技能ID</th>
                                    <th class="cyber-th">技能名称</th>
                                    <th class="cyber-th">技能百分比</th>
                                    <th class="cyber-th">效果类型</th>
                                    <th class="cyber-th">耗蓝量</th>
                                    <th class="cyber-th">五行限制</th>
                                    <th class="cyber-th">创建时间</th>
                                    <th class="cyber-th">操作</th>
                                </tr>
                            </thead>
                            <tbody class="cyber-tbody" v-if="skills.length === 0">
                                <tr class="cyber-tr">
                                    <td colspan="8" class="cyber-td text-center py-4 text-muted">
                                        <i class="fas fa-inbox me-2"></i>暂无技能数据
                                    </td>
                                </tr>
                            </tbody>
                            <tbody class="cyber-tbody" v-else>
                                <tr class="cyber-tr" v-for="skill in skills" :key="skill.skillId">
                                    <td class="cyber-td">{{ skill.skillId }}</td>
                                    <td class="cyber-td">
                                        <i class="fas fa-magic me-1" style="color: var(--cyber-blue);"></i>
                                        {{ skill.skillName }}
                                    </td>
                                    <td class="cyber-td">
                                        <span class="badge badge-info">{{ skill.skillPercent }}%</span>
                                    </td>
                                    <td class="cyber-td">{{ skill.effectType || '-' }}</td>
                                    <td class="cyber-td">{{ skill.manaCost }}</td>
                                    <td class="cyber-td">
                                        <span v-if="skill.elementLimit" class="badge badge-warning">{{ skill.elementLimit }}</span>
                                        <span v-else class="text-muted">-</span>
                                    </td>
                                    <td class="cyber-td">{{ formatDate(skill.createTime) }}</td>
                                    <td class="cyber-td">
                                        <div class="d-flex gap-1 flex-wrap">
                                            <button type="button" class="cyber-btn cyber-btn-sm"
                                                    v-on:click="showDetailModal(skill.skillId)" title="查看详情"
                                                    style="background: linear-gradient(135deg, var(--cyber-blue), var(--cyber-purple));">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button type="button" class="cyber-btn cyber-btn-sm cyber-btn-warning"
                                                    v-on:click="showEditModal(skill.skillId)" title="编辑技能">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button type="button" class="cyber-btn cyber-btn-sm cyber-btn-danger"
                                                    v-on:click="deleteSkill(skill.skillId)" title="删除技能">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <!-- 科幻分页 -->
                    <div class="mt-3" v-if="totalCount > 0">
                        <div class="row align-items-center">
                            <div class="col-sm-6">
                                <div class="text-muted">
                                    共 {{ totalCount }} 条记录，第 {{ currentPage }} / {{ totalPages }} 页
                                </div>
                            </div>
                            <div class="col-sm-6">
                                <ul class="cyber-pagination justify-content-end">
                                    <li class="page-item" :class="{ disabled: currentPage <= 1 }">
                                        <a class="page-link" href="#" v-on:click.prevent="changePage(currentPage - 1)">上一页</a>
                                    </li>
                                    <li class="page-item" v-for="page in visiblePages" :key="page" :class="{ active: page === currentPage }">
                                        <a class="page-link" href="#" v-on:click.prevent="changePage(page)" v-text="page"></a>
                                    </li>
                                    <li class="page-item" :class="{ disabled: currentPage >= totalPages }">
                                        <a class="page-link" href="#" v-on:click.prevent="changePage(currentPage + 1)">下一页</a>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 科幻新增/编辑技能模态框 -->
        <div class="modal fade cyber-modal" id="skillModal" tabindex="-1" aria-hidden="true">
            <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title">
                        <i class="fas fa-magic me-2"></i>{{ isEdit ? '编辑技能' : '新增技能' }}
                    </h4>
                    <button type="button" class="btn-close" v-on:click="closeModal" aria-label="关闭"></button>
                </div>
                <div class="modal-body">
                    <form>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="cyber-form-group">
                                    <label class="cyber-form-label">技能ID <span class="text-danger">*</span></label>
                                    <input type="text" class="cyber-form-control" v-model="skillForm.skillId"
                                           placeholder="请输入技能ID" :disabled="isEdit">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="cyber-form-group">
                                    <label class="cyber-form-label">技能名称 <span class="text-danger">*</span></label>
                                    <input type="text" class="cyber-form-control" v-model="skillForm.skillName" placeholder="请输入技能名称">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="cyber-form-group">
                                    <label class="cyber-form-label">技能百分比 <span class="text-danger">*</span></label>
                                    <input type="number" step="0.01" class="cyber-form-control" v-model="skillForm.skillPercent" placeholder="请输入技能百分比">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="cyber-form-group">
                                    <label class="cyber-form-label">耗蓝量</label>
                                    <input type="number" class="cyber-form-control" v-model="skillForm.manaCost" placeholder="请输入耗蓝量">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="cyber-form-group">
                                    <label class="cyber-form-label">效果类型</label>
                                    <input type="text" class="cyber-form-control" v-model="skillForm.effectType"
                                           placeholder="例如：攻击,命中,防御（用逗号分隔多个效果）">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="cyber-form-group">
                                    <label class="cyber-form-label">五行限制</label>
                                    <input type="text" class="cyber-form-control" v-model="skillForm.elementLimit"
                                           placeholder="例如：金,木,水（用逗号分隔多个五行）">
                                </div>
                            </div>
                        </div>
                        <div class="cyber-form-group">
                            <label class="cyber-form-label">效果数值（JSON格式）</label>
                            <textarea class="cyber-form-control" rows="3" v-model="skillForm.effectValue"
                                      placeholder='例如：{"攻击": 100, "防御": 50}'></textarea>
                        </div>
                        <div class="cyber-form-group">
                            <label class="cyber-form-label">BUFF信息（JSON格式）</label>
                            <textarea class="cyber-form-control" rows="3" v-model="skillForm.buffInfo"
                                      placeholder='例如：{"类型": "增益", "持续回合": 3, "数值": 20}'></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="cyber-btn cyber-btn-outline" v-on:click="closeModal">
                        <i class="fas fa-times me-1"></i>关闭
                    </button>
                    <button type="button" class="cyber-btn cyber-btn-success" v-on:click="saveSkill" :disabled="saving">
                        <span v-if="saving" class="spinner-border spinner-border-sm me-1"></span>
                        <i v-if="!saving" class="fas fa-save me-1"></i>
                        {{ saving ? '保存中...' : '保存' }}
                    </button>
                </div>
            </div>
        </div>

        <!-- 科幻详情模态框 -->
        <div class="modal fade cyber-modal" id="detailModal" tabindex="-1" aria-hidden="true">
            <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title">
                        <i class="fas fa-info-circle me-2"></i>技能详情
                    </h4>
                    <button type="button" class="btn-close" v-on:click="closeModal" aria-label="关闭"></button>
                </div>
                <div class="modal-body">
                    <div v-if="currentSkill">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <h6 class="cyber-section-title">
                                    <i class="fas fa-cog me-2"></i>基本信息
                                </h6>
                                <div class="cyber-detail-item">
                                    <strong>技能ID：</strong>
                                    <span>{{ currentSkill.skillId }}</span>
                                </div>
                                <div class="cyber-detail-item">
                                    <strong>技能名称：</strong>
                                    <span>{{ currentSkill.skillName }}</span>
                                </div>
                                <div class="cyber-detail-item">
                                    <strong>技能百分比：</strong>
                                    <span class="badge badge-info">{{ currentSkill.skillPercent }}%</span>
                                </div>
                                <div class="cyber-detail-item">
                                    <strong>效果类型：</strong>
                                    <span>{{ currentSkill.effectType || '-' }}</span>
                                </div>
                                <div class="cyber-detail-item">
                                    <strong>耗蓝量：</strong>
                                    <span>{{ currentSkill.manaCost }}</span>
                                </div>
                                <div class="cyber-detail-item">
                                    <strong>五行限制：</strong>
                                    <span v-if="currentSkill.elementLimit" class="badge badge-warning">{{ currentSkill.elementLimit }}</span>
                                    <span v-else class="text-muted">-</span>
                                </div>
                                <div class="cyber-detail-item">
                                    <strong>创建时间：</strong>
                                    <span>{{ formatDate(currentSkill.createTime) }}</span>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <h6 class="cyber-section-title">
                                    <i class="fas fa-chart-bar me-2"></i>技能数据
                                </h6>
                                <div class="cyber-detail-item">
                                    <strong>效果数值：</strong>
                                    <div v-if="currentSkill.effectValue" class="cyber-json-display">
                                        <pre>{{ formatJson(currentSkill.effectValue) }}</pre>
                                    </div>
                                    <span v-else class="text-muted">-</span>
                                </div>
                                <div class="cyber-detail-item">
                                    <strong>BUFF信息：</strong>
                                    <div v-if="currentSkill.buffInfo" class="cyber-json-display">
                                        <pre>{{ formatJson(currentSkill.buffInfo) }}</pre>
                                    </div>
                                    <span v-else class="text-muted">-</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="cyber-btn cyber-btn-outline" v-on:click="closeModal">
                        <i class="fas fa-times me-1"></i>关闭
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Vue 3.x CDN -->
<script src="~/lib/vue/vue.global.js"></script>
<script src="~/lib/axios.min.js"></script>
<script>
    // 确保DOM加载完成后再初始化Vue
    document.addEventListener('DOMContentLoaded', function() {
        const { createApp, ref, computed, onMounted } = Vue;

        const skillApp = createApp({
            setup() {
                // 响应式数据
                const loading = ref(false);
                const saving = ref(false);
                const skills = ref([]);
                const effectTypes = ref(@Html.Raw(Json.Serialize(ViewBag.EffectTypes ?? new List<string>())));
                const elementLimits = ref(@Html.Raw(Json.Serialize(ViewBag.ElementLimits ?? new List<string>())));
                const totalCount = ref(0);
                const currentPage = ref(1);
                const pageSize = ref(10);
                
                const queryForm = ref({
                    skillId: '',
                    skillName: '',
                    effectType: '',
                    elementLimit: '',
                    page: 1,
                    pageSize: 10
                });
                
                const skillForm = ref({
                    skillId: '',
                    skillName: '',
                    skillPercent: 0,
                    effectType: '',
                    effectValue: '',
                    manaCost: 0,
                    buffInfo: '',
                    elementLimit: ''
                });
                
                const currentSkill = ref(null);
                const isEdit = ref(false);

                // 计算属性
                const totalPages = computed(() => {
                    return Math.ceil(totalCount.value / pageSize.value);
                });

                const visiblePages = computed(() => {
                    const start = Math.max(1, currentPage.value - 2);
                    const end = Math.min(totalPages.value, currentPage.value + 2);
                    const pages = [];
                    for (let i = start; i <= end; i++) {
                        pages.push(i);
                    }
                    return pages;
                });

                // 方法
                const loadSkills = async () => {
                    loading.value = true;
                    try {
                        const response = await axios.post('/Skill/GetList', {
                            ...queryForm.value,
                            page: currentPage.value,
                            pageSize: pageSize.value
                        });

                        console.log('API Response:', response.data);

                        if (response.data.code === 200) {
                            skills.value = response.data.data || [];
                            totalCount.value = response.data.total || 0;
                        } else {
                            showMessage('加载失败：' + (response.data.message || '未知错误'), 'error');
                        }
                    } catch (error) {
                        console.error('Load skills error:', error);
                        showMessage('加载失败：' + error.message, 'error');
                    } finally {
                        loading.value = false;
                    }
                };

                const searchSkills = () => {
                    currentPage.value = 1;
                    loadSkills();
                };

                const resetSearch = () => {
                    queryForm.value = {
                        skillId: '',
                        skillName: '',
                        effectType: '',
                        elementLimit: '',
                        page: 1,
                        pageSize: 10
                    };
                    currentPage.value = 1;
                    loadSkills();
                };

                const changePage = (page) => {
                    if (page >= 1 && page <= totalPages.value) {
                        currentPage.value = page;
                        loadSkills();
                    }
                };

                const showCreateModal = () => {
                    isEdit.value = false;
                    skillForm.value = {
                        skillId: '',
                        skillName: '',
                        skillPercent: 0,
                        effectType: '',
                        effectValue: '',
                        manaCost: 0,
                        buffInfo: '',
                        elementLimit: ''
                    };
                    $('#skillModal').modal('show');
                };

                const showEditModal = async (skillId) => {
                    try {
                        const response = await axios.get(`/Skill/GetById?skillId=${skillId}`);
                        console.log('GetById Response:', response.data);
                        
                        if (response.data.code === 200) {
                            isEdit.value = true;
                            const skill = response.data.data;
                            skillForm.value = {
                                skillId: skill.skillId,
                                skillName: skill.skillName,
                                skillPercent: skill.skillPercent,
                                effectType: skill.effectType || '',
                                effectValue: skill.effectValue || '',
                                manaCost: skill.manaCost,
                                buffInfo: skill.buffInfo || '',
                                elementLimit: skill.elementLimit || ''
                            };
                            $('#skillModal').modal('show');
                        } else {
                            showMessage('加载失败：' + (response.data.message || '未知错误'), 'error');
                        }
                    } catch (error) {
                        console.error('Load skill error:', error);
                        showMessage('加载失败：' + error.message, 'error');
                    }
                };

                const showDetailModal = async (skillId) => {
                    try {
                        const response = await axios.get(`/Skill/GetById?skillId=${skillId}`);
                        if (response.data.code === 200) {
                            currentSkill.value = response.data.data;
                            $('#detailModal').modal('show');
                        } else {
                            showMessage('加载失败：' + (response.data.message || '未知错误'), 'error');
                        }
                    } catch (error) {
                        console.error('Load skill error:', error);
                        showMessage('加载失败：' + error.message, 'error');
                    }
                };

                const saveSkill = async () => {
                    if (!skillForm.value.skillId || !skillForm.value.skillName) {
                        showMessage('请填写必填项', 'warning');
                        return;
                    }

                    saving.value = true;
                    try {
                        const url = isEdit.value ? '/Skill/Update' : '/Skill/Create';
                        const response = await axios.post(url, skillForm.value);

                        console.log('Save Response:', response.data);

                        if (response.data.code === 200) {
                            showMessage(response.data.message || '操作成功', 'success');
                            $('#skillModal').modal('hide');
                            loadSkills();
                        } else {
                            showMessage('保存失败：' + (response.data.message || '未知错误'), 'error');
                        }
                    } catch (error) {
                        console.error('Save skill error:', error);
                        showMessage('保存失败：' + error.message, 'error');
                    } finally {
                        saving.value = false;
                    }
                };

                const deleteSkill = async (skillId) => {
                    if (!confirm('确定要删除这个技能吗？')) {
                        return;
                    }

                    try {
                        const response = await axios.post('/Skill/Delete', { skillId });

                        console.log('Delete Response:', response.data);

                        if (response.data.code === 200) {
                            showMessage(response.data.message || '删除成功', 'success');
                            loadSkills();
                        } else {
                            showMessage('删除失败：' + (response.data.message || '未知错误'), 'error');
                        }
                    } catch (error) {
                        console.error('Delete skill error:', error);
                        showMessage('删除失败：' + error.message, 'error');
                    }
                };

                const formatDate = (dateString) => {
                    if (!dateString) return '-';
                    const date = new Date(dateString);
                    return date.toLocaleString('zh-CN');
                };

                const formatJson = (jsonString) => {
                    if (!jsonString) return '';
                    try {
                        const obj = JSON.parse(jsonString);
                        return JSON.stringify(obj, null, 2);
                    } catch (e) {
                        return jsonString;
                    }
                };

                const closeModal = () => {
                    $('#skillModal').modal('hide');
                    $('#detailModal').modal('hide');
                };

                const showMessage = (message, type) => {
                    const alertClass = type === 'success' ? 'alert-success' : 
                                     type === 'warning' ? 'alert-warning' : 'alert-danger';
                    
                    const alertHtml = `
                        <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
                            ${message}
                            <button type="button" class="close" data-dismiss="alert">
                                <span>&times;</span>
                            </button>
                        </div>
                    `;
                    
                    $('body').prepend(alertHtml);
                    setTimeout(() => {
                        $('.alert').alert('close');
                    }, 3000);
                };

                // 组件挂载时执行
                onMounted(() => {
                    console.log('Vue 3 Skill App Mounted');
                    loadSkills();
                });

                // 返回所有需要在模板中使用的数据和方法
                return {
                    loading,
                    saving,
                    skills,
                    effectTypes,
                    elementLimits,
                    totalCount,
                    currentPage,
                    pageSize,
                    queryForm,
                    skillForm,
                    currentSkill,
                    isEdit,
                    totalPages,
                    visiblePages,
                    loadSkills,
                    searchSkills,
                    resetSearch,
                    changePage,
                    showCreateModal,
                    showEditModal,
                    showDetailModal,
                    saveSkill,
                    deleteSkill,
                    formatDate,
                    formatJson,
                    closeModal,
                    showMessage
                };
            }
        });

        skillApp.mount('#skillApp');
    });
</script> 