[2025-09-09 00:13:00.257 +08:00 INF] BMS: 🚀 应用程序启动，日志系统已配置
[2025-09-09 00:13:00.291 +08:00 DBG] BMS: 🔧 调试级别日志测试
[2025-09-09 00:13:00.313 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-09 00:13:00.316 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-09 00:13:00.418 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-09 00:13:01.111 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-09 00:13:01.125 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-09 00:13:01.127 +08:00 INF] BMS: ✅ DbContext 初始化成功，日志文件位置: logs/
[2025-09-09 00:13:03.072 +08:00 WRN] Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware: Failed to determine the https port for redirect.
[2025-09-09 00:13:10.397 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-09 00:13:10.407 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-09 00:13:10.412 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-09 00:13:10.467 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-09 00:13:10.618 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-09 00:13:10.712 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-09 00:13:10.714 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-09 00:13:10.716 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-09 00:13:10.768 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-09 00:13:10.775 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-09 00:13:10.807 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-09 00:13:10.818 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-09 00:13:10.820 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-09 00:13:10.875 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-09 00:13:10.880 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-09 00:13:10.929 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `task_id` AS `Value` , `task_name` AS `Label`  FROM `task_config`  WHERE ( `is_active` = @is_active0 )ORDER BY `sort_order` ASC  | 参数: [@is_active0=1]
[2025-09-09 00:13:10.995 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `task_id` AS `Value` , `task_name` AS `Label`  FROM `task_config`  WHERE ( `is_active` = @is_active0 )ORDER BY `sort_order` ASC 
[2025-09-09 00:13:11.048 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-09 00:13:11.049 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-09 00:13:11.051 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-09 00:13:11.102 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-09 00:13:11.112 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-09 00:13:11.126 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `id` AS `Id` , `item_no` AS `ItemNo` , `name` AS `Name` , `type` AS `Type` , `description` AS `Description` , `quality` AS `Quality` , `icon` AS `Icon` , `price` AS `Price` , `use_limit` AS `UseLimit` , `create_time` AS `CreateTime`  FROM `item_config` ORDER BY `item_no` ASC 
[2025-09-09 00:13:11.183 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `id` AS `Id` , `item_no` AS `ItemNo` , `name` AS `Name` , `type` AS `Type` , `description` AS `Description` , `quality` AS `Quality` , `icon` AS `Icon` , `price` AS `Price` , `use_limit` AS `UseLimit` , `create_time` AS `CreateTime`  FROM `item_config` ORDER BY `item_no` ASC 
[2025-09-09 00:13:11.272 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-09 00:13:11.274 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-09 00:13:11.276 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-09 00:13:11.329 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-09 00:13:11.331 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-09 00:13:11.336 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`monster_no`,`skill`,`name`,`attribute` FROM `monster_config` ORDER BY `monster_no` ASC 
[2025-09-09 00:13:11.389 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`monster_no`,`skill`,`name`,`attribute` FROM `monster_config` ORDER BY `monster_no` ASC 
[2025-09-09 00:13:11.414 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-09 00:13:11.415 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-09 00:13:11.418 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-09 00:13:11.471 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-09 00:13:11.475 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-09 00:13:11.520 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM `task_config`  
[2025-09-09 00:13:11.575 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM `task_config`  
[2025-09-09 00:13:11.579 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`    ORDER BY `sort_order` ASC,`created_at` DESC LIMIT 0,10
[2025-09-09 00:13:11.633 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`    ORDER BY `sort_order` ASC,`created_at` DESC LIMIT 0,10
[2025-09-09 00:13:11.646 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_name`,`target_amount`,`objective_order`,`objective_description`,`complete_template`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_001]
[2025-09-09 00:13:11.974 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_name`,`target_amount`,`objective_order`,`objective_description`,`complete_template`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-09-09 00:13:12.465 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_name`,`target_amount`,`objective_order`,`objective_description`,`complete_template`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_002]
[2025-09-09 00:13:12.567 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_name`,`target_amount`,`objective_order`,`objective_description`,`complete_template`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-09-09 00:13:12.569 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_name`,`target_amount`,`objective_order`,`objective_description`,`complete_template`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_003]
[2025-09-09 00:13:12.620 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_name`,`target_amount`,`objective_order`,`objective_description`,`complete_template`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-09-09 00:13:12.625 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_name`,`target_amount`,`objective_order`,`objective_description`,`complete_template`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_004]
[2025-09-09 00:13:12.677 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_name`,`target_amount`,`objective_order`,`objective_description`,`complete_template`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-09-09 00:13:12.682 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_name`,`target_amount`,`objective_order`,`objective_description`,`complete_template`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_005]
[2025-09-09 00:13:12.733 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_name`,`target_amount`,`objective_order`,`objective_description`,`complete_template`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-09-09 00:13:12.736 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_name`,`target_amount`,`objective_order`,`objective_description`,`complete_template`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_010]
[2025-09-09 00:13:12.787 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_name`,`target_amount`,`objective_order`,`objective_description`,`complete_template`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-09-09 00:13:15.339 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-09 00:13:15.355 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-09 00:13:15.369 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-09 00:13:15.426 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-09 00:13:15.429 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-09 00:13:15.439 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`   WHERE ( `task_id` = @task_id0 )   LIMIT 0,1 | 参数: [@task_id0=TASK_002]
[2025-09-09 00:13:15.628 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`   WHERE ( `task_id` = @task_id0 )   LIMIT 0,1
[2025-09-09 00:13:15.634 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_name`,`target_amount`,`objective_order`,`objective_description`,`complete_template`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_002]
[2025-09-09 00:13:15.703 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_name`,`target_amount`,`objective_order`,`objective_description`,`complete_template`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-09-09 00:13:26.788 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-09 00:13:26.932 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-09 00:13:26.950 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-09 00:13:27.010 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-09 00:13:27.013 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-09 00:13:27.015 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`   WHERE ( `task_id` = @task_id0 )   LIMIT 0,1 | 参数: [@task_id0=TASK_002]
[2025-09-09 00:13:27.070 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`   WHERE ( `task_id` = @task_id0 )   LIMIT 0,1
[2025-09-09 00:13:27.075 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_name`,`target_amount`,`objective_order`,`objective_description`,`complete_template`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_002]
[2025-09-09 00:13:27.130 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_name`,`target_amount`,`objective_order`,`objective_description`,`complete_template`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-09-09 00:13:50.877 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-09 00:13:50.893 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-09 00:13:50.910 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-09 00:13:50.975 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-09 00:13:50.986 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-09 00:13:51.011 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`   WHERE ( `task_id` = @task_id0 )   LIMIT 0,1 | 参数: [@task_id0=TASK_002]
[2025-09-09 00:13:51.073 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`   WHERE ( `task_id` = @task_id0 )   LIMIT 0,1
[2025-09-09 00:13:51.151 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: UPDATE `task_config`  SET
           `task_name`=@task_name,`task_description`=@task_description,`task_type`=@task_type,`is_repeatable`=@is_repeatable,`prerequisite_task`=@prerequisite_task,`required_pet`=@required_pet,`reward_config`=@reward_config,`is_network_task`=@is_network_task,`is_active`=@is_active,`sort_order`=@sort_order,`created_at`=@created_at,`updated_at`=@updated_at  WHERE `task_id`=@task_id | 参数: [@task_id=TASK_002, @task_name=新手地图击杀任务, @task_description=亲爱的口袋勇士！我这里有一些精美礼品送给你们哦，但是也不能不劳而获，来帮助自然女神做几件事情吧，完成后这些精美礼品就属于你的啦..., @task_type=1, @is_repeatable=1, @prerequisite_task=, @required_pet=, @reward_config={"items":[{"id":"2016092304","amount":10,"name":"飞升丹","quality":1},{"id":"454523","amount":50,"name":"[宝石]3级蓝宝石","quality":1}]}, @is_network_task=0, @is_active=1, @sort_order=1, @created_at=2025/7/24 0:00:00, @updated_at=2025/9/9 0:13:51]
[2025-09-09 00:13:51.941 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: UPDATE `task_config`  SET
           `task_name`=@task_name,`task_description`=@task_description,`task_type`=@task_type,`is_repeatable`=@is_repeatable,`prerequisite_task`=@prerequisite_task,`required_pet`=@required_pet,`reward_config`=@reward_config,`is_network_task`=@is_network_task,`is_active`=@is_active,`sort_order`=@sort_order,`created_at`=@created_at,`updated_at`=@updated_at  WHERE `task_id`=@task_id
[2025-09-09 00:13:51.988 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: DELETE FROM task_objective WHERE task_id = @taskId | 参数: [@taskId=TASK_002]
[2025-09-09 00:13:52.051 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: DELETE FROM task_objective WHERE task_id = @taskId
[2025-09-09 00:13:52.129 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: INSERT INTO task_objective
                           (task_id, objective_type, target_id, target_name, target_amount, objective_order, objective_description, complete_template, created_at)
                           VALUES (@taskId0, @objectiveType0, @targetId0, @targetName0, @targetAmount0, @objectiveOrder0, @objectiveDescription0, @completeTemplate0, @createdAt0), (@taskId1, @objectiveType1, @targetId1, @targetName1, @targetAmount1, @objectiveOrder1, @objectiveDescription1, @completeTemplate1, @createdAt1), (@taskId2, @objectiveType2, @targetId2, @targetName2, @targetAmount2, @objectiveOrder2, @objectiveDescription2, @completeTemplate2, @createdAt2) | 参数: [@taskId0=TASK_002, @objectiveType0=KILL_MONSTER, @targetId0=111, @targetName0=小狸猫, @targetAmount0=10, @objectiveOrder0=0, @objectiveDescription0=<span>击杀  <strong>小狸猫</strong> 10个</span></br>, @completeTemplate0=<span>击杀  <strong>小狸猫</strong> ({0}/10)个 {1}</span></br>, @createdAt0=2025/9/9 0:13:52, @taskId1=TASK_002, @objectiveType1=KILL_MONSTER, @targetId1=1, @targetName1=金波姆, @targetAmount1=10, @objectiveOrder1=1, @objectiveDescription1=<span>击杀  <strong>金波母</strong> 10个</span></br>, @completeTemplate1=<span>击杀  <strong>金波母</strong> ({0}/10)个 {1}</span></br>, @createdAt1=2025/9/9 0:13:52, @taskId2=TASK_002, @objectiveType2=COLLECT_ITEM, @targetId2=201708076, @targetName2=小强化丹A礼包, @targetAmount2=5, @objectiveOrder2=2, @objectiveDescription2=<span>收集 <strong>小强化丹礼包</strong> 5个</span></br>, @completeTemplate2=<span>收集 <strong>小强化丹礼包</strong> （{0}/5）个{1} </span></br>, @createdAt2=2025/9/9 0:13:52]
[2025-09-09 00:13:52.201 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: INSERT INTO task_objective
                           (task_id, objective_type, target_id, target_name, target_amount, objective_order, objective_description, complete_template, created_at)
                           VALUES (@taskId0, @objectiveType0, @targetId0, @targetName0, @targetAmount0, @objectiveOrder0, @objectiveDescription0, @completeTemplate0, @createdAt0), (@taskId1, @objectiveType1, @targetId1, @targetName1, @targetAmount1, @objectiveOrder1, @objectiveDescription1, @completeTemplate1, @createdAt1), (@taskId2, @objectiveType2, @targetId2, @targetName2, @targetAmount2, @objectiveOrder2, @objectiveDescription2, @completeTemplate2, @createdAt2)
[2025-09-09 00:13:52.204 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`   WHERE ( `task_id` = @task_id0 )   LIMIT 0,1 | 参数: [@task_id0=TASK_002]
[2025-09-09 00:13:52.258 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`   WHERE ( `task_id` = @task_id0 )   LIMIT 0,1
[2025-09-09 00:13:52.261 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_name`,`target_amount`,`objective_order`,`objective_description`,`complete_template`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_002]
[2025-09-09 00:13:52.314 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_name`,`target_amount`,`objective_order`,`objective_description`,`complete_template`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-09-09 00:13:52.325 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-09 00:13:52.327 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-09 00:13:52.330 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-09 00:13:52.383 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-09 00:13:52.407 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-09 00:13:52.415 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM `task_config`  
[2025-09-09 00:13:52.473 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM `task_config`  
[2025-09-09 00:13:52.475 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`    ORDER BY `sort_order` ASC,`created_at` DESC LIMIT 0,10
[2025-09-09 00:13:52.530 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`    ORDER BY `sort_order` ASC,`created_at` DESC LIMIT 0,10
[2025-09-09 00:13:52.533 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_name`,`target_amount`,`objective_order`,`objective_description`,`complete_template`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_001]
[2025-09-09 00:13:52.585 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_name`,`target_amount`,`objective_order`,`objective_description`,`complete_template`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-09-09 00:13:52.599 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_name`,`target_amount`,`objective_order`,`objective_description`,`complete_template`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_002]
[2025-09-09 00:13:52.658 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_name`,`target_amount`,`objective_order`,`objective_description`,`complete_template`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-09-09 00:13:52.663 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_name`,`target_amount`,`objective_order`,`objective_description`,`complete_template`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_003]
[2025-09-09 00:13:52.723 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_name`,`target_amount`,`objective_order`,`objective_description`,`complete_template`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-09-09 00:13:52.726 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_name`,`target_amount`,`objective_order`,`objective_description`,`complete_template`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_004]
[2025-09-09 00:13:52.782 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_name`,`target_amount`,`objective_order`,`objective_description`,`complete_template`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-09-09 00:13:52.785 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_name`,`target_amount`,`objective_order`,`objective_description`,`complete_template`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_005]
[2025-09-09 00:13:52.836 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_name`,`target_amount`,`objective_order`,`objective_description`,`complete_template`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-09-09 00:13:52.841 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_name`,`target_amount`,`objective_order`,`objective_description`,`complete_template`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_010]
[2025-09-09 00:13:52.896 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_name`,`target_amount`,`objective_order`,`objective_description`,`complete_template`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-09-09 00:19:33.356 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-09 00:19:33.593 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-09 00:19:33.666 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-09 00:19:34.353 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-09 00:19:34.397 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-09 00:19:34.433 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`   WHERE ( `task_id` = @task_id0 )   LIMIT 0,1 | 参数: [@task_id0=TASK_001]
[2025-09-09 00:19:34.514 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`   WHERE ( `task_id` = @task_id0 )   LIMIT 0,1
[2025-09-09 00:19:34.677 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_name`,`target_amount`,`objective_order`,`objective_description`,`complete_template`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_001]
[2025-09-09 00:19:34.827 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_name`,`target_amount`,`objective_order`,`objective_description`,`complete_template`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
