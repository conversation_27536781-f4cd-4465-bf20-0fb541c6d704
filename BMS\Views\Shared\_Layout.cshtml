﻿<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - 口袋后台管理系统</title>
    
    <!-- Google Fonts -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap">
    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="~/lib/bootstrap/dist/css/bootstrap.min.css" />
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
    <!-- Toastr -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.css" />
    <!-- Custom CSS -->
    <link rel="stylesheet" href="~/css/site.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/BMS.styles.css" asp-append-version="true" />
    <!-- 科幻主题样式 -->
    <link rel="stylesheet" href="~/css/cyber-theme.css" asp-append-version="true" />
    
    <!-- Axios -->
    <script src="~/lib/axios.min.js"></script>
    <!-- Vue.js 3 -->
    <script src="~/lib/vue/vue.global.js"></script>
    
    <style>
        :root {
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --nav-shadow: 0 2px 20px rgba(0, 0, 0, 0.08);
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #0a0a0f 0%, #1a1a2e 50%, #16213e 100%);
            background-attachment: fixed;
            color: #e2e8f0;
            line-height: 1.6;
            min-height: 100vh;
        }

        /* 科幻导航栏 */
        .navbar-modern {
            background: linear-gradient(135deg, rgba(10, 10, 15, 0.95) 0%, rgba(26, 26, 46, 0.95) 100%) !important;
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(0, 212, 255, 0.3);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3), 0 0 40px rgba(0, 212, 255, 0.1);
            padding: 12px 0;
            transition: var(--transition);
        }

        .navbar-brand {
            font-weight: 700;
            font-size: 1.4rem;
            background: linear-gradient(135deg, #00d4ff, #8b5cf6);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-decoration: none;
            transition: var(--transition);
            text-shadow: 0 0 20px rgba(0, 212, 255, 0.5);
        }

        .navbar-brand:hover {
            transform: translateY(-1px);
            text-decoration: none;
            filter: brightness(1.2);
        }

        .navbar-nav .nav-link {
            font-weight: 500;
            color: #e2e8f0 !important;
            padding: 10px 16px !important;
            border-radius: 8px;
            margin: 0 4px;
            transition: var(--transition);
            position: relative;
            border: 1px solid transparent;
        }

        .navbar-nav .nav-link:hover {
            color: #00d4ff !important;
            background: rgba(0, 212, 255, 0.1);
            border-color: rgba(0, 212, 255, 0.3);
            transform: translateY(-1px);
            box-shadow: 0 4px 15px rgba(0, 212, 255, 0.2);
        }

        .navbar-nav .nav-link.active {
            color: #00d4ff !important;
            background: rgba(0, 212, 255, 0.1);
            border-color: rgba(0, 212, 255, 0.3);
        }

        .navbar-nav .nav-link i {
            margin-right: 8px;
            width: 16px;
            text-align: center;
        }

        /* 科幻下拉菜单 */
        .dropdown-menu {
            border: 1px solid rgba(0, 212, 255, 0.3);
            border-radius: 12px;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.5), 0 0 20px rgba(0, 212, 255, 0.2);
            padding: 8px;
            margin-top: 8px;
            backdrop-filter: blur(20px);
            background: linear-gradient(135deg, rgba(26, 26, 46, 0.95) 0%, rgba(22, 33, 62, 0.95) 100%);
        }

        .dropdown-item {
            border-radius: 8px;
            padding: 12px 16px;
            transition: var(--transition);
            font-weight: 500;
            color: #e2e8f0;
            margin-bottom: 2px;
            border: 1px solid transparent;
        }

        .dropdown-item:hover {
            background: rgba(0, 212, 255, 0.1);
            color: #00d4ff;
            border-color: rgba(0, 212, 255, 0.3);
            transform: translateX(4px);
            box-shadow: 0 4px 15px rgba(0, 212, 255, 0.2);
        }

        .dropdown-item i {
            margin-right: 12px;
            width: 16px;
            text-align: center;
            opacity: 0.7;
        }

        .dropdown-divider {
            margin: 8px 0;
            border-color: rgba(0, 0, 0, 0.08);
        }

        /* 用户下拉菜单 */
        .dropdown-toggle::after {
            margin-left: 8px;
            opacity: 0.6;
        }

        /* 主内容区域 */
        .main-content {
            min-height: calc(100vh - 140px);
            padding: 30px 0;
        }

        /* 科幻页脚 */
        .footer-modern {
            background: linear-gradient(135deg, rgba(10, 10, 15, 0.95) 0%, rgba(26, 26, 46, 0.95) 100%);
            border-top: 1px solid rgba(0, 212, 255, 0.3);
            padding: 25px 0;
            color: #94a3b8;
            font-size: 0.9rem;
            margin-top: auto;
            backdrop-filter: blur(20px);
        }

        .footer-modern a {
            color: #00d4ff;
            text-decoration: none;
            transition: var(--transition);
        }

        .footer-modern a:hover {
            color: #8b5cf6;
            text-decoration: none;
            text-shadow: 0 0 10px rgba(139, 92, 246, 0.5);
        }

        /* 响应式优化 */
        @@media (max-width: 991.98px) {
            .navbar-nav {
                padding: 10px 0;
            }
            
            .navbar-nav .nav-link {
                margin: 2px 0;
            }
            
            .dropdown-menu {
                box-shadow: none;
                background: #f8fafc;
                border: 1px solid #e2e8f0;
            }
        }

        /* 加载动画 */
        .page-loading {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 3px;
            background: var(--primary-gradient);
            transform: scaleX(0);
            transform-origin: left;
            transition: transform 0.3s ease;
            z-index: 9999;
        }

        .page-loading.loading {
            animation: loading-bar 2s ease-in-out;
        }

        @@keyframes loading-bar {
            0% { transform: scaleX(0); }
            50% { transform: scaleX(0.7); }
            100% { transform: scaleX(1); }
        }

        /* 滚动条美化 */
        ::-webkit-scrollbar {
            width: 6px;
        }

        ::-webkit-scrollbar-track {
            background: #f1f1f1;
        }

        ::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 3px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }

        /* 面包屑导航 */
        .breadcrumb-modern {
            background: none;
            padding: 0;
            margin: 0 0 20px 0;
        }

        .breadcrumb-modern .breadcrumb-item {
            color: #718096;
            font-size: 0.9rem;
        }

        .breadcrumb-modern .breadcrumb-item.active {
            color: #2d3748;
            font-weight: 500;
        }

        .breadcrumb-modern .breadcrumb-item + .breadcrumb-item::before {
            content: "›";
            color: #cbd5e0;
            font-size: 1.2em;
        }
    </style>
</head>
<body>
    <!-- 页面加载指示器 -->
    <div class="page-loading" id="pageLoading"></div>

    <header>
        <nav class="navbar navbar-expand-lg navbar-light navbar-modern fixed-top">
            <div class="container-fluid">
                <a class="navbar-brand" asp-area="" asp-controller="Home" asp-action="Index">
                    <i class="fas fa-cube"></i> 口袋后台管理系统
                </a>
                
                <button class="navbar-toggler border-0" type="button" data-bs-toggle="collapse" data-bs-target=".navbar-collapse" 
                        aria-controls="navbarSupportedContent" aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>
                
                <div class="navbar-collapse collapse d-lg-inline-flex justify-content-between">
                    <ul class="navbar-nav flex-grow-1">
                        <li class="nav-item">
                            <a class="nav-link" asp-area="" asp-controller="Home" asp-action="Index">
                                <i class="fas fa-satellite-dish"></i> 控制中心
                            </a>
                        </li>
                        @if (User.Identity.IsAuthenticated)
                        {
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle" href="#" id="quickAccessDropdown" role="button"
                                   data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="fas fa-bolt"></i> 快速访问
                                </a>
                                <ul class="dropdown-menu" aria-labelledby="quickAccessDropdown">
                                    <li><h6 class="dropdown-header">常用功能</h6></li>
                                    <li><a class="dropdown-item" asp-controller="User" asp-action="Index">
                                        <i class="fas fa-users"></i> 用户管理
                                    </a></li>
                                    <li><a class="dropdown-item" asp-controller="PetConfig" asp-action="Index">
                                        <i class="fas fa-dog"></i> 宠物配置
                                    </a></li>
                                    <li><a class="dropdown-item" asp-controller="ItemConfig" asp-action="Index">
                                        <i class="fas fa-cubes"></i> 道具配置
                                    </a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" asp-controller="AdminBm" asp-action="Index">
                                        <i class="fas fa-users-cog"></i> 管理员管理
                                    </a></li>
                                </ul>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="#" onclick="showDevelopingAlert()">
                                    <i class="fas fa-chart-line"></i> 数据统计
                                </a>
                            </li>
                        }
                    </ul>
                    <ul class="navbar-nav">
                        @if (User.Identity.IsAuthenticated)
                        {
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" 
                                   data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="fas fa-user-circle"></i> @User.Identity.Name
                                </a>
                                <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                                    <li><a class="dropdown-item" href="#" onclick="showDevelopingAlert()">
                                        <i class="fas fa-user-edit"></i> 个人设置
                                    </a></li>
                                    <li><a class="dropdown-item" href="#" onclick="showDevelopingAlert()">
                                        <i class="fas fa-lock"></i> 修改密码
                                    </a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" asp-controller="Auth" asp-action="Logout">
                                        <i class="fas fa-sign-out-alt"></i> 退出登录
                                    </a></li>
                                </ul>
                            </li>
                        }
                        else
                        {
                            <li class="nav-item">
                                <a class="nav-link" asp-controller="Auth" asp-action="Login">
                                    <i class="fas fa-sign-in-alt"></i> 登录
                                </a>
                            </li>
                        }
                    </ul>
                </div>
            </div>
        </nav>
    </header>
    
    <div class="container-fluid main-content" style="margin-top: 80px;">
        <main role="main" class="pb-3">
            @RenderBody()
        </main>
    </div>

    <footer class="footer-modern">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <p class="mb-0">
                        &copy; 2025 口袋后台管理系统 - 
                        <a asp-area="" asp-controller="Home" asp-action="Privacy">隐私政策</a>
                    </p>
                </div>
                <div class="col-md-6 text-md-end">
                    <small>
                        <i class="fas fa-code"></i> 基于 ASP.NET Core & Vue.js 构建
                    </small>
                </div>
            </div>
        </div>
    </footer>
    
    <!-- Scripts -->
    <script src="~/lib/jquery/dist/jquery.min.js"></script>
    <script src="~/lib/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Toastr -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js"></script>
    <!-- Razor安全工具类 -->
    <script src="~/js/razor-safe.js"></script>
    <script src="~/js/site.js" asp-append-version="true"></script>
    
    @await RenderSectionAsync("Scripts", required: false)
    
    <!-- 全局脚本 -->
    <script>
        // 配置 Toastr
        if (typeof toastr !== 'undefined') {
            toastr.options = {
                closeButton: true,
                progressBar: true,
                positionClass: "toast-top-right",
                timeOut: 3000,
                extendedTimeOut: 1000,
                showEasing: "swing",
                hideEasing: "linear",
                showMethod: "fadeIn",
                hideMethod: "fadeOut"
            };
        }

        // 显示开发中提示
        function showDevelopingAlert() {
            if (typeof toastr !== 'undefined') {
                toastr.info('功能开发中，敬请期待！', '提示', {
                    timeOut: 2000,
                    progressBar: true
                });
            } else {
                alert('功能开发中...');
            }
        }

        // 页面加载效果
        document.addEventListener('DOMContentLoaded', function() {
            const loadingBar = document.getElementById('pageLoading');
            if (loadingBar) {
                loadingBar.classList.add('loading');
                
                // 2秒后隐藏加载条
                setTimeout(() => {
                    loadingBar.style.opacity = '0';
                    setTimeout(() => {
                        loadingBar.style.display = 'none';
                    }, 300);
                }, 2000);
            }

            // 为当前页面的导航项添加活动状态
            const currentPath = window.location.pathname;
            const navLinks = document.querySelectorAll('.navbar-nav .nav-link');
            
            navLinks.forEach(link => {
                if (link.getAttribute('href') === currentPath) {
                    link.classList.add('active');
                }
            });
        });

        // 平滑滚动
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // 导航栏滚动效果
        let lastScrollTop = 0;
        const navbar = document.querySelector('.navbar-modern');
        
        window.addEventListener('scroll', function() {
            const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
            
            if (scrollTop > lastScrollTop && scrollTop > 100) {
                // 向下滚动时隐藏导航栏
                navbar.style.transform = 'translateY(-100%)';
            } else {
                // 向上滚动时显示导航栏
                navbar.style.transform = 'translateY(0)';
            }
            
            // 滚动时添加阴影效果
            if (scrollTop > 10) {
                navbar.style.boxShadow = '0 4px 20px rgba(0, 0, 0, 0.15)';
            } else {
                navbar.style.boxShadow = '0 2px 20px rgba(0, 0, 0, 0.08)';
            }
            
            lastScrollTop = scrollTop;
        });
    </script>
</body>
</html>
