@model AdminBmCreateDto
@{
    ViewData["Title"] = "新增管理员";
}

<div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1>新增管理员</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="@Url.Action("Index", "Home")">首页</a></li>
                        <li class="breadcrumb-item"><a href="@Url.Action("Index")">管理员管理</a></li>
                        <li class="breadcrumb-item active">新增管理员</li>
                    </ol>
                </div>
            </div>
        </div>
    </section>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            <div class="card">
                <div class="card-body" id="createApp">
                    <form v-on:submit.prevent="submitForm">
                        @Html.AntiForgeryToken()
                        <div class="form-group">
                            <label>用户名</label>
                            <input type="text" class="form-control" v-model="formData.Username" 
                                   placeholder="请输入用户名" v-on:blur="checkUsername" />
                            <small class="form-text text-danger" v-if="errors.Username">{{ errors.Username }}</small>
                        </div>
                        <div class="form-group">
                            <label>密码</label>
                            <input type="password" class="form-control" v-model="formData.Password" 
                                   placeholder="请输入密码" />
                            <small class="form-text text-danger" v-if="errors.Password">{{ errors.Password }}</small>
                        </div>
                        <div class="form-group">
                            <label>确认密码</label>
                            <input type="password" class="form-control" v-model="formData.ConfirmPassword" 
                                   placeholder="请再次输入密码" />
                            <small class="form-text text-danger" v-if="errors.ConfirmPassword">{{ errors.ConfirmPassword }}</small>
                        </div>
                        <div class="form-group">
                            <label>真实姓名</label>
                            <input type="text" class="form-control" v-model="formData.RealName" 
                                   placeholder="请输入真实姓名" />
                        </div>
                        <div class="form-group">
                            <label>手机号码</label>
                            <input type="text" class="form-control" v-model="formData.Phone" 
                                   placeholder="请输入手机号码" />
                            <small class="form-text text-danger" v-if="errors.Phone">{{ errors.Phone }}</small>
                        </div>
                        <div class="form-group">
                            <label>邮箱地址</label>
                            <input type="email" class="form-control" v-model="formData.Email" 
                                   placeholder="请输入邮箱地址" />
                            <small class="form-text text-danger" v-if="errors.Email">{{ errors.Email }}</small>
                        </div>
                        <div class="form-group">
                            <label>状态</label>
                            <select class="form-control" v-model="formData.Status">
                                <option :value="1">启用</option>
                                <option :value="0">禁用</option>
                            </select>
                        </div>
                        <button type="submit" class="btn btn-primary" :disabled="isSubmitting">
                            <i class="fas fa-save mr-1"></i>
                            {{ isSubmitting ? '保存中...' : '保存' }}
                        </button>
                        <a href="@Url.Action("Index")" class="btn btn-default">
                            <i class="fas fa-arrow-left mr-1"></i>
                            返回列表
                        </a>
                    </form>
                </div>
            </div>
        </div>
    </section>

</div>

@section Scripts {
    <script>
        // 初始化Vue应用
        Vue.createApp({
            data() {
                return {
                    formData: {
                        Username: '',
                        Password: '',
                        ConfirmPassword: '',
                        RealName: '',
                        Phone: '',
                        Email: '',
                        Status: 1
                    },
                    errors: {},
                    isSubmitting: false,
                    debugInfo: false,
                    lastResponse: null
                };
            },
            methods: {
                // 检查用户名是否存在
                async checkUsername() {
                    if (!this.formData.Username) return;
                    
                    try {
                        const url = `/AdminBm/CheckUsername?username=${encodeURIComponent(this.formData.Username)}`;
                        const response = await fetch(url);
                        const result = await response.json();
                        
                        if (result.exists) {
                            this.errors.Username = '该用户名已存在';
                        } else {
                            this.errors.Username = '';
                        }
                    } catch (error) {
                        console.error('检查用户名出错:', error);
                    }
                },
                
                // 表单验证
                validate() {
                    this.errors = {};
                    
                    if (!this.formData.Username) {
                        this.errors.Username = '请输入用户名';
                    }
                    
                    if (!this.formData.Password) {
                        this.errors.Password = '请输入密码';
                    }
                    
                    if (!this.formData.ConfirmPassword) {
                        this.errors.ConfirmPassword = '请确认密码';
                    } else if (this.formData.Password !== this.formData.ConfirmPassword) {
                        this.errors.ConfirmPassword = '两次输入的密码不一致';
                    }
                    
                    // 手机号验证
                    if (this.formData.Phone) {
                        const phonePattern = '^1' + String.fromCharCode(91) + '3-9' + String.fromCharCode(93) + '\\d{9}$';
                        const phoneRegex = new RegExp(phonePattern);
                        if (!phoneRegex.test(this.formData.Phone)) {
                            this.errors.Phone = '请输入有效的手机号码';
                        }
                    }
                    
                    // 邮箱验证
                    if (this.formData.Email) {
                        // 简单的邮箱格式验证，使用字符编码避免Razor解析at符号
                        const atChar = String.fromCharCode(64); // at符号
                        if (!this.formData.Email.includes(atChar) || !this.formData.Email.includes('.')) {
                            this.errors.Email = '请输入有效的邮箱地址';
                        }
                    }
                    
                    return Object.keys(this.errors).length === 0;
                },
                
                // 提交表单
                async submitForm() {
                    if (!this.validate()) return;
                    
                    this.isSubmitting = true;
                    this.lastResponse = null;
                    
                    try {
                        const response = await fetch('/AdminBm/CreateApi', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]').value
                            },
                            body: JSON.stringify(this.formData)
                        });
                        
                        const result = await response.json();
                        this.lastResponse = result; // 记录响应用于调试
                        
                        if (result.success) {
                            toastr.success('管理员创建成功');
                            setTimeout(() => {
                                window.location.href = '/AdminBm/Index';
                            }, 1500);
                        } else {
                            // 处理后端验证错误
                            if (result.errors) {
                                // 清空前端验证错误
                                this.errors = {};
                                // 显示后端验证错误
                                Object.keys(result.errors).forEach(key => {
                                    this.errors[key] = result.errors[key];
                                });
                                
                                // 同时显示汇总错误信息
                                const errorMessages = Object.values(result.errors).join('; ');
                                toastr.error(`验证失败：${errorMessages}`);
                                
                                // 自动开启调试模式
                                this.debugInfo = true;
                            } else {
                                toastr.error(result.message || '保存失败，请重试');
                            }
                        }
                    } catch (error) {
                        console.error('保存出错:', error);
                        toastr.error('系统错误，请重试');
                        this.lastResponse = { error: error.message };
                        this.debugInfo = true;
                    } finally {
                        this.isSubmitting = false;
                    }
                }
            }
        }).mount('#createApp');
    </script>
}