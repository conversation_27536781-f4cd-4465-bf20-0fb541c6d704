namespace BMS.Models.Common
{
    /// <summary>
    /// API统一返回结果格式
    /// </summary>
    /// <typeparam name="T">数据类型</typeparam>
    public class ApiResult<T>
    {
        /// <summary>
        /// 状态码（200：成功，其他：失败）
        /// </summary>
        public int Code { get; set; }

        /// <summary>
        /// 返回消息
        /// </summary>
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// 返回数据
        /// </summary>
        public T? Data { get; set; }

        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success => Code == 200;

        /// <summary>
        /// 成功结果
        /// </summary>
        /// <param name="data">数据</param>
        /// <param name="message">消息</param>
        /// <returns></returns>
        public static ApiResult<T> Ok(T? data = default, string message = "操作成功")
        {
            return new ApiResult<T>
            {
                Code = 200,
                Message = message,
                Data = data
            };
        }

        /// <summary>
        /// 失败结果
        /// </summary>
        /// <param name="message">错误消息</param>
        /// <param name="code">错误码</param>
        /// <returns></returns>
        public static ApiResult<T> Fail(string message, int code = 400)
        {
            return new ApiResult<T>
            {
                Code = code,
                Message = message,
                Data = default
            };
        }
    }

    /// <summary>
    /// 无数据的API返回结果
    /// </summary>
    public class ApiResult : ApiResult<object>
    {
        /// <summary>
        /// 成功结果
        /// </summary>
        /// <param name="message">消息</param>
        /// <returns></returns>
        public new static ApiResult Ok(string message = "操作成功")
        {
            return new ApiResult
            {
                Code = 200,
                Message = message,
                Data = null
            };
        }

        /// <summary>
        /// 失败结果
        /// </summary>
        /// <param name="message">错误消息</param>
        /// <param name="code">错误码</param>
        /// <returns></returns>
        public new static ApiResult Fail(string message, int code = 400)
        {
            return new ApiResult
            {
                Code = code,
                Message = message,
                Data = null
            };
        }
    }
} 