using Microsoft.AspNetCore.Mvc;
using BMS.Models.DTOs;
using BMS.Models.Common;
using BMS.Services.Interfaces;

namespace BMS.Controllers
{
    /// <summary>
    /// 地图详情控制器
    /// </summary>
    public class MapDetailController : Controller
    {
        private readonly IMapDetailService _mapDetailService;
        private readonly IMapConfigService _mapConfigService;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="mapDetailService">地图详情服务</param>
        /// <param name="mapConfigService">地图配置服务</param>
        public MapDetailController(IMapDetailService mapDetailService, IMapConfigService mapConfigService)
        {
            _mapDetailService = mapDetailService;
            _mapConfigService = mapConfigService;
        }

        /// <summary>
        /// 地图详情管理页面
        /// </summary>
        /// <returns>页面视图</returns>
        public async Task<IActionResult> Index()
        {
            // 获取地图列表用于下拉选择
            var maps = await _mapConfigService.GetAllAsync();
            ViewBag.Maps = maps;
            return View();
        }

        /// <summary>
        /// 创建地图详情页面
        /// </summary>
        /// <returns>页面视图</returns>
        public async Task<IActionResult> Create()
        {
            // 获取地图列表用于下拉选择
            var maps = await _mapConfigService.GetAllAsync();
            ViewBag.Maps = maps;
            return View();
        }

        /// <summary>
        /// 编辑地图详情页面
        /// </summary>
        /// <param name="id">地图详情ID</param>
        /// <returns>页面视图</returns>
        public async Task<IActionResult> Edit(int id)
        {
            var mapDetail = await _mapDetailService.GetByIdAsync(id);
            if (mapDetail == null)
            {
                return NotFound();
            }

            // 获取地图列表用于下拉选择
            var maps = await _mapConfigService.GetAllAsync();
            ViewBag.Maps = maps;
            
            return View(mapDetail);
        }

        /// <summary>
        /// 分页获取地图详情列表
        /// </summary>
        /// <param name="queryDto">查询条件</param>
        /// <returns>分页结果</returns>
        [HttpGet]
        public async Task<IActionResult> GetPagedList([FromQuery] MapDetailQueryDto queryDto)
        {
            try
            {
                var result = await _mapDetailService.GetPagedListAsync(queryDto);
                return Json(new
                {
                    success = true,
                    data = result.Data,
                    page = result.PageIndex,
                    pageSize = result.PageSize,
                    totalCount = result.TotalCount,
                    totalPages = result.TotalPages
                });
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = $"获取地图详情列表失败：{ex.Message}" });
            }
        }

        /// <summary>
        /// 根据ID获取地图详情
        /// </summary>
        /// <param name="id">地图详情ID</param>
        /// <returns>地图详情信息</returns>
        [HttpGet]
        public async Task<IActionResult> GetById(int id)
        {
            try
            {
                var mapDetail = await _mapDetailService.GetByIdAsync(id);
                if (mapDetail == null)
                {
                    return Json(new { success = false, message = "地图详情不存在" });
                }
                return Json(new { success = true, data = mapDetail });
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = $"获取地图详情失败：{ex.Message}" });
            }
        }

        /// <summary>
        /// 根据地图ID获取地图详情
        /// </summary>
        /// <param name="mapId">地图ID</param>
        /// <returns>地图详情信息</returns>
        [HttpGet]
        public async Task<IActionResult> GetByMapId(int mapId)
        {
            try
            {
                var mapDetail = await _mapDetailService.GetByMapIdAsync(mapId);
                return Json(new { success = true, data = mapDetail });
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = $"获取地图详情失败：{ex.Message}" });
            }
        }

        /// <summary>
        /// 创建地图详情
        /// </summary>
        /// <param name="createDto">创建DTO</param>
        /// <returns>操作结果</returns>
        [HttpPost]
        public async Task<IActionResult> Create([FromBody] MapDetailCreateDto createDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    var errors = ModelState.Values
                        .SelectMany(v => v.Errors)
                        .Select(e => e.ErrorMessage)
                        .ToList();
                    return Json(new { success = false, message = string.Join(", ", errors) });
                }

                var result = await _mapDetailService.CreateAsync(createDto);
                return Json(new { success = result.Success, message = result.Message });
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = $"创建地图详情失败：{ex.Message}" });
            }
        }

        /// <summary>
        /// 更新地图详情
        /// </summary>
        /// <param name="updateDto">更新DTO</param>
        /// <returns>操作结果</returns>
        [HttpPost]
        public async Task<IActionResult> Update([FromBody] MapDetailUpdateDto updateDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    var errors = ModelState.Values
                        .SelectMany(v => v.Errors)
                        .Select(e => e.ErrorMessage)
                        .ToList();
                    return Json(new { success = false, message = string.Join(", ", errors) });
                }

                var result = await _mapDetailService.UpdateAsync(updateDto);
                return Json(new { success = result.Success, message = result.Message });
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = $"更新地图详情失败：{ex.Message}" });
            }
        }

        /// <summary>
        /// 删除地图详情
        /// </summary>
        /// <param name="dto">删除地图详情DTO</param>
        /// <returns>操作结果</returns>
        [HttpPost]
        public async Task<IActionResult> Delete([FromBody] MapDetailDeleteDto dto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    var errors = ModelState.Values
                        .SelectMany(v => v.Errors)
                        .Select(e => e.ErrorMessage)
                        .ToList();
                    return Json(new { success = false, message = string.Join(", ", errors) });
                }

                var result = await _mapDetailService.DeleteAsync(dto.Id);
                return Json(new { success = result.Success, message = result.Message });
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = $"删除地图详情失败：{ex.Message}" });
            }
        }
    }
} 