@{
    ViewData["Title"] = "地图详情管理";
}

<!-- 科幻背景 -->
<div class="cyber-bg"></div>

<!-- 科幻地图详情管理应用容器 -->
<div id="mapDetailApp">
    <!-- 科幻页面标题 -->
    <div class="container-fluid py-4">
        <div class="row mb-4">
            <div class="col-12">
                <div class="cyber-card">
                    <div class="cyber-card-header">
                        <div class="cyber-icon">🗺️</div>
                        <h1 class="cyber-card-title">地图详情管理系统</h1>
                        <div class="ms-auto">
                            <button type="button" class="cyber-btn cyber-btn-success" v-on:click="showCreateModal">
                                <i class="fas fa-plus"></i> 新增地图详情
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 科幻主要内容 -->
    <div class="container-fluid">
        <!-- 科幻搜索条件 -->
        <div class="cyber-card">
            <div class="cyber-card-header">
                <div class="cyber-icon">🔍</div>
                <h3 class="cyber-card-title">搜索条件</h3>
            </div>
            <div class="cyber-card-body">
                <div class="row">
                    <div class="col-md-4">
                        <div class="cyber-form-group">
                            <label class="cyber-form-label">选择地图</label>
                            <div class="cyber-select-wrapper">
                                <select class="cyber-form-control" v-model="searchForm.mapId">
                                    <option value="">请选择地图</option>
                                    <option v-for="map in maps" v-bind:key="map.id" v-bind:value="map.mapId">
                                        {{ map.mapId }} - {{ map.mapName }}
                                    </option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="cyber-form-group">
                            <label class="cyber-form-label">地图类型</label>
                            <input type="number" class="cyber-form-control" v-model="searchForm.type" placeholder="请输入地图类型">
                        </div>
                    </div>
                    <div class="col-md-5">
                        <div class="cyber-form-group">
                            <label class="cyber-form-label">&nbsp;</label>
                            <div class="d-flex gap-2 flex-wrap">
                                <button type="button" class="cyber-btn cyber-btn-primary" v-on:click="searchMapDetails">
                                    <i class="fas fa-search"></i> 搜索
                                </button>
                                <button type="button" class="cyber-btn cyber-btn-outline" v-on:click="resetSearch">
                                    <i class="fas fa-redo"></i> 重置
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 科幻地图详情列表 -->
        <div class="cyber-card">
            <div class="cyber-card-header">
                <div class="cyber-icon">🗺️</div>
                <h3 class="cyber-card-title">地图详情列表</h3>
            </div>
            <div class="cyber-card-body">
                <div v-if="loading && mapDetails.length === 0" class="text-center py-4">
                    <i class="fas fa-spinner fa-spin me-2" style="font-size: 1.5rem; color: var(--cyber-blue);"></i>
                    <span>数据加载中...</span>
                </div>
                <div v-else>
                    <div class="cyber-table-container">
                        <table class="cyber-table">
                            <thead class="cyber-thead">
                                <tr>
                                    <th class="cyber-th">ID</th>
                                    <th class="cyber-th">地图ID</th>
                                    <th class="cyber-th">地图名称</th>
                                    <th class="cyber-th">限制成长</th>
                                    <th class="cyber-th">限制等级</th>
                                    <th class="cyber-th">限制钥匙</th>
                                    <th class="cyber-th">金币范围</th>
                                    <th class="cyber-th">元宝范围</th>
                                    <th class="cyber-th">掉落范围</th>
                                    <th class="cyber-th">操作</th>
                                </tr>
                            </thead>
                            <tbody class="cyber-tbody" v-if="!loading && mapDetails.length === 0">
                                <tr class="cyber-tr">
                                    <td colspan="10" class="cyber-td text-center py-4 text-muted">
                                        <i class="fas fa-inbox me-2"></i>暂无地图详情数据
                                    </td>
                                </tr>
                            </tbody>
                            <tbody class="cyber-tbody" v-else>
                                <tr class="cyber-tr" v-for="item in mapDetails" v-bind:key="item.id">
                                    <td class="cyber-td">{{ item.id }}</td>
                                    <td class="cyber-td">{{ item.mapId }}</td>
                                    <td class="cyber-td">
                                        <i class="fas fa-map me-1" style="color: var(--cyber-blue);"></i>
                                        {{ item.mapName }}
                                    </td>
                                    <td class="cyber-td">{{ item.limitGrowth }}</td>
                                    <td class="cyber-td">{{ item.limitLevel }}</td>
                                    <td class="cyber-td">
                                        <span class="badge" v-bind:class="item.limitKey ? 'badge-danger' : 'badge-success'">
                                            {{ item.limitKey ? '限制' : '不限制' }}
                                        </span>
                                    </td>
                                    <td class="cyber-td">
                                        <span class="badge badge-warning">{{ item.minGold }} - {{ item.maxGold }}</span>
                                    </td>
                                    <td class="cyber-td">
                                        <span class="badge badge-info">{{ item.minYuanbao }} - {{ item.maxYuanbao }}</span>
                                    </td>
                                    <td class="cyber-td">
                                        <span class="badge badge-primary">{{ item.minDrop }} - {{ item.maxDrop }}</span>
                                    </td>
                                    <td class="cyber-td">
                                        <div class="d-flex gap-1 flex-wrap">
                                            <button type="button" class="cyber-btn cyber-btn-sm cyber-btn-warning"
                                                    v-on:click="showEditModal(item)" title="编辑地图详情">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button type="button" class="cyber-btn cyber-btn-sm cyber-btn-danger"
                                                    v-on:click="deleteMapDetail(item.id)" title="删除地图详情">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <!-- 科幻分页 -->
                    <div class="mt-3" v-if="pagination.totalCount > 0">
                        <div class="row align-items-center">
                            <div class="col-sm-6">
                                <div class="text-muted">
                                    显示第 {{ (pagination.page - 1) * pagination.pageSize + 1 }} 到
                                    {{ Math.min(pagination.page * pagination.pageSize, pagination.totalCount) }} 条，
                                    共 {{ pagination.totalCount }} 条记录
                                </div>
                            </div>
                            <div class="col-sm-6">
                                <ul class="cyber-pagination justify-content-end">
                                    <li class="page-item" :class="{ disabled: pagination.page <= 1 }">
                                        <a class="page-link" href="#" v-on:click.prevent="changePage(pagination.page - 1)">上一页</a>
                                    </li>
                                    <li class="page-item" v-for="page in getPageNumbers()" :key="page" :class="{ active: page === pagination.page }">
                                        <a class="page-link" href="#" v-on:click.prevent="changePage(page)" v-text="page"></a>
                                    </li>
                                    <li class="page-item" :class="{ disabled: pagination.page >= pagination.totalPages }">
                                        <a class="page-link" href="#" v-on:click.prevent="changePage(pagination.page + 1)">下一页</a>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
     
       <!-- 科幻新增/编辑模态框 -->
    <div class="modal fade cyber-modal" id="mapDetailModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-map me-2"></i>{{ modalTitle }}
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="cyber-form-group">
                                    <label class="cyber-form-label">选择地图 <span class="text-danger">*</span></label>
                                    <div class="cyber-select-wrapper">
                                        <select class="cyber-form-control" v-model="formData.mapId" v-bind:class="{ 'is-invalid': formErrors.mapId }">
                                            <option value="">请选择地图</option>
                                            <option v-for="map in maps" v-bind:key="map.id" v-bind:value="map.mapId">
                                                {{ map.mapId }} - {{ map.mapName }}
                                            </option>
                                        </select>
                                    </div>
                                    <div class="invalid-feedback" v-if="formErrors.mapId">{{ formErrors.mapId }}</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="cyber-form-group">
                                    <label class="cyber-form-label">地图类型</label>
                                    <input type="number" class="cyber-form-control" v-model="formData.type">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="cyber-form-group">
                                    <label class="cyber-form-label">限制成长</label>
                                    <input type="number" class="cyber-form-control" v-model="formData.limitGrowth" step="0.01">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="cyber-form-group">
                                    <label class="cyber-form-label">限制等级</label>
                                    <input type="number" class="cyber-form-control" v-model="formData.limitLevel">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="cyber-form-group">
                                    <label class="cyber-form-label">限制钥匙</label>
                                    <div class="cyber-checkbox-wrapper mt-2">
                                        <input class="cyber-checkbox" type="checkbox" v-model="formData.limitKey" id="limitKeyCheck">
                                        <label class="cyber-checkbox-label" for="limitKeyCheck">限制钥匙</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="cyber-form-group">
                                    <label class="cyber-form-label">最小金币</label>
                                    <input type="number" class="cyber-form-control" v-model="formData.minGold">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="cyber-form-group">
                                    <label class="cyber-form-label">最大金币</label>
                                    <input type="number" class="cyber-form-control" v-model="formData.maxGold">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="cyber-form-group">
                                    <label class="cyber-form-label">最小元宝</label>
                                    <input type="number" class="cyber-form-control" v-model="formData.minYuanbao">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="cyber-form-group">
                                    <label class="cyber-form-label">最大元宝</label>
                                    <input type="number" class="cyber-form-control" v-model="formData.maxYuanbao">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="cyber-form-group">
                                    <label class="cyber-form-label">最小掉落</label>
                                    <input type="number" class="cyber-form-control" v-model="formData.minDrop">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="cyber-form-group">
                                    <label class="cyber-form-label">最大掉落</label>
                                    <input type="number" class="cyber-form-control" v-model="formData.maxDrop">
                                </div>
                            </div>
                        </div>
                        <div class="cyber-form-group">
                            <label class="cyber-form-label">地图图标</label>
                            <input type="text" class="cyber-form-control" v-model="formData.icon" placeholder="地图图标路径">
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="cyber-btn cyber-btn-outline" data-bs-dismiss="modal">
                        <i class="fas fa-times me-1"></i>取消
                    </button>
                    <button type="button" class="cyber-btn cyber-btn-success" v-on:click="saveMapDetail" v-bind:disabled="loading">
                        <span v-if="loading" class="spinner-border spinner-border-sm me-1"></span>
                        <i v-if="!loading" class="fas fa-save me-1"></i>
                        {{ isEdit ? '更新' : '保存' }}
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

   

</div> <!-- 关闭 #mapDetailApp 容器 -->

<script src="~/lib/axios.min.js"></script>
<script>
console.log('MapDetail页面：开始初始化');

// 创建Vue应用 - 按照MapConfig的模式
const mapDetailVueApp = Vue.createApp({
    data() {
        return {
            mapDetails: [],
            maps: [], // 地图列表
            searchForm: {
                mapId: '',
                type: null
            },
            pagination: {
                page: 1,
                pageSize: 10,
                totalCount: 0,
                totalPages: 0
            },
            formData: {
                id: 0,
                mapId: '',
                limitGrowth: 0,
                limitLevel: 0,
                limitKey: false,
                minGold: 0,
                minYuanbao: 0,
                maxGold: 0,
                maxYuanbao: 0,
                maxDrop: 0,
                minDrop: 0,
                icon: '',
                type: 0
            },
            formErrors: {},
            loading: true,  // 初始化时设为true
            isEdit: false,
            modalTitle: ''
        };
    },
    mounted() {
        console.log('MapDetail Vue组件：已挂载，开始加载数据');
        this.loadMaps();
        this.loadMapDetails();
    },
    methods: {
        // 加载地图列表
        async loadMaps() {
            try {
                const response = await axios.get('/MapConfig/GetAll');
                if (response.data.success) {
                    this.maps = response.data.data;
                }
            } catch (error) {
                console.error('加载地图列表失败：', error);
            }
        },
        
        // 加载地图详情列表
        async loadMapDetails() {
            try {
                this.loading = true;
                console.log('正在加载地图详情列表...');
                
                const params = {
                    page: this.pagination.page,
                    pageSize: this.pagination.pageSize,
                    ...this.searchForm
                };
                
                const response = await axios.get('/MapDetail/GetPagedList', { params });
                console.log('地图详情列表响应：', response.data);
                
                if (response.data.success) {
                    this.mapDetails = response.data.data;
                    this.pagination.totalCount = response.data.totalCount;
                    this.pagination.totalPages = response.data.totalPages;
                    console.log('地图详情列表加载成功，数据条数：', this.mapDetails.length);
                } else {
                    console.error('加载数据失败：', response.data.message);
                    alert('加载数据失败：' + response.data.message);
                }
            } catch (error) {
                console.error('加载地图详情列表失败：', error);
                alert('加载数据失败');
            } finally {
                this.loading = false;
            }
        },
        
        // 搜索
        searchMapDetails() {
            this.pagination.page = 1;
            this.loadMapDetails();
        },

        // 重置搜索
        resetSearch() {
            this.searchForm = {
                mapId: '',
                type: null
            };
            this.pagination.page = 1;
            this.loadMapDetails();
        },
        
        // 分页
        changePage(page) {
            if (page >= 1 && page <= this.pagination.totalPages) {
                this.pagination.page = page;
                this.loadMapDetails();
            }
        },
        
        // 获取页码数组
        getPageNumbers() {
            const pages = [];
            const start = Math.max(1, this.pagination.page - 2);
            const end = Math.min(this.pagination.totalPages, start + 4);
            
            for (let i = start; i <= end; i++) {
                pages.push(i);
            }
            return pages;
        },
        
        // 显示新增模态框
        showCreateModal() {
            console.log('showCreateModal clicked');
            this.isEdit = false;
            this.modalTitle = '新增地图详情';
            this.resetForm();
            // 使用Vue nextTick确保数据更新后再显示模态框
            this.$nextTick(() => {
                const modal = new bootstrap.Modal(document.getElementById('mapDetailModal'), {
                    backdrop: 'static',
                    keyboard: false
                });
                modal.show();
            });
        },
        
        // 显示编辑模态框
        showEditModal(item) {
            console.log('showEditModal clicked', item);
            this.isEdit = true;
            this.modalTitle = '编辑地图详情';
            this.formData = { ...item };
            this.formErrors = {};
            // 使用Vue nextTick确保数据更新后再显示模态框
            this.$nextTick(() => {
                const modal = new bootstrap.Modal(document.getElementById('mapDetailModal'), {
                    backdrop: 'static',
                    keyboard: false
                });
                modal.show();
            });
        },
        
        // 重置表单
        resetForm() {
            this.formData = {
                id: 0,
                mapId: '',
                limitGrowth: 0,
                limitLevel: 0,
                limitKey: false,
                minGold: 0,
                minYuanbao: 0,
                maxGold: 0,
                maxYuanbao: 0,
                maxDrop: 0,
                minDrop: 0,
                icon: '',
                type: 0
            };
            this.formErrors = {};
        },
        
        // 验证表单
        validateForm() {
            this.formErrors = {};
            
            if (!this.formData.mapId) {
                this.formErrors.mapId = '请选择地图';
            }
            
            return Object.keys(this.formErrors).length === 0;
        },
        
        // 保存地图详情
        async saveMapDetail() {
            if (!this.validateForm()) {
                return;
            }
            
            this.loading = true;
            
            try {
                const url = this.isEdit ? '/MapDetail/Update' : '/MapDetail/Create';
                const response = await axios.post(url, this.formData);
                
                if (response.data.success) {
                    alert(response.data.message);
                    // 关闭模态框
                    const modalElement = document.getElementById('mapDetailModal');
                    const modal = bootstrap.Modal.getInstance(modalElement);
                    if (modal) {
                        modal.hide();
                    }
                    this.loadMapDetails();
                } else {
                    alert(response.data.message);
                }
            } catch (error) {
                console.error('保存地图详情失败：', error);
                alert('保存失败');
            } finally {
                this.loading = false;
            }
        },
        
        // 删除地图详情
        async deleteMapDetail(id) {
            if (!confirm('确定要删除这个地图详情吗？')) {
                return;
            }
            
            try {
                const response = await axios.post('/MapDetail/Delete', { id });
                
                if (response.data.success) {
                    alert(response.data.message);
                    this.loadMapDetails();
                } else {
                    alert(response.data.message);
                }
            } catch (error) {
                console.error('删除地图详情失败：', error);
                alert('删除失败');
            }
        }
    }
});

// 确保DOM完全加载后挂载Vue应用
function mountVueApp() {
    const element = document.getElementById('mapDetailApp');
    const modal = document.getElementById('mapDetailModal');
    if (element && modal) {
        console.log('Mounting Vue app to #mapDetailApp');
        console.log('Modal element found:', modal);
        mapDetailVueApp.mount('#mapDetailApp');
    } else {
        console.error('Element not found! mapDetailApp:', !!element, 'modal:', !!modal);
        // 重试
        setTimeout(mountVueApp, 50);
    }
}

// 使用多种方式确保DOM准备就绪
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', mountVueApp);
} else {
    setTimeout(mountVueApp, 50);
}
</script> 