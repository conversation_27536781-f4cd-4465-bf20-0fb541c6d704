﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace BMS.Models.Entities
{
    ///<summary>
    ///任务配置表
    ///</summary>
    [SugarTable("task_config")]
    public partial class task_config
    {
           public task_config(){


           }
           /// <summary>
           /// Desc:任务ID
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true)]
           public string task_id {get;set;}

           /// <summary>
           /// Desc:任务名称
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string task_name {get;set;}

           /// <summary>
           /// Desc:任务描述
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? task_description {get;set;}

           /// <summary>
           /// Desc:任务类型(0=普通,1=循环,2=活动)
           /// Default:0
           /// Nullable:True
           /// </summary>           
           public byte? task_type {get;set;}

           /// <summary>
           /// Desc:是否可重复(0=否,1=是)
           /// Default:0
           /// Nullable:True
           /// </summary>           
           public byte? is_repeatable {get;set;}

           /// <summary>
           /// Desc:前置任务ID
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? prerequisite_task {get;set;}

           /// <summary>
           /// Desc:指定宠物ID
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? required_pet {get;set;}

           /// <summary>
           /// Desc:奖励配置(JSON格式)
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? reward_config {get;set;}

           /// <summary>
           /// Desc:是否网络任务
           /// Default:0
           /// Nullable:True
           /// </summary>           
           public byte? is_network_task {get;set;}

           /// <summary>
           /// Desc:是否激活
           /// Default:1
           /// Nullable:True
           /// </summary>           
           public byte? is_active {get;set;}

           /// <summary>
           /// Desc:排序顺序
           /// Default:0
           /// Nullable:True
           /// </summary>           
           public int? sort_order {get;set;}

           /// <summary>
           /// Desc:创建时间
           /// Default:CURRENT_TIMESTAMP
           /// Nullable:True
           /// </summary>           
           public DateTime? created_at {get;set;}

           /// <summary>
           /// Desc:更新时间
           /// Default:CURRENT_TIMESTAMP
           /// Nullable:True
           /// </summary>           
           public DateTime? updated_at {get;set;}

    }
}
