# 进度模版功能实施说明

## 功能概述
在任务配置管理中新增了"进度模版"字段，用于定义任务目标的进度显示模版。该字段与`objective_description`字段类似，存储在`task_objective`表中。

## 实施内容

### 1. 数据库字段
- 表名：`task_objective`
- 字段名：`complete_template`
- 字段类型：`varchar(500)`，可为空
- 字段描述：进度模版，用于定义进度显示格式，如：`{current}/{total}`

### 2. 实体类修改
**文件：** `BMS\Models\Entities\task_objective.cs`

已添加进度模版字段：
```csharp
/// <summary>
/// Desc:进度模版
/// Default:
/// Nullable:True
/// </summary>           
public string? complete_template { get; set; }
```

### 3. DTO类修改
**文件：** `BMS\Models\DTOs\TaskConfigDto.cs`

#### 3.1 TaskObjectiveDto类
添加了CompleteTemplate属性：
```csharp
/// <summary>
/// 进度模版
/// </summary>
public string? CompleteTemplate { get; set; }
```

#### 3.2 TaskObjectiveCreateDto类
添加了CompleteTemplate属性：
```csharp
/// <summary>
/// 进度模版
/// </summary>
public string? CompleteTemplate { get; set; }
```

### 4. 服务层修改
**文件：** `BMS\Services\Implementations\TaskConfigService.cs`

#### 4.1 GetTaskObjectivesAsync方法
在查询任务目标时，添加了CompleteTemplate字段的映射：
```csharp
return objectives.Select(o => new TaskObjectiveDto
{
    // ... 其他字段
    CompleteTemplate = o.complete_template,
    // ... 其他字段
}).ToList();
```

#### 4.2 CreateTaskObjectivesAsync方法
在创建任务目标时，添加了complete_template字段的处理：

**SQL语句修改：**
```sql
INSERT INTO task_objective
(task_id, objective_type, target_id, target_amount, objective_order, objective_description, complete_template, created_at)
VALUES ...
```

**参数添加：**
```csharp
parameters.Add(new
{
    name = $"completeTemplate{i}",
    value = obj.CompleteTemplate ?? (object)DBNull.Value
});
```

### 5. 前端界面修改
**文件：** `BMS\Views\TaskConfig\Index.cshtml`

#### 5.1 任务目标编辑表单
在任务目标编辑区域添加了进度模版输入框：
```html
<div class="col-md-6">
    <label class="cyber-label">进度模版</label>
    <input type="text" class="cyber-form-control"
           v-model="objective.completeTemplate"
           placeholder="进度显示模版，如：{current}/{total}">
</div>
```

#### 5.2 任务详情显示
在任务详情的目标表格中添加了进度模版列：
```html
<thead>
    <tr>
        <!-- ... 其他列 -->
        <th>进度模版</th>
    </tr>
</thead>
<tbody>
    <tr v-for="objective in selectedTask.objectives" :key="objective.objectiveId">
        <!-- ... 其他列 -->
        <td>{{ objective.completeTemplate || '-' }}</td>
    </tr>
</tbody>
```

#### 5.3 JavaScript修改
在addObjective方法中添加了completeTemplate字段的初始化：
```javascript
addObjective() {
    this.taskForm.objectives.push({
        // ... 其他字段
        completeTemplate: ''
    });
}
```

## 使用说明

### 1. 添加任务目标时
1. 在任务配置页面点击"添加目标"
2. 填写目标类型、目标ID、目标数量等基本信息
3. 在"进度模版"字段中输入进度显示格式，例如：
   - `{current}/{total}` - 显示当前进度/总进度
   - `完成度：{current}/{total}` - 带文字说明的进度
   - `{current}` - 仅显示当前进度

### 2. 查看任务详情时
在任务详情页面的"任务目标"表格中可以看到每个目标的进度模版设置。

## 技术要点

1. **字段可为空**：进度模版字段设计为可选字段，不填写不会影响任务的正常功能
2. **与描述字段并列**：进度模版与目标描述字段在界面上并排显示，便于同时维护
3. **数据库兼容性**：新增字段为可空字段，对现有数据无影响
4. **前后端一致性**：前端Vue.js模型与后端DTO保持字段名称一致

## 注意事项

1. 进度模版字段目前仅用于存储和显示，具体的进度计算和模版解析需要在游戏客户端实现
2. 建议进度模版使用统一的格式约定，如使用`{current}`和`{total}`作为占位符
3. 字段长度限制为500字符，足够存储复杂的进度模版格式
