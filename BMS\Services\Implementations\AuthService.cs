using BMS.Services.Interfaces;
using BMS.Models.DTOs;
using BMS.Models.Common;
using BMS.Common.Helpers;
using BMS.Models.Entities;

namespace BMS.Services.Implementations
{
    /// <summary>
    /// 认证服务实现
    /// </summary>
    public class AuthService : IAuthService
    {
        private readonly IDbService _dbService;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="dbService">数据库服务</param>
        public AuthService(IDbService dbService)
        {
            _dbService = dbService;
        }

        /// <summary>
        /// 管理员登录
        /// </summary>
        /// <param name="loginDto">登录信息</param>
        /// <returns>登录结果</returns>
        public async Task<ApiResult<admin_bm>> LoginAsync(LoginDto loginDto)
        {
            try
            {
                // 根据用户名查找管理员
                var admin = await GetAdminByUsernameAsync(loginDto.Username);
                if (admin == null)
                {
                    return ApiResult<admin_bm>.Fail("用户名或密码错误");
                }

                // 检查账号状态
                if (!admin.status)
                {
                    return ApiResult<admin_bm>.Fail("账号已被禁用，请联系管理员");
                }

                // 验证密码
                if (!VerifyPassword(loginDto.Password, admin.password))
                {
                    return ApiResult<admin_bm>.Fail("用户名或密码错误");
                }

                // 更新最后登录时间
                await UpdateLastLoginTimeAsync(admin.id);

                // 清除敏感信息
                admin.password = string.Empty;

                return ApiResult<admin_bm>.Ok(admin, "登录成功");
            }
            catch (Exception ex)
            {
                return ApiResult<admin_bm>.Fail($"登录失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 根据用户名获取管理员信息
        /// </summary>
        /// <param name="username">用户名</param>
        /// <returns>管理员信息</returns>
        public async Task<admin_bm?> GetAdminByUsernameAsync(string username)
        {
            try
            {
                return await _dbService.Queryable<admin_bm>()
                    .Where(x => x.username == username)
                    .FirstAsync();
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// 根据ID获取管理员信息
        /// </summary>
        /// <param name="id">管理员ID</param>
        /// <returns>管理员信息</returns>
        public async Task<admin_bm?> GetAdminByIdAsync(int id)
        {
            try
            {
                return await _dbService.Queryable<admin_bm>()
                    .Where(x => x.id == id)
                    .FirstAsync();
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// 验证密码
        /// </summary>
        /// <param name="password">原始密码</param>
        /// <param name="hashedPassword">加密后的密码</param>
        /// <returns>是否匹配</returns>
        public bool VerifyPassword(string password, string hashedPassword)
        {
            // 使用MD5验证（兼容现有系统）
            var md5Hash = PasswordHelper.Md5Hash(password);
            return md5Hash == hashedPassword;
        }

        /// <summary>
        /// 更新最后登录时间
        /// </summary>
        /// <param name="adminId">管理员ID</param>
        /// <returns>是否成功</returns>
        public async Task<bool> UpdateLastLoginTimeAsync(int adminId)
        {
            try
            {
                var result = await _dbService.Updateable<admin_bm>()
                    .SetColumns(x => new admin_bm
                    {
                        last_login_time = DateTime.Now,
                        update_time = DateTime.Now
                    })
                    .Where(x => x.id == adminId)
                    .ExecuteCommandAsync();

                return result > 0;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 修改密码
        /// </summary>
        /// <param name="adminId">管理员ID</param>
        /// <param name="changePasswordDto">修改密码信息</param>
        /// <returns>修改结果</returns>
        public async Task<ApiResult> ChangePasswordAsync(int adminId, ChangePasswordDto changePasswordDto)
        {
            try
            {
                // 获取管理员信息
                var admin = await GetAdminByIdAsync(adminId);
                if (admin == null)
                {
                    return ApiResult.Fail("管理员不存在");
                }

                // 验证原密码
                if (!VerifyPassword(changePasswordDto.OldPassword, admin.password))
                {
                    return ApiResult.Fail("原密码错误");
                }

                // 加密新密码
                var newPasswordHash = PasswordHelper.Md5Hash(changePasswordDto.NewPassword);

                // 更新密码
                var result = await _dbService.Updateable<admin_bm>()
                    .SetColumns(x => new admin_bm
                    {
                        password = newPasswordHash,
                        update_time = DateTime.Now
                    })
                    .Where(x => x.id == adminId)
                    .ExecuteCommandAsync();

                if (result > 0)
                {
                    return ApiResult.Ok("密码修改成功");
                }
                else
                {
                    return ApiResult.Fail("密码修改失败");
                }
            }
            catch (Exception ex)
            {
                return ApiResult.Fail($"密码修改失败：{ex.Message}");
            }
        }
    }
} 