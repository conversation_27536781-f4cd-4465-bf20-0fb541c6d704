﻿using SqlSugar;

namespace BMS.Sugar
{
    public class DbContext
    {
        public SqlSugarScope Db;
        private readonly ILogger<DbContext> _logger;

        public DbContext(IConfiguration configuration, ILogger<DbContext> logger)
        {
            _logger = logger;

            var connectionString = configuration.GetConnectionString("DefaultConnection");

            // 记录数据库连接信息（隐藏密码）
            var safeConnectionString = HideSensitiveInfo(connectionString ?? "");
            _logger.LogInformation("初始化数据库连接: {ConnectionString}", safeConnectionString);

            Db = new SqlSugarScope(new ConnectionConfig()
            {
                ConnectionString = connectionString,
                DbType = DbType.MySql,
                IsAutoCloseConnection = true
            },
            db =>
            {
                // 执行SQL前事件
                db.Aop.OnLogExecuting = (sql, pars) =>
                {
                    try
                    {
                        var parameters = pars?.Select(p => $"{p.ParameterName}={p.Value}").ToArray();
                        var paramStr = parameters?.Length > 0 ? $" | 参数: [{string.Join(", ", parameters)}]" : "";

                        _logger.LogDebug("执行SQL: {Sql}{Parameters}", sql, paramStr);
                        Console.WriteLine($"SQL: {sql}{paramStr}");
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "SQL日志记录失败");
                    }
                };

                // 执行SQL后事件
                db.Aop.OnLogExecuted = (sql, pars) =>
                {
                    try
                    {
                        _logger.LogDebug("SQL执行完成: {Sql}", sql);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "SQL执行完成日志记录失败");
                    }
                };

                // 数据库连接打开事件
                db.Aop.OnLogExecuting = (sql, pars) =>
                {
                    try
                    {
                        var parameters = pars?.Select(p => $"{p.ParameterName}={p.Value}").ToArray();
                        var paramStr = parameters?.Length > 0 ? $" | 参数: [{string.Join(", ", parameters)}]" : "";

                        _logger.LogDebug("执行SQL: {Sql}{Parameters}", sql, paramStr);
                        Console.WriteLine($"SQL: {sql}{paramStr}");
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "SQL日志记录失败");
                    }
                };

                // 错误处理事件
                db.Aop.OnError = (exp) =>
                {
                    try
                    {
                        _logger.LogError(exp, "数据库操作发生错误: {Message}", exp.Message);
                        Console.WriteLine($"数据库错误: {exp.Message}");

                        if (exp.InnerException != null)
                        {
                            _logger.LogError("内部异常: {InnerMessage}", exp.InnerException.Message);
                            Console.WriteLine($"内部错误: {exp.InnerException.Message}");
                        }
                    }
                    catch (Exception logEx)
                    {
                        Console.WriteLine($"日志记录失败: {logEx.Message}");
                    }
                };

                // 数据库连接事件
                db.Aop.OnDiffLogEvent = (diffLog) =>
                {
                    try
                    {
                        _logger.LogInformation("数据变更: {DiffLog}", diffLog);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "数据变更日志记录失败");
                    }
                };
            });

            // 测试数据库连接
            TestConnection();
        }

        /// <summary>
        /// 隐藏连接字符串中的敏感信息
        /// </summary>
        private static string HideSensitiveInfo(string connectionString)
        {
            if (string.IsNullOrEmpty(connectionString))
                return connectionString;

            try
            {
                // 使用简单的字符串替换避免正则表达式警告
                var result = connectionString;
                var patterns = new[] { "pwd=", "password=" };

                foreach (var pattern in patterns)
                {
                    var index = result.IndexOf(pattern, StringComparison.OrdinalIgnoreCase);
                    if (index >= 0)
                    {
                        var start = index + pattern.Length;
                        var end = result.IndexOf(';', start);
                        if (end == -1) end = result.Length;

                        result = result.Substring(0, start) + "***" + result.Substring(end);
                    }
                }

                return result;
            }
            catch
            {
                return "连接字符串解析失败";
            }
        }

        /// <summary>
        /// 测试数据库连接
        /// </summary>
        private void TestConnection()
        {
            try
            {
                _logger.LogInformation("开始测试数据库连接...");

                // 简单的连接测试
                var result = Db.Queryable<dynamic>().AS("(SELECT 1 as test) t").First();

                _logger.LogInformation("✅ 数据库连接测试成功");
                Console.WriteLine("✅ 数据库连接正常");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ 数据库连接测试失败: {Message}", ex.Message);
                Console.WriteLine($"❌ 数据库连接失败: {ex.Message}");

                // 记录详细的连接错误信息
                if (ex.InnerException != null)
                {
                    _logger.LogError("连接失败详情: {InnerMessage}", ex.InnerException.Message);
                    Console.WriteLine($"连接失败详情: {ex.InnerException.Message}");
                }
            }
        }

        /// <summary>
        /// 获取数据库处理对象
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <returns></returns>
        public SimpleClient<T> GetEntityDB<T>() where T : class, new()
        {
            return new SimpleClient<T>(Db);
        }

        /// <summary>
        /// 记录数据库操作日志
        /// </summary>
        /// <param name="operation">操作类型</param>
        /// <param name="tableName">表名</param>
        /// <param name="details">详细信息</param>
        public void LogDatabaseOperation(string operation, string tableName, string? details = null)
        {
            try
            {
                var message = $"数据库操作 - {operation} | 表: {tableName}";
                if (!string.IsNullOrEmpty(details))
                {
                    message += $" | 详情: {details}";
                }

                _logger.LogInformation("{Message}", message);
                Console.WriteLine($"DB操作: {message}");
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "数据库操作日志记录失败");
            }
        }

        /// <summary>
        /// 记录性能日志
        /// </summary>
        /// <param name="operation">操作名称</param>
        /// <param name="duration">执行时间</param>
        /// <param name="recordCount">记录数量</param>
        public void LogPerformance(string operation, TimeSpan duration, int recordCount = 0)
        {
            try
            {
                var message = $"性能统计 - {operation} | 耗时: {duration.TotalMilliseconds:F2}ms";
                if (recordCount > 0)
                {
                    message += $" | 记录数: {recordCount}";
                }

                if (duration.TotalMilliseconds > 1000) // 超过1秒的操作记录为警告
                {
                    _logger.LogWarning("慢查询警告: {Message}", message);
                }
                else
                {
                    _logger.LogDebug("{Message}", message);
                }

                Console.WriteLine($"性能: {message}");
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "性能日志记录失败");
            }
        }

        /// <summary>
        /// 获取一个自定义的数据库处理对象
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="db"></param>
        /// <returns></returns>
        public SimpleClient<T> GetEntityDB<T>(SqlSugarScope db) where T : class, new()
        {
            return new SimpleClient<T>(db);
        }
    }
}
