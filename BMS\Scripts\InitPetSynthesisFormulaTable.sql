-- 初始化pet_synthesis_formula表
-- 如果表不存在则创建

-- 检查表是否存在
SET @table_exists = (SELECT COUNT(*) FROM information_schema.tables 
                     WHERE table_schema = DATABASE() 
                     AND table_name = 'pet_synthesis_formula');

-- 如果表不存在，创建表
SET @sql = IF(@table_exists = 0,
    'CREATE TABLE pet_synthesis_formula (
        id INT AUTO_INCREMENT PRIMARY KEY COMMENT ''自增主键'',
        main_pet_no INT NOT NULL COMMENT ''主宠物编号'',
        sub_pet_no INT NOT NULL COMMENT ''副宠物编号'',
        main_growth_min DECIMAL(9,6) DEFAULT 0.000000 COMMENT ''主宠最小成长要求'',
        sub_growth_min DECIMAL(9,6) DEFAULT 0.000000 COMMENT ''副宠最小成长要求'',
        result_pet_no INT NOT NULL COMMENT ''合成结果宠物编号'',
        base_success_rate DECIMAL(5,2) DEFAULT 50.00 COMMENT ''基础成功率(%)'',
        required_level INT DEFAULT 40 COMMENT ''所需等级'',
        cost_gold BIGINT DEFAULT 50000 COMMENT ''消耗金币'',
        formula_type VARCHAR(20) DEFAULT ''FIXED'' COMMENT ''公式类型 FIXED（固定公式）、RANDOM_GOD（随机神宠）、RANDOM_HOLY（随机神圣宠物）'',
        is_active TINYINT DEFAULT 1 COMMENT ''是否启用'',
        create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT ''创建时间'',
        INDEX idx_main_sub_pet (main_pet_no, sub_pet_no),
        INDEX idx_result_pet (result_pet_no),
        INDEX idx_formula_type (formula_type),
        INDEX idx_is_active (is_active)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT=''宠物合成公式表'';',
    'SELECT ''pet_synthesis_formula表已存在'' as message;'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查是否有测试数据，如果没有则插入一些基础数据
SET @data_count = (SELECT COUNT(*) FROM pet_synthesis_formula);

-- 如果没有数据，插入一些基础测试数据
SET @insert_sql = IF(@data_count = 0,
    'INSERT INTO pet_synthesis_formula (
        main_pet_no, sub_pet_no, main_growth_min, sub_growth_min, 
        result_pet_no, base_success_rate, required_level, cost_gold, 
        formula_type, is_active, create_time
    ) VALUES 
    (1, 2, 0.500000, 0.400000, 3, 75.00, 50, 100000, ''FIXED'', 1, NOW()),
    (2, 1, 0.450000, 0.500000, 4, 70.00, 45, 80000, ''FIXED'', 1, NOW()),
    (3, 4, 0.600000, 0.550000, 5, 60.00, 60, 200000, ''FIXED'', 1, NOW());',
    'SELECT ''已有测试数据，跳过插入'' as message;'
);

PREPARE stmt2 FROM @insert_sql;
EXECUTE stmt2;
DEALLOCATE PREPARE stmt2;

-- 显示表结构
DESCRIBE pet_synthesis_formula;

-- 显示数据统计
SELECT 
    COUNT(*) as total_formulas,
    COUNT(CASE WHEN is_active = 1 THEN 1 END) as active_formulas,
    COUNT(CASE WHEN formula_type = 'FIXED' THEN 1 END) as fixed_formulas,
    COUNT(CASE WHEN formula_type = 'RANDOM_GOD' THEN 1 END) as random_god_formulas,
    COUNT(CASE WHEN formula_type = 'RANDOM_HOLY' THEN 1 END) as random_holy_formulas
FROM pet_synthesis_formula;

-- 显示前几条数据
SELECT * FROM pet_synthesis_formula LIMIT 5;
