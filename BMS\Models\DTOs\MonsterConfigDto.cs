using System.ComponentModel.DataAnnotations;

namespace BMS.Models.DTOs
{
    /// <summary>
    /// 怪物配置DTO
    /// </summary>
    public class MonsterConfigDto
    {
        /// <summary>
        /// 自增主键
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 怪物编号
        /// </summary>
        public int MonsterNo { get; set; }

        /// <summary>
        /// 怪物名称
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 怪物属性
        /// </summary>
        public string Attribute { get; set; } = string.Empty;

        /// <summary>
        /// 技能编号
        /// </summary>
        public string? Skill { get; set; }
    }

    /// <summary>
    /// 怪物配置查询DTO
    /// </summary>
    public class MonsterConfigQueryDto
    {
        /// <summary>
        /// 怪物编号
        /// </summary>
        public int? MonsterNo { get; set; }

        /// <summary>
        /// 怪物名称（支持模糊查询）
        /// </summary>
        public string? Name { get; set; }

        /// <summary>
        /// 怪物属性
        /// </summary>
        public string? Attribute { get; set; }

        /// <summary>
        /// 页码
        /// </summary>
        public int Page { get; set; } = 1;

        /// <summary>
        /// 每页数量
        /// </summary>
        public int PageSize { get; set; } = 10;
    }

    /// <summary>
    /// 创建怪物配置DTO
    /// </summary>
    public class MonsterConfigCreateDto
    {
        /// <summary>
        /// 怪物编号
        /// </summary>
        [Required(ErrorMessage = "怪物编号不能为空")]
        [Range(1, int.MaxValue, ErrorMessage = "怪物编号必须大于0")]
        public int MonsterNo { get; set; }

        /// <summary>
        /// 怪物名称
        /// </summary>
        [Required(ErrorMessage = "怪物名称不能为空")]
        [StringLength(50, ErrorMessage = "怪物名称长度不能超过50个字符")]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 怪物属性
        /// </summary>
        [Required(ErrorMessage = "怪物属性不能为空")]
        [StringLength(10, ErrorMessage = "怪物属性长度不能超过10个字符")]
        public string Attribute { get; set; } = string.Empty;

        /// <summary>
        /// 技能编号
        /// </summary>
        [StringLength(50, ErrorMessage = "技能编号长度不能超过50个字符")]
        public string? Skill { get; set; }
    }

    /// <summary>
    /// 更新怪物配置DTO
    /// </summary>
    public class MonsterConfigUpdateDto
    {
        /// <summary>
        /// 自增主键
        /// </summary>
        [Required(ErrorMessage = "ID不能为空")]
        [Range(1, int.MaxValue, ErrorMessage = "ID必须大于0")]
        public int Id { get; set; }

        /// <summary>
        /// 怪物编号
        /// </summary>
        [Required(ErrorMessage = "怪物编号不能为空")]
        [Range(1, int.MaxValue, ErrorMessage = "怪物编号必须大于0")]
        public int MonsterNo { get; set; }

        /// <summary>
        /// 怪物名称
        /// </summary>
        [Required(ErrorMessage = "怪物名称不能为空")]
        [StringLength(50, ErrorMessage = "怪物名称长度不能超过50个字符")]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 怪物属性
        /// </summary>
        [Required(ErrorMessage = "怪物属性不能为空")]
        [StringLength(10, ErrorMessage = "怪物属性长度不能超过10个字符")]
        public string Attribute { get; set; } = string.Empty;

        /// <summary>
        /// 技能编号
        /// </summary>
        [StringLength(50, ErrorMessage = "技能编号长度不能超过50个字符")]
        public string? Skill { get; set; }
    }


} 